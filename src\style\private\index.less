@import '../../assets/fonts/private/iconfont.css';
@import './layout.less';

// 通用详情页面
.custom-detail-page {
  @hpadding: 40px;
  > .ant-card-head {
    font-size: 18px;
    border-bottom: none;
    padding: 8px @hpadding;

    &::before {
      display: block;
      content: '';
      width: 8px;
      height: 16px;
      position: absolute;
      top: 0;
      left: 16px;
      border-radius: 0 0 4px 4px;
      background: @primary-color;
    }
  }
  > .ant-card-body {
    padding: 0;
  }

  .ant-divider {
    margin: 0;
  }

  .block-wrapper {
    padding: 24px @hpadding;
    &:hover {
      background: #fcfcfc;
    }
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #000000;
      margin-bottom: 24px;

      .anticon {
        color: @primary-color;
        margin-right: 8px;
      }
    }
  }

  .btns {
    text-align: right;
    padding: 20px @hpadding;
  }
}

// 抽屉
.ant-drawer {
  .ant-drawer-header {
    // background: @primary-background-image;
    // background: @primary-color;
    border-radius: 0;
    .ant-drawer-title,
    .ant-drawer-close {
      // color: #ffffff;
    }
  }
  &.big-title {
    .ant-drawer-header {
      .ant-drawer-title {
        font-size: 20px;
      }
    }
  }
}

/** add by wangjian366 start**/
.ant-layout {
  background: #f9f9f9;
}
.ant-input[disabled],
.ant-select-disabled .ant-select-selection {
  background: #fbfbfb;
}
.ant-layout-content > .page-list-with-tabs {
  margin-top: -16px;
  .ant-tabs-nav-wrap {
    .ant-tabs-nav .ant-tabs-tab {
      margin-right: 16px;
      padding: 12px 4px;
      font-size: 15px;
    }
  }
  .search-area {
    border: 1px solid #f2f2f2;
    border-top: none;
    background: #fdfdfd;
  }
  .ant-tabs-bar {
    // border-bottom: none;
    padding-left: 8px;
  }

  div.custom-table {
    margin-top: 0;
  }
  .frame-button-wrapper-relative {
    top: -48px;
  }
}
.page-list-single {
  .search-area {
    border: 1px solid #f2f2f2;
    background: #fdfdfd;
  }
  .custom-table.need-search-area {
    margin-top: -16px;
  }
}
// 新版表格
.custom-table {
  .ant-table {
    .ant-table-thead {
      th {
        font-size: 12px;
        // background: #F9FAFB;
        padding: 10px 16px;
      }
    }
    .ant-table-tbody {
      // > tr:nth-child(even) {
      //   background: transparent !important;
      // }
      td {
        font-size: 13px;
        // border-bottom: 1px solid #F0F0F0;
        padding: 12px 16px;
      }
    }
  }
}

.ant-table-empty {
  .custom-empty {
    margin: 12px 0;
  }
  // 滚动table处理
  .ant-table-scroll {
    .ant-table-placeholder {
      margin-top: 12px;
    }
  }
  .ant-table-placeholder {
    border: none !important;
    background: transparent;
  }
  // .ant-table-scroll {
  //   height: 200px;
  //   min-height: 200px;
  // }
  // .ant-table-body {
  //   min-height: 200px;
  //   height: 200px;
  // }
  // .ant-table-placeholder {
  //   margin-top: -160px;
  //   height: 150px;
  //   border: none;
  //   background:transparent
  // }
}
// .ant-table-small.ant-table-bordered .ant-table-content {
//   border-right: 0;
// }
.ant-modal-content {
  border-radius: 8px;
}
/** add by wangjian366 end**/

.ant-alert.small {
  font-size: 12px;
  font-weight: normal;
  border: none;

  > .ant-alert-icon {
    top: 11px;
  }

  > .ant-alert-message {
    color: rgba(0, 0, 0, 0.65);
  }
}

// 空组件
.custom-empty {
  opacity: 0.5;
  color: rgba(0, 0, 0, 0.65);
  > .ant-empty-image {
    height: 60px;
  }
}

// 全局loading
div.full-page-loading-container {
  background: transparent !important;
}

// 页面通用头样式
.custom-page-header {
  padding: @page-padding;
  color: @font-color-strong;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 66px;
  .font-bold();
  .font-size(20px);

  &.border-top {
    .border(1px, 0, 0, 0);
  }

  &.border-bottom {
    .border(0, 0, 1px, 0);
  }

  &.border-top-bottom {
    .border(1px, 0, 1px, 0);
  }
}

// popover美化
.ant-popover {
  .ant-popover-inner {
    .ant-popover-inner-content {
      max-width: 600px;
      max-height: 300px;
    }
  }
}

// icon样式居中
div.database-image {
  .iconClass {
    display: flex;
    align-items: center !important;
    .iconStyle {
      padding-top: 0px;
    }
  }
}

.seach-area-btns {
  > .ant-btn {
    // font-size: 14px;
    // font-weight: 600;
    margin-right: 0;
    margin-left: 8px;
    // color: #008adc;
    // border-color: #7fc4ed;
    // &:hover {
    //   color: @primary-5;
    //   border-color: #008adc;
    // }

    // &.ant-btn-primary {
    //   background: #008adc;
    //   color: #ffffff;
    //   &:hover {
    //     background: #219be3;
    //     color: #ffffff;
    //   }
    // }
  }
}

.up-20 {
  transform: translateY(-20px);
}
