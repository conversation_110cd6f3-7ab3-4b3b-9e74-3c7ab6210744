import Http from '@/utils/request';

export function homeReqTest(params = {}) {
  return Http({
    url: `/api/home/<USER>
    method: 'get',
    params: params
  });
}

export function createReview(params = {}) {
  return Http({
    url: `/sqlreview/review/list`,
    method: 'post',
    data: params
  });
}

export function createFileReview(params = {}) {
  return Http({
    url: `/sqlreview/review/file_review`,
    method: 'post',
    data: params
  });
}
// 多git扫描 创建审核对象
export function createMoreReview(params = {}) {
  return Http({
    url: `/sqlreview/review/multi_git_raise`,
    method: 'post',
    data: params
  });
}

export function exportReviewDetail(params = {}) {
  return Http({
    // url: `/sqlreview/review/${params.sqlId}/export/`,
    url: `/sqlreview/review/export_review_html`,
    method: 'post',
    data: params,
    timeout: 60000 * 10,
    responseType: 'blob'
  });
}

export function exportOrderReviewDetail(params = {}) {
  return Http({
    // url: `/sqlreview/review/${params.sqlId}/export-dba/`,
    url: `/sqlreview/review/export_review_html`,
    method: 'post',
    data: params,
    timeout: 60000 * 3,
    responseType: 'blob'
  });
}

export function batchUpdateDetail(params = {}) {
  return Http({
    url: `/sqlreview/common/upload_file/`,
    method: 'post',
    data: params,
    timeout: 60000 * 3,
    headers: { 'Content-Type': 'multipart/form-data' }
  });
}
export function getUpdateProgress(params = {}) {
  return Http({
    url: `/sqlreview/common/task/${params.task_id}/`,
    method: 'get',
    params: {},
    timeout: 60000 * 3
  });
}
export function reviewRetry(params = {}) {
  return Http({
    url: `/sqlreview/review/review-retry/`,
    method: 'post',
    data: params,
    timeout: 60000 * 3
  });
}
// 检查项目合法性
export function checkProject(params) {
  return Http({
    url: `/sqlreview/review/project-review-lock/`,
    method: 'get',
    params
  });
}

// 报表信息
export function getReviewDetailReport(params) {
  return Http({
    url: `/sqlreview/review/review_detail_report`,
    method: 'get',
    params
  });
}

// 一键通过
export function reviewDetailAllPass(params) {
  // ?review_id=1000832
  return Http({
    url: `/sqlreview/review/review-detail-all-pass`,
    method: 'get',
    params
  });
}

// 提交评审
export function sendEmail(params) {
  // ?record-id=1000832
  return Http({
    url: `/sqlreview/review/send-email`,
    method: 'get',
    params
  });
}
// 评价五星
export function sendStars(params = {}) {
  return Http({
    url: `/sqlreview/review/feedback/`,
    method: 'post',
    data: params
  });
}
// 催办
export function pressToDo(params = {}) {
  return Http({
    url: `/sqlreview/review/send-email`,
    method: 'post',
    data: params
  });
}

// 基本信息
export function getReviewDetailWeekendReport(params) {
  return Http({
    url: `/sqlreview/review/weekend_report`,
    method: 'get',
    params
  });
}

// 规则图表信息
export function getReviewDetailRuleChart(params) {
  return Http({
    url: `/sqlreview/review/rule_chart`,
    method: 'get',
    params
  });
}

// 通过图表信息
export function getReviewDetailPassChart(params) {
  return Http({
    url: `/sqlreview/review/pass_chart`,
    method: 'get',
    params
  });
}

// 事后审核
export function afterwardsReview(method, params) {
  return Http({
    url: `sqlreview/review/afterwards_review`,
    method,
    data: params
  });
}

// 事后审核
export function afterwardsPostgreReview(method, params) {
  return Http({
    url: `sqlreview/after_audit/pg_after_audit`,
    method,
    data: params
  });
}

// 事后审核 新建审核对象 连接测试
export function checkAuth(params = {}) {
  return Http({
    url: `/sqlreview/review/check_auth`,
    method: 'post',
    data: params
  });
}

// 再次发起
export function afterwardsRetry(params) {
  return Http({
    url: `sqlreview/review/afterwards_retry`,
    method: 'get',
    params
  });
}

// pg再次发起
export function pgafterwardsRetry(params) {
  return Http({
    url: `/sqlreview/after_audit/reissue`,
    method: 'get',
    params
  });
}
// SQL详情
export function sqlDetailInfo(params) {
  return Http({
    url: `sqlreview/review/afterwards_detail`,
    method: 'get',
    params
  });
}

export function sqlDetailInfoMysql(params) {
  return Http({
    url: `sqlreview/review/afterwards_mysql_detail`,
    method: 'get',
    params
  });
}

// 索引优化建议
export function sqlAdviceInfo(params) {
  return Http({
    url: `sqlreview/review/sql_advice`,
    method: 'get',
    params
  });
}

// 事后审核
export function afterwardsReviewMiddle(params) {
  return Http({
    url: `sqlreview/review/afterwards_review`,
    method: 'get',
    params
  });
}

// 开发工作台-统计数据
export function workBenchGetMain(params) {
  return Http({
    url: `/sqlreview/review/work_bench/get_main/`,
    method: 'get',
    params
  });
}

// 开发工作台-不通过原因
export function workBenchGetReason(params) {
  return Http({
    url: `/sqlreview/review/work_bench/get_reason/`,
    method: 'get',
    params
  });
}
// 用户手册下载
export function downLoadUserManual(params = {}) {
  return Http({
    url: `/sqlreview/common/down_load_user_manual`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}
// 一键备份
export function backup(params = {}) {
  return Http({
    url: `/sqlreview/project_config/download_backup_zip`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}
// 事前审核详情列表页
export function beforeReviewDetailPage(params) {
  return Http({
    url: `/sqlreview/review/review-detail-list/`,
    method: 'get',
    params
  });
}
// 峰值调用频率查询接口
export function maxFrequencyDynamicForm(params) {
  return Http({
    url: `/sqlreview/project_config/max-frequency-dynamic-form`,
    method: 'get'
  });
}
// 开发人员工作台获取初始数据
export function getInitData(params) {
  return Http({
    url: `/sqlreview/review/list1`,
    method: 'post',
    data: params
  });
}
// 查询提交评审的未审核的sql
export function getSubmitMessage(params) {
  return Http({
    url: `/sqlreview/review/review_submit_message`,
    method: 'get',
    params: params
  });
}

// 事后审核下载报告
export function afterwardsReortExport(params) {
  return Http({
    url: `/sqlreview/review/afterwards_reort_export`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 事后审核pg查看报告
export function getChartData(params) {
  return Http({
    url: `/sqlreview/after_audit/postgre_chart`,
    method: 'post',
    data: params
  });
}

// 事后审核pgsqldetail页，获取数据
export function getPostgreReportDetail(params) {
  return Http({
    url: `/sqlreview/after_audit/postgre_report_detail`,
    method: 'get',
    params: params
  });
}

// 事前审核 新建审核对象 是否多git扫描
export function sqlreviewProjectCheckGit(params) {
  return Http({
    url: `/sqlreview/project/check_git`,
    method: 'get',
    params: params
  });
}
// 事前审核 SQLSERVER report页，获取chart数据
export function getSqlServerChart(params) {
  return Http({
    url: `/sqlreview/after_audit/sqlserver_chart`,
    method: 'post',
    data: params
  });
}

// 事前审核 SQLSERVER plan页，获取数据
export function getSqlserverCheckDetail(params) {
  return Http({
    url: `/sqlreview/after_audit/sqlserver_report_detail`,
    method: 'get',
    params: params
  });
}

// 历史标准基线弹窗
export function editHistoryBaseline(params) {
  return Http({
    url: `/sqlreview/review/history`,
    method: 'post',
    data: params
  });
}
// 删除行
export function removeRecord(params) {
  return Http({
    url: `/sqlreview/review/del_record`,
    method: 'post',
    data: params
  });
}
// 终止行
export function terminationRecord(params) {
  return Http({
    url: `/sqlreview/review/termination_record`,
    method: 'post',
    data: params
  });
}

// 打标
export function saveLabel(params) {
  return Http({
    url: `/sqlreview/review/save_label/`,
    method: 'post',
    data: params
  });
}

// 删除打标
export function deleteLabel(params) {
  return Http({
    url: `/sqlreview/review/delete_label/`,
    method: 'post',
    data: params
  });
}

// sql备注-获取sqlmap表
export function getSqlmapTable(params) {
  return Http({
    url: `/sqlreview/review/get_sqlmap_table/`,
    method: 'get',
    params
  });
}

// 保存SQL备注
export function saveSqlMap(params) {
  return Http({
    url: `/sqlreview/review/save_sqlmap/`,
    method: 'post',
    data: params
  });
}
export default {};
