<template>
  <div class="data-base-audi-config">
    <div class="top-info">
      <div class="left">
        <Form
          ref="form"
          v-bind="formParams"
          :formData="formData"
          :iconCombine="true"
          class="search-form"
        ></Form>
        <div class="seach-area-btns">
          <a-button @click="search" type="primary">查询</a-button>
          <a-tooltip>
            <template slot="title">重置</template>
            <custom-icon type="lu-icon-clean" @click="reset" />
          </a-tooltip>
        </div>
      </div>
      <div class="right">
        <a-button @click="batchAction(1)" class="batch-open">批量开启</a-button>
        <a-button @click="batchAction(0)" class="batch-close"
          >批量关闭</a-button
        >
      </div>
    </div>
    <Table
      ref="table"
      v-bind="tableParams"
      :dataSource="dataSource || []"
      class="new-view-table small-size"
      @selectChange="selectChange"
    >
      <template slot="after_audit_status" slot-scope="{ text, record }">
        <custom-icon type="lu-icon-loading" v-if="text == -1"></custom-icon>
        <a-switch
          v-else
          checked-children="开"
          un-checked-children="关"
          :checked="text === 1"
          @change="onChange(record)"
        />
      </template>
      <template slot="error_message" slot-scope="{ text }">
        <LimitLabel :label="text || ''" :limit="24"></LimitLabel>
      </template>

      <div slot="name" slot-scope="{ text, record }" class="name-box">
        <Tag type="Env" :text="record.env.toUpperCase()" />
        <DbImg
          value="OCEANBASE"
          mode="ellipsis"
          :schemaName="text + '(' + record.db_url + ')'"
          v-if="record.db_type == 'OB_MYSQL' || record.db_type == 'OB_ORACLE'"
        />
        <DbImg
          value="TDSQL"
          mode="ellipsis"
          :schemaName="text + '(' + record.db_url + ')'"
          v-else-if="
            record.db_type == 'TD_MYSQL' || record.db_type == 'TD_PGSQL'
          "
        />
        <DbImg
          v-else
          :value="record.db_type"
          :schemaName="text + '(' + record.db_url + ')'"
          mode="ellipsis"
        />
      </div>
      <template slot="collect_type" slot-scope="{ record }">
        <span>{{ record.collect_type_display || '--' }}</span>
      </template>
      <template slot="collect_frequency" slot-scope="{ record }">
        <span>{{ record.collect_frequency_display || '--' }}</span>
      </template>
      <template slot="statistical_interval" slot-scope="{ record }">
        <span>{{ record.statistical_interval_display || '--' }}</span>
      </template>
      <custom-btns-wrapper slot="action" slot-scope="{ record }">
        <a actionBtn @click="collect(record)">编辑</a>
        <a
          actionBtn
          @click="toDetail(record)"
          :disabled="record.after_audit_status == 0"
          >任务详情</a
        >
      </custom-btns-wrapper>
    </Table>
    <!-- 采集配置 -->
    <CollectConfigDrawer
      ref="collectConfig"
      @save="saveConfig"
    ></CollectConfigDrawer>
    <!-- 批量操作弹窗 -->
    <BatchAction ref="batchAction" @save="saveConfig"></BatchAction>
  </div>
</template>

<script>
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import Form from '@/components/Form';
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import CollectConfigDrawer from './components/CollectConfigDrawer';
import BatchAction from './components/batchAction/index';
import config from './config';
import { collectSwitch } from '@/api/databaseaudit/auditconfig';
export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Tag,
    Form,
    DbImg,
    Table,
    LimitLabel,
    BatchAction,
    CollectConfigDrawer
  },
  props: {
    type: String
  },
  data() {
    this.config = config(this);
    return {
      formData: {},
      formParams: {
        layout: 'horizontal',
        multiCols: 3,
        fields: this.config.searchFields
      },
      dataSource: [],
      tableParams: {
        url: `/sqlreview/after_audit/db_collect/list`,
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id',
        // rowSelection: {
        //   type: 'checkbox',
        //   columnWidth: '40',
        //   getCheckboxProps: record => ({
        //     // 选择框的默认属性配置
        //     props: {
        //       disabled: false
        //     }
        //   })
        // },
        scroll: { x: 'max-content' }
      },
      selectedRowKeys: []
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    // 查询
    search() {
      const { table, form } = this.$refs;
      const data = form.getData();
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    },
    // 重置
    reset() {
      this.formData = {};
      const { table } = this.$refs;
      table.searchParams = {};
      table.refresh();
    },
    // 打开采集配置弹窗
    collect(record) {
      this.$refs.collectConfig.show(record);
    },
    // 采集配置保存后 刷新表格
    saveConfig() {
      const { table } = this.$refs;
      table.refreshKeep();
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    // 批量开启/关闭
    batchAction(num) {
      this.$refs.batchAction.show(num);
    },
    // 权限控制 启用禁用
    onChange(data) {
      if (!data.is_configured) {
        this.$refs.collectConfig.show(data);
        return;
      }
      this.$showLoading();
      collectSwitch({
        after_audit_status: data.after_audit_status == 0 ? 1 : 0,
        data_source_id: data.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const { table } = this.$refs;
            table.refreshKeep(null, { _clear: true });
            this.selectedRowKeys = [];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    toDetail(record) {
      if (
        record.db_type == 'ORACLE' ||
        (record.collect_type == 'openapi' && record.db_type == 'MYSQL')
      ) {
        this.$router.push({
          name: 'audit-config-detail',
          query: {
            id: record.id,
            type: record.collect_type,
            db_type: record.db_type
          }
        });
      } else {
        this.$router.push({
          name: 'schedule-job',
          query: {
            id: record.id
          }
        });
      }
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.data-base-audi-config {
  background: #fff;
  border-radius: 16px;
  /deep/.top-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 12px 24px;
    .left {
      width: 70%;
      display: flex;
      .ant-form {
        flex-grow: 1;
        height: 48px;
        overflow: hidden;
        * {
          transition: none;
        }
        > .ant-row {
          display: flex;
          > .ant-col {
            vertical-align: top;
            padding: 0 12px !important;
            .ant-form-item {
              display: flex;
              margin: 0 0 8px 0;
              .ant-form-item-label {
                display: none;
              }
              .ant-form-item-control-wrapper {
                flex-grow: 1;
                flex: auto;
                width: auto;
              }
            }
          }
        }
      }
      .seach-area-btns {
        padding-top: 4px;
        white-space: nowrap;
        .ant-btn {
          padding: 2px 12px;
          // border-radius: 4px !important;
          // border: 1px solid #a5d9f8;
          > span {
            // font-size: 14px;
            // color: #fff;
            // font-weight: 400;
          }
        }
        .anticon {
          font-size: 16px;
          margin: 0 12px;
          border-radius: 50%;
          &:hover {
            color: #000;
            cursor: pointer;
          }
        }
      }

      &.icon-combine {
        .ant-form {
          > .ant-row {
            > .ant-col {
              padding-left: 24px !important;
              padding-right: 0 !important;
              .ant-form-item {
                .ant-form-item-label {
                  max-width: 0;
                  min-width: 0;
                }
                .ant-form-item-control-wrapper {
                  flex-grow: 1;
                  flex: auto;
                  width: auto;
                  max-width: 100%;
                  min-width: 310px;
                }
              }
            }
          }
        }
        .seach-area-btns {
          margin-left: 24px;
        }
      }
    }
    .right {
      display: flex;
      justify-content: flex-end;
      width: 30%;
      margin-bottom: 12px;
      padding-top: 4px;
      white-space: nowrap;
      .ant-btn {
        padding: 2px 12px;
        margin-left: 8px;
        border-radius: 4px !important;
        border: 1px solid #a5d9f8;
        > span {
          font-size: 14px;
          color: #008adc;
          font-weight: 400;
        }
        &:hover {
          > span {
            color: #25a7e8;
          }
        }
      }
    }
  }
  /deep/ .custom-table {
    .name-box {
      display: flex;
      align-items: center;
      .ant-tag {
        border-radius: 6px;
        white-space: nowrap;
        color: #fff !important;
      }
      .limit-label {
        width: 160px;
      }
    }
  }
}
@media screen and (max-width: 1620px) {
  .data-base-audi-config {
    /deep/ .custom-table {
      .name-box {
        .limit-label {
          width: 120px;
        }
      }
    }
  }
}
</style>