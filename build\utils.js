'use strict'
const path = require('path')
const config = require('../config')
const ExtractTextPlugin = require('extract-text-webpack-plugin')
const packageConfig = require('../package.json')
const themeConfig = require('../src/style/themes/index')
const { getChannelInfo } = require('../config/channel');
const channelDetail = getChannelInfo();

exports.assetsPath = function (_path) {
  const assetsSubDirectory = process.env.NODE_ENV === 'production'
    ? config.build.assetsSubDirectory
    : config.dev.assetsSubDirectory

  return path.posix.join(assetsSubDirectory, _path)
}

exports.cssLoaders = function (options) {
  options = options || {}

  const cssLoader = {
    loader: 'css-loader',
    options: {
      sourceMap: options.sourceMap
    }
  }

  const postcssLoader = {
    loader: 'postcss-loader',
    options: {
      sourceMap: options.sourceMap
    }
  }

  // generate loader string to be used with extract text plugin
  function generateLoaders(loader, loaderOptions) {
    const loaders = options.usePostCSS ? [cssLoader, postcssLoader] : [cssLoader]

    if (loader) {
      loaders.push({
        loader: loader + '-loader',
        options: Object.assign({}, loaderOptions, {
          sourceMap: options.sourceMap
        })
      })
      if (loader === 'less') {
        loaders.push({
          loader: 'sass-resources-loader',
          options: Object.assign({}, {
            resources: path.resolve(__dirname, '../src/style/var.less')
          }, {
            sourceMap: options.sourceMap
          })
        })
      }
    }

    // Extract CSS when that option is specified
    // (which is the case during production build)
    if (options.extract) {
      return ExtractTextPlugin.extract({
        use: loaders,
        fallback: 'vue-style-loader',
        publicPath: '../../'
      })
    } else {
      return ['vue-style-loader'].concat(loaders)
    }
  }

  // https://vue-loader.vuejs.org/en/configurations/extract-css.html
  return {
    css: generateLoaders(),
    postcss: generateLoaders(),
    less: generateLoaders('less', { // If you are using less-loader@5 please spread the lessOptions to options directly
      modifyVars: themeConfig[channelDetail.theme] || {},
      javascriptEnabled: true
      // globalVars: {
      //   'primary-color': '#1890ff', // 全局主色
      //   'link-color': '#1890ff', // 链接色
      //   'success-color': '#52c41a', // 成功色
      //   'warning-color': '#faad14', // 警告色
      //   'error-color': '#f5222d', // 错误色
      //   'font-size-base': '14px', // 主字号
      //   'heading-color': 'rgba(0, 0, 0, 0.85)', // 标题色
      //   'text-color': 'rgba(0, 0, 0, 0.65)', // 主文本色
      //   'text-color-secondary': 'rgba(0, 0, 0, 0.45)', // 次文本色
      //   'disabled-color': 'rgba(0, 0, 0, 0.25)', // 失效色
      //   'border-radius-base': '4px', // 组件/浮层圆角
      //   'border-color-base': '#d9d9d9', // 边框色
      //   'box-shadow-base': '0 2px 8px rgba(0, 0, 0, 0.15)' // 浮层阴影
      // }
      // resources: path.resolve(__dirname, '../src/style/app.less')
    }),
    sass: generateLoaders('sass', { indentedSyntax: true }),
    scss: generateLoaders('sass'),
    stylus: generateLoaders('stylus'),
    styl: generateLoaders('stylus')
  }
}

// Generate loaders for standalone style files (outside of .vue)
exports.styleLoaders = function (options) {
  const output = []
  const loaders = exports.cssLoaders(options)

  for (const extension in loaders) {
    const loader = loaders[extension]
    output.push({
      test: new RegExp('\\.' + extension + '$'),
      use: loader
    })
  }

  return output
}

exports.createNotifierCallback = () => {
  const notifier = require('node-notifier')

  return (severity, errors) => {
    if (severity !== 'error') return

    const error = errors[0]
    const filename = error.file && error.file.split('!').pop()

    notifier.notify({
      title: packageConfig.name,
      message: severity + ': ' + error.name,
      subtitle: filename || '',
      icon: path.join(__dirname, 'logo.png')
    })
  }
}
