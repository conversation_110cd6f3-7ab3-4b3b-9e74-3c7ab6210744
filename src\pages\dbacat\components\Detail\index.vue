<template>
  <div class="page-dbacat-detail-wrapper">
    <template v-for="(item, index) in data">
      <Item :data="item" :key="index" />
    </template>
  </div>
</template>

<script>
import Item from './Item.vue';

export default {
  components: { Item },
  data() {
    return {
      data: Array(20).fill()
    };
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.page-dbacat-detail-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-top: 32px;
}
</style>
