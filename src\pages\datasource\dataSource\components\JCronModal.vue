<template>
  <Form class="jcron-form" ref="form" v-bind="params" :formData="formData"></Form>
</template>
<script>
import Form from '@/components/Form';
import config from './config';
import moment from 'moment';
export default {
  components: { Form },
  name: 'VueCron',
  props: {
    data: {
      type: String,
      default: ''
    }
  },
  data() {
    this.config = config(this);
    return {
      params: {
        fields: this.config.cornInfo,
        multiCols: 3
      },
      formData: {},
      time: '',
      weekOption: [
        {
          label: '星期一',
          value: '2',
          cron: 2
        },
        {
          label: '星期二',
          value: '3',
          cron: 3
        },
        {
          label: '星期三',
          value: '4',
          cron: 4
        },
        {
          label: '星期四',
          value: '5',
          cron: 5
        },
        {
          label: '星期五',
          value: '6',
          cron: 6
        },
        {
          label: '星期六',
          value: '7',
          cron: 7
        },
        {
          label: '星期日',
          value: '1',
          cron: 1
        }
      ],
      monthOption: []
    };
  },
  created() {},
  mounted() {
    this.initData();
  },
  watch: {
    data(newData) {
      if (newData) {
        this.initData();
      }
    }
  },
  methods: {
    show() {},
    initData() {
      let arr = [];
      for (let i = 1; i < 32; i++) {
        arr.push({
          label: i + '号',
          value: i,
          cron: i
        });
      }
      this.monthOption = arr;
      if (this.data) {
        let labelArr = this.data.split(' ') || [];
        let time =
          `${labelArr[2]}` + ':' + `${labelArr[1]}` + ':' + `${labelArr[0]}`;
        this.formData = Object.assign({}, this.formData, {
          time: moment(time, 'HH:mm:ss')
        });
        if (labelArr[2] === '*') {
          let time = `${labelArr[1]}` + ':' + `${labelArr[0]}`;
          this.formData = Object.assign({}, this.formData, {
            time: moment(time, 'mm:ss')
          });
          this.formData = Object.assign({}, this.formData, { type: 'hour' });
        } else if (labelArr[3] === '*') {
          this.formData = Object.assign({}, this.formData, { type: 'day' });
        }
        if (labelArr[3] === '?') {
          this.weekOption.find(val => {
            if (val.cron === Number(labelArr[5])) {
              this.formData = Object.assign({}, this.formData, {
                type: 'week',
                week: val.value
              });
            }
          });
        }
        if (Number(labelArr[3]) > 0) {
          this.monthOption.find(val => {
            if (val.cron === Number(labelArr[3])) {
              this.formData = Object.assign({}, this.formData, {
                type: 'month',
                month: val.value
              });
            }
          });
        }
      }
    },
    handleSubmit() {
      let data = this.$refs.form.getData();
      if (data.time) {
        if (data.type == 'hour') {
          this.time = data.time.format('mm:ss');
        } else {
          this.time = data.time.format('HH:mm:ss');
        }
      }
      let dataArr = [];
      let timeCron;
      let clockCornArr = this.time.split(':').reverse();
      if (data.type == 'day') {
        dataArr.push(data.type, this.time);
        timeCron = clockCornArr.join(' ') + ' * * ?';
      } else if (data.type == 'month') {
        dataArr.push(data.type, data.month, this.time);
        timeCron = clockCornArr.join(' ') + ' ' + data.month + ' * ?';
      } else if (data.type == 'week') {
        dataArr.push(data.type, data.week, this.time);
        timeCron = clockCornArr.join(' ') + ' ? * ' + data.week;
      } else if (data.type == 'hour') {
        dataArr.push(data.type, data.hour, this.time);
        timeCron = clockCornArr.join(' ') + ' * * * * ';
      }
      this.value = dataArr.join(',');
      this.$emit('cronExpression', timeCron, data.type);
    },
    close() {
      this.time = '';
    }
  }
};
</script>

<style lang="less" scoped>
.form {
  // display: flex;
  // justify-content: space-between;
}
.footer {
  text-align: right;
  margin-top: 10px;
}
.jcron-form {
  /deep/ .ant-form-item {
    padding-bottom: 0;
    margin-bottom: 0;
  }
}
</style>