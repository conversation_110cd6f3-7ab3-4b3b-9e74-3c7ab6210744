<template>
  <div class="biz-private-bigfileuplaod">
    <a-upload
      :file-list="[]"
      :remove="handleRemove"
      :before-upload="beforeUpload"
    >
      <a-button> <a-icon type="upload" />上传</a-button>
    </a-upload>
    <!-- <a-button @click="onPause">暂停</a-button>
    <a-button @click="onResume">恢复</a-button> -->
  </div>
</template>

<script>
import BigFileUpload from './upload';

export default {
  components: {},
  props: {},
  data() {
    return {
      fileList: []
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.fileList = newFileList;
    },
    beforeUpload(file) {
      this.fileList = [...this.fileList, file];
      console.log(file);
      this.instance = new BigFileUpload();
      this.instance.run({
        file,
        reqInstance: () =>
          new Promise(resolve => {
            setTimeout(() => {
              resolve();
            }, 2000);
          })
      });
      return false;
    },
    onPause() {
      if (this.instance) {
        this.instance.pause();
      }
    },
    onResume() {
      if (this.instance) {
        this.instance.resume();
      }
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.biz-private-bigfileuplaod {
  display: flex;
  > * {
    margin-right: 8px;
  }
}
</style>
