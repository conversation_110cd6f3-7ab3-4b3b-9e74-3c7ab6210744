<template>
  <div class="config-datasource-import-and-export">
    <a-tabs
      default-active-key="import"
      :animated="false"
      class="card"
      type="card"
      @change="tabChange"
    >
      <a-tab-pane key="import">
        <span slot="tab" class="tab-area">
          <custom-icon type="lu-icon-import" />
          <span class="tab-title">信息导入</span>
        </span>
        <Import ref="importInfo" />
      </a-tab-pane>
      <a-tab-pane key="export">
        <span slot="tab" class="tab-area">
          <custom-icon type="lu-icon-pass" />
          <span class="tab-title">信息导出</span>
        </span>
        <Export ref="exportInfo" />
      </a-tab-pane>
      <a-tab-pane key="history">
        <span slot="tab" class="tab-area">
          <custom-icon type="history" />
          <span class="tab-title">历史记录</span>
        </span>
        <History />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import Import from './Import';
import Export from './Export';
import History from './History';
import config from './config';

export default {
  name: 'import-and-export',
  components: { Import, Export, History },
  props: {},
  data() {
    this.config = config(this);
    return {
      activeKey: ''
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    tabChange(activeKey) {
      this.activeKey = activeKey;
      const { importInfo, exportInfo } = this.$refs;
      importInfo && importInfo.carryOn();
      exportInfo && exportInfo.carryOn();
    }
  }
};
</script>

<style lang="less" scoped>
.config-datasource-import-and-export {
  /deep/.ant-tabs-bar {
    .ant-tabs-nav-container {
      .ant-tabs-nav-scroll {
        .ant-tabs-nav {
          > div {
            .ant-tabs-tab {
              border-radius: 0;
              border-bottom: none;
              background: rgba(243, 244, 246, 0.5);
              transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
              margin-left: 16px;
              .tab-area {
                .anticon {
                  font-size: 16px;
                  color: #a1a1a1;
                }
                .tab-title {
                  font-size: 16px;
                }
              }
              &:hover {
                .tab-area {
                  .anticon {
                    font-size: 16px;
                    color: rgb(37, 167, 232);
                  }
                }
              }
              &.ant-tabs-tab-active {
                border-top: 2px solid #219be3;
                border-bottom: none;
                background: #fff;
                .tab-area {
                  .anticon {
                    font-size: 16px;
                    color: #008adc;
                  }

                  .tab-title {
                    color: #008adc;
                    font-weight: 600;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
