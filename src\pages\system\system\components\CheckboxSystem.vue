<template>
  <div class="checkbox-system">
    <a-checkbox-group :value="id" @change="onChange">
      <a-row>
        <a-col
          :span="24"
          :key="item.id"
          class="line"
          v-for="item in (data &&
          data.sql_review_dynamic_form_element) ||
        []"
        >
          <a-checkbox :value="item.id">
            <div class="line-label">{{ item.element_name }}</div>
            <div class="line-text">{{ item.element_desc }}</div>
          </a-checkbox>
        </a-col>
      </a-row>
    </a-checkbox-group>
  </div>
</template>

<script>
export default {
  props: {
    value: String,
    data: Object
  },
  data() {
    return {
      id: []
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    onChange(checkedValues) {
      // console.log('checked = ', checkedValues);
      this.id = checkedValues;
    },
    getData() {
      let res = [];
      const data = this.data && this.data.sql_review_dynamic_form_element;
      data.forEach(item => {
        this.id.forEach(elem => {
          if (item.id == elem) {
            res.push(item.element_value);
          }
        });
      });
      return res;
    }
  },
  watch: {
    value: {
      handler(newVal) {
        const list = newVal.split(',');
        const data = this.data && this.data.sql_review_dynamic_form_element;
        data.forEach(item => {
          list.forEach(elem => {
            if (item.element_value == elem) {
              this.id.push(item.id);
            }
          });
        });
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.checkbox-system {
  .title {
    font-size: 16px;
    font-weight: 400;
    color: #333;
  }
  .line {
    display: flex;
    align-items: center;
    margin-top: 20px;
    /deep/ span:nth-of-type(2) {
      display: flex;
      align-items: center;
    }
    /deep/ span:nth-of-type(2) div:nth-of-type(1) {
      width: 80px;
      margin-right: 10px;
    }
    .line-text {
      margin-left: 20px;
      color: #7f7f7f;
      display: flex;
    }
  }
  /deep/ .ant-checkbox-wrapper {
    display: flex;
    text-align: start;
    align-self: center;
    .ant-checkbox {
      margin-top: 3px;
    }
    .ant-checkbox + span {
      padding-right: 8px;
      padding-left: 16px;
    }
  }
}
</style>
