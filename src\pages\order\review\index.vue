<template>
  <div class="order-detail-container">
    <CodeReviewReview ref="codeReviewReview" v-if="activeKey == 'sqlreview'">
    </CodeReviewReview>
    <DataSourceReviewReview ref="codeReviewReview" v-else>
    </DataSourceReviewReview>
  </div>
</template>

<script>
import bodyMinWidth from '@/mixins/bodyMinWidth';
import CodeReviewReview from './CodeReviewReview';
import DataSourceReviewReview from './DataSourceReviewReview';

export default {
  name: 'orderReview',
  components: {
    CodeReviewReview,
    DataSourceReviewReview
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  computed: {},
  data() {
    return {
      activeKey: 'sqlreview'
    };
  },
  mounted() {},
  created() {},
  methods: {},
  watch: {
    '$route.query.activeKey': {
      handler(newVal) {
        if (newVal) {
          this.activeKey = newVal;
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less">
</style>
