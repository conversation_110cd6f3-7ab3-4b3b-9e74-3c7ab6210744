
import { routesMap } from '@/router/lazy';
import Vue from 'vue';

// 处理table columns绑定
const combineColumns = function (columns = [], infos = {}) {
  columns.forEach(item => {
    if (!item) return;
    let matchItem = infos[item.key];
    if (matchItem) {
      // 需要绑定
      Object.assign(item, matchItem);
    } else if (item.children && item.children.length > 0) {
      // 递归处理
      combineColumns(item.children, infos);
    }
  })
  // console.log(columns, 999)
}

// 获取表格slots
const getTableSlots = function (columns = []) {
  let slots = [];
  let scopedSlots = [];
  let columnMap = {};

  function deal(list) {
    list.forEach(item => {
      if (!item) return;
      columnMap[item.key] = item;
      if (item.slots) {
        slots = [...slots, ...Object.values(item.slots)];
      }
      if (item.scopedSlots) {
        scopedSlots = [...scopedSlots, ...Object.values(item.scopedSlots)];
      }
      if (item.children && item.children.length > 0) {
        deal(item.children);
      }
    });
  }
  deal(columns);

  return {
    tableSlots: [...new Set(slots)],
    tableScopedSlots: [...new Set(scopedSlots)],
    columnMap
  };
}

// 获取查询参数
const getQueryParams = function (url, name) {
  let path = url || window.location.href;
  let match = path.match(/(\?[^/#]*)/ig);
  let map = {};
  if (match) {
    match.forEach(matchItem => {
      matchItem.slice(1).split('&').map(item => {
        const keyItem = item.match(/^([^&=]+)=(.+)/, '');
        map[keyItem[1]] = decodeURIComponent(keyItem[2]);
      });
    })
  }
  if (name) return map[name];
  return map;
};
// 对象转换成路径参数 带问号
const objToQuery = (obj = {}, joinStr = '&') => {
  const keys = Object.keys(obj) || [];
  if (keys.length == 0) return '';
  const retArr = [];
  keys.forEach(el => {
    obj[el] && retArr.push(`${el}=${obj[el]}`);
  });
  const retStr = retArr.length > 0 ? '?' + retArr.join(joinStr) : '';
  return retStr;
};

// 自行设置navis
const setNavis = function (ctx, getPath) {
  const { path, meta = {}, name } = ctx.$route;
  const { navi, desc, icon } = meta;
  let navis = [];
  navi.forEach(key => {
    if (key === name) {
      // 当前页
      navis.push({
        name: desc,
        icon: icon,
        path
      });
    } else {
      const item = routesMap[key];
      const { path, meta = {} } = item;
      const _path = getPath(key, path);
      if (_path) {
        navis.push({
          name: meta.desc,
          icon: meta.icon,
          path: _path
        });
      } else {
        navis.push({
          name: meta.desc,
          icon: meta.icon,
          path: item.path
        });
      }
    }
  });
  ctx.$store.commit('common/setNavi', navis);
}

// 下载
function downLoad(ctx, res) {
  const contentType = _.get(res, 'headers.content-type').toLowerCase();
  if (contentType === 'application/json' && !isSuccessCode(res)) {
    let reader = new FileReader();
    reader.onload = e => {
      try {
        const realRes = JSON.parse(e.target.result);
        ctx.$hideLoading({
          method: 'error',
          tips: _.get(realRes, 'message')
        });
      } catch (e) {
        console.log(e);
        ctx.$hideLoading({
          method: 'error',
          tips: _.get(res, 'data.message')
        });
      }
    };
    reader.readAsText(res.data);
    return;
  }
  ctx.$hideLoading({ tips: '下载成功！' });
  downloadByBlob(res);
}

// 下载文件
function downloadByBlob(response) {
  // 提取文件名
  const fileName = response.headers['content-disposition'].match(
    /filename=(.*)/i
  )[1].replace(new RegExp('"', 'g'), '')
  // 将二进制流转为blob
  const blob = new Blob([response.data], { type: 'application/octet-stream' })
  if (typeof window.navigator.msSaveBlob !== 'undefined') {
    // 兼容IE，window.navigator.msSaveBlob：以本地方式保存文件
    window.navigator.msSaveBlob(blob, decodeURIComponent(fileName))
  } else {
    // 创建新的URL并指向File对象或者Blob对象的地址
    const blobURL = window.URL.createObjectURL(blob)
    // 创建a标签，用于跳转至下载链接
    const tempLink = document.createElement('a')
    tempLink.style.display = 'none'
    tempLink.href = blobURL
    tempLink.setAttribute('download', decodeURIComponent(fileName))
    // 兼容：某些浏览器不支持HTML5的download属性
    if (typeof tempLink.download === 'undefined') {
      tempLink.setAttribute('target', '_blank')
    }
    // 挂载a标签
    document.body.appendChild(tempLink)
    tempLink.click()
    document.body.removeChild(tempLink)
    // 释放blob URL地址
    window.URL.revokeObjectURL(blobURL)
  }
}

// 判断接口返回是否是成功码
const isSuccessCode = function (res) {
  const successCode = GLOBAL_CONFIG.successCode;
  if (Array.isArray(successCode)) {
    return successCode.find(item => item == _.get(res, 'data.code')) != null;
  }
  return _.get(res, 'data.code') == '0000' || _.get(res, 'data.code') == '0' || _.get(res, 'data.code') == '200'
}

// 散列数组转为树结构
const toTree = function (list = [], params = {}) {
  let obj = {};
  let result = [];
  const { parentId = 'parent_id', rootId = 0, rowKey = 'id', dealItem, sorter } = params;
  // 将数组中数据转为键值对结构 (这里的数组和obj会相互引用)
  list.map(el => {
    let item = dealItem ? dealItem(el) : el;
    obj[item[rowKey]] = item;
    item.children = [];
  });
  for (let i = 0, len = list.length; i < len; i++) {
    let item = obj[list[i][rowKey]];
    let pid = item[parentId];
    if (pid == rootId) {
      result.push(item);
      continue;
    }
    if (obj[pid]) {
      if (obj[pid].children) {
        obj[pid].children.push(item);
      } else {
        obj[pid].children = [item];
      }
    }
  }
  if (sorter) {
    _.forEach(obj, item => {
      if (item && item.children && item.children.length > 0) {
        item.children = sorter(item.children);
      }
    })
    result = sorter(result);
  }
  return result;
}

function uuid() {
  var s = [];
  var hexDigits = '0123456789abcdef';
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-';

  var uuid = s.join('');
  return uuid;
}

// 计算动态的px
const calDynamicPixel = (num, params = {}) => {
  const { min } = params;
  let res = num || 0;
  res = res * (document.body.offsetWidth / 1920);
  res = res < min ? min : res;
  return res;
}

// 展开、折叠
const toggleDom = ({ element, time = 0, startHeight, show }) => {
  if (!element) {
    console.error('元素不存在');
    return;
  }
  if (startHeight == null) {
    console.error('没有startHeight');
    return;
  }
  if (typeof window.getComputedStyle == 'undefined') {
    element.style.height = 'auto';
    element.style.overflow = 'visible';
    return;
  }

  // 获取实际高度
  element.style.transition = 'none'; // 本行2015-05-20新增，mac Safari下，貌似auto也会触发transition, 故要none下~
  element.style.height = 'auto';
  let targetHeight = window.getComputedStyle(element).height;

  if (show) {
    element.style.height = startHeight;
    if (time) element.style.transition = 'height ' + time + 's';
    setTimeout(() => {
      element.style.height = targetHeight;
    });
    setTimeout(() => {
      element.style.height = 'auto';
    }, time * 1000);
  } else {
    element.style.height = targetHeight;
    if (time) element.style.transition = 'height ' + time + 's';
    setTimeout(() => {
      element.style.height = startHeight;
    });
  }
}

// table组件filter函数
const tableFilters = {
  includes: (value, record, column = 'column_name') => {
    return (record[column] != null ? record[column] + '' : '')
      .toString()
      .toLowerCase()
      .includes(value.toLowerCase())
  }
}

// 表格同名列合并
const getTableCombineInfo = (combineColumns = [], data = []) => {
  // const combineColumns = ['table_name', 'index_name'];
  // let map = { table_name: {}, index_name: {} };
  let map = {};
  combineColumns.forEach(item => {
    map[item] = {};
  });
  data.forEach((item, index) => {
    combineColumns.forEach(key => {
      let colVal = item[key];
      let colMap = map[key];
      // console.log(colVal, colMap[colVal], index);
      if (colVal != null) {
        let uid = colVal + '_' + index;
        colMap[uid] = {
          index,
          rowSpan: 1
        };
        // 和前面值相同
        if (index > 0 && data[index - 1][key] == colVal) {
          colMap[uid].rowSpan = 0;
          let pid = colVal + '_' + (index - 1);
          if (!colMap[pid].parent) {
            colMap[pid].rowSpan += 1;
            colMap[uid].parent = colMap[pid];
          } else {
            colMap[pid].parent.rowSpan += 1;
            colMap[uid].parent = colMap[pid].parent;
          }
        }
      }
    });
  });

  console.log(map);
  return map;
}

// 设置表格列为配置排序
const setColumnsSort = (column = [], columnSort = [], extendColumn = []) => {
  const retColumn = [];
  columnSort.map(item => {
    const col = column.find(colItem => colItem.key == item.key);
    if (col) {
      retColumn.push({
        ...col,
        ...item
      })
    }
  })
  if (extendColumn.length > 0) {
    extendColumn.map(item => {
      retColumn.push(item);
    })
  }
  return retColumn;
}

// 驼峰式命名转换为下划线
const hump2UnderLine = (s) => {
  return s.replace(/([A-Z])/g, '_$1').toLowerCase();
}
const jsonToUnderLine = (obj, only) => {
  let newObj = obj;
  if (newObj instanceof Array) {
    newObj = obj.map((v, i) => {
      return jsonToUnderLine(v, only)
    })
  } else if (newObj instanceof Object) {
    newObj = {};
    const newKeysArray = only && only.length > 0 ? only : Object.keys(obj);
    newKeysArray.map(key => {
      newObj[hump2UnderLine(key)] = obj[key];
    });
  }
  return newObj;
}

// 数字转成汉字
const toChinesNum = (num) => {
  let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  let unit = ['', '十', '百', '千', '万']
  num = parseInt(num)
  let getWan = (temp) => {
    let strArr = temp.toString().split('').reverse()
    let newNum = ''
    let newArr = []
    strArr.forEach((item, index) => {
      newArr.unshift(item === '0' ? changeNum[item] : changeNum[item] + unit[index])
    })
    let numArr = []
    newArr.forEach((m, n) => {
      if (m !== '零') numArr.push(n)
    })
    if (newArr.length > 1) {
      newArr.forEach((m, n) => {
        if (newArr[newArr.length - 1] === '零') {
          if (n <= numArr[numArr.length - 1]) {
            newNum += m
          }
        } else {
          newNum += m
        }
      })
    } else {
      newNum = newArr[0]
    }

    return newNum
  }
  let overWan = Math.floor(num / 10000)
  let noWan = num % 10000
  if (noWan.toString().length < 4) {
    noWan = '0' + noWan
  }
  return overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num)
}

// 复制文本
const copy = ({ value, callback, allowEmpty }) => {
  if (allowEmpty === false) {
    Vue.prototype.$message.warning('复制值为空，请检查！');
    return;
  }
  var input = document.createElement('input'); // 创建input对象
  input.value = value; // 设置复制内容
  document.body.appendChild(input); // 添加临时实例
  input.select(); // 选择实例内容
  document.execCommand('Copy'); // 执行复制
  document.body.removeChild(input); // 删除临时实例
  if (_.isFunction(callback)) {
    callback()
  } else {
    Vue.prototype.$message.success('复制成功');
  }
}

// 获取系统唯一key（系统code + 路由name + 后缀）
const getSystemUniqKey = (ctx, postfix) => {
  return GLOBAL_CONFIG.ProjectCode + (ctx ? '_' + ctx.$route.name : '') + (postfix ? '_' + postfix : '');
}

let commonUtil = {
  combineColumns,
  getTableSlots,
  getQueryParams,
  objToQuery,
  setNavis,
  downLoad,
  downloadByBlob,
  isSuccessCode,
  toTree,
  uuid,
  calDynamicPixel,
  toggleDom,
  tableFilters,
  getTableCombineInfo,
  setColumnsSort,
  jsonToUnderLine,
  toChinesNum,
  copy,
  getSystemUniqKey
}
window.CommonUtil = commonUtil;

export default commonUtil;