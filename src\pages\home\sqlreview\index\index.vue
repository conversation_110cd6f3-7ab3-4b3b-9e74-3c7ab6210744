<!--
 * @Author: your name
 * @Date: 2021-01-29 17:56:15
 * @LastEditTime: 2021-02-03 14:26:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/index.vue
-->
<template>
  <PageList :mode="'single'">
    <div class="home page-list-single">
      <div class="frame-button-wrapper">
        <a-button
          slot="extra"
          class="button"
          icon="plus"
          type="primary"
          @click="addProject"
          >新建审核</a-button
        >
      </div>
      <SwitchTable
        ref="switchTable"
        v-bind="tableParams"
        :cardColumns="cardColumns"
        :listColumns="listColumns"
      >
        <!-- sql总数筛选 -->
        <template slot="tableTopRight">
          <div class="sql-count">
            <span class="sql-count-text">SQL总数不为0</span>
            <a-switch size="small" @change="onChange" :checked="sql_count" />
          </div>
          <a-divider type="vertical" />
        </template>
        <!-- 卡片模式 -->
        <CardTable
          slot="cardTable"
          slot-scope="{ record }"
          v-bind="{ cardData: record }"
          @authSubmit="authSubmit(record.id)"
          @showReport="showReport(record)"
          @toDetail="toDetail(record)"
          @remove="remove(record)"
          @terminate="terminate(record)"
          @reReview="reReview(record)"
          @urge="urge(record.id)"
          @refresh="refresh"
        ></CardTable>
        <!-- 列表模式 -->
        <template slot="project_name" slot-scope="{ text }">
          <LimitTags
            :tags="text ? text.split(',').map((item) => ({ label: item })) : []"
            :limit="1"
          ></LimitTags>
        </template>
        <template slot="project_group" slot-scope="{ text }">
          <span class="project-group" v-if="text && text.length > 0">
            <span>{{ text[0] }}</span>
            <span v-if="text.length > 1">
              <a-tooltip>
                <template slot="title">
                  <span>{{ text.toString() }}</span>
                </template>
                <span>...</span>
              </a-tooltip>
            </span>
          </span>
        </template>
        <template slot="review_point" slot-scope="{ text }">
          <LimitTags
            :tags="text ? text.split(',').map((item) => ({ label: item })) : []"
            :limit="1"
          ></LimitTags>
        </template>
        <!-- DBA评审状态 -->
        <div slot="dba_status" slot-scope="{ text, record }" class="dba-status">
          <a-tooltip v-if="text == '未通过'">
            <template slot="title">
              <span>驳回原因：{{ record.comment_content }}</span>
            </template>
            <StatusTag type="dba" :status="text">
              <a-icon style="marginleft: 4px" type="question-circle" />
            </StatusTag>
          </a-tooltip>
          <StatusTag type="dba" :status="text" v-else />
        </div>
        <template slot="status" slot-scope="{ text, record }">
          <div class="status">
            <Status :status="text" :message="record.error_message"></Status>
          </div>
        </template>
        <template slot="created_by" slot-scope="{ record, text }">
          <a-tooltip v-if="record.ch_creater" placement="topLeft">
            <template slot="title">
              <span>{{ text }}</span>
            </template>
            <div class="des">
              <a-icon type="user" />
              <span>{{ record.ch_creater }}</span>
            </div>
          </a-tooltip>
          <span v-else>{{ text || '--' }}</span>
        </template>
        <template slot="operator_dba" slot-scope="{ record, text }">
          <a-tooltip v-if="record.ch_dba" placement="topLeft">
            <template slot="title">
              <span>{{ text }}</span>
            </template>
            <div class="des">
              <a-icon type="user" />
              <span>{{ record.ch_dba }}</span>
            </div>
          </a-tooltip>
          <span v-else>{{ text || '--' }}</span>
        </template>
        <custom-btns-wrapper
          slot="action"
          slot-scope="{ text, record }"
          :limit="3"
        >
          <a @click="toDetail(text, record, $event)" actionBtn>详情</a>
          <a @click="showReport(record)" actionBtn>报表</a>
          <a
            v-if="[1, 2].includes(record.review_status)"
            :disabled="record.review_status == 1"
            actionBtn
            @click="authSubmit(record.id)"
            >提交评审</a
          >
          <!-- <a
            v-if="
              record.status === 1 ||
              record.status === 9 ||
              record.sql_count === 0 ||
              record.sql_count === null ||
              record.is_multi == 2 ||
              [1, 2].includes(record.type)
            "
            disabled
            actionBtn
            >提交评审</a
          > -->
          <a
            v-if="
              record.dba_status === '评审中' || record.dba_status === '待评审'
            "
            @click="urge(record.id)"
            actionBtn
            >催办</a
          >
          <!-- <a
            v-else-if="
              record.dba_status === '已通过' || record.dba_status === '未通过'
            "
            disabled
            actionBtn
            >催办</a
          > -->
          <!-- <a
            v-if="
              ![1, 2].includes(record.type) &&
              ['未提交', '未通过'].includes(record.dba_status)
            "
            @click="authSubmit(record.id)"
            actionBtn
            >提交评审</a
          > -->
          <a-popconfirm
            title="确定移除该数据?"
            @confirm="() => remove(record)"
            v-if="canDo && ![0, 3, 4, 5].includes(record.status)"
          >
            <a actionBtn>删除</a>
          </a-popconfirm>
          <a
            v-if="[0, 3, 4, 5].includes(record.status)"
            @click="terminate(record)"
            actionBtn
            >终止</a
          >
          <a-popconfirm
            title="重新发起审核任务?"
            @confirm="() => reReview(record)"
          >
            <a
              v-if="
                ![1, 2].includes(record.type) &&
                [-1, 9].includes(record.status) &&
                ['未提交', '未通过'].includes(record.dba_status)
              "
              actionBtn
              >重新review</a
            >
          </a-popconfirm>
          <!-- <a
            @click="showHistoryBaselineModel(record)"
            v-if="canDo && record.mode == 0 && record.history_baseline == 1"
            actionBtn
          >历史标准基线</a>-->
        </custom-btns-wrapper>
      </SwitchTable>
      <!-- 新建项目弹窗 -->
      <AddModal ref="addModal" @save="saveProject"></AddModal>
      <!-- 报表抽屉 -->
      <Report ref="Report"></Report>
      <!-- 审核弹窗 -->
      <Audit ref="audit" @refresh="refresh"></Audit>
      <!-- 历史标准基线 -->
      <HistoryBaseline ref="historyBaseline" @onSave="onSave"></HistoryBaseline>
    </div>
  </PageList>
</template>

<script>
import {
  createReview,
  createFileReview,
  createMoreReview,
  reviewDetailAllPass,
  pressToDo,
  editHistoryBaseline,
  removeRecord,
  terminationRecord,
  reviewRetry
} from '@/api/home';
import LimitTags from '@/components/LimitTags';
import Table from '@/components/Table';
import StatusTag from '@/components/Biz/Status/Tag';
import Status from '@/components/Biz/Status';
import SearchArea from '@/components/SearchArea';
import AddModal from './components/AddModal';
import Report from './components/Report';
import Audit from '@/components/Biz/AuditModel';
import CardTable from './components/CardTable';
import config from './config';
import moment from 'moment';
import bodyMinWidth from '@/mixins/bodyMinWidth';
// import common from '@/utils/common';
import PageList from '@/components/PageListNew/SwitchTable/index.vue';
import SwitchTable from '@/components/PageListNew/SwitchTable/table.vue';
import HistoryBaseline from './components/HistoryBaseline';
export default {
  name: 'code-review',
  components: {
    CardTable,
    Table,
    Status,
    SearchArea,
    AddModal,
    Report,
    Audit,
    LimitTags,
    StatusTag,
    PageList,
    SwitchTable,
    HistoryBaseline
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    this.config = config(this);
    // 这里是第三方登录跳转，登录跳转是带查询参数的
    // searchCache为页面搜索框的查询数据，needCache: true每次进入该页面都会触发该方法
    // 如果没有this.$route.query, 那么该页面searchCache.home-sqlreview = null
    // 因此对于已经填写了数据的搜索框，每次进入该页面都会被null覆盖
    // 所以这里应该判断searchCache.home-sqlreview是否为空对象
    const searchCacheData = _.get(
      this.$store.state,
      'common.searchCache.code-review'
    );
    let tableQuery = { ...this.$route.query };
    let createdAt = this.$route.query.created_at;
    if (createdAt) {
      let createdAtArr = createdAt.split(',');
      tableQuery.created_at = createdAtArr.map(item => moment(item));
    }
    if (_.isEmpty(searchCacheData)) {
      this.$store.commit('common/setSearchCache', {
        'code-review': { ...tableQuery }
      });
    }
    // keep-alive是否激活
    this.activated = null;
    return {
      record: {},
      statusColor: this.config.statusColor,
      tableParams: {
        url: '/sqlreview/review/list1',
        reqParams: {
          sql_count: '0'
        },
        method: 'post',
        columns: this.config.cardColumns,
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        showHeader: false,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        cacheKey: 'code-review',
        // uniqKey: CommonUtil.getSystemUniqKey(this, 'list')
        // rowSelection: {
        //   // type: 'radio'
        // }
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' }
      },
      starDesc: ['非常差', '差', '一般', '好', '非常好'],
      cardColumns: this.config.cardColumns,
      listColumns: this.config.listColumns
    };
  },
  created() {},
  mounted() {},
  activated() {
    if (this.activated === false) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refreshKeep();
      this.activated = true;
    }
  },
  deactivated() {
    this.activated = false;
  },
  computed: {
    showAKeyPass() {
      const role = this.$store.state.account.user.role || '';
      return role == 'admin' || role.toLowerCase() == 'dba';
    },
    // sql总数为0是否被选中，读取vuex中保存的缓存数值
    sql_count() {
      const data = this.$store.state.common.searchCache['code-review'];
      return data ? data.sql_count === '1' : false;
    },
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  methods: {
    // 筛选sql是否为0
    onChange(data) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      this.$set(this.tableParams, 'reqParams', { sql_count: data ? '1' : '0' });
      this.$set(table.searchParams, 'sql_count', data ? '1' : '0');
    },
    // 新建项目
    addProject() {
      const { addModal } = this.$refs;
      addModal.show();
    },
    // 新建项目保存
    saveProject(data, bool) {
      const { addModal, switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const reviewType = data.review_type;

      // 请求
      this.$showLoading();
      if (bool == 0 && reviewType !== 2) {
        createReview({
          ...data,
          source_flag: 0
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              addModal.hide();
              table.refresh();
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else if (reviewType == 2) {
        const submitData = new FormData();
        data.files_list.forEach(item => {
          submitData.append('files_list', item);
        });

        submitData.append('schema_list', JSON.stringify(data.schema_list));
        submitData.append('rule_set_id', String(data.rule_set_id));
        createFileReview(submitData)
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              addModal.hide();
              table.refresh();
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else {
        createMoreReview({
          ...data
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              addModal.hide();
              table.refresh();
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      }
    },
    toDetail(record, e) {
      // name: 'home-sqlreview-detail',
      this.$router.push({
        name: 'code-review-detail',
        params: { id: record.id }
      });
    },
    showReport(record) {
      this.record = record;
      this.$refs.Report.show(record);
    },
    // 查询
    search(data) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refresh();
    },
    // 一键通过
    aKeyPass(id) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      this.$confirm({
        title: '确认一键通过当前任务?',
        onOk: () => {
          this.$showLoading();
          reviewDetailAllPass({
            review_id: id
          })
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.$hideLoading({ tips: _.get(res, 'data.message') });
                table.refresh();
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        },
        onCancel() {}
      });
    },
    // 提交评审
    authSubmit(id) {
      this.$refs.audit.show(id);
    },
    // 催办
    urge(id) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const obj = { record_id: id };
      pressToDo(obj)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            table.refreshKeep();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    refresh() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refreshKeep();
    },
    // 历史标准基线
    showHistoryBaselineModel(record) {
      this.$refs.historyBaseline.show(record);
    },
    remove(record) {
      this.$showLoading();
      removeRecord({ id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 重新review
    reReview(record) {
      this.$showLoading();
      reviewRetry({ review_id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 终止
    terminate(record) {
      this.$showLoading();
      terminationRecord({ id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onSave(id) {
      const params = {
        id: id,
        history_baseline: 1
      };
      this.$showLoading();
      editHistoryBaseline(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.sql-count {
  margin-right: 12px;
  display: flex;
  align-items: center;
  .sql-count-text {
    margin-right: 4px;
  }
}
.project-group {
  display: flex;
  > span {
    margin-right: 16px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #27272a;
    letter-spacing: 0;
    font-weight: 400;
    text-align: center;
    border: 1px solid #e4e4e7;
    border-radius: 4px;
    padding: 4px 7px;
    white-space: nowrap;
  }
}
.dba-status {
  .ant-tag {
    width: auto !important;
  }
}
</style>
