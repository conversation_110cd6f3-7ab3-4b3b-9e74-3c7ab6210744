!function(E,T){"object"==typeof exports&&"object"==typeof module?module.exports=T():"function"==typeof define&&define.amd?define([],T):"object"==typeof exports?exports.sqlFormatter=T():E.sqlFormatter=T()}(self,(()=>(()=>{var E={9662:(E,T,t)=>{var e=t(614),R=t(6330),A=TypeError;E.exports=function(E){if(e(E))return E;throw A(R(E)+" is not a function")}},9483:(E,T,t)=>{var e=t(4411),R=t(6330),A=TypeError;E.exports=function(E){if(e(E))return E;throw A(R(E)+" is not a constructor")}},6077:(E,T,t)=>{var e=t(614),R=String,A=TypeError;E.exports=function(E){if("object"==typeof E||e(E))return E;throw A("Can't set "+R(E)+" as a prototype")}},1223:(E,T,t)=>{var e=t(5112),R=t(30),A=t(3070).f,r=e("unscopables"),S=Array.prototype;null==S[r]&&A(S,r,{configurable:!0,value:R(null)}),E.exports=function(E){S[r][E]=!0}},1530:(E,T,t)=>{"use strict";var e=t(8710).charAt;E.exports=function(E,T,t){return T+(t?e(E,T).length:1)}},5787:(E,T,t)=>{var e=t(7976),R=TypeError;E.exports=function(E,T){if(e(T,E))return E;throw R("Incorrect invocation")}},9670:(E,T,t)=>{var e=t(111),R=String,A=TypeError;E.exports=function(E){if(e(E))return E;throw A(R(E)+" is not an object")}},7556:(E,T,t)=>{var e=t(7293);E.exports=e((function(){if("function"==typeof ArrayBuffer){var E=new ArrayBuffer(8);Object.isExtensible(E)&&Object.defineProperty(E,"a",{value:8})}}))},8533:(E,T,t)=>{"use strict";var e=t(2092).forEach,R=t(9341)("forEach");E.exports=R?[].forEach:function(E){return e(this,E,arguments.length>1?arguments[1]:void 0)}},8457:(E,T,t)=>{"use strict";var e=t(9974),R=t(6916),A=t(7908),r=t(3411),S=t(7659),n=t(4411),O=t(6244),I=t(6135),N=t(8554),o=t(1246),C=Array;E.exports=function(E){var T=A(E),t=n(this),L=arguments.length,i=L>1?arguments[1]:void 0,_=void 0!==i;_&&(i=e(i,L>2?arguments[2]:void 0));var a,u,s,P,D,c,M=o(T),f=0;if(!M||this===C&&S(M))for(a=O(T),u=t?new this(a):C(a);a>f;f++)c=_?i(T[f],f):T[f],I(u,f,c);else for(D=(P=N(T,M)).next,u=t?new this:[];!(s=R(D,P)).done;f++)c=_?r(P,i,[s.value,f],!0):s.value,I(u,f,c);return u.length=f,u}},1318:(E,T,t)=>{var e=t(5656),R=t(1400),A=t(6244),r=function(E){return function(T,t,r){var S,n=e(T),O=A(n),I=R(r,O);if(E&&t!=t){for(;O>I;)if((S=n[I++])!=S)return!0}else for(;O>I;I++)if((E||I in n)&&n[I]===t)return E||I||0;return!E&&-1}};E.exports={includes:r(!0),indexOf:r(!1)}},2092:(E,T,t)=>{var e=t(9974),R=t(1702),A=t(8361),r=t(7908),S=t(6244),n=t(5417),O=R([].push),I=function(E){var T=1==E,t=2==E,R=3==E,I=4==E,N=6==E,o=7==E,C=5==E||N;return function(L,i,_,a){for(var u,s,P=r(L),D=A(P),c=e(i,_),M=S(D),f=0,U=a||n,l=T?U(L,M):t||o?U(L,0):void 0;M>f;f++)if((C||f in D)&&(s=c(u=D[f],f,P),E))if(T)l[f]=s;else if(s)switch(E){case 3:return!0;case 5:return u;case 6:return f;case 2:O(l,u)}else switch(E){case 4:return!1;case 7:O(l,u)}return N?-1:R||I?I:l}};E.exports={forEach:I(0),map:I(1),filter:I(2),some:I(3),every:I(4),find:I(5),findIndex:I(6),filterReject:I(7)}},1194:(E,T,t)=>{var e=t(7293),R=t(5112),A=t(7392),r=R("species");E.exports=function(E){return A>=51||!e((function(){var T=[];return(T.constructor={})[r]=function(){return{foo:1}},1!==T[E](Boolean).foo}))}},9341:(E,T,t)=>{"use strict";var e=t(7293);E.exports=function(E,T){var t=[][E];return!!t&&e((function(){t.call(null,T||function(){return 1},1)}))}},1589:(E,T,t)=>{var e=t(1400),R=t(6244),A=t(6135),r=Array,S=Math.max;E.exports=function(E,T,t){for(var n=R(E),O=e(T,n),I=e(void 0===t?n:t,n),N=r(S(I-O,0)),o=0;O<I;O++,o++)A(N,o,E[O]);return N.length=o,N}},206:(E,T,t)=>{var e=t(1702);E.exports=e([].slice)},4362:(E,T,t)=>{var e=t(1589),R=Math.floor,A=function(E,T){var t=E.length,n=R(t/2);return t<8?r(E,T):S(E,A(e(E,0,n),T),A(e(E,n),T),T)},r=function(E,T){for(var t,e,R=E.length,A=1;A<R;){for(e=A,t=E[A];e&&T(E[e-1],t)>0;)E[e]=E[--e];e!==A++&&(E[e]=t)}return E},S=function(E,T,t,e){for(var R=T.length,A=t.length,r=0,S=0;r<R||S<A;)E[r+S]=r<R&&S<A?e(T[r],t[S])<=0?T[r++]:t[S++]:r<R?T[r++]:t[S++];return E};E.exports=A},7475:(E,T,t)=>{var e=t(3157),R=t(4411),A=t(111),r=t(5112)("species"),S=Array;E.exports=function(E){var T;return e(E)&&(T=E.constructor,(R(T)&&(T===S||e(T.prototype))||A(T)&&null===(T=T[r]))&&(T=void 0)),void 0===T?S:T}},5417:(E,T,t)=>{var e=t(7475);E.exports=function(E,T){return new(e(E))(0===T?0:T)}},3411:(E,T,t)=>{var e=t(9670),R=t(9212);E.exports=function(E,T,t,A){try{return A?T(e(t)[0],t[1]):T(t)}catch(T){R(E,"throw",T)}}},7072:(E,T,t)=>{var e=t(5112)("iterator"),R=!1;try{var A=0,r={next:function(){return{done:!!A++}},return:function(){R=!0}};r[e]=function(){return this},Array.from(r,(function(){throw 2}))}catch(E){}E.exports=function(E,T){if(!T&&!R)return!1;var t=!1;try{var A={};A[e]=function(){return{next:function(){return{done:t=!0}}}},E(A)}catch(E){}return t}},4326:(E,T,t)=>{var e=t(1702),R=e({}.toString),A=e("".slice);E.exports=function(E){return A(R(E),8,-1)}},648:(E,T,t)=>{var e=t(1694),R=t(614),A=t(4326),r=t(5112)("toStringTag"),S=Object,n="Arguments"==A(function(){return arguments}());E.exports=e?A:function(E){var T,t,e;return void 0===E?"Undefined":null===E?"Null":"string"==typeof(t=function(E,T){try{return E[T]}catch(E){}}(T=S(E),r))?t:n?A(T):"Object"==(e=A(T))&&R(T.callee)?"Arguments":e}},7741:(E,T,t)=>{var e=t(1702),R=Error,A=e("".replace),r=String(R("zxcasd").stack),S=/\n\s*at [^:]*:[^\n]*/,n=S.test(r);E.exports=function(E,T){if(n&&"string"==typeof E&&!R.prepareStackTrace)for(;T--;)E=A(E,S,"");return E}},5631:(E,T,t)=>{"use strict";var e=t(3070).f,R=t(30),A=t(9190),r=t(9974),S=t(5787),n=t(408),O=t(654),I=t(6340),N=t(9781),o=t(2423).fastKey,C=t(9909),L=C.set,i=C.getterFor;E.exports={getConstructor:function(E,T,t,O){var I=E((function(E,e){S(E,C),L(E,{type:T,index:R(null),first:void 0,last:void 0,size:0}),N||(E.size=0),null!=e&&n(e,E[O],{that:E,AS_ENTRIES:t})})),C=I.prototype,_=i(T),a=function(E,T,t){var e,R,A=_(E),r=u(E,T);return r?r.value=t:(A.last=r={index:R=o(T,!0),key:T,value:t,previous:e=A.last,next:void 0,removed:!1},A.first||(A.first=r),e&&(e.next=r),N?A.size++:E.size++,"F"!==R&&(A.index[R]=r)),E},u=function(E,T){var t,e=_(E),R=o(T);if("F"!==R)return e.index[R];for(t=e.first;t;t=t.next)if(t.key==T)return t};return A(C,{clear:function(){for(var E=_(this),T=E.index,t=E.first;t;)t.removed=!0,t.previous&&(t.previous=t.previous.next=void 0),delete T[t.index],t=t.next;E.first=E.last=void 0,N?E.size=0:this.size=0},delete:function(E){var T=this,t=_(T),e=u(T,E);if(e){var R=e.next,A=e.previous;delete t.index[e.index],e.removed=!0,A&&(A.next=R),R&&(R.previous=A),t.first==e&&(t.first=R),t.last==e&&(t.last=A),N?t.size--:T.size--}return!!e},forEach:function(E){for(var T,t=_(this),e=r(E,arguments.length>1?arguments[1]:void 0);T=T?T.next:t.first;)for(e(T.value,T.key,this);T&&T.removed;)T=T.previous},has:function(E){return!!u(this,E)}}),A(C,t?{get:function(E){var T=u(this,E);return T&&T.value},set:function(E,T){return a(this,0===E?0:E,T)}}:{add:function(E){return a(this,E=0===E?0:E,E)}}),N&&e(C,"size",{get:function(){return _(this).size}}),I},setStrong:function(E,T,t){var e=T+" Iterator",R=i(T),A=i(e);O(E,T,(function(E,T){L(this,{type:e,target:E,state:R(E),kind:T,last:void 0})}),(function(){for(var E=A(this),T=E.kind,t=E.last;t&&t.removed;)t=t.previous;return E.target&&(E.last=t=t?t.next:E.state.first)?"keys"==T?{value:t.key,done:!1}:"values"==T?{value:t.value,done:!1}:{value:[t.key,t.value],done:!1}:(E.target=void 0,{value:void 0,done:!0})}),t?"entries":"values",!t,!0),I(T)}}},7710:(E,T,t)=>{"use strict";var e=t(2109),R=t(7854),A=t(1702),r=t(4705),S=t(8052),n=t(2423),O=t(408),I=t(5787),N=t(614),o=t(111),C=t(7293),L=t(7072),i=t(8003),_=t(9587);E.exports=function(E,T,t){var a=-1!==E.indexOf("Map"),u=-1!==E.indexOf("Weak"),s=a?"set":"add",P=R[E],D=P&&P.prototype,c=P,M={},f=function(E){var T=A(D[E]);S(D,E,"add"==E?function(E){return T(this,0===E?0:E),this}:"delete"==E?function(E){return!(u&&!o(E))&&T(this,0===E?0:E)}:"get"==E?function(E){return u&&!o(E)?void 0:T(this,0===E?0:E)}:"has"==E?function(E){return!(u&&!o(E))&&T(this,0===E?0:E)}:function(E,t){return T(this,0===E?0:E,t),this})};if(r(E,!N(P)||!(u||D.forEach&&!C((function(){(new P).entries().next()})))))c=t.getConstructor(T,E,a,s),n.enable();else if(r(E,!0)){var U=new c,l=U[s](u?{}:-0,1)!=U,p=C((function(){U.has(1)})),G=L((function(E){new P(E)})),y=!u&&C((function(){for(var E=new P,T=5;T--;)E[s](T,T);return!E.has(-0)}));G||((c=T((function(E,T){I(E,D);var t=_(new P,E,c);return null!=T&&O(T,t[s],{that:t,AS_ENTRIES:a}),t}))).prototype=D,D.constructor=c),(p||y)&&(f("delete"),f("has"),a&&f("get")),(y||l)&&f(s),u&&D.clear&&delete D.clear}return M[E]=c,e({global:!0,constructor:!0,forced:c!=P},M),i(c,E),u||t.setStrong(c,E,a),c}},9920:(E,T,t)=>{var e=t(2597),R=t(3887),A=t(1236),r=t(3070);E.exports=function(E,T,t){for(var S=R(T),n=r.f,O=A.f,I=0;I<S.length;I++){var N=S[I];e(E,N)||t&&e(t,N)||n(E,N,O(T,N))}}},4964:(E,T,t)=>{var e=t(5112)("match");E.exports=function(E){var T=/./;try{"/./"[E](T)}catch(t){try{return T[e]=!1,"/./"[E](T)}catch(E){}}return!1}},8544:(E,T,t)=>{var e=t(7293);E.exports=!e((function(){function E(){}return E.prototype.constructor=null,Object.getPrototypeOf(new E)!==E.prototype}))},4994:(E,T,t)=>{"use strict";var e=t(3383).IteratorPrototype,R=t(30),A=t(9114),r=t(8003),S=t(7497),n=function(){return this};E.exports=function(E,T,t,O){var I=T+" Iterator";return E.prototype=R(e,{next:A(+!O,t)}),r(E,I,!1,!0),S[I]=n,E}},8880:(E,T,t)=>{var e=t(9781),R=t(3070),A=t(9114);E.exports=e?function(E,T,t){return R.f(E,T,A(1,t))}:function(E,T,t){return E[T]=t,E}},9114:E=>{E.exports=function(E,T){return{enumerable:!(1&E),configurable:!(2&E),writable:!(4&E),value:T}}},6135:(E,T,t)=>{"use strict";var e=t(4948),R=t(3070),A=t(9114);E.exports=function(E,T,t){var r=e(T);r in E?R.f(E,r,A(0,t)):E[r]=t}},7045:(E,T,t)=>{var e=t(6339),R=t(3070);E.exports=function(E,T,t){return t.get&&e(t.get,T,{getter:!0}),t.set&&e(t.set,T,{setter:!0}),R.f(E,T,t)}},8052:(E,T,t)=>{var e=t(614),R=t(3070),A=t(6339),r=t(3072);E.exports=function(E,T,t,S){S||(S={});var n=S.enumerable,O=void 0!==S.name?S.name:T;if(e(t)&&A(t,O,S),S.global)n?E[T]=t:r(T,t);else{try{S.unsafe?E[T]&&(n=!0):delete E[T]}catch(E){}n?E[T]=t:R.f(E,T,{value:t,enumerable:!1,configurable:!S.nonConfigurable,writable:!S.nonWritable})}return E}},9190:(E,T,t)=>{var e=t(8052);E.exports=function(E,T,t){for(var R in T)e(E,R,T[R],t);return E}},3072:(E,T,t)=>{var e=t(7854),R=Object.defineProperty;E.exports=function(E,T){try{R(e,E,{value:T,configurable:!0,writable:!0})}catch(t){e[E]=T}return T}},654:(E,T,t)=>{"use strict";var e=t(2109),R=t(6916),A=t(1913),r=t(6530),S=t(614),n=t(4994),O=t(9518),I=t(7674),N=t(8003),o=t(8880),C=t(8052),L=t(5112),i=t(7497),_=t(3383),a=r.PROPER,u=r.CONFIGURABLE,s=_.IteratorPrototype,P=_.BUGGY_SAFARI_ITERATORS,D=L("iterator"),c="keys",M="values",f="entries",U=function(){return this};E.exports=function(E,T,t,r,L,_,l){n(t,T,r);var p,G,y,h=function(E){if(E===L&&H)return H;if(!P&&E in v)return v[E];switch(E){case c:case M:case f:return function(){return new t(this,E)}}return function(){return new t(this)}},B=T+" Iterator",d=!1,v=E.prototype,F=v[D]||v["@@iterator"]||L&&v[L],H=!P&&F||h(L),Y="Array"==T&&v.entries||F;if(Y&&(p=O(Y.call(new E)))!==Object.prototype&&p.next&&(A||O(p)===s||(I?I(p,s):S(p[D])||C(p,D,U)),N(p,B,!0,!0),A&&(i[B]=U)),a&&L==M&&F&&F.name!==M&&(!A&&u?o(v,"name",M):(d=!0,H=function(){return R(F,this)})),L)if(G={values:h(M),keys:_?H:h(c),entries:h(f)},l)for(y in G)(P||d||!(y in v))&&C(v,y,G[y]);else e({target:T,proto:!0,forced:P||d},G);return A&&!l||v[D]===H||C(v,D,H,{name:L}),i[T]=H,G}},7235:(E,T,t)=>{var e=t(857),R=t(2597),A=t(6061),r=t(3070).f;E.exports=function(E){var T=e.Symbol||(e.Symbol={});R(T,E)||r(T,E,{value:A.f(E)})}},5117:(E,T,t)=>{"use strict";var e=t(6330),R=TypeError;E.exports=function(E,T){if(!delete E[T])throw R("Cannot delete property "+e(T)+" of "+e(E))}},9781:(E,T,t)=>{var e=t(7293);E.exports=!e((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},317:(E,T,t)=>{var e=t(7854),R=t(111),A=e.document,r=R(A)&&R(A.createElement);E.exports=function(E){return r?A.createElement(E):{}}},7207:E=>{var T=TypeError;E.exports=function(E){if(E>9007199254740991)throw T("Maximum allowed index exceeded");return E}},8324:E=>{E.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},8509:(E,T,t)=>{var e=t(317)("span").classList,R=e&&e.constructor&&e.constructor.prototype;E.exports=R===Object.prototype?void 0:R},8886:(E,T,t)=>{var e=t(8113).match(/firefox\/(\d+)/i);E.exports=!!e&&+e[1]},256:(E,T,t)=>{var e=t(8113);E.exports=/MSIE|Trident/.test(e)},8113:(E,T,t)=>{var e=t(5005);E.exports=e("navigator","userAgent")||""},7392:(E,T,t)=>{var e,R,A=t(7854),r=t(8113),S=A.process,n=A.Deno,O=S&&S.versions||n&&n.version,I=O&&O.v8;I&&(R=(e=I.split("."))[0]>0&&e[0]<4?1:+(e[0]+e[1])),!R&&r&&(!(e=r.match(/Edge\/(\d+)/))||e[1]>=74)&&(e=r.match(/Chrome\/(\d+)/))&&(R=+e[1]),E.exports=R},8008:(E,T,t)=>{var e=t(8113).match(/AppleWebKit\/(\d+)\./);E.exports=!!e&&+e[1]},748:E=>{E.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},2914:(E,T,t)=>{var e=t(7293),R=t(9114);E.exports=!e((function(){var E=Error("a");return!("stack"in E)||(Object.defineProperty(E,"stack",R(1,7)),7!==E.stack)}))},2109:(E,T,t)=>{var e=t(7854),R=t(1236).f,A=t(8880),r=t(8052),S=t(3072),n=t(9920),O=t(4705);E.exports=function(E,T){var t,I,N,o,C,L=E.target,i=E.global,_=E.stat;if(t=i?e:_?e[L]||S(L,{}):(e[L]||{}).prototype)for(I in T){if(o=T[I],N=E.dontCallGetSet?(C=R(t,I))&&C.value:t[I],!O(i?I:L+(_?".":"#")+I,E.forced)&&void 0!==N){if(typeof o==typeof N)continue;n(o,N)}(E.sham||N&&N.sham)&&A(o,"sham",!0),r(t,I,o,E)}}},7293:E=>{E.exports=function(E){try{return!!E()}catch(E){return!0}}},7007:(E,T,t)=>{"use strict";t(4916);var e=t(1702),R=t(8052),A=t(2261),r=t(7293),S=t(5112),n=t(8880),O=S("species"),I=RegExp.prototype;E.exports=function(E,T,t,N){var o=S(E),C=!r((function(){var T={};return T[o]=function(){return 7},7!=""[E](T)})),L=C&&!r((function(){var T=!1,t=/a/;return"split"===E&&((t={}).constructor={},t.constructor[O]=function(){return t},t.flags="",t[o]=/./[o]),t.exec=function(){return T=!0,null},t[o](""),!T}));if(!C||!L||t){var i=e(/./[o]),_=T(o,""[E],(function(E,T,t,R,r){var S=e(E),n=T.exec;return n===A||n===I.exec?C&&!r?{done:!0,value:i(T,t,R)}:{done:!0,value:S(t,T,R)}:{done:!1}}));R(String.prototype,E,_[0]),R(I,o,_[1])}N&&n(I[o],"sham",!0)}},6790:(E,T,t)=>{"use strict";var e=t(3157),R=t(6244),A=t(7207),r=t(9974),S=function(E,T,t,n,O,I,N,o){for(var C,L,i=O,_=0,a=!!N&&r(N,o);_<n;)_ in t&&(C=a?a(t[_],_,T):t[_],I>0&&e(C)?(L=R(C),i=S(E,T,C,L,i,I-1)-1):(A(i+1),E[i]=C),i++),_++;return i};E.exports=S},6677:(E,T,t)=>{var e=t(7293);E.exports=!e((function(){return Object.isExtensible(Object.preventExtensions({}))}))},2104:(E,T,t)=>{var e=t(4374),R=Function.prototype,A=R.apply,r=R.call;E.exports="object"==typeof Reflect&&Reflect.apply||(e?r.bind(A):function(){return r.apply(A,arguments)})},9974:(E,T,t)=>{var e=t(1702),R=t(9662),A=t(4374),r=e(e.bind);E.exports=function(E,T){return R(E),void 0===T?E:A?r(E,T):function(){return E.apply(T,arguments)}}},4374:(E,T,t)=>{var e=t(7293);E.exports=!e((function(){var E=function(){}.bind();return"function"!=typeof E||E.hasOwnProperty("prototype")}))},7065:(E,T,t)=>{"use strict";var e=t(1702),R=t(9662),A=t(111),r=t(2597),S=t(206),n=t(4374),O=Function,I=e([].concat),N=e([].join),o={},C=function(E,T,t){if(!r(o,T)){for(var e=[],R=0;R<T;R++)e[R]="a["+R+"]";o[T]=O("C,a","return new C("+N(e,",")+")")}return o[T](E,t)};E.exports=n?O.bind:function(E){var T=R(this),t=T.prototype,e=S(arguments,1),r=function(){var t=I(e,S(arguments));return this instanceof r?C(T,t.length,t):T.apply(E,t)};return A(t)&&(r.prototype=t),r}},6916:(E,T,t)=>{var e=t(4374),R=Function.prototype.call;E.exports=e?R.bind(R):function(){return R.apply(R,arguments)}},6530:(E,T,t)=>{var e=t(9781),R=t(2597),A=Function.prototype,r=e&&Object.getOwnPropertyDescriptor,S=R(A,"name"),n=S&&"something"===function(){}.name,O=S&&(!e||e&&r(A,"name").configurable);E.exports={EXISTS:S,PROPER:n,CONFIGURABLE:O}},1702:(E,T,t)=>{var e=t(4374),R=Function.prototype,A=R.bind,r=R.call,S=e&&A.bind(r,r);E.exports=e?function(E){return E&&S(E)}:function(E){return E&&function(){return r.apply(E,arguments)}}},5005:(E,T,t)=>{var e=t(7854),R=t(614),A=function(E){return R(E)?E:void 0};E.exports=function(E,T){return arguments.length<2?A(e[E]):e[E]&&e[E][T]}},1246:(E,T,t)=>{var e=t(648),R=t(8173),A=t(7497),r=t(5112)("iterator");E.exports=function(E){if(null!=E)return R(E,r)||R(E,"@@iterator")||A[e(E)]}},8554:(E,T,t)=>{var e=t(6916),R=t(9662),A=t(9670),r=t(6330),S=t(1246),n=TypeError;E.exports=function(E,T){var t=arguments.length<2?S(E):T;if(R(t))return A(e(t,E));throw n(r(E)+" is not iterable")}},8173:(E,T,t)=>{var e=t(9662);E.exports=function(E,T){var t=E[T];return null==t?void 0:e(t)}},647:(E,T,t)=>{var e=t(1702),R=t(7908),A=Math.floor,r=e("".charAt),S=e("".replace),n=e("".slice),O=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,I=/\$([$&'`]|\d{1,2})/g;E.exports=function(E,T,t,e,N,o){var C=t+E.length,L=e.length,i=I;return void 0!==N&&(N=R(N),i=O),S(o,i,(function(R,S){var O;switch(r(S,0)){case"$":return"$";case"&":return E;case"`":return n(T,0,t);case"'":return n(T,C);case"<":O=N[n(S,1,-1)];break;default:var I=+S;if(0===I)return R;if(I>L){var o=A(I/10);return 0===o?R:o<=L?void 0===e[o-1]?r(S,1):e[o-1]+r(S,1):R}O=e[I-1]}return void 0===O?"":O}))}},7854:(E,T,t)=>{var e=function(E){return E&&E.Math==Math&&E};E.exports=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof t.g&&t.g)||function(){return this}()||Function("return this")()},2597:(E,T,t)=>{var e=t(1702),R=t(7908),A=e({}.hasOwnProperty);E.exports=Object.hasOwn||function(E,T){return A(R(E),T)}},3501:E=>{E.exports={}},490:(E,T,t)=>{var e=t(5005);E.exports=e("document","documentElement")},4664:(E,T,t)=>{var e=t(9781),R=t(7293),A=t(317);E.exports=!e&&!R((function(){return 7!=Object.defineProperty(A("div"),"a",{get:function(){return 7}}).a}))},8361:(E,T,t)=>{var e=t(1702),R=t(7293),A=t(4326),r=Object,S=e("".split);E.exports=R((function(){return!r("z").propertyIsEnumerable(0)}))?function(E){return"String"==A(E)?S(E,""):r(E)}:r},9587:(E,T,t)=>{var e=t(614),R=t(111),A=t(7674);E.exports=function(E,T,t){var r,S;return A&&e(r=T.constructor)&&r!==t&&R(S=r.prototype)&&S!==t.prototype&&A(E,S),E}},2788:(E,T,t)=>{var e=t(1702),R=t(614),A=t(5465),r=e(Function.toString);R(A.inspectSource)||(A.inspectSource=function(E){return r(E)}),E.exports=A.inspectSource},8340:(E,T,t)=>{var e=t(111),R=t(8880);E.exports=function(E,T){e(T)&&"cause"in T&&R(E,"cause",T.cause)}},2423:(E,T,t)=>{var e=t(2109),R=t(1702),A=t(3501),r=t(111),S=t(2597),n=t(3070).f,O=t(8006),I=t(1156),N=t(2050),o=t(9711),C=t(6677),L=!1,i=o("meta"),_=0,a=function(E){n(E,i,{value:{objectID:"O"+_++,weakData:{}}})},u=E.exports={enable:function(){u.enable=function(){},L=!0;var E=O.f,T=R([].splice),t={};t[i]=1,E(t).length&&(O.f=function(t){for(var e=E(t),R=0,A=e.length;R<A;R++)if(e[R]===i){T(e,R,1);break}return e},e({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:I.f}))},fastKey:function(E,T){if(!r(E))return"symbol"==typeof E?E:("string"==typeof E?"S":"P")+E;if(!S(E,i)){if(!N(E))return"F";if(!T)return"E";a(E)}return E[i].objectID},getWeakData:function(E,T){if(!S(E,i)){if(!N(E))return!0;if(!T)return!1;a(E)}return E[i].weakData},onFreeze:function(E){return C&&L&&N(E)&&!S(E,i)&&a(E),E}};A[i]=!0},9909:(E,T,t)=>{var e,R,A,r=t(8536),S=t(7854),n=t(1702),O=t(111),I=t(8880),N=t(2597),o=t(5465),C=t(6200),L=t(3501),i="Object already initialized",_=S.TypeError,a=S.WeakMap;if(r||o.state){var u=o.state||(o.state=new a),s=n(u.get),P=n(u.has),D=n(u.set);e=function(E,T){if(P(u,E))throw new _(i);return T.facade=E,D(u,E,T),T},R=function(E){return s(u,E)||{}},A=function(E){return P(u,E)}}else{var c=C("state");L[c]=!0,e=function(E,T){if(N(E,c))throw new _(i);return T.facade=E,I(E,c,T),T},R=function(E){return N(E,c)?E[c]:{}},A=function(E){return N(E,c)}}E.exports={set:e,get:R,has:A,enforce:function(E){return A(E)?R(E):e(E,{})},getterFor:function(E){return function(T){var t;if(!O(T)||(t=R(T)).type!==E)throw _("Incompatible receiver, "+E+" required");return t}}}},7659:(E,T,t)=>{var e=t(5112),R=t(7497),A=e("iterator"),r=Array.prototype;E.exports=function(E){return void 0!==E&&(R.Array===E||r[A]===E)}},3157:(E,T,t)=>{var e=t(4326);E.exports=Array.isArray||function(E){return"Array"==e(E)}},614:E=>{E.exports=function(E){return"function"==typeof E}},4411:(E,T,t)=>{var e=t(1702),R=t(7293),A=t(614),r=t(648),S=t(5005),n=t(2788),O=function(){},I=[],N=S("Reflect","construct"),o=/^\s*(?:class|function)\b/,C=e(o.exec),L=!o.exec(O),i=function(E){if(!A(E))return!1;try{return N(O,I,E),!0}catch(E){return!1}},_=function(E){if(!A(E))return!1;switch(r(E)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return L||!!C(o,n(E))}catch(E){return!0}};_.sham=!0,E.exports=!N||R((function(){var E;return i(i.call)||!i(Object)||!i((function(){E=!0}))||E}))?_:i},4705:(E,T,t)=>{var e=t(7293),R=t(614),A=/#|\.prototype\./,r=function(E,T){var t=n[S(E)];return t==I||t!=O&&(R(T)?e(T):!!T)},S=r.normalize=function(E){return String(E).replace(A,".").toLowerCase()},n=r.data={},O=r.NATIVE="N",I=r.POLYFILL="P";E.exports=r},111:(E,T,t)=>{var e=t(614);E.exports=function(E){return"object"==typeof E?null!==E:e(E)}},1913:E=>{E.exports=!1},7850:(E,T,t)=>{var e=t(111),R=t(4326),A=t(5112)("match");E.exports=function(E){var T;return e(E)&&(void 0!==(T=E[A])?!!T:"RegExp"==R(E))}},2190:(E,T,t)=>{var e=t(5005),R=t(614),A=t(7976),r=t(3307),S=Object;E.exports=r?function(E){return"symbol"==typeof E}:function(E){var T=e("Symbol");return R(T)&&A(T.prototype,S(E))}},408:(E,T,t)=>{var e=t(9974),R=t(6916),A=t(9670),r=t(6330),S=t(7659),n=t(6244),O=t(7976),I=t(8554),N=t(1246),o=t(9212),C=TypeError,L=function(E,T){this.stopped=E,this.result=T},i=L.prototype;E.exports=function(E,T,t){var _,a,u,s,P,D,c,M=t&&t.that,f=!(!t||!t.AS_ENTRIES),U=!(!t||!t.IS_RECORD),l=!(!t||!t.IS_ITERATOR),p=!(!t||!t.INTERRUPTED),G=e(T,M),y=function(E){return _&&o(_,"normal",E),new L(!0,E)},h=function(E){return f?(A(E),p?G(E[0],E[1],y):G(E[0],E[1])):p?G(E,y):G(E)};if(U)_=E.iterator;else if(l)_=E;else{if(!(a=N(E)))throw C(r(E)+" is not iterable");if(S(a)){for(u=0,s=n(E);s>u;u++)if((P=h(E[u]))&&O(i,P))return P;return new L(!1)}_=I(E,a)}for(D=U?E.next:_.next;!(c=R(D,_)).done;){try{P=h(c.value)}catch(E){o(_,"throw",E)}if("object"==typeof P&&P&&O(i,P))return P}return new L(!1)}},9212:(E,T,t)=>{var e=t(6916),R=t(9670),A=t(8173);E.exports=function(E,T,t){var r,S;R(E);try{if(!(r=A(E,"return"))){if("throw"===T)throw t;return t}r=e(r,E)}catch(E){S=!0,r=E}if("throw"===T)throw t;if(S)throw r;return R(r),t}},3383:(E,T,t)=>{"use strict";var e,R,A,r=t(7293),S=t(614),n=t(30),O=t(9518),I=t(8052),N=t(5112),o=t(1913),C=N("iterator"),L=!1;[].keys&&("next"in(A=[].keys())?(R=O(O(A)))!==Object.prototype&&(e=R):L=!0),null==e||r((function(){var E={};return e[C].call(E)!==E}))?e={}:o&&(e=n(e)),S(e[C])||I(e,C,(function(){return this})),E.exports={IteratorPrototype:e,BUGGY_SAFARI_ITERATORS:L}},7497:E=>{E.exports={}},6244:(E,T,t)=>{var e=t(7466);E.exports=function(E){return e(E.length)}},6339:(E,T,t)=>{var e=t(7293),R=t(614),A=t(2597),r=t(9781),S=t(6530).CONFIGURABLE,n=t(2788),O=t(9909),I=O.enforce,N=O.get,o=Object.defineProperty,C=r&&!e((function(){return 8!==o((function(){}),"length",{value:8}).length})),L=String(String).split("String"),i=E.exports=function(E,T,t){"Symbol("===String(T).slice(0,7)&&(T="["+String(T).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),t&&t.getter&&(T="get "+T),t&&t.setter&&(T="set "+T),(!A(E,"name")||S&&E.name!==T)&&(r?o(E,"name",{value:T,configurable:!0}):E.name=T),C&&t&&A(t,"arity")&&E.length!==t.arity&&o(E,"length",{value:t.arity});try{t&&A(t,"constructor")&&t.constructor?r&&o(E,"prototype",{writable:!1}):E.prototype&&(E.prototype=void 0)}catch(E){}var e=I(E);return A(e,"source")||(e.source=L.join("string"==typeof T?T:"")),E};Function.prototype.toString=i((function(){return R(this)&&N(this).source||n(this)}),"toString")},4758:E=>{var T=Math.ceil,t=Math.floor;E.exports=Math.trunc||function(E){var e=+E;return(e>0?t:T)(e)}},735:(E,T,t)=>{var e=t(133);E.exports=e&&!!Symbol.for&&!!Symbol.keyFor},133:(E,T,t)=>{var e=t(7392),R=t(7293);E.exports=!!Object.getOwnPropertySymbols&&!R((function(){var E=Symbol();return!String(E)||!(Object(E)instanceof Symbol)||!Symbol.sham&&e&&e<41}))},8536:(E,T,t)=>{var e=t(7854),R=t(614),A=t(2788),r=e.WeakMap;E.exports=R(r)&&/native code/.test(A(r))},6277:(E,T,t)=>{var e=t(1340);E.exports=function(E,T){return void 0===E?arguments.length<2?"":T:e(E)}},3929:(E,T,t)=>{var e=t(7850),R=TypeError;E.exports=function(E){if(e(E))throw R("The method doesn't accept regular expressions");return E}},1574:(E,T,t)=>{"use strict";var e=t(9781),R=t(1702),A=t(6916),r=t(7293),S=t(1956),n=t(5181),O=t(5296),I=t(7908),N=t(8361),o=Object.assign,C=Object.defineProperty,L=R([].concat);E.exports=!o||r((function(){if(e&&1!==o({b:1},o(C({},"a",{enumerable:!0,get:function(){C(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var E={},T={},t=Symbol(),R="abcdefghijklmnopqrst";return E[t]=7,R.split("").forEach((function(E){T[E]=E})),7!=o({},E)[t]||S(o({},T)).join("")!=R}))?function(E,T){for(var t=I(E),R=arguments.length,r=1,o=n.f,C=O.f;R>r;)for(var i,_=N(arguments[r++]),a=o?L(S(_),o(_)):S(_),u=a.length,s=0;u>s;)i=a[s++],e&&!A(C,_,i)||(t[i]=_[i]);return t}:o},30:(E,T,t)=>{var e,R=t(9670),A=t(6048),r=t(748),S=t(3501),n=t(490),O=t(317),I=t(6200)("IE_PROTO"),N=function(){},o=function(E){return"<script>"+E+"<\/script>"},C=function(E){E.write(o("")),E.close();var T=E.parentWindow.Object;return E=null,T},L=function(){try{e=new ActiveXObject("htmlfile")}catch(E){}var E,T;L="undefined"!=typeof document?document.domain&&e?C(e):((T=O("iframe")).style.display="none",n.appendChild(T),T.src=String("javascript:"),(E=T.contentWindow.document).open(),E.write(o("document.F=Object")),E.close(),E.F):C(e);for(var t=r.length;t--;)delete L.prototype[r[t]];return L()};S[I]=!0,E.exports=Object.create||function(E,T){var t;return null!==E?(N.prototype=R(E),t=new N,N.prototype=null,t[I]=E):t=L(),void 0===T?t:A.f(t,T)}},6048:(E,T,t)=>{var e=t(9781),R=t(3353),A=t(3070),r=t(9670),S=t(5656),n=t(1956);T.f=e&&!R?Object.defineProperties:function(E,T){r(E);for(var t,e=S(T),R=n(T),O=R.length,I=0;O>I;)A.f(E,t=R[I++],e[t]);return E}},3070:(E,T,t)=>{var e=t(9781),R=t(4664),A=t(3353),r=t(9670),S=t(4948),n=TypeError,O=Object.defineProperty,I=Object.getOwnPropertyDescriptor;T.f=e?A?function(E,T,t){if(r(E),T=S(T),r(t),"function"==typeof E&&"prototype"===T&&"value"in t&&"writable"in t&&!t.writable){var e=I(E,T);e&&e.writable&&(E[T]=t.value,t={configurable:"configurable"in t?t.configurable:e.configurable,enumerable:"enumerable"in t?t.enumerable:e.enumerable,writable:!1})}return O(E,T,t)}:O:function(E,T,t){if(r(E),T=S(T),r(t),R)try{return O(E,T,t)}catch(E){}if("get"in t||"set"in t)throw n("Accessors not supported");return"value"in t&&(E[T]=t.value),E}},1236:(E,T,t)=>{var e=t(9781),R=t(6916),A=t(5296),r=t(9114),S=t(5656),n=t(4948),O=t(2597),I=t(4664),N=Object.getOwnPropertyDescriptor;T.f=e?N:function(E,T){if(E=S(E),T=n(T),I)try{return N(E,T)}catch(E){}if(O(E,T))return r(!R(A.f,E,T),E[T])}},1156:(E,T,t)=>{var e=t(4326),R=t(5656),A=t(8006).f,r=t(1589),S="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];E.exports.f=function(E){return S&&"Window"==e(E)?function(E){try{return A(E)}catch(E){return r(S)}}(E):A(R(E))}},8006:(E,T,t)=>{var e=t(6324),R=t(748).concat("length","prototype");T.f=Object.getOwnPropertyNames||function(E){return e(E,R)}},5181:(E,T)=>{T.f=Object.getOwnPropertySymbols},9518:(E,T,t)=>{var e=t(2597),R=t(614),A=t(7908),r=t(6200),S=t(8544),n=r("IE_PROTO"),O=Object,I=O.prototype;E.exports=S?O.getPrototypeOf:function(E){var T=A(E);if(e(T,n))return T[n];var t=T.constructor;return R(t)&&T instanceof t?t.prototype:T instanceof O?I:null}},2050:(E,T,t)=>{var e=t(7293),R=t(111),A=t(4326),r=t(7556),S=Object.isExtensible,n=e((function(){S(1)}));E.exports=n||r?function(E){return!!R(E)&&(!r||"ArrayBuffer"!=A(E))&&(!S||S(E))}:S},7976:(E,T,t)=>{var e=t(1702);E.exports=e({}.isPrototypeOf)},6324:(E,T,t)=>{var e=t(1702),R=t(2597),A=t(5656),r=t(1318).indexOf,S=t(3501),n=e([].push);E.exports=function(E,T){var t,e=A(E),O=0,I=[];for(t in e)!R(S,t)&&R(e,t)&&n(I,t);for(;T.length>O;)R(e,t=T[O++])&&(~r(I,t)||n(I,t));return I}},1956:(E,T,t)=>{var e=t(6324),R=t(748);E.exports=Object.keys||function(E){return e(E,R)}},5296:(E,T)=>{"use strict";var t={}.propertyIsEnumerable,e=Object.getOwnPropertyDescriptor,R=e&&!t.call({1:2},1);T.f=R?function(E){var T=e(this,E);return!!T&&T.enumerable}:t},7674:(E,T,t)=>{var e=t(1702),R=t(9670),A=t(6077);E.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var E,T=!1,t={};try{(E=e(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(t,[]),T=t instanceof Array}catch(E){}return function(t,e){return R(t),A(e),T?E(t,e):t.__proto__=e,t}}():void 0)},4699:(E,T,t)=>{var e=t(9781),R=t(1702),A=t(1956),r=t(5656),S=R(t(5296).f),n=R([].push),O=function(E){return function(T){for(var t,R=r(T),O=A(R),I=O.length,N=0,o=[];I>N;)t=O[N++],e&&!S(R,t)||n(o,E?[t,R[t]]:R[t]);return o}};E.exports={entries:O(!0),values:O(!1)}},288:(E,T,t)=>{"use strict";var e=t(1694),R=t(648);E.exports=e?{}.toString:function(){return"[object "+R(this)+"]"}},2140:(E,T,t)=>{var e=t(6916),R=t(614),A=t(111),r=TypeError;E.exports=function(E,T){var t,S;if("string"===T&&R(t=E.toString)&&!A(S=e(t,E)))return S;if(R(t=E.valueOf)&&!A(S=e(t,E)))return S;if("string"!==T&&R(t=E.toString)&&!A(S=e(t,E)))return S;throw r("Can't convert object to primitive value")}},3887:(E,T,t)=>{var e=t(5005),R=t(1702),A=t(8006),r=t(5181),S=t(9670),n=R([].concat);E.exports=e("Reflect","ownKeys")||function(E){var T=A.f(S(E)),t=r.f;return t?n(T,t(E)):T}},857:(E,T,t)=>{var e=t(7854);E.exports=e},2626:(E,T,t)=>{var e=t(3070).f;E.exports=function(E,T,t){t in E||e(E,t,{configurable:!0,get:function(){return T[t]},set:function(E){T[t]=E}})}},7651:(E,T,t)=>{var e=t(6916),R=t(9670),A=t(614),r=t(4326),S=t(2261),n=TypeError;E.exports=function(E,T){var t=E.exec;if(A(t)){var O=e(t,E,T);return null!==O&&R(O),O}if("RegExp"===r(E))return e(S,E,T);throw n("RegExp#exec called on incompatible receiver")}},2261:(E,T,t)=>{"use strict";var e,R,A=t(6916),r=t(1702),S=t(1340),n=t(7066),O=t(2999),I=t(2309),N=t(30),o=t(9909).get,C=t(9441),L=t(7168),i=I("native-string-replace",String.prototype.replace),_=RegExp.prototype.exec,a=_,u=r("".charAt),s=r("".indexOf),P=r("".replace),D=r("".slice),c=(R=/b*/g,A(_,e=/a/,"a"),A(_,R,"a"),0!==e.lastIndex||0!==R.lastIndex),M=O.BROKEN_CARET,f=void 0!==/()??/.exec("")[1];(c||f||M||C||L)&&(a=function(E){var T,t,e,R,r,O,I,C=this,L=o(C),U=S(E),l=L.raw;if(l)return l.lastIndex=C.lastIndex,T=A(a,l,U),C.lastIndex=l.lastIndex,T;var p=L.groups,G=M&&C.sticky,y=A(n,C),h=C.source,B=0,d=U;if(G&&(y=P(y,"y",""),-1===s(y,"g")&&(y+="g"),d=D(U,C.lastIndex),C.lastIndex>0&&(!C.multiline||C.multiline&&"\n"!==u(U,C.lastIndex-1))&&(h="(?: "+h+")",d=" "+d,B++),t=new RegExp("^(?:"+h+")",y)),f&&(t=new RegExp("^"+h+"$(?!\\s)",y)),c&&(e=C.lastIndex),R=A(_,G?t:C,d),G?R?(R.input=D(R.input,B),R[0]=D(R[0],B),R.index=C.lastIndex,C.lastIndex+=R[0].length):C.lastIndex=0:c&&R&&(C.lastIndex=C.global?R.index+R[0].length:e),f&&R&&R.length>1&&A(i,R[0],t,(function(){for(r=1;r<arguments.length-2;r++)void 0===arguments[r]&&(R[r]=void 0)})),R&&p)for(R.groups=O=N(null),r=0;r<p.length;r++)O[(I=p[r])[0]]=R[I[1]];return R}),E.exports=a},7066:(E,T,t)=>{"use strict";var e=t(9670);E.exports=function(){var E=e(this),T="";return E.hasIndices&&(T+="d"),E.global&&(T+="g"),E.ignoreCase&&(T+="i"),E.multiline&&(T+="m"),E.dotAll&&(T+="s"),E.unicode&&(T+="u"),E.unicodeSets&&(T+="v"),E.sticky&&(T+="y"),T}},4706:(E,T,t)=>{var e=t(6916),R=t(2597),A=t(7976),r=t(7066),S=RegExp.prototype;E.exports=function(E){var T=E.flags;return void 0!==T||"flags"in S||R(E,"flags")||!A(S,E)?T:e(r,E)}},2999:(E,T,t)=>{var e=t(7293),R=t(7854).RegExp,A=e((function(){var E=R("a","y");return E.lastIndex=2,null!=E.exec("abcd")})),r=A||e((function(){return!R("a","y").sticky})),S=A||e((function(){var E=R("^r","gy");return E.lastIndex=2,null!=E.exec("str")}));E.exports={BROKEN_CARET:S,MISSED_STICKY:r,UNSUPPORTED_Y:A}},9441:(E,T,t)=>{var e=t(7293),R=t(7854).RegExp;E.exports=e((function(){var E=R(".","s");return!(E.dotAll&&E.exec("\n")&&"s"===E.flags)}))},7168:(E,T,t)=>{var e=t(7293),R=t(7854).RegExp;E.exports=e((function(){var E=R("(?<a>b)","g");return"b"!==E.exec("b").groups.a||"bc"!=="b".replace(E,"$<a>c")}))},4488:E=>{var T=TypeError;E.exports=function(E){if(null==E)throw T("Can't call method on "+E);return E}},6340:(E,T,t)=>{"use strict";var e=t(5005),R=t(3070),A=t(5112),r=t(9781),S=A("species");E.exports=function(E){var T=e(E),t=R.f;r&&T&&!T[S]&&t(T,S,{configurable:!0,get:function(){return this}})}},8003:(E,T,t)=>{var e=t(3070).f,R=t(2597),A=t(5112)("toStringTag");E.exports=function(E,T,t){E&&!t&&(E=E.prototype),E&&!R(E,A)&&e(E,A,{configurable:!0,value:T})}},6200:(E,T,t)=>{var e=t(2309),R=t(9711),A=e("keys");E.exports=function(E){return A[E]||(A[E]=R(E))}},5465:(E,T,t)=>{var e=t(7854),R=t(3072),A="__core-js_shared__",r=e[A]||R(A,{});E.exports=r},2309:(E,T,t)=>{var e=t(1913),R=t(5465);(E.exports=function(E,T){return R[E]||(R[E]=void 0!==T?T:{})})("versions",[]).push({version:"3.24.1",mode:e?"pure":"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.24.1/LICENSE",source:"https://github.com/zloirock/core-js"})},6707:(E,T,t)=>{var e=t(9670),R=t(9483),A=t(5112)("species");E.exports=function(E,T){var t,r=e(E).constructor;return void 0===r||null==(t=e(r)[A])?T:R(t)}},8710:(E,T,t)=>{var e=t(1702),R=t(9303),A=t(1340),r=t(4488),S=e("".charAt),n=e("".charCodeAt),O=e("".slice),I=function(E){return function(T,t){var e,I,N=A(r(T)),o=R(t),C=N.length;return o<0||o>=C?E?"":void 0:(e=n(N,o))<55296||e>56319||o+1===C||(I=n(N,o+1))<56320||I>57343?E?S(N,o):e:E?O(N,o,o+2):I-56320+(e-55296<<10)+65536}};E.exports={codeAt:I(!1),charAt:I(!0)}},4986:(E,T,t)=>{var e=t(8113);E.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(e)},6650:(E,T,t)=>{var e=t(1702),R=t(7466),A=t(1340),r=t(8415),S=t(4488),n=e(r),O=e("".slice),I=Math.ceil,N=function(E){return function(T,t,e){var r,N,o=A(S(T)),C=R(t),L=o.length,i=void 0===e?" ":A(e);return C<=L||""==i?o:((N=n(i,I((r=C-L)/i.length))).length>r&&(N=O(N,0,r)),E?o+N:N+o)}};E.exports={start:N(!1),end:N(!0)}},8415:(E,T,t)=>{"use strict";var e=t(9303),R=t(1340),A=t(4488),r=RangeError;E.exports=function(E){var T=R(A(this)),t="",S=e(E);if(S<0||S==1/0)throw r("Wrong number of repetitions");for(;S>0;(S>>>=1)&&(T+=T))1&S&&(t+=T);return t}},365:(E,T,t)=>{"use strict";var e=t(3111).end,R=t(6091);E.exports=R("trimEnd")?function(){return e(this)}:"".trimEnd},6091:(E,T,t)=>{var e=t(6530).PROPER,R=t(7293),A=t(1361);E.exports=function(E){return R((function(){return!!A[E]()||"​᠎"!=="​᠎"[E]()||e&&A[E].name!==E}))}},3217:(E,T,t)=>{"use strict";var e=t(3111).start,R=t(6091);E.exports=R("trimStart")?function(){return e(this)}:"".trimStart},3111:(E,T,t)=>{var e=t(1702),R=t(4488),A=t(1340),r=t(1361),S=e("".replace),n="["+r+"]",O=RegExp("^"+n+n+"*"),I=RegExp(n+n+"*$"),N=function(E){return function(T){var t=A(R(T));return 1&E&&(t=S(t,O,"")),2&E&&(t=S(t,I,"")),t}};E.exports={start:N(1),end:N(2),trim:N(3)}},6532:(E,T,t)=>{var e=t(6916),R=t(5005),A=t(5112),r=t(8052);E.exports=function(){var E=R("Symbol"),T=E&&E.prototype,t=T&&T.valueOf,S=A("toPrimitive");T&&!T[S]&&r(T,S,(function(E){return e(t,this)}),{arity:1})}},1400:(E,T,t)=>{var e=t(9303),R=Math.max,A=Math.min;E.exports=function(E,T){var t=e(E);return t<0?R(t+T,0):A(t,T)}},5656:(E,T,t)=>{var e=t(8361),R=t(4488);E.exports=function(E){return e(R(E))}},9303:(E,T,t)=>{var e=t(4758);E.exports=function(E){var T=+E;return T!=T||0===T?0:e(T)}},7466:(E,T,t)=>{var e=t(9303),R=Math.min;E.exports=function(E){return E>0?R(e(E),9007199254740991):0}},7908:(E,T,t)=>{var e=t(4488),R=Object;E.exports=function(E){return R(e(E))}},7593:(E,T,t)=>{var e=t(6916),R=t(111),A=t(2190),r=t(8173),S=t(2140),n=t(5112),O=TypeError,I=n("toPrimitive");E.exports=function(E,T){if(!R(E)||A(E))return E;var t,n=r(E,I);if(n){if(void 0===T&&(T="default"),t=e(n,E,T),!R(t)||A(t))return t;throw O("Can't convert object to primitive value")}return void 0===T&&(T="number"),S(E,T)}},4948:(E,T,t)=>{var e=t(7593),R=t(2190);E.exports=function(E){var T=e(E,"string");return R(T)?T:T+""}},1694:(E,T,t)=>{var e={};e[t(5112)("toStringTag")]="z",E.exports="[object z]"===String(e)},1340:(E,T,t)=>{var e=t(648),R=String;E.exports=function(E){if("Symbol"===e(E))throw TypeError("Cannot convert a Symbol value to a string");return R(E)}},6330:E=>{var T=String;E.exports=function(E){try{return T(E)}catch(E){return"Object"}}},9711:(E,T,t)=>{var e=t(1702),R=0,A=Math.random(),r=e(1..toString);E.exports=function(E){return"Symbol("+(void 0===E?"":E)+")_"+r(++R+A,36)}},3307:(E,T,t)=>{var e=t(133);E.exports=e&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},3353:(E,T,t)=>{var e=t(9781),R=t(7293);E.exports=e&&R((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},6061:(E,T,t)=>{var e=t(5112);T.f=e},5112:(E,T,t)=>{var e=t(7854),R=t(2309),A=t(2597),r=t(9711),S=t(133),n=t(3307),O=R("wks"),I=e.Symbol,N=I&&I.for,o=n?I:I&&I.withoutSetter||r;E.exports=function(E){if(!A(O,E)||!S&&"string"!=typeof O[E]){var T="Symbol."+E;S&&A(I,E)?O[E]=I[E]:O[E]=n&&N?N(T):o(T)}return O[E]}},1361:E=>{E.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},9191:(E,T,t)=>{"use strict";var e=t(5005),R=t(2597),A=t(8880),r=t(7976),S=t(7674),n=t(9920),O=t(2626),I=t(9587),N=t(6277),o=t(8340),C=t(7741),L=t(2914),i=t(9781),_=t(1913);E.exports=function(E,T,t,a){var u="stackTraceLimit",s=a?2:1,P=E.split("."),D=P[P.length-1],c=e.apply(null,P);if(c){var M=c.prototype;if(!_&&R(M,"cause")&&delete M.cause,!t)return c;var f=e("Error"),U=T((function(E,T){var t=N(a?T:E,void 0),e=a?new c(E):new c;return void 0!==t&&A(e,"message",t),L&&A(e,"stack",C(e.stack,2)),this&&r(M,this)&&I(e,this,U),arguments.length>s&&o(e,arguments[s]),e}));if(U.prototype=M,"Error"!==D?S?S(U,f):n(U,f,{name:!0}):i&&u in c&&(O(U,c,u),O(U,c,"prepareStackTrace")),n(U,c),!_)try{M.name!==D&&A(M,"name",D),M.constructor=U}catch(E){}return U}}},2222:(E,T,t)=>{"use strict";var e=t(2109),R=t(7293),A=t(3157),r=t(111),S=t(7908),n=t(6244),O=t(7207),I=t(6135),N=t(5417),o=t(1194),C=t(5112),L=t(7392),i=C("isConcatSpreadable"),_=L>=51||!R((function(){var E=[];return E[i]=!1,E.concat()[0]!==E})),a=o("concat"),u=function(E){if(!r(E))return!1;var T=E[i];return void 0!==T?!!T:A(E)};e({target:"Array",proto:!0,arity:1,forced:!_||!a},{concat:function(E){var T,t,e,R,A,r=S(this),o=N(r,0),C=0;for(T=-1,e=arguments.length;T<e;T++)if(u(A=-1===T?r:arguments[T]))for(R=n(A),O(C+R),t=0;t<R;t++,C++)t in A&&I(o,C,A[t]);else O(C+1),I(o,C++,A);return o.length=C,o}})},7327:(E,T,t)=>{"use strict";var e=t(2109),R=t(2092).filter;e({target:"Array",proto:!0,forced:!t(1194)("filter")},{filter:function(E){return R(this,E,arguments.length>1?arguments[1]:void 0)}})},6535:(E,T,t)=>{"use strict";var e=t(2109),R=t(6790),A=t(9662),r=t(7908),S=t(6244),n=t(5417);e({target:"Array",proto:!0},{flatMap:function(E){var T,t=r(this),e=S(t);return A(E),(T=n(t,0)).length=R(T,t,t,e,0,1,E,arguments.length>1?arguments[1]:void 0),T}})},4944:(E,T,t)=>{"use strict";var e=t(2109),R=t(6790),A=t(7908),r=t(6244),S=t(9303),n=t(5417);e({target:"Array",proto:!0},{flat:function(){var E=arguments.length?arguments[0]:void 0,T=A(this),t=r(T),e=n(T,0);return e.length=R(e,T,T,t,0,void 0===E?1:S(E)),e}})},1038:(E,T,t)=>{var e=t(2109),R=t(8457);e({target:"Array",stat:!0,forced:!t(7072)((function(E){Array.from(E)}))},{from:R})},6699:(E,T,t)=>{"use strict";var e=t(2109),R=t(1318).includes,A=t(7293),r=t(1223);e({target:"Array",proto:!0,forced:A((function(){return!Array(1).includes()}))},{includes:function(E){return R(this,E,arguments.length>1?arguments[1]:void 0)}}),r("includes")},6992:(E,T,t)=>{"use strict";var e=t(5656),R=t(1223),A=t(7497),r=t(9909),S=t(3070).f,n=t(654),O=t(1913),I=t(9781),N="Array Iterator",o=r.set,C=r.getterFor(N);E.exports=n(Array,"Array",(function(E,T){o(this,{type:N,target:e(E),index:0,kind:T})}),(function(){var E=C(this),T=E.target,t=E.kind,e=E.index++;return!T||e>=T.length?(E.target=void 0,{value:void 0,done:!0}):"keys"==t?{value:e,done:!1}:"values"==t?{value:T[e],done:!1}:{value:[e,T[e]],done:!1}}),"values");var L=A.Arguments=A.Array;if(R("keys"),R("values"),R("entries"),!O&&I&&"values"!==L.name)try{S(L,"name",{value:"values"})}catch(E){}},9600:(E,T,t)=>{"use strict";var e=t(2109),R=t(1702),A=t(8361),r=t(5656),S=t(9341),n=R([].join),O=A!=Object,I=S("join",",");e({target:"Array",proto:!0,forced:O||!I},{join:function(E){return n(r(this),void 0===E?",":E)}})},1249:(E,T,t)=>{"use strict";var e=t(2109),R=t(2092).map;e({target:"Array",proto:!0,forced:!t(1194)("map")},{map:function(E){return R(this,E,arguments.length>1?arguments[1]:void 0)}})},7042:(E,T,t)=>{"use strict";var e=t(2109),R=t(3157),A=t(4411),r=t(111),S=t(1400),n=t(6244),O=t(5656),I=t(6135),N=t(5112),o=t(1194),C=t(206),L=o("slice"),i=N("species"),_=Array,a=Math.max;e({target:"Array",proto:!0,forced:!L},{slice:function(E,T){var t,e,N,o=O(this),L=n(o),u=S(E,L),s=S(void 0===T?L:T,L);if(R(o)&&(t=o.constructor,(A(t)&&(t===_||R(t.prototype))||r(t)&&null===(t=t[i]))&&(t=void 0),t===_||void 0===t))return C(o,u,s);for(e=new(void 0===t?_:t)(a(s-u,0)),N=0;u<s;u++,N++)u in o&&I(e,N,o[u]);return e.length=N,e}})},2707:(E,T,t)=>{"use strict";var e=t(2109),R=t(1702),A=t(9662),r=t(7908),S=t(6244),n=t(5117),O=t(1340),I=t(7293),N=t(4362),o=t(9341),C=t(8886),L=t(256),i=t(7392),_=t(8008),a=[],u=R(a.sort),s=R(a.push),P=I((function(){a.sort(void 0)})),D=I((function(){a.sort(null)})),c=o("sort"),M=!I((function(){if(i)return i<70;if(!(C&&C>3)){if(L)return!0;if(_)return _<603;var E,T,t,e,R="";for(E=65;E<76;E++){switch(T=String.fromCharCode(E),E){case 66:case 69:case 70:case 72:t=3;break;case 68:case 71:t=4;break;default:t=2}for(e=0;e<47;e++)a.push({k:T+e,v:t})}for(a.sort((function(E,T){return T.v-E.v})),e=0;e<a.length;e++)T=a[e].k.charAt(0),R.charAt(R.length-1)!==T&&(R+=T);return"DGBEFHACIJK"!==R}}));e({target:"Array",proto:!0,forced:P||!D||!c||!M},{sort:function(E){void 0!==E&&A(E);var T=r(this);if(M)return void 0===E?u(T):u(T,E);var t,e,R=[],I=S(T);for(e=0;e<I;e++)e in T&&s(R,T[e]);for(N(R,function(E){return function(T,t){return void 0===t?-1:void 0===T?1:void 0!==E?+E(T,t)||0:O(T)>O(t)?1:-1}}(E)),t=R.length,e=0;e<t;)T[e]=R[e++];for(;e<I;)n(T,e++);return T}})},9244:(E,T,t)=>{t(1223)("flatMap")},3792:(E,T,t)=>{t(1223)("flat")},1703:(E,T,t)=>{var e=t(2109),R=t(7854),A=t(2104),r=t(9191),S=R.WebAssembly,n=7!==Error("e",{cause:7}).cause,O=function(E,T){var t={};t[E]=r(E,T,n),e({global:!0,constructor:!0,arity:1,forced:n},t)},I=function(E,T){if(S&&S[E]){var t={};t[E]=r("WebAssembly."+E,T,n),e({target:"WebAssembly",stat:!0,constructor:!0,arity:1,forced:n},t)}};O("Error",(function(E){return function(T){return A(E,this,arguments)}})),O("EvalError",(function(E){return function(T){return A(E,this,arguments)}})),O("RangeError",(function(E){return function(T){return A(E,this,arguments)}})),O("ReferenceError",(function(E){return function(T){return A(E,this,arguments)}})),O("SyntaxError",(function(E){return function(T){return A(E,this,arguments)}})),O("TypeError",(function(E){return function(T){return A(E,this,arguments)}})),O("URIError",(function(E){return function(T){return A(E,this,arguments)}})),I("CompileError",(function(E){return function(T){return A(E,this,arguments)}})),I("LinkError",(function(E){return function(T){return A(E,this,arguments)}})),I("RuntimeError",(function(E){return function(T){return A(E,this,arguments)}}))},8309:(E,T,t)=>{var e=t(9781),R=t(6530).EXISTS,A=t(1702),r=t(3070).f,S=Function.prototype,n=A(S.toString),O=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,I=A(O.exec);e&&!R&&r(S,"name",{configurable:!0,get:function(){try{return I(O,n(this))[1]}catch(E){return""}}})},8862:(E,T,t)=>{var e=t(2109),R=t(5005),A=t(2104),r=t(6916),S=t(1702),n=t(7293),O=t(3157),I=t(614),N=t(111),o=t(2190),C=t(206),L=t(133),i=R("JSON","stringify"),_=S(/./.exec),a=S("".charAt),u=S("".charCodeAt),s=S("".replace),P=S(1..toString),D=/[\uD800-\uDFFF]/g,c=/^[\uD800-\uDBFF]$/,M=/^[\uDC00-\uDFFF]$/,f=!L||n((function(){var E=R("Symbol")();return"[null]"!=i([E])||"{}"!=i({a:E})||"{}"!=i(Object(E))})),U=n((function(){return'"\\udf06\\ud834"'!==i("\udf06\ud834")||'"\\udead"'!==i("\udead")})),l=function(E,T){var t=C(arguments),e=T;if((N(T)||void 0!==E)&&!o(E))return O(T)||(T=function(E,T){if(I(e)&&(T=r(e,this,E,T)),!o(T))return T}),t[1]=T,A(i,null,t)},p=function(E,T,t){var e=a(t,T-1),R=a(t,T+1);return _(c,E)&&!_(M,R)||_(M,E)&&!_(c,e)?"\\u"+P(u(E,0),16):E};i&&e({target:"JSON",stat:!0,arity:3,forced:f||U},{stringify:function(E,T,t){var e=C(arguments),R=A(f?l:i,null,e);return U&&"string"==typeof R?s(R,D,p):R}})},9098:(E,T,t)=>{"use strict";t(7710)("Map",(function(E){return function(){return E(this,arguments.length?arguments[0]:void 0)}}),t(5631))},1532:(E,T,t)=>{t(9098)},9601:(E,T,t)=>{var e=t(2109),R=t(1574);e({target:"Object",stat:!0,arity:2,forced:Object.assign!==R},{assign:R})},9720:(E,T,t)=>{var e=t(2109),R=t(4699).entries;e({target:"Object",stat:!0},{entries:function(E){return R(E)}})},8559:(E,T,t)=>{var e=t(2109),R=t(408),A=t(6135);e({target:"Object",stat:!0},{fromEntries:function(E){var T={};return R(E,(function(E,t){A(T,E,t)}),{AS_ENTRIES:!0}),T}})},9660:(E,T,t)=>{var e=t(2109),R=t(133),A=t(7293),r=t(5181),S=t(7908);e({target:"Object",stat:!0,forced:!R||A((function(){r.f(1)}))},{getOwnPropertySymbols:function(E){var T=r.f;return T?T(S(E)):[]}})},489:(E,T,t)=>{var e=t(2109),R=t(7293),A=t(7908),r=t(9518),S=t(8544);e({target:"Object",stat:!0,forced:R((function(){r(1)})),sham:!S},{getPrototypeOf:function(E){return r(A(E))}})},7941:(E,T,t)=>{var e=t(2109),R=t(7908),A=t(1956);e({target:"Object",stat:!0,forced:t(7293)((function(){A(1)}))},{keys:function(E){return A(R(E))}})},8304:(E,T,t)=>{t(2109)({target:"Object",stat:!0},{setPrototypeOf:t(7674)})},1539:(E,T,t)=>{var e=t(1694),R=t(8052),A=t(288);e||R(Object.prototype,"toString",A,{unsafe:!0})},6833:(E,T,t)=>{var e=t(2109),R=t(4699).values;e({target:"Object",stat:!0},{values:function(E){return R(E)}})},2419:(E,T,t)=>{var e=t(2109),R=t(5005),A=t(2104),r=t(7065),S=t(9483),n=t(9670),O=t(111),I=t(30),N=t(7293),o=R("Reflect","construct"),C=Object.prototype,L=[].push,i=N((function(){function E(){}return!(o((function(){}),[],E)instanceof E)})),_=!N((function(){o((function(){}))})),a=i||_;e({target:"Reflect",stat:!0,forced:a,sham:a},{construct:function(E,T){S(E),n(T);var t=arguments.length<3?E:S(arguments[2]);if(_&&!i)return o(E,T,t);if(E==t){switch(T.length){case 0:return new E;case 1:return new E(T[0]);case 2:return new E(T[0],T[1]);case 3:return new E(T[0],T[1],T[2]);case 4:return new E(T[0],T[1],T[2],T[3])}var e=[null];return A(L,e,T),new(A(r,E,e))}var R=t.prototype,N=I(O(R)?R:C),a=A(E,N,T);return O(a)?a:N}})},1299:(E,T,t)=>{var e=t(2109),R=t(7854),A=t(8003);e({global:!0},{Reflect:{}}),A(R.Reflect,"Reflect",!0)},4603:(E,T,t)=>{var e=t(9781),R=t(7854),A=t(1702),r=t(4705),S=t(9587),n=t(8880),O=t(8006).f,I=t(7976),N=t(7850),o=t(1340),C=t(4706),L=t(2999),i=t(2626),_=t(8052),a=t(7293),u=t(2597),s=t(9909).enforce,P=t(6340),D=t(5112),c=t(9441),M=t(7168),f=D("match"),U=R.RegExp,l=U.prototype,p=R.SyntaxError,G=A(l.exec),y=A("".charAt),h=A("".replace),B=A("".indexOf),d=A("".slice),v=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,F=/a/g,H=/a/g,Y=new U(F)!==F,V=L.MISSED_STICKY,b=L.UNSUPPORTED_Y;if(r("RegExp",e&&(!Y||V||c||M||a((function(){return H[f]=!1,U(F)!=F||U(H)==H||"/a/i"!=U(F,"i")}))))){for(var m=function(E,T){var t,e,R,A,r,O,L=I(l,this),i=N(E),_=void 0===T,a=[],P=E;if(!L&&i&&_&&E.constructor===m)return E;if((i||I(l,E))&&(E=E.source,_&&(T=C(P))),E=void 0===E?"":o(E),T=void 0===T?"":o(T),P=E,c&&"dotAll"in F&&(e=!!T&&B(T,"s")>-1)&&(T=h(T,/s/g,"")),t=T,V&&"sticky"in F&&(R=!!T&&B(T,"y")>-1)&&b&&(T=h(T,/y/g,"")),M&&(A=function(E){for(var T,t=E.length,e=0,R="",A=[],r={},S=!1,n=!1,O=0,I="";e<=t;e++){if("\\"===(T=y(E,e)))T+=y(E,++e);else if("]"===T)S=!1;else if(!S)switch(!0){case"["===T:S=!0;break;case"("===T:G(v,d(E,e+1))&&(e+=2,n=!0),R+=T,O++;continue;case">"===T&&n:if(""===I||u(r,I))throw new p("Invalid capture group name");r[I]=!0,A[A.length]=[I,O],n=!1,I="";continue}n?I+=T:R+=T}return[R,A]}(E),E=A[0],a=A[1]),r=S(U(E,T),L?this:l,m),(e||R||a.length)&&(O=s(r),e&&(O.dotAll=!0,O.raw=m(function(E){for(var T,t=E.length,e=0,R="",A=!1;e<=t;e++)"\\"!==(T=y(E,e))?A||"."!==T?("["===T?A=!0:"]"===T&&(A=!1),R+=T):R+="[\\s\\S]":R+=T+y(E,++e);return R}(E),t)),R&&(O.sticky=!0),a.length&&(O.groups=a)),E!==P)try{n(r,"source",""===P?"(?:)":P)}catch(E){}return r},g=O(U),X=0;g.length>X;)i(m,U,g[X++]);l.constructor=m,m.prototype=l,_(R,"RegExp",m,{constructor:!0})}P("RegExp")},8450:(E,T,t)=>{var e=t(9781),R=t(9441),A=t(4326),r=t(7045),S=t(9909).get,n=RegExp.prototype,O=TypeError;e&&R&&r(n,"dotAll",{configurable:!0,get:function(){if(this!==n){if("RegExp"===A(this))return!!S(this).dotAll;throw O("Incompatible receiver, RegExp required")}}})},4916:(E,T,t)=>{"use strict";var e=t(2109),R=t(2261);e({target:"RegExp",proto:!0,forced:/./.exec!==R},{exec:R})},8386:(E,T,t)=>{var e=t(9781),R=t(2999).MISSED_STICKY,A=t(4326),r=t(7045),S=t(9909).get,n=RegExp.prototype,O=TypeError;e&&R&&r(n,"sticky",{configurable:!0,get:function(){if(this!==n){if("RegExp"===A(this))return!!S(this).sticky;throw O("Incompatible receiver, RegExp required")}}})},7601:(E,T,t)=>{"use strict";t(4916);var e,R,A=t(2109),r=t(6916),S=t(1702),n=t(614),O=t(111),I=(e=!1,(R=/[ac]/).exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===R.test("abc")&&e),N=TypeError,o=S(/./.test);A({target:"RegExp",proto:!0,forced:!I},{test:function(E){var T=this.exec;if(!n(T))return o(this,E);var t=r(T,this,E);if(null!==t&&!O(t))throw new N("RegExp exec method returned something other than an Object or null");return!!t}})},9714:(E,T,t)=>{"use strict";var e=t(6530).PROPER,R=t(8052),A=t(9670),r=t(1340),S=t(7293),n=t(4706),O="toString",I=RegExp.prototype.toString,N=S((function(){return"/a/b"!=I.call({source:"a",flags:"b"})})),o=e&&I.name!=O;(N||o)&&R(RegExp.prototype,O,(function(){var E=A(this);return"/"+r(E.source)+"/"+r(n(E))}),{unsafe:!0})},7227:(E,T,t)=>{"use strict";t(7710)("Set",(function(E){return function(){return E(this,arguments.length?arguments[0]:void 0)}}),t(5631))},189:(E,T,t)=>{t(7227)},7852:(E,T,t)=>{"use strict";var e,R=t(2109),A=t(1702),r=t(1236).f,S=t(7466),n=t(1340),O=t(3929),I=t(4488),N=t(4964),o=t(1913),C=A("".endsWith),L=A("".slice),i=Math.min,_=N("endsWith");R({target:"String",proto:!0,forced:!(!o&&!_&&(e=r(String.prototype,"endsWith"),e&&!e.writable)||_)},{endsWith:function(E){var T=n(I(this));O(E);var t=arguments.length>1?arguments[1]:void 0,e=T.length,R=void 0===t?e:i(S(t),e),A=n(E);return C?C(T,A,R):L(T,R-A.length,R)===A}})},2023:(E,T,t)=>{"use strict";var e=t(2109),R=t(1702),A=t(3929),r=t(4488),S=t(1340),n=t(4964),O=R("".indexOf);e({target:"String",proto:!0,forced:!n("includes")},{includes:function(E){return!!~O(S(r(this)),S(A(E)),arguments.length>1?arguments[1]:void 0)}})},8783:(E,T,t)=>{"use strict";var e=t(8710).charAt,R=t(1340),A=t(9909),r=t(654),S="String Iterator",n=A.set,O=A.getterFor(S);r(String,"String",(function(E){n(this,{type:S,string:R(E),index:0})}),(function(){var E,T=O(this),t=T.string,R=T.index;return R>=t.length?{value:void 0,done:!0}:(E=e(t,R),T.index+=E.length,{value:E,done:!1})}))},4723:(E,T,t)=>{"use strict";var e=t(6916),R=t(7007),A=t(9670),r=t(7466),S=t(1340),n=t(4488),O=t(8173),I=t(1530),N=t(7651);R("match",(function(E,T,t){return[function(T){var t=n(this),R=null==T?void 0:O(T,E);return R?e(R,T,t):new RegExp(T)[E](S(t))},function(E){var e=A(this),R=S(E),n=t(T,e,R);if(n.done)return n.value;if(!e.global)return N(e,R);var O=e.unicode;e.lastIndex=0;for(var o,C=[],L=0;null!==(o=N(e,R));){var i=S(o[0]);C[L]=i,""===i&&(e.lastIndex=I(R,r(e.lastIndex),O)),L++}return 0===L?null:C}]}))},6528:(E,T,t)=>{"use strict";var e=t(2109),R=t(6650).end;e({target:"String",proto:!0,forced:t(4986)},{padEnd:function(E){return R(this,E,arguments.length>1?arguments[1]:void 0)}})},3112:(E,T,t)=>{"use strict";var e=t(2109),R=t(6650).start;e({target:"String",proto:!0,forced:t(4986)},{padStart:function(E){return R(this,E,arguments.length>1?arguments[1]:void 0)}})},2481:(E,T,t)=>{t(2109)({target:"String",proto:!0},{repeat:t(8415)})},5306:(E,T,t)=>{"use strict";var e=t(2104),R=t(6916),A=t(1702),r=t(7007),S=t(7293),n=t(9670),O=t(614),I=t(9303),N=t(7466),o=t(1340),C=t(4488),L=t(1530),i=t(8173),_=t(647),a=t(7651),u=t(5112)("replace"),s=Math.max,P=Math.min,D=A([].concat),c=A([].push),M=A("".indexOf),f=A("".slice),U="$0"==="a".replace(/./,"$0"),l=!!/./[u]&&""===/./[u]("a","$0");r("replace",(function(E,T,t){var A=l?"$":"$0";return[function(E,t){var e=C(this),A=null==E?void 0:i(E,u);return A?R(A,E,e,t):R(T,o(e),E,t)},function(E,R){var r=n(this),S=o(E);if("string"==typeof R&&-1===M(R,A)&&-1===M(R,"$<")){var C=t(T,r,S,R);if(C.done)return C.value}var i=O(R);i||(R=o(R));var u=r.global;if(u){var U=r.unicode;r.lastIndex=0}for(var l=[];;){var p=a(r,S);if(null===p)break;if(c(l,p),!u)break;""===o(p[0])&&(r.lastIndex=L(S,N(r.lastIndex),U))}for(var G,y="",h=0,B=0;B<l.length;B++){for(var d=o((p=l[B])[0]),v=s(P(I(p.index),S.length),0),F=[],H=1;H<p.length;H++)c(F,void 0===(G=p[H])?G:String(G));var Y=p.groups;if(i){var V=D([d],F,v,S);void 0!==Y&&c(V,Y);var b=o(e(R,void 0,V))}else b=_(d,S,v,F,Y,R);v>=h&&(y+=f(S,h,v)+b,h=v+d.length)}return y+f(S,h)}]}),!!S((function(){var E=/./;return E.exec=function(){var E=[];return E.groups={a:"7"},E},"7"!=="".replace(E,"$<a>")}))||!U||l)},3123:(E,T,t)=>{"use strict";var e=t(2104),R=t(6916),A=t(1702),r=t(7007),S=t(7850),n=t(9670),O=t(4488),I=t(6707),N=t(1530),o=t(7466),C=t(1340),L=t(8173),i=t(1589),_=t(7651),a=t(2261),u=t(2999),s=t(7293),P=u.UNSUPPORTED_Y,D=4294967295,c=Math.min,M=[].push,f=A(/./.exec),U=A(M),l=A("".slice);r("split",(function(E,T,t){var A;return A="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(E,t){var A=C(O(this)),r=void 0===t?D:t>>>0;if(0===r)return[];if(void 0===E)return[A];if(!S(E))return R(T,A,E,r);for(var n,I,N,o=[],L=(E.ignoreCase?"i":"")+(E.multiline?"m":"")+(E.unicode?"u":"")+(E.sticky?"y":""),_=0,u=new RegExp(E.source,L+"g");(n=R(a,u,A))&&!((I=u.lastIndex)>_&&(U(o,l(A,_,n.index)),n.length>1&&n.index<A.length&&e(M,o,i(n,1)),N=n[0].length,_=I,o.length>=r));)u.lastIndex===n.index&&u.lastIndex++;return _===A.length?!N&&f(u,"")||U(o,""):U(o,l(A,_)),o.length>r?i(o,0,r):o}:"0".split(void 0,0).length?function(E,t){return void 0===E&&0===t?[]:R(T,this,E,t)}:T,[function(T,t){var e=O(this),r=null==T?void 0:L(T,E);return r?R(r,T,e,t):R(A,C(e),T,t)},function(E,e){var R=n(this),r=C(E),S=t(A,R,r,e,A!==T);if(S.done)return S.value;var O=I(R,RegExp),L=R.unicode,i=(R.ignoreCase?"i":"")+(R.multiline?"m":"")+(R.unicode?"u":"")+(P?"g":"y"),a=new O(P?"^(?:"+R.source+")":R,i),u=void 0===e?D:e>>>0;if(0===u)return[];if(0===r.length)return null===_(a,r)?[r]:[];for(var s=0,M=0,f=[];M<r.length;){a.lastIndex=P?0:M;var p,G=_(a,P?l(r,M):r);if(null===G||(p=c(o(a.lastIndex+(P?M:0)),r.length))===s)M=N(r,M,L);else{if(U(f,l(r,s,M)),f.length===u)return f;for(var y=1;y<=G.length-1;y++)if(U(f,G[y]),f.length===u)return f;M=s=p}}return U(f,l(r,s)),f}]}),!!s((function(){var E=/(?:)/,T=E.exec;E.exec=function(){return T.apply(this,arguments)};var t="ab".split(E);return 2!==t.length||"a"!==t[0]||"b"!==t[1]})),P)},8702:(E,T,t)=>{t(3462);var e=t(2109),R=t(365);e({target:"String",proto:!0,name:"trimEnd",forced:"".trimEnd!==R},{trimEnd:R})},9967:(E,T,t)=>{var e=t(2109),R=t(3217);e({target:"String",proto:!0,name:"trimStart",forced:"".trimLeft!==R},{trimLeft:R})},3462:(E,T,t)=>{var e=t(2109),R=t(365);e({target:"String",proto:!0,name:"trimEnd",forced:"".trimRight!==R},{trimRight:R})},5674:(E,T,t)=>{t(9967);var e=t(2109),R=t(3217);e({target:"String",proto:!0,name:"trimStart",forced:"".trimStart!==R},{trimStart:R})},4032:(E,T,t)=>{"use strict";var e=t(2109),R=t(7854),A=t(6916),r=t(1702),S=t(1913),n=t(9781),O=t(133),I=t(7293),N=t(2597),o=t(7976),C=t(9670),L=t(5656),i=t(4948),_=t(1340),a=t(9114),u=t(30),s=t(1956),P=t(8006),D=t(1156),c=t(5181),M=t(1236),f=t(3070),U=t(6048),l=t(5296),p=t(8052),G=t(2309),y=t(6200),h=t(3501),B=t(9711),d=t(5112),v=t(6061),F=t(7235),H=t(6532),Y=t(8003),V=t(9909),b=t(2092).forEach,m=y("hidden"),g="Symbol",X=V.set,W=V.getterFor(g),K=Object.prototype,w=R.Symbol,x=w&&w.prototype,J=R.TypeError,k=R.QObject,j=M.f,Q=f.f,Z=D.f,$=l.f,q=r([].push),z=G("symbols"),EE=G("op-symbols"),TE=G("wks"),tE=!k||!k.prototype||!k.prototype.findChild,eE=n&&I((function(){return 7!=u(Q({},"a",{get:function(){return Q(this,"a",{value:7}).a}})).a}))?function(E,T,t){var e=j(K,T);e&&delete K[T],Q(E,T,t),e&&E!==K&&Q(K,T,e)}:Q,RE=function(E,T){var t=z[E]=u(x);return X(t,{type:g,tag:E,description:T}),n||(t.description=T),t},AE=function(E,T,t){E===K&&AE(EE,T,t),C(E);var e=i(T);return C(t),N(z,e)?(t.enumerable?(N(E,m)&&E[m][e]&&(E[m][e]=!1),t=u(t,{enumerable:a(0,!1)})):(N(E,m)||Q(E,m,a(1,{})),E[m][e]=!0),eE(E,e,t)):Q(E,e,t)},rE=function(E,T){C(E);var t=L(T),e=s(t).concat(IE(t));return b(e,(function(T){n&&!A(SE,t,T)||AE(E,T,t[T])})),E},SE=function(E){var T=i(E),t=A($,this,T);return!(this===K&&N(z,T)&&!N(EE,T))&&(!(t||!N(this,T)||!N(z,T)||N(this,m)&&this[m][T])||t)},nE=function(E,T){var t=L(E),e=i(T);if(t!==K||!N(z,e)||N(EE,e)){var R=j(t,e);return!R||!N(z,e)||N(t,m)&&t[m][e]||(R.enumerable=!0),R}},OE=function(E){var T=Z(L(E)),t=[];return b(T,(function(E){N(z,E)||N(h,E)||q(t,E)})),t},IE=function(E){var T=E===K,t=Z(T?EE:L(E)),e=[];return b(t,(function(E){!N(z,E)||T&&!N(K,E)||q(e,z[E])})),e};O||(p(x=(w=function(){if(o(x,this))throw J("Symbol is not a constructor");var E=arguments.length&&void 0!==arguments[0]?_(arguments[0]):void 0,T=B(E),t=function(E){this===K&&A(t,EE,E),N(this,m)&&N(this[m],T)&&(this[m][T]=!1),eE(this,T,a(1,E))};return n&&tE&&eE(K,T,{configurable:!0,set:t}),RE(T,E)}).prototype,"toString",(function(){return W(this).tag})),p(w,"withoutSetter",(function(E){return RE(B(E),E)})),l.f=SE,f.f=AE,U.f=rE,M.f=nE,P.f=D.f=OE,c.f=IE,v.f=function(E){return RE(d(E),E)},n&&(Q(x,"description",{configurable:!0,get:function(){return W(this).description}}),S||p(K,"propertyIsEnumerable",SE,{unsafe:!0}))),e({global:!0,constructor:!0,wrap:!0,forced:!O,sham:!O},{Symbol:w}),b(s(TE),(function(E){F(E)})),e({target:g,stat:!0,forced:!O},{useSetter:function(){tE=!0},useSimple:function(){tE=!1}}),e({target:"Object",stat:!0,forced:!O,sham:!n},{create:function(E,T){return void 0===T?u(E):rE(u(E),T)},defineProperty:AE,defineProperties:rE,getOwnPropertyDescriptor:nE}),e({target:"Object",stat:!0,forced:!O},{getOwnPropertyNames:OE}),H(),Y(w,g),h[m]=!0},1817:(E,T,t)=>{"use strict";var e=t(2109),R=t(9781),A=t(7854),r=t(1702),S=t(2597),n=t(614),O=t(7976),I=t(1340),N=t(3070).f,o=t(9920),C=A.Symbol,L=C&&C.prototype;if(R&&n(C)&&(!("description"in L)||void 0!==C().description)){var i={},_=function(){var E=arguments.length<1||void 0===arguments[0]?void 0:I(arguments[0]),T=O(L,this)?new C(E):void 0===E?C():C(E);return""===E&&(i[T]=!0),T};o(_,C),_.prototype=L,L.constructor=_;var a="Symbol(test)"==String(C("test")),u=r(L.toString),s=r(L.valueOf),P=/^Symbol\((.*)\)[^)]+$/,D=r("".replace),c=r("".slice);N(L,"description",{configurable:!0,get:function(){var E=s(this),T=u(E);if(S(i,E))return"";var t=a?c(T,7,-1):D(T,P,"$1");return""===t?void 0:t}}),e({global:!0,constructor:!0,forced:!0},{Symbol:_})}},763:(E,T,t)=>{var e=t(2109),R=t(5005),A=t(2597),r=t(1340),S=t(2309),n=t(735),O=S("string-to-symbol-registry"),I=S("symbol-to-string-registry");e({target:"Symbol",stat:!0,forced:!n},{for:function(E){var T=r(E);if(A(O,T))return O[T];var t=R("Symbol")(T);return O[T]=t,I[t]=T,t}})},2165:(E,T,t)=>{t(7235)("iterator")},2526:(E,T,t)=>{t(4032),t(763),t(6620),t(8862),t(9660)},6620:(E,T,t)=>{var e=t(2109),R=t(2597),A=t(2190),r=t(6330),S=t(2309),n=t(735),O=S("symbol-to-string-registry");e({target:"Symbol",stat:!0,forced:!n},{keyFor:function(E){if(!A(E))throw TypeError(r(E)+" is not a symbol");if(R(O,E))return O[E]}})},4747:(E,T,t)=>{var e=t(7854),R=t(8324),A=t(8509),r=t(8533),S=t(8880),n=function(E){if(E&&E.forEach!==r)try{S(E,"forEach",r)}catch(T){E.forEach=r}};for(var O in R)R[O]&&n(e[O]&&e[O].prototype);n(A)},3948:(E,T,t)=>{var e=t(7854),R=t(8324),A=t(8509),r=t(6992),S=t(8880),n=t(5112),O=n("iterator"),I=n("toStringTag"),N=r.values,o=function(E,T){if(E){if(E[O]!==N)try{S(E,O,N)}catch(T){E[O]=N}if(E[I]||S(E,I,T),R[T])for(var t in r)if(E[t]!==r[t])try{S(E,t,r[t])}catch(T){E[t]=r[t]}}};for(var C in R)o(e[C]&&e[C].prototype,C);o(A,"DOMTokenList")}},T={};function t(e){var R=T[e];if(void 0!==R)return R.exports;var A=T[e]={exports:{}};return E[e](A,A.exports,t),A.exports}t.d=(E,T)=>{for(var e in T)t.o(T,e)&&!t.o(E,e)&&Object.defineProperty(E,e,{enumerable:!0,get:T[e]})},t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(E){if("object"==typeof window)return window}}(),t.o=(E,T)=>Object.prototype.hasOwnProperty.call(E,T),t.r=E=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(E,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(E,"__esModule",{value:!0})};var e={};return(()=>{"use strict";function E(E){return"tabularLeft"===E.indentStyle||"tabularRight"===E.indentStyle?" ".repeat(10):E.useTabs?"\t":" ".repeat(E.tabWidth)}function T(E){return"tabularLeft"===E.indentStyle||"tabularRight"===E.indentStyle}function R(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}t.r(e),t.d(e,{ConfigError:()=>AR,format:()=>RR,formatters:()=>TR,supportedDialects:()=>tR}),t(7941),t(1703),t(9601),t(6699),t(2023),t(6833),t(1539),t(2526),t(1817),t(2165),t(6992),t(8783),t(3948),t(8304),t(489),t(9714),t(1299),t(2419),t(1532),t(2222),t(4944),t(3792),t(1249),t(7042),t(9600),t(1038),t(8309),t(4916),t(7601),t(8702),t(2481);var A,r=function(){function E(T){!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,E),this.params=T,this.index=0}var T,t;return T=E,(t=[{key:"get",value:function(E){var T=E.key,t=E.value;return this.params?T?this.params[T]:this.params[this.index++]:t}}])&&R(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),E}();!function(E){E.QUOTED_IDENTIFIER="QUOTED_IDENTIFIER",E.IDENTIFIER="IDENTIFIER",E.STRING="STRING",E.VARIABLE="VARIABLE",E.RESERVED_KEYWORD="RESERVED_KEYWORD",E.RESERVED_LOGICAL_OPERATOR="RESERVED_LOGICAL_OPERATOR",E.RESERVED_DEPENDENT_CLAUSE="RESERVED_DEPENDENT_CLAUSE",E.RESERVED_BINARY_COMMAND="RESERVED_BINARY_COMMAND",E.RESERVED_COMMAND="RESERVED_COMMAND",E.RESERVED_JOIN="RESERVED_JOIN",E.RESERVED_JOIN_CONDITION="RESERVED_JOIN_CONDITION",E.RESERVED_CASE_START="RESERVED_CASE_START",E.RESERVED_CASE_END="RESERVED_CASE_END",E.OPERATOR="OPERATOR",E.COMMA="COMMA",E.OPEN_PAREN="OPEN_PAREN",E.CLOSE_PAREN="CLOSE_PAREN",E.LINE_COMMENT="LINE_COMMENT",E.BLOCK_COMMENT="BLOCK_COMMENT",E.NUMBER="NUMBER",E.NAMED_PARAMETER="NAMED_PARAMETER",E.QUOTED_PARAMETER="QUOTED_PARAMETER",E.INDEXED_PARAMETER="INDEXED_PARAMETER",E.POSITIONAL_PARAMETER="POSITIONAL_PARAMETER",E.DELIMITER="DELIMITER",E.EOF="EOF"}(A||(A={}));var S,n={type:A.EOF,text:"«EOF»",value:"«EOF»"},O=function(E){return function(T){return T.type===E.type&&T.value===E.value}},I={AS:O({value:"AS",type:A.RESERVED_KEYWORD}),AND:O({value:"AND",type:A.RESERVED_LOGICAL_OPERATOR}),ARRAY:O({value:"ARRAY",type:A.RESERVED_KEYWORD}),BETWEEN:O({value:"BETWEEN",type:A.RESERVED_KEYWORD}),CASE:O({value:"CASE",type:A.RESERVED_CASE_START}),CAST:O({value:"CAST",type:A.RESERVED_KEYWORD}),BY:O({value:"BY",type:A.RESERVED_KEYWORD}),END:O({value:"END",type:A.RESERVED_CASE_END}),FROM:O({value:"FROM",type:A.RESERVED_COMMAND}),LIMIT:O({value:"LIMIT",type:A.RESERVED_COMMAND}),SELECT:O({value:"SELECT",type:A.RESERVED_COMMAND}),SET:O({value:"SET",type:A.RESERVED_COMMAND}),STRUCT:O({value:"STRUCT",type:A.RESERVED_KEYWORD}),TABLE:O({value:"TABLE",type:A.RESERVED_KEYWORD}),WINDOW:O({value:"WINDOW",type:A.RESERVED_COMMAND}),WITH:O({value:"WITH",type:A.RESERVED_COMMAND})},N=function(E){return E.type===A.RESERVED_KEYWORD||E.type===A.RESERVED_LOGICAL_OPERATOR||E.type===A.RESERVED_DEPENDENT_CLAUSE||E.type===A.RESERVED_JOIN_CONDITION||E.type===A.RESERVED_COMMAND||E.type===A.RESERVED_BINARY_COMMAND||E.type===A.RESERVED_JOIN||E.type===A.RESERVED_CASE_START||E.type===A.RESERVED_CASE_END};function o(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}!function(E){E.statement="statement",E.clause="clause",E.binary_clause="binary_clause",E.function_call="function_call",E.array_subscript="array_subscript",E.parenthesis="parenthesis",E.between_predicate="between_predicate",E.limit_clause="limit_clause",E.all_columns_asterisk="all_columns_asterisk",E.token="token"}(S||(S={}));var C=function(){function E(T){!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,E),this.tokens=T,this.index=0}var T,t;return T=E,t=[{key:"parse",value:function(){for(var E,T=[];E=this.statement();)T.push(E);return T}},{key:"statement",value:function(){for(var E=[];;){if(this.look().type===A.DELIMITER)return this.next(),{type:S.statement,children:E,hasSemicolon:!0};if(this.look().type===A.EOF)return E.length>0?{type:S.statement,children:E,hasSemicolon:!1}:void 0;E.push(this.expression())}}},{key:"expression",value:function(){return this.limitClause()||this.clause()||this.binaryClause()||this.functionCall()||this.arraySubscript()||this.parenthesis()||this.betweenPredicate()||this.allColumnsAsterisk()||this.nextTokenNode()}},{key:"clause",value:function(){if(this.look().type===A.RESERVED_COMMAND){var E=this.next(),T=this.expressionsUntilClauseEnd();return{type:S.clause,nameToken:E,children:T}}}},{key:"binaryClause",value:function(){if(this.look().type===A.RESERVED_BINARY_COMMAND){var E=this.next(),T=this.expressionsUntilClauseEnd();return{type:S.binary_clause,nameToken:E,children:T}}}},{key:"functionCall",value:function(){if((this.look().type===A.RESERVED_KEYWORD||this.look().type===A.IDENTIFIER)&&"("===this.look(1).value&&!this.look(1).whitespaceBefore)return{type:S.function_call,nameToken:this.next(),parenthesis:this.parenthesis()}}},{key:"arraySubscript",value:function(){if((this.look().type===A.RESERVED_KEYWORD||this.look().type===A.IDENTIFIER)&&"["===this.look(1).value)return{type:S.array_subscript,arrayToken:this.next(),parenthesis:this.parenthesis()}}},{key:"parenthesis",value:function(){if(this.look().type===A.OPEN_PAREN){for(var E=[],T=this.next().value,t="";this.look().type!==A.CLOSE_PAREN&&this.look().type!==A.EOF;)E.push(this.expression());return this.look().type===A.CLOSE_PAREN&&(t=this.next().value),{type:S.parenthesis,children:E,openParen:T,closeParen:t}}}},{key:"betweenPredicate",value:function(){if(I.BETWEEN(this.look())&&I.AND(this.look(2)))return{type:S.between_predicate,betweenToken:this.next(),expr1:this.next(),andToken:this.next(),expr2:this.next()}}},{key:"limitClause",value:function(){if(I.LIMIT(this.look())){var E=this.next(),T=this.expressionsUntilClauseEnd((function(E){return E.type===A.COMMA}));if(this.look().type===A.COMMA){this.next();var t=this.expressionsUntilClauseEnd();return{type:S.limit_clause,limitToken:E,offset:T,count:t}}return{type:S.limit_clause,limitToken:E,count:T}}}},{key:"allColumnsAsterisk",value:function(){if("*"===this.look().value&&I.SELECT(this.look(-1)))return this.next(),{type:S.all_columns_asterisk}}},{key:"expressionsUntilClauseEnd",value:function(){for(var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return!1},T=[];this.look().type!==A.RESERVED_COMMAND&&this.look().type!==A.RESERVED_BINARY_COMMAND&&this.look().type!==A.EOF&&this.look().type!==A.CLOSE_PAREN&&this.look().type!==A.DELIMITER&&!E(this.look());)T.push(this.expression());return T}},{key:"look",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.tokens[this.index+E]||n}},{key:"next",value:function(){return this.tokens[this.index++]||n}},{key:"nextTokenNode",value:function(){return{type:S.token,token:this.next()}}}],t&&o(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),E}();function L(E,T){if(E){if("string"==typeof E)return i(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?i(E,T):void 0}}function i(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}t(6535),t(9244),t(3123),t(4723),t(5306),t(5674),t(4603),t(8450),t(8386),t(189),t(2707);var _=function(E){return function(E){return function(E){if(Array.isArray(E))return i(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||L(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(new Set(E))},a=function(E){return E[E.length-1]},u=function(E){return E.sort((function(E,T){return T.length-E.length||E.localeCompare(T)}))},s=function(E){return E.reduce((function(E,T){return Math.max(E,T.length)}),0)};function P(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}var D=/^[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+/;function c(E,T,t){return function(E){for(var T=[],t=0;t<E.length;t++){for(var e=[E[t]];E[t].match(/.*,$/);)t++,e.push(E[t]);T.push(e)}return T}(E.split("\n")).flatMap((function(E){if(1===E.length)return E;if("tabular"===T)return function(E){var T=s(E);return M(E).map((function(t,e){return e===E.length-1?t:t+" ".repeat(T-t.length-1)+","}))}(E);if("before"===T)return function(E,T){return M(E).map((function(E,t){return 0===t?E:function(E,T){return E.replace(new RegExp(T+"$"),"")}(function(E,T){return function(E){if(Array.isArray(E))return E}(E)||function(E,T){var t=null==E?null:"undefined"!=typeof Symbol&&E[Symbol.iterator]||E["@@iterator"];if(null!=t){var e,R,A=[],r=!0,S=!1;try{for(t=t.call(E);!(r=(e=t.next()).done)&&(A.push(e.value),!T||A.length!==T);r=!0);}catch(E){S=!0,R=E}finally{try{r||null==t.return||t.return()}finally{if(S)throw R}}return A}}(E,T)||function(E,T){if(E){if("string"==typeof E)return P(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?P(E,T):void 0}}(E,T)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(E.match(D)||[""],1)[0],T)+T.replace(/ {2}$/,", ")+E.trimStart()}))}(E,t);throw new Error("Unexpected commaPosition: ".concat(T))})).join("\n")}function M(E){return E.map((function(E){return E.replace(/,$/,"")}))}function f(E){return function(E){if(Array.isArray(E))return U(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return U(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?U(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function U(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function l(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function p(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}t(4747);var G,y=function(){function E(T){!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,E),this.expressionWidth=T}var T,t;return T=E,t=[{key:"isInlineBlock",value:function(E){return this.inlineWidth(E)<=this.expressionWidth}},{key:"inlineWidth",value:function(E){var T,t=2,e=function(E,T){var t="undefined"!=typeof Symbol&&E[Symbol.iterator]||E["@@iterator"];if(!t){if(Array.isArray(E)||(t=function(E,T){if(E){if("string"==typeof E)return l(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?l(E,T):void 0}}(E))||T&&E&&"number"==typeof E.length){t&&(E=t);var e=0,R=function(){};return{s:R,n:function(){return e>=E.length?{done:!0}:{done:!1,value:E[e++]}},e:function(E){throw E},f:R}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var A,r=!0,S=!1;return{s:function(){t=t.call(E)},n:function(){var E=t.next();return r=E.done,E},e:function(E){S=!0,A=E},f:function(){try{r||null==t.return||t.return()}finally{if(S)throw A}}}}(E.children);try{for(e.s();!(T=e.n()).done;){var R=T.value;switch(R.type){case S.function_call:t+=R.nameToken.value.length+this.inlineWidth(R.parenthesis);break;case S.array_subscript:t+=R.arrayToken.value.length+this.inlineWidth(R.parenthesis);break;case S.parenthesis:t+=this.inlineWidth(R);break;case S.between_predicate:t+=this.betweenWidth(R);break;case S.clause:case S.limit_clause:case S.binary_clause:return 1/0;case S.all_columns_asterisk:t+=1;break;case S.token:if(t+=R.token.value.length,this.isForbiddenToken(R.token))return 1/0}if(t>this.expressionWidth)return t}}catch(E){e.e(E)}finally{e.f()}return t}},{key:"betweenWidth",value:function(E){return function(E){var T,t=0,e=function(E,T){var t="undefined"!=typeof Symbol&&E[Symbol.iterator]||E["@@iterator"];if(!t){if(Array.isArray(E)||(t=L(E))){t&&(E=t);var e=0,R=function(){};return{s:R,n:function(){return e>=E.length?{done:!0}:{done:!1,value:E[e++]}},e:function(E){throw E},f:R}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var A,r=!0,S=!1;return{s:function(){t=t.call(E)},n:function(){var E=t.next();return r=E.done,E},e:function(E){S=!0,A=E},f:function(){try{r||null==t.return||t.return()}finally{if(S)throw A}}}}(E);try{for(e.s();!(T=e.n()).done;)t+=T.value}catch(E){e.e(E)}finally{e.f()}return t}([E.betweenToken,E.expr1,E.andToken,E.expr2].map((function(E){return E.value.length})))}},{key:"isForbiddenToken",value:function(E){return E.type===A.RESERVED_LOGICAL_OPERATOR||E.type===A.LINE_COMMENT||E.type===A.BLOCK_COMMENT||I.CASE(E)}}],t&&p(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),E}();function h(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}!function(E){E[E.SPACE=0]="SPACE",E[E.NO_SPACE=1]="NO_SPACE",E[E.NEWLINE=2]="NEWLINE",E[E.INDENT=3]="INDENT",E[E.SINGLE_INDENT=4]="SINGLE_INDENT"}(G||(G={}));var B=function(){function E(T){!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,E),this.indentation=T,this.items=[]}var T,t;return T=E,t=[{key:"add",value:function(){for(var E=arguments.length,T=new Array(E),t=0;t<E;t++)T[t]=arguments[t];for(var e=0,R=T;e<R.length;e++){var A=R[e];switch(A){case G.SPACE:this.items.push(G.SPACE);break;case G.NO_SPACE:this.trimHorizontalWhitespace();break;case G.NEWLINE:this.trimHorizontalWhitespace(),this.addNewline();break;case G.INDENT:this.addIndentation();break;case G.SINGLE_INDENT:this.items.push(G.SINGLE_INDENT);break;default:this.items.push(A)}}}},{key:"trimHorizontalWhitespace",value:function(){for(;d(a(this.items));)this.items.pop()}},{key:"addNewline",value:function(){this.items.length>0&&a(this.items)!==G.NEWLINE&&this.items.push(G.NEWLINE)}},{key:"addIndentation",value:function(){for(var E=0;E<this.indentation.getLevel();E++)this.items.push(G.SINGLE_INDENT)}},{key:"toString",value:function(){var E=this;return this.items.map((function(T){return E.itemToString(T)})).join("")}},{key:"itemToString",value:function(E){switch(E){case G.SPACE:return" ";case G.NEWLINE:return"\n";case G.SINGLE_INDENT:return this.indentation.getSingleIndent();default:return E}}}],t&&h(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),E}(),d=function(E){return E===G.SPACE||E===G.SINGLE_INDENT};function v(E,T){if(E){if("string"==typeof E)return F(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?F(E,T):void 0}}function F(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function H(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}function Y(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}t(6528),t(3112);var V=function(){function E(T){var t=T.cfg,e=T.params,R=T.layout,A=T.inline,r=void 0!==A&&A;!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,E),this.inline=!1,this.nodes=[],this.index=-1,this.cfg=t,this.inline=r,this.inlineBlock=new y(this.cfg.expressionWidth),this.params=e,this.layout=R}var t,e;return t=E,e=[{key:"format",value:function(E){for(this.nodes=E,this.index=0;this.index<this.nodes.length;this.index++){var T=this.nodes[this.index];switch(T.type){case S.function_call:this.formatFunctionCall(T);break;case S.array_subscript:this.formatArraySubscript(T);break;case S.parenthesis:this.formatParenthesis(T);break;case S.between_predicate:this.formatBetweenPredicate(T);break;case S.clause:this.formatClause(T);break;case S.binary_clause:this.formatBinaryClause(T);break;case S.limit_clause:this.formatLimitClause(T);break;case S.all_columns_asterisk:this.formatAllColumnsAsterisk(T);break;case S.token:this.formatToken(T.token)}}return this.layout}},{key:"formatFunctionCall",value:function(E){this.layout.add(this.show(E.nameToken)),this.formatParenthesis(E.parenthesis)}},{key:"formatArraySubscript",value:function(E){this.layout.add(this.show(E.arrayToken)),this.formatParenthesis(E.parenthesis)}},{key:"formatParenthesis",value:function(E){var t=this.inlineBlock.isInlineBlock(E);t?(this.layout.add(E.openParen),this.layout=this.formatSubExpression(E.children,t),this.layout.add(G.NO_SPACE,E.closeParen,G.SPACE)):(this.layout.add(E.openParen,G.NEWLINE),T(this.cfg)?(this.layout.add(G.INDENT),this.layout=this.formatSubExpression(E.children,t)):(this.layout.indentation.increaseBlockLevel(),this.layout.add(G.INDENT),this.layout=this.formatSubExpression(E.children,t),this.layout.indentation.decreaseBlockLevel()),this.layout.add(G.NEWLINE,G.INDENT,E.closeParen,G.SPACE))}},{key:"formatBetweenPredicate",value:function(E){this.layout.add(this.show(E.betweenToken),G.SPACE,this.show(E.expr1),G.SPACE,this.show(E.andToken),G.SPACE,this.show(E.expr2),G.SPACE)}},{key:"formatClause",value:function(E){T(this.cfg)?this.layout.add(G.NEWLINE,G.INDENT,this.show(E.nameToken),G.SPACE):this.layout.add(G.NEWLINE,G.INDENT,this.show(E.nameToken),G.NEWLINE),this.layout.indentation.increaseTopLevel(),T(this.cfg)||this.layout.add(G.INDENT),this.layout=this.formatSubExpression(E.children),this.layout.indentation.decreaseTopLevel()}},{key:"formatBinaryClause",value:function(E){this.layout.indentation.decreaseTopLevel(),this.layout.add(G.NEWLINE,G.INDENT,this.show(E.nameToken),G.NEWLINE),this.layout.add(G.INDENT),this.layout=this.formatSubExpression(E.children)}},{key:"formatLimitClause",value:function(E){this.layout.add(G.NEWLINE,G.INDENT,this.show(E.limitToken)),this.layout.indentation.increaseTopLevel(),E.offset?(this.layout.add(G.NEWLINE,G.INDENT),this.layout=this.formatSubExpression(E.offset),this.layout.add(G.NO_SPACE,",",G.SPACE),this.layout=this.formatSubExpression(E.count)):(this.layout.add(G.NEWLINE,G.INDENT),this.layout=this.formatSubExpression(E.count)),this.layout.indentation.decreaseTopLevel()}},{key:"formatAllColumnsAsterisk",value:function(E){this.layout.add("*",G.SPACE)}},{key:"formatSubExpression",value:function(T){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.inline;return new E({cfg:this.cfg,params:this.params,layout:this.layout,inline:t}).format(T)}},{key:"formatToken",value:function(E){switch(E.type){case A.LINE_COMMENT:return this.formatLineComment(E);case A.BLOCK_COMMENT:return this.formatBlockComment(E);case A.RESERVED_JOIN:return this.formatJoin(E);case A.RESERVED_DEPENDENT_CLAUSE:return this.formatDependentClause(E);case A.RESERVED_JOIN_CONDITION:return this.formatJoinCondition(E);case A.RESERVED_LOGICAL_OPERATOR:return this.formatLogicalOperator(E);case A.RESERVED_KEYWORD:return this.formatKeyword(E);case A.RESERVED_CASE_START:return this.formatCaseStart(E);case A.RESERVED_CASE_END:return this.formatCaseEnd(E);case A.NAMED_PARAMETER:case A.QUOTED_PARAMETER:case A.INDEXED_PARAMETER:case A.POSITIONAL_PARAMETER:return this.formatParameter(E);case A.COMMA:return this.formatComma(E);case A.OPERATOR:return this.formatOperator(E);case A.IDENTIFIER:case A.QUOTED_IDENTIFIER:case A.STRING:case A.NUMBER:case A.VARIABLE:return this.formatWord(E);default:throw new Error("Unexpected token type: ".concat(E.type))}}},{key:"formatWord",value:function(E){this.layout.add(this.show(E),G.SPACE)}},{key:"formatLineComment",value:function(E){this.layout.add(this.show(E),G.NEWLINE,G.INDENT)}},{key:"formatBlockComment",value:function(E){var T=this;this.splitBlockComment(E.value).forEach((function(E){T.layout.add(G.NEWLINE,G.INDENT,E)})),this.layout.add(G.NEWLINE,G.INDENT)}},{key:"splitBlockComment",value:function(E){return E.split(/\n/).map((function(E){return/^\s*\*/.test(E)?" "+E.replace(/^\s*/,""):E.replace(/^\s*/,"")}))}},{key:"formatJoin",value:function(E){T(this.cfg)?(this.layout.indentation.decreaseTopLevel(),this.layout.add(G.NEWLINE,G.INDENT,this.show(E),G.SPACE),this.layout.indentation.increaseTopLevel()):this.layout.add(G.NEWLINE,G.INDENT,this.show(E),G.SPACE)}},{key:"formatKeyword",value:function(E){this.layout.add(this.show(E),G.SPACE)}},{key:"formatDependentClause",value:function(E){this.layout.add(G.NEWLINE,G.INDENT,this.show(E),G.SPACE)}},{key:"formatJoinCondition",value:function(E){this.layout.add(this.show(E),G.SPACE)}},{key:"formatOperator",value:function(E){":"!==E.value?"."!==E.value&&"::"!==E.value?this.cfg.denseOperators?this.layout.add(G.NO_SPACE,this.show(E)):this.layout.add(this.show(E),G.SPACE):this.layout.add(G.NO_SPACE,this.show(E)):this.layout.add(G.NO_SPACE,this.show(E),G.SPACE)}},{key:"formatLogicalOperator",value:function(E){"before"===this.cfg.logicalOperatorNewline?T(this.cfg)?(this.layout.indentation.decreaseTopLevel(),this.layout.add(G.NEWLINE,G.INDENT,this.show(E),G.SPACE),this.layout.indentation.increaseTopLevel()):this.layout.add(G.NEWLINE,G.INDENT,this.show(E),G.SPACE):this.layout.add(this.show(E),G.NEWLINE,G.INDENT)}},{key:"formatCaseStart",value:function(E){this.layout.indentation.increaseBlockLevel(),this.layout.add(this.show(E),G.NEWLINE,G.INDENT)}},{key:"formatCaseEnd",value:function(E){this.formatMultilineBlockEnd(E)}},{key:"formatMultilineBlockEnd",value:function(E){this.layout.indentation.decreaseBlockLevel(),this.layout.add(G.NEWLINE,G.INDENT,this.show(E),G.SPACE)}},{key:"formatParameter",value:function(E){this.layout.add(this.params.get(E),G.SPACE)}},{key:"formatComma",value:function(E){this.inline?this.layout.add(G.NO_SPACE,this.show(E),G.SPACE):this.layout.add(G.NO_SPACE,this.show(E),G.NEWLINE,G.INDENT)}},{key:"show",value:function(E){return function(E){return E.type===A.RESERVED_LOGICAL_OPERATOR||E.type===A.RESERVED_DEPENDENT_CLAUSE||E.type===A.RESERVED_COMMAND||E.type===A.RESERVED_BINARY_COMMAND||E.type===A.RESERVED_JOIN}(E)?function(E,T){if("standard"===T)return E;var t,e=[];if(E.length>=10&&E.includes(" ")){var R=function(E){if(Array.isArray(E))return E}(t=E.split(" "))||H(t)||v(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();E=R[0],e=R.slice(1)}return(E="tabularLeft"===T?E.padEnd(9," "):E.padStart(9," "))+[""].concat(function(E){return function(E){if(Array.isArray(E))return F(E)}(E)||H(E)||v(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(e)).join(" ")}(this.showToken(E),this.cfg.indentStyle):this.showToken(E)}},{key:"showToken",value:function(E){if(!N(E))return E.value;switch(this.cfg.keywordCase){case"preserve":return E.text.replace(/[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+/g," ");case"upper":return E.value;case"lower":return E.value.toLowerCase()}}}],e&&Y(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),E}();function b(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function m(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}t(7852),t(7327);var g=function(){function E(T){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];b(this,E),this.keywordCase=T,this.detectedCase=this.autoDetectCase(t)}var T,t;return T=E,(t=[{key:"autoDetectCase",value:function(E){var T=E.filter(I.AS);return T.filter((function(E){return"AS"===E.text})).length>T.length/2?"upper":"lower"}},{key:"token",value:function(){return{type:A.RESERVED_KEYWORD,value:this.asTokenValue(),text:this.asTokenValue()}}},{key:"asTokenValue",value:function(){return"upper"===("preserve"===this.keywordCase?this.detectedCase:this.keywordCase)?"AS":"as"}}])&&m(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),E}();function X(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}var W=function(){function E(T,t){!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,E),this.index=0,this.tokens=[],this.previousReservedToken=n,this.previousCommandToken=n,this.aliasAs=T.aliasAs,this.asTokenFactory=new g(T.keywordCase,t),this.tokens=t}var T,t;return T=E,t=[{key:"process",value:function(){var E=[];for(this.index=0;this.index<this.tokens.length;this.index++){var T=this.tokens[this.index];N(T)&&(this.previousReservedToken=T,T.type===A.RESERVED_COMMAND&&(this.previousCommandToken=T)),I.AS(T)?this.shouldRemove()||E.push(T):T.type===A.IDENTIFIER||T.type===A.NUMBER||T.type===A.STRING||T.type===A.VARIABLE?(this.shouldAddBefore(T)&&E.push(this.asTokenFactory.token()),E.push(T),this.shouldAddAfter()&&E.push(this.asTokenFactory.token())):E.push(T)}return E}},{key:"shouldAddBefore",value:function(E){return this.isMissingTableAlias(E)||this.isMissingSelectColumnAlias(E)}},{key:"isMissingTableAlias",value:function(E){return"always"===this.aliasAs&&E.type===A.IDENTIFIER&&")"===this.lookBehind().value}},{key:"isMissingSelectColumnAlias",value:function(E){var T=this.lookBehind(),t=this.lookAhead();return("always"===this.aliasAs||"select"===this.aliasAs)&&this.isWithinSelect()&&E.type===A.IDENTIFIER&&(I.END(T)||(T.type===A.IDENTIFIER||T.type===A.NUMBER)&&(t.type===A.COMMA||function(E){return E.type===A.RESERVED_COMMAND||E.type===A.RESERVED_BINARY_COMMAND}(t)))}},{key:"shouldAddAfter",value:function(){return this.isEdgeCaseCTE()||this.isEdgeCaseCreateTable()||this.isMissingTypeCastAs()}},{key:"isMissingTypeCastAs",value:function(){return"never"===this.aliasAs&&this.isWithinSelect()&&I.CAST(this.getPreviousReservedToken())&&I.AS(this.lookAhead())&&(this.lookAhead(2).type===A.IDENTIFIER||this.lookAhead(2).type===A.RESERVED_KEYWORD)&&")"===this.lookAhead(3).value}},{key:"isEdgeCaseCTE",value:function(){var E=this.lookAhead();return"never"===this.aliasAs&&I.WITH(this.lookBehind())&&("("===E.value||I.AS(E)&&"("===this.lookAhead(2).value)}},{key:"isEdgeCaseCreateTable",value:function(){var E=this.lookBehind(),T=this.lookAhead();return"never"===this.aliasAs&&(I.TABLE(E)||E.value.endsWith("TABLE"))&&(I.WITH(T)||I.AS(T)&&I.WITH(this.lookAhead(2)))}},{key:"shouldRemove",value:function(){return"never"===this.aliasAs||"select"===this.aliasAs&&this.isRemovableNonSelectAs()}},{key:"isRemovableNonSelectAs",value:function(){return")"===this.lookBehind().value&&!this.isWithinSelect()&&"("!==this.lookAhead().value}},{key:"getPreviousReservedToken",value:function(){return this.previousReservedToken}},{key:"isWithinSelect",value:function(){return I.SELECT(this.previousCommandToken)}},{key:"lookBehind",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.lookAhead(-E)}},{key:"lookAhead",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.tokens[this.index+E]||n}}],t&&X(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),E}();function K(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}var w="top-level",x=function(){function E(T){!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,E),this.indent=T,this.indentTypes=[]}var T,t;return T=E,(t=[{key:"getSingleIndent",value:function(){return this.indent}},{key:"getIndent",value:function(){return this.indent.repeat(this.indentTypes.length)}},{key:"getLevel",value:function(){return this.indentTypes.length}},{key:"increaseTopLevel",value:function(){this.indentTypes.push(w)}},{key:"increaseBlockLevel",value:function(){this.indentTypes.push("block-level")}},{key:"decreaseTopLevel",value:function(){this.indentTypes.length>0&&a(this.indentTypes)===w&&this.indentTypes.pop()}},{key:"decreaseBlockLevel",value:function(){for(;this.indentTypes.length>0&&this.indentTypes.pop()===w;);}},{key:"resetIndentation",value:function(){this.indentTypes=[]}}])&&K(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),E}();function J(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}var k=function(){function T(E){!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,T),this.cfg=E,this.params=new r(this.cfg.params)}var t,e;return t=T,(e=[{key:"tokenizer",value:function(){throw new Error("tokenizer() not implemented by subclass")}},{key:"cachedTokenizer",value:function(){var E=this.constructor;return E.cachedTokenizer||(E.cachedTokenizer=this.tokenizer()),E.cachedTokenizer}},{key:"format",value:function(E){var T=this.cachedTokenizer().tokenize(E),t=new W(this.cfg,T).process(),e=new C(t).parse(),R=this.formatAst(e);return this.postFormat(R).trimEnd()}},{key:"formatAst",value:function(E){var T=this;return E.map((function(E){return T.formatStatement(E)})).join("\n".repeat(this.cfg.linesBetweenQueries+1))}},{key:"formatStatement",value:function(T){var t=new V({cfg:this.cfg,params:this.params,layout:new B(new x(E(this.cfg)))}).format(T.children);return T.hasSemicolon&&(this.cfg.newlineBeforeSemicolon?t.add(G.NEWLINE,";"):t.add(G.NO_SPACE,";")),t.toString()}},{key:"postFormat",value:function(T){return this.cfg.tabulateAlias&&(T=function(E){for(var T=E.split("\n"),t=[],e=0;e<T.length;e++)T[e].match(/^\s*SELECT/i)&&"continue"===function(){var E=[];if(T[e].match(/.*,$/))E=[T[e]];else{if(t.push(T[e]),T[e].match(/^\s*SELECT\s+.+(?!,$)/i))return"continue";E.push(T[++e])}for(;T[e++].match(/.*,$/);)E.push(T[e]);var R=E.map((function(E){return{line:E,matches:E.match(/(^.*?\S) (AS )?(\S+,?$)/i)}})).map((function(E){var T=E.line,t=E.matches;return t?{precedingText:t[1],as:t[2],alias:t[3]}:{precedingText:T}})),A=s(R.map((function(E){return E.precedingText.replace(/\s*,\s*$/,"")})));E=R.map((function(E){var T=E.precedingText,t=E.as,e=E.alias;return T+(e?" ".repeat(A-T.length+1)+(null!=t?t:"")+e:"")})),t=[].concat(f(t),f(E))}()||t.push(T[e]);return t.join("\n")}(T)),"before"!==this.cfg.commaPosition&&"tabular"!==this.cfg.commaPosition||(T=c(T,this.cfg.commaPosition,E(this.cfg))),T}}])&&J(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),T}(),j=(t(8559),t(9720),function(E){return E.replace(/[\$\(-\+\.\?\[-\^\{-\}]/g,"\\$&")}),Q=/^(?!)/,Z=new RegExp("([\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]+)","y"),$=function(E){return new RegExp("(?:".concat(E,")"),"uy")},q=function(E){return E.split("").map((function(E){return/ /g.test(E)?"\\s+":"[".concat(E.toUpperCase()).concat(E.toLowerCase(),"]")})).join("")},z=function(E){return 1===E.length?j(E):"\\b"+E+"\\b"},EE=function(E){return E+"(?:-"+E+")*"},TE=function(E){return $(E.map(z).join("|"))},tE=function(E){var T=E.rest,t=E.dashes;return T||t?"(?![".concat(T||"").concat(t?"-":"","])"):""},eE=function(E){var T=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(0===E.length)return/^\b$/;var t=tE(T),e=u(E).map(q).join("|").replace(/ /g,"\\s+");return new RegExp("(?:".concat(e,")").concat(t,"\\b"),"iuy")},RE=function(E,T){if(E.length){var t=E.map(j).join("|");return $("(?:".concat(t,")(?:").concat(T,")"))}},AE={"``":"(?:`[^`]*(?:$|`))+","[]":"(?:\\[[^\\]]*(?:$|\\]))(?:\\][^\\]]*(?:$|\\]))*",'""':'(?:"[^"\\\\]*(?:\\\\.[^"\\\\]*)*(?:"|$))+',"''":"(?:'[^'\\\\]*(?:\\\\.[^'\\\\]*)*(?:'|$))+",$$:"(?<tag>\\$\\w*\\$)[\\s\\S]*?(?:\\k<tag>|$)","'''..'''":"'''[^\\\\]*?(?:\\\\.[^\\\\]*?)*?(?:'''|$)",'""".."""':'"""[^\\\\]*?(?:\\\\.[^\\\\]*?)*?(?:"""|$)',"{}":"(?:\\{[^\\}]*(?:$|\\}))"},rE=function(E){return"string"==typeof E?AE[E]:(t=(T=E).prefixes,e=T.requirePrefix,"(?:".concat(t.map(q).join("|")).concat(e?"":"|",")")+AE[E.quote]);var T,t,e},SE=function(E){return E.map(rE).join("|")},nE=function(E){return $(SE(E))},OE=function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return $(IE(E))},IE=function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},T=E.first,t=E.rest,e=E.dashes,R="\\w_",A="\\d",r=j(null!=T?T:""),S=j(null!=t?t:""),n="[".concat(R).concat(r,"][").concat(R).concat(A).concat(S,"]*");return e?EE(n):n};function NE(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}var oE=function(){function E(T){!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,E),this.input="",this.index=0,this.rules=T}var T,t;return T=E,(t=[{key:"tokenize",value:function(E){this.input=E,this.index=0;for(var T,t=[];this.index<this.input.length;){var e=this.getWhitespace();if(this.index<this.input.length){if(!(T=this.getNextToken(T)))throw new Error('Parse error: Unexpected "'.concat(E.slice(this.index,100),'"'));t.push(Object.assign(Object.assign({},T),{whitespaceBefore:e}))}}return t}},{key:"getWhitespace",value:function(){Z.lastIndex=this.index;var E=Z.exec(this.input);return E?(this.index+=E[0].length,E[0]):""}},{key:"getNextToken",value:function(E){return this.matchToken(A.BLOCK_COMMENT)||this.matchToken(A.LINE_COMMENT)||this.matchToken(A.COMMA)||this.matchToken(A.OPEN_PAREN)||this.matchToken(A.CLOSE_PAREN)||this.matchToken(A.QUOTED_IDENTIFIER)||this.matchToken(A.NUMBER)||this.matchReservedWordToken(E)||this.matchPlaceholderToken(A.NAMED_PARAMETER)||this.matchPlaceholderToken(A.QUOTED_PARAMETER)||this.matchPlaceholderToken(A.INDEXED_PARAMETER)||this.matchPlaceholderToken(A.POSITIONAL_PARAMETER)||this.matchToken(A.VARIABLE)||this.matchToken(A.STRING)||this.matchToken(A.IDENTIFIER)||this.matchToken(A.DELIMITER)||this.matchToken(A.OPERATOR)}},{key:"matchPlaceholderToken",value:function(E){if(E in this.rules){var T=this.matchToken(E),t=this.rules[E];if(T)return(null==t?void 0:t.key)?Object.assign(Object.assign({},T),{key:t.key(T.value)}):T}}},{key:"matchReservedWordToken",value:function(E){if("."!==(null==E?void 0:E.value))return this.matchToken(A.RESERVED_CASE_START)||this.matchToken(A.RESERVED_CASE_END)||this.matchToken(A.RESERVED_COMMAND)||this.matchToken(A.RESERVED_BINARY_COMMAND)||this.matchToken(A.RESERVED_DEPENDENT_CLAUSE)||this.matchToken(A.RESERVED_JOIN)||this.matchToken(A.RESERVED_KEYWORD)||this.matchToken(A.RESERVED_LOGICAL_OPERATOR)||this.matchToken(A.RESERVED_JOIN_CONDITION)}},{key:"matchToken",value:function(E){var T=this.rules[E];if(!T)throw Error("Unknown token type found: ".concat(E));return this.match({type:E,regex:T.regex,transform:T.value})}},{key:"match",value:function(E){var T=E.type,t=E.regex,e=E.transform;t.lastIndex=this.index;var R=t.exec(this.input);if(R){var A=R[0];return this.index+=A.length,{type:T,text:A,value:e?e(A):A}}}}])&&NE(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),E}();function CE(E,T,t){return T in E?Object.defineProperty(E,T,{value:t,enumerable:!0,configurable:!0,writable:!0}):E[T]=t,E}function LE(E,T){if(E){if("string"==typeof E)return iE(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?iE(E,T):void 0}}function iE(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function _E(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}var aE=function(){function E(T){var t,e,R,r,S,n,O,I,N,o;!function(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}(this,E);var C,L,i,_,a=this.validRules((CE(t={},A.BLOCK_COMMENT,{regex:new RegExp("(\\/\\*(?:(?![])[\\s\\S])*?(?:\\*\\/|$))","y")}),CE(t,A.LINE_COMMENT,{regex:(i=null!==(e=T.lineCommentTypes)&&void 0!==e?e:["--"],new RegExp("(?:".concat(i.map(j).join("|"),").*?(?=\r\n|\r|\n|$)"),"uy"))}),CE(t,A.COMMA,{regex:new RegExp("[,]","y")}),CE(t,A.OPEN_PAREN,{regex:TE(null!==(R=T.openParens)&&void 0!==R?R:["("])}),CE(t,A.CLOSE_PAREN,{regex:TE(null!==(r=T.closeParens)&&void 0!==r?r:[")"])}),CE(t,A.QUOTED_IDENTIFIER,{regex:nE(T.identTypes)}),CE(t,A.NUMBER,{regex:new RegExp("(?:0x[0-9A-Fa-f]+|0b[01]+|(?:\\x2D[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*)?[0-9]+(?:\\.[0-9]*)?(?:[Ee][\\+\\x2D]?[0-9]+(?:\\.[0-9]+)?)?)","y")}),CE(t,A.RESERVED_CASE_START,{regex:new RegExp("[Cc][Aa][Ss][Ee]\\b","y"),value:function(E){return E.toUpperCase()}}),CE(t,A.RESERVED_CASE_END,{regex:new RegExp("[Ee][Nn][Dd]\\b","y"),value:function(E){return E.toUpperCase()}}),CE(t,A.RESERVED_COMMAND,{regex:eE(T.reservedCommands,T.identChars),value:function(E){return E.toUpperCase()}}),CE(t,A.RESERVED_BINARY_COMMAND,{regex:eE(T.reservedBinaryCommands,T.identChars),value:function(E){return E.toUpperCase()}}),CE(t,A.RESERVED_DEPENDENT_CLAUSE,{regex:eE(T.reservedDependentClauses,T.identChars),value:function(E){return E.toUpperCase()}}),CE(t,A.RESERVED_JOIN,{regex:eE(T.reservedJoins,T.identChars),value:function(E){return E.toUpperCase()}}),CE(t,A.RESERVED_KEYWORD,{regex:eE(T.reservedKeywords,T.identChars),value:function(E){return E.toUpperCase()}}),CE(t,A.RESERVED_LOGICAL_OPERATOR,{regex:eE(null!==(S=T.reservedLogicalOperators)&&void 0!==S?S:["AND","OR"],T.identChars),value:function(E){return E.toUpperCase()}}),CE(t,A.RESERVED_JOIN_CONDITION,{regex:eE(null!==(n=T.reservedJoinConditions)&&void 0!==n?n:["ON","USING"],T.identChars),value:function(E){return E.toUpperCase()}}),CE(t,A.NAMED_PARAMETER,{regex:RE(null!==(O=T.namedParamTypes)&&void 0!==O?O:[],IE(T.paramChars||T.identChars)),key:function(E){return E.slice(1)}}),CE(t,A.QUOTED_PARAMETER,{regex:RE(null!==(I=T.quotedParamTypes)&&void 0!==I?I:[],SE(T.identTypes)),key:function(E){return t=(T={tokenKey:E.slice(2,-1),quoteChar:E.slice(-1)}).quoteChar,T.tokenKey.replace(new RegExp(j("\\"+t),"gu"),t);var T,t}}),CE(t,A.INDEXED_PARAMETER,{regex:RE(null!==(N=T.numberedParamTypes)&&void 0!==N?N:[],"[0-9]+"),key:function(E){return E.slice(1)}}),CE(t,A.POSITIONAL_PARAMETER,{regex:T.positionalParams?new RegExp("[?]","y"):void 0}),CE(t,A.VARIABLE,{regex:T.variableTypes?(L=T.variableTypes,$(L.map((function(E){return"regex"in E?E.regex:rE(E)})).join("|"))):Q}),CE(t,A.STRING,{regex:nE(T.stringTypes)}),CE(t,A.IDENTIFIER,{regex:OE(T.identChars)}),CE(t,A.DELIMITER,{regex:new RegExp(";","y")}),CE(t,A.OPERATOR,{regex:("+-/*%&|^><=.:$@#?~![]{}",C=["<>","<=",">=","!="].concat((_=null!==(o=T.operators)&&void 0!==o?o:[],function(E){if(Array.isArray(E))return iE(E)}(_)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(_)||LE(_)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())),$("".concat(u(C).map(j).join("|"),"|")+"[".concat("+-/*%&|^><=.:$@#?~![]{}".split("").map(j).join(""),"]")))}),t));this.engine=new oE(a),this.postProcess=T.postProcess}var T,t;return T=E,(t=[{key:"validRules",value:function(E){return Object.fromEntries(Object.entries(E).filter((function(E){var T,t,e=(t=2,function(E){if(Array.isArray(E))return E}(T=E)||function(E,T){var t=null==E?null:"undefined"!=typeof Symbol&&E[Symbol.iterator]||E["@@iterator"];if(null!=t){var e,R,A=[],r=!0,S=!1;try{for(t=t.call(E);!(r=(e=t.next()).done)&&(A.push(e.value),!T||A.length!==T);r=!0);}catch(E){S=!0,R=E}finally{try{r||null==t.return||t.return()}finally{if(S)throw R}}return A}}(T,t)||LE(T,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());return e[0],e[1].regex})))}},{key:"tokenize",value:function(E){var T=this.engine.tokenize(E);return this.postProcess?this.postProcess(T):T}}])&&_E(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),E}();function uE(E){return uE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},uE(E)}function sE(E){return function(E){if(Array.isArray(E))return PE(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return PE(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?PE(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function PE(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function DE(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function cE(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function ME(E,T){return ME=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},ME(E,T)}function fE(E,T){if(T&&("object"===uE(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function UE(E){return UE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},UE(E)}var lE={aead:["KEYS.NEW_KEYSET","KEYS.ADD_KEY_FROM_RAW_BYTES","AEAD.DECRYPT_BYTES","AEAD.DECRYPT_STRING","AEAD.ENCRYPT","KEYS.KEYSET_CHAIN","KEYS.KEYSET_FROM_JSON","KEYS.KEYSET_TO_JSON","KEYS.ROTATE_KEYSET","KEYS.KEYSET_LENGTH"],aggregateAnalytic:["ANY_VALUE","ARRAY_AGG","AVG","CORR","COUNT","COUNTIF","COVAR_POP","COVAR_SAMP","MAX","MIN","ST_CLUSTERDBSCAN","STDDEV_POP","STDDEV_SAMP","STRING_AGG","SUM","VAR_POP","VAR_SAMP"],aggregate:["ANY_VALUE","ARRAY_AGG","ARRAY_CONCAT_AGG","AVG","BIT_AND","BIT_OR","BIT_XOR","COUNT","COUNTIF","LOGICAL_AND","LOGICAL_OR","MAX","MIN","STRING_AGG","SUM"],approximateAggregate:["APPROX_COUNT_DISTINCT","APPROX_QUANTILES","APPROX_TOP_COUNT","APPROX_TOP_SUM"],array:["ARRAY","ARRAY_CONCAT","ARRAY_LENGTH","ARRAY_TO_STRING","GENERATE_ARRAY","GENERATE_DATE_ARRAY","GENERATE_TIMESTAMP_ARRAY","ARRAY_REVERSE","OFFSET","SAFE_OFFSET","ORDINAL","SAFE_ORDINAL"],bitwise:["BIT_COUNT"],conversion:["PARSE_BIGNUMERIC","PARSE_NUMERIC","SAFE_CAST"],date:["CURRENT_DATE","EXTRACT","DATE","DATE_ADD","DATE_SUB","DATE_DIFF","DATE_TRUNC","DATE_FROM_UNIX_DATE","FORMAT_DATE","LAST_DAY","PARSE_DATE","UNIX_DATE"],datetime:["CURRENT_DATETIME","DATETIME","EXTRACT","DATETIME_ADD","DATETIME_SUB","DATETIME_DIFF","DATETIME_TRUNC","FORMAT_DATETIME","LAST_DAY","PARSE_DATETIME"],debugging:["ERROR"],federatedQuery:["EXTERNAL_QUERY"],geography:["S2_CELLIDFROMPOINT","S2_COVERINGCELLIDS","ST_ANGLE","ST_AREA","ST_ASBINARY","ST_ASGEOJSON","ST_ASTEXT","ST_AZIMUTH","ST_BOUNDARY","ST_BOUNDINGBOX","ST_BUFFER","ST_BUFFERWITHTOLERANCE","ST_CENTROID","ST_CENTROID_AGG","ST_CLOSESTPOINT","ST_CLUSTERDBSCAN","ST_CONTAINS","ST_CONVEXHULL","ST_COVEREDBY","ST_COVERS","ST_DIFFERENCE","ST_DIMENSION","ST_DISJOINT","ST_DISTANCE","ST_DUMP","ST_DWITHIN","ST_ENDPOINT","ST_EQUALS","ST_EXTENT","ST_EXTERIORRING","ST_GEOGFROM","ST_GEOGFROMGEOJSON","ST_GEOGFROMTEXT","ST_GEOGFROMWKB","ST_GEOGPOINT","ST_GEOGPOINTFROMGEOHASH","ST_GEOHASH","ST_GEOMETRYTYPE","ST_INTERIORRINGS","ST_INTERSECTION","ST_INTERSECTS","ST_INTERSECTSBOX","ST_ISCOLLECTION","ST_ISEMPTY","ST_LENGTH","ST_MAKELINE","ST_MAKEPOLYGON","ST_MAKEPOLYGONORIENTED","ST_MAXDISTANCE","ST_NPOINTS","ST_NUMGEOMETRIES","ST_NUMPOINTS","ST_PERIMETER","ST_POINTN","ST_SIMPLIFY","ST_SNAPTOGRID","ST_STARTPOINT","ST_TOUCHES","ST_UNION","ST_UNION_AGG","ST_WITHIN","ST_X","ST_Y"],hash:["FARM_FINGERPRINT","MD5","SHA1","SHA256","SHA512"],hll:["HLL_COUNT.INIT","HLL_COUNT.MERGE","HLL_COUNT.MERGE_PARTIAL","HLL_COUNT.EXTRACT"],interval:["MAKE_INTERVAL","EXTRACT","JUSTIFY_DAYS","JUSTIFY_HOURS","JUSTIFY_INTERVAL"],json:["JSON_EXTRACT","JSON_QUERY","JSON_EXTRACT_SCALAR","JSON_VALUE","JSON_EXTRACT_ARRAY","JSON_QUERY_ARRAY","JSON_EXTRACT_STRING_ARRAY","JSON_VALUE_ARRAY","TO_JSON_STRING"],math:["ABS","SIGN","IS_INF","IS_NAN","IEEE_DIVIDE","RAND","SQRT","POW","POWER","EXP","LN","LOG","LOG10","GREATEST","LEAST","DIV","SAFE_DIVIDE","SAFE_MULTIPLY","SAFE_NEGATE","SAFE_ADD","SAFE_SUBTRACT","MOD","ROUND","TRUNC","CEIL","CEILING","FLOOR","COS","COSH","ACOS","ACOSH","SIN","SINH","ASIN","ASINH","TAN","TANH","ATAN","ATANH","ATAN2","RANGE_BUCKET"],navigation:["FIRST_VALUE","LAST_VALUE","NTH_VALUE","LEAD","LAG","PERCENTILE_CONT","PERCENTILE_DISC"],net:["NET.IP_FROM_STRING","NET.SAFE_IP_FROM_STRING","NET.IP_TO_STRING","NET.IP_NET_MASK","NET.IP_TRUNC","NET.IPV4_FROM_INT64","NET.IPV4_TO_INT64","NET.HOST","NET.PUBLIC_SUFFIX","NET.REG_DOMAIN"],numbering:["RANK","DENSE_RANK","PERCENT_RANK","CUME_DIST","NTILE","ROW_NUMBER"],security:["SESSION_USER"],statisticalAggregate:["CORR","COVAR_POP","COVAR_SAMP","STDDEV_POP","STDDEV_SAMP","STDDEV","VAR_POP","VAR_SAMP","VARIANCE"],string:["ASCII","BYTE_LENGTH","CHAR_LENGTH","CHARACTER_LENGTH","CHR","CODE_POINTS_TO_BYTES","CODE_POINTS_TO_STRING","CONCAT","CONTAINS_SUBSTR","ENDS_WITH","FORMAT","FROM_BASE32","FROM_BASE64","FROM_HEX","INITCAP","INSTR","LEFT","LENGTH","LPAD","LOWER","LTRIM","NORMALIZE","NORMALIZE_AND_CASEFOLD","OCTET_LENGTH","REGEXP_CONTAINS","REGEXP_EXTRACT","REGEXP_EXTRACT_ALL","REGEXP_INSTR","REGEXP_REPLACE","REGEXP_SUBSTR","REPLACE","REPEAT","REVERSE","RIGHT","RPAD","RTRIM","SAFE_CONVERT_BYTES_TO_STRING","SOUNDEX","SPLIT","STARTS_WITH","STRPOS","SUBSTR","SUBSTRING","TO_BASE32","TO_BASE64","TO_CODE_POINTS","TO_HEX","TRANSLATE","TRIM","UNICODE","UPPER"],time:["CURRENT_TIME","TIME","EXTRACT","TIME_ADD","TIME_SUB","TIME_DIFF","TIME_TRUNC","FORMAT_TIME","PARSE_TIME"],timestamp:["CURRENT_TIMESTAMP","EXTRACT","STRING","TIMESTAMP","TIMESTAMP_ADD","TIMESTAMP_SUB","TIMESTAMP_DIFF","TIMESTAMP_TRUNC","FORMAT_TIMESTAMP","PARSE_TIMESTAMP","TIMESTAMP_SECONDS","TIMESTAMP_MILLIS","TIMESTAMP_MICROS","UNIX_SECONDS","UNIX_MILLIS","UNIX_MICROS"],uuid:["GENERATE_UUID"],conditional:["COALESCE","IF","IFNULL","NULLIF"],legacyAggregate:["AVG","BIT_AND","BIT_OR","BIT_XOR","CORR","COUNT","COVAR_POP","COVAR_SAMP","EXACT_COUNT_DISTINCT","FIRST","GROUP_CONCAT","GROUP_CONCAT_UNQUOTED","LAST","MAX","MIN","NEST","NTH","QUANTILES","STDDEV","STDDEV_POP","STDDEV_SAMP","SUM","TOP","UNIQUE","VARIANCE","VAR_POP","VAR_SAMP"],legacyBitwise:["BIT_COUNT"],legacyCasting:["BOOLEAN","BYTES","CAST","FLOAT","HEX_STRING","INTEGER","STRING"],legacyComparison:["COALESCE","GREATEST","IFNULL","IS_INF","IS_NAN","IS_EXPLICITLY_DEFINED","LEAST","NVL"],legacyDatetime:["CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","DATE","DATE_ADD","DATEDIFF","DAY","DAYOFWEEK","DAYOFYEAR","FORMAT_UTC_USEC","HOUR","MINUTE","MONTH","MSEC_TO_TIMESTAMP","NOW","PARSE_UTC_USEC","QUARTER","SEC_TO_TIMESTAMP","SECOND","STRFTIME_UTC_USEC","TIME","TIMESTAMP","TIMESTAMP_TO_MSEC","TIMESTAMP_TO_SEC","TIMESTAMP_TO_USEC","USEC_TO_TIMESTAMP","UTC_USEC_TO_DAY","UTC_USEC_TO_HOUR","UTC_USEC_TO_MONTH","UTC_USEC_TO_WEEK","UTC_USEC_TO_YEAR","WEEK","YEAR"],legacyIp:["FORMAT_IP","PARSE_IP","FORMAT_PACKED_IP","PARSE_PACKED_IP"],legacyJson:["JSON_EXTRACT","JSON_EXTRACT_SCALAR"],legacyMath:["ABS","ACOS","ACOSH","ASIN","ASINH","ATAN","ATANH","ATAN2","CEIL","COS","COSH","DEGREES","EXP","FLOOR","LN","LOG","LOG2","LOG10","PI","POW","RADIANS","RAND","ROUND","SIN","SINH","SQRT","TAN","TANH"],legacyRegex:["REGEXP_MATCH","REGEXP_EXTRACT","REGEXP_REPLACE"],legacyString:["CONCAT","INSTR","LEFT","LENGTH","LOWER","LPAD","LTRIM","REPLACE","RIGHT","RPAD","RTRIM","SPLIT","SUBSTR","UPPER"],legacyTableWildcard:["TABLE_DATE_RANGE","TABLE_DATE_RANGE_STRICT","TABLE_QUERY"],legacyUrl:["HOST","DOMAIN","TLD"],legacyWindow:["AVG","COUNT","MAX","MIN","STDDEV","SUM","CUME_DIST","DENSE_RANK","FIRST_VALUE","LAG","LAST_VALUE","LEAD","NTH_VALUE","NTILE","PERCENT_RANK","PERCENTILE_CONT","PERCENTILE_DISC","RANK","RATIO_TO_REPORT","ROW_NUMBER"],legacyMisc:["CURRENT_USER","EVERY","FROM_BASE64","HASH","FARM_FINGERPRINT","IF","POSITION","SHA1","SOME","TO_BASE64"],other:["BQ.JOBS.CANCEL","BQ.REFRESH_MATERIALIZED_VIEW"],pivot:["PIVOT","UNPIVOT"]},pE={keywords:["ALL","ANY","AS","ASC","ASSERT_ROWS_MODIFIED","AT","BETWEEN","BY","CAST","COLLATE","CONTAINS","CUBE","CURRENT","DEFAULT","DEFINE","DESC","DISTINCT","ENUM","ESCAPE","EXISTS","EXTRACT","FALSE","FOLLOWING","FOR","FULL","GROUPING","GROUPS","HASH","IF","IGNORE","IN","INTO","IS","LIKE","LOOKUP","NEW","NO","NOT","NULL","NULLS","OF","OVER","PARTITION","PRECEDING","PROTO","RANGE","RECURSIVE","RESPECT","ROLLUP","ROWS","SOME","TABLE","TABLESAMPLE SYSTEM","THEN","TO","TREAT","TRUE","UNBOUNDED","UNNEST","WITHIN"],datatypes:["ARRAY","BOOL","BYTES","DATE","DATETIME","GEOGRAPHY","INTERVAL","INT64","INT","SMALLINT","INTEGER","BIGINT","TINYINT","BYTEINT","NUMERIC","DECIMAL","BIGNUMERIC","BIGDECIMAL","FLOAT64","STRING","STRUCT","TIME","TIMEZONE"],stringFormat:["HEX","BASEX","BASE64M","ASCII","UTF-8","UTF8"],misc:["SAFE"]},GE=["SELECT","FROM","WHERE","GROUP BY","HAVING","ORDER BY","QUALIFY","WINDOW","PARTITION BY","LIMIT","OFFSET","WITH","OMIT RECORD IF","INSERT","INSERT INTO","VALUES","DELETE","DELETE FROM","TRUNCATE TABLE","UPDATE","MERGE","MERGE INTO","SET SCHEMA","CREATE SCHEMA","CREATE TABLE","CREATE TABLE IF NOT EXISTS","CREATE TEMP TABLE","CREATE TEMP TABLE IF NOT EXISTS","CREATE TEMPORARY TABLE","CREATE TEMPORARY TABLE IF NOT EXISTS","CREATE OR REPLACE TABLE","CREATE OR REPLACE TEMP TABLE","CREATE OR REPLACE TEMPORARY TABLE","CREATE TABLE LIKE","CREATE TABLE COPY","CREATE SNAPSHOT TABLE","CREATE TABLE CLONE","CREATE VIEW","CREATE VIEW IF NOT EXISTS","CREATE OR REPLACE VIEW","CREATE MATERIALIZED VIEW","CREATE EXTERNAL TABLE","CREATE FUNCTION","CREATE TABLE FUNCTION","CREATE PROCEDURE","CREATE ROW ACCESS POLICY","ALTER SCHEMA SET OPTIONS","ALTER TABLE SET OPTIONS","ALTER TABLE ADD COLUMN","ALTER TABLE RENAME TO","ALTER TABLE DROP COLUMN","ALTER COLUMN SET OPTIONS","ALTER COLUMN DROP NOT NULL","ALTER COLUMN SET DATA TYPE","ALTER VIEW SET OPTIONS","ALTER MATERIALIZED VIEW SET OPTIONS","DROP SCHEMA","DROP TABLE","DROP SNAPSHOT TABLE","DROP EXTERNAL TABLE","DROP VIEW","DROP MATERIALIZED VIEW","DROP FUNCTION","DROP TABLE FUNCTION","DROP PROCEDURE","DROP ROW ACCESS POLICY","GRANT","REVOKE","CREATE CAPACITY","CREATE RESERVATION","CREATE ASSIGNMENT","DROP CAPACITY","DROP RESERVATION","DROP ASSIGNMENT","DECLARE","SET","EXECUTE IMMEDIATE","LOOP","END LOOP","REPEAT","END REPEAT","WHILE","END WHILE","BREAK","LEAVE","CONTINUE","ITERATE","FOR","END FOR","BEGIN","BEGIN TRANSACTION","COMMIT TRANSACTION","ROLLBACK TRANSACTION","RAISE","RETURN","CALL","ASSERT","EXPORT DATA"],yE=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT"],hE=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN"],BE=["WHEN","ELSE"],dE=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&ME(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=UE(e);if(R){var t=UE(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return fE(this,E)});function r(){return DE(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:GE,reservedBinaryCommands:yE,reservedJoins:hE,reservedDependentClauses:BE,reservedKeywords:_([].concat(sE(Object.values(lE).flat()),sE(Object.values(pE).flat()))),openParens:["(","["],closeParens:[")","]"],stringTypes:[{quote:'""".."""',prefixes:["R","B","RB","BR"]},{quote:"'''..'''",prefixes:["R","B","RB","BR"]},{quote:'""',prefixes:["R","B","RB","BR"]},{quote:"''",prefixes:["R","B","RB","BR"]}],identTypes:["``"],identChars:{dashes:!0},positionalParams:!0,namedParamTypes:["@"],quotedParamTypes:["@"],lineCommentTypes:["--","#"],operators:r.operators,postProcess:vE})}}])&&cE(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function vE(E){return function(E){var T=n;return E.map((function(E){return"OFFSET"===E.value&&"["===T.value?(T=E,Object.assign(Object.assign({},E),{type:A.RESERVED_KEYWORD})):(T=E,E)}))}(function(E){for(var T=[],t=0;t<E.length;t++){var e=E[t],R=E[t+1]||n;if((I.ARRAY(e)||I.STRUCT(e))&&"<"===R.value){var r=HE(E,t+1),S=E.slice(t,r+1);T.push({type:A.IDENTIFIER,value:S.map(FE("value")).join(""),text:S.map(FE("text")).join("")}),t=r}else T.push(e)}return T}(E))}dE.operators=["~",">>","<<","||"];var FE=function(E){return function(T){return T.type===A.IDENTIFIER||T.type===A.COMMA?T[E]+" ":T[E]}};function HE(E,T){for(var t=0,e=T;e<E.length;e++){var R=E[e];if("<"===R.value?t++:">"===R.value?t--:">>"===R.value&&(t-=2),0===t)return e}return E.length-1}function YE(E){return YE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},YE(E)}function VE(E){return function(E){if(Array.isArray(E))return bE(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return bE(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?bE(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function bE(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function mE(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function gE(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function XE(E,T){return XE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},XE(E,T)}function WE(E,T){if(T&&("object"===YE(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function KE(E){return KE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},KE(E)}var wE={aggregate:["ARRAY_AGG","AVG","CORR","CORRELATION","COUNT","COUNT_BIG","COVAR_POP","COVARIANCE","COVAR","COVAR_SAMP","COVARIANCE_SAMP","CUME_DIST","GROUPING","LISTAGG","MAX","MEDIAN","MIN","PERCENTILE_CONT","PERCENTILE_DISC","PERCENT_RANK","REGR_AVGX","REGR_AVGY","REGR_COUNT","REGR_INTERCEPT","REGR_ICPT","REGR_R2","REGR_SLOPE","REGR_SXX","REGR_SXY","REGR_SYY","STDDEV_POP","STDDEV","STDDEV_SAMP","SUM","VAR_POP","VARIANCE","VAR","VAR_SAMP","VARIANCE_SAMP","XMLAGG"],scalar:["ABS","ABSVAL","ACOS","ADD_DAYS","ADD_MONTHS","ARRAY_DELETE","ARRAY_FIRST","ARRAY_LAST","ARRAY_NEXT","ARRAY_PRIOR","ARRAY_TRIM","ASCII","ASCII_CHR","ASCII_STR","ASCIISTR","ASIN","ATAN","ATANH","ATAN2","BIGINT","BINARY","BITAND","BITANDNOT","BITOR","BITXOR","BITNOT","BLOB","BTRIM","CARDINALITY","CCSID_ENCODING","CEILING","CEIL","CHAR","CHAR9","CHARACTER_LENGTH","CHAR_LENGTH","CHR","CLOB","COALESCE","COLLATION_KEY","COMPARE_DECFLOAT","CONCAT","CONTAINS","COS","COSH","DATE","DAY","DAYOFMONTH","DAYOFWEEK","DAYOFWEEK_ISO","DAYOFYEAR","DAYS","DAYS_BETWEEN","DBCLOB","DECFLOAT","DECFLOAT_FORMAT","DECFLOAT_SORTKEY","DECIMAL","DEC","DECODE","DECRYPT_BINARY","DECRYPT_BIT","DECRYPT_CHAR","DECRYPT_DB","DECRYPT_DATAKEY_BIGINT","DECRYPT_DATAKEY_BIT","DECRYPT_DATAKEY_CLOB","DECRYPT_DATAKEY_DBCLOB","DECRYPT_DATAKEY_DECIMAL","DECRYPT_DATAKEY_INTEGER","DECRYPT_DATAKEY_VARCHAR","DECRYPT_DATAKEY_VARGRAPHIC","DEGREES","DIFFERENCE","DIGITS","DOUBLE_PRECISION","DOUBLE","DSN_XMLVALIDATE","EBCDIC_CHR","EBCDIC_STR","ENCRYPT_DATAKEY","ENCRYPT_TDES","EXP","EXTRACT","FLOAT","FLOOR","GENERATE_UNIQUE","GENERATE_UNIQUE_BINARY","GETHINT","GETVARIABLE","GRAPHIC","GREATEST","HASH","HASH_CRC32","HASH_MD5","HASH_SHA1","HASH_SHA256","HEX","HOUR","IDENTITY_VAL_LOCAL","IFNULL","INSERT","INSTR","INTEGER","INT","JULIAN_DAY","LAST_DAY","LCASE","LEAST","LEFT","LENGTH","LN","LOCATE","LOCATE_IN_STRING","LOG10","LOWER","LPAD","LTRIM","MAX","MAX_CARDINALITY","MICROSECOND","MIDNIGHT_SECONDS","MIN","MINUTE","MOD","MONTH","MONTHS_BETWEEN","MQREAD","MQREADCLOB","MQRECEIVE","MQRECEIVECLOB","MQSEND","MULTIPLY_ALT","NEXT_DAY","NEXT_MONTH","NORMALIZE_DECFLOAT","NORMALIZE_STRING","NULLIF","NVL","OVERLAY","PACK","POSITION","POSSTR","POWER","POW","QUANTIZE","QUARTER","RADIANS","RAISE_ERROR","RANDOM","RAND","REAL","REGEXP_COUNT","REGEXP_INSTR","REGEXP_LIKE","REGEXP_REPLACE","REGEXP_SUBSTR","REPEAT","REPLACE","RID","RIGHT","ROUND","ROUND_TIMESTAMP","ROWID","RPAD","RTRIM","SCORE","SECOND","SIGN","SIN","SINH","SMALLINT","SOUNDEX","SOAPHTTPC","SOAPHTTPV","SOAPHTTPNC","SOAPHTTPNV","SPACE","SQRT","STRIP","STRLEFT","STRPOS","STRRIGHT","SUBSTR","SUBSTRING","TAN","TANH","TIME","TIMESTAMP","TIMESTAMPADD","TIMESTAMPDIFF","TIMESTAMP_FORMAT","TIMESTAMP_ISO","TIMESTAMP_TZ","TO_CHAR","TO_CLOB","TO_DATE","TO_NUMBER","TOTALORDER","TO_TIMESTAMP","TRANSLATE","TRIM","TRIM_ARRAY","TRUNCATE","TRUNC","TRUNC_TIMESTAMP","UCASE","UNICODE","UNICODE_STR","UNISTR","UPPER","VALUE","VARBINARY","VARCHAR","VARCHAR9","VARCHAR_BIT_FORMAT","VARCHAR_FORMAT","VARGRAPHIC","VERIFY_GROUP_FOR_USER","VERIFY_ROLE_FOR_USER","VERIFY_TRUSTED_CONTEXT_ROLE_FOR_USER","WEEK","WEEK_ISO","WRAP","XMLATTRIBUTES","XMLCOMMENT","XMLCONCAT","XMLDOCUMENT","XMLELEMENT","XMLFOREST","XMLMODIFY","XMLNAMESPACES","XMLPARSE","XMLPI","XMLQUERY","XMLSERIALIZE","XMLTEXT","XMLXSROBJECTID","XSLTRANSFORM","YEAR"],table:["ADMIN_TASK_LIST","ADMIN_TASK_OUTPUT","ADMIN_TASK_STATUS","BLOCKING_THREADS","MQREADALL","MQREADALLCLOB","MQRECEIVEALL","MQRECEIVEALLCLOB","XMLTABLE"],row:["UNPACK"],olap:["FIRST_VALUE","LAG","LAST_VALUE","LEAD","NTH_VALUE","NTILE","RATIO_TO_REPORT"]},xE={standard:["ALL","ALLOCATE","ALLOW","ALTERAND","ANY","AS","ARRAY","ARRAY_EXISTS","ASENSITIVE","ASSOCIATE","ASUTIME","AT","AUDIT","AUX","AUXILIARY","BEFORE","BEGIN","BETWEEN","BUFFERPOOL","BY","CAPTURE","CASCADED","CAST","CCSID","CHARACTER","CHECK","CLONE","CLUSTER","COLLECTION","COLLID","COLUMN","CONDITION","CONNECTION","CONSTRAINT","CONTENT","CONTINUE","CREATE","CUBE","CURRENT","CURRENT_DATE","CURRENT_LC_CTYPE","CURRENT_PATH","CURRENT_SCHEMA","CURRENT_TIME","CURRENT_TIMESTAMP","CURRVAL","CURSOR","DATA","DATABASE","DBINFO","DECLARE","DEFAULT","DESCRIPTOR","DETERMINISTIC","DISABLE","DISALLOW","DISTINCT","DO","DOCUMENT","DSSIZE","DYNAMIC","EDITPROC","ENCODING","ENCRYPTION","ENDING","END-EXEC","ERASE","ESCAPE","EXCEPTION","EXISTS","EXIT","EXTERNAL","FENCED","FIELDPROC","FINAL","FIRST","FOR","FREE","FULL","FUNCTION","GENERATED","GET","GLOBAL","GOTO","GROUP","HANDLER","HOLD","HOURS","IF","IMMEDIATE","IN","INCLUSIVE","INDEX","INHERIT","INNER","INOUT","INSENSITIVE","INTO","IS","ISOBID","ITERATE","JAR","KEEP","KEY","LANGUAGE","LAST","LC_CTYPE","LEAVE","LIKE","LOCAL","LOCALE","LOCATOR","LOCATORS","LOCK","LOCKMAX","LOCKSIZE","LONG","LOOP","MAINTAINED","MATERIALIZED","MICROSECONDS","MINUTEMINUTES","MODIFIES","MONTHS","NEXT","NEXTVAL","NO","NONE","NOT","NULL","NULLS","NUMPARTS","OBID","OF","OLD","ON DELETE","ON UPDATE","OPTIMIZATION","OPTIMIZE","ORDER","ORGANIZATION","OUT","OUTER","PACKAGE","PARAMETER","PART","PADDED","PARTITION","PARTITIONED","PARTITIONING","PATH","PIECESIZE","PERIOD","PLAN","PRECISION","PREVVAL","PRIOR","PRIQTY","PRIVILEGES","PROCEDURE","PROGRAM","PSID","PUBLIC","QUERY","QUERYNO","READS","REFERENCES","RESIGNAL","RESTRICT","RESULT","RESULT_SET_LOCATOR","RETURN","RETURNS","ROLE","ROLLUP","ROUND_CEILING","ROUND_DOWN","ROUND_FLOOR","ROUND_HALF_DOWN","ROUND_HALF_EVEN","ROUND_HALF_UP","ROUND_UP","ROW","ROWSET","SCHEMA","SCRATCHPAD","SECONDS","SECQTY","SECURITY","SEQUENCE","SENSITIVE","SESSION_USER","SIMPLE","SOME","SOURCE","SPECIFIC","STANDARD","STATIC","STATEMENT","STAY","STOGROUP","STORES","STYLE","SUMMARY","SYNONYM","SYSDATE","SYSTEM","SYSTIMESTAMP","TABLE","TABLESPACE","THEN","TO","TRIGGER","TYPE","UNDO","UNIQUE","UNTIL","USER","VALIDPROC","VARIABLE","VARIANT","VCAT","VERSIONING","VIEW","VOLATILE","VOLUMES","WHILE","WLM","XMLEXISTS","XMLCAST","YEARS","ZONE"],onlineUtilies:["BACKUP SYSTEM","CATENFM","CATMAINT","CHECK DATA","CHECK INDEX","CHECK LOB","COPY","COPYTOCOPY","DIAGNOSE","EXEC SQL","LISTDEF","LOAD","MERGECOPY","MODIFY RECOVERY","MODIFY STATISTICS","OPTIONS","QUIESCE","REBUILD INDEX","RECOVER","REORG INDEX","REORG TABLESPACE","REPAIR","REPORT","RESTORE SYSTEM","RUNSTATS","STOSPACE","TEMPLATE","UNLOAD"],commands:["ABEND","ACCESS DATABASE","ALTER BUFFERPOOL","ALTER GROUPBUFFERPOOL","ALTER UTILITY","ARCHIVE LOG","BIND PACKAGE","BIND PLAN","BIND QUERY","BIND SERVICE","BIND","REBIND","CANCEL THREAD","DCLGEN","DISPLAY ACCEL","DISPLAY ARCHIVE","DISPLAY BLOCKERS","DISPLAY BUFFERPOOL","DISPLAY DATABASE","DISPLAY DDF","DISPLAY FUNCTION SPECIFIC","DISPLAY GROUP","DISPLAY GROUPBUFFERPOOL","DISPLAY LOCATION","DISPLAY LOG","DISPLAY PROCEDURE","DISPLAY PROFILE","DISPLAY RLIMIT","DISPLAY RESTSVC","DISPLAY THREAD","DISPLAY TRACE","DISPLAY UTILITY","DSN","DSNH","FREE PACKAGE","FREE PLAN","FREE QUERY","FREE SERVICE","MODIFY admtproc,APPL=SHUTDOWN","MODIFY admtproc,APPL=TRACE","MODIFY DDF","MODIFY irlmproc,ABEND","MODIFY irlmproc,DIAG","MODIFY irlmproc,PURGE","MODIFY irlmproc,SET","MODIFY irlmproc,STATUS","MODIFY TRACE","REBIND PACKAGE","REBIND PLAN","REBIND TRIGGER PACKAGE","RECOVER BSDS","RECOVER INDOUBT","RECOVER POSTPONED","REFRESH DB2,EARLY","RESET GENERICLU","RESET INDOUBT","RUN","SET ARCHIVE","SET LOG","SET SYSPARM","SPUFI","START ACCEL","START admtproc","START CDDS","START DATABASE","START DB2","START DDF","START FUNCTION SPECIFIC","START irlmproc","START PROCEDURE","START PROFILE","START RLIMIT","START RESTSVC","START TRACE","STOP ACCEL","STOP admtproc","STOP CDDS","STOP DATABASE","STOP DB2","STOP DDF","STOP FUNCTION SPECIFIC","STOP irlmproc","STOP PROCEDURE","STOP PROFILE","STOP RLIMIT","STOP RESTSVC","STOP TRACE","TERM UTILITY","TRACE CT"]},JE=["ALLOCATE CURSOR","ALTER DATABASE","ALTER FUNCTION","ALTER INDEX","ALTER MASK","ALTER PERMISSION","ALTER PROCEDURE","ALTER SEQUENCE","ALTER STOGROUP","ALTER TABLE","ALTER TABLESPACE","ALTER TRIGGER","ALTER TRUSTED CONTEXT","ALTER VIEW","ASSOCIATE LOCATORS","BEGIN DECLARE SECTION","CALL","CLOSE","COMMENT","COMMIT","CONNECT","CREATE ALIAS","CREATE AUXILIARY TABLE","CREATE DATABASE","CREATE FUNCTION","CREATE GLOBAL TEMPORARY TABLE","CREATE INDEX","CREATE LOB TABLESPACE","CREATE MASK","CREATE PERMISSION","CREATE PROCEDURE","CREATE ROLE","CREATE SEQUENCE","CREATE STOGROUP","CREATE SYNONYM","CREATE TABLE","CREATE TABLESPACE","CREATE TRIGGER","CREATE TRUSTED CONTEXT","CREATE TYPE","CREATE VARIABLE","CREATE VIEW","DECLARE CURSOR","DECLARE GLOBAL TEMPORARY TABLE","DECLARE STATEMENT","DECLARE TABLE","DECLARE VARIABLE","DELETE","DELETE FROM","DESCRIBE CURSOR","DESCRIBE INPUT","DESCRIBE OUTPUT","DESCRIBE PROCEDURE","DESCRIBE TABLE","DROP","END DECLARE SECTION","EXCHANGE","EXECUTE","EXECUTE IMMEDIATE","EXPLAIN","FETCH","FREE LOCATOR","GET DIAGNOSTICS","GRANT","HOLD LOCATOR","INCLUDE","INSERT","LABEL","LOCK TABLE","MERGE","OPEN","PREPARE","REFRESH","RELEASE","RELEASE SAVEPOINT","RENAME","REVOKE","ROLLBACK","SAVEPOINT","SELECT","SELECT INTO","SET CONNECTION","SET","SET CURRENT ACCELERATOR","SET CURRENT APPLICATION COMPATIBILITY","SET CURRENT APPLICATION ENCODING SCHEME","SET CURRENT DEBUG MODE","SET CURRENT DECFLOAT ROUNDING MODE","SET CURRENT DEGREE","SET CURRENT EXPLAIN MODE","SET CURRENT GET_ACCEL_ARCHIVE","SET CURRENT LOCALE LC_CTYPE","SET CURRENT MAINTAINED TABLE TYPES FOR OPTIMIZATION","SET CURRENT OPTIMIZATION HINT","SET CURRENT PACKAGE PATH","SET CURRENT PACKAGESET","SET CURRENT PRECISION","SET CURRENT QUERY ACCELERATION","SET CURRENT QUERY ACCELERATION WAITFORDATA","SET CURRENT REFRESH AGE","SET CURRENT ROUTINE VERSION","SET CURRENT RULES","SET CURRENT SQLID","SET CURRENT TEMPORAL BUSINESS_TIME","SET CURRENT TEMPORAL SYSTEM_TIME","SET ENCRYPTION PASSWORD","SET PATH","SET SCHEMA","SET SESSION TIME ZONE","SIGNAL","TRUNCATE","UPDATE","VALUES","VALUES INTO","WHENEVER","ADD","ALTER COLUMN","AFTER","DROP TABLE","FETCH FIRST","FROM","GROUP BY","GO","HAVING","INSERT INTO","LIMIT","OFFSET","ORDER BY","SELECT","SET CURRENT SCHEMA","WHERE","WITH"],kE=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT"],jE=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],QE=["WHEN","ELSE","ELSEIF"],ZE=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&XE(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=KE(e);if(R){var t=KE(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return WE(this,E)});function r(){return mE(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:JE,reservedBinaryCommands:kE,reservedJoins:jE,reservedDependentClauses:QE,reservedKeywords:_([].concat(VE(Object.values(wE).flat()),VE(Object.values(xE).flat()))),stringTypes:[{quote:"''",prefixes:["X","G","N","GX","UX","U&"]}],identTypes:['""'],positionalParams:!0,namedParamTypes:[":"],paramChars:{first:"@#$",rest:"@#$"},operators:r.operators})}}])&&gE(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function $E(E){return $E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},$E(E)}function qE(E){return function(E){if(Array.isArray(E))return zE(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return zE(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?zE(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zE(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function ET(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function TT(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function tT(E,T){return tT=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},tT(E,T)}function eT(E,T){if(T&&("object"===$E(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function RT(E){return RT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},RT(E)}ZE.operators=["**","¬=","¬>","¬<","!>","!<","||"];var AT={math:["ABS","ACOS","ASIN","ATAN","BIN","BROUND","CBRT","CEIL","CEILING","CONV","COS","DEGREES","EXP","FACTORIAL","FLOOR","GREATEST","HEX","LEAST","LN","LOG","LOG10","LOG2","NEGATIVE","PI","PMOD","POSITIVE","POW","POWER","RADIANS","RAND","ROUND","SHIFTLEFT","SHIFTRIGHT","SHIFTRIGHTUNSIGNED","SIGN","SIN","SQRT","TAN","UNHEX","WIDTH_BUCKET"],array:["ARRAY_CONTAINS","MAP_KEYS","MAP_VALUES","SIZE","SORT_ARRAY"],conversion:["BINARY","CAST"],date:["ADD_MONTHS","DATE","DATE_ADD","DATE_FORMAT","DATE_SUB","DATEDIFF","DAY","DAYNAME","DAYOFMONTH","DAYOFYEAR","EXTRACT","FROM_UNIXTIME","FROM_UTC_TIMESTAMP","HOUR","LAST_DAY","MINUTE","MONTH","MONTHS_BETWEEN","NEXT_DAY","QUARTER","SECOND","TIMESTAMP","TO_DATE","TO_UTC_TIMESTAMP","TRUNC","UNIX_TIMESTAMP","WEEKOFYEAR","YEAR"],conditional:["ASSERT_TRUE","COALESCE","IF","ISNOTNULL","ISNULL","NULLIF","NVL"],string:["ASCII","BASE64","CHARACTER_LENGTH","CHR","CONCAT","CONCAT_WS","CONTEXT_NGRAMS","DECODE","ELT","ENCODE","FIELD","FIND_IN_SET","FORMAT_NUMBER","GET_JSON_OBJECT","IN_FILE","INITCAP","INSTR","LCASE","LENGTH","LEVENSHTEIN","LOCATE","LOWER","LPAD","LTRIM","NGRAMS","OCTET_LENGTH","PARSE_URL","PRINTF","QUOTE","REGEXP_EXTRACT","REGEXP_REPLACE","REPEAT","REVERSE","RPAD","RTRIM","SENTENCES","SOUNDEX","SPACE","SPLIT","STR_TO_MAP","SUBSTR","SUBSTRING","TRANSLATE","TRIM","UCASE","UNBASE64","UPPER"],masking:["MASK","MASK_FIRST_N","MASK_HASH","MASK_LAST_N","MASK_SHOW_FIRST_N","MASK_SHOW_LAST_N"],misc:["AES_DECRYPT","AES_ENCRYPT","CRC32","CURRENT_DATABASE","CURRENT_USER","HASH","JAVA_METHOD","LOGGED_IN_USER","MD5","REFLECT","SHA","SHA1","SHA2","SURROGATE_KEY","VERSION"],aggregate:["AVG","COLLECT_LIST","COLLECT_SET","CORR","COUNT","COVAR_POP","COVAR_SAMP","HISTOGRAM_NUMERIC","MAX","MIN","NTILE","PERCENTILE","PERCENTILE_APPROX","REGR_AVGX","REGR_AVGY","REGR_COUNT","REGR_INTERCEPT","REGR_R2","REGR_SLOPE","REGR_SXX","REGR_SXY","REGR_SYY","STDDEV_POP","STDDEV_SAMP","SUM","VAR_POP","VAR_SAMP","VARIANCE"],table:["EXPLODE","INLINE","JSON_TUPLE","PARSE_URL_TUPLE","POSEXPLODE","STACK"]},rT={nonReserved:["ADD","ADMIN","AFTER","ANALYZE","ARCHIVE","ASC","BEFORE","BUCKET","BUCKETS","CASCADE","CHANGE","CLUSTER","CLUSTERED","CLUSTERSTATUS","COLLECTION","COLUMNS","COMMENT","COMPACT","COMPACTIONS","COMPUTE","CONCATENATE","CONTINUE","DATA","DATABASES","DATETIME","DAY","DBPROPERTIES","DEFERRED","DEFINED","DELIMITED","DEPENDENCY","DESC","DIRECTORIES","DIRECTORY","DISABLE","DISTRIBUTE","ELEM_TYPE","ENABLE","ESCAPED","EXCLUSIVE","EXPLAIN","EXPORT","FIELDS","FILE","FILEFORMAT","FIRST","FORMAT","FORMATTED","FUNCTIONS","HOLD_DDLTIME","HOUR","IDXPROPERTIES","IGNORE","INDEX","INDEXES","INPATH","INPUTDRIVER","INPUTFORMAT","ITEMS","JAR","KEYS","KEY_TYPE","LIMIT","LINES","LOAD","LOCATION","LOCK","LOCKS","LOGICAL","LONG","MAPJOIN","MATERIALIZED","METADATA","MINUS","MINUTE","MONTH","MSCK","NOSCAN","NO_DROP","OFFLINE","OPTION","OUTPUTDRIVER","OUTPUTFORMAT","OVERWRITE","OWNER","PARTITIONED","PARTITIONS","PLUS","PRETTY","PRINCIPALS","PROTECTION","PURGE","READ","READONLY","REBUILD","RECORDREADER","RECORDWRITER","RELOAD","RENAME","REPAIR","REPLACE","REPLICATION","RESTRICT","REWRITE","ROLE","ROLES","SCHEMA","SCHEMAS","SECOND","SEMI","SERDE","SERDEPROPERTIES","SERVER","SETS","SHARED","SHOW","SHOW_DATABASE","SKEWED","SORT","SORTED","SSL","STATISTICS","STORED","STREAMTABLE","STRING","STRUCT","TABLES","TBLPROPERTIES","TEMPORARY","TERMINATED","TINYINT","TOUCH","TRANSACTIONS","UNARCHIVE","UNDO","UNIONTYPE","UNLOCK","UNSET","UNSIGNED","URI","UTC","UTCTIMESTAMP","VALUE_TYPE","VIEW","WHILE","YEAR","AUTOCOMMIT","ISOLATION","LEVEL","OFFSET","SNAPSHOT","TRANSACTION","WORK","WRITE","ABORT","KEY","LAST","NORELY","NOVALIDATE","NULLS","RELY","VALIDATE","DETAIL","DOW","EXPRESSION","OPERATOR","QUARTER","SUMMARY","VECTORIZATION","WEEK","YEARS","MONTHS","WEEKS","DAYS","HOURS","MINUTES","SECONDS","TIMESTAMPTZ","ZONE"],reserved:["ALL","ARRAY","AS","AUTHORIZATION","BETWEEN","BIGINT","BINARY","BOOLEAN","BOTH","BY","CAST","CHAR","COLUMN","CONF","CROSS","CUBE","CURRENT","CURRENT_DATE","CURRENT_TIMESTAMP","CURSOR","DATABASE","DATE","DECIMAL","DELETE","DISTINCT","DOUBLE","EXCHANGE","EXISTS","EXTENDED","EXTERNAL","FALSE","FLOAT","FOLLOWING","FOR","FULL","FUNCTION","GRANT","GROUPING","IF","IMPORT","IN","INNER","INT","INTERVAL","INTO","IS","LATERAL","LEFT","LESS","LIKE","LOCAL","MACRO","MAP","MORE","NONE","NOT","NULL","OF","ORDER","OUT","OUTER","OVER","PARTIALSCAN","PARTITION","PERCENT","PRECEDING","PRESERVE","PROCEDURE","RANGE","READS","REDUCE","REVOKE","RIGHT","ROLLUP","ROW","ROWS","SET","SMALLINT","TABLE","TABLESAMPLE","THEN","TIMESTAMP","TO","TRANSFORM","TRIGGER","TRUE","UNBOUNDED","UNIQUEJOIN","USER","UTC_TMESTAMP","VARCHAR","WINDOW","COMMIT","ONLY","REGEXP","RLIKE","ROLLBACK","START","CACHE","CONSTRAINT","FOREIGN","PRIMARY","REFERENCES","DAYOFWEEK","EXTRACT","FLOOR","INTEGER","PRECISION","VIEWS","TIME","NUMERIC","SYNC"],fileTypes:["TEXTFILE","SEQUENCEFILE","ORC","CSV","TSV","PARQUET","AVRO","RCFILE","JSONFILE","INPUTFORMAT","OUTPUTFORMAT"]},ST=["ALTER","ALTER COLUMN","ALTER TABLE","CREATE","CREATE TABLE","USE","DESCRIBE","DROP","DROP TABLE","FETCH","FROM","GROUP BY","HAVING","INSERT","INSERT INTO","LIMIT","OFFSET","ORDER BY","SELECT","SET","SET SCHEMA","SHOW","SORT BY","TRUNCATE","UPDATE","VALUES","WHERE","WITH","WINDOW","PARTITION BY","STORED AS","STORED BY","ROW FORMAT"],nT=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT"],OT=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN"],IT=["WHEN","ELSE"],NT=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&tT(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=RT(e);if(R){var t=RT(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return eT(this,E)});function r(){return ET(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:ST,reservedBinaryCommands:nT,reservedJoins:OT,reservedDependentClauses:IT,reservedKeywords:_([].concat(qE(Object.values(AT).flat()),qE(Object.values(rT).flat()))),openParens:["(","["],closeParens:[")","]"],stringTypes:['""',"''"],identTypes:["``"],variableTypes:[{quote:"{}",prefixes:["$"],requirePrefix:!0}],operators:r.operators})}}])&&TT(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function oT(E){return oT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},oT(E)}function CT(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function LT(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function iT(E,T){return iT=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},iT(E,T)}function _T(E,T){if(T&&("object"===oT(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function aT(E){return aT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},aT(E)}NT.operators=["<=>","==","||"];var uT=["ADDDATE","ADD_MONTHS","BIT_AND","BIT_OR","BIT_XOR","CAST","COUNT","CUME_DIST","CURDATE","CURTIME","DATE_ADD","DATE_SUB","DATE_FORMAT","DECODE","DENSE_RANK","EXTRACT","FIRST_VALUE","GROUP_CONCAT","JSON_ARRAYAGG","JSON_OBJECTAGG","LAG","LEAD","MAX","MEDIAN","MID","MIN","NOW","NTH_VALUE","NTILE","POSITION","PERCENT_RANK","PERCENTILE_CONT","PERCENTILE_DISC","RANK","ROW_NUMBER","SESSION_USER","STD","STDDEV","STDDEV_POP","STDDEV_SAMP","SUBDATE","SUBSTR","SUBSTRING","SUM","SYSTEM_USER","TRIM","TRIM_ORACLE","VARIANCE","VAR_POP","VAR_SAMP","ABS","ACOS","ADDTIME","AES_DECRYPT","AES_ENCRYPT","ASIN","ATAN","ATAN2","BENCHMARK","BIN","BINLOG_GTID_POS","BIT_COUNT","BIT_LENGTH","CEIL","CEILING","CHARACTER_LENGTH","CHAR_LENGTH","CHR","COERCIBILITY","COLUMN_CHECK","COLUMN_EXISTS","COLUMN_LIST","COLUMN_JSON","COMPRESS","CONCAT","CONCAT_OPERATOR_ORACLE","CONCAT_WS","CONNECTION_ID","CONV","CONVERT_TZ","COS","COT","CRC32","DATEDIFF","DAYNAME","DAYOFMONTH","DAYOFWEEK","DAYOFYEAR","DEGREES","DECODE_HISTOGRAM","DECODE_ORACLE","DES_DECRYPT","DES_ENCRYPT","ELT","ENCODE","ENCRYPT","EXP","EXPORT_SET","EXTRACTVALUE","FIELD","FIND_IN_SET","FLOOR","FORMAT","FOUND_ROWS","FROM_BASE64","FROM_DAYS","FROM_UNIXTIME","GET_LOCK","GREATEST","HEX","IFNULL","INSTR","ISNULL","IS_FREE_LOCK","IS_USED_LOCK","JSON_ARRAY","JSON_ARRAY_APPEND","JSON_ARRAY_INSERT","JSON_COMPACT","JSON_CONTAINS","JSON_CONTAINS_PATH","JSON_DEPTH","JSON_DETAILED","JSON_EXISTS","JSON_EXTRACT","JSON_INSERT","JSON_KEYS","JSON_LENGTH","JSON_LOOSE","JSON_MERGE","JSON_MERGE_PATCH","JSON_MERGE_PRESERVE","JSON_QUERY","JSON_QUOTE","JSON_OBJECT","JSON_REMOVE","JSON_REPLACE","JSON_SET","JSON_SEARCH","JSON_TYPE","JSON_UNQUOTE","JSON_VALID","JSON_VALUE","LAST_DAY","LAST_INSERT_ID","LCASE","LEAST","LENGTH","LENGTHB","LN","LOAD_FILE","LOCATE","LOG","LOG10","LOG2","LOWER","LPAD","LPAD_ORACLE","LTRIM","LTRIM_ORACLE","MAKEDATE","MAKETIME","MAKE_SET","MASTER_GTID_WAIT","MASTER_POS_WAIT","MD5","MONTHNAME","NAME_CONST","NVL","NVL2","NULLIF","OCT","OCTET_LENGTH","ORD","PERIOD_ADD","PERIOD_DIFF","PI","POW","POWER","QUOTE","REGEXP_INSTR","REGEXP_REPLACE","REGEXP_SUBSTR","RADIANS","RAND","RELEASE_ALL_LOCKS","RELEASE_LOCK","REPLACE_ORACLE","REVERSE","ROUND","RPAD","RPAD_ORACLE","RTRIM","RTRIM_ORACLE","SEC_TO_TIME","SHA","SHA1","SHA2","SIGN","SIN","SLEEP","SOUNDEX","SPACE","SQRT","STRCMP","STR_TO_DATE","SUBSTR_ORACLE","SUBSTRING_INDEX","SUBTIME","SYS_GUID","TAN","TIMEDIFF","TIME_FORMAT","TIME_TO_SEC","TO_BASE64","TO_CHAR","TO_DAYS","TO_SECONDS","UCASE","UNCOMPRESS","UNCOMPRESSED_LENGTH","UNHEX","UNIX_TIMESTAMP","UPDATEXML","UPPER","UUID","UUID_SHORT","VERSION","WEEKDAY","WEEKOFYEAR","WSREP_LAST_WRITTEN_GTID","WSREP_LAST_SEEN_GTID","WSREP_SYNC_WAIT_UPTO_GTID","YEARWEEK"],sT=["ACCESSIBLE","ACCOUNT","ACTION","ADMIN","AFTER","AGAINST","AGGREGATE","ALL","ALGORITHM","ALTER","ALWAYS","ANY","AS","ASC","ASCII","ASENSITIVE","AT","ATOMIC","AUTHORS","AUTO_INCREMENT","AUTOEXTEND_SIZE","AUTO","AVG","AVG_ROW_LENGTH","BACKUP","BEFORE","BETWEEN","BIGINT","BINARY","BIT","BLOB","BLOCK","BODY","BOOL","BOOLEAN","BOTH","BTREE","BY","BYTE","CACHE","CASCADE","CASCADED","CATALOG_NAME","CHAIN","CHANGE","CHANGED","CHAR","CHARACTER","CHARACTER SET","CHARSET","CHECK","CHECKPOINT","CHECKSUM","CIPHER","CLASS_ORIGIN","CLIENT","CLOB","CLOSE","COALESCE","CODE","COLLATE","COLLATION","COLUMN","COLUMN_NAME","COLUMNS","COLUMN_ADD","COLUMN_CREATE","COLUMN_DELETE","COLUMN_GET","COMMENT","COMMITTED","COMPACT","COMPLETION","COMPRESSED","CONCURRENT","CONDITION","CONNECTION","CONSISTENT","CONSTRAINT","CONSTRAINT_CATALOG","CONSTRAINT_NAME","CONSTRAINT_SCHEMA","CONTAINS","CONTEXT","CONTINUE","CONTRIBUTORS","CONVERT","CPU","CREATE","CROSS","CUBE","CURRENT","CURRENT_DATE","CURRENT_POS","CURRENT_ROLE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","CURSOR_NAME","CYCLE","DATA","DATABASE","DATABASES","DATAFILE","DATE","DATETIME","DAY","DAY_HOUR","DAY_MICROSECOND","DAY_MINUTE","DAY_SECOND","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DEFINER","DELAYED","DELAY_KEY_WRITE","DELETE_DOMAIN_ID","DES_KEY_FILE","DESC","DETERMINISTIC","DIAGNOSTICS","DIRECTORY","DISABLE","DISCARD","DISK","DISTINCT","DISTINCTROW","DIV","DOUBLE","DO_DOMAIN_IDS","DROP","DUAL","DUMPFILE","DUPLICATE","DYNAMIC","EACH","EMPTY","ENABLE","ENCLOSED","ENDS","ENGINE","ENGINES","ENUM","ERROR","ERRORS","ESCAPE","ESCAPED","EVENT","EVENTS","EVERY","EXAMINED","EXCHANGE","EXCLUDE","EXCEPTION","EXISTS","EXIT","EXPANSION","EXPIRE","EXPORT","EXTENDED","EXTENT_SIZE","FALSE","FAST","FAULTS","FEDERATED","FETCH","FIELDS","FILE","FIRST","FIXED","FLOAT","FLOAT4","FLOAT8","FOLLOWING","FOLLOWS","FOR","FORCE","FOREIGN","FOUND","FULL","FULLTEXT","FUNCTION","GENERAL","GENERATED","GET_FORMAT","GET","GLOBAL","GOTO","GRANTS","GROUP","HARD","HASH","HIGH_PRIORITY","HISTORY","HOST","HOSTS","HOUR","HOUR_MICROSECOND","HOUR_MINUTE","HOUR_SECOND","IDENTIFIED","IF","IGNORE","IGNORED","IGNORE_DOMAIN_IDS","IGNORE_SERVER_IDS","IMMEDIATE","IMPORT","IN","INCREMENT","INDEX","INDEXES","INFILE","INITIAL_SIZE","INNER","INOUT","INSENSITIVE","INSERT_METHOD","INSTALL","INT","INT1","INT2","INT3","INT4","INT8","INTEGER","INTERVAL","INVISIBLE","INTO","IO","IO_THREAD","IPC","IS","ISOLATION","ISOPEN","ISSUER","ITERATE","INVOKER","JSON","JSON_TABLE","KEY","KEYS","KEY_BLOCK_SIZE","LANGUAGE","LAST","LAST_VALUE","LASTVAL","LEADING","LEAVE","LEAVES","LEFT","LESS","LEVEL","LIKE","LINEAR","LINES","LIST","LOAD","LOCAL","LOCALTIME","LOCALTIMESTAMP","LOCK","LOCKED","LOCKS","LOGFILE","LOGS","LONG","LONGBLOB","LONGTEXT","LOOP","LOW_PRIORITY","MASTER","MASTER_CONNECT_RETRY","MASTER_DELAY","MASTER_GTID_POS","MASTER_HOST","MASTER_LOG_FILE","MASTER_LOG_POS","MASTER_PASSWORD","MASTER_PORT","MASTER_SERVER_ID","MASTER_SSL","MASTER_SSL_CA","MASTER_SSL_CAPATH","MASTER_SSL_CERT","MASTER_SSL_CIPHER","MASTER_SSL_CRL","MASTER_SSL_CRLPATH","MASTER_SSL_KEY","MASTER_SSL_VERIFY_SERVER_CERT","MASTER_USER","MASTER_USE_GTID","MASTER_HEARTBEAT_PERIOD","MATCH","MAX_CONNECTIONS_PER_HOUR","MAX_QUERIES_PER_HOUR","MAX_ROWS","MAX_SIZE","MAX_STATEMENT_TIME","MAX_UPDATES_PER_HOUR","MAX_USER_CONNECTIONS","MAXVALUE","MEDIUM","MEDIUMBLOB","MEDIUMINT","MEDIUMTEXT","MEMORY","MERGE","MESSAGE_TEXT","MICROSECOND","MIDDLEINT","MIGRATE","MINUS","MINUTE","MINUTE_MICROSECOND","MINUTE_SECOND","MINVALUE","MIN_ROWS","MOD","MODE","MODIFIES","MODIFY","MONITOR","MONTH","MUTEX","MYSQL","MYSQL_ERRNO","NAME","NAMES","NATIONAL","NATURAL","NCHAR","NESTED","NEVER","NEW","NEXT","NEXTVAL","NO","NOMAXVALUE","NOMINVALUE","NOCACHE","NOCYCLE","NO_WAIT","NOWAIT","NODEGROUP","NONE","NOT","NOTFOUND","NO_WRITE_TO_BINLOG","NULL","NUMBER","NUMERIC","NVARCHAR","OF","OFFSET","OLD_PASSWORD","ON DELETE","ON UPDATE","ONE","ONLINE","ONLY","OPEN","OPTIMIZE","OPTIONS","OPTION","OPTIONALLY","ORDER","ORDINALITY","OTHERS","OUT","OUTER","OUTFILE","OVER","OVERLAPS","OWNER","PACKAGE","PACK_KEYS","PAGE","PAGE_CHECKSUM","PARSER","PARSE_VCOL_EXPR","PATH","PERIOD","PARTIAL","PARTITION","PARTITIONING","PARTITIONS","PASSWORD","PERSISTENT","PHASE","PLUGIN","PLUGINS","PORT","PORTION","PRECEDES","PRECEDING","PRECISION","PRESERVE","PREV","PREVIOUS","PRIMARY","PRIVILEGES","PROCEDURE","PROCESS","PROCESSLIST","PROFILE","PROFILES","PROXY","PURGE","QUARTER","QUERY","QUICK","RAISE","RANGE","RAW","READ","READ_ONLY","READ_WRITE","READS","REAL","REBUILD","RECOVER","RECURSIVE","REDO_BUFFER_SIZE","REDOFILE","REDUNDANT","REFERENCES","REGEXP","RELAY","RELAYLOG","RELAY_LOG_FILE","RELAY_LOG_POS","RELAY_THREAD","RELEASE","RELOAD","REMOVE","RENAME","REORGANIZE","REPAIR","REPEATABLE","REPLAY","REPLICA","REPLICAS","REPLICA_POS","REPLICATION","REPEAT","REQUIRE","RESET","RESTART","RESTORE","RESTRICT","RESUME","RETURNED_SQLSTATE","RETURN","RETURNS","REUSE","RIGHT","RLIKE","ROLE","ROLLUP","ROUTINE","ROW","ROWCOUNT","ROWNUM","ROWS","ROWTYPE","ROW_COUNT","ROW_FORMAT","RTREE","SCHEDULE","SCHEMA","SCHEMA_NAME","SCHEMAS","SECOND","SECOND_MICROSECOND","SECURITY","SENSITIVE","SEPARATOR","SEQUENCE","SERIAL","SERIALIZABLE","SESSION","SERVER","SETVAL","SHARE","SIGNED","SIMPLE","SKIP","SLAVE","SLAVES","SLAVE_POS","SLOW","SNAPSHOT","SMALLINT","SOCKET","SOFT","SOME","SONAME","SOUNDS","SOURCE","STAGE","STORED","SPATIAL","SPECIFIC","REF_SYSTEM_ID","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQL_BIG_RESULT","SQL_BUFFER_RESULT","SQL_CACHE","SQL_CALC_FOUND_ROWS","SQL_NO_CACHE","SQL_SMALL_RESULT","SQL_THREAD","SQL_TSI_SECOND","SQL_TSI_MINUTE","SQL_TSI_HOUR","SQL_TSI_DAY","SQL_TSI_WEEK","SQL_TSI_MONTH","SQL_TSI_QUARTER","SQL_TSI_YEAR","SSL","START","STARTING","STARTS","STATEMENT","STATS_AUTO_RECALC","STATS_PERSISTENT","STATS_SAMPLE_PAGES","STATUS","STOP","STORAGE","STRING","SUBCLASS_ORIGIN","SUBJECT","SUBPARTITION","SUBPARTITIONS","SUPER","SUSPEND","SWAPS","SWITCHES","SYSDATE","SYSTEM","SYSTEM_TIME","TABLE","TABLE_NAME","TABLES","TABLESPACE","TABLE_CHECKSUM","TEMPORARY","TEMPTABLE","TERMINATED","TEXT","THAN","THEN","TIES","TIME","TIMESTAMP","TIMESTAMPADD","TIMESTAMPDIFF","TINYBLOB","TINYINT","TINYTEXT","TO","TRAILING","TRANSACTION","TRANSACTIONAL","THREADS","TRIGGER","TRIGGERS","TRUE","TYPE","TYPES","UNBOUNDED","UNCOMMITTED","UNDEFINED","UNDO_BUFFER_SIZE","UNDOFILE","UNDO","UNICODE","UNIQUE","UNKNOWN","UNLOCK","UNINSTALL","UNSIGNED","UNTIL","UPGRADE","USAGE","USER","USER_RESOURCES","USE_FRM","UTC_DATE","UTC_TIME","UTC_TIMESTAMP","VALUE","VARBINARY","VARCHAR","VARCHARACTER","VARCHAR2","VARIABLES","VARYING","VIA","VIEW","VIRTUAL","VISIBLE","VERSIONING","WAIT","WARNINGS","WEEK","WEIGHT_STRING","WHILE","WINDOW","WITHIN","WITHOUT","WORK","WRAPPER","WRITE","X509","XA","XML","YEAR","YEAR_MONTH","ZEROFILL"],PT=["ALTER DATABASE","ALTER DATABASE COMMENT","ALTER EVENT","ALTER FUNCTION","ALTER PROCEDURE","ALTER SCHEMA","ALTER SCHEMA COMMENT","ALTER SEQUENCE","ALTER SERVER","ALTER TABLE","ALTER USER","ALTER VIEW","ANALYZE","ANALYZE TABLE","BACKUP LOCK","BACKUP STAGE","BACKUP UNLOCK","BEGIN","BINLOG","CACHE INDEX","CALL","CHANGE MASTER TO","CHECK TABLE","CHECK VIEW","CHECKSUM TABLE","COMMIT","CREATE AGGREGATE FUNCTION","CREATE DATABASE","CREATE EVENT","CREATE FUNCTION","CREATE INDEX","CREATE PROCEDURE","CREATE ROLE","CREATE SEQUENCE","CREATE SERVER","CREATE SPATIAL INDEX","CREATE TABLE","CREATE TRIGGER","CREATE UNIQUE INDEX","CREATE USER","CREATE VIEW","DEALLOCATE PREPARE","DELETE","DELETE FROM","DESCRIBE","DO","DROP DATABASE","DROP EVENT","DROP FUNCTION","DROP INDEX","DROP PREPARE","DROP PROCEDURE","DROP ROLE","DROP SEQUENCE","DROP SERVER","DROP TABLE","DROP TRIGGER","DROP USER","DROP VIEW","EXECUTE","EXPLAIN","FLUSH","GET DIAGNOSTICS","GET DIAGNOSTICS CONDITION","GRANT","HANDLER","HELP","INSERT","INSTALL PLUGIN","INSTALL SONAME","KILL","LOAD DATA INFILE","LOAD INDEX INTO CACHE","LOAD XML INFILE","LOCK TABLE","OPTIMIZE TABLE","PREPARE","PURGE BINARY LOGS","PURGE MASTER LOGS","RELEASE SAVEPOINT","RENAME TABLE","RENAME USER","REPAIR TABLE","REPAIR VIEW","REPLACE","RESET MASTER","RESET QUERY CACHE","RESET REPLICA","RESET SLAVE","RESIGNAL","RETURNING","REVOKE","ROLLBACK","SAVEPOINT","SELECT","SET","SET CHARACTER SET","SET DEFAULT ROLE","SET GLOBAL TRANSACTION","SET NAMES","SET PASSWORD","SET ROLE","SET STATEMENT","SET TRANSACTION","SHOW","SHOW ALL REPLICAS STATUS","SHOW ALL SLAVES STATUS","SHOW AUTHORS","SHOW BINARY LOGS","SHOW BINLOG EVENTS","SHOW BINLOG STATUS","SHOW CHARACTER SET","SHOW CLIENT_STATISTICS","SHOW COLLATION","SHOW COLUMNS","SHOW CONTRIBUTORS","SHOW CREATE DATABASE","SHOW CREATE EVENT","SHOW CREATE FUNCTION","SHOW CREATE PACKAGE","SHOW CREATE PACKAGE BODY","SHOW CREATE PROCEDURE","SHOW CREATE SEQUENCE","SHOW CREATE TABLE","SHOW CREATE TRIGGER","SHOW CREATE USER","SHOW CREATE VIEW","SHOW DATABASES","SHOW ENGINE","SHOW ENGINE INNODB STATUS","SHOW ENGINES","SHOW ERRORS","SHOW EVENTS","SHOW EXPLAIN","SHOW FUNCTION CODE","SHOW FUNCTION STATUS","SHOW GRANTS","SHOW INDEX","SHOW INDEXES","SHOW INDEX_STATISTICS","SHOW KEYS","SHOW LOCALES","SHOW MASTER LOGS","SHOW MASTER STATUS","SHOW OPEN TABLES","SHOW PACKAGE BODY CODE","SHOW PACKAGE BODY STATUS","SHOW PACKAGE STATUS","SHOW PLUGINS","SHOW PLUGINS SONAME","SHOW PRIVILEGES","SHOW PROCEDURE CODE","SHOW PROCEDURE STATUS","SHOW PROCESSLIST","SHOW PROFILE","SHOW PROFILES","SHOW QUERY_RESPONSE_TIME","SHOW RELAYLOG EVENTS","SHOW REPLICA","SHOW REPLICA HOSTS","SHOW REPLICA STATUS","SHOW SCHEMAS","SHOW SLAVE","SHOW SLAVE HOSTS","SHOW SLAVE STATUS","SHOW STATUS","SHOW STORAGE ENGINES","SHOW TABLE STATUS","SHOW TABLES","SHOW TRIGGERS","SHOW USER_STATISTICS","SHOW VARIABLES","SHOW WARNINGS","SHOW WSREP_MEMBERSHIP","SHOW WSREP_STATUS","SHUTDOWN","SIGNAL","START ALL REPLICAS","START ALL SLAVES","START REPLICA","START SLAVE","START TRANSACTION","STOP ALL REPLICAS","STOP ALL SLAVES","STOP REPLICA","STOP SLAVE","TRUNCATE","TRUNCATE TABLE","UNINSTALL PLUGIN","UNINSTALL SONAME","UNLOCK TABLE","UPDATE","USE","WITH","XA BEGIN","XA COMMIT","XA END","XA PREPARE","XA RECOVER","XA ROLLBACK","XA START","ADD","ALTER COLUMN","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","OFFSET","ORDER BY","SELECT","VALUES","WHERE"],DT=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT","MINUS","MINUS ALL","MINUS DISTINCT"],cT=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","CROSS JOIN","NATURAL JOIN","STRAIGHT_JOIN","NATURAL LEFT JOIN","NATURAL LEFT OUTER JOIN","NATURAL RIGHT JOIN","NATURAL RIGHT OUTER JOIN"],MT=["WHEN","ELSE","ELSEIF","ELSIF"],fT=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&iT(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=aT(e);if(R){var t=aT(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return _T(this,E)});function r(){return CT(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:PT,reservedBinaryCommands:DT,reservedJoins:cT,reservedDependentClauses:MT,reservedLogicalOperators:["AND","OR","XOR"],reservedKeywords:_([].concat(sT,uT)),stringTypes:['""',{quote:"''",prefixes:["X"]}],identTypes:["``"],identChars:{first:"$",rest:"$"},variableTypes:[{regex:"@[A-Za-z0-9_.$]+"},{quote:'""',prefixes:["@"],requirePrefix:!0},{quote:"''",prefixes:["@"],requirePrefix:!0},{quote:"``",prefixes:["@"],requirePrefix:!0}],positionalParams:!0,lineCommentTypes:["--","#"],operators:r.operators,postProcess:UT})}}])&&LT(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function UT(E){return E.map((function(T,t){var e=E[t+1]||n;return I.SET(T)&&"("===e.value?Object.assign(Object.assign({},T),{type:A.RESERVED_KEYWORD}):T}))}function lT(E){return lT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},lT(E)}function pT(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function GT(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function yT(E,T){return yT=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},yT(E,T)}function hT(E,T){if(T&&("object"===lT(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function BT(E){return BT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},BT(E)}fT.operators=[":=","<<",">>","<=>","&&","||"];var dT=["ABS","ACOS","ADDDATE","ADDTIME","AES_DECRYPT","AES_ENCRYPT","ANY_VALUE","ASCII","ASIN","ATAN","ATAN2","AVG","BENCHMARK","BIN","BIN_TO_UUID","BINARY","BIT_AND","BIT_COUNT","BIT_LENGTH","BIT_OR","BIT_XOR","CAN_ACCESS_COLUMN","CAN_ACCESS_DATABASE","CAN_ACCESS_TABLE","CAN_ACCESS_USER","CAN_ACCESS_VIEW","CAST","CEIL","CEILING","CHAR","CHAR_LENGTH","CHARACTER_LENGTH","CHARSET","COALESCE","COERCIBILITY","COLLATION","COMPRESS","CONCAT","CONCAT_WS","CONNECTION_ID","CONV","CONVERT","CONVERT_TZ","COS","COT","COUNT","CRC32","CUME_DIST","CURDATE","CURRENT_DATE","CURRENT_ROLE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURTIME","DATABASE","DATE","DATE_ADD","DATE_FORMAT","DATE_SUB","DATEDIFF","DAY","DAYNAME","DAYOFMONTH","DAYOFWEEK","DAYOFYEAR","DEFAULT","DEGREES","DENSE_RANK","DIV","ELT","EXP","EXPORT_SET","EXTRACT","EXTRACTVALUE","FIELD","FIND_IN_SET","FIRST_VALUE","FLOOR","FORMAT","FORMAT_BYTES","FORMAT_PICO_TIME","FOUND_ROWS","FROM_BASE64","FROM_DAYS","FROM_UNIXTIME","GEOMCOLLECTION","GEOMETRYCOLLECTION","GET_DD_COLUMN_PRIVILEGES","GET_DD_CREATE_OPTIONS","GET_DD_INDEX_SUB_PART_LENGTH","GET_FORMAT","GET_LOCK","GREATEST","GROUP_CONCAT","GROUPING","GTID_SUBSET","GTID_SUBTRACT","HEX","HOUR","ICU_VERSION","IF","IFNULL","IN","INET_ATON","INET_NTOA","INET6_ATON","INET6_NTOA","INSERT","INSTR","INTERNAL_AUTO_INCREMENT","INTERNAL_AVG_ROW_LENGTH","INTERNAL_CHECK_TIME","INTERNAL_CHECKSUM","INTERNAL_DATA_FREE","INTERNAL_DATA_LENGTH","INTERNAL_DD_CHAR_LENGTH","INTERNAL_GET_COMMENT_OR_ERROR","INTERNAL_GET_ENABLED_ROLE_JSON","INTERNAL_GET_HOSTNAME","INTERNAL_GET_USERNAME","INTERNAL_GET_VIEW_WARNING_OR_ERROR","INTERNAL_INDEX_COLUMN_CARDINALITY","INTERNAL_INDEX_LENGTH","INTERNAL_IS_ENABLED_ROLE","INTERNAL_IS_MANDATORY_ROLE","INTERNAL_KEYS_DISABLED","INTERNAL_MAX_DATA_LENGTH","INTERNAL_TABLE_ROWS","INTERNAL_UPDATE_TIME","INTERVAL","IS","IS_FREE_LOCK","IS_IPV4","IS_IPV4_COMPAT","IS_IPV4_MAPPED","IS_IPV6","IS NOT","IS NOT NULL","IS NULL","IS_USED_LOCK","IS_UUID","ISNULL","JSON_ARRAY","JSON_ARRAY_APPEND","JSON_ARRAY_INSERT","JSON_ARRAYAGG","JSON_CONTAINS","JSON_CONTAINS_PATH","JSON_DEPTH","JSON_EXTRACT","JSON_INSERT","JSON_KEYS","JSON_LENGTH","JSON_MERGE","JSON_MERGE_PATCH","JSON_MERGE_PRESERVE","JSON_OBJECT","JSON_OBJECTAGG","JSON_OVERLAPS","JSON_PRETTY","JSON_QUOTE","JSON_REMOVE","JSON_REPLACE","JSON_SCHEMA_VALID","JSON_SCHEMA_VALIDATION_REPORT","JSON_SEARCH","JSON_SET","JSON_STORAGE_FREE","JSON_STORAGE_SIZE","JSON_TABLE","JSON_TYPE","JSON_UNQUOTE","JSON_VALID","JSON_VALUE","LAG","LAST_DAY","LAST_INSERT_ID","LAST_VALUE","LCASE","LEAD","LEAST","LEFT","LENGTH","LIKE","LINESTRING","LN","LOAD_FILE","LOCALTIME","LOCALTIMESTAMP","LOCATE","LOG","LOG10","LOG2","LOWER","LPAD","LTRIM","MAKE_SET","MAKEDATE","MAKETIME","MASTER_POS_WAIT","MATCH","MAX","MBRCONTAINS","MBRCOVEREDBY","MBRCOVERS","MBRDISJOINT","MBREQUALS","MBRINTERSECTS","MBROVERLAPS","MBRTOUCHES","MBRWITHIN","MD5","MEMBER OF","MICROSECOND","MID","MIN","MINUTE","MOD","MONTH","MONTHNAME","MULTILINESTRING","MULTIPOINT","MULTIPOLYGON","NAME_CONST","NOT","NOT IN","NOT LIKE","NOT REGEXP","NOW","NTH_VALUE","NTILE","NULLIF","OCT","OCTET_LENGTH","ORD","PERCENT_RANK","PERIOD_ADD","PERIOD_DIFF","PI","POINT","POLYGON","POSITION","POW","POWER","PS_CURRENT_THREAD_ID","PS_THREAD_ID","QUARTER","QUOTE","RADIANS","RAND","RANDOM_BYTES","RANK","REGEXP","REGEXP_INSTR","REGEXP_LIKE","REGEXP_REPLACE","REGEXP_SUBSTR","RELEASE_ALL_LOCKS","RELEASE_LOCK","REPEAT","REPLACE","REVERSE","RIGHT","RLIKE","ROLES_GRAPHML","ROUND","ROW_COUNT","ROW_NUMBER","RPAD","RTRIM","SCHEMA","SEC_TO_TIME","SECOND","SESSION_USER","SHA1","SHA2","SIGN","SIN","SLEEP","SOUNDEX","SOUNDS LIKE","SOURCE_POS_WAIT","SPACE","SQRT","ST_AREA","ST_ASBINARY","ST_ASGEOJSON","ST_ASTEXT","ST_BUFFER","ST_BUFFER_STRATEGY","ST_CENTROID","ST_COLLECT","ST_CONTAINS","ST_CONVEXHULL","ST_CROSSES","ST_DIFFERENCE","ST_DIMENSION","ST_DISJOINT","ST_DISTANCE","ST_DISTANCE_SPHERE","ST_ENDPOINT","ST_ENVELOPE","ST_EQUALS","ST_EXTERIORRING","ST_FRECHETDISTANCE","ST_GEOHASH","ST_GEOMCOLLFROMTEXT","ST_GEOMCOLLFROMWKB","ST_GEOMETRYN","ST_GEOMETRYTYPE","ST_GEOMFROMGEOJSON","ST_GEOMFROMTEXT","ST_GEOMFROMWKB","ST_HAUSDORFFDISTANCE","ST_INTERIORRINGN","ST_INTERSECTION","ST_INTERSECTS","ST_ISCLOSED","ST_ISEMPTY","ST_ISSIMPLE","ST_ISVALID","ST_LATFROMGEOHASH","ST_LATITUDE","ST_LENGTH","ST_LINEFROMTEXT","ST_LINEFROMWKB","ST_LINEINTERPOLATEPOINT","ST_LINEINTERPOLATEPOINTS","ST_LONGFROMGEOHASH","ST_LONGITUDE","ST_MAKEENVELOPE","ST_MLINEFROMTEXT","ST_MLINEFROMWKB","ST_MPOINTFROMTEXT","ST_MPOINTFROMWKB","ST_MPOLYFROMTEXT","ST_MPOLYFROMWKB","ST_NUMGEOMETRIES","ST_NUMINTERIORRING","ST_NUMPOINTS","ST_OVERLAPS","ST_POINTATDISTANCE","ST_POINTFROMGEOHASH","ST_POINTFROMTEXT","ST_POINTFROMWKB","ST_POINTN","ST_POLYFROMTEXT","ST_POLYFROMWKB","ST_SIMPLIFY","ST_SRID","ST_STARTPOINT","ST_SWAPXY","ST_SYMDIFFERENCE","ST_TOUCHES","ST_TRANSFORM","ST_UNION","ST_VALIDATE","ST_WITHIN","ST_X","ST_Y","STATEMENT_DIGEST","STATEMENT_DIGEST_TEXT","STD","STDDEV","STDDEV_POP","STDDEV_SAMP","STR_TO_DATE","STRCMP","SUBDATE","SUBSTR","SUBSTRING","SUBSTRING_INDEX","SUBTIME","SUM","SYSDATE","SYSTEM_USER","TAN","TIME","TIME_FORMAT","TIME_TO_SEC","TIMEDIFF","TIMESTAMP","TIMESTAMPADD","TIMESTAMPDIFF","TO_BASE64","TO_DAYS","TO_SECONDS","TRIM","TRUNCATE","UCASE","UNCOMPRESS","UNCOMPRESSED_LENGTH","UNHEX","UNIX_TIMESTAMP","UPDATEXML","UPPER","USER","UTC_DATE","UTC_TIME","UTC_TIMESTAMP","UUID","UUID_SHORT","UUID_TO_BIN","VALIDATE_PASSWORD_STRENGTH","VALUES","VAR_POP","VAR_SAMP","VARIANCE","VERSION","WAIT_FOR_EXECUTED_GTID_SET","WAIT_UNTIL_SQL_THREAD_AFTER_GTIDS","WEEK","WEEKDAY","WEEKOFYEAR","WEIGHT_STRING","YEAR","YEARWEEK"],vT=["ACCESSIBLE","ACCOUNT","ACTION","ACTIVE","ADMIN","AFTER","AGAINST","AGGREGATE","ALGORITHM","ALL","ALTER","ALWAYS","ANALYSE","ANALYZE","ANY","ARRAY","AS","ASC","ASENSITIVE","AT","ATTRIBUTE","AUTHENTICATION","AUTOEXTEND_SIZE","AUTO_INCREMENT","AVG_ROW_LENGTH","BACKUP","BEFORE","BEGIN","BETWEEN","BIGINT","BIT","BLOB","BLOCK","BOOL","BOOLEAN","BOTH","BTREE","BUCKETS","BY","BYTE","CACHE","CASCADE","CASCADED","CATALOG_NAME","CHAIN","CHALLENGE_RESPONSE","CHANGE","CHANGED","CHANNEL","CHARACTER","CHARACTER SET","CHECK","CHECKSUM","CIPHER","CLASS_ORIGIN","CLIENT","CLOSE","CODE","COLLATE","COLUMN","COLUMNS","COLUMN_FORMAT","COLUMN_NAME","COMMENT","COMMITTED","COMPACT","COMPLETION","COMPONENT","COMPRESSED","COMPRESSION","CONCURRENT","CONDITION","CONNECTION","CONSISTENT","CONSTRAINT","CONSTRAINT_CATALOG","CONSTRAINT_NAME","CONSTRAINT_SCHEMA","CONTAINS","CONTEXT","CONTINUE","CPU","CREATE","CROSS","CUBE","CURRENT","CURSOR","CURSOR_NAME","DATA","DATABASES","DATAFILE","DATETIME","DAY_HOUR","DAY_MICROSECOND","DAY_MINUTE","DAY_SECOND","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT_AUTH","DEFINER","DEFINITION","DELAYED","DELAY_KEY_WRITE","DESC","DESCRIPTION","DES_KEY_FILE","DETERMINISTIC","DIAGNOSTICS","DIRECTORY","DISABLE","DISCARD","DISK","DISTINCT","DISTINCTROW","DOUBLE","DROP","DUAL","DUMPFILE","DUPLICATE","DYNAMIC","EACH","EMPTY","ENABLE","ENCLOSED","ENCRYPTION","ENDS","ENFORCED","ENGINE","ENGINES","ENGINE_ATTRIBUTE","ENUM","ERROR","ERRORS","ESCAPE","ESCAPED","EVENT","EVENTS","EVERY","EXCHANGE","EXCLUDE","EXISTS","EXIT","EXPANSION","EXPIRE","EXPORT","EXTENDED","EXTENT_SIZE","FACTOR","FAILED_LOGIN_ATTEMPTS","FALSE","FAST","FAULTS","FETCH","FIELDS","FILE","FILE_BLOCK_SIZE","FILTER","FINISH","FIRST","FIXED","FLOAT","FLOAT4","FLOAT8","FOLLOWING","FOLLOWS","FOR","FORCE","FOREIGN","FOUND","FULL","FULLTEXT","FUNCTION","GENERAL","GENERATED","GEOMCOLLECTION","GEOMETRY","GEOMETRYCOLLECTION","GET","GET_MASTER_PUBLIC_KEY","GET_SOURCE_PUBLIC_KEY","GLOBAL","@@GLOBAL","GRANTS","GROUP","GROUPS","GROUP_REPLICATION","GTID_ONLY","HASH","HIGH_PRIORITY","HISTOGRAM","HISTORY","HOST","HOSTS","HOUR_MICROSECOND","HOUR_MINUTE","HOUR_SECOND","IDENTIFIED","IGNORE","IGNORE_SERVER_IDS","IMPORT","INACTIVE","INDEX","INDEXES","INFILE","INITIAL","INITIAL_SIZE","INITIATE","INNER","INOUT","INSENSITIVE","INSERT_METHOD","INSTALL","INSTANCE","INT","INT1","INT2","INT3","INT4","INT8","INTEGER","INTO","INVISIBLE","INVOKER","IO","IO_AFTER_GTIDS","IO_BEFORE_GTIDS","IO_THREAD","IPC","ISOLATION","ISSUER","ITERATE","JSON","KEY","KEYRING","KEYS","KEY_BLOCK_SIZE","LANGUAGE","LAST","LATERAL","LEADING","LEAVE","LEAVES","LESS","LEVEL","LINEAR","LINES","LINESTRING","LIST","LOAD","LOCAL","LOCK","LOCKED","LOCKS","LOGFILE","LOGS","LONG","LONGBLOB","LONGTEXT","LOOP","LOW_PRIORITY","MASTER","MASTER_AUTO_POSITION","MASTER_BIND","MASTER_COMPRESSION_ALGORITHMS","MASTER_CONNECT_RETRY","MASTER_DELAY","MASTER_HEARTBEAT_PERIOD","MASTER_HOST","MASTER_LOG_FILE","MASTER_LOG_POS","MASTER_PASSWORD","MASTER_PORT","MASTER_PUBLIC_KEY_PATH","MASTER_RETRY_COUNT","MASTER_SERVER_ID","MASTER_SSL","MASTER_SSL_CA","MASTER_SSL_CAPATH","MASTER_SSL_CERT","MASTER_SSL_CIPHER","MASTER_SSL_CRL","MASTER_SSL_CRLPATH","MASTER_SSL_KEY","MASTER_SSL_VERIFY_SERVER_CERT","MASTER_TLS_CIPHERSUITES","MASTER_TLS_VERSION","MASTER_USER","MASTER_ZSTD_COMPRESSION_LEVEL","MAXVALUE","MAX_CONNECTIONS_PER_HOUR","MAX_QUERIES_PER_HOUR","MAX_ROWS","MAX_SIZE","MAX_UPDATES_PER_HOUR","MAX_USER_CONNECTIONS","MEDIUM","MEDIUMBLOB","MEDIUMINT","MEDIUMTEXT","MEMBER","MEMORY","MERGE","MESSAGE_TEXT","MIDDLEINT","MIGRATE","MINUTE_MICROSECOND","MINUTE_SECOND","MIN_ROWS","MODE","MODIFIES","MODIFY","MULTILINESTRING","MULTIPOINT","MULTIPOLYGON","MUTEX","MYSQL_ERRNO","NAME","NAMES","NATIONAL","NATURAL","NCHAR","NDB","NDBCLUSTER","NESTED","NETWORK_NAMESPACE","NEVER","NEW","NEXT","NO","NODEGROUP","NONE","NOWAIT","NO_WAIT","NO_WRITE_TO_BINLOG","NULL","NULLS","NUMBER","NUMERIC","NVARCHAR","OF","OFF","OFFSET","OJ","OLD","ON DELETE","ON UPDATE","ONE","ONLY","OPEN","OPTIMIZE","OPTIMIZER_COSTS","OPTION","OPTIONAL","OPTIONALLY","OPTIONS","ORDER","ORDINALITY","ORGANIZATION","OTHERS","OUT","OUTER","OUTFILE","OVER","OWNER","PACK_KEYS","PAGE","PARSER","PARSE_GCOL_EXPR","PARTIAL","PARTITION","PARTITIONING","PARTITIONS","PASSWORD","PASSWORD_LOCK_TIME","PATH","PERSIST","@@PERSIST","PERSIST_ONLY","@@PERSIST_ONLY","PHASE","PLUGIN","PLUGINS","PLUGIN_DIR","POINT","POLYGON","PORT","PRECEDES","PRECEDING","PRECISION","PRESERVE","PREV","PRIMARY","PRIVILEGES","PRIVILEGE_CHECKS_USER","PROCEDURE","PROCESS","PROCESSLIST","PROFILE","PROFILES","PROXY","PURGE","QUERY","QUICK","RANDOM","RANGE","READ","READS","READ_ONLY","READ_WRITE","REAL","REBUILD","RECOVER","RECURSIVE","REDOFILE","REDO_BUFFER_SIZE","REDUNDANT","REFERENCE","REFERENCES","REGISTRATION","RELAY","RELAYLOG","RELAY_LOG_FILE","RELAY_LOG_POS","RELAY_THREAD","RELEASE","RELOAD","REMOTE","REMOVE","RENAME","REORGANIZE","REPAIR","REPEATABLE","REPLICA","REPLICAS","REPLICATE_DO_DB","REPLICATE_DO_TABLE","REPLICATE_IGNORE_DB","REPLICATE_IGNORE_TABLE","REPLICATE_REWRITE_DB","REPLICATE_WILD_DO_TABLE","REPLICATE_WILD_IGNORE_TABLE","REPLICATION","REQUIRE","REQUIRE_ROW_FORMAT","RESIGNAL","RESOURCE","RESPECT","RESTORE","RESTRICT","RESUME","RETAIN","RETURN","RETURNED_SQLSTATE","RETURNING","RETURNS","REUSE","ROLE","ROLLUP","ROTATE","ROUTINE","ROW","ROWS","ROW_FORMAT","RTREE","SCHEDULE","SCHEMAS","SCHEMA_NAME","SECONDARY","SECONDARY_ENGINE","SECONDARY_ENGINE_ATTRIBUTE","SECONDARY_LOAD","SECONDARY_UNLOAD","SECOND_MICROSECOND","SECURITY","SENSITIVE","SEPARATOR","SERIAL","SERIALIZABLE","SERVER","SESSION","@@SESSION","SHARE","SIGNAL","SIGNED","SIMPLE","SKIP","SLAVE","SLOW","SMALLINT","SNAPSHOT","SOCKET","SOME","SONAME","SOUNDS","SOURCE","SOURCE_AUTO_POSITION","SOURCE_BIND","SOURCE_COMPRESSION_ALGORITHMS","SOURCE_CONNECT_RETRY","SOURCE_DELAY","SOURCE_HEARTBEAT_PERIOD","SOURCE_HOST","SOURCE_LOG_FILE","SOURCE_LOG_POS","SOURCE_PASSWORD","SOURCE_PORT","SOURCE_PUBLIC_KEY_PATH","SOURCE_RETRY_COUNT","SOURCE_SSL","SOURCE_SSL_CA","SOURCE_SSL_CAPATH","SOURCE_SSL_CERT","SOURCE_SSL_CIPHER","SOURCE_SSL_CRL","SOURCE_SSL_CRLPATH","SOURCE_SSL_KEY","SOURCE_SSL_VERIFY_SERVER_CERT","SOURCE_TLS_CIPHERSUITES","SOURCE_TLS_VERSION","SOURCE_USER","SOURCE_ZSTD_COMPRESSION_LEVEL","SPATIAL","SPECIFIC","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQL_AFTER_GTIDS","SQL_AFTER_MTS_GAPS","SQL_BEFORE_GTIDS","SQL_BIG_RESULT","SQL_BUFFER_RESULT","SQL_CACHE","SQL_CALC_FOUND_ROWS","SQL_NO_CACHE","SQL_SMALL_RESULT","SQL_THREAD","SQL_TSI_DAY","SQL_TSI_HOUR","SQL_TSI_MINUTE","SQL_TSI_MONTH","SQL_TSI_QUARTER","SQL_TSI_SECOND","SQL_TSI_WEEK","SQL_TSI_YEAR","SRID","SSL","STACKED","START","STARTING","STARTS","STATS_AUTO_RECALC","STATS_PERSISTENT","STATS_SAMPLE_PAGES","STATUS","STOP","STORAGE","STORED","STREAM","STRING","SUBCLASS_ORIGIN","SUBJECT","SUBPARTITION","SUBPARTITIONS","SUPER","SUSPEND","SWAPS","SWITCHES","SYSTEM","TABLES","TABLESPACE","TABLE_CHECKSUM","TABLE_NAME","TEMPORARY","TEMPTABLE","TERMINATED","TEXT","THAN","THEN","THREAD_PRIORITY","TIES","TINYBLOB","TINYINT","TINYTEXT","TLS","TO","TRAILING","TRANSACTION","TRIGGER","TRIGGERS","TRUE","TYPE","TYPES","UNBOUNDED","UNCOMMITTED","UNDEFINED","UNDO","UNDOFILE","UNDO_BUFFER_SIZE","UNICODE","UNINSTALL","UNIQUE","UNKNOWN","UNLOCK","UNREGISTER","UNSIGNED","UNTIL","UPGRADE","USAGE","USER_RESOURCES","USE_FRM","VALIDATION","VALUE","VARBINARY","VARCHAR","VARCHARACTER","VARIABLES","VARYING","VCPU","VIEW","VIRTUAL","VISIBLE","WAIT","WARNINGS","WHILE","WINDOW","WITHOUT","WORK","WRAPPER","WRITE","X509","XID","XML","YEAR_MONTH","ZEROFILL","ZONE"],FT=["ALTER DATABASE","ALTER EVENT","ALTER FUNCTION","ALTER INSTANCE","ALTER LOGFILE GROUP","ALTER PROCEDURE","ALTER RESOURCE GROUP","ALTER SERVER","ALTER TABLE","ALTER TABLESPACE","ALTER USER","ALTER VIEW","ANALYZE TABLE","BINLOG","CACHE INDEX","CALL","CHANGE MASTER TO","CHANGE REPLICATION FILTER","CHANGE REPLICATION SOURCE TO","CHECK TABLE","CHECKSUM TABLE","CLONE","COMMIT","CREATE DATABASE","CREATE EVENT","CREATE FUNCTION","CREATE FUNCTION","CREATE INDEX","CREATE LOGFILE GROUP","CREATE PROCEDURE","CREATE RESOURCE GROUP","CREATE ROLE","CREATE SERVER","CREATE SPATIAL REFERENCE SYSTEM","CREATE TABLE","CREATE TABLESPACE","CREATE TRIGGER","CREATE USER","CREATE VIEW","DEALLOCATE PREPARE","DELETE","DELETE FROM","DESCRIBE","DO","DROP DATABASE","DROP EVENT","DROP FUNCTION","DROP FUNCTION","DROP INDEX","DROP LOGFILE GROUP","DROP PROCEDURE","DROP RESOURCE GROUP","DROP ROLE","DROP SERVER","DROP SPATIAL REFERENCE SYSTEM","DROP TABLE","DROP TABLESPACE","DROP TRIGGER","DROP USER","DROP VIEW","EXECUTE","EXPLAIN","FLUSH","GRANT","HANDLER","HELP","IMPORT TABLE","INSERT","INSTALL COMPONENT","INSTALL PLUGIN","KILL","LOAD DATA","LOAD INDEX INTO CACHE","LOAD XML","LOCK INSTANCE FOR BACKUP","LOCK TABLES","MASTER_POS_WAIT","OPTIMIZE TABLE","PREPARE","PURGE BINARY LOGS","RELEASE SAVEPOINT","RENAME TABLE","RENAME USER","REPAIR TABLE","REPLACE","RESET","RESET MASTER","RESET PERSIST","RESET REPLICA","RESET SLAVE","RESTART","REVOKE","ROLLBACK","ROLLBACK TO SAVEPOINT","SAVEPOINT","SELECT","SET","SET CHARACTER SET","SET DEFAULT ROLE","SET NAMES","SET PASSWORD","SET RESOURCE GROUP","SET ROLE","SET TRANSACTION","SHOW","SHOW BINARY LOGS","SHOW BINLOG EVENTS","SHOW CHARACTER SET","SHOW COLLATION","SHOW COLUMNS","SHOW CREATE DATABASE","SHOW CREATE EVENT","SHOW CREATE FUNCTION","SHOW CREATE PROCEDURE","SHOW CREATE TABLE","SHOW CREATE TRIGGER","SHOW CREATE USER","SHOW CREATE VIEW","SHOW DATABASES","SHOW ENGINE","SHOW ENGINES","SHOW ERRORS","SHOW EVENTS","SHOW FUNCTION CODE","SHOW FUNCTION STATUS","SHOW GRANTS","SHOW INDEX","SHOW MASTER STATUS","SHOW OPEN TABLES","SHOW PLUGINS","SHOW PRIVILEGES","SHOW PROCEDURE CODE","SHOW PROCEDURE STATUS","SHOW PROCESSLIST","SHOW PROFILE","SHOW PROFILES","SHOW RELAYLOG EVENTS","SHOW REPLICA STATUS","SHOW REPLICAS","SHOW SLAVE","SHOW SLAVE HOSTS","SHOW STATUS","SHOW TABLE STATUS","SHOW TABLES","SHOW TRIGGERS","SHOW VARIABLES","SHOW WARNINGS","SHUTDOWN","SOURCE_POS_WAIT","START GROUP_REPLICATION","START REPLICA","START SLAVE","START TRANSACTION","STOP GROUP_REPLICATION","STOP REPLICA","STOP SLAVE","TABLE","TRUNCATE TABLE","UNINSTALL COMPONENT","UNINSTALL PLUGIN","UNLOCK INSTANCE","UNLOCK TABLES","UPDATE","USE","VALUES","WITH","XA","ITERATE","LEAVE","LOOP","REPEAT","RETURN","WHILE","ADD","ALTER COLUMN","FROM","GROUP BY","HAVING","INSERT INTO","LIMIT","OFFSET","ORDER BY","WHERE","WINDOW","PARTITION BY"],HT=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT"],YT=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","CROSS JOIN","NATURAL JOIN","STRAIGHT_JOIN","NATURAL LEFT JOIN","NATURAL LEFT OUTER JOIN","NATURAL RIGHT JOIN","NATURAL RIGHT OUTER JOIN"],VT=["WHEN","ELSE","ELSEIF"],bT=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&yT(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=BT(e);if(R){var t=BT(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return hT(this,E)});function r(){return pT(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:FT,reservedBinaryCommands:HT,reservedJoins:YT,reservedDependentClauses:VT,reservedLogicalOperators:["AND","OR","XOR"],reservedKeywords:_([].concat(vT,dT)),stringTypes:['""',{quote:"''",prefixes:["X"]}],identTypes:["``"],identChars:{first:"$",rest:"$"},variableTypes:[{regex:"@[A-Za-z0-9_.$]+"},{quote:'""',prefixes:["@"],requirePrefix:!0},{quote:"''",prefixes:["@"],requirePrefix:!0},{quote:"``",prefixes:["@"],requirePrefix:!0}],positionalParams:!0,lineCommentTypes:["--","#"],operators:r.operators,postProcess:mT})}}])&&GT(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function mT(E){return E.map((function(T,t){var e=E[t+1]||n;return I.SET(T)&&"("===e.value?Object.assign(Object.assign({},T),{type:A.RESERVED_KEYWORD}):T}))}function gT(E){return gT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},gT(E)}function XT(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function WT(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function KT(E,T){return KT=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},KT(E,T)}function wT(E,T){if(T&&("object"===gT(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function xT(E){return xT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},xT(E)}bT.operators=["~",":=","<<",">>","<=>","&&","||","->","->>"];var JT=["ABORT","ABS","ACOS","ADVISOR","ARRAY_AGG","ARRAY_AGG","ARRAY_APPEND","ARRAY_AVG","ARRAY_BINARY_SEARCH","ARRAY_CONCAT","ARRAY_CONTAINS","ARRAY_COUNT","ARRAY_DISTINCT","ARRAY_EXCEPT","ARRAY_FLATTEN","ARRAY_IFNULL","ARRAY_INSERT","ARRAY_INTERSECT","ARRAY_LENGTH","ARRAY_MAX","ARRAY_MIN","ARRAY_MOVE","ARRAY_POSITION","ARRAY_PREPEND","ARRAY_PUT","ARRAY_RANGE","ARRAY_REMOVE","ARRAY_REPEAT","ARRAY_REPLACE","ARRAY_REVERSE","ARRAY_SORT","ARRAY_STAR","ARRAY_SUM","ARRAY_SYMDIFF","ARRAY_SYMDIFF1","ARRAY_SYMDIFFN","ARRAY_UNION","ASIN","ATAN","ATAN2","AVG","BASE64","BASE64_DECODE","BASE64_ENCODE","BITAND ","BITCLEAR ","BITNOT ","BITOR ","BITSET ","BITSHIFT ","BITTEST ","BITXOR ","CEIL","CLOCK_LOCAL","CLOCK_MILLIS","CLOCK_STR","CLOCK_TZ","CLOCK_UTC","COALESCE","CONCAT","CONCAT2","CONTAINS","CONTAINS_TOKEN","CONTAINS_TOKEN_LIKE","CONTAINS_TOKEN_REGEXP","COS","COUNT","COUNT","COUNTN","CUME_DIST","CURL","DATE_ADD_MILLIS","DATE_ADD_STR","DATE_DIFF_MILLIS","DATE_DIFF_STR","DATE_FORMAT_STR","DATE_PART_MILLIS","DATE_PART_STR","DATE_RANGE_MILLIS","DATE_RANGE_STR","DATE_TRUNC_MILLIS","DATE_TRUNC_STR","DECODE","DECODE_JSON","DEGREES","DENSE_RANK","DURATION_TO_STR","ENCODED_SIZE","ENCODE_JSON","EXP","FIRST_VALUE","FLOOR","GREATEST","HAS_TOKEN","IFINF","IFMISSING","IFMISSINGORNULL","IFNAN","IFNANORINF","IFNULL","INITCAP","ISARRAY","ISATOM","ISBITSET","ISBOOLEAN","ISNUMBER","ISOBJECT","ISSTRING","LAG","LAST_VALUE","LEAD","LEAST","LENGTH","LN","LOG","LOWER","LTRIM","MAX","MEAN","MEDIAN","META","MILLIS","MILLIS_TO_LOCAL","MILLIS_TO_STR","MILLIS_TO_TZ","MILLIS_TO_UTC","MILLIS_TO_ZONE_NAME","MIN","MISSINGIF","NANIF","NEGINFIF","NOW_LOCAL","NOW_MILLIS","NOW_STR","NOW_TZ","NOW_UTC","NTH_VALUE","NTILE","NULLIF","NVL","NVL2","OBJECT_ADD","OBJECT_CONCAT","OBJECT_INNER_PAIRS","OBJECT_INNER_VALUES","OBJECT_LENGTH","OBJECT_NAMES","OBJECT_PAIRS","OBJECT_PUT","OBJECT_REMOVE","OBJECT_RENAME","OBJECT_REPLACE","OBJECT_UNWRAP","OBJECT_VALUES","PAIRS","PERCENT_RANK","PI","POLY_LENGTH","POSINFIF","POSITION","POWER","RADIANS","RANDOM","RANK","RATIO_TO_REPORT","REGEXP_CONTAINS","REGEXP_LIKE","REGEXP_MATCHES","REGEXP_POSITION","REGEXP_REPLACE","REGEXP_SPLIT","REGEX_CONTAINS","REGEX_LIKE","REGEX_MATCHES","REGEX_POSITION","REGEX_REPLACE","REGEX_SPLIT","REPEAT","REPLACE","REVERSE","ROUND","ROW_NUMBER","RTRIM","SEARCH","SEARCH_META","SEARCH_SCORE","SIGN","SIN","SPLIT","SQRT","STDDEV","STDDEV_POP","STDDEV_SAMP","STR_TO_DURATION","STR_TO_MILLIS","STR_TO_TZ","STR_TO_UTC","STR_TO_ZONE_NAME","SUBSTR","SUFFIXES","SUM","TAN","TITLE","TOARRAY","TOATOM","TOBOOLEAN","TOKENS","TOKENS","TONUMBER","TOOBJECT","TOSTRING","TRIM","TRUNC","UPPER","UUID","VARIANCE","VARIANCE_POP","VARIANCE_SAMP","VAR_POP","VAR_SAMP","WEEKDAY_MILLIS","WEEKDAY_STR"],kT=["ALL","ALTER","ANALYZE","ANY","ARRAY","AS","ASC","AT","BEGIN","BETWEEN","BINARY","BOOLEAN","BREAK","BUCKET","BUILD","BY","CALL","CAST","CHAR","CLUSTER","COLLATE","COLLECTION","COMMIT","COMMITTED","CONNECT","CONTINUE","CORRELATE","CORRELATED","COVER","CREATE","CURRENT","DATABASE","DATASET","DATASTORE","DECLARE","DECREMENT","DERIVED","DESC","DESCRIBE","DISTINCT","DO","DROP","EACH","ELEMENT","EVERY","EXCLUDE","EXISTS","FALSE","FETCH","FILTER","FIRST","FLATTEN","FLUSH","FOLLOWING","FOR","FORCE","FTS","FUNCTION","GOLANG","GROUP","GROUPS","GSI","HASH","IF","IGNORE","ILIKE","IN","INCLUDE","INCREMENT","INDEX","INLINE","INNER","INTO","IS","ISOLATION","JAVASCRIPT","KEY","KEYS","KEYSPACE","KNOWN","LANGUAGE","LAST","LEFT","LETTING","LEVEL","LIKE","LSM","MAP","MAPPING","MATCHED","MATERIALIZED","MISSING","NAMESPACE","NL","NO","NOT","NULL","NULLS","NUMBER","OBJECT","OFFSET","OPTION","OPTIONS","ORDER","OTHERS","OUTER","OVER","PARSE","PARTITION","PASSWORD","PATH","POOL","PRECEDING","PRIMARY","PRIVATE","PRIVILEGE","PROBE","PROCEDURE","PUBLIC","RANGE","RAW","REALM","REDUCE","RENAME","RESPECT","RETURN","RIGHT","ROLE","ROLLBACK","ROW","ROWS","SATISFIES","SCHEMA","SCOPE","SELF","SEMI","SOME","START","STATISTICS","STRING","SYSTEM","THEN","TIES","TO","TRAN","TRANSACTION","TRIGGER","TRUE","TRUNCATE","UNBOUNDED","UNDER","UNIQUE","UNKNOWN","UNSET","USE","USER","VALIDATE","VALUE","VALUED","VIA","VIEW","WHILE","WINDOW","WITHIN","WORK"],jT=["ADVISE","ALTER INDEX","BEGIN TRANSACTION","BUILD INDEX","COMMIT TRANSACTION","CREATE COLLECTION","CREATE FUNCTION","CREATE INDEX","CREATE PRIMARY INDEX","CREATE SCOPE","CREATE TABLE","DELETE","DELETE FROM","DROP COLLECTION","DROP FUNCTION","DROP INDEX","DROP PRIMARY INDEX","DROP SCOPE","EXECUTE","EXECUTE FUNCTION","EXPLAIN","GRANT","INFER","INSERT","MERGE","PREPARE","RETURNING","REVOKE","ROLLBACK TRANSACTION","SAVEPOINT","SELECT","SET TRANSACTION","UPDATE","UPDATE STATISTICS","UPSERT","DROP TABLE","FROM","GROUP BY","HAVING","INSERT INTO","LET","LIMIT","OFFSET","NEST","ORDER BY","SET CURRENT SCHEMA","SET SCHEMA","SET","SHOW","UNNEST","USE KEYS","VALUES","WHERE","WITH","WINDOW","PARTITION BY"],QT=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT","MINUS","MINUS ALL","MINUS DISTINCT"],ZT=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN"],$T=["WHEN","ELSE"],qT=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&KT(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=xT(e);if(R){var t=xT(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return wT(this,E)});function r(){return XT(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:jT,reservedBinaryCommands:QT,reservedJoins:ZT,reservedDependentClauses:$T,reservedLogicalOperators:["AND","OR","XOR"],reservedKeywords:_([].concat(kT,JT)),stringTypes:['""',"''"],identTypes:["``"],openParens:["(","[","{"],closeParens:[")","]","}"],positionalParams:!0,numberedParamTypes:["$"],namedParamTypes:["$"],lineCommentTypes:["#","--"],operators:r.operators})}}])&&WT(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function zT(E){return zT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},zT(E)}function Et(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function Tt(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function tt(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function et(E,T){return et=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},et(E,T)}function Rt(E,T){if(T&&("object"===zT(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function At(E){return At=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},At(E)}qT.operators=["==","||"];var rt={numeric:["ABS","ACOS","ASIN","ATAN","ATAN2","BITAND","CEIL","COS","COSH","EXP","FLOOR","LN","LOG","MOD","NANVL","POWER","REMAINDER","ROUND","SIGN","SIN","SINH","SQRT","TAN","TANH","TRUNC","WIDTH_BUCKET"],character:["CHR","CONCAT","INITCAP","LOWER","LPAD","LTRIM","NLS_INITCAP","NLS_LOWER","NLSSORT","NLS_UPPER","REGEXP_REPLACE","REGEXP_SUBSTR","REPLACE","RPAD","RTRIM","SOUNDEX","SUBSTR","TRANSLATE","TREAT","TRIM","UPPER","NLS_CHARSET_DECL_LEN","NLS_CHARSET_ID","NLS_CHARSET_NAME","ASCII","INSTR","LENGTH","REGEXP_INSTR"],datetime:["ADD_MONTHS","CURRENT_DATE","CURRENT_TIMESTAMP","DBTIMEZONE","EXTRACT","FROM_TZ","LAST_DAY","LOCALTIMESTAMP","MONTHS_BETWEEN","NEW_TIME","NEXT_DAY","NUMTODSINTERVAL","NUMTOYMINTERVAL","ROUND","SESSIONTIMEZONE","SYS_EXTRACT_UTC","SYSDATE","SYSTIMESTAMP","TO_CHAR","TO_TIMESTAMP","TO_TIMESTAMP_TZ","TO_DSINTERVAL","TO_YMINTERVAL","TRUNC","TZ_OFFSET"],comparison:["GREATEST","LEAST"],conversion:["ASCIISTR","BIN_TO_NUM","CAST","CHARTOROWID","COMPOSE","CONVERT","DECOMPOSE","HEXTORAW","NUMTODSINTERVAL","NUMTOYMINTERVAL","RAWTOHEX","RAWTONHEX","ROWIDTOCHAR","ROWIDTONCHAR","SCN_TO_TIMESTAMP","TIMESTAMP_TO_SCN","TO_BINARY_DOUBLE","TO_BINARY_FLOAT","TO_CHAR","TO_CLOB","TO_DATE","TO_DSINTERVAL","TO_LOB","TO_MULTI_BYTE","TO_NCHAR","TO_NCLOB","TO_NUMBER","TO_DSINTERVAL","TO_SINGLE_BYTE","TO_TIMESTAMP","TO_TIMESTAMP_TZ","TO_YMINTERVAL","TO_YMINTERVAL","TRANSLATE","UNISTR"],largeObject:["BFILENAME","EMPTY_BLOB,","EMPTY_CLOB"],collection:["CARDINALITY","COLLECT","POWERMULTISET","POWERMULTISET_BY_CARDINALITY","SET"],hierarchical:["SYS_CONNECT_BY_PATH"],dataMining:["CLUSTER_ID","CLUSTER_PROBABILITY","CLUSTER_SET","FEATURE_ID","FEATURE_SET","FEATURE_VALUE","PREDICTION","PREDICTION_COST","PREDICTION_DETAILS","PREDICTION_PROBABILITY","PREDICTION_SET"],xml:["APPENDCHILDXML","DELETEXML","DEPTH","EXTRACT","EXISTSNODE","EXTRACTVALUE","INSERTCHILDXML","INSERTXMLBEFORE","PATH","SYS_DBURIGEN","SYS_XMLAGG","SYS_XMLGEN","UPDATEXML","XMLAGG","XMLCDATA","XMLCOLATTVAL","XMLCOMMENT","XMLCONCAT","XMLFOREST","XMLPARSE","XMLPI","XMLQUERY","XMLROOT","XMLSEQUENCE","XMLSERIALIZE","XMLTABLE","XMLTRANSFORM"],encoding:["DECODE","DUMP","ORA_HASH","VSIZE"],nullRelated:["COALESCE","LNNVL","NULLIF","NVL","NVL2"],env:["SYS_CONTEXT","SYS_GUID","SYS_TYPEID","UID","USER","USERENV"],aggregate:["AVG","COLLECT","CORR","CORR_S","CORR_K","COUNT","COVAR_POP","COVAR_SAMP","CUME_DIST","DENSE_RANK","FIRST","GROUP_ID","GROUPING","GROUPING_ID","LAST","MAX","MEDIAN","MIN","PERCENTILE_CONT","PERCENTILE_DISC","PERCENT_RANK","RANK","REGR_SLOPE","REGR_INTERCEPT","REGR_COUNT","REGR_R2","REGR_AVGX","REGR_AVGY","REGR_SXX","REGR_SYY","REGR_SXY","STATS_BINOMIAL_TEST","STATS_CROSSTAB","STATS_F_TEST","STATS_KS_TEST","STATS_MODE","STATS_MW_TEST","STATS_ONE_WAY_ANOVA","STATS_T_TEST_ONE","STATS_T_TEST_PAIRED","STATS_T_TEST_INDEP","STATS_T_TEST_INDEPU","STATS_WSR_TEST","STDDEV","STDDEV_POP","STDDEV_SAMP","SUM","VAR_POP","VAR_SAMP","VARIANCE"],window:["FIRST_VALUE","LAG","LAST_VALUE","LEAD","NTILE","RATIO_TO_REPORT","ROW_NUMBER"],objectReference:["DEREF","MAKE_REF","REF","REFTOHEX","VALUE"],model:["CV","ITERATION_NUMBER","PRESENTNNV","PRESENTV","PREVIOUS"]},St=["ACCESSIBLE","AGENT","AGGREGATE","ALL","ALTER","ANY","ARRAY","AS","ASC","AT","ATTRIBUTE","AUTHID","AVG","BETWEEN","BFILE_BASE","BINARY","BINARY_INTEGER","BLOB_BASE","BLOCK","BODY","BOOLEAN","BOTH","BOUND","BREADTH","BULK","BY","BYTE","CALL","CALLING","CASCADE","CAST","CHAR","CHARACTER","CHARSET","CHARSETFORM","CHARSETID","CHAR_BASE","CHECK","CLOB_BASE","CLONE","CLOSE","CLUSTER","CLUSTERS","COALESCE","COLAUTH","COLLECT","COLUMNS","COMMENT","COMMIT","COMMITTED","COMPILED","COMPRESS","CONNECT","CONSTANT","CONSTRUCTOR","CONTEXT","CONTINUE","CONVERT","COUNT","CRASH","CREATE","CREDENTIAL","CURRENT","CURRVAL","CURSOR","CUSTOMDATUM","DANGLING","DATA","DATE","DATE_BASE","DAY","DECIMAL","DEFAULT","DEFINE","DEPTH","DESC","DETERMINISTIC","DIRECTORY","DISTINCT","DO","DOUBLE","DROP","DURATION","ELEMENT","ELSIF","EMPTY","ESCAPE","EXCEPTIONS","EXCLUSIVE","EXECUTE","EXISTS","EXIT","EXTENDS","EXTERNAL","EXTRACT","FALSE","FETCH","FINAL","FIRST","FIXED","FLOAT","FOR","FORALL","FORCE","FUNCTION","GENERAL","GOTO","GRANT","GROUP","HASH","HEAP","HIDDEN","HOUR","IDENTIFIED","IF","IMMEDIATE","IN","INCLUDING","INDEX","INDEXES","INDICATOR","INDICES","INFINITE","INSTANTIABLE","INT","INTEGER","INTERFACE","INTERVAL","INTO","INVALIDATE","IS","ISOLATION","JAVA","LANGUAGE","LARGE","LEADING","LENGTH","LEVEL","LIBRARY","LIKE","LIKE2","LIKE4","LIKEC","LIMITED","LOCAL","LOCK","LONG","MAP","MAX","MAXLEN","MEMBER","MERGE","MIN","MINUTE","MLSLABEL","MOD","MODE","MONTH","MULTISET","NAME","NAN","NATIONAL","NATIVE","NATURAL","NATURALN","NCHAR","NEW","NEXTVAL","NOCOMPRESS","NOCOPY","NOT","NOWAIT","NULL","NULLIF","NUMBER","NUMBER_BASE","OBJECT","OCICOLL","OCIDATE","OCIDATETIME","OCIDURATION","OCIINTERVAL","OCILOBLOCATOR","OCINUMBER","OCIRAW","OCIREF","OCIREFCURSOR","OCIROWID","OCISTRING","OCITYPE","OF","OLD","ON DELETE","ON UPDATE","ONLY","OPAQUE","OPEN","OPERATOR","OPTION","ORACLE","ORADATA","ORDER","ORGANIZATION","ORLANY","ORLVARY","OTHERS","OUT","OVERLAPS","OVERRIDING","PACKAGE","PARALLEL_ENABLE","PARAMETER","PARAMETERS","PARENT","PARTITION","PASCAL","PCTFREE","PIPE","PIPELINED","PLS_INTEGER","PLUGGABLE","POSITIVE","POSITIVEN","PRAGMA","PRECISION","PRIOR","PRIVATE","PROCEDURE","PUBLIC","RAISE","RANGE","RAW","READ","REAL","RECORD","REF","REFERENCE","RELEASE","RELIES_ON","REM","REMAINDER","RENAME","RESOURCE","RESULT","RESULT_CACHE","RETURN","REVERSE","REVOKE","ROLLBACK","ROW","ROWID","ROWNUM","ROWTYPE","SAMPLE","SAVE","SAVEPOINT","SB1","SB2","SB4","SEARCH","SECOND","SEGMENT","SELF","SEPARATE","SEQUENCE","SERIALIZABLE","SHARE","SHORT","SIZE","SIZE_T","SMALLINT","SOME","SPACE","SPARSE","SQL","SQLCODE","SQLDATA","SQLERRM","SQLNAME","SQLSTATE","STANDARD","START","STATIC","STDDEV","STORED","STRING","STRUCT","STYLE","SUBMULTISET","SUBPARTITION","SUBSTITUTABLE","SUBTYPE","SUCCESSFUL","SUM","SYNONYM","SYSDATE","TABAUTH","TABLE","TDO","THE","THEN","TIME","TIMESTAMP","TIMEZONE_ABBR","TIMEZONE_HOUR","TIMEZONE_MINUTE","TIMEZONE_REGION","TO","TRAILING","TRANSACTION","TRANSACTIONAL","TRIGGER","TRUE","TRUSTED","TYPE","UB1","UB2","UB4","UID","UNDER","UNIQUE","UNPLUG","UNSIGNED","UNTRUSTED","USE","USER","VALIDATE","VALIST","VALUE","VARCHAR","VARCHAR2","VARIABLE","VARIANCE","VARRAY","VARYING","VIEW","VIEWS","VOID","WHENEVER","WHILE","WORK","WRAPPED","WRITE","YEAR","ZONE"],nt=["ADD","ALTER COLUMN","ALTER TABLE","BEGIN","CONNECT BY","CREATE TABLE","DROP TABLE","DECLARE","DELETE","DELETE FROM","EXCEPT","EXCEPTION","FETCH FIRST","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","OFFSET","LOOP","MODIFY","ORDER BY","RETURNING","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","START WITH","UPDATE","VALUES","WHERE","WITH"],Ot=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT","MINUS","MINUS ALL","MINUS DISTINCT","CROSS APPLY","OUTER APPLY"],It=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],Nt=["WHEN","ELSE"],ot=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&et(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=At(e);if(R){var t=At(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return Rt(this,E)});function r(){return Tt(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:nt,reservedBinaryCommands:Ot,reservedJoins:It,reservedDependentClauses:Nt,reservedLogicalOperators:["AND","OR","XOR"],reservedKeywords:_([].concat(St,(E=Object.values(rt).flat(),function(E){if(Array.isArray(E))return Et(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return Et(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Et(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()))),stringTypes:[{quote:"''",prefixes:["N"]}],identTypes:['""'],identChars:{rest:"$#"},variableTypes:[{regex:"&{1,2}[A-Za-z][A-Za-z0-9_$#]*"}],numberedParamTypes:[":"],namedParamTypes:[":"],paramChars:{},operators:r.operators,postProcess:Ct});var E}}])&&tt(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function Ct(E){var T=n;return E.map((function(E){return I.SET(E)&&I.BY(T)?Object.assign(Object.assign({},E),{type:A.RESERVED_KEYWORD}):(N(E)&&(T=E),E)}))}function Lt(E){return Lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},Lt(E)}function it(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function _t(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function at(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function ut(E,T){return ut=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},ut(E,T)}function st(E,T){if(T&&("object"===Lt(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function Pt(E){return Pt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},Pt(E)}ot.operators=["||","**",":=","~=","^=",">>","<<","=>"];var Dt={math:["ABS","ACOS","ACOSD","ACOSH","ASIN","ASIND","ASINH","ATAN","ATAN2","ATAN2D","ATAND","ATANH","CBRT","CEIL","CEILING","COS","COSD","COSH","COT","COTD","DEGREES","DIV","EXP","FACTORIAL","FLOOR","GCD","LCM","LN","LOG","LOG10","MIN_SCALE","MOD","PI","POWER","RADIANS","RANDOM","ROUND","SCALE","SETSEED","SIGN","SIN","SIND","SINH","SQRT","TAN","TAND","TANH","TRIM_SCALE","TRUNC","WIDTH_BUCKET"],string:["ABS","ASCII","BIT_LENGTH","BTRIM","CHARACTER_LENGTH","CHAR_LENGTH","CHR","CONCAT","CONCAT_WS","FORMAT","INITCAP","LEFT","LENGTH","LOWER","LPAD","LTRIM","MD5","NORMALIZE","OCTET_LENGTH","OVERLAY","PARSE_IDENT","PG_CLIENT_ENCODING","POSITION","QUOTE_IDENT","QUOTE_LITERAL","QUOTE_NULLABLE","REGEXP_MATCH","REGEXP_MATCHES","REGEXP_REPLACE","REGEXP_SPLIT_TO_ARRAY","REGEXP_SPLIT_TO_TABLE","REPEAT","REPLACE","REVERSE","RIGHT","RPAD","RTRIM","SPLIT_PART","SPRINTF","STARTS_WITH","STRING_AGG","STRING_TO_ARRAY","STRING_TO_TABLE","STRPOS","SUBSTR","SUBSTRING","TO_ASCII","TO_HEX","TRANSLATE","TRIM","UNISTR","UPPER"],binary:["BIT_COUNT","BIT_LENGTH","BTRIM","CONVERT","CONVERT_FROM","CONVERT_TO","DECODE","ENCODE","GET_BIT","GET_BYTE","LENGTH","LTRIM","MD5","OCTET_LENGTH","OVERLAY","POSITION","RTRIM","SET_BIT","SET_BYTE","SHA224","SHA256","SHA384","SHA512","STRING_AGG","SUBSTR","SUBSTRING","TRIM"],bitstring:["BIT_COUNT","BIT_LENGTH","GET_BIT","LENGTH","OCTET_LENGTH","OVERLAY","POSITION","SET_BIT","SUBSTRING"],pattern:["REGEXP_MATCH","REGEXP_MATCHES","REGEXP_REPLACE","REGEXP_SPLIT_TO_ARRAY","REGEXP_SPLIT_TO_TABLE"],datatype:["TO_CHAR","TO_DATE","TO_NUMBER","TO_TIMESTAMP"],datetime:["CLOCK_TIMESTAMP","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","DATE_BIN","DATE_PART","DATE_TRUNC","EXTRACT","ISFINITE","JUSTIFY_DAYS","JUSTIFY_HOURS","JUSTIFY_INTERVAL","LOCALTIME","LOCALTIMESTAMP","MAKE_DATE","MAKE_INTERVAL","MAKE_TIME","MAKE_TIMESTAMP","MAKE_TIMESTAMPTZ","NOW","PG_SLEEP","PG_SLEEP_FOR","PG_SLEEP_UNTIL","STATEMENT_TIMESTAMP","TIMEOFDAY","TO_TIMESTAMP","TRANSACTION_TIMESTAMP"],enum:["ENUM_FIRST","ENUM_LAST","ENUM_RANGE"],geometry:["AREA","BOUND_BOX","BOX","CENTER","CIRCLE","DIAGONAL","DIAMETER","HEIGHT","ISCLOSED","ISOPEN","LENGTH","LINE","LSEG","NPOINTS","PATH","PCLOSE","POINT","POLYGON","POPEN","RADIUS","SLOPE","WIDTH"],network:["ABBREV","BROADCAST","FAMILY","HOST","HOSTMASK","INET_MERGE","INET_SAME_FAMILY","MACADDR8_SET7BIT","MASKLEN","NETMASK","NETWORK","SET_MASKLEN","TEXT","TRUNC"],textsearch:["ARRAY_TO_TSVECTOR","GET_CURRENT_TS_CONFIG","JSONB_TO_TSVECTOR","JSON_TO_TSVECTOR","LENGTH","NUMNODE","PHRASETO_TSQUERY","PLAINTO_TSQUERY","QUERYTREE","SETWEIGHT","STRIP","TO_TSQUERY","TO_TSVECTOR","TSQUERY_PHRASE","TSVECTOR_TO_ARRAY","TS_DEBUG","TS_DELETE","TS_FILTER","TS_HEADLINE","TS_LEXIZE","TS_PARSE","TS_RANK","TS_RANK_CD","TS_REWRITE","TS_STAT","TS_TOKEN_TYPE","WEBSEARCH_TO_TSQUERY"],uuid:["UUID"],xml:["CURSOR_TO_XML","CURSOR_TO_XMLSCHEMA","DATABASE_TO_XML","DATABASE_TO_XMLSCHEMA","DATABASE_TO_XML_AND_XMLSCHEMA","NEXTVAL","QUERY_TO_XML","QUERY_TO_XMLSCHEMA","QUERY_TO_XML_AND_XMLSCHEMA","SCHEMA_TO_XML","SCHEMA_TO_XMLSCHEMA","SCHEMA_TO_XML_AND_XMLSCHEMA","STRING","TABLE_TO_XML","TABLE_TO_XMLSCHEMA","TABLE_TO_XML_AND_XMLSCHEMA","XMLAGG","XMLCOMMENT","XMLCONCAT","XMLELEMENT","XMLEXISTS","XMLFOREST","XMLPARSE","XMLPI","XMLROOT","XMLSERIALIZE","XMLTABLE","XML_IS_WELL_FORMED","XML_IS_WELL_FORMED_CONTENT","XML_IS_WELL_FORMED_DOCUMENT","XPATH","XPATH_EXISTS"],json:["ARRAY_TO_JSON","JSONB_AGG","JSONB_ARRAY_ELEMENTS","JSONB_ARRAY_ELEMENTS_TEXT","JSONB_ARRAY_LENGTH","JSONB_BUILD_ARRAY","JSONB_BUILD_OBJECT","JSONB_EACH","JSONB_EACH_TEXT","JSONB_EXTRACT_PATH","JSONB_EXTRACT_PATH_TEXT","JSONB_INSERT","JSONB_OBJECT","JSONB_OBJECT_AGG","JSONB_OBJECT_KEYS","JSONB_PATH_EXISTS","JSONB_PATH_EXISTS_TZ","JSONB_PATH_MATCH","JSONB_PATH_MATCH_TZ","JSONB_PATH_QUERY","JSONB_PATH_QUERY_ARRAY","JSONB_PATH_QUERY_ARRAY_TZ","JSONB_PATH_QUERY_FIRST","JSONB_PATH_QUERY_FIRST_TZ","JSONB_PATH_QUERY_TZ","JSONB_POPULATE_RECORD","JSONB_POPULATE_RECORDSET","JSONB_PRETTY","JSONB_SET","JSONB_SET_LAX","JSONB_STRIP_NULLS","JSONB_TO_RECORD","JSONB_TO_RECORDSET","JSONB_TYPEOF","JSON_AGG","JSON_ARRAY_ELEMENTS","JSON_ARRAY_ELEMENTS_TEXT","JSON_ARRAY_LENGTH","JSON_BUILD_ARRAY","JSON_BUILD_OBJECT","JSON_EACH","JSON_EACH_TEXT","JSON_EXTRACT_PATH","JSON_EXTRACT_PATH_TEXT","JSON_OBJECT","JSON_OBJECT_AGG","JSON_OBJECT_KEYS","JSON_POPULATE_RECORD","JSON_POPULATE_RECORDSET","JSON_STRIP_NULLS","JSON_TO_RECORD","JSON_TO_RECORDSET","JSON_TYPEOF","ROW_TO_JSON","TO_JSON","TO_JSONB","TO_TIMESTAMP"],sequence:["CURRVAL","LASTVAL","NEXTVAL","SETVAL"],conditional:["COALESCE","GREATEST","LEAST","NULLIF"],array:["ARRAY_AGG","ARRAY_APPEND","ARRAY_CAT","ARRAY_DIMS","ARRAY_FILL","ARRAY_LENGTH","ARRAY_LOWER","ARRAY_NDIMS","ARRAY_POSITION","ARRAY_POSITIONS","ARRAY_PREPEND","ARRAY_REMOVE","ARRAY_REPLACE","ARRAY_TO_STRING","ARRAY_UPPER","CARDINALITY","STRING_TO_ARRAY","TRIM_ARRAY","UNNEST"],range:["ISEMPTY","LOWER","LOWER_INC","LOWER_INF","MULTIRANGE","RANGE_MERGE","UPPER","UPPER_INC","UPPER_INF"],aggregate:["ANY","ARRAY_AGG","AVG","BIT_AND","BIT_OR","BIT_XOR","BOOL_AND","BOOL_OR","COALESCE","CORR","COUNT","COVAR_POP","COVAR_SAMP","CUME_DIST","DENSE_RANK","EVERY","GROUPING","JSONB_AGG","JSONB_OBJECT_AGG","JSON_AGG","JSON_OBJECT_AGG","MAX","MIN","MODE","PERCENTILE_CONT","PERCENTILE_DISC","PERCENT_RANK","RANGE_AGG","RANGE_INTERSECT_AGG","RANK","REGR_AVGX","REGR_AVGY","REGR_COUNT","REGR_INTERCEPT","REGR_R2","REGR_SLOPE","REGR_SXX","REGR_SXY","REGR_SYY","SOME","STDDEV","STDDEV_POP","STDDEV_SAMP","STRING_AGG","SUM","TO_JSON","TO_JSONB","VARIANCE","VAR_POP","VAR_SAMP","XMLAGG"],window:["CUME_DIST","DENSE_RANK","FIRST_VALUE","LAG","LAST_VALUE","LEAD","NTH_VALUE","NTILE","PERCENT_RANK","RANK","ROW_NUMBER"],set:["GENERATE_SERIES","GENERATE_SUBSCRIPTS"],sysInfo:["ACLDEFAULT","ACLEXPLODE","COL_DESCRIPTION","CURRENT_CATALOG","CURRENT_DATABASE","CURRENT_QUERY","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_SCHEMAS","CURRENT_USER","FORMAT_TYPE","HAS_ANY_COLUMN_PRIVILEGE","HAS_COLUMN_PRIVILEGE","HAS_DATABASE_PRIVILEGE","HAS_FOREIGN_DATA_WRAPPER_PRIVILEGE","HAS_FUNCTION_PRIVILEGE","HAS_LANGUAGE_PRIVILEGE","HAS_SCHEMA_PRIVILEGE","HAS_SEQUENCE_PRIVILEGE","HAS_SERVER_PRIVILEGE","HAS_TABLESPACE_PRIVILEGE","HAS_TABLE_PRIVILEGE","HAS_TYPE_PRIVILEGE","INET_CLIENT_ADDR","INET_CLIENT_PORT","INET_SERVER_ADDR","INET_SERVER_PORT","MAKEACLITEM","OBJ_DESCRIPTION","PG_BACKEND_PID","PG_BLOCKING_PIDS","PG_COLLATION_IS_VISIBLE","PG_CONF_LOAD_TIME","PG_CONTROL_CHECKPOINT","PG_CONTROL_INIT","PG_CONTROL_SYSTEM","PG_CONVERSION_IS_VISIBLE","PG_CURRENT_LOGFILE","PG_CURRENT_SNAPSHOT","PG_CURRENT_XACT_ID","PG_CURRENT_XACT_ID_IF_ASSIGNED","PG_DESCRIBE_OBJECT","PG_FUNCTION_IS_VISIBLE","PG_GET_CATALOG_FOREIGN_KEYS","PG_GET_CONSTRAINTDEF","PG_GET_EXPR","PG_GET_FUNCTIONDEF","PG_GET_FUNCTION_ARGUMENTS","PG_GET_FUNCTION_IDENTITY_ARGUMENTS","PG_GET_FUNCTION_RESULT","PG_GET_INDEXDEF","PG_GET_KEYWORDS","PG_GET_OBJECT_ADDRESS","PG_GET_OWNED_SEQUENCE","PG_GET_RULEDEF","PG_GET_SERIAL_SEQUENCE","PG_GET_STATISTICSOBJDEF","PG_GET_TRIGGERDEF","PG_GET_USERBYID","PG_GET_VIEWDEF","PG_HAS_ROLE","PG_IDENTIFY_OBJECT","PG_IDENTIFY_OBJECT_AS_ADDRESS","PG_INDEXAM_HAS_PROPERTY","PG_INDEX_COLUMN_HAS_PROPERTY","PG_INDEX_HAS_PROPERTY","PG_IS_OTHER_TEMP_SCHEMA","PG_JIT_AVAILABLE","PG_LAST_COMMITTED_XACT","PG_LISTENING_CHANNELS","PG_MY_TEMP_SCHEMA","PG_NOTIFICATION_QUEUE_USAGE","PG_OPCLASS_IS_VISIBLE","PG_OPERATOR_IS_VISIBLE","PG_OPFAMILY_IS_VISIBLE","PG_OPTIONS_TO_TABLE","PG_POSTMASTER_START_TIME","PG_SAFE_SNAPSHOT_BLOCKING_PIDS","PG_SNAPSHOT_XIP","PG_SNAPSHOT_XMAX","PG_SNAPSHOT_XMIN","PG_STATISTICS_OBJ_IS_VISIBLE","PG_TABLESPACE_DATABASES","PG_TABLESPACE_LOCATION","PG_TABLE_IS_VISIBLE","PG_TRIGGER_DEPTH","PG_TS_CONFIG_IS_VISIBLE","PG_TS_DICT_IS_VISIBLE","PG_TS_PARSER_IS_VISIBLE","PG_TS_TEMPLATE_IS_VISIBLE","PG_TYPEOF","PG_TYPE_IS_VISIBLE","PG_VISIBLE_IN_SNAPSHOT","PG_XACT_COMMIT_TIMESTAMP","PG_XACT_COMMIT_TIMESTAMP_ORIGIN","PG_XACT_STATUS","PQSERVERVERSION","ROW_SECURITY_ACTIVE","SESSION_USER","SHOBJ_DESCRIPTION","TO_REGCLASS","TO_REGCOLLATION","TO_REGNAMESPACE","TO_REGOPER","TO_REGOPERATOR","TO_REGPROC","TO_REGPROCEDURE","TO_REGROLE","TO_REGTYPE","TXID_CURRENT","TXID_CURRENT_IF_ASSIGNED","TXID_CURRENT_SNAPSHOT","TXID_SNAPSHOT_XIP","TXID_SNAPSHOT_XMAX","TXID_SNAPSHOT_XMIN","TXID_STATUS","TXID_VISIBLE_IN_SNAPSHOT","USER","VERSION"],sysAdmin:["BRIN_DESUMMARIZE_RANGE","BRIN_SUMMARIZE_NEW_VALUES","BRIN_SUMMARIZE_RANGE","CONVERT_FROM","CURRENT_SETTING","GIN_CLEAN_PENDING_LIST","PG_ADVISORY_LOCK","PG_ADVISORY_LOCK_SHARED","PG_ADVISORY_UNLOCK","PG_ADVISORY_UNLOCK_ALL","PG_ADVISORY_UNLOCK_SHARED","PG_ADVISORY_XACT_LOCK","PG_ADVISORY_XACT_LOCK_SHARED","PG_BACKUP_START_TIME","PG_CANCEL_BACKEND","PG_COLLATION_ACTUAL_VERSION","PG_COLUMN_COMPRESSION","PG_COLUMN_SIZE","PG_COPY_LOGICAL_REPLICATION_SLOT","PG_COPY_PHYSICAL_REPLICATION_SLOT","PG_CREATE_LOGICAL_REPLICATION_SLOT","PG_CREATE_PHYSICAL_REPLICATION_SLOT","PG_CREATE_RESTORE_POINT","PG_CURRENT_WAL_FLUSH_LSN","PG_CURRENT_WAL_INSERT_LSN","PG_CURRENT_WAL_LSN","PG_DATABASE_SIZE","PG_DROP_REPLICATION_SLOT","PG_EXPORT_SNAPSHOT","PG_FILENODE_RELATION","PG_GET_WAL_REPLAY_PAUSE_STATE","PG_IMPORT_SYSTEM_COLLATIONS","PG_INDEXES_SIZE","PG_IS_IN_BACKUP","PG_IS_IN_RECOVERY","PG_IS_WAL_REPLAY_PAUSED","PG_LAST_WAL_RECEIVE_LSN","PG_LAST_WAL_REPLAY_LSN","PG_LAST_XACT_REPLAY_TIMESTAMP","PG_LOGICAL_EMIT_MESSAGE","PG_LOGICAL_SLOT_GET_BINARY_CHANGES","PG_LOGICAL_SLOT_GET_CHANGES","PG_LOGICAL_SLOT_PEEK_BINARY_CHANGES","PG_LOGICAL_SLOT_PEEK_CHANGES","PG_LOG_BACKEND_MEMORY_CONTEXTS","PG_LS_ARCHIVE_STATUSDIR","PG_LS_DIR","PG_LS_LOGDIR","PG_LS_TMPDIR","PG_LS_WALDIR","PG_PARTITION_ANCESTORS","PG_PARTITION_ROOT","PG_PARTITION_TREE","PG_PROMOTE","PG_READ_BINARY_FILE","PG_READ_FILE","PG_RELATION_FILENODE","PG_RELATION_FILEPATH","PG_RELATION_SIZE","PG_RELOAD_CONF","PG_REPLICATION_ORIGIN_ADVANCE","PG_REPLICATION_ORIGIN_CREATE","PG_REPLICATION_ORIGIN_DROP","PG_REPLICATION_ORIGIN_OID","PG_REPLICATION_ORIGIN_PROGRESS","PG_REPLICATION_ORIGIN_SESSION_IS_SETUP","PG_REPLICATION_ORIGIN_SESSION_PROGRESS","PG_REPLICATION_ORIGIN_SESSION_RESET","PG_REPLICATION_ORIGIN_SESSION_SETUP","PG_REPLICATION_ORIGIN_XACT_RESET","PG_REPLICATION_ORIGIN_XACT_SETUP","PG_REPLICATION_SLOT_ADVANCE","PG_ROTATE_LOGFILE","PG_SIZE_BYTES","PG_SIZE_PRETTY","PG_START_BACKUP","PG_STAT_FILE","PG_STOP_BACKUP","PG_SWITCH_WAL","PG_TABLESPACE_SIZE","PG_TABLE_SIZE","PG_TERMINATE_BACKEND","PG_TOTAL_RELATION_SIZE","PG_TRY_ADVISORY_LOCK","PG_TRY_ADVISORY_LOCK_SHARED","PG_TRY_ADVISORY_XACT_LOCK","PG_TRY_ADVISORY_XACT_LOCK_SHARED","PG_WALFILE_NAME","PG_WALFILE_NAME_OFFSET","PG_WAL_LSN_DIFF","PG_WAL_REPLAY_PAUSE","PG_WAL_REPLAY_RESUME","SET_CONFIG"],trigger:["SUPPRESS_REDUNDANT_UPDATES_TRIGGER","TSVECTOR_UPDATE_TRIGGER","TSVECTOR_UPDATE_TRIGGER_COLUMN"],eventTrigger:["PG_EVENT_TRIGGER_DDL_COMMANDS","PG_EVENT_TRIGGER_DROPPED_OBJECTS","PG_EVENT_TRIGGER_TABLE_REWRITE_OID","PG_EVENT_TRIGGER_TABLE_REWRITE_REASON","PG_GET_OBJECT_ADDRESS"],stats:["PG_MCV_LIST_ITEMS"]},ct=["ABSENT","ABSOLUTE","ACCESS","ACCORDING","ACTION","ADA","ADMIN","AGGREGATE","ALL","ALLOCATE","ALSO","ALTER","ALWAYS","ANALYSE","ARE","ARRAY","ARRAY_MAX_CARDINALITY","AS","ASC","ASENSITIVE","ASSERTION","ASSIGNMENT","ASYMMETRIC","AT","ATOMIC","ATTACH","ATTRIBUTE","ATTRIBUTES","AUTHORIZATION","BACKWARD","BASE64","BEFORE","BEGIN_FRAME","BEGIN_PARTITION","BERNOULLI","BETWEEN","BIGINT","BINARY","BIT","BLOB","BLOCKED","BOM","BOOLEAN","BOTH","BREADTH","BY","CACHE","CALLED","CASCADE","CASCADED","CAST","CATALOG","CATALOG_NAME","CHAIN","CHAINING","CHAR","CHARACTER","CHARACTERISTICS","CHARACTERS","CHARACTER_SET_CATALOG","CHARACTER_SET_NAME","CHARACTER_SET_SCHEMA","CHECK","CLASS","CLASSIFIER","CLASS_ORIGIN","CLOB","COBOL","COLLATE","COLLATION","COLLATION_CATALOG","COLLATION_NAME","COLLATION_SCHEMA","COLLECT","COLUMN","COLUMNS","COLUMN_NAME","COMMAND_FUNCTION","COMMAND_FUNCTION_CODE","COMMENTS","COMMITTED","COMPRESSION","CONCURRENTLY","CONDITION","CONDITIONAL","CONDITION_NUMBER","CONFIGURATION","CONFLICT","CONNECT","CONNECTION","CONNECTION_NAME","CONSTRAINT","CONSTRAINTS","CONSTRAINT_CATALOG","CONSTRAINT_NAME","CONSTRAINT_SCHEMA","CONSTRUCTOR","CONTAINS","CONTENT","CONTINUE","CONTROL","CONVERSION","CORRESPONDING","COST","CREATE","CROSS","CSV","CUBE","CURRENT","CURRENT_DEFAULT_TRANSFORM_GROUP","CURRENT_PATH","CURRENT_ROW","CURRENT_TRANSFORM_GROUP_FOR_TYPE","CURSOR","CURSOR_NAME","CYCLE","DATA","DATABASE","DATALINK","DATE","DATETIME_INTERVAL_CODE","DATETIME_INTERVAL_PRECISION","DAY","DB","DEC","DECFLOAT","DECIMAL","DEFAULT","DEFAULTS","DEFERRABLE","DEFERRED","DEFINE","DEFINED","DEFINER","DEGREE","DELIMITER","DELIMITERS","DEPENDS","DEPTH","DEREF","DERIVED","DESC","DESCRIBE","DESCRIPTOR","DETACH","DETERMINISTIC","DIAGNOSTICS","DICTIONARY","DISABLE","DISCONNECT","DISPATCH","DISTINCT","DLNEWCOPY","DLPREVIOUSCOPY","DLURLCOMPLETE","DLURLCOMPLETEONLY","DLURLCOMPLETEWRITE","DLURLPATH","DLURLPATHONLY","DLURLPATHWRITE","DLURLSCHEME","DLURLSERVER","DLVALUE","DOCUMENT","DOMAIN","DOUBLE","DROP","DYNAMIC","DYNAMIC_FUNCTION","DYNAMIC_FUNCTION_CODE","EACH","ELEMENT","EMPTY","ENABLE","ENCODING","ENCRYPTED","END-EXEC","END_FRAME","END_PARTITION","ENFORCED","ENUM","EQUALS","ERROR","ESCAPE","EVENT","EXCEPTION","EXCLUDE","EXCLUDING","EXCLUSIVE","EXEC","EXISTS","EXPRESSION","EXTENSION","EXTERNAL","FALSE","FILE","FILTER","FINAL","FINALIZE","FINISH","FIRST","FLAG","FLOAT","FOLLOWING","FOR","FORCE","FOREIGN","FORTRAN","FORWARD","FOUND","FRAME_ROW","FREE","FREEZE","FS","FULFILL","FULL","FUNCTION","FUNCTIONS","FUSION","GENERAL","GENERATED","GET","GLOBAL","GO","GOTO","GRANTED","GROUP","GROUPS","HANDLER","HEADER","HEX","HIERARCHY","HOLD","HOUR","IDENTITY","IF","IGNORE","ILIKE","IMMEDIATE","IMMEDIATELY","IMMUTABLE","IMPLEMENTATION","IMPLICIT","IMPORT","IN","INCLUDE","INCLUDING","INCREMENT","INDENT","INDEX","INDEXES","INDICATOR","INHERIT","INHERITS","INITIAL","INITIALLY","INLINE","INNER","INOUT","INPUT","INSENSITIVE","INSTANCE","INSTANTIABLE","INSTEAD","INT","INTEGER","INTEGRITY","INTERSECTION","INTERVAL","INTO","INVOKER","IS","ISNULL","ISOLATION","JSON","JSON_ARRAY","JSON_ARRAYAGG","JSON_EXISTS","JSON_OBJECTAGG","JSON_QUERY","JSON_TABLE","JSON_TABLE_PRIMITIVE","JSON_VALUE","KEEP","KEY","KEYS","KEY_MEMBER","KEY_TYPE","LABEL","LANGUAGE","LARGE","LAST","LATERAL","LEADING","LEAKPROOF","LEVEL","LIBRARY","LIKE","LIKE_REGEX","LINK","LISTAGG","LOCAL","LOCATION","LOCATOR","LOCKED","LOGGED","MAP","MAPPING","MATCH","MATCHED","MATCHES","MATCH_NUMBER","MATCH_RECOGNIZE","MATERIALIZED","MAXVALUE","MEASURES","MEMBER","MERGE","MESSAGE_LENGTH","MESSAGE_OCTET_LENGTH","MESSAGE_TEXT","METHOD","MINUTE","MINVALUE","MODIFIES","MODULE","MONTH","MORE","MULTISET","MUMPS","NAME","NAMES","NAMESPACE","NATIONAL","NATURAL","NCHAR","NCLOB","NESTED","NESTING","NEW","NEXT","NFC","NFD","NFKC","NFKD","NIL","NO","NONE","NORMALIZED","NOT","NOTHING","NOTNULL","NOWAIT","NULL","NULLABLE","NULLS","NUMBER","NUMERIC","OBJECT","OCCURRENCES_REGEX","OCTETS","OF","OFF","OFFSET","OIDS","OLD","OMIT","ON COMMIT","ON DELETE","ON UPDATE","ONE","ONLY","OPEN","OPERATOR","OPTION","OPTIONS","ORDER","ORDERING","ORDINALITY","OTHERS","OUT","OUTER","OUTPUT","OVER","OVERFLOW","OVERLAPS","OVERRIDING","OWNED","OWNER","PAD","PARALLEL","PARAMETER","PARAMETER_MODE","PARAMETER_NAME","PARAMETER_ORDINAL_POSITION","PARAMETER_SPECIFIC_CATALOG","PARAMETER_SPECIFIC_NAME","PARAMETER_SPECIFIC_SCHEMA","PARSER","PARTIAL","PARTITION","PASCAL","PASS","PASSING","PASSTHROUGH","PASSWORD","PAST","PATTERN","PER","PERCENT","PERIOD","PERMISSION","PERMUTE","PLACING","PLAN","PLANS","PLI","POLICY","PORTION","POSITION_REGEX","PRECEDES","PRECEDING","PRECISION","PREPARED","PRESERVE","PRIMARY","PRIOR","PRIVATE","PRIVILEGES","PROCEDURAL","PROCEDURE","PROCEDURES","PROGRAM","PRUNE","PTF","PUBLIC","PUBLICATION","QUOTE","QUOTES","RANGE","READ","READS","REAL","REASSIGN","RECHECK","RECOVERY","RECURSIVE","REF","REFERENCES","REFERENCING","REFRESH","RELATIVE","RELEASE","RENAME","REPEATABLE","REPLICA","REQUIRING","RESPECT","RESTART","RESTORE","RESTRICT","RESULT","RETURN","RETURNED_CARDINALITY","RETURNED_LENGTH","RETURNED_OCTET_LENGTH","RETURNED_SQLSTATE","RETURNS","ROLE","ROLLUP","ROUTINE","ROUTINES","ROUTINE_CATALOG","ROUTINE_NAME","ROUTINE_SCHEMA","ROW","ROWS","ROW_COUNT","RULE","RUNNING","SCALAR","SCHEMA","SCHEMAS","SCHEMA_NAME","SCOPE","SCOPE_CATALOG","SCOPE_NAME","SCOPE_SCHEMA","SCROLL","SEARCH","SECOND","SECTION","SECURITY","SEEK","SELECTIVE","SELF","SENSITIVE","SEQUENCE","SEQUENCES","SERIALIZABLE","SERVER","SERVER_NAME","SESSION","SETOF","SETS","SHARE","SIMILAR","SIMPLE","SIZE","SKIP","SMALLINT","SNAPSHOT","SOURCE","SPACE","SPECIFIC","SPECIFICTYPE","SPECIFIC_NAME","SQL","SQLCODE","SQLERROR","SQLEXCEPTION","SQLSTATE","SQLWARNING","STABLE","STANDALONE","START","STATE","STATEMENT","STATIC","STATISTICS","STDIN","STDOUT","STORAGE","STORED","STRICT","STRUCTURE","STYLE","SUBCLASS_ORIGIN","SUBMULTISET","SUBSCRIPTION","SUBSET","SUBSTRING_REGEX","SUCCEEDS","SUPPORT","SYMMETRIC","SYSID","SYSTEM","SYSTEM_TIME","SYSTEM_USER","TABLE","TABLES","TABLESAMPLE","TABLESPACE","TABLE_NAME","TEMP","TEMPLATE","TEMPORARY","THEN","THROUGH","TIES","TIME","TIMESTAMP","TIMEZONE_HOUR","TIMEZONE_MINUTE","TO","TOKEN","TOP_LEVEL_COUNT","TRAILING","TRANSACTION","TRANSACTIONS_COMMITTED","TRANSACTIONS_ROLLED_BACK","TRANSACTION_ACTIVE","TRANSFORM","TRANSFORMS","TRANSLATE_REGEX","TRANSLATION","TREAT","TRIGGER","TRIGGER_CATALOG","TRIGGER_NAME","TRIGGER_SCHEMA","TRUE","TRUSTED","TYPE","TYPES","UESCAPE","UNBOUNDED","UNCOMMITTED","UNCONDITIONAL","UNDER","UNENCRYPTED","UNIQUE","UNKNOWN","UNLINK","UNLOGGED","UNMATCHED","UNNAMED","UNTIL","UNTYPED","URI","USAGE","USER_DEFINED_TYPE_CATALOG","USER_DEFINED_TYPE_CODE","USER_DEFINED_TYPE_NAME","USER_DEFINED_TYPE_SCHEMA","UTF16","UTF32","UTF8","VALID","VALIDATE","VALIDATOR","VALUE","VALUE_OF","VARBINARY","VARCHAR","VARIADIC","VARYING","VERBOSE","VERSIONING","VIEW","VIEWS","VOLATILE","WHENEVER","WHITESPACE","WINDOW","WITHIN","WITHOUT","WORK","WRAPPER","WRITE","XML","XMLATTRIBUTES","XMLBINARY","XMLCAST","XMLDECLARATION","XMLDOCUMENT","XMLITERATE","XMLNAMESPACES","XMLQUERY","XMLSCHEMA","XMLTEXT","XMLVALIDATE","YEAR","YES","ZONE"],Mt=["ABORT","ALTER AGGREGATE","ALTER COLLATION","ALTER CONVERSION","ALTER DATABASE","ALTER DEFAULT PRIVILEGES","ALTER DOMAIN","ALTER EVENT TRIGGER","ALTER EXTENSION","ALTER FOREIGN DATA WRAPPER","ALTER FOREIGN TABLE","ALTER FUNCTION","ALTER GROUP","ALTER INDEX","ALTER LANGUAGE","ALTER LARGE OBJECT","ALTER MATERIALIZED VIEW","ALTER OPERATOR","ALTER OPERATOR CLASS","ALTER OPERATOR FAMILY","ALTER POLICY","ALTER PROCEDURE","ALTER PUBLICATION","ALTER ROLE","ALTER ROUTINE","ALTER RULE","ALTER SCHEMA","ALTER SEQUENCE","ALTER SERVER","ALTER STATISTICS","ALTER SUBSCRIPTION","ALTER SYSTEM","ALTER TABLE","ALTER TABLESPACE","ALTER TEXT SEARCH CONFIGURATION","ALTER TEXT SEARCH DICTIONARY","ALTER TEXT SEARCH PARSER","ALTER TEXT SEARCH TEMPLATE","ALTER TRIGGER","ALTER TYPE","ALTER USER","ALTER USER MAPPING","ALTER VIEW","ANALYZE","BEGIN","CALL","CHECKPOINT","CLOSE","CLUSTER","COMMENT","COMMIT","COMMIT PREPARED","COPY","CREATE ACCESS METHOD","CREATE AGGREGATE","CREATE CAST","CREATE COLLATION","CREATE CONVERSION","CREATE DATABASE","CREATE DOMAIN","CREATE EVENT TRIGGER","CREATE EXTENSION","CREATE FOREIGN DATA WRAPPER","CREATE FOREIGN TABLE","CREATE FUNCTION","CREATE GROUP","CREATE INDEX","CREATE LANGUAGE","CREATE MATERIALIZED VIEW","CREATE OPERATOR","CREATE OPERATOR CLASS","CREATE OPERATOR FAMILY","CREATE POLICY","CREATE PROCEDURE","CREATE PUBLICATION","CREATE ROLE","CREATE RULE","CREATE SCHEMA","CREATE SEQUENCE","CREATE SERVER","CREATE STATISTICS","CREATE SUBSCRIPTION","CREATE TABLE","CREATE TABLE AS","CREATE TABLESPACE","CREATE TEXT SEARCH CONFIGURATION","CREATE TEXT SEARCH DICTIONARY","CREATE TEXT SEARCH PARSER","CREATE TEXT SEARCH TEMPLATE","CREATE TRANSFORM","CREATE TRIGGER","CREATE TYPE","CREATE USER","CREATE USER MAPPING","CREATE VIEW","DEALLOCATE","DECLARE","DELETE","DELETE FROM","DISCARD","DO","DROP ACCESS METHOD","DROP AGGREGATE","DROP CAST","DROP COLLATION","DROP CONVERSION","DROP DATABASE","DROP DOMAIN","DROP EVENT TRIGGER","DROP EXTENSION","DROP FOREIGN DATA WRAPPER","DROP FOREIGN TABLE","DROP FUNCTION","DROP GROUP","DROP INDEX","DROP LANGUAGE","DROP MATERIALIZED VIEW","DROP OPERATOR","DROP OPERATOR CLASS","DROP OPERATOR FAMILY","DROP OWNED","DROP POLICY","DROP PROCEDURE","DROP PUBLICATION","DROP ROLE","DROP ROUTINE","DROP RULE","DROP SCHEMA","DROP SEQUENCE","DROP SERVER","DROP STATISTICS","DROP SUBSCRIPTION","DROP TABLE","DROP TABLESPACE","DROP TEXT SEARCH CONFIGURATION","DROP TEXT SEARCH DICTIONARY","DROP TEXT SEARCH PARSER","DROP TEXT SEARCH TEMPLATE","DROP TRANSFORM","DROP TRIGGER","DROP TYPE","DROP USER","DROP USER MAPPING","DROP VIEW","EXECUTE","EXPLAIN","FETCH","GRANT","IMPORT FOREIGN SCHEMA","INSERT","LISTEN","LOAD","LOCK","MOVE","NOTIFY","PREPARE","PREPARE TRANSACTION","REASSIGN OWNED","REFRESH MATERIALIZED VIEW","REINDEX","RELEASE SAVEPOINT","RESET","RETURNING","REVOKE","ROLLBACK","ROLLBACK PREPARED","ROLLBACK TO SAVEPOINT","SAVEPOINT","SECURITY LABEL","SELECT","SELECT INTO","SET","SET CONSTRAINTS","SET ROLE","SET SESSION AUTHORIZATION","SET TRANSACTION","SHOW","START TRANSACTION","TRUNCATE","UNLISTEN","UPDATE","VACUUM","VALUES","ADD","AFTER","ALTER COLUMN","INSERT INTO","SET SCHEMA","FROM","GROUP BY","HAVING","LIMIT","OFFSET","ORDER BY","WHERE","WITH","WINDOW","PARTITION BY"],ft=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT","MINUS","MINUS ALL","MINUS DISTINCT"],Ut=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],lt=["WHEN","ELSE"],pt=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&ut(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=Pt(e);if(R){var t=Pt(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return st(this,E)});function r(){return _t(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:Mt,reservedBinaryCommands:ft,reservedJoins:Ut,reservedDependentClauses:lt,reservedKeywords:_([].concat(ct,(E=Object.values(Dt).flat(),function(E){if(Array.isArray(E))return it(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return it(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?it(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()))),openParens:["(","["],closeParens:[")","]"],stringTypes:[{quote:"''",prefixes:["U&","E","X","B"]},"$$"],identTypes:[{quote:'""',prefixes:["U&"]}],identChars:{rest:"$"},numberedParamTypes:["$"],operators:r.operators});var E}}])&&at(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function Gt(E){return Gt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},Gt(E)}function yt(E){return function(E){if(Array.isArray(E))return ht(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return ht(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?ht(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ht(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function Bt(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function dt(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function vt(E,T){return vt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},vt(E,T)}function Ft(E,T){if(T&&("object"===Gt(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function Ht(E){return Ht=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},Ht(E)}pt.operators=["<<",">>","|/","||/","!!","||","~~","~~*","!~~","!~~*","~","~*","!~","!~*","<%","<<%","%>","%>>","~>~","~<~","~>=~","~<=~","@-@","@@","#","##","<->","&&","&<","&>","<<|","&<|","|>>","|&>","<^","^>","?#","?-","?|","?-|","?||","@>","<@","~=",">>=","<<=","@@@","?","@?","?&","->","->>","#>","#>>","#-",":=","::","=>","-|-"];var Yt={aggregate:["ANY_VALUE","APPROXIMATE PERCENTILE_DISC","AVG","COUNT","LISTAGG","MAX","MEDIAN","MIN","PERCENTILE_CONT","STDDEV_SAMP","STDDEV_POP","SUM","VAR_SAMP","VAR_POP"],array:["array","array_concat","array_flatten","get_array_length","split_to_array","subarray"],bitwise:["BIT_AND","BIT_OR","BOOL_AND","BOOL_OR"],conditional:["COALESCE","DECODE","GREATEST","LEAST","NVL","NVL2","NULLIF"],dateTime:["ADD_MONTHS","AT TIME ZONE","CONVERT_TIMEZONE","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","DATE_CMP","DATE_CMP_TIMESTAMP","DATE_CMP_TIMESTAMPTZ","DATE_PART_YEAR","DATEADD","DATEDIFF","DATE_PART","DATE_TRUNC","EXTRACT","GETDATE","INTERVAL_CMP","LAST_DAY","MONTHS_BETWEEN","NEXT_DAY","SYSDATE","TIMEOFDAY","TIMESTAMP_CMP","TIMESTAMP_CMP_DATE","TIMESTAMP_CMP_TIMESTAMPTZ","TIMESTAMPTZ_CMP","TIMESTAMPTZ_CMP_DATE","TIMESTAMPTZ_CMP_TIMESTAMP","TIMEZONE","TO_TIMESTAMP","TRUNC"],spatial:["AddBBox","DropBBox","GeometryType","ST_AddPoint","ST_Angle","ST_Area","ST_AsBinary","ST_AsEWKB","ST_AsEWKT","ST_AsGeoJSON","ST_AsText","ST_Azimuth","ST_Boundary","ST_Collect","ST_Contains","ST_ContainsProperly","ST_ConvexHull","ST_CoveredBy","ST_Covers","ST_Crosses","ST_Dimension","ST_Disjoint","ST_Distance","ST_DistanceSphere","ST_DWithin","ST_EndPoint","ST_Envelope","ST_Equals","ST_ExteriorRing","ST_Force2D","ST_Force3D","ST_Force3DM","ST_Force3DZ","ST_Force4D","ST_GeometryN","ST_GeometryType","ST_GeomFromEWKB","ST_GeomFromEWKT","ST_GeomFromText","ST_GeomFromWKB","ST_InteriorRingN","ST_Intersects","ST_IsPolygonCCW","ST_IsPolygonCW","ST_IsClosed","ST_IsCollection","ST_IsEmpty","ST_IsSimple","ST_IsValid","ST_Length","ST_LengthSphere","ST_Length2D","ST_LineFromMultiPoint","ST_LineInterpolatePoint","ST_M","ST_MakeEnvelope","ST_MakeLine","ST_MakePoint","ST_MakePolygon","ST_MemSize","ST_MMax","ST_MMin","ST_Multi","ST_NDims","ST_NPoints","ST_NRings","ST_NumGeometries","ST_NumInteriorRings","ST_NumPoints","ST_Perimeter","ST_Perimeter2D","ST_Point","ST_PointN","ST_Points","ST_Polygon","ST_RemovePoint","ST_Reverse","ST_SetPoint","ST_SetSRID","ST_Simplify","ST_SRID","ST_StartPoint","ST_Touches","ST_Within","ST_X","ST_XMax","ST_XMin","ST_Y","ST_YMax","ST_YMin","ST_Z","ST_ZMax","ST_ZMin","SupportsBBox"],hash:["CHECKSUM","FUNC_SHA1","FNV_HASH","MD5","SHA","SHA1","SHA2"],hyperLogLog:["HLL","HLL_CREATE_SKETCH","HLL_CARDINALITY","HLL_COMBINE"],json:["IS_VALID_JSON","IS_VALID_JSON_ARRAY","JSON_ARRAY_LENGTH","JSON_EXTRACT_ARRAY_ELEMENT_TEXT","JSON_EXTRACT_PATH_TEXT","JSON_PARSE","JSON_SERIALIZE"],math:["ABS","ACOS","ASIN","ATAN","ATAN2","CBRT","CEILING","CEIL","COS","COT","DEGREES","DEXP","DLOG1","DLOG10","EXP","FLOOR","LN","LOG","MOD","PI","POWER","RADIANS","RANDOM","ROUND","SIN","SIGN","SQRT","TAN","TO_HEX","TRUNC"],machineLearning:["EXPLAIN_MODEL"],string:["ASCII","BPCHARCMP","BTRIM","BTTEXT_PATTERN_CMP","CHAR_LENGTH","CHARACTER_LENGTH","CHARINDEX","CHR","COLLATE","CONCAT","CRC32","DIFFERENCE","INITCAP","LEFT","RIGHT","LEN","LENGTH","LOWER","LPAD","RPAD","LTRIM","OCTETINDEX","OCTET_LENGTH","POSITION","QUOTE_IDENT","QUOTE_LITERAL","REGEXP_COUNT","REGEXP_INSTR","REGEXP_REPLACE","REGEXP_SUBSTR","REPEAT","REPLACE","REPLICATE","REVERSE","RTRIM","SOUNDEX","SPLIT_PART","STRPOS","STRTOL","SUBSTRING","TEXTLEN","TRANSLATE","TRIM","UPPER"],superType:["decimal_precision","decimal_scale","is_array","is_bigint","is_boolean","is_char","is_decimal","is_float","is_integer","is_object","is_scalar","is_smallint","is_varchar","json_typeof"],window:["AVG","COUNT","CUME_DIST","DENSE_RANK","FIRST_VALUE","LAST_VALUE","LAG","LEAD","LISTAGG","MAX","MEDIAN","MIN","NTH_VALUE","NTILE","PERCENT_RANK","PERCENTILE_CONT","PERCENTILE_DISC","RANK","RATIO_TO_REPORT","ROW_NUMBER","STDDEV_SAMP","STDDEV_POP","SUM","VAR_SAMP","VAR_POP"],dataType:["CAST","CONVERT","TO_CHAR","TO_DATE","TO_NUMBER","TEXT_TO_INT_ALT","TEXT_TO_NUMERIC_ALT"],sysAdmin:["CHANGE_QUERY_PRIORITY","CHANGE_SESSION_PRIORITY","CHANGE_USER_PRIORITY","CURRENT_SETTING","PG_CANCEL_BACKEND","PG_TERMINATE_BACKEND","REBOOT_CLUSTER","SET_CONFIG"],sysInfo:["CURRENT_AWS_ACCOUNT","CURRENT_DATABASE","CURRENT_NAMESPACE","CURRENT_SCHEMA","CURRENT_SCHEMAS","CURRENT_USER","CURRENT_USER_ID","HAS_ASSUMEROLE_PRIVILEGE","HAS_DATABASE_PRIVILEGE","HAS_SCHEMA_PRIVILEGE","HAS_TABLE_PRIVILEGE","PG_BACKEND_PID","PG_GET_COLS","PG_GET_GRANTEE_BY_IAM_ROLE","PG_GET_IAM_ROLE_BY_USER","PG_GET_LATE_BINDING_VIEW_COLS","PG_LAST_COPY_COUNT","PG_LAST_COPY_ID","PG_LAST_UNLOAD_ID","PG_LAST_QUERY_ID","PG_LAST_UNLOAD_COUNT","SESSION_USER","SLICE_NUM","USER","VERSION"]},Vt={standard:["AES128","AES256","ALL","ALLOWOVERWRITE","ANY","ARRAY","AS","ASC","AUTHORIZATION","BACKUP","BETWEEN","BINARY","BOTH","CHECK","COLUMN","CONSTRAINT","CREATE","CROSS","DEFAULT","DEFERRABLE","DEFLATE","DEFRAG","DESC","DISABLE","DISTINCT","DO","ENABLE","ENCODE","ENCRYPT","ENCRYPTION","EXPLICIT","FALSE","FOR","FOREIGN","FREEZE","FROM","FULL","GLOBALDICT256","GLOBALDICT64K","GROUP","IDENTITY","IGNORE","ILIKE","IN","INITIALLY","INNER","INTO","IS","ISNULL","LANGUAGE","LEADING","LIKE","LIMIT","LOCALTIME","LOCALTIMESTAMP","LUN","LUNS","MINUS","NATURAL","NEW","NOT","NOTNULL","NULL","NULLS","OFF","OFFLINE","OFFSET","OID","OLD","ONLY","OPEN","ORDER","OUTER","OVERLAPS","PARALLEL","PARTITION","PERCENT","PERMISSIONS","PLACING","PRIMARY","RECOVER","REFERENCES","REJECTLOG","RESORT","RESPECT","RESTORE","SIMILAR","SNAPSHOT","SOME","SYSTEM","TABLE","TAG","TDES","THEN","TIMESTAMP","TO","TOP","TRAILING","TRUE","UNIQUE","VERBOSE","WALLET","WITHOUT"],dataConversionParams:["ACCEPTANYDATE","ACCEPTINVCHARS","BLANKSASNULL","DATEFORMAT","EMPTYASNULL","ENCODING","ESCAPE","EXPLICIT_IDS","FILLRECORD","IGNOREBLANKLINES","IGNOREHEADER","NULL AS","REMOVEQUOTES","ROUNDEC","TIMEFORMAT","TRIMBLANKS","TRUNCATECOLUMNS"],dataLoadParams:["COMPROWS","COMPUPDATE","MAXERROR","NOLOAD","STATUPDATE"],dataFormatParams:["FORMAT","CSV","DELIMITER","FIXEDWIDTH","SHAPEFILE","AVRO","JSON","PARQUET","ORC"],copyAuthParams:["ACCESS_KEY_ID","CREDENTIALS","ENCRYPTED","IAM_ROLE","MASTER_SYMMETRIC_KEY","SECRET_ACCESS_KEY","SESSION_TOKEN"],copyCompressionParams:["BZIP2","GZIP","LZOP","ZSTD"],copyMiscParams:["MANIFEST","READRATIO","REGION","SSH"],compressionEncodings:["RAW","AZ64","BYTEDICT","DELTA","DELTA32K","LZO","MOSTLY8","MOSTLY16","MOSTLY32","RUNLENGTH","TEXT255","TEXT32K"],misc:["CATALOG_ROLE","SECRET_ARN","EXTERNAL","HIVE METASTORE","AUTO","EVEN","KEY","PREDICATE","COMPRESSION","DATA CATALOG"],dataTypes:["CHAR","CHARACTER","NCHAR","VARCHAR","CHARACTER VARYING","NVARCHAR","BPCHAR","TEXT"]},bt=["ABORT","ALTER DATABASE","ALTER DATASHARE","ALTER DEFAULT PRIVILEGES","ALTER GROUP","ALTER MATERIALIZED VIEW","ALTER PROCEDURE","ALTER SCHEMA","ALTER TABLE","ALTER TABLE APPEND","ALTER USER","ANALYSE","ANALYZE","ANALYSE COMPRESSION","ANALYZE COMPRESSION","BEGIN","CALL","CANCEL","CLOSE","COMMENT","COMMIT","COPY","CREATE DATABASE","CREATE DATASHARE","CREATE EXTERNAL FUNCTION","CREATE EXTERNAL SCHEMA","CREATE EXTERNAL TABLE","CREATE FUNCTION","CREATE GROUP","CREATE LIBRARY","CREATE MATERIALIZED VIEW","CREATE MODEL","CREATE PROCEDURE","CREATE SCHEMA","CREATE TABLE","CREATE TABLE AS","CREATE USER","CREATE VIEW","DEALLOCATE","DECLARE","DELETE","DELETE FROM","DESC DATASHARE","DROP DATABASE","DROP DATASHARE","DROP FUNCTION","DROP GROUP","DROP LIBRARY","DROP MODEL","DROP MATERIALIZED VIEW","DROP PROCEDURE","DROP SCHEMA","DROP TABLE","DROP USER","DROP VIEW","DROP","EXECUTE","EXPLAIN","FETCH","FROM","GRANT","HAVING","INSERT","LOCK","PREPARE","REFRESH MATERIALIZED VIEW","RESET","REVOKE","ROLLBACK","SELECT","SELECT INTO","SET","SET SESSION AUTHORIZATION","SET SESSION CHARACTERISTICS","SHOW","SHOW EXTERNAL TABLE","SHOW MODEL","SHOW DATASHARES","SHOW PROCEDURE","SHOW TABLE","SHOW VIEW","START TRANSACTION","TRUNCATE","UNLOAD","UPDATE","VACUUM","WHERE","WITH","GROUP BY","ORDER BY","LIMIT","OFFSET","VALUES","MODIFY","INSERT INTO","ALTER COLUMN","SET SCHEMA"],mt=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT"],gt=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],Xt=["WHEN","ELSE"],Wt=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&vt(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=Ht(e);if(R){var t=Ht(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return Ft(this,E)});function r(){return Bt(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:bt,reservedBinaryCommands:mt,reservedJoins:gt,reservedDependentClauses:Xt,reservedKeywords:_([].concat(yt(Object.values(Vt).flat()),yt(Object.values(Yt).flat()))),stringTypes:["''"],identTypes:['""'],numberedParamTypes:["$"],operators:r.operators})}}])&&dt(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function Kt(E){return Kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},Kt(E)}function wt(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function xt(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function Jt(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function kt(E,T){return kt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},kt(E,T)}function jt(E,T){if(T&&("object"===Kt(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function Qt(E){return Qt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},Qt(E)}Wt.operators=["~","|/","||/","<<",">>","||"];var Zt={aggregate:["ANY","APPROX_COUNT_DISTINCT","APPROX_PERCENTILE","AVG","BIT_AND","BIT_OR","BIT_XOR","BOOL_AND","BOOL_OR","COLLECT_LIST","COLLECT_SET","CORR","COUNT","COUNT","COUNT","COUNT_IF","COUNT_MIN_SKETCH","COVAR_POP","COVAR_SAMP","EVERY","FIRST","FIRST_VALUE","GROUPING","GROUPING_ID","KURTOSIS","LAST","LAST_VALUE","MAX","MAX_BY","MEAN","MIN","MIN_BY","PERCENTILE","PERCENTILE","PERCENTILE_APPROX","SKEWNESS","SOME","STD","STDDEV","STDDEV_POP","STDDEV_SAMP","SUM","VAR_POP","VAR_SAMP","VARIANCE"],window:["CUME_DIST","DENSE_RANK","LAG","LEAD","NTH_VALUE","NTILE","PERCENT_RANK","RANK","ROW_NUMBER"],array:["ARRAY","ARRAY_CONTAINS","ARRAY_DISTINCT","ARRAY_EXCEPT","ARRAY_INTERSECT","ARRAY_JOIN","ARRAY_MAX","ARRAY_MIN","ARRAY_POSITION","ARRAY_REMOVE","ARRAY_REPEAT","ARRAY_UNION","ARRAYS_OVERLAP","ARRAYS_ZIP","FLATTEN","SEQUENCE","SHUFFLE","SLICE","SORT_ARRAY"],map:["ELEMENT_AT","ELEMENT_AT","MAP","MAP_CONCAT","MAP_ENTRIES","MAP_FROM_ARRAYS","MAP_FROM_ENTRIES","MAP_KEYS","MAP_VALUES","STR_TO_MAP"],datetime:["ADD_MONTHS","CURRENT_DATE","CURRENT_DATE","CURRENT_TIMESTAMP","CURRENT_TIMESTAMP","CURRENT_TIMEZONE","DATE_ADD","DATE_FORMAT","DATE_FROM_UNIX_DATE","DATE_PART","DATE_SUB","DATE_TRUNC","DATEDIFF","DAY","DAYOFMONTH","DAYOFWEEK","DAYOFYEAR","EXTRACT","FROM_UNIXTIME","FROM_UTC_TIMESTAMP","HOUR","LAST_DAY","MAKE_DATE","MAKE_DT_INTERVAL","MAKE_INTERVAL","MAKE_TIMESTAMP","MAKE_YM_INTERVAL","MINUTE","MONTH","MONTHS_BETWEEN","NEXT_DAY","NOW","QUARTER","SECOND","SESSION_WINDOW","TIMESTAMP_MICROS","TIMESTAMP_MILLIS","TIMESTAMP_SECONDS","TO_DATE","TO_TIMESTAMP","TO_UNIX_TIMESTAMP","TO_UTC_TIMESTAMP","TRUNC","UNIX_DATE","UNIX_MICROS","UNIX_MILLIS","UNIX_SECONDS","UNIX_TIMESTAMP","WEEKDAY","WEEKOFYEAR","WINDOW","YEAR"],json:["FROM_JSON","GET_JSON_OBJECT","JSON_ARRAY_LENGTH","JSON_OBJECT_KEYS","JSON_TUPLE","SCHEMA_OF_JSON","TO_JSON"],misc:["ABS","ACOS","ACOSH","AGGREGATE","ARRAY_SORT","ASCII","ASIN","ASINH","ASSERT_TRUE","ATAN","ATAN2","ATANH","BASE64","BIGINT","BIN","BINARY","BIT_COUNT","BIT_GET","BIT_LENGTH","BOOLEAN","BROUND","BTRIM","CARDINALITY","CBRT","CEIL","CEILING","CHAR","CHAR_LENGTH","CHARACTER_LENGTH","CHR","CONCAT","CONCAT_WS","CONV","COS","COSH","COT","CRC32","CURRENT_CATALOG","CURRENT_DATABASE","CURRENT_USER","DATE","DECIMAL","DEGREES","DOUBLE","ELT","EXP","EXPM1","FACTORIAL","FIND_IN_SET","FLOAT","FLOOR","FORALL","FORMAT_NUMBER","FORMAT_STRING","FROM_CSV","GETBIT","HASH","HEX","HYPOT","INITCAP","INLINE","INLINE_OUTER","INPUT_FILE_BLOCK_LENGTH","INPUT_FILE_BLOCK_START","INPUT_FILE_NAME","INSTR","INT","ISNAN","ISNOTNULL","ISNULL","JAVA_METHOD","LCASE","LEFT","LENGTH","LEVENSHTEIN","LN","LOCATE","LOG","LOG10","LOG1P","LOG2","LOWER","LPAD","LTRIM","MAP_FILTER","MAP_ZIP_WITH","MD5","MOD","MONOTONICALLY_INCREASING_ID","NAMED_STRUCT","NANVL","NEGATIVE","NVL","NVL2","OCTET_LENGTH","OVERLAY","PARSE_URL","PI","PMOD","POSEXPLODE","POSEXPLODE_OUTER","POSITION","POSITIVE","POW","POWER","PRINTF","RADIANS","RAISE_ERROR","RAND","RANDN","RANDOM","REFLECT","REGEXP_EXTRACT","REGEXP_EXTRACT_ALL","REGEXP_LIKE","REGEXP_REPLACE","REPEAT","REPLACE","REVERSE","RIGHT","RINT","ROUND","RPAD","RTRIM","SCHEMA_OF_CSV","SENTENCES","SHA","SHA1","SHA2","SHIFTLEFT","SHIFTRIGHT","SHIFTRIGHTUNSIGNED","SIGN","SIGNUM","SIN","SINH","SMALLINT","SOUNDEX","SPACE","SPARK_PARTITION_ID","SPLIT","SQRT","STACK","SUBSTR","SUBSTRING","SUBSTRING_INDEX","TAN","TANH","TIMESTAMP","TINYINT","TO_CSV","TRANSFORM_KEYS","TRANSFORM_VALUES","TRANSLATE","TRIM","TRY_ADD","TRY_DIVIDE","TYPEOF","UCASE","UNBASE64","UNHEX","UPPER","UUID","VERSION","WIDTH_BUCKET","XPATH","XPATH_BOOLEAN","XPATH_DOUBLE","XPATH_FLOAT","XPATH_INT","XPATH_LONG","XPATH_NUMBER","XPATH_SHORT","XPATH_STRING","XXHASH64","ZIP_WITH"]},$t=["ADD","AFTER","ALL","ALTER","ANALYZE","ANTI","ANY","ARCHIVE","ARRAY","AS","ASC","AT","AUTHORIZATION","BETWEEN","BOTH","BUCKET","BUCKETS","BY","CACHE","CASCADE","CAST","CHANGE","CHECK","CLEAR","CLUSTER","CLUSTERED","CODEGEN","COLLATE","COLLECTION","COLUMN","COLUMNS","COMMENT","COMMIT","COMPACT","COMPACTIONS","COMPUTE","CONCATENATE","CONSTRAINT","COST","CREATE","CROSS","CUBE","CURRENT","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","DATA","DATABASE","DATABASES","DAY","DBPROPERTIES","DEFINED","DELETE","DELIMITED","DESC","DESCRIBE","DFS","DIRECTORIES","DIRECTORY","DISTINCT","DISTRIBUTE","DIV","DROP","ESCAPE","ESCAPED","EXCEPT","EXCHANGE","EXISTS","EXPORT","EXTENDED","EXTERNAL","EXTRACT","FALSE","FETCH","FIELDS","FILTER","FILEFORMAT","FIRST","FIRST_VALUE","FOLLOWING","FOR","FOREIGN","FORMAT","FORMATTED","FULL","FUNCTION","FUNCTIONS","GLOBAL","GRANT","GROUP","GROUPING","HOUR","IF","IGNORE","IMPORT","IN","INDEX","INDEXES","INNER","INPATH","INPUTFORMAT","INTERSECT","INTERVAL","INTO","IS","ITEMS","KEYS","LAST","LAST_VALUE","LATERAL","LAZY","LEADING","LEFT","LIKE","LINES","LIST","LOCAL","LOCATION","LOCK","LOCKS","LOGICAL","MACRO","MAP","MATCHED","MERGE","MINUTE","MONTH","MSCK","NAMESPACE","NAMESPACES","NATURAL","NO","NOT","NULL","NULLS","OF","ONLY","OPTION","OPTIONS","ORDER","OUT","OUTER","OUTPUTFORMAT","OVER","OVERLAPS","OVERLAY","OVERWRITE","OWNER","PARTITION","PARTITIONED","PARTITIONS","PERCENT","PLACING","POSITION","PRECEDING","PRIMARY","PRINCIPALS","PROPERTIES","PURGE","QUERY","RANGE","RECORDREADER","RECORDWRITER","RECOVER","REDUCE","REFERENCES","RENAME","REPAIR","REPLACE","RESPECT","RESTRICT","REVOKE","RIGHT","RLIKE","ROLE","ROLES","ROLLBACK","ROLLUP","ROW","ROWS","SCHEMA","SECOND","SELECT","SEMI","SEPARATED","SERDE","SERDEPROPERTIES","SESSION_USER","SETS","SHOW","SKEWED","SOME","SORT","SORTED","START","STATISTICS","STORED","STRATIFY","STRUCT","SUBSTR","SUBSTRING","TABLE","TABLES","TBLPROPERTIES","TEMPORARY","TERMINATED","THEN","TO","TOUCH","TRAILING","TRANSACTION","TRANSACTIONS","TRIM","TRUE","TRUNCATE","UNARCHIVE","UNBOUNDED","UNCACHE","UNIQUE","UNKNOWN","UNLOCK","UNSET","USE","USER","VIEW","WINDOW","YEAR","ANALYSE","ARRAY_ZIP","COALESCE","CONTAINS","CONVERT","CURRENT ROW","DAYS","DAY_HOUR","DAY_MINUTE","DAY_SECOND","DECODE","DEFAULT","DISTINCTROW","ENCODE","EXPLODE","EXPLODE_OUTER","FIXED","GREATEST","GROUP_CONCAT","HOURS","HOUR_MINUTE","HOUR_SECOND","IFNULL","LEAST","LEVEL","MINUTE_SECOND","NULLIF","OFFSET","ON DELETE","ON UPDATE","OPTIMIZE","REGEXP","SEPARATOR","SIZE","STRING","TYPE","TYPES","UNSIGNED","VARIABLES","YEAR_MONTH"],qt=["ALTER COLUMN","ALTER DATABASE","ALTER TABLE","ALTER VIEW","CREATE DATABASE","CREATE FUNCTION","CREATE TABLE","CREATE VIEW","DROP DATABASE","DROP FUNCTION","DROP TABLE","DROP VIEW","REPAIR TABLE","TRUNCATE TABLE","USE DATABASE","INSERT INTO","INSERT OVERWRITE","INSERT OVERWRITE DIRECTORY","LOAD","SELECT","WITH","CLUSTER BY","DISTRIBUTE BY","GROUP BY","HAVING","VALUES","LIMIT","OFFSET","ORDER BY","SORT BY","TABLESAMPLE","WHERE","PIVOT","TRANSFORM","EXPLAIN","ADD FILE","ADD JAR","ANALYZE TABLE","CACHE TABLE","CLEAR CACHE","DESCRIBE DATABASE","DESCRIBE FUNCTION","DESCRIBE QUERY","DESCRIBE TABLE","LIST FILE","LIST JAR","REFRESH","REFRESH TABLE","REFRESH FUNCTION","RESET","SET","SET SCHEMA","SHOW COLUMNS","SHOW CREATE TABLE","SHOW DATABASES","SHOW FUNCTIONS","SHOW PARTITIONS","SHOW TABLE EXTENDED","SHOW TABLES","SHOW TBLPROPERTIES","SHOW VIEWS","UNCACHE TABLE","FROM","INSERT","LATERAL VIEW","UPDATE","WINDOW"],zt=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT","MINUS","MINUS ALL","MINUS DISTINCT","CROSS APPLY","OUTER APPLY"],Ee=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN","ANTI JOIN","SEMI JOIN","LEFT ANTI JOIN","LEFT SEMI JOIN","RIGHT OUTER JOIN","RIGHT SEMI JOIN","NATURAL ANTI JOIN","NATURAL FULL OUTER JOIN","NATURAL INNER JOIN","NATURAL LEFT ANTI JOIN","NATURAL LEFT OUTER JOIN","NATURAL LEFT SEMI JOIN","NATURAL OUTER JOIN","NATURAL RIGHT OUTER JOIN","NATURAL RIGHT SEMI JOIN","NATURAL SEMI JOIN"],Te=["WHEN","ELSE"],te=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&kt(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=Qt(e);if(R){var t=Qt(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return jt(this,E)});function r(){return xt(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:qt,reservedBinaryCommands:zt,reservedJoins:Ee,reservedDependentClauses:Te,reservedLogicalOperators:["AND","OR","XOR"],reservedKeywords:_([].concat($t,(E=Object.values(Zt).flat(),function(E){if(Array.isArray(E))return wt(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return wt(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?wt(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()))),openParens:["(","["],closeParens:[")","]"],stringTypes:[{quote:"''",prefixes:["X"]}],identTypes:["``"],variableTypes:[{quote:"{}",prefixes:["$"],requirePrefix:!0}],operators:r.operators,postProcess:ee});var E}}])&&Jt(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function ee(E){return E.map((function(T,t){var e=E[t-1]||n,R=E[t+1]||n;return I.WINDOW(T)&&R.type===A.OPEN_PAREN?Object.assign(Object.assign({},T),{type:A.RESERVED_KEYWORD}):"ITEMS"!==T.value||T.type!==A.RESERVED_KEYWORD||"COLLECTION"===e.value&&"TERMINATED"===R.value?T:{type:A.IDENTIFIER,text:T.text,value:T.text}}))}function Re(E){return Re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},Re(E)}function Ae(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function re(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function Se(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function ne(E,T){return ne=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},ne(E,T)}function Oe(E,T){if(T&&("object"===Re(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function Ie(E){return Ie=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},Ie(E)}te.operators=["~","<=>","&&","||","==","->"];var Ne={scalar:["ABS","CHANGES","CHAR","COALESCE","FORMAT","GLOB","HEX","IFNULL","IIF","INSTR","LAST_INSERT_ROWID","LENGTH","LIKE","LIKELIHOOD","LIKELY","LOAD_EXTENSION","LOWER","LTRIM","NULLIF","PRINTF","QUOTE","RANDOM","RANDOMBLOB","REPLACE","ROUND","RTRIM","SIGN","SOUNDEX","SQLITE_COMPILEOPTION_GET","SQLITE_COMPILEOPTION_USED","SQLITE_OFFSET","SQLITE_SOURCE_ID","SQLITE_VERSION","SUBSTR","SUBSTRING","TOTAL_CHANGES","TRIM","TYPEOF","UNICODE","UNLIKELY","UPPER","ZEROBLOB"],aggregate:["AVG","COUNT","GROUP_CONCAT","MAX","MIN","SUM","TOTAL"],datetime:["DATE","TIME","DATETIME","JULIANDAY","UNIXEPOCH","STRFTIME"]},oe=["ABORT","ACTION","ADD","AFTER","ALL","ALTER","ANY","ARE","ARRAY","ALWAYS","ANALYZE","AS","ASC","ATTACH","AUTOINCREMENT","BEFORE","BEGIN","BETWEEN","BY","CASCADE","CASE","CAST","CHECK","COLLATE","COLUMN","COMMIT","CONFLICT","CONSTRAINT","CREATE","CROSS","CURRENT","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","DATABASE","DEFAULT","DEFERRABLE","DEFERRED","DELETE","DESC","DETACH","DISTINCT","DO","DROP","EACH","ELSE","END","ESCAPE","EXCEPT","EXCLUDE","EXCLUSIVE","EXISTS","EXPLAIN","FAIL","FILTER","FIRST","FOLLOWING","FOR","FOREIGN","FROM","FULL","GENERATED","GLOB","GROUP","GROUPS","HAVING","IF","IGNORE","IMMEDIATE","IN","INDEX","INDEXED","INITIALLY","INNER","INSERT","INSTEAD","INTERSECT","INTO","IS","ISNULL","JOIN","KEY","LAST","LEFT","LIKE","LIMIT","MATCH","MATERIALIZED","NATURAL","NO","NOT","NOTHING","NOTNULL","NULL","NULLS","OF","OFFSET","ON DELETE","ON UPDATE","ONLY","OPEN","ORDER","OTHERS","OUTER","OVER","PARTITION","PLAN","PRAGMA","PRECEDING","PRIMARY","QUERY","RAISE","RANGE","RECURSIVE","REFERENCES","REGEXP","REINDEX","RELEASE","RENAME","REPLACE","RESTRICT","RETURNING","RIGHT","ROLLBACK","ROW","ROWS","SAVEPOINT","SELECT","SET","TABLE","TEMP","TEMPORARY","THEN","TIES","TO","TRANSACTION","TRIGGER","UNBOUNDED","UNION","UNIQUE","UPDATE","USING","VACUUM","VALUES","VIEW","VIRTUAL","WHEN","WHERE","WINDOW","WITH","WITHOUT"],Ce=["ADD","ALTER COLUMN","ALTER TABLE","CREATE TABLE","DROP TABLE","DELETE","DELETE FROM","FETCH FIRST","FETCH NEXT","FETCH PRIOR","FETCH LAST","FETCH ABSOLUTE","FETCH RELATIVE","FROM","GROUP BY","HAVING","INSERT INTO","LIMIT","OFFSET","ORDER BY","SELECT","SET SCHEMA","SET","UPDATE","VALUES","WHERE","WITH","WINDOW","PARTITION BY"],Le=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT"],ie=["JOIN","LEFT JOIN","LEFT OUTER JOIN","INNER JOIN","CROSS JOIN","NATURAL JOIN","NATURAL LEFT JOIN","NATURAL LEFT OUTER JOIN","NATURAL INNER JOIN","NATURAL CROSS JOIN"],_e=["WHEN","ELSE"],ae=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&ne(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=Ie(e);if(R){var t=Ie(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return Oe(this,E)});function r(){return re(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:Ce,reservedBinaryCommands:Le,reservedJoins:ie,reservedDependentClauses:_e,reservedKeywords:_([].concat(oe,(E=Object.values(Ne).flat(),function(E){if(Array.isArray(E))return Ae(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return Ae(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Ae(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()))),stringTypes:[{quote:"''",prefixes:["X"]}],identTypes:['""',"``","[]"],positionalParams:!0,numberedParamTypes:["?"],namedParamTypes:[":","@","$"],operators:r.operators});var E}}])&&Se(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function ue(E){return ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},ue(E)}function se(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function Pe(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function De(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function ce(E,T){return ce=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},ce(E,T)}function Me(E,T){if(T&&("object"===ue(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function fe(E){return fe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},fe(E)}ae.operators=["~","->","->>","||","<<",">>","=="];var Ue={set:["GROUPING"],window:["RANK","DENSE_RANK","PERCENT_RANK","CUME_DIST","ROW_NUMBER"],numeric:["POSITION","OCCURRENCES_REGEX","POSITION_REGEX","EXTRACT","CHAR_LENGTH","CHARACTER_LENGTH","OCTET_LENGTH","CARDINALITY","ABS","MOD","LN","EXP","POWER","SQRT","FLOOR","CEIL","CEILING","WIDTH_BUCKET"],string:["SUBSTRING","SUBSTRING_REGEX","UPPER","LOWER","CONVERT","TRANSLATE","TRANSLATE_REGEX","TRIM","OVERLAY","NORMALIZE","SPECIFICTYPE"],datetime:["CURRENT_DATE","CURRENT_TIME","LOCALTIME","CURRENT_TIMESTAMP","LOCALTIMESTAMP"],aggregate:["COUNT","AVG","MAX","MIN","SUM","EVERY","ANY","SOME","STDDEV_POP","STDDEV_SAMP","VAR_SAMP","VAR_POP","COLLECT","FUSION","INTERSECTION","COVAR_POP","COVAR_SAMP","CORR","REGR_SLOPE","REGR_INTERCEPT","REGR_COUNT","REGR_R2","REGR_AVGX","REGR_AVGY","REGR_SXX","REGR_SYY","REGR_SXY","PERCENTILE_CONT","PERCENTILE_DISC"]},le=["ALL","ALLOCATE","ALTER","ARE","ARRAY","AS","ASENSITIVE","ASYMMETRIC","AT","ATOMIC","AUTHORIZATION","BEGIN","BETWEEN","BIGINT","BINARY","BLOB","BOOLEAN","BOTH","BY","CALL","CALLED","CASCADED","CAST","CHAR","CHARACTER","CHECK","CLOB","CLOSE","COALESCE","COLLATE","COLUMN","COMMIT","CONDITION","CONNECT","CONSTRAINT","CORRESPONDING","CREATE","CROSS","CUBE","CURRENT","CURRENT_CATALOG","CURRENT_DEFAULT_TRANSFORM_GROUP","CURRENT_PATH","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_TRANSFORM_GROUP_FOR_TYPE","CURRENT_USER","CURSOR","CYCLE","DATE","DAY","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DELETE","DEREF","DESCRIBE","DETERMINISTIC","DISCONNECT","DISTINCT","DOUBLE","DROP","DYNAMIC","EACH","ELEMENT","END-EXEC","ESCAPE","EXCEPT","EXEC","EXECUTE","EXISTS","EXTERNAL","FALSE","FETCH","FILTER","FLOAT","FOR","FOREIGN","FREE","FROM","FULL","FUNCTION","GET","GLOBAL","GRANT","GROUP","HAVING","HOLD","HOUR","IDENTITY","IN","INDICATOR","INNER","INOUT","INSENSITIVE","INSERT","INT","INTEGER","INTERSECT","INTERVAL","INTO","IS","LANGUAGE","LARGE","LATERAL","LEADING","LEFT","LIKE","LIKE_REGEX","LOCAL","MATCH","MEMBER","MERGE","METHOD","MINUTE","MODIFIES","MODULE","MONTH","MULTISET","NATIONAL","NATURAL","NCHAR","NCLOB","NEW","NO","NONE","NOT","NULL","NULLIF","NUMERIC","OF","OLD","ON DELETE","ON UPDATE","ONLY","OPEN","ORDER","OUT","OUTER","OVER","OVERLAPS","PARAMETER","PARTITION","PRECISION","PREPARE","PRIMARY","PROCEDURE","RANGE","READS","REAL","RECURSIVE","REF","REFERENCES","REFERENCING","RELEASE","RESULT","RETURN","RETURNS","REVOKE","RIGHT","ROLLBACK","ROLLUP","ROW","ROWS","SAVEPOINT","SCOPE","SCROLL","SEARCH","SECOND","SELECT","SENSITIVE","SESSION_USER","SET","SIMILAR","SMALLINT","SPECIFIC","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","START","STATIC","SUBMULTISET","SYMMETRIC","SYSTEM","SYSTEM_USER","TABLE","TABLESAMPLE","THEN","TIME","TIMESTAMP","TIMEZONE_HOUR","TIMEZONE_MINUTE","TO","TRAILING","TRANSLATION","TREAT","TRIGGER","TRUE","UESCAPE","UNION","UNIQUE","UNKNOWN","UNNEST","UPDATE","USER","VALUE","VALUES","VARBINARY","VARCHAR","VARYING","WHENEVER","WINDOW","WITHIN","WITHOUT","YEAR"],pe=["ADD","ALTER COLUMN","ALTER TABLE","CREATE TABLE","DROP TABLE","DELETE FROM","FETCH FIRST","FETCH NEXT","FETCH PRIOR","FETCH LAST","FETCH ABSOLUTE","FETCH RELATIVE","FROM","GROUP BY","HAVING","INSERT INTO","LIMIT","OFFSET","ORDER BY","SELECT","SET SCHEMA","SET","UPDATE","VALUES","WHERE","WITH","WINDOW","PARTITION BY"],Ge=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT"],ye=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],he=["WHEN","ELSE"],Be=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&ce(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=fe(e);if(R){var t=fe(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return Me(this,E)});function r(){return Pe(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:pe,reservedBinaryCommands:Ge,reservedJoins:ye,reservedDependentClauses:he,reservedKeywords:_([].concat(le,(E=Object.values(Ue).flat(),function(E){if(Array.isArray(E))return se(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return se(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?se(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()))),stringTypes:[{quote:"''",prefixes:["X"]}],identTypes:['""',"``"],positionalParams:!0});var E}}])&&De(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function de(E){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},de(E)}function ve(E){return function(E){if(Array.isArray(E))return Fe(E)}(E)||function(E){if("undefined"!=typeof Symbol&&null!=E[Symbol.iterator]||null!=E["@@iterator"])return Array.from(E)}(E)||function(E,T){if(E){if("string"==typeof E)return Fe(E,T);var t=Object.prototype.toString.call(E).slice(8,-1);return"Object"===t&&E.constructor&&(t=E.constructor.name),"Map"===t||"Set"===t?Array.from(E):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Fe(E,T):void 0}}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fe(E,T){(null==T||T>E.length)&&(T=E.length);for(var t=0,e=new Array(T);t<T;t++)e[t]=E[t];return e}function He(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function Ye(E,T){for(var t=0;t<T.length;t++){var e=T[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(E,e.key,e)}}function Ve(E,T){return Ve=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},Ve(E,T)}function be(E,T){if(T&&("object"===de(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function me(E){return me=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},me(E)}Be.operators=[];var ge={aggregate:["APPROX_COUNT_DISTINCT","AVG","CHECKSUM_AGG","COUNT","COUNT_BIG","GROUPING","GROUPING_ID","MAX","MIN","STDEV","STDEVP","SUM","VAR","VARP"],analytic:["CUME_DIST","FIRST_VALUE","LAG","LAST_VALUE","LEAD","PERCENTILE_CONT","PERCENTILE_DISC","PERCENT_RANK","Collation - COLLATIONPROPERTY","Collation - TERTIARY_WEIGHTS"],configuration:["@@DBTS","@@LANGID","@@LANGUAGE","@@LOCK_TIMEOUT","@@MAX_CONNECTIONS","@@MAX_PRECISION","@@NESTLEVEL","@@OPTIONS","@@REMSERVER","@@SERVERNAME","@@SERVICENAME","@@SPID","@@TEXTSIZE","@@VERSION"],conversion:["CAST","CONVERT","PARSE","TRY_CAST","TRY_CONVERT","TRY_PARSE"],cryptographic:["ASYMKEY_ID","ASYMKEYPROPERTY","CERTPROPERTY","CERT_ID","CRYPT_GEN_RANDOM","DECRYPTBYASYMKEY","DECRYPTBYCERT","DECRYPTBYKEY","DECRYPTBYKEYAUTOASYMKEY","DECRYPTBYKEYAUTOCERT","DECRYPTBYPASSPHRASE","ENCRYPTBYASYMKEY","ENCRYPTBYCERT","ENCRYPTBYKEY","ENCRYPTBYPASSPHRASE","HASHBYTES","IS_OBJECTSIGNED","KEY_GUID","KEY_ID","KEY_NAME","SIGNBYASYMKEY","SIGNBYCERT","SYMKEYPROPERTY","VERIFYSIGNEDBYCERT","VERIFYSIGNEDBYASYMKEY"],cursor:["@@CURSOR_ROWS","@@FETCH_STATUS","CURSOR_STATUS"],dataType:["DATALENGTH","IDENT_CURRENT","IDENT_INCR","IDENT_SEED","IDENTITY","SQL_VARIANT_PROPERTY"],datetime:["@@DATEFIRST","CURRENT_TIMESTAMP","CURRENT_TIMEZONE","CURRENT_TIMEZONE_ID","DATEADD","DATEDIFF","DATEDIFF_BIG","DATEFROMPARTS","DATENAME","DATEPART","DATETIME2FROMPARTS","DATETIMEFROMPARTS","DATETIMEOFFSETFROMPARTS","DAY","EOMONTH","GETDATE","GETUTCDATE","ISDATE","MONTH","SMALLDATETIMEFROMPARTS","SWITCHOFFSET","SYSDATETIME","SYSDATETIMEOFFSET","SYSUTCDATETIME","TIMEFROMPARTS","TODATETIMEOFFSET","YEAR","JSON","ISJSON","JSON_VALUE","JSON_QUERY","JSON_MODIFY"],mathematical:["ABS","ACOS","ASIN","ATAN","ATN2","CEILING","COS","COT","DEGREES","EXP","FLOOR","LOG","LOG10","PI","POWER","RADIANS","RAND","ROUND","SIGN","SIN","SQRT","SQUARE","TAN","CHOOSE","GREATEST","IIF","LEAST"],metadata:["@@PROCID","APP_NAME","APPLOCK_MODE","APPLOCK_TEST","ASSEMBLYPROPERTY","COL_LENGTH","COL_NAME","COLUMNPROPERTY","DATABASEPROPERTYEX","DB_ID","DB_NAME","FILE_ID","FILE_IDEX","FILE_NAME","FILEGROUP_ID","FILEGROUP_NAME","FILEGROUPPROPERTY","FILEPROPERTY","FILEPROPERTYEX","FULLTEXTCATALOGPROPERTY","FULLTEXTSERVICEPROPERTY","INDEX_COL","INDEXKEY_PROPERTY","INDEXPROPERTY","NEXT VALUE FOR","OBJECT_DEFINITION","OBJECT_ID","OBJECT_NAME","OBJECT_SCHEMA_NAME","OBJECTPROPERTY","OBJECTPROPERTYEX","ORIGINAL_DB_NAME","PARSENAME","SCHEMA_ID","SCHEMA_NAME","SCOPE_IDENTITY","SERVERPROPERTY","STATS_DATE","TYPE_ID","TYPE_NAME","TYPEPROPERTY"],ranking:["DENSE_RANK","NTILE","RANK","ROW_NUMBER","PUBLISHINGSERVERNAME"],security:["CERTENCODED","CERTPRIVATEKEY","CURRENT_USER","DATABASE_PRINCIPAL_ID","HAS_DBACCESS","HAS_PERMS_BY_NAME","IS_MEMBER","IS_ROLEMEMBER","IS_SRVROLEMEMBER","LOGINPROPERTY","ORIGINAL_LOGIN","PERMISSIONS","PWDENCRYPT","PWDCOMPARE","SESSION_USER","SESSIONPROPERTY","SUSER_ID","SUSER_NAME","SUSER_SID","SUSER_SNAME","SYSTEM_USER","USER","USER_ID","USER_NAME"],string:["ASCII","CHAR","CHARINDEX","CONCAT","CONCAT_WS","DIFFERENCE","FORMAT","LEFT","LEN","LOWER","LTRIM","NCHAR","PATINDEX","QUOTENAME","REPLACE","REPLICATE","REVERSE","RIGHT","RTRIM","SOUNDEX","SPACE","STR","STRING_AGG","STRING_ESCAPE","STUFF","SUBSTRING","TRANSLATE","TRIM","UNICODE","UPPER"],system:["$PARTITION","@@ERROR","@@IDENTITY","@@PACK_RECEIVED","@@ROWCOUNT","@@TRANCOUNT","BINARY_CHECKSUM","CHECKSUM","COMPRESS","CONNECTIONPROPERTY","CONTEXT_INFO","CURRENT_REQUEST_ID","CURRENT_TRANSACTION_ID","DECOMPRESS","ERROR_LINE","ERROR_MESSAGE","ERROR_NUMBER","ERROR_PROCEDURE","ERROR_SEVERITY","ERROR_STATE","FORMATMESSAGE","GET_FILESTREAM_TRANSACTION_CONTEXT","GETANSINULL","HOST_ID","HOST_NAME","ISNULL","ISNUMERIC","MIN_ACTIVE_ROWVERSION","NEWID","NEWSEQUENTIALID","ROWCOUNT_BIG","SESSION_CONTEXT","XACT_STATE"],statistical:["@@CONNECTIONS","@@CPU_BUSY","@@IDLE","@@IO_BUSY","@@PACK_SENT","@@PACKET_ERRORS","@@TIMETICKS","@@TOTAL_ERRORS","@@TOTAL_READ","@@TOTAL_WRITE","TEXTPTR","TEXTVALID"],trigger:["COLUMNS_UPDATED","EVENTDATA","TRIGGER_NESTLEVEL","UPDATE"]},Xe={standard:["ADD","ALL","ALTER","ANY","AS","ASC","AUTHORIZATION","BACKUP","BEGIN","BETWEEN","BREAK","BROWSE","BULK","BY","CASCADE","CHECK","CHECKPOINT","CLOSE","CLUSTERED","COALESCE","COLLATE","COLUMN","COMMIT","COMPUTE","CONSTRAINT","CONTAINS","CONTAINSTABLE","CONTINUE","CONVERT","CREATE","CROSS","CURRENT","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","DATABASE","DBCC","DEALLOCATE","DECLARE","DEFAULT","DELETE","DENY","DESC","DISK","DISTINCT","DISTRIBUTED","DOUBLE","DROP","DUMP","ERRLVL","ESCAPE","EXEC","EXECUTE","EXISTS","EXIT","EXTERNAL","FETCH","FILE","FILLFACTOR","FOR","FOREIGN","FREETEXT","FREETEXTTABLE","FROM","FULL","FUNCTION","GOTO","GRANT","GROUP","HAVING","HOLDLOCK","IDENTITY","IDENTITYCOL","IDENTITY_INSERT","IF","IN","INDEX","INNER","INSERT","INTERSECT","INTO","IS","JOIN","KEY","KILL","LEFT","LIKE","LINENO","LOAD","MERGE","NATIONAL","NOCHECK","NONCLUSTERED","NOT","NULL","NULLIF","OF","OFF","OFFSETS","ON DELETE","ON UPDATE","OPEN","OPENDATASOURCE","OPENQUERY","OPENROWSET","OPENXML","OPTION","ORDER","OUTER","OVER","PERCENT","PIVOT","PLAN","PRECISION","PRIMARY","PRINT","PROC","PROCEDURE","PUBLIC","RAISERROR","READ","READTEXT","RECONFIGURE","REFERENCES","REPLICATION","RESTORE","RESTRICT","RETURN","REVERT","REVOKE","RIGHT","ROLLBACK","ROWCOUNT","ROWGUIDCOL","RULE","SAVE","SCHEMA","SECURITYAUDIT","SELECT","SEMANTICKEYPHRASETABLE","SEMANTICSIMILARITYDETAILSTABLE","SEMANTICSIMILARITYTABLE","SESSION_USER","SET","SETUSER","SHUTDOWN","SOME","STATISTICS","SYSTEM_USER","TABLE","TABLESAMPLE","TEXTSIZE","THEN","TO","TOP","TRAN","TRANSACTION","TRIGGER","TRUNCATE","TRY_CONVERT","TSEQUAL","UNION","UNIQUE","UNPIVOT","UPDATE","UPDATETEXT","USE","USER","VALUES","VARYING","VIEW","WAITFOR","WHERE","WHILE","WITH","WITHIN GROUP","WRITETEXT"],odbc:["ABSOLUTE","ACTION","ADA","ADD","ALL","ALLOCATE","ALTER","ANY","ARE","AS","ASC","ASSERTION","AT","AUTHORIZATION","AVG","BEGIN","BETWEEN","BIT","BIT_LENGTH","BOTH","BY","CASCADE","CASCADED","CAST","CATALOG","CHAR","CHARACTER","CHARACTER_LENGTH","CHAR_LENGTH","CHECK","CLOSE","COALESCE","COLLATE","COLLATION","COLUMN","COMMIT","CONNECT","CONNECTION","CONSTRAINT","CONSTRAINTS","CONTINUE","CONVERT","CORRESPONDING","COUNT","CREATE","CROSS","CURRENT","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","DATE","DAY","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DEFERRABLE","DEFERRED","DELETE","DESC","DESCRIBE","DESCRIPTOR","DIAGNOSTICS","DISCONNECT","DISTINCT","DOMAIN","DOUBLE","DROP","END-EXEC","ESCAPE","EXCEPTION","EXEC","EXECUTE","EXISTS","EXTERNAL","EXTRACT","FALSE","FETCH","FIRST","FLOAT","FOR","FOREIGN","FORTRAN","FOUND","FROM","FULL","GET","GLOBAL","GO","GOTO","GRANT","GROUP","HAVING","HOUR","IDENTITY","IMMEDIATE","IN","INCLUDE","INDEX","INDICATOR","INITIALLY","INNER","INPUT","INSENSITIVE","INSERT","INT","INTEGER","INTERSECT","INTERVAL","INTO","IS","ISOLATION","JOIN","KEY","LANGUAGE","LAST","LEADING","LEFT","LEVEL","LIKE","LOCAL","LOWER","MATCH","MAX","MIN","MINUTE","MODULE","MONTH","NAMES","NATIONAL","NATURAL","NCHAR","NEXT","NO","NONE","NOT","NULL","NULLIF","NUMERIC","OCTET_LENGTH","OF","ONLY","OPEN","OPTION","ORDER","OUTER","OUTPUT","OVERLAPS","PAD","PARTIAL","PASCAL","POSITION","PRECISION","PREPARE","PRESERVE","PRIMARY","PRIOR","PRIVILEGES","PROCEDURE","PUBLIC","READ","REAL","REFERENCES","RELATIVE","RESTRICT","REVOKE","RIGHT","ROLLBACK","ROWS","SCHEMA","SCROLL","SECOND","SECTION","SELECT","SESSION","SESSION_USER","SET","SIZE","SMALLINT","SOME","SPACE","SQL","SQLCA","SQLCODE","SQLERROR","SQLSTATE","SQLWARNING","SUBSTRING","SUM","SYSTEM_USER","TABLE","TEMPORARY","TIME","TIMESTAMP","TIMEZONE_HOUR","TIMEZONE_MINUTE","TO","TRAILING","TRANSACTION","TRANSLATE","TRANSLATION","TRIM","TRUE","UNION","UNIQUE","UNKNOWN","UPDATE","UPPER","USAGE","USER","VALUE","VALUES","VARCHAR","VARYING","VIEW","WHENEVER","WHERE","WITH","WORK","WRITE","YEAR","ZONE"],future:["ABSOLUTE","ACTION","ADMIN","AFTER","AGGREGATE","ALIAS","ALLOCATE","ARE","ARRAY","ASENSITIVE","ASSERTION","ASYMMETRIC","AT","ATOMIC","BEFORE","BINARY","BIT","BLOB","BOOLEAN","BOTH","BREADTH","CALL","CALLED","CARDINALITY","CASCADED","CAST","CATALOG","CHAR","CHARACTER","CLASS","CLOB","COLLATION","COLLECT","COMPLETION","CONDITION","CONNECT","CONNECTION","CONSTRAINTS","CONSTRUCTOR","CORR","CORRESPONDING","COVAR_POP","COVAR_SAMP","CUBE","CUME_DIST","CURRENT_CATALOG","CURRENT_DEFAULT_TRANSFORM_GROUP","CURRENT_PATH","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_TRANSFORM_GROUP_FOR_TYPE","CYCLE","DATA","DATE","DAY","DEC","DECIMAL","DEFERRABLE","DEFERRED","DEPTH","DEREF","DESCRIBE","DESCRIPTOR","DESTROY","DESTRUCTOR","DETERMINISTIC","DIAGNOSTICS","DICTIONARY","DISCONNECT","DOMAIN","DYNAMIC","EACH","ELEMENT","END-EXEC","EQUALS","EVERY","FALSE","FILTER","FIRST","FLOAT","FOUND","FREE","FULLTEXTTABLE","FUSION","GENERAL","GET","GLOBAL","GO","GROUPING","HOLD","HOST","HOUR","IGNORE","IMMEDIATE","INDICATOR","INITIALIZE","INITIALLY","INOUT","INPUT","INT","INTEGER","INTERSECTION","INTERVAL","ISOLATION","ITERATE","LANGUAGE","LARGE","LAST","LATERAL","LEADING","LESS","LEVEL","LIKE_REGEX","LIMIT","LN","LOCAL","LOCALTIME","LOCALTIMESTAMP","LOCATOR","MAP","MATCH","MEMBER","METHOD","MINUTE","MOD","MODIFIES","MODIFY","MODULE","MONTH","MULTISET","NAMES","NATURAL","NCHAR","NCLOB","NEW","NEXT","NO","NONE","NORMALIZE","NUMERIC","OBJECT","OCCURRENCES_REGEX","OLD","ONLY","OPERATION","ORDINALITY","OUT","OUTPUT","OVERLAY","PAD","PARAMETER","PARAMETERS","PARTIAL","PARTITION","PATH","PERCENTILE_CONT","PERCENTILE_DISC","PERCENT_RANK","POSITION_REGEX","POSTFIX","PREFIX","PREORDER","PREPARE","PRESERVE","PRIOR","PRIVILEGES","RANGE","READS","REAL","RECURSIVE","REF","REFERENCING","REGR_AVGX","REGR_AVGY","REGR_COUNT","REGR_INTERCEPT","REGR_R2","REGR_SLOPE","REGR_SXX","REGR_SXY","REGR_SYY","RELATIVE","RELEASE","RESULT","RETURNS","ROLE","ROLLUP","ROUTINE","ROW","ROWS","SAVEPOINT","SCOPE","SCROLL","SEARCH","SECOND","SECTION","SENSITIVE","SEQUENCE","SESSION","SETS","SIMILAR","SIZE","SMALLINT","SPACE","SPECIFIC","SPECIFICTYPE","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","START","STATE","STATEMENT","STATIC","STDDEV_POP","STDDEV_SAMP","STRUCTURE","SUBMULTISET","SUBSTRING_REGEX","SYMMETRIC","SYSTEM","TEMPORARY","TERMINATE","THAN","TIME","TIMESTAMP","TIMEZONE_HOUR","TIMEZONE_MINUTE","TRAILING","TRANSLATE_REGEX","TRANSLATION","TREAT","TRUE","UESCAPE","UNDER","UNKNOWN","UNNEST","USAGE","USING","VALUE","VARCHAR","VARIABLE","VAR_POP","VAR_SAMP","WHENEVER","WIDTH_BUCKET","WINDOW","WITHIN","WITHOUT","WORK","WRITE","XMLAGG","XMLATTRIBUTES","XMLBINARY","XMLCAST","XMLCOMMENT","XMLCONCAT","XMLDOCUMENT","XMLELEMENT","XMLEXISTS","XMLFOREST","XMLITERATE","XMLNAMESPACES","XMLPARSE","XMLPI","XMLQUERY","XMLSERIALIZE","XMLTABLE","XMLTEXT","XMLVALIDATE","YEAR","ZONE"]},We=["ADD SENSITIVITY CLASSIFICATION","ADD SIGNATURE","AGGREGATE","ANSI_DEFAULTS","ANSI_NULLS","ANSI_NULL_DFLT_OFF","ANSI_NULL_DFLT_ON","ANSI_PADDING","ANSI_WARNINGS","APPLICATION ROLE","ARITHABORT","ARITHIGNORE","ASSEMBLY","ASYMMETRIC KEY","AUTHORIZATION","AVAILABILITY GROUP","BACKUP","BACKUP CERTIFICATE","BACKUP MASTER KEY","BACKUP SERVICE MASTER KEY","BEGIN CONVERSATION TIMER","BEGIN DIALOG CONVERSATION","BROKER PRIORITY","BULK INSERT","CERTIFICATE","CLOSE MASTER KEY","CLOSE SYMMETRIC KEY","COLLATE","COLUMN ENCRYPTION KEY","COLUMN MASTER KEY","COLUMNSTORE INDEX","CONCAT_NULL_YIELDS_NULL","CONTEXT_INFO","CONTRACT","CREDENTIAL","CRYPTOGRAPHIC PROVIDER","CURSOR_CLOSE_ON_COMMIT","DATABASE","DATABASE AUDIT SPECIFICATION","DATABASE ENCRYPTION KEY","DATABASE HADR","DATABASE SCOPED CONFIGURATION","DATABASE SCOPED CREDENTIAL","DATABASE SET","DATEFIRST","DATEFORMAT","DEADLOCK_PRIORITY","DEFAULT","DELETE","DELETE FROM","DENY","DENY XML","DISABLE TRIGGER","ENABLE TRIGGER","END CONVERSATION","ENDPOINT","EVENT NOTIFICATION","EVENT SESSION","EXECUTE AS","EXTERNAL DATA SOURCE","EXTERNAL FILE FORMAT","EXTERNAL LANGUAGE","EXTERNAL LIBRARY","EXTERNAL RESOURCE POOL","EXTERNAL TABLE","FIPS_FLAGGER","FMTONLY","FORCEPLAN","FULLTEXT CATALOG","FULLTEXT INDEX","FULLTEXT STOPLIST","FUNCTION","GET CONVERSATION GROUP","GET_TRANSMISSION_STATUS","GRANT","GRANT XML","IDENTITY_INSERT","IMPLICIT_TRANSACTIONS","INDEX","INSERT","LANGUAGE","LOCK_TIMEOUT","LOGIN","MASTER KEY","MERGE","MESSAGE TYPE","MOVE CONVERSATION","NOCOUNT","NOEXEC","NUMERIC_ROUNDABORT","OFFSETS","OPEN MASTER KEY","OPEN SYMMETRIC KEY","PARSEONLY","PARTITION FUNCTION","PARTITION SCHEME","PROCEDURE","QUERY_GOVERNOR_COST_LIMIT","QUEUE","QUOTED_IDENTIFIER","RECEIVE","REMOTE SERVICE BINDING","REMOTE_PROC_TRANSACTIONS","RESOURCE GOVERNOR","RESOURCE POOL","RESTORE","RESTORE FILELISTONLY","RESTORE HEADERONLY","RESTORE LABELONLY","RESTORE MASTER KEY","RESTORE REWINDONLY","RESTORE SERVICE MASTER KEY","RESTORE VERIFYONLY","REVERT","REVOKE","REVOKE XML","ROLE","ROUTE","ROWCOUNT","RULE","SCHEMA","SEARCH PROPERTY LIST","SECURITY POLICY","SELECTIVE XML INDEX","SEND","SENSITIVITY CLASSIFICATION","SEQUENCE","SERVER AUDIT","SERVER AUDIT SPECIFICATION","SERVER CONFIGURATION","SERVER ROLE","SERVICE","SERVICE MASTER KEY","SET","SETUSER","SHOWPLAN_ALL","SHOWPLAN_TEXT","SHOWPLAN_XML","SIGNATURE","SPATIAL INDEX","STATISTICS","STATISTICS IO","STATISTICS PROFILE","STATISTICS TIME","STATISTICS XML","SYMMETRIC KEY","SYNONYM","TABLE","TABLE IDENTITY","TEXTSIZE","TRANSACTION ISOLATION LEVEL","TRIGGER","TRUNCATE TABLE","TYPE","UPDATE","UPDATE STATISTICS","USER","VIEW","WORKLOAD GROUP","XACT_ABORT","XML INDEX","XML SCHEMA COLLECTION","ALTER COLUMN","ALTER TABLE","CREATE TABLE","FROM","GROUP BY","HAVING","INSERT INTO","DROP TABLE","SET SCHEMA","LIMIT","OFFSET","ORDER BY","SELECT","VALUES","WHERE","WITH","WINDOW","PARTITION BY"],Ke=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT","MINUS","MINUS ALL","MINUS DISTINCT"],we=["JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN"],xe=["WHEN","ELSE"],Je=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&Ve(E,T)}(r,E);var T,t,e,R,A=(e=r,R=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}(),function(){var E,T=me(e);if(R){var t=me(this).constructor;E=Reflect.construct(T,arguments,t)}else E=T.apply(this,arguments);return be(this,E)});function r(){return He(this,r),A.apply(this,arguments)}return T=r,(t=[{key:"tokenizer",value:function(){return new aE({reservedCommands:We,reservedBinaryCommands:Ke,reservedJoins:we,reservedDependentClauses:xe,reservedKeywords:_([].concat(ve(Object.values(Xe).flat()),ve(Object.values(ge).flat()))),stringTypes:[{quote:"''",prefixes:["N"]}],identTypes:['""',"[]"],identChars:{first:"#@",rest:"#@$"},namedParamTypes:["@"],quotedParamTypes:["@"],operators:r.operators})}}])&&Ye(T.prototype,t),Object.defineProperty(T,"prototype",{writable:!1}),r}(k);function ke(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function je(E,T){if(T&&("object"===ER(T)||"function"==typeof T))return T;if(void 0!==T)throw new TypeError("Derived constructors may only return object or undefined");return function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E)}function Qe(E){var T="function"==typeof Map?new Map:void 0;return Qe=function(E){if(null===E||(t=E,-1===Function.toString.call(t).indexOf("[native code]")))return E;var t;if("function"!=typeof E)throw new TypeError("Super expression must either be null or a function");if(void 0!==T){if(T.has(E))return T.get(E);T.set(E,e)}function e(){return Ze(E,arguments,ze(this).constructor)}return e.prototype=Object.create(E.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),qe(e,E)},Qe(E)}function Ze(E,T,t){return Ze=$e()?Reflect.construct.bind():function(E,T,t){var e=[null];e.push.apply(e,T);var R=new(Function.bind.apply(E,e));return t&&qe(R,t.prototype),R},Ze.apply(null,arguments)}function $e(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(E){return!1}}function qe(E,T){return qe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(E,T){return E.__proto__=T,E},qe(E,T)}function ze(E){return ze=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(E){return E.__proto__||Object.getPrototypeOf(E)},ze(E)}function ER(E){return ER="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},ER(E)}Je.operators=["~","!<","!>","+=","-=","*=","/=","%=","|=","&=","^=","::"];var TR={bigquery:dE,db2:ZE,hive:NT,mariadb:fT,mysql:bT,n1ql:qT,plsql:ot,postgresql:pt,redshift:Wt,spark:te,sql:Be,sqlite:ae,tsql:Je},tR=Object.keys(TR),eR={language:"sql",tabWidth:2,useTabs:!1,keywordCase:"preserve",indentStyle:"standard",logicalOperatorNewline:"before",aliasAs:"preserve",tabulateAlias:!1,commaPosition:"after",expressionWidth:50,linesBetweenQueries:1,denseOperators:!1,newlineBeforeSemicolon:!1},RR=function(E){var T=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"!=typeof E)throw new Error("Invalid query argument. Expected string, instead got "+ER(E));var t=rR(Object.assign(Object.assign({},eR),T)),e=TR[t.language];return new e(t).format(E)},AR=function(E){!function(E,T){if("function"!=typeof T&&null!==T)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),Object.defineProperty(E,"prototype",{writable:!1}),T&&qe(E,T)}(A,E);var T,t,e,R=(T=A,t=$e(),function(){var E,e=ze(T);if(t){var R=ze(this).constructor;E=Reflect.construct(e,arguments,R)}else E=e.apply(this,arguments);return je(this,E)});function A(){return ke(this,A),R.apply(this,arguments)}return e=A,Object.defineProperty(e,"prototype",{writable:!1}),e}(Qe(Error));function rR(E){if(!tR.includes(E.language))throw new AR("Unsupported SQL dialect: ".concat(E.language));if("multilineLists"in E)throw new AR("multilineLists config is no more supported.");if("newlineBeforeOpenParen"in E)throw new AR("newlineBeforeOpenParen config is no more supported.");if("newlineBeforeCloseParen"in E)throw new AR("newlineBeforeCloseParen config is no more supported.");if(E.expressionWidth<=0)throw new AR("expressionWidth config must be positive number. Received ".concat(E.expressionWidth," instead."));if("before"===E.commaPosition&&E.useTabs)throw new AR("commaPosition: before does not work when tabs are used for indentation.");if("hive"===E.language&&void 0!==E.params)throw new AR('Unexpected "params" option. Prepared statement placeholders not supported for Hive.');if("spark"===E.language&&void 0!==E.params)throw new AR('Unexpected "params" option. Prepared statement placeholders not supported for Spark.');var T;return E.params&&!((T=E.params)instanceof Array?T:Object.values(T)).every((function(E){return"string"==typeof E}))&&console.warn('WARNING: All "params" option values should be strings.'),E}})(),e})()));
//# sourceMappingURL=sql-formatter.min.js.map