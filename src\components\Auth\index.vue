<template>
  <div class="custom-auth" :authKey="authKey">
    <slot v-if="visible" v-bind="{authParams}"></slot>
  </div>
</template>

<script>
export default {
  // inheritAttrs: false,
  components: {},
  props: {
    code: String,
    type: String,
    authKey: String
  },
  data() {
    const key = this.code;
    const type = this.type;
    // const AuthKeys = window.AuthKeys || {};
    // const AuthMap = window.AuthMap || {};
    // const authItem = AuthMap[key];
    const auth = this.$store.state.auth;
    let visible = true;
    let authParams = {};

    if (key && auth) {
      // if (!key.startsWith('systemAuth_')) {
      // let show = null;
      // if (type === 'DefaultShow') {
      //   auth.elements.forEach(item => {
      //     if (item.id.startsWith(key)) {
      //       const sourceExt = item.source_ext || {};
      //       if (sourceExt._show == 0) {
      //         show = 0;
      //         return false;
      //       }
      //     }
      //   });
      // } else if (type === 'DefaultHide') {
      //   auth.elements.forEach(item => {
      //     if (item.id.startsWith(key)) {
      //       const sourceExt = item.source_ext || {};
      //       if (sourceExt._show == 1) {
      //         show = 1;
      //         return false;
      //       }
      //     }
      //   });
      // }

      // if (
      //   (type === 'DefaultShow' && show == 0) ||
      //   (type === 'DefaultHide' && show != 1)
      // ) {
      //   visible = false;
      // }
      const matchItem = auth.elements.find(item => item.id === key) || {};
      const sourceExt = matchItem.source_ext || {};
      if (type === 'DefaultShow' || type === 'DefaultHide') {
        if (sourceExt._show != null && sourceExt._show == 0) {
          visible = false;
        }
      }
    }
    _.forEach(auth.elements, (item, index) => {
      if (item.id.startsWith(key)) {
        authParams = item.source_ext || {};
        return false;
      }
    });
    // }

    return {
      visible,
      authParams
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.custom-auth {
}
</style>
