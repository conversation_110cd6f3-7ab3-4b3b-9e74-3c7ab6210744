export default function (ctx) {
  const columns = [
    {
      title: '审核结果',
      dataIndex: 'ai_status',
      key: 'ai_status',
      scopedSlots: { customRender: 'ai_status' },
      width: 200
    },
    {
      title: 'SQL文本',
      dataIndex: 'STMT_TEXT',
      key: 'STMT_TEXT',
      scopedSlots: { customRender: 'STMT_TEXT' },
      width: 150
    },
    {
      title: '开始时间',
      key: 'INSERT_TIMESTAMP',
      dataIndex: 'INSERT_TIMESTAMP',
      scopedSlots: { customRender: 'INSERT_TIMESTAMP' },
      sorter: true,
      width: 180
    },
    {
      title: '执行的次数',
      key: 'NUM_EXECUTIONS',
      dataIndex: 'NUM_EXECUTIONS',
      scopedSlots: { customRender: 'NUM_EXECUTIONS' },
      sorter: true,
      width: 200
    },
    {
      title: '总时长(ms)',
      key: 'TOTAL_ACT_TIME',
      dataIndex: 'TOTAL_ACT_TIME',
      scopedSlots: { customRender: 'TOTAL_ACT_TIME' },
      sorter: true,
      width: 180
    },
    {
      title: 'SQL执行时长(ms)',
      key: 'STMT_EXEC_TIME',
      dataIndex: 'STMT_EXEC_TIME',
      sorter: true,
      width: 180
    },
    {
      title: 'CPU总消耗(ms)',
      dataIndex: 'TOTAL_CPU_TIME',
      key: 'TOTAL_CPU_TIME',
      sorter: true,
      width: 300
    },
    {
      title: '直接读',
      dataIndex: 'DIRECT_READS',
      key: 'DIRECT_READS',
      sorter: true,
      width: 300
    },
    {
      title: '直接写',
      dataIndex: 'DIRECT_WRITES',
      key: 'DIRECT_WRITES',
      sorter: true,
      width: 300
    },
    {
      title: '锁等待时间(ms)',
      dataIndex: 'LOCK_WAIT_TIME',
      key: 'LOCK_WAIT_TIME',
      sorter: true,
      width: 300
    },
    {
      title: '查询总行数',
      dataIndex: 'ROWS_READ',
      key: 'ROWS_READ',
      sorter: true,
      width: 150
    },
    {
      title: '返回总行数',
      dataIndex: 'ROWS_RETURNED',
      key: 'ROWS_RETURNED',
      sorter: true,
      width: 150
    },
    {
      title: '逻辑读',
      dataIndex: 'POOL_DATA_L_READS',
      key: 'POOL_DATA_L_READS',
      sorter: true,
      width: 150
    },
    {
      title: '物理读',
      dataIndex: 'POOL_DATA_P_READS',
      key: 'POOL_DATA_P_READS',
      sorter: true,
      width: 150
    },
    {
      title: '索引逻辑读',
      dataIndex: 'POOL_INDEX_L_READS',
      key: 'POOL_INDEX_L_READS',
      sorter: true,
      width: 150
    },
    {
      title: '索引物理读',
      dataIndex: 'POOL_INDEX_P_READS',
      key: 'POOL_INDEX_P_READS',
      sorter: true,
      width: 150
    },
    {
      title: '落库时间',
      dataIndex: 'created_at',
      key: 'created_at',
      scopedSlots: { customRender: 'created_at' },
      sorter: true,
      width: 150
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 80,
      fixed: 'right'
    }
  ].map(item => {
    return {
      ...item,
      width: undefined
    }
  });
  const fields = [
    {
      type: 'Select',
      label: '审核结果',
      key: 'ai_status',
      props: {
        options: [
          {
            label: '未知',
            value: 0
          },
          {
            label: '通过',
            value: 1
          },
          {
            label: '未通过',
            value: -1
          },
          {
            label: '白名单通过',
            value: 2
          },
          {
            label: '错误',
            value: 9
          }

        ]
      }
    },
    // 待定
    // {
    //   type: 'Input',
    //   label: '开始时间',
    //   key: 'INSERT_TIMESTAMP'
    // },
    // {
    //   type: 'Input',
    //   label: '执行的次数',
    //   key: 'NUM_EXECUTIONS'
    // },
    // {
    //   type: 'Input',
    //   label: '总时长',
    //   key: 'TOTAL_ACT_TIME'
    // },
    // {
    //   type: 'Input',
    //   label: 'SQL执行时间',
    //   key: 'STMT_EXEC_TIME'
    // },
    // {
    //   type: 'Input',
    //   label: 'CPU总消耗',
    //   key: 'TOTAL_CPU_TIME'
    // },
    // {
    //   type: 'Input',
    //   label: '直接读',
    //   key: 'DIRECT_READS'
    // },
    // {
    //   type: 'Select',
    //   label: '直接写',
    //   key: 'DIRECT_WRITES'
    // },
    // {
    //   type: 'Input',
    //   label: '锁等待时间',
    //   key: 'LOCK_WAIT_TIME'
    // },
    // {
    //   type: 'Input',
    //   label: '查询总行数',
    //   key: 'ROWS_READ'
    // },
    // {
    //   type: 'Input',
    //   label: '返回总行数',
    //   key: 'ROWS_RETURNED'
    // },
    // {
    //   type: 'Input',
    //   label: '逻辑读',
    //   key: 'POOL_DATA_L_READS'
    // },
    // {
    //   type: 'Input',
    //   label: '物理读',
    //   key: 'POOL_DATA_P_READS'
    // },
    // {
    //   type: 'Input',
    //   label: '索引逻辑读',
    //   key: 'POOL_INDEX_L_READS'
    // },
    // {
    //   type: 'Input',
    //   label: '索引物理读',
    //   key: 'POOL_INDEX_P_READS'
    // },
    {
      type: 'Input',
      label: 'SQL文本',
      key: 'STMT_TEXT'
    }
  ];
  return {
    columns,
    searchFields: fields
  };
}
