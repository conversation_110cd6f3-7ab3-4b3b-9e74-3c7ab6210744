/*
 * @Author: your name
 * @Date: 2021-01-28 10:16:11
 * @LastEditTime: 2021-02-05 17:43:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/api/review.js
 */
import Http from '@/utils/request'

export function getHasSubscribe(params = {}) {
  return Http({
    url: `/sqlreview/subscribe/sub-op/`,
    method: 'post',
    data: params
  });
}

export function getInfo(params = {}) {
  return Http({
    url: `/sqlreview/subscribe/user_projects`,
    method: 'get',
    params
  });
}

export default {};