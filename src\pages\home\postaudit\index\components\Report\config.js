export default function (ctx) {
  const pieOption = (data = []) => {
    const total = data.map(item => item.count).reduce((a, b) => a + b, 0);

    if (data.length <= 0) return;
    return {
      title: {
        text: 'SQL总数',
        textStyle: {
          color: '#b1b1b1',
          fontSize: 14,
          fontWeight: 400
        },
        subtext: `${total}条`,
        subtextStyle: {
          color: '#000000',
          fontSize: 24,
          fontWeight: 600
        },
        left: 'center',
        top: '40%'
      },
      color: ['#0ca1ff', '#00cdcb', '#00ce6c', '#ffd213', '#ff587a'],
      tooltip: {
        trigger: 'item'
      },
      legend: {
        show: false
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['40%', '60%'],
          data: data.map(item => {
            return {
              value: item.count,
              name: item.name
            }
          }),
          itemStyle: {
            borderWidth: 2,
            borderColor: '#ffffff'
          },
          label: {
            color: '#656565',
            formatter: '{b}:{d}%'
          },
          labelLine: {
            smooth: true
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  };

  const columns = [
    {
      title: '类型',
      dataIndex: 'name',
      key: 'name',
      width: 200
    },
    {
      title: 'review方式',
      dataIndex: 'mode',
      key: 'mode',
      width: 200
    },
    {
      title: 'SQL数量',
      dataIndex: 'count',
      key: 'count',
      width: 200
    }
  ];

  const barOption = (params = {}) => {
    const { current = 0, data = [], ruleType } = params;
    let names = ruleType === 'SQL' ? data.map(item => `ID: ${item.name}`) : data.map((item, index) => `TOP ${index + 1}`);

    if (data.length <= 0) return;
    return () => ({
      color: [ruleType === 'SQL' ? '#679fff' : '#eca61a'],
      grid: {
        top: '8%',
        bottom: '8%',
        left: '8%',
        right: '8%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        show: false
      },
      yAxis: {
        type: 'category',
        data: names,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#656565',
          padding: [0, 8, 0, 0],
          // width: CommonUtil.calDynamicPixel(72, { min: 50 }),
          overflow: 'break'
          // align: 'left'
        },
        inverse: true
      },
      series: [{
        data: data.map((item, index) => {
          return {
            value: item.count,
            itemStyle: current === index ? {
              color: ruleType === 'SQL' ? '#0ca1ff' : '#ffb41e',
              // borderWidth: 1,
              // borderColor: '#ffffff',
              shadowBlur: 4,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            } : {}
          }
        }),
        type: 'bar',
        barWidth: '50%',
        // selectedMode: 'single',
        // select: {
        //   itemStyle: {
        //     color: 'red'
        //   }
        // },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        },
        label: {
          show: true,
          formatter: `{count|{c}${ruleType === 'SQL' ? '条' : '次'}}`,
          rich: {
            count: {
              color: '#ffffff'
            }
          }
        }
      }]
    })
  };

  return {
    pieOption,
    columns,
    barOption
  };
}