import Vue from 'vue';
import DataBaseChoose from './DataBaseChoose';
import RuleCompsRange from './RuleComps/Range';
import CustomTableEditComponents from '../TableEdit/register';
import CustomFormComponents from '../Form/register';
import Item from './RuleComps/Rules/Item/index';
import Group from './RuleComps/Rules/Group/index';

// 注册全局组件，可以防止基础组件递归引用
CustomTableEditComponents.setTableEditComp('DataBaseChoose', DataBaseChoose);
CustomTableEditComponents.setTableEditComp('RuleCompsRange', RuleCompsRange);
CustomFormComponents.setFormComp('DataBaseChoose', DataBaseChoose);

Vue.component(GLOBAL_COMPONENTS['biz-rule-item'], Item);
Vue.component(GLOBAL_COMPONENTS['biz-rule-group'], Group);