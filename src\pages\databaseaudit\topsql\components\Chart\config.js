export default function (ctx) {
  let intervalFn = (length) => {
    let num = null;
    switch (length) {
      case length < 6:
        num = 0;
        break;
      case length <= 25 && length >= 6:
        num = 1;
        break;
      case length <= 50 && length > 25:
        num = 3;
        break;
      case length > 50:
        num = 8;
        break;
      default:
        break;
    }
    return num;
  };
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },

    {
      title: 'SQL文本',
      key: 'sql_text',
      dataIndex: 'sql_text',
      scopedSlots: { customRender: 'sql_text' }
    },
    {
      title: '执行时间',
      dataIndex: 'value',
      key: 'value',
      scopedSlots: { customRender: 'value' }
    }
  ];
  const topOption = (params = {}) => {
    return {
      title: [
        {
          text: 'Top 5',
          textStyle: {
            color: '#27272A',
            fontSize: '14px',
            fontWeight: 600
          }
        },
        {
          text: '(' + params.chart_desc + ')',
          left: 48,
          textStyle: {
            color: '#BFBFBF',
            fontSize: '14px',
            fontWeight: 400
          }
        }
      ],
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        icon: 'circle',
        right: 0,
        top: '4px'
      },
      grid: {
        left: '5%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        axisLabel: {
          interval: intervalFn(params.time.length),
          rotate: params.time.length < 6 ? 0 : 30,
          color: 'rgba(0,0,0,0.65)',
          fontSize: 10
        },
        data: params.time || []
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'TOP 1',
          type: 'line',
          stack: 'top 1',
          symbol: 'none',
          data: params.TOP1 || []
        },
        {
          name: '2',
          type: 'line',
          stack: 'top 2',
          symbol: 'none',
          data: params.TOP2 || []
        },
        {
          name: '3',
          type: 'line',
          stack: 'top 3',
          symbol: 'none',
          data: params.TOP3 || []
        },
        {
          name: '4',
          type: 'line',
          stack: 'top 4',
          symbol: 'none',
          data: params.TOP4 || []
        },
        {
          name: '5',
          type: 'line',
          stack: 'top 5',
          symbol: 'none',
          data: params.TOP5 || []
        }
      ]
    };
  };
  const exeNumberOption = (params = {}, dbType) => {
    const seriesMap = {
      MYSQL: [
        {
          name: '执行次数',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + '次';
            }
          },
          barWidth: 20,
          barMaxWidth: 50,
          data: params.exe_number
        },
        {
          name: '返回行数',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.return_avg_rows
        },
        {
          name: '扫描行数',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.examined_avg_rows
        }
      ],
      ORACLE: [
        {
          name: '执行次数',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + '次';
            }
          },
          barWidth: 20,
          barMaxWidth: 50,
          data: params.execution_number
        },
        {
          name: '平均逻辑读',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.logical_avg_read
        },
        {
          name: '平均物理读',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.physical_avg_read
        }
      ],
      GOLDENDB: [
        {
          name: '执行次数',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + '次';
            }
          },
          barWidth: 20,
          barMaxWidth: 50,
          data: params.exe_number
        },
        {
          name: '返回行数',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.return_avg_rows
        },
        {
          name: '扫描行数',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.examined_avg_rows
        }
      ],
      DB2: [
        {
          name: '执行行数',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + 's';
            }
          },
          data: params.num_executions
        },
        {
          name: '读取行数',
          type: 'line',
          stack: 'rows_read',
          symbol: 'none',
          data: params.rows_read
        },
        {
          name: '返回行数',
          type: 'line',
          stack: 'rows_returned',
          symbol: 'none',
          data: params.rows_returned
        },
        {
          name: '修改行数',
          type: 'line',
          stack: 'rows_modified',
          symbol: 'none',
          data: params.rows_modified
        }
      ],
      GAUSSDB: [
        {
          name: '最大持锁数',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + 's';
            }
          },
          data: params.lock_max_count
        },
        {
          name: '加锁次数',
          type: 'line',
          stack: 'rows_read',
          symbol: 'none',
          data: params.lock_count
        }
      ]
    }
    return {
      title: [
        {
          text: 'SQL执行概览',
          textStyle: {
            color: '#27272A',
            fontSize: '14px',
            fontWeight: 600
          }
        }
      ],
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        icon: 'circle',
        right: 0,
        top: 0
      },
      xAxis: [
        {
          type: 'category',
          data: params.time,
          boundaryGap: true,
          axisLabel: {
            interval: intervalFn(params.time.length),
            rotate: params.time.length < 6 ? 0 : 30,
            color: 'rgba(0,0,0,0.65)',
            fontSize: 10
          }
        }
      ],
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}'
        }
      },
      series: seriesMap[dbType]
    };
  };
  const exeTimeOption = (params = [], value) => {
    let current = _.findIndex(params.time, (item) => {
      return item == value;
    });
    return {
      title: [
        {
          text: '耗时报表',
          textStyle: {
            color: '#27272A',
            fontSize: '14px',
            fontWeight: 600
          }
        },
        {
          text: '(增删改查)',
          left: 64,
          textStyle: {
            color: '#BFBFBF',
            fontSize: '14px',
            fontWeight: 400
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        icon: 'circle',
        right: 0,
        top: '4px'
      },
      grid: {
        left: '5%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: params.time,
          boundaryGap: true,
          axisLabel: {
            interval: intervalFn(params.time.length),
            rotate: params.time.length < 6 ? 0 : 30,
            fontSize: 10,
            color: (e) => {
              return value == e ? '#25a7e8' : 'rgba(0,0,0,0.65)';
            }
          },
          triggerEvent: true
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      color: ['#4C6FBF', '#4DB5F2', '#A5D9F8', '#E7EFFF'],
      series: [
        {
          name: 'SELECT',
          type: 'bar',
          stack: 'Direct',
          emphasis: {
            focus: 'series'
          },
          barWidth: 20,
          data: params.SELECT.map((item, index) => {
            return {
              value: item,
              itemStyle:
                current === index
                  ? {
                      color: '#214ac0',
                      shadowBlur: 4,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  : {}
            };
          })
        },
        {
          name: 'INSERT',
          type: 'bar',
          stack: 'Direct',
          emphasis: {
            focus: 'series'
          },
          barWidth: 20,
          data: params.INSERT.map((item, index) => {
            return {
              value: item,
              itemStyle:
                current === index
                  ? {
                      color: '#219be3',
                      shadowBlur: 4,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  : {}
            };
          })
        },
        {
          name: 'UPDATE',
          type: 'bar',
          stack: 'Direct',
          emphasis: {
            focus: 'series'
          },
          barWidth: 20,
          data: params.UPDATE.map((item, index) => {
            return {
              value: item,
              itemStyle:
                current === index
                  ? {
                      color: '#7fc4ed',
                      shadowBlur: 4,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  : {}
            };
          })
        },
        {
          name: 'DELETE',
          type: 'bar',
          stack: 'Direct',
          emphasis: {
            focus: 'series'
          },
          barWidth: 20,
          data: params.DELETE.map((item, index) => {
            return {
              value: item,
              itemStyle:
                current === index
                  ? {
                      color: '#d6e4ff',
                      shadowBlur: 4,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  : {}
            };
          })
        }
      ]
    };
  };
  const exeMaxTimeOption = (params = [], dbType) => {
    const seriesMap = {
      MYSQL: [
        {
          name: '执行总耗时',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + 's';
            }
          },
          data: params.exe_total_time
        },
        {
          name: '最大耗时',
          type: 'line',
          stack: 'max time',
          symbol: 'none',
          data: params.exe_max_time
        },
        {
          name: '平均耗时',
          type: 'line',
          stack: 'avg time',
          symbol: 'none',
          data: params.exe_avg_time
        },
        {
          name: '锁平均时间',
          type: 'line',
          stack: 'lock time',
          symbol: 'none',
          data: params.lock_avg_time
        },
        {
          name: '查询时间95值',
          type: 'line',
          stack: 'execution_time_95',
          symbol: 'none',
          data: params.execution_time_95
        }
      ],
      ORACLE: [
        {
          name: '执行总耗时',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + 's';
            }
          },
          data: params.execution_total_time
        },
        {
          name: '平均执行时间耗时',
          type: 'line',
          stack: 'execution_average_time',
          symbol: 'none',
          data: params.execution_average_time
        },
        {
          name: '平均IO等待时间',
          type: 'line',
          stack: 'io_avg_wait',
          symbol: 'none',
          data: params.io_avg_wait
        },
        {
          name: '平均集群等待时间',
          type: 'line',
          stack: 'cluster_avg_wait',
          symbol: 'none',
          data: params.cluster_avg_wait
        }
      ],
      GOLDENDB: [
        {
          name: '执行总耗时',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + 's';
            }
          },
          data: params.exe_total_time
        },
        {
          name: '最大耗时',
          type: 'line',
          stack: 'max time',
          symbol: 'none',
          data: params.exe_max_time
        },
        {
          name: '平均耗时',
          type: 'line',
          stack: 'avg time',
          symbol: 'none',
          data: params.exe_avg_time
        },
        {
          name: '锁平均时间',
          type: 'line',
          stack: 'lock time',
          symbol: 'none',
          data: params.lock_avg_time
        },
        {
          name: '查询时间95值',
          type: 'line',
          stack: 'execution_time_95',
          symbol: 'none',
          data: params.execution_time_95
        }
      ],
      DB2: [
        {
          name: 'cpu总时间',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + '次';
            }
          },
          barWidth: 20,
          barMaxWidth: 50,
          data: params.total_cpu_time
        },
        {
          name: '活动总时间',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.total_act_time
        }
      ],
      GAUSSDB: [
        {
          name: '执行时间',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + '次';
            }
          },
          barWidth: 20,
          barMaxWidth: 50,
          data: params.actuator_time
        },
        {
          name: 'PL执行时间',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.pl_execution_time
        },
        {
          name: 'PL编译时间',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value + '次';
            }
          },
          barWidth: 20,
          barMaxWidth: 50,
          data: params.pl_compilation_time
        },
        {
          name: '计划时间',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.plan_time
        },
        {
          name: '重写时间',
          type: 'line',
          symbol: 'none',
          tooltip: {
            valueFormatter: function (value) {
              return value;
            }
          },
          data: params.rewrite_time
        }
      ]
    }
    return {
      title: {
        text: 'SQL执行耗时',
        textStyle: {
          color: '#27272A',
          fontSize: '14px',
          fontWeight: 600
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        icon: 'circle',
        right: 8,
        top: '4px'
      },
      grid: {
        left: '5%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        axisLabel: {
          interval: intervalFn(params.time.length),
          rotate: params.time.length < 6 ? 0 : 30,
          color: 'rgba(0,0,0,0.65)',
          fontSize: 10
        },
        data: params.time
      },
      yAxis: {
        type: 'value'
      },
      series: seriesMap[dbType]
    };
  };
  return {
    columns,
    topOption,
    exeTimeOption,
    exeNumberOption,
    exeMaxTimeOption
  };
}
