export default function (ctx) {
  const fields = (type, cybrearkSwitch) => {
    const isTaiLong = process.channel === 'TaiLongBank';
    const config = [
      (formData = {}) => {
        return {
          type: 'Select',
          label: '模式类型',
          key: 'ob_mode',
          visible:
            formData.db_type == 'OCEANBASE' || formData.db_type == 'TDSQL',
          props: {
            options:
              formData.db_type == 'OCEANBASE'
                ? [
                  {
                    label: 'OB_MYSQL',
                    value: 'OB_MYSQL'
                  },
                  {
                    label: 'OB_ORACLE',
                    value: 'OB_ORACLE'
                  }
                ]
                : [
                  {
                    label: 'TD_MYSQL',
                    value: 'TD_MYSQL'
                  },
                  {
                    label: 'TD_PGSQL',
                    value: 'TD_PGSQL'
                  }
                ]
          },
          listeners: {
            change: (value) => {
              // let dbUrL = ctx.formParams.fields.find(
              //   (item) => item.key === 'db_url'
              // );
              // if (value === 'ob_oracle') {
              //   dbUrL.props.placeholder = '//0.0.0.0:1521/service name';
              // } else if (value === 'ob_mysql') {
              //   dbUrL.props.placeholder = '//0.0.0.0:2883';
              // }
              // ctx.formParams.fields = [...ctx.formParams.fields];
            }
          },
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      {
        type: 'Input',
        label: '数据源名称',
        key: 'name',
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'RadioGroup',
        label: '环境',
        key: 'env',
        props: {
          mode: 'tips',
          class: 'inline',
          options: [
            {
              label: '测试',
              value: 'TEST'
            },
            {
              label: '生产',
              value: 'PROD'
            }
          ]
        },
        rules: [{ required: true, message: '该项为必填项' }]
      },
      {
        type: 'Input',
        label: '连接串',
        key: 'db_url',
        props: {},
        listeners: {
          blur: (e) => {
            ctx.$refs.form.saving({
              db_url: e.target.value
            });
          }
        },
        rules: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const { form } = ctx.$refs;
              const dbType = form.getData().db_type;
              if (dbType === 'oracle' || dbType === 'db2') {
                // !/^\/\/([0-9]{1,3}\.){3}[0-9]{1,3}:[0-9]{1,9}\/[^/\s]+$/.test(value)
                if (
                  !/^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\d+)\/[^/\s]+$/.test(
                    value
                  )
                ) {
                  const port = dbType === 'oracle' ? '1521' : '50000';
                  callback(
                    new Error(`请输入正确格式：0.0.0.0:${port}/service name`)
                  );
                } else {
                  callback();
                }
              } else if (dbType === 'mysql' || dbType === 'tidb') {
                // !/^\/\/([0-9]{1,3}\.){3}[0-9]{1,3}:[0-9]{1,9}$/.test(value)
                if (
                  !/^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\d+)$/.test(
                    value
                  )
                ) {
                  const port = dbType === 'mysql' ? '3306' : '4000';
                  callback(new Error(`请输入正确格式：0.0.0.0:${port}`));
                } else {
                  callback();
                }
              } else {
                if (!value) {
                  callback(new Error('该项为必填项'));
                } else {
                  callback();
                }
              }
            },
            trigger: 'blur'
          }
        ]
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '租户',
          key: 'tenant',
          visible: formData.db_type == 'OCEANBASE',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      {
        type: 'Input',
        label: 'agent_ip',
        key: 'agent_ip',
        visible: isTaiLong
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Input',
        label: '账号',
        key: 'user',
        listeners: {
          blur: (e) => {
            ctx.$refs.form.saving({
              user: e.target.value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'RadioGroup',
        label: ' ',
        className: 'hidden-label',
        key: 'decode_type',
        props: {
          mode: 'tips',
          class: 'inline',
          options: [
            {
              label: '密码',
              value: 0
            },
            {
              label: '密钥',
              value: 1
            }
          ]
        },
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              decode_type: value,
              password: ''
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        const password = {
          type: 'InputPassword',
          label: '密码',
          key: 'password',
          className: 'password',
          props: { placeholder: '请输入' },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
        if (type === 'edit') {
          password.rules = [];
          password.props = {
            placeholder:
              formData.decode_type == 0
                ? '填写该项，会更新密码'
                : '密码为非必填'
          };
        } else if (type === 'add' && cybrearkSwitch == 1) {
          password.rules =
            formData.decode_type == 0
              ? [{ required: true, message: '该项为必填项', trigger: 'blur' }]
              : [];
          password.props = {
            placeholder: formData.decode_type == 0 ? '请输入' : '密码为非必填'
          };
        }
        return password;
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: 'proxy代理',
          key: 'proxy_status',
          props: {
            options: [
              {
                label: '开',
                value: 1
              },
              {
                label: '关',
                value: 0
              }
            ]
          },
          listeners: {
            change: (value) => {
              ctx.$refs.form.saving({
                proxy_status: value
              });
            }
          },
          visible: formData.db_type == 'OCEANBASE',
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '集群名称',
          key: 'cluster',
          visible: formData.proxy_status === 1,
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'catalog',
          key: 'catalog_name',
          props: {
            url: '/sqlreview/project/data_source_get_presto_catalog',
            reqParams: {
              db_url: formData.db_url,
              user: formData.user
            }
          },
          visible: formData.db_type == 'PRESTO',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: 'SSL',
          key: 'ssl',
          props: {
            mode: 'tips',
            class: 'inline',
            options: [
              {
                label: '启用',
                value: 1
              },
              {
                label: '禁用',
                value: 0
              }
            ]
          },
          listeners: {
            change: (value) => {
              ctx.$refs.form.saving({
                ssl: value,
                ssl_mode: null
              });
            }
          },
          visible: formData.db_type == 'MYSQL'
          // rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'CA file',
          key: 'ssl_ca',
          hideComponent: true,
          slots: [{ key: 'ssl_ca' }],
          visible: formData.ssl == 1
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'Client certificate file',
          key: 'ssl_cert',
          hideComponent: true,
          slots: [{ key: 'ssl_cert' }],
          visible: formData.ssl == 1
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'Client key file',
          key: 'ssl_key',
          hideComponent: true,
          slots: [{ key: 'ssl_key' }],
          visible: formData.ssl == 1
        };
      },
      (formData = {}) => {
        const password = {
          type: 'InputPassword',
          label: 'Client Key password',
          key: 'ssl_key_password',
          className: 'password',
          props: { placeholder: '请输入' },
          rules: [],
          visible: formData.ssl == 1
        };
        if (type === 'edit') {
          password.props = {
            placeholder: '填写该项，会更新密码'
          };
        }
        return password;
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'Mode',
          key: 'ssl_mode',
          props: {
            options: [
              {
                label: 'Require',
                value: 'require'
              },
              {
                label: 'Verify Ca',
                value: 'verify_ca'
              },
              {
                label: 'Full Verification',
                value: 'full_verification'
              }
            ]
          },
          visible: formData.ssl == 1,
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        }
      }
      // {
      //   type: 'RadioGroup',
      //   label: '管控模式',
      //   key: 'is_permission_control',
      //   props: {
      //     mode: 'lineTips',
      //     options: [
      //       {
      //         label: '自由操作',
      //         value: 0,
      //         tips: '开发的元数据权限包含查询、DML、DDL等，一般用于测试环境。'
      //       },
      //       {
      //         label: '安全协同',
      //         value: 1,
      //         tips: '开发的元数据权限需要申请和授权，一般用于生产环境。'
      //       }
      //     ]
      //   },
      //   rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      // },
      // 管控模式 客户理解不了 改为 权限控制
      // {
      //   type: 'RadioGroup',
      //   label: '权限控制',
      //   key: 'is_permission_control',
      //   props: {
      //     options: [
      //       {
      //         label: '关',
      //         value: 0
      //       },
      //       {
      //         label: '开',
      //         value: 1
      //       }
      //     ]
      //   },
      //   rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      // }
    ];
    // if (type === 'edit' || cybrearkSwitch == 1) {
    //   let password = config.find((item) => item.key === 'password');
    //   password.rules = [];
    //   type === 'edit' &&
    //     (password.props = { placeholder: '填写该项，会更新密码' });
    // }
    return config;
  };

  const obInfo = (type, examineUser) => {
    return [
      {
        type: 'Input',
        label: '数据库审核账号',
        key: 'examine_user',
        rules: []
      },
      {
        type: 'RadioGroup',
        label: ' ',
        className: 'hidden-label',
        key: 'examine_pwd_type',
        props: {
          mode: 'tips',
          class: 'inline',
          options: [
            {
              label: '密码',
              value: 0
            },
            {
              label: '密钥',
              value: 1
            }
          ]
        },
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              examine_pwd_type: value,
              examine_pwd: null
            });
            ctx.$refs.obForm.saving({
              examine_pwd_type: value,
              examine_pwd: null
            });
          }
        },
        rules: []
      },
      (formData = {}) => {
        const password = {
          type: 'InputPassword',
          label: '数据库审核密码',
          key: 'examine_pwd',
          className: 'password',
          props: {
            placeholder:
              formData.examine_pwd_type == 0 && examineUser
                ? '填写该项，会更新密码'
                : formData.examine_pwd_type == 1
                  ? '密码为非必填'
                  : '请输入'
          },
          rules: []
        };
        if (type === 'edit') {
          password.rules = [];
          password.props = {
            placeholder:
              formData.examine_pwd_type == 0 && examineUser
                ? '填写该项，会更新密码'
                : formData.examine_pwd_type == 1
                  ? '密码为非必填'
                  : '请输入'
          };
        }
        return password;
      }
    ];
  };

  return {
    fields,
    obInfo
  };
}
