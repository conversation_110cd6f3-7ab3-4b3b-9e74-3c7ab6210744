<template>
  <div>
    <Table ref="table" v-bind="tableParams || {}" :dataSource="dataSource" bordered></Table>
  </div>
</template>

<script>
import Table from '@/components/Table';
import config from './config';
export default {
  components: { Table },
  props: {
    activeTab: {
      type: String,
      default: ''
    },
    dataSource: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    this.config = config(this);
    return {
      tableParams: {
        columns: this.currentCol(),
        rowKey: 'id'
      }
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    currentCol() {
      if (this.activeTab === 'tableMessage') {
        return this.config.tableColumns;
      } else if (this.activeTab === 'indexMessage') {
        return this.config.indexColumns;
      } else {
        return this.config.fieldColumns;
      }
    }
  }
};
</script>

<style scoped lang="scss">
</style>
