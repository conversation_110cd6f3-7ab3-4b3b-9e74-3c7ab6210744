<template>
  <div :class="className">
    <div class="base-content">
      <Base :value="data" :dbType="dbType" />
      <div class="action-area">
        <custom-icon type="lu-icon-folder" @click="addGroup" />
        <custom-icon type="lu-icon-node" @click="addItem" />
        <custom-icon type="delete" @click="remove" />
      </div>
    </div>
    <!-- 子节点 -->
    <div
      class="rule-tree-item-children"
      v-if="data.children && data.children.length > 0"
    >
      <template v-for="(item, index) in data.children">
        <biz-rule-group
          :key="index"
          :value="item"
          v-if="item.role_type == 'group'"
        ></biz-rule-group>
        <Item :key="index" :value="item" v-if="item.role_type == 'item'"></Item>
      </template>
      <SvgLine ref="SvgLine" :list="data.children" />
    </div>
  </div>
</template>

<script>
import Base from '../Base';
import Line from './Line';
import ResizeObserver from 'resize-observer-polyfill';
export default {
  name: 'Item',
  inheritAttrs: false,
  components: { Base, SvgLine: Line },
  props: {
    value: {
      type: Object,
      default: () => {}
    },
    dbType: {
      type: String,
      default: 'ORACLE'
    }
  },
  data() {
    const { value } = this;
    return {
      data: value
    };
  },
  computed: {
    className() {
      const { data } = this;
      return [
        'rule-tree-item',
        data.isRoot && 'is-root-item',
        data.isLeaf && 'is-leaf-item',
        data.isFirst && 'is-first-item',
        data.isLast && 'is-last-item',
        data.hasChildren && 'has-children',
        data.hasRelation && 'has-relation',
        data.level && `rule-level-${data.level}`
      ].filter(item => item);
    }
  },
  created() {},
  mounted() {
    let lastHeight = this.$el.offsetHeight;
    this.ro = new ResizeObserver((entries, observer) => {
      const { contentRect } = entries[0];
      const { height } = contentRect || {};
      if (!height) return;
      if (height !== lastHeight) {
        // console.log('redraw svg-line!!!!!!!');
        this.$refs.SvgLine && this.$refs.SvgLine.draw();
        lastHeight = height;
      }
    });
    this.ro.observe(this.$el);
  },
  destroyed() {
    if (this.ro) {
      this.ro.unobserve(this.$el);
      this.ro = null;
    }
  },
  methods: {
    addGroup() {
      if (this.data.children) {
        this.data.children.push({
          role_type: 'group',
          item_order: 3,
          relation: 'OR',
          children: [
            {
              role_type: 'item',
              source_code: '',
              index_code: { key: '', label: '' },
              prev_operator: { key: '', label: '' },
              target_operator: { key: '', label: '' },
              target_value: '',
              id: _.uniqueId('init_')
            },
            {
              role_type: 'item',
              source_code: '',
              index_code: { key: '', label: '' },
              prev_operator: { key: '', label: '' },
              target_operator: { key: '', label: '' },
              target_value: '',
              id: _.uniqueId('init_')
            }
          ]
        });
      } else {
        this.$set(this.data, 'children', [
          {
            role_type: 'group',
            item_order: 3,
            relation: 'OR',
            children: [
              {
                role_type: 'item',
                source_code: '',
                index_code: { key: '', label: '' },
                prev_operator: { key: '', label: '' },
                target_operator: { key: '', label: '' },
                target_value: '',
                id: _.uniqueId('init_')
              },
              {
                role_type: 'item',
                source_code: '',
                index_code: { key: '', label: '' },
                prev_operator: { key: '', label: '' },
                target_operator: { key: '', label: '' },
                target_value: '',
                id: _.uniqueId('init_')
              }
            ]
          }
        ]);
      }
      this.$set(this.data, 'hasChildren', true);
      this.refreshData();
    },
    addItem() {
      if (this.data.children) {
        this.data.children.push({
          role_type: 'item',
          source_code: '',
          index_code: { key: '', label: '' },
          prev_operator: { key: '', label: '' },
          target_operator: { key: '', label: '' },
          target_value: '',
          id: _.uniqueId('init_')
        });
      } else {
        this.$set(this.data, 'children', [
          {
            role_type: 'item',
            source_code: '',
            index_code: { key: '', label: '' },
            prev_operator: { key: '', label: '' },
            target_operator: { key: '', label: '' },
            target_value: '',
            id: _.uniqueId('init_')
          }
        ]);
      }
      this.$set(this.data, 'hasChildren', true);
      this.refreshData();
    },
    remove() {
      const level = _.get(this.data, '_parent.level');
      const children = _.get(this.data, '_parent.children');
      // 最少一个item
      if (level == 0 && children && children.length <= 1) return;
      const res =
        children &&
        children.filter(item => {
          return item.id !== this.data.id;
        });
      this.$set(this.data._parent, 'children', res);
      // 删除组的时候 没有item时 同时删除关系节点
      if (
        this.data._parent.children.length == 0 &&
        this.data._parent.role_type == 'group'
      ) {
        const id = this.data._parent.id;
        const children = _.get(this.data._parent, '_parent.children');
        const res =
          children &&
          children.filter(item => {
            return item.id !== id;
          });
        this.$set(this.data._parent._parent, 'children', res);
      }
    },
    refreshData() {
      const loop = (item, level) => {
        // 处理新增节点
        item.level = level;
        item.id = _.uniqueId('init_');
        if (!item.children || item.children.length <= 0) {
          item.isLeaf = true;
        }
        if (item.children && item.children.length > 0) {
          item.children.forEach((child, index) => {
            if (
              child.role_type == 'item' &&
              child.children &&
              child.children.length > 0
            ) {
              child.hasChildren = true;
            }
            child._parent = item;
            child.item_order = index;
            loop(child, ++level - index);
          });
        }
      };
      loop(this.data, this.data.level);
    }
  },
  watch: {
    value(newVal) {
      this.data = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
.rule-tree-item {
  .base-content {
    position: relative;
    > .action-area {
      visibility: hidden;
      position: absolute;
      top: -6px;
      right: 8px;
      z-index: 10;
      .anticon {
        width: 20px;
        height: 20px;
        line-height: 22px;
        font-size: 12px;
        border-radius: 50%;
        background: #4cbb3a;
        color: #fff;
        // margin-left: 2px;
        cursor: pointer;
        &:first-child {
          background: #4db5f2;
        }
        &:last-child {
          background: #a1a1a1;
        }
      }
    }
    &:hover {
      > .action-area {
        visibility: visible;
      }
    }
  }
}
</style>