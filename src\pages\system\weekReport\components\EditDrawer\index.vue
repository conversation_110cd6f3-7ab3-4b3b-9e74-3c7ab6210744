<template>
  <a-drawer
    title="编辑"
    :width="600"
    :visible="visible"
    :drawerStyle="{ paddingBottom: '80px' }"
    wrapClassName="week-report-edit"
    @close="onCancel"
  >
    <a-spin :spinning="loading">
      <Form ref="baseForm" v-bind="baseInfoParams" :formData="baseData"> </Form>
    </a-spin>
    <!-- 按钮区域 -->
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 10
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="onCancel"
        >取消</a-button
      >
      <a-button
        :style="{ marginRight: '8px' }"
        @click="onOk('new')"
        type="primary"
        >重新生成</a-button
      >
      <a-button :style="{ marginRight: '8px' }" @click="onOk" type="primary"
        >覆盖</a-button
      >
    </div>
  </a-drawer>
</template>

<script>
import Moment from 'moment';
import Form from '@/components/Form';
import { weeklyReportSubmit } from '@/api/weekReport';
import config from './config';

export default {
  components: { Form },
  data() {
    this.config = config(this);
    return {
      visible: false,
      loading: false,
      data: {},
      baseInfoParams: {
        fields: this.config.baseFields,
        // fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        colon: true
      },
      baseData: {}
    };
  },
  mounted() {},
  created() {},
  computed: {},
  watch: {},
  provide() {},
  methods: {
    show(record = {}) {
      this.visible = true;
      this.data = record;
      this.baseData = {
        ...record,
        date: [new Moment(record.start_date), new Moment(record.end_date)]
      };
    },
    hide() {
      this.visible = false;
      this.data = {};
      this.baseData = {};
    },
    onCancel() {
      this.hide();
    },
    onOk(type) {
      const formData = this.$refs.baseForm.getData();
      const date = formData.date || [];
      let params = {
        ...formData,
        start_date: date[0].format('YYYY-MM-DD'),
        end_date: date[1].format('YYYY-MM-DD'),
        busi_type: 'save-report'
      };
      if (type !== 'new') {
        params.record_id = this.data.id;
      }
      // console.log(params);
      // 请求
      this.$showLoading();
      weeklyReportSubmit(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              useMessage: true,
              tips: `保存成功`
            });
            this.onCancel();
            this.$emit('success');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less">
.week-report-edit {
}
</style>
