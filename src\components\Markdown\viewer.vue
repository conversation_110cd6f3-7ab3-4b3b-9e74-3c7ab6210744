<template>
  <div :class="['custom-markdown-viewer', 'toastui-editor-md-preview']">
    <div class="custom-markdown-viewer-container toastui-editor-contents"></div>
  </div>
</template>

<script>
import markdown from 'markdown-it';
import hljs from 'highlight.js';
import '@toast-ui/editor/dist/toastui-editor.css';

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: String,
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    this.defaultOptions = {
      html: true, // Enable HTML tags in source
      xhtmlOut: false, // Use '/' to close single tags (<br />)
      breaks: false, // Convert '\n' in paragraphs into <br>
      langPrefix: 'language-', // CSS language prefix for fenced blocks
      linkify: true, // autoconvert URL-like texts to links
      typographer: true, // Enable smartypants and other sweet transforms
      highlight: (str, lang) => {
        var esc = this.md.utils.escapeHtml;
        // console.log(str, lang, hljs.getLanguage(lang), esc, hljs, 777)

        try {
          // if (!defaults._highlight) {
          //   throw 'highlighting disabled';
          // }

          if (lang && lang !== 'auto' && hljs.getLanguage(lang)) {
            return (
              '<pre class="hljs language-' +
              esc(lang.toLowerCase()) +
              '"><code>' +
              hljs.highlight(lang, str, true).value +
              '</code></pre>'
            );
          } else if (lang === 'auto') {
            var result = hljs.highlightAuto(str);

            /*eslint-disable*/
            console.log(
              'highlight language: ' +
                result.language +
                ', relevance: ' +
                result.relevance
            );

            return (
              '<pre class="hljs language-' +
              esc(result.language) +
              '"><code>' +
              result.value +
              '</code></pre>'
            );
          }
        } catch (__) {
          /**/
        }

        return '<pre class="hljs"><code>' + esc(str) + '</code></pre>';
      }
    };
    return {};
  },
  created() {
    this.init();
  },
  mounted() {
    this.format(this.value);
  },
  beforeDestroy() {},
  methods: {
    init() {
      this.md = markdown(Object.assign({}, this.defaultOptions, this.options))
        .use(require('markdown-it-abbr'))
        .use(require('markdown-it-container'), 'warning')
        .use(require('markdown-it-deflist'))
        .use(require('markdown-it-emoji'))
        .use(require('markdown-it-footnote'))
        .use(require('markdown-it-ins'))
        .use(require('markdown-it-mark'))
        .use(require('markdown-it-sub'))
        .use(require('markdown-it-sup'));

      // Beautify output of parser for html content
      this.md.renderer.rules.table_open = function() {
        return '<table class="table table-striped">\n';
      };
      // // Replace emoji codes with images
      // this.md.renderer.rules.emoji = function(token, idx) {
      //   return window.twemoji.parse(token[idx].content);
      // };
    },
    format(data) {
      let result = this.md.render(data || '');
      // console.log(result);
      this.$el.querySelector(
        '.custom-markdown-viewer-container'
      ).innerHTML = result;
    }
  },
  watch: {
    value(newVal, oldVal) {
      // console.log(newVal, this.data, 222);
      if (newVal !== this.data) {
        this.format(newVal);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.custom-markdown-viewer {
  /deep/ .custom-markdown-viewer-container {
    // h1,
    // .h1 {
    //   font-size: 36px;
    // }
    // h2,
    // .h2 {
    //   font-size: 30px;
    // }
    // h3,
    // .h3 {
    //   font-size: 24px;
    // }
    // h4,
    // .h4 {
    //   font-size: 18px;
    // }
    // h5,
    // .h5 {
    //   font-size: 14px;
    // }
    // h6,
    // .h6 {
    //   font-size: 12px;
    // }

    hr {
      margin-top: 20px;
      margin-bottom: 20px;
      border: 0;
      border-top: 1px solid #eee;
    }

    dt,
    b,
    strong {
      font-weight: bold;
    }

    dt,
    dd {
      line-height: 1.42857143;
      margin-bottom: 0;
    }

    p {
      margin: 0 0 10px;
    }

    img {
      max-width: 35%;
    }

    abbr[title],
    abbr[data-original-title] {
      cursor: help;
      border-bottom: 1px dotted #777;
    }

    blockquote {
      padding: 10px 20px;
      margin: 0 0 20px;
      font-size: 14px;
      border-left: 5px solid #eee;
    }

    code {
      padding: 2px 4px;
      font-size: 90%;
      color: #c7254e;
      background-color: #f9f2f4;
      border-radius: 4px;
    }

    pre {
      display: block;
      padding: 9.5px;
      margin: 0 0 10px;
      font-size: 13px;
      line-height: 1.42857143;
      color: #333;
      word-break: break-all;
      word-wrap: break-word;
      background-color: #f5f5f5;
      border: 1px solid #ccc;
      border-radius: 4px;

      code {
        padding: 0;
        font-size: inherit;
        color: inherit;
        white-space: pre-wrap;
        background-color: transparent;
        border-radius: 0;
      }
    }

    .table {
      width: 100%;
      max-width: 100%;
      margin-bottom: 20px;

      &.table-striped > tbody > tr:nth-child(odd) > td,
      &.table-striped > tbody > tr:nth-child(odd) > th {
        background-color: #f9f9f9;
      }

      > thead > tr > th,
      > tbody > tr > th,
      > tfoot > tr > th,
      > thead > tr > td,
      > tbody > tr > td,
      > tfoot > tr > td {
        padding: 8px;
        line-height: 1.42857143;
        vertical-align: top;
        border-top: 1px solid #ddd;
        border: 1px solid rgba(0, 0, 0, 0.1);
      }

      > thead > tr > th {
        border-top: none;
        background-color: #555;
        font-weight: 300;
        color: #fff;
      }
    }

    .warning {
      background-color: #ff8;
      padding: 20px;
      border-radius: 6px;
    }

    .footnotes {
      column-count: 2;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 0;
      margin-bottom: 0.6em;
      font-weight: 500;
    }

    .ProseMirror-trailingBreak {
      display: none;
    }
  }
}
</style>