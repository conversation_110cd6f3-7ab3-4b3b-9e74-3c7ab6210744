<template>
  <div class="post-audit-sql-page-list page-list-single">
    <Table ref="table" v-bind="tableParams || {}">
      <div slot="name" slot-scope="{ record, text }" class="instance-name">
        <Tag type="Env" :text="record.env.toUpperCase()" />
        <DbImg :value="record.db_type" :schemaName="text" :limit="16" />
      </div>
      <span slot="scheduler_status" slot-scope="{ record }">
        <a-switch
          v-if="record.scheduler_status !== null"
          checked-children="运行"
          un-checked-children="停止"
          :checked="record.scheduler_status === 1"
          @change="onChange(record)"
        />
      </span>
      <div slot="running_status" slot-scope="{ text,record }">
        <span v-if="text == 0">{{'一直运行'}}</span>
        <span v-else-if="text == 1">{{'每天' + record.run_start_time + '-' + record.run_end_time}}</span>
      </div>
      <template slot="created_by" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_creater">
          <template slot="title">
            <span>{{'用户名：' + text}}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{record.ch_creater}}</span>
          </div>
        </a-tooltip>
        <span v-else>{{text || '--'}}</span>
      </template>
      <custom-btns-wrapper slot="action" slot-scope="{ text, record }" :limit="3">
        <a @click="toDetail(text, record, $event)" actionBtn>查看报告</a>
        <a @click="addProject(record)" v-if="!isDev" actionBtn>编辑</a>
        <a-popconfirm
          v-if="!isDev"
          title="确定删除?"
          @confirm="() => del('delete', record.id)"
          actionBtn
        >
          <a class="remove">删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </Table>
    <!-- <Audit ref="audit" @refresh="refreshKeep"></Audit> -->
    <!-- 新建项目弹窗 -->
    <AddModal ref="addModal" @save="save"></AddModal>
  </div>
</template>

<script>
import Tag from '@/components/Biz/Tag';
import Table from '@/components/Table';
import SearchArea from '@/components/SearchArea';
import Audit from './components/AuditModel';
import AddModal from '@/pages/home/<USER>/index/components/AddModal.vue';
import DbImg from '@/components/CustomImg/DbImg';
import { afterwardsReview, afterwardsPostgreReview } from '@/api/home';

import config from './config';

export default {
  components: { Table, SearchArea, Audit, AddModal, DbImg, Tag },
  props: {},
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: 'sqlreview/review/afterwards_review',
        reqParams: {
          db_type: 'SQLSERVER'
        },
        columns: this.config.columns,
        rowKey: 'id',
        searchFields: this.config.searchFields,
        needTools: true,
        scroll: { x: 1450 },
        needSearchArea: true
      },
      id: null
    };
  },
  mounted() {},

  computed: {
    isDev() {
      const user = this.$store.state.account.user || {};
      return user.role === 'developer';
    }
  },
  created() {},
  methods: {
    // 新建项目
    addProject(record) {
      this.id = record && record.id;
      this.$refs.addModal.show(record, 'SQLSERVER');
    },
    toDetail(text, record, e) {
      this.$store.commit('common/setPageCache', {
        homePostaudit: { dbType: record.db_type }
      });
      this.$router.push({
        name: 'home-postaudit-sqlserver-report',
        query: {
          db_type: record.db_type,
          task_id: record.id,
          name: record.name,
          env: record.env
        }
      });
    },
    onChange(record) {
      const params = {
        id: record.id,
        scheduler_status: record.scheduler_status ? 0 : 1
      };
      this.save('put', params);
    }, // 请求函数
    save(method, data, flag, dbType) {
      const { addModal, table } = this.$refs;
      // 请求
      this.$showLoading();
      if (this.id) {
        data.id = this.id;
      }
      const reqInstance = ['POSTGRE', 'SQLSERVER'].includes(dbType)
        ? afterwardsPostgreReview
        : afterwardsReview;
      reqInstance(method, data)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            if (!flag) {
              addModal.hide();
            }
            // this.id = null;
            table.refresh();
          } else {
            // addModal.hide();
            // this.id = null;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(() => {
          // addModal.hide();
          // this.id = null;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 删除
    del(method, id) {
      this.save(method, { id }, true);
    },
    refreshKeep() {
      this.$refs.table.refreshKeep();
    }
  }
};
</script>

<style lang="less" scoped>
.instance-name {
  display: flex;
  align-items: center;
  .ant-tag {
    border-radius: 6px;
  }
}
</style>
