<template>
  <a-tabs class="page-help custom-pure-bg" type="card" v-model="activeKey">
    <a-tab-pane key="common" tab="公用">
      <a-tabs tabPosition="left" v-model="commonActiveKey" @change="onChange">
        <a-tab-pane v-for="(item) in params.tabs" :key="item.key" :tab="item.title">
          <component :is="item.key" />
        </a-tab-pane>
      </a-tabs>
    </a-tab-pane>
    <a-tab-pane key="private" tab="私有">
      <Private />
    </a-tab-pane>
  </a-tabs>
  <!-- <PageList v-bind="params">
    <Panel1 slot="p0"></Panel1>
    <Panel2 slot="p1"></Panel2>
    <Panel3 slot="p2"></Panel3>
    <Panel4 slot="p3"></Panel4>
    <Panel5 slot="p4"></Panel5>
    <Panel6 slot="p5"></Panel6>
    <Panel7 slot="p6"></Panel7>
    <Panel8 slot="p7"></Panel8>
    <Panel9 slot="p8"></Panel9>
    <Panel10 slot="p9"></Panel10>
    <Panel11 slot="p10"></Panel11>
    <Panel12 slot="p11"></Panel12>
    <Panel13 slot="p12"></Panel13>
  </PageList>-->
</template>

<script>
// import { homeReqTest } from '@/api/home';
// import PageList from '@/components/PageList';
import Private from './private';
import PageList from './components/PageList';
import PageListAllConfig from './components/PageListAllConfig';
import LimitLabel from './components/LimitLabel';
import TableEdit from './components/TableEdit';
import Form from './components/Form';
import SearchArea from './components/SearchArea';
import HTree from './components/HTree';
import RelationGroup from './components/RelationGroup';
import Chart from './components/Chart';
import Flow from './components/Flow';
import Schema from './components/Schema';
import Markdown from './components/Markdown';
import RichEditor from './components/RichEditor';
import Prettier from './components/Prettier';
import Actions from './components/Actions';
import Shower from './components/Shower';
import Tabs from './components/Tabs';
import Blocks from './components/Blocks';
import InputSearch from './components/InputSearch';
import FormTriggerComp from './components/FormTriggerComp';
import Diff from './components/Diff';

// import common from '@/utils/common';

export default {
  components: {
    Private,
    PageList,
    // Panel1,
    LimitLabel,
    PageListAllConfig,
    TableEdit,
    Form,
    SearchArea,
    HTree,
    RelationGroup,
    Chart,
    Flow,
    Schema,
    Markdown,
    RichEditor,
    Prettier,
    Actions,
    Shower,
    Tabs,
    Blocks,
    InputSearch,
    FormTriggerComp,
    Diff
  },
  props: {},
  data() {
    return {
      activeKey: 'common',
      commonActiveKey: 'PageList',
      params: {
        title: '测试页',
        tabs: [
          {
            title: 'pageList通用组件',
            key: 'PageList'
          },
          {
            title: 'pageList全配置化',
            key: 'PageListAllConfig'
          },
          {
            title: '可编辑表格',
            key: 'TableEdit'
          },
          {
            title: '通用表单',
            key: 'Form'
          },
          {
            title: '搜索区域',
            key: 'SearchArea'
          },
          {
            title: '横向tree',
            key: 'HTree'
          },
          {
            title: '关系组',
            key: 'RelationGroup'
          },
          {
            title: 'chart',
            key: 'Chart'
          },
          {
            title: '流程图',
            key: 'Flow'
          },
          {
            title: 'schema',
            key: 'Schema'
          },
          {
            title: 'markdown',
            key: 'Markdown'
          },
          {
            title: '富文本编辑器',
            key: 'RichEditor'
          },
          {
            title: '文本格式化',
            key: 'Prettier'
          },
          {
            title: '按钮组组件',
            key: 'Actions'
          },
          {
            title: 'Shower',
            key: 'Shower'
          },
          {
            title: 'Tabs',
            key: 'Tabs'
          },
          {
            title: 'Blocks',
            key: 'Blocks'
          },
          {
            title: '搜索组件',
            key: 'InputSearch'
          },
          {
            title: 'LimitLabel',
            key: 'LimitLabel'
          },
          {
            title: '单个表单编辑',
            key: 'FormTriggerComp'
          },
          {
            title: 'Diff',
            key: 'Diff'
          }
        ]
      }
    };
  },
  mounted() {
    // 请求、全局loading测试
    // this.$showLoading();
    // homeReqTest({
    //   a: 'ssss'
    // })
    //   .then(res => {
    //     // this.$hideLoading();
    //   })
    //   .catch(e => {
    //     console.log(e);
    //   });
  },
  created() {},
  methods: {
    onChange() {
      window.LAYOUT_ROOT.scrollTo(0);
    }
  }
};
</script>

<style lang="less" scoped>
#layout-root.fresh {
  .page-help {
    padding: 12px 16px;
  }
}
</style>
