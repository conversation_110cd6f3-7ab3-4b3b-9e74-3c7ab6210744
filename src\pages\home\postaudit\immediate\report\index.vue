<template>
  <div class="immediate-report">
    <div>
      <div class="instance-box">
        <InstanceItem v-if="!!db_type" :tagText="env" :src="db_type" :text="name"></InstanceItem>
        <div class="check-report-default-time">
          <a-range-picker
            v-model="dateModel"
            :show-time="{
              hideDisabledOptions: true,
              defaultValue: [
                moment('00:00:00', 'HH:mm:ss'),
                moment('11:59:59', 'HH:mm:ss')
              ]
            }"
            :disabledDate="disabledDate"
            :format="dateFormat"
            @ok="rangePickerChange"
          />
        </div>
      </div>
      <div class="report-wrapper">
        <Tabs
          v-model="activeKey"
          :tabsList="tabsList"
          :animated="false"
          mode="tag"
          @change="tabChange"
          class="report-wrapper-tabs"
        />
        <commonView
          ref="commonView"
          :chartList="chartList"
          :commonViewLoading="loading"
          @onClickLine="onClickLine"
          @onClickxAxisTwice="onClickxAxisTwice"
        />
      </div>
    </div>
    <div>
      <Table ref="table" v-bind="tableParams || {}" :dataSource="dataSource" @reset="reset">
        <template slot="STMT_TEXT" slot-scope="{ text }">
          <LimitLabel :label="text || ''" :limit="16" format="sql"></LimitLabel>
        </template>
        <!-- 审核结果 -->
        <template slot="INSERT_TIMESTAMP" slot-scope="{ record }">
          <span>{{ moment(record.INSERT_TIMESTAMP).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
        <template slot="created_at" slot-scope="{ record }">
          <span>{{ moment(record.created_at).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
        <span slot="ai_status" slot-scope="{ record }">
          <span v-if="record.ai_status === null">--</span>
          <a-badge :color="record.ai_status | color" :text="record.ai_status | status" />
        </span>
        <span slot="action" slot-scope="{ text, record }">
          <a @click="toDetail(text, record, $event)">详情</a>
        </span>
      </Table>
    </div>
  </div>
</template>

<script>
import Table from '@/components/Table';
import Chart from '@/components/Chart';
import commonView from './commonView/index';
import Tabs from '@/components/Tabs';
import LimitLabel from '@/components/LimitLabel';
import InstanceItem from '@/components/Biz/InstanceItem';
import { getDb2Chart } from '@/api/immediate';
import moment from 'moment';
import common from '@/utils/common';

import config from './config';
export default {
  name: 'immediateReport',
  components: {
    Tabs,
    Table,
    Chart,
    commonView,
    InstanceItem,
    LimitLabel
  },
  props: {},
  data() {
    this.config = config(this);
    const timeData = this.getTime();
    const startTime = timeData[0];
    const endTime = timeData[1];
    // keep-alive是否激活
    this.activated = null;
    return {
      data: {},
      id: this.$route.query.id || '',
      db_type: this.$route.query.db_type || '',
      name: this.$route.query.name || '',
      env: (this.$route.query.env || '').toUpperCase(),
      tableParams: {
        url: '/sqlreview/real_time/db2_detail_list',
        method: 'post',
        reqParams: {
          data_source_id: this.$route.query.id,
          start_time: startTime,
          end_time: endTime
        },
        columns: this.config.columns,
        rowKey: 'id',
        // isInitReq: false,
        // needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 'max-content' }
      },
      dataSource: [],
      activeKey: this.$route.query.activeKey || 'STMT_EXEC_TIME',
      tabsList: [],
      loading: false,
      chartList: null,
      dateModel: [
        moment(this.startTime, 'YYYY-MM-DD HH:mm:ss'),
        moment(this.endTime, 'YYYY-MM-DD HH:mm:ss')
      ],
      dateFormat: ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss'],
      startTime: startTime,
      endTime: endTime,
      clickTime: null
      // rangePickerStartTime: '',
      // rangePickerEndTime: ''
    };
  },
  mounted() {
    this.setNavi();
    this.getCheckData(this.activeKey);
  },
  created() {},
  beforeDestroy() {},
  activated() {
    if (this.activated === false) {
      this.$refs.table.refreshKeep();
      this.activated = true;
    }
  },
  deactivated() {
    this.activated = false;
  },
  methods: {
    moment,
    onClickLine(data) {
      this.clickTime = data;
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        click_time: data,
        start_time: '',
        end_time: ''
      });
      table.refresh();
    },
    onClickxAxisTwice(data) {
      this.clickTime = '';
      // this.startTime = data[0];
      // this.endTime = data[1];
      const { table } = this.$refs;
      const { searchParams } = table;
      // x轴先后点击两个点获取时间戳，计算两个时间戳相差毫秒数
      // res>0 说明data[0]比data[1]大 反之data[0]比data[1]小
      // start_time为时间小的，end_time为时间大的
      const res = moment(data[0]).diff(moment(data[1]), 'x');
      Object.assign(searchParams, {
        click_time: '',
        start_time: res < 0 ? data[0] : data[1],
        end_time: res < 0 ? data[1] : data[0]
      });
      table.refresh();
    },
    getTime(bool = true) {
      // const pageCache = this.$store.state.common.pageCache.immediateReport;
      // if (bool && !_.isEmpty(pageCache)) {
      //   this.startTime = pageCache.startTime;
      //   this.endTime = pageCache.endTime;
      // } else {
      //   this.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      //   this.startTime = moment()
      //     .subtract(12, 'hour')
      //     .format('YYYY-MM-DD HH:mm:ss');
      // }
      this.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      this.startTime = moment()
        .subtract(12, 'hour')
        .format('YYYY-MM-DD HH:mm:ss');
      return [this.startTime, this.endTime];
    },
    // 请求查看报告chart数据
    getCheckData(param) {
      getDb2Chart({
        data_source_id: this.id,
        start_time: this.startTime,
        end_time: this.endTime,
        index_type: param
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            if (_.isEmpty(this.tabsList)) {
              this.tabsList = res.data.data.header || [];
            }
            this.chartList = res.data.data.chart_list || [];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    // chart图表默认时间搜索
    rangePickerChange(values) {
      const start = moment(values[0]).format('YYYY-MM-DD HH:mm:ss');
      const end = moment(values[1]).format('YYYY-MM-DD HH:mm:ss');
      this.startTime = start;
      this.endTime = end;
      // this.rangePickerStartTime = start;
      // this.rangePickerEndTime = end;
      // this.$store.commit('common/setPageCache', {
      //   immediateReport: { startTime: start, endTime: end }
      // });
      this.dateModel = values;
      this.getCheckData(this.activeKey);
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        click_time: '',
        start_time: this.startTime,
        end_time: this.endTime
      });
      table.refresh();
    },
    // 切换 tab 选项卡
    tabChange(activeKey) {
      this.activeKey = activeKey;
      this.getCheckData(activeKey);
      this.clickTime = '';
      this.reset(false);
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        click_time: '',
        start_time: this.startTime,
        end_time: this.endTime
      });
      table.refresh();
    },
    toDetail(text, record, e) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        click_time: this.clickTime,
        start_time: this.startTime,
        end_time: this.endTime
      });
      this.$router.push({
        name: 'immediateReportPlan',
        query: {
          id: record.id,
          start_time: this.startTime,
          end_time: this.endTime,
          ai_status: searchParams.ai_status,
          activeKey: this.activeKey,
          click_time: this.clickTime
        }
      });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      table.refresh(null);
    },
    // 重置
    async reset(bool) {
      if (bool !== false) await this.getTime(false);
      this.dateModel = [
        moment(this.startTime, 'YYYY-MM-DD HH:mm:ss'),
        moment(this.endTime, 'YYYY-MM-DD HH:mm:ss')
      ];
      this.getCheckData(this.activeKey);
      this.clickTime = '';
      const commonView = this.$refs.commonView;
      commonView.reset();
    },
    // 不可选择日期
    disabledDate(current) {
      return current && current > moment().endOf('day');
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'immediateReport') {
          path =
            sourcePath +
            '?id=' +
            this.id +
            '&db_type=' +
            this.db_type +
            '&name=' +
            this.name +
            '&env=' +
            this.env;
        }
        return path;
      });
    }
  },
  watch: {},
  filters: {
    status(value) {
      let obj = {
        0: '未知',
        1: '通过',
        '-1': '未通过',
        2: '白名单通过',
        9: '错误'
      };
      return obj[value];
    },
    color(value) {
      let obj = {
        0: '#B0AEAE',
        1: '#52C41A',
        '-1': '#FF4D4F',
        2: '#52C41A',
        9: '#FF4D4F'
      };
      return obj[value];
    }
  }
};
</script>

<style lang="less" scoped>
.instance-box {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 0 16px 0;
  /deep/ .database-image .iconStyle {
    font-size: 24px;
    padding-top: 0;
  }
  /deep/.limit-label.ellipsis > pre {
    font-size: 16px;
    vertical-align: middle;
  }
}
.report-wrapper {
  .report-wrapper-tabs {
    /deep/.ant-tabs-tab {
      padding: 5px 12px;
      line-height: 24px;
      font-size: 14px;
      background: #f2f4f5 !important;
      border-radius: 8px;
      border: 2px solid #fff;
    }
    /deep/
      .ant-tabs-bar
      > .ant-tabs-nav-container
      > .ant-tabs-nav-wrap
      > .ant-tabs-nav-scroll
      > .ant-tabs-nav
      > div
      > .ant-tabs-tab-active {
      padding: 5px 12px;
      line-height: 24px;
      font-size: 14px;
      color: rgba(255, 255, 255) !important;
      background: #1890ff !important;
      border-radius: 8px;
      border: 2px solid #fff;
    }
  }
}
</style>
