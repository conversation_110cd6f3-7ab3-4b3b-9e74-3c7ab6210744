<template>
  <div class="history-of-statistics-info">
    <div class="table-content">
      <div class="top-info">
        <Form
          ref="form"
          v-bind="formParams"
          :formData="formData"
          :iconCombine="true"
          class="search-form"
        ></Form>
        <div class="seach-area-btns">
          <a-button @click="search">查询</a-button>
          <a-tooltip>
            <template slot="title">重置</template>
            <custom-icon type="lu-icon-clean" @click="reset" />
          </a-tooltip>
        </div>
      </div>
      <Table
        ref="table"
        v-bind="tableParams"
        :dataSource="dataSource || []"
        class="new-view-table small-size"
      >
        <LimitLabel
          slot="sql_id"
          slot-scope="{ text }"
          :label="text"
          :limit="24"
        ></LimitLabel>
        <LimitLabel
          slot="sql_text"
          slot-scope="{ text }"
          :label="text"
          :limit="24"
        ></LimitLabel>

        <template slot="source_type" slot-scope="{ text, record }">
          <div v-if="text == 'file'">
            <custom-icon type="paper-clip" />
            <LimitLabel :label="record.source || '--'" :limit="24"></LimitLabel>
          </div>
          <div v-else class="source-display">
            <a-popover>
              <template slot="content">
                <span>{{ record.source_db_url }}</span>
              </template>

              <Tag type="Env" :text="record.source_env.toUpperCase()" />
              <DbImg
                :value="record.source_db_type"
                :schemaName="record.source"
                :limit="16"
              />
            </a-popover>
          </div>
        </template>
        <template slot="target_type" slot-scope="{ text, record }">
          <div v-if="text == 'file'">
            <custom-icon type="paper-clip" />
            <LimitLabel :label="record.target || '--'" :limit="24"></LimitLabel>
          </div>
          <div v-else class="target-display">
            <a-popover>
              <template slot="content">
                <span>{{ record.target_db_url }}</span>
              </template>

              <Tag type="Env" :text="record.target_env.toUpperCase()" />
              <DbImg
                :value="record.target_db_type"
                :schemaName="record.target"
                :limit="16"
              />
            </a-popover>
          </div>
        </template>
        <template slot="status" slot-scope="{ text }">
          <span :class="['status-info', `status-${text}`]">{{
            statusText[text]
          }}</span>
        </template>
        <custom-btns-wrapper slot="action" slot-scope="{ record }">
          <a actionBtn @click="toDetail(record)" :disabled="record.status == 0"
            >详情</a
          >
          <a
            actionBtn
            v-if="record.target_type == 'file'"
            @click="download(record)"
            :disabled="record.status == 0"
            >下载</a
          >
        </custom-btns-wrapper>
      </Table>
    </div>
    <Detail ref="detail" />
  </div>
</template>

<script>
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import Form from '@/components/Form';
import Table from '@/components/Table';
import Detail from './components/Detail';
import LimitLabel from '@/components/LimitLabel';
import InstanceItem from '@/components/Biz/InstanceItem';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import common from '@/utils/common';
import config from './config';
import { downloadSqlFile } from '@/api/config/dataSource';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Tag,
    Form,
    DbImg,
    Table,
    Detail,
    InstanceItem,
    LimitLabel
  },
  props: {
    pane: Object,
    id: Number | String
  },
  data() {
    this.config = config(this);
    return {
      formData: {},
      formParams: {
        layout: 'horizontal',
        multiCols: 4,
        fields: this.config.historySearchFields()
      },
      dataSource: [],
      tableParams: {
        url: '/sqlreview/project/oracle_stat_history_list',
        reqParams: {},
        columns: this.config.historyColumns,
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      statusText: {
        0: '执行中',
        1: '成功',
        2: '失败'
      }
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    // 查询
    search() {
      const { table, form } = this.$refs;
      const data = form.getData();
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    },
    // 重置
    reset() {
      this.formData = {};
      const { table } = this.$refs;
      table.refresh();
    },
    toDetail(record) {
      this.$refs.detail.show(record);
    },
    download(record) {
      const params = { id: record.id, type: 'batch' };
      this.$showLoading({
        tips: `下载中...`
      });
      downloadSqlFile(params)
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.history-of-statistics-info {
  /deep/.table-content {
    background: #fff;
    border-radius: 16px;
    .top-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0;
      .ant-form {
        flex-grow: 1;
        height: 48px;
        padding-right: 24px;
        overflow: hidden;
        * {
          transition: none;
        }
        > .ant-row {
          > .ant-col {
            vertical-align: top;
            padding-left: 12px !important;
            padding-right: 12px !important;
            .ant-form-item {
              display: flex;
              margin: 0 0 8px 0;
              .ant-form-item-label {
                display: none;
              }
              .ant-form-item-control-wrapper {
                flex-grow: 1;
                flex: auto;
                width: auto;
                // max-width: 240px;
                // min-width: 160px;
              }
            }
          }
        }
      }
      .seach-area-btns {
        padding-top: 4px;
        white-space: nowrap;
        .ant-btn {
          padding: 2px 12px;
          border-radius: 4px !important;
          border: 1px solid #a5d9f8;
          > span {
            font-size: 14px;
            color: #008adc;
            font-weight: 400;
          }
        }
        .anticon {
          font-size: 16px;
          margin: 0 12px;
          border-radius: 50%;
          &:hover {
            color: #000;
            cursor: pointer;
          }
        }
      }
      &.icon-combine {
        .ant-form {
          > .ant-row {
            > .ant-col {
              padding-left: 24px !important;
              padding-right: 0 !important;
              .ant-form-item {
                .ant-form-item-label {
                  max-width: 0;
                  min-width: 0;
                }
                .ant-form-item-control-wrapper {
                  flex-grow: 1;
                  flex: auto;
                  width: auto;
                  max-width: 100%;
                  min-width: 310px;
                }
              }
            }
          }
        }
        .seach-area-btns {
          margin-left: 24px;
        }
      }
    }
    .source-display,
    .target-display {
      padding: 6px 8px;
      border: 1px solid rgba(228, 228, 231, 1);
      border-radius: 4px;
      // width: 270px;
      > span {
        display: flex;
        align-items: center;
      }
      .ant-tag {
        margin-right: 0;
        padding: 0 7px 0 0;
        border: none;
        font-size: 14px;
        color: #a1a1aa;
        font-weight: 400;
        background-color: #fff !important;
      }
      .database-image {
        .iconClass {
          .limit-label {
            pre {
              font-size: 14px;
              color: #27272a;
              font-weight: 400;
            }
          }
        }
      }
    }
    .status-info {
      width: 56px;
      height: 28px;
      font-size: 12px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.status-0 {
        background-color: rgba(242, 147, 57, 0.17);
        color: rgba(153, 27, 27, 0.5);
      }
      &.status-1 {
        color: #3a974c;
        background: #ecf5ee;
      }
      &.status-2 {
        color: #e71d36;
        background: #fdeff1;
      }
    }
  }
}
@media screen and (max-width: 1640px) {
  .history-of-statistics-info {
    .search-area {
      .ant-form {
        > .ant-row {
          > .ant-col {
            width: 50%;
          }
        }
      }
    }
  }
}
</style>