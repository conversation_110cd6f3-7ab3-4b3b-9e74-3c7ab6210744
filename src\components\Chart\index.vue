<template>
  <div :class="className" :style="{ width: '100%', height: '100%' }">
    <div class="chart-container" :style="{ width: '100%', height: '100%' }" v-show="option"></div>
    <custom-empty class="custom-chart-empty" v-show="!option" />
  </div>
</template>

<script>
import ResizeObserver from 'resize-observer-polyfill';
export default {
  components: {},
  props: {
    option: [Object, Function],
    // 延迟（抽屉里的chart需要延迟）
    delay: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {};
  },
  mounted() {
    const { option } = this;
    this.setOption(option);

    // 监听resize
    let refresh = _.debounce(() => {
      _.isFunction(this.option) && this.setOption(this.option);
    }, 300);
    this.ro = new ResizeObserver((entries, observer) => {
      if (!this.$el.offsetWidth || !option) return;
      this.chart && this.chart.resize();
      // 最后在刷新一遍
      refresh();
    });
    this.ro.observe(this.$el);
  },
  destroyed() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    if (this.ro) {
      this.ro.unobserve(this.$el);
      this.ro = null;
    }
  },
  created() {},
  computed: {
    className() {
      return ['custom-chart'];
    }
  },
  methods: {
    setOption(option) {
      if (!option) return;
      setTimeout(() => {
        if (!this.chart) {
          this.chart = this.$echarts.init(
            this.$el.querySelector('.chart-container')
          );
        }
        // 更新下视图，再setOption
        // this.chart.resize();
        let _option = _.isFunction(option) ? option() : option;
        this.chart.setOption(_option, true);
        this.$emit('optionLoaded');
      }, this.delay);
    },
    on(event, func) {
      if (event && func) {
        // 防止重复注册
        this.off(event);
        this.chart &&
          this.chart.on(event, e => {
            func(e);
          });
      }
    },
    off(event, handler) {
      this.chart && this.chart.off(event, handler);
    },
    exec(method, ...args) {
      if (this.chart && _.isFunction(this.chart[method])) {
        this.chart[method](...args);
      }
    }
  },
  watch: {
    option(newVal) {
      if (newVal) {
        this.setOption(newVal);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.custom-chart {
}
.custom-chart-empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
}
</style>
