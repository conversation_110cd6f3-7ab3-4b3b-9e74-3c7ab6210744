<template>
  <a-drawer
    placement="right"
    :maskClosable="true"
    :closable="true"
    :visible="visible"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="data-base-audit-oracle-setting"
    @close="onClose"
    width="720px"
  >
    <a-spin tip="加载中" :spinning="spinning" class="oracle-setting">
      <div class="review-header">
        <h4>设置</h4>
      </div>

      <a-card type="small" :bordered="false">
        <div class="title">
          <a-icon type="flag" />
          <span style="margin-left: 4px">选择SQL模板</span>
        </div>
        <div class="setting-options">
          <a-radio-group v-model="value" @change="onChange">
            <a-radio-button
              v-for="item in setOptions"
              :key="item.value"
              :value="item.value"
              >{{ item.label }}</a-radio-button
            >
          </a-radio-group>
        </div>
      </a-card>
      <!-- 执行计划 -->
      <a-card type="small" :bordered="false">
        <div class="title">
          <custom-icon type="lu-icon-code" />
          <span style="margin-left: 4px">SQL执行文本</span>
        </div>
        <div v-if="sqlPlan">
          <Coder
            v-model="sqlPlan[value]"
            type="txt"
            :needFormat="true"
            :options="options"
          ></Coder>
        </div>
        <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>
      </a-card>
    </a-spin>

    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 10
      }"
    >
      <a-button @click="onClose">关闭</a-button>
      <a-button @click="onSave" type="primary">保存</a-button>
    </div>
  </a-drawer>
</template>
<script>
import Coder from '@/components/Coder';
import {
  getOracleProcess,
  getDB2Process,
  getGaussdbProcess,
  getPostgresqlProcess,
  savePostgresqlProcess
} from '@/api/databaseaudit/realtime';
export default {
  components: {
    Coder
  },
  props: {
    id: Number | String,
    sqlPlanInfo: {
      type: Array,
      default: () => []
    },
    dbType: String
  },
  data() {
    return {
      visible: false,
      spinning: false,
      sqlPlan: {},
      options: {
        readOnly: !this.canEditTemplate()
      },
      setOptions: [],
      value: undefined
    };
  },
  created() {},
  mounted() {},
  watch: {},
  destroyed() {},
  computed: {},
  methods: {
    canEditTemplate() {
      let user = this.$store.state.account.user || {};
      return (
        ['POSTGRE'].includes(this.dbType) &&
        ['dba', 'admin'].includes(user.role)
      );
    },
    show() {
      this.sqlPlan = {};
      this.visible = true;
      this.spinning = true;
      let req;
      if (this.dbType == 'ORACLE') {
        req = getOracleProcess;
      } else if (this.dbType == 'DB2') {
        req = getDB2Process;
      } else if (this.dbType == 'GAUSSDB') {
        req = getGaussdbProcess;
      } else {
        req = getPostgresqlProcess;
      }

      req()
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.setOptions = _.get(res, 'data.data');
            const obj = this.setOptions[0];
            const sqlId = window.localStorage.getItem(
              `${this.dbType}RealTimeSqlId`
            );
            this.value = !_.isEmpty(sqlId) ? Number(sqlId) : obj.value;
            this.setOptions.forEach(item => {
              this.sqlPlan[item.value] = item.sql;
            });
            this.spinning = false;
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    onChange(e) {
      this.value = e.target.value;
    },
    onClose() {
      this.visible = false;
    },
    onSave() {
      this.saveReq();
      window.localStorage.setItem(`${this.dbType}RealTimeSqlId`, this.value);
      this.visible = false;
    },
    saveReq() {
      if (!this.canEditTemplate()) return;
      let req = savePostgresqlProcess;
      // if (this.dbType == 'ORACLE') {
      //   req = getOracleProcess;
      // } else if (this.dbType == 'DB2') {
      //   req = getDB2Process;
      // } else if (this.dbType == 'GAUSSDB') {
      //   req = getGaussdbProcess;
      // } else {
      //   req = getPostgresqlProcess;
      // }

      req({
        id: this.value,
        sql: this.sqlPlan[this.value]
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>
<style lang="less" scoped>
.oracle-setting {
  color: rgba(86, 87, 89, 1);
  /deep/.ant-spin-container {
    background: #fff;
    .review-header {
      display: flex;
      align-items: center;
      padding: 0;
      > h4 {
        font-family: PingFangSC-Semibold;
        font-size: 20px;
        color: #27272a;
        font-weight: 600;
        margin-right: 32px;
        margin-bottom: 0;
      }
    }
    .ant-card {
      .ant-card-body {
        padding: 32px 0 24px 0;
        padding-bottom: 0;
        border-radius: 12px;
        .title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 8px;
          .anticon {
            font-size: 16px;
            color: #333333;
          }
          > span {
            font-family: PingFangSC-Semibold;
            font-size: 14px;
            font-weight: 600;
          }
        }
        .setting-options {
          .ant-radio-group {
            .ant-radio-button-wrapper {
              margin: 8px 12px 8px 0;
            }
          }
        }
        .custom-coder {
          .CodeMirror {
            height: 330px !important;
          }
        }
      }
    }
    .ai-comment-part {
      height: 330px;
      padding: 16px;
      background-color: #2b2b2b;
      border-radius: 5px;
      > span {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #a9b7c6;
        font-weight: 400;
      }
    }
  }
}
</style>
