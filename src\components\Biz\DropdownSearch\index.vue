<template>
  <div class="dropdown-search-area">
    <div class="dropdown-search-area-box">
      <div v-for="item in dataList" :key="item.key" class="dropdown-search-area-box-item">
        <span class="title">{{item.title}}</span>
        <a-dropdown :trigger="['click']">
          <a class="ant-dropdown-link" @click="e => e.preventDefault()">
            {{item.default}}
            <a-icon type="down" />
          </a>
          <a-menu slot="overlay" @click="(e) => onDropdownClick(e, item.key, item)">
            <a-menu-item v-for="(elem, index) in item.options" :key="index">
              <a href="javascript:;">{{elem.label}}</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
      </div>
    </div>
    <div>
      <InputSearch :placeholder="placeholder" @search="search"></InputSearch>
    </div>
  </div>
</template>

<script>
import InputSearch from '@/components/InputSearch';
export default {
  props: {
    DropdownSearchList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    placeholder: {
      type: String,
      default: '请输入'
    }
  },
  components: { InputSearch },
  data() {
    return {};
  },
  computed: {
    dataList() {
      return this.DropdownSearchList;
    }
  },
  mounted() {},
  destroyed() {},
  methods: {
    onDropdownClick(e, type, data) {
      let emitData = {};
      this.DropdownSearchList.forEach(item => {
        if (item.key == type) {
          item.options.forEach((elem, i) => {
            if (i === e.key) {
              item.default = elem.label;
              emitData[type] = elem.value;
            }
          });
        }
      });
      this.$emit('dropdownSearch', emitData);
    },
    search(params) {
      this.$emit('search', params);
    }
  }
};
</script>

<style lang="less" scoped>
.dropdown-search-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .dropdown-search-area-box {
    display: flex;
    .dropdown-search-area-box-item {
      margin-right: 16px;
      > span {
        margin-right: 4px;
      }
      .title {
        font-size: 12px;
        color: #8c8c8c;
        line-height: 22px;
        font-weight: 400;
      }
    }
  }
}
</style>