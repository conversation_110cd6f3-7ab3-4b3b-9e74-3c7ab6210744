<template>
  <div class="config-system-email">
    <Form ref="form" v-bind="params" :formData="formData"></Form>
  </div>
</template>

<script>
import Form from '@/components/Form';
import { Base64 } from 'js-base64';
export default {
  components: { Form },
  props: {
    data: Object
  },
  data() {
    return {
      inputValue: null,
      id: null,
      params: {
        fields: [
          {
            type: 'Input',
            label: '邮箱服务器地址',
            key: 'email_address',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'change' }
            ]
          },
          {
            type: 'Input',
            label: '发送人邮件',
            key: 'send_email',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'change' }
            ]
          },
          {
            type: 'InputPassword',
            label: '发件人邮箱密码',
            key: 'email_password',
            props: {
              placeholder: '填写会更新密码'
            },
            rules: []
          },
          {
            type: 'Input',
            label: '测试邮件收件邮箱',
            key: 'receive_email',
            rules: []
          }
        ],
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 8 },
        wrapperCol: { span: 16 }
      },
      formData: {}
    };
  },
  watch: {
    data: {
      handler(val) {
        const res = _.get(val, 'sql_review_dynamic_form_element');
        const value = {};
        const ids = [];
        res.forEach(item => {
          value[item.element_label] = item.element_name;
          ids.push(item.id);
        });
        this.formData = value;
        this.id = ids;
      },
      immediate: true
    }
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    getData() {
      let res = {};
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          res = this.$refs.form.getData();
          res.email_password = Base64.encode(res.email_password || '');
          res.validate = true;
        }
      });
      return res;
    },
    reset() {
      this.$refs.form.resetFields();
    }
  }
};
</script>

<style lang="less" scoped>
.config-system-email {
  margin-top: 20px;
}
</style>
