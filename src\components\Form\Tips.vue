<template>
  <span class="form-item-tips" v-if="tips">
    <!-- 收起的 -->
    <a-tooltip v-if="mode === 'folded'">
      <template slot="title">
        <div v-html="tips"></div>
      </template>
      <a-icon type="question-circle" />
    </a-tooltip>
    <!-- 展开的 -->
    <template v-else-if="mode === 'expanded'">
      <a-icon type="question-circle" />
      <span v-html="tips"></span>
    </template>
    <a-tooltip v-else-if="mode === 'pre'">
      <template slot="title">
        <div style="white-space: pre-wrap;" v-html="tips"></div>
      </template>
      <a-icon type="question-circle" />
    </a-tooltip>
  </span>
</template>

<script>
export default {
  components: {},
  props: {
    tips: String,
    mode: {
      type: String,
      default: 'folded'
    }
  },
  data() {
    return {};
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped></style>
