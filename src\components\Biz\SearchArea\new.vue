<template>
  <div
    :class="`search-area ${expanded && 'expanded'} ${
      iconCombine && 'icon-combine'
    } `"
  >
    <Form
      ref="form"
      :fields="localFields"
      v-bind="searchProps"
      :formData="formData"
      :iconCombine="iconCombine"
      class="fixed-label-left"
    >
      <div slot="btns" class="seach-area-btns">
        <span @click="onToggle" v-if="count > expandLimit && isShowExpandIcon">
          {{ expanded ? '收起' : '展开' }}
          <custom-icon :class="`${expanded && 'expanded'}`" type="caret-down" />
        </span>
        <a-button @click="reset">清空</a-button>
        <div class="switch-search-mode">
          <a-button @click="search" type="primary">{{ searchText }}</a-button>
          <a-dropdown overlayClassName="biz-search-area-dropdown">
            <a-menu slot="overlay" @click="handleMenuClick">
              <a-menu-item key="0"> 模糊查询 </a-menu-item>
              <a-menu-item key="1"> 精确查询 </a-menu-item>
            </a-menu>
            <a-button> <a-icon type="ellipsis" /> </a-button>
          </a-dropdown>
        </div>
      </div>
    </Form>
  </div>
</template>

<script>
import Form from '@/components/Form';
import _ from 'lodash';
const defaultSearchProps = {
  fixedLabel: true,
  layout: 'horizontal',
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
  multiCols: 3,
  gutter: 48
  // colon: true
};

export default {
  components: { Form },
  props: {
    // switch、defualt，
    // switch模式可以切换模糊查询和精确查询，default默认模糊查询
    searchMode: {
      type: String,
      default: 'defualt'
    },
    // 是否需要展开按钮，false全展开
    isShowExpandIcon: {
      type: Boolean,
      default: false
    },
    fields: {
      type: Array,
      default: function() {
        return [];
      }
    },
    // 多列布局（只支持能整除24的数字）
    multiCols: {
      type: Number,
      default: 3
    },
    gutter: {
      type: Number,
      default: 48
    },
    searchData: {
      type: Object,
      default: () => ({})
    },
    needCache: {
      type: Boolean,
      default: false
    },
    cacheKey: {
      type: String,
      default: ''
    },
    iconCombine: {
      type: Boolean,
      default: true
    },
    expandLimit: {
      type: Number,
      default: 3
    }
  },
  data() {
    let count = (_.isFunction(this.fields) ? this.fields() : this.fields)
      .length;
    return {
      mode: 0,
      formData: {},
      searchText: '模糊查询',
      cols: this.multiCols,
      maxLine: 1,
      localFields: this.fields,
      count,
      expanded: !this.isShowExpandIcon || count <= this.expandLimit
    };
  },
  computed: {
    searchProps() {
      const { cols, gutter } = this;
      const { labelCol, wrapperCol } = this.$attrs;
      const tmp = {
        multiCols: cols,
        gutter,
        labelCol,
        wrapperCol
      };
      let props = {};

      _.forEach(tmp, (item, key) => {
        if (item) {
          props[key] = item;
        }
      });

      return { ...defaultSearchProps, ...props };
    }
  },
  created() {},
  mounted() {},
  methods: {
    reset() {
      const { form } = this.$refs;
      this.formData = {};
      form.resetFields();
      this.$emit('reset', form.getData());
    },
    search() {
      this.emitSearchData();
    },
    emitSearchData(data) {
      const { fields } = this;
      const { form } = this.$refs;
      const formData = data || form.getData();
      let res = {};

      // 处理数据
      _.forEach(formData, (item, key) => {
        let match = fields.find(f => f.key === key) || {};
        if (match.type === 'RangePicker') {
          res[key] = item
            ? item.map(time => time.format('YYYY-MM-DD HH:mm:ss')).join(',')
            : item;
        } else if (match.type === 'Input') {
          res[key] = _.isString(item) ? item.trim() : item;
        } else {
          res[key] = item;
        }
      });
      res.search_mode = this.mode;
      this.$emit('search', res);
    },
    onToggle() {
      this.expanded = !this.expanded;
    },
    handleMenuClick(e) {
      this.mode = e.key;
      this.searchText = e.key == 0 ? '模糊查询' : '精确查询';
    },
    saving(data = {}) {
      this.$refs.form.saving(data);
    }
  },
  watch: {
    searchData: {
      handler(newVal, oldVal) {
        this.formData = newVal || {};
      },
      immediate: true
      // deep: true
    },
    fields: {
      handler(newVal, oldVal) {
        let arr = newVal.map((item, index) => {
          if (!item.props) {
            item.props = {};
          }
          Object.assign(item.props, {
            getPopupContainer: el => {
              return this.$el.parentNode;
            }
          });
          return item;
        });

        this.localFields = arr;
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.search-area {
  display: flex;
  justify-content: space-between;
  padding: 12px 24px 6px 24px;
  margin-bottom: 0;
  position: relative;
  border-radius: 2px;
  border-radius: 2px;
  border: 1px solid #f2f2f2;
  // box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.3);
  /deep/ .ant-form {
    flex-grow: 1;
    height: 48px;
    padding-right: 24px;
    overflow: hidden;
    * {
      transition: none;
    }
    > .ant-row {
      display: flex;
      flex-wrap: wrap;
      > .ant-col {
        vertical-align: top;
        padding-left: 12px !important;
        padding-right: 12px !important;
        width: 340px;
        .ant-form-item {
          display: flex;
          // margin: 0 0 8px 0;
          .ant-form-item-label {
            min-height: 32px;
            width: 110px;
            > label {
              justify-content: flex-end;
            }
          }
          .ant-form-item-control-wrapper {
            width: 220px;
            .ant-form-item-control {
              line-height: 32px;
              .ant-form-item-children {
                .ant-select-selection__rendered {
                  display: flex;
                  align-items: center;
                  .biz-instance-item {
                    .instance-item-tag {
                      &.new {
                        padding: 0;
                        border: none;
                        background: transparent;
                        width: 300px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      > .ant-col:last-child {
        flex: 1;
        padding-right: 0 !important;
        .ant-form-item {
          display: flex;
          justify-content: flex-end;
          .ant-form-item-control-wrapper {
            width: auto;
            display: flex;
            align-items: center;
            .seach-area-btns {
              display: flex;
              align-items: center;
              white-space: nowrap;
              .ant-btn {
                // padding: 2px 12px;
                // border-radius: 6px !important;
                // border: 1px solid #d9d9d9;
                // > span {
                //   color: #1f1f1f;
                // }
              }
              .anticon {
                font-size: 16px;
                margin: 0 12px;
                border-radius: 50%;
                &:hover {
                  color: #000;
                  cursor: pointer;
                }
              }
              .switch-search-mode {
                display: flex;
                justify-content: center;
                // box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
                border-radius: 6px;
                margin-left: 12px;
                .ant-btn {
                  border: none;
                  border-radius: 6px 0 0 6px !important;
                  background: #008adc;
                  > span {
                    color: #fff;
                  }
                  &:last-child {
                    width: 32px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-left: 1px solid rgba(255, 255, 255, 0.15);
                    border-radius: 0 6px 6px 0 !important;
                    .anticon {
                      padding: 0;
                      color: #fff;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  &.expanded {
    .fixed-label-left {
      height: auto;
      overflow: visible;
    }
  }
  &.icon-combine {
    /deep/ .ant-form {
      > .ant-row {
        > .ant-col {
          padding-left: 24px !important;
          padding-right: 0 !important;
          .ant-form-item {
            .ant-form-item-label {
              max-width: 0;
              min-width: 0;
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              flex: auto;
              width: auto;
              max-width: 100%;
              min-width: 310px;
            }
          }
        }
      }
    }
    .seach-area-btns {
      margin-left: 24px;
    }
  }
}
</style>
<style lang="less">
.biz-search-area-dropdown {
  &.ant-dropdown {
    .ant-dropdown-menu {
      padding: 0;
      .ant-dropdown-menu-item {
        &:hover {
          font-family: PingFangSC-Regular;
          // color: #1677ff;
        }
      }
    }
  }
}
</style>
