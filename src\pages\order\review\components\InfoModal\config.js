
export default function (ctx) {
  const tableColumns = [
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name',
      width: 200
    },
    {
      title: '数据量',
      dataIndex: 'table_rows',
      key: 'table_rows',
      width: 200
    },
    {
      title: '分区类型',
      dataIndex: 'partition_type',
      key: 'partition_type',
      width: 200
    },
    {
      title: '分区键',
      key: 'part_column',
      dataIndex: 'part_column',
      width: 200
    },
    {
      title: '统计信息收集时间',
      key: 'last_collect_time',
      dataIndex: 'last_collect_time',
      width: 250
    },
    {
      title: '表注释',
      key: 'comment',
      dataIndex: 'comment',
      width: 200
    }
  ];

  const indexColumns = (params = {}) => {
    const { combineInfo = {} } = params;
    const { table_name: tableName, index_name: indexName } = combineInfo;
    return [
      {
        title: '表名',
        dataIndex: 'table_name',
        key: 'table_name',
        width: 200,
        customRender: (value, row, index) => {
          let obj = {
            children: value,
            attrs: {}
          };
          if (tableName) {
            let uid = value + '_' + index;
            let matchItem = tableName[uid];
            if (matchItem) {
              obj.attrs.rowSpan = matchItem.rowSpan;
            }
          }
          return obj;
        }
      },
      {
        title: '索引名称',
        dataIndex: 'index_name',
        key: 'index_name',
        width: 200,
        customRender: (value, row, index) => {
          let obj = {
            children: value,
            attrs: {}
          };
          if (indexName) {
            let uid = value + '_' + index;
            let matchItem = indexName[uid];
            if (matchItem) {
              obj.attrs.rowSpan = matchItem.rowSpan;
            }
          }
          return obj;
        }
      },
      {
        title: '索引类型',
        key: 'unique_name',
        dataIndex: 'unique_name',
        width: 200
      },
      {
        title: '索引字段',
        dataIndex: 'column_name',
        key: 'column_name',
        width: 200
      },
      {
        title: '字段类型',
        key: 'data_type',
        dataIndex: 'data_type',
        width: 200
      },
      {
        title: '字段位置',
        key: 'column_position',
        dataIndex: 'column_position',
        width: 200
      },
      {
        title: '区分度',
        key: 'cardinality',
        dataIndex: 'cardinality',
        width: 200
      }
    ];
  };

  return {
    tableColumns,
    indexColumns
  };
};
