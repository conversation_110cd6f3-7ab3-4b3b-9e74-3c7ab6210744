export default function (ctx) {
  const columns = [
    {
      title: '#',
      key: 'index',
      customRender: (text, record, index) => {
        return index + 1
      },
      width: 60
    },
    {
      title: '角色Code',
      dataIndex: 'role_code',
      key: 'role_code',
      width: 200
    },
    {
      title: '角色名称',
      dataIndex: 'role_name',
      key: 'role_name',
      scopedSlots: { customRender: 'role_name' },
      width: 200
    },
    {
      title: '角色描述',
      dataIndex: 'comments',
      key: 'comments',
      width: 200
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 100,
      visible: $permissionBatch.some([
        { module: 'role', values: ['edit', 'delete'] }
      ])
    }
  ].filter(item => item.visible !== false)

  const searchFields = [
    {
      type: 'Input',
      label: '角色名称',
      key: 'role_name',
      mainSearch: true,
      props: {
        placeholder: '请输入角色名称'
      }
    }
  ]
  return {
    columns, searchFields
  };
};
