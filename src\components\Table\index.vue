<template>
  <div :class="className">
    <!-- searchArea -->
    <div :class="['search-area-wrapper', !needSearchArea && 'no-search-area']">
      <div class="custom-table-top-left">
        <SearchArea
          v-if="needSearchArea"
          ref="SearchArea"
          :needCache="needCache"
          :cacheKey="cacheKey"
          :fields="localSearchFields"
          :searchData="searchParams"
          @search="onSearch"
        ></SearchArea>
        <slot name="tableTopLeft"></slot>
      </div>
      <div class="custom-table-top-right">
        <slot name="tableTopRight"></slot>
        <!-- 操作区域 -->
        <Tools
          v-if="needTools"
          :fields="localSearchFields"
          :searchData="searchParams"
          :extraTools="extraTools"
          :extraToolsAfter="extraToolsAfter"
          @search="onSearch"
          @reset="onReset"
          @filter="onFilter"
          @filterColumns="onFilterColumns"
        ></Tools>
      </div>
    </div>
    <slot name="tableMiddle"></slot>
    <a-table
      v-bind="tableProps"
      v-on="tableListeners"
      :data-source="data"
      :loading="loading"
      :row-selection="rowSelection"
      :scroll="scroll"
      :columns="showColumns"
    >
      <template v-for="sItem in scopedSlots" v-slot:[sItem]="text, record, index, column">
        <slot :name="sItem" v-bind="{text, record, index, column}"></slot>
      </template>
      <template v-for="item in slots" v-slot:[item]>
        <slot :name="item"></slot>
      </template>
      <!-- 筛选 -->
      <div
        slot="defaultFilterDropdown"
        slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        style="padding: 8px;width: 200px;"
        :class="['filter-content-wrapper', getSearchItem(column.key).filerLayout]"
      >
        <div class="filter-content">
          <a-input
            v-if="!localSearchFields || localSearchFields.length <= 0"
            v-ant-ref="c => (searchInput = c)"
            :placeholder="'请输入'"
            :value="selectedKeys[0]"
            style="margin-bottom: 8px; display: block;"
            @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
            @pressEnter="() => handleSearch(setSelectedKeys, selectedKeys, confirm, column)"
          />
          <custom-form
            v-else
            class="custom-table-filter-form"
            :ref="`filter-${getSearchItem(column.key).key}`"
            :fields="[getSearchItem(column.key)]"
            :formData="searchParams"
          ></custom-form>
        </div>
        <div class="filter-btns" style="text-align: center;">
          <a-button
            size="small"
            style="width: 80px"
            @click="() => handleReset(setSelectedKeys, selectedKeys, clearFilters, column)"
          >重置</a-button>
          <a-button
            type="primary"
            icon="search"
            size="small"
            style="width: 80px; margin-right: 4px"
            @click="() => handleSearch(setSelectedKeys, selectedKeys, confirm, column)"
          >确定</a-button>
        </div>
      </div>
    </a-table>
    <!-- 加载更多 -->
    <LoadMore
      :loading="loading"
      :pageNum="pageNum"
      :pageSize="pageSize"
      :total="total"
      @next="onLoadMore"
      v-if="hasLoadMore"
    />
  </div>
</template>

<script>
import _ from 'lodash';
import Http from '@/utils/request';
import common from '@/utils/common';
import Config from '@/utils/config';
import SearchArea from './SearchArea';
import Tools from './Tools/index';
import LoadMore from '@/components/LoadMore';
const TableConfig = Config.tableComp;

// 表格默认属性
const defaultProps = {
  bordered: false,
  columns: [],
  pagination: {
    showTotal: (total, range) => {
      return `共${total}条`;
    },
    showSizeChanger: true,
    showQuickJumper: true,
    size: 'small',
    pageSizeOptions: TableConfig.pageSizeOptions || ['10', '20', '30', '40']
  }
  // getPopupContainer(triggerNode) {
  //   console.log(triggerNode, 9090)
  //   return triggerNode;
  // }
};
export default {
  inheritAttrs: false,
  components: { Tools, SearchArea, LoadMore },
  props: {
    url: String,
    method: {
      type: String,
      default: 'get'
    },
    reqParams: Object,
    dataSource: {
      type: Array,
      default: function() {
        return [];
      }
    },
    isInitReq: {
      type: Boolean,
      default: true
    },
    needAllSelectedRows: {
      type: Boolean,
      default: false
    },
    needCache: {
      type: Boolean,
      default: false
    },
    cacheKey: {
      type: String,
      default: ''
    },
    auth: {
      type: Object,
      default: function() {
        return {};
      }
    },
    beforeLoaded: Function,
    loaded: Function,
    searchFields: Array | Function,
    searchInitData: Object,
    searchParamsFilter: Function,
    needTools: {
      type: Boolean,
      default: false
    },
    needSearchArea: {
      type: Boolean,
      default: false
    },
    extraTools: {
      type: Array,
      default: () => []
    },
    extraToolsAfter: {
      type: Array,
      default: () => []
    },
    paginationMode: {
      type: String,
      default: '' // loadMore:加载更多
    },
    adjustScrollY: Number | Function,
    ignoreLoading: {
      type: Boolean,
      default: false
    },
    ignoreFastReqTips: {
      type: Boolean,
      default: false
    },
    uniqKey: String
  },
  provide() {
    return {
      uniqKey: this.uniqKey,
      columns: () => this.tableProps.columns,
      checkedColumnKeys: () => this.checkedColumnKeys
    };
  },
  data() {
    // 初始化完毕否
    this.inited = false;
    // 初始化请求完毕否
    this.initReq = false;
    this.extraParams = {};
    const { needCache, cacheKey } = this;
    let initData = {
      searchParams: this.searchInitData || {}
    };
    if (needCache) {
      const _key = cacheKey || this.$route.name;
      initData = _.merge({}, this.$store.state.common.tableCache[_key] || {});
      initData.searchParams = _.merge(
        initData.searchParams,
        this.$store.state.common.searchCache[_key] || {}
      );
    }
    this.initTableHeight = null;
    this.loadMorePageNum = 1;
    const filterColumns = localStorage.getItem(
      this.uniqKey + '_filter_columns'
    );
    return {
      loading: false,
      data: [],
      scopedSlots: [],
      slots: [],
      // 分页
      pageSize:
        initData.pageSize ||
        _.get(this.$attrs, 'pagination.pageSize') ||
        TableConfig.pageSize ||
        10,
      pageNum: initData.pageNum || 1,
      total: 0,
      // 勾选
      selectedRows: [],
      selectedRowKeys: _.get(this.$attrs, 'rowSelection.selectedRowKeys') || [],
      // 滚动
      screenType: 'normal',
      localScroll: undefined,
      // 过滤
      localSearchFields: this.getLocalSearchFields(
        this.searchFields,
        initData.searchParams || {}
      ),
      searchParams: initData.searchParams || {},
      // 排序
      sorter: undefined,
      // 计算后的scrollHeight
      adjustScrollHeight: this.getAdjustScrollHeight(),
      // 自定义筛选出的列
      checkedColumnKeys: filterColumns != null ? filterColumns.split(',') : null
    };
  },
  computed: {
    tableProps() {
      let props = _.merge({}, defaultProps, this.$attrs);
      if (props.pagination) {
        props.pagination.current = this.pageNum;
        props.pagination.pageSize = this.pageSize;
        props.pagination.total = this.total;
      }
      if (this.paginationMode === 'loadMore') {
        props.pagination = false;
      }
      if (
        window.COMP_CONFIG &&
        _.get(COMP_CONFIG, 'TABLE.locale') &&
        !props.locale
      ) {
        const locale = _.get(COMP_CONFIG, 'TABLE.locale');
        props.locale = locale(this.$createElement);
      }
      return Object.assign({}, props, {
        columns: $Auth(this.auth.columns, this.$attrs.columns || [])
      });
    },
    tableListeners() {
      return { ...this.$listeners, ...{ change: this.handleChange } };
    },
    showColumns() {
      let _cols = this.tableProps.columns;
      if (this.checkedColumnKeys && this.uniqKey) {
        _cols = _cols.filter(item => this.checkedColumnKeys.includes(item.key));
      } else {
        _cols = _cols.filter(item => item.defaultVisible !== false)
      }
      return _cols;
    },
    rowSelection() {
      const { rowSelection = null } = this.tableProps;
      return _.isPlainObject(rowSelection)
        ? Object.assign({}, rowSelection, {
            selectedRowKeys: this.selectedRowKeys,
            onChange: this.onSelectChange
          })
        : null;
    },
    scroll() {
      let scroll = this.localScroll || this.$attrs.scroll;
      if (scroll) {
        if (scroll.x === 'max-content' && scroll.y && !(CSS && CSS.supports('position', 'sticky'))) {
          scroll.y = null;
        } else if (scroll.y && this.adjustScrollHeight) {
          scroll.y = this.adjustScrollHeight;
        }
      }
      return scroll;
    },
    className() {
      const { columns = [], scroll } = this.$attrs;
      let arr = ['custom-table'];

      let isMultiHeader = false;
      columns.forEach(item => {
        if (item.children && item.children.length > 0) {
          isMultiHeader = true;
          return false;
        }
      });
      if (isMultiHeader) {
        arr.push('is-multi-header');
      }
      if (this.needSearchArea) {
        arr.push('need-search-area');
      }
      if (!this.localScroll && _.isEmpty(scroll)) {
        arr.push('no-scroll');
      }
      if (!_.isEmpty(scroll)) {
        if (this.scroll.x === 'max-content') {
          arr.push('x-scroll-max-content');
        }
      }

      return arr;
    },
    hasLoadMore() {
      return (
        this.$attrs.pagination !== false &&
        this.paginationMode === 'loadMore' &&
        this.data.length > 0
      );
    }
  },
  created() {
    const { dataSource, url } = this;
    // 根据window设置信息
    this.initColumns();
    const screenType = this.setInfoByWindow();
    this.screenType = screenType;
    // 如果传入dataSource，以传入dataSource渲染
    if (!url) {
      this.data = _.isArray(dataSource) ? dataSource : [];
      // 设置初始化请求成功
      !this.inited && this.$emit('inited');
      this.inited = true;
      return;
    }
    this.getList();
  },
  mounted() {
    this.scopedSlots = Object.keys(this.$scopedSlots);
    this.slots = Object.keys(this.$slots);
    // 监听tbody变化，修复fixed列高度问题
    this.observe();
    // 添加适配
    this.addAdapter();
    // 处理特殊结构
    this.dealSpecialStruct();
  },
  destroyed() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    if (this.resize) {
      this.$bus.$off('contentResize', this.resize);
      this.resize = null;
    }
  },
  methods: {
    // 模拟请求
    getList(config = {}) {
      const {
        pageSize,
        pageNum,
        url,
        method,
        reqParams,
        extraParams,
        isInitReq
      } = this;
      if (!url) return;
      if (!isInitReq && !this.initReq) {
        this.initReq = true;
        return;
      }
      // 忽略loading
      const ignoreLoading = config.ignoreLoading || this.ignoreLoading;
      // 忽略快速请求提示
      const ignoreFastReqTips =
        config.ignoreFastReqTips || this.ignoreFastReqTips;

      // loading透明度手动处理【某些情况会出现透明度没消失】
      const loadingSpin =
        this.$el && this.$el.querySelector('.ant-spin-container');
      if (!ignoreLoading && loadingSpin) {
        loadingSpin.style.opacity = '0.5';
      }
      // console.log('table reqParams: ', JSON.stringify(reqParams));
      if (this.loading) {
        !ignoreLoading &&
          !ignoreFastReqTips &&
          this.$message.warning('正在请求，请稍后', 1.5);
        return;
      }
      this.initReq = true;
      !ignoreLoading && (this.loading = true);
      // 请求数据
      let reqData =
        this.$attrs.pagination !== false
          ? {
              [TableConfig.pageNumberKey || 'page']: pageNum,
              [TableConfig.pageSizeKey || 'pageSize']: pageSize
            }
          : {};
      reqData = {
        ...reqData,
        ...reqParams,
        ...extraParams,
        ...(this.sorter && this.sorter.order
          ? { _sorter: { [this.sorter.columnKey]: this.sorter.order } }
          : {})
      };
      const optionData =
        method === 'get'
          ? {
              params: reqData
            }
          : {
              data: reqData
            };
      return Http({
        url,
        method,
        ...optionData
      })
        .then(res => {
          this.loading = false;
          // const code = _.get(res, 'data.code');
          if (common.isSuccessCode(res)) {
            let { results: dataSource = [], count: total = 0 } =
              _.get(res, 'data.data') || {};

            if (this.beforeLoaded) {
              dataSource = this.beforeLoaded(dataSource);
            }
            // 设置data
            // if (this.hasLoadMore && pageNum > this.loadMorePageNum) {
            if (this.hasLoadMore) {
              // this.data = [...this.data, ...dataSource];
              this.data = [
                ...this.data.slice(0, (pageNum - 1) * pageSize),
                ...dataSource
              ];
            } else {
              this.data = dataSource;
            }

            this.loadMorePageNum = pageNum;
            // 设置pagination
            this.total = total;

            if (_.isFunction(this.loaded)) {
              this.loaded(this.data, res, this);
            }
          } else {
            if (
              window.GLOBAL_CONFIG &&
              GLOBAL_CONFIG.loadingErrWay === 'notify'
            ) {
              this.$notification['error']({
                message: '错误信息',
                description: _.get(res, 'data.message') || '请求失败！',
                duration: 6
              });
            } else {
              this.$message.error(_.get(res, 'data.message'));
            }
            this.pageNum = 1;
            this.selectedRowKeys = [];
            this.selectedRows = [];
            this.data = [];
          }
        })
        .catch(e => {
          this.loading = false;
          this.pageNum = 1;
          this.selectedRowKeys = [];
          this.selectedRows = [];
          this.data = [];
          // this.$hideLoading({
          //   method: 'error',
          //   tips: _.get(e, 'response.data.message') || '请求失败'
          // });
        })
        .finally(e => {
          // 设置初始化请求成功
          if (!this.inited) {
            this.$emit('inited');
          } else {
            this.$emit('refresh');
          }
          this.inited = true;
          // loading透明度手动处理【某些情况会出现透明度没消失】
          const loadingSpin =
            this.$el && this.$el.querySelector('.ant-spin-container');
          if (!ignoreLoading && loadingSpin) {
            this.$nextTick(() => {
              loadingSpin.style.opacity = '';
            });
          }
        });
    },
    // 刷新表格数据
    refresh(dataSource, params = {}, config = {}) {
      if (!this.needCache || this.inited) {
        this.pageNum = params._pageNum || 1;
        delete params['_pageNum'];
      }
      // 设置_clear才清空
      if (params._clear) {
        this.selectedRowKeys = [];
        this.selectedRows = [];
        delete params['_clear'];
      }

      if (dataSource) {
        this.data = dataSource;
        return;
      }
      this.extraParams = params || {};
      if (this.localSearchFields) {
        this.extraParams = Object.assign(
          {},
          this.getFinalSearchParams(),
          this.extraParams
        );
      }
      // if (this.localSearchFields) {
      //   this.setSearchInfo(this.extraParams);
      // }

      // 更新全局存储
      this.emitCacheData();

      if (!this.url) {
        this.data = [...this.data];
        return;
      }

      return this.getList(config);
    },
    // 刷新表格数据（保持分页）
    refreshKeep(type, params = {}, config) {
      let pageNum = this.pageNum;
      if (type === 'remove') {
        pageNum = this.data.length <= 1 ? 1 : this.pageNum;
      }
      return this.refresh(null, { ...params, _pageNum: pageNum }, config);
    },
    // 刷新表格数据（清除状态）
    refreshClear(params = {}, config) {
      return this.refresh(null, { ...params, _clear: true }, config);
    },
    // 监听table change
    handleChange(pagination, filters, sorter, { currentDataSource = [] }) {
      // console.log(pagination, filters, sorter, currentDataSource);
      const { current, pageSize } = pagination;
      let flag = false;
      if (pageSize != null && pageSize != this.pageSize) {
        // 更换了pageSize
        this.pageNum = 1;
        this.pageSize = pageSize;
        flag = true;
      } else if (current != null && current != this.pageNum) {
        // 更换了pageNum
        this.pageNum = current;
        flag = true;
      }

      if (!this.url) {
        this.total = currentDataSource.length;
        // this.pageNum = 1;
      }
      if (
        this.sorter == null ||
        this.sorter.columnKey !== sorter.columnKey ||
        this.sorter.order !== sorter.order
      ) {
        this.setSorter(sorter);
        flag = true;
      }
      flag && this.getList();
      // 更新全局存储
      this.emitCacheData();
    },
    onLoadMore(pageNum) {
      if (this.loading) return;
      this.pageNum = pageNum;
      this.getList();
    },
    // 监听table勾选
    onSelectChange(selectedRowKeys = [], selectedRows = []) {
      const { rowKey = 'key' } = this.$attrs;
      this.selectedRowKeys = selectedRowKeys;
      if (this.needAllSelectedRows) {
        const temp = [...this.selectedRows, ...selectedRows];
        let allRows = [];
        let map = {};
        temp.forEach(item => {
          if (!map[item[rowKey]] && selectedRowKeys.includes(item[rowKey])) {
            allRows.push(item);
            map[item[rowKey]] = 1;
          }
        });
        this.selectedRows = allRows;
      } else {
        this.selectedRows = selectedRows;
      }
      console.log(
        'selectedRowKeys changed: ',
        this.selectedRowKeys,
        this.selectedRows
      );
      this.$emit('selectChange', {
        selectedRowKeys: this.selectedRowKeys,
        selectedRows: this.selectedRows
      });
    },
    // 设置选中
    setSelectedInfo(keys = []) {
      const { rowKey = 'key' } = this.$attrs;
      this.selectedRowKeys = keys;
      this.selectedRows = this.data.filter(item => keys.includes(item[rowKey]));
    },
    getSelectedInfo() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        selectedRows: this.selectedRows
      };
    },
    // 全局存储
    emitCacheData(data = {}) {
      const { needCache, cacheKey } = this;
      if (needCache) {
        const _key = cacheKey || this.$route.name;
        this.$store.commit('common/setTableCache', {
          [_key]: {
            pageNum: data.pageNum || this.pageNum,
            pageSize: data.pageSize || this.pageSize
          }
        });
        this.$store.commit('common/setSearchCache', {
          [_key]: this.searchParams
        });
      }
    },
    // 监听tbody变化，修复fixed列高度问题
    observe() {
      const MutationObserver =
        window.MutationObserver ||
        window.webkitMutationObserver ||
        window.MozMutationObserver;

      if (MutationObserver) {
        this.observer = new MutationObserver(m => {
          this.ajustTrHeight();
          this.dealSpecialStruct();
        });
        this.observer.observe(this.$el, {
          attributes: true,
          childList: true,
          characterData: true,
          subtree: true
        });
      }
    },
    getTrueHeight(element) {
      if (!element) return;
      // 获取实际高度
      element.style.height = 'auto';
      let targetHeight = window.getComputedStyle(element).height;
      return (targetHeight + '').replace('px', '');
    },
    ajustTrHeight() {
      this.$nextTick(() => {
        if (!this.$el) return;
        var tbody = this.$el.querySelector(
          '.ant-table-content .ant-table-tbody'
        );
        var fixedRight = this.$el.querySelector(
          '.ant-table-fixed-right .ant-table-tbody'
        );
        if (!tbody || !fixedRight || _.isEmpty(this.scroll)) {
          return;
        }
        // let currentHeight = tbody.offsetHeight;
        // let currentFixedRightHeight = fixedRight && fixedRight.offsetHeight;
        // if (
        //   currentFixedRightHeight &&
        //   currentFixedRightHeight != currentHeight
        // ) {
        const fixedRightTr = [...fixedRight.children];
        [...tbody.children].forEach((tr, index) => {
          // const fixedLeft = this.$el.querySelectorAll(
          //   '.ant-table-fixed-left tbody tr'
          // );
          // console.log(tr.offsetHeight, fixedRightTr, fixedRightTr[index]);
          // if (fixedLeft && fixedLeft[index]) {
          //   fixedLeft[index].style.height = tr.offsetHeight + 'px';
          // }
          if (fixedRightTr && fixedRightTr[index]) {
            // 老逻辑
            // if (
            //   currentFixedRightHeight > currentHeight &&
            //   (!this.initTableHeight || currentHeight < this.initTableHeight)
            // ) {
            //   tr.style.height = fixedRightTr[index].offsetHeight + 'px';
            // } else {
            //   fixedRightTr[index].style.height = tr.offsetHeight + 'px';
            // }
            // 新逻辑
            if (tr.offsetHeight !== fixedRightTr[index].offsetHeight) {
              const trHeight = this.getTrueHeight(tr);
              const fixedRightTrHeight = this.getTrueHeight(
                fixedRightTr[index]
              );
              if (fixedRightTrHeight === 'auto') return;
              // console.log(index, trHeight, fixedRightTrHeight, 1111111);

              if (tr.offsetHeight < fixedRightTr[index].offsetHeight) {
                tr.style.height = '';
                if (tr.getAttribute('height') !== fixedRightTrHeight + 'px') {
                  tr.setAttribute('height', fixedRightTrHeight + 'px');
                }
              } else if (tr.offsetHeight > fixedRightTr[index].offsetHeight) {
                fixedRightTr[index].style.height = '';
                if (
                  fixedRightTr[index].getAttribute('height') !==
                  trHeight + 'px'
                ) {
                  fixedRightTr[index].setAttribute('height', trHeight + 'px');
                }
              }
            }
          }
        });
        // oldHeight = currentFixedRightHeight;
        // }
        // !this.initTableHeight && (this.initTableHeight = tbody.offsetHeight);
      });
    },
    // page跳转
    pageJump(current) {
      this.pageNum = current;
      return this.getList();
    },
    getLocalSearchFields(fields = [], data) {
      let _data = data || this.searchParams;
      let res = _.isFunction(fields) ? fields(_data, this) : [...fields];
      res.forEach(item => {
        if (
          !item.filerLayout &&
          item.type === 'Select' &&
          ['multiple', 'tags'].includes(_.get(item, 'props.mode'))
        ) {
          item.filerLayout = 'lr';
        }
        // 设置表格内筛选功能select下拉列表浮层的渲染节点是否在body上
        if (
          item.type === 'Select' &&
          !_.get(item, 'props.getPopupContainer') &&
          window.COMP_CONFIG &&
          _.get(COMP_CONFIG, 'TABLE.filterPopContainerBindToBody')
        ) {
          item.props = {
            ...(item.props || {}),
            getPopupContainer: el => {
              return document.body;
            }
          };
        }
      });
      return res;
    },
    getFinalSearchParams() {
      let res = {};
      _.forEach(this.searchParams, (item, key) => {
        let match = this.localSearchFields.find(f => f.key === key) || {};
        if (match && _.isFunction(match.valueFormat)) {
          res[key] = match.valueFormat(item, res);
        } else if (match.type === 'RangePicker') {
          const formatStr =
            _.get(match, 'props.format') || 'YYYY-MM-DD HH:mm:ss';
          res[key] = item
            ? item.map(time => time.format(formatStr)).join(',')
            : item;
        } else if (match.type === 'Input') {
          res[key] = _.isString(item) ? item.trim() : item;
        } else {
          res[key] = item;
        }
      });
      if (this.searchParamsFilter) {
        res = this.searchParamsFilter(res);
      }
      return res;
    },
    // 过滤搜索
    handleSearch(setSelectedKeys, selectedKeys, confirm, column = {}) {
      console.log(selectedKeys, column, 'handleSearch');
      const { backFilterKey } = column;
      if (!backFilterKey) {
        // 前端过滤
        // setSelectedKeys(selectedKeys[0]);
        confirm();
      } else {
        // 后端过滤
        const filterRef = this.$refs[`filter-${backFilterKey}`];
        const filterValue = (filterRef.getData() || {})[backFilterKey];
        this.setSearchInfo(
          Object.assign({}, this.searchParams, {
            [backFilterKey]: filterValue
          })
        );
        confirm();
        this.refresh();
        this.$emit('filter-confirm', this.searchParams, column);
      }
    },
    // 过滤重置
    handleReset(setSelectedKeys, selectedKeys, clearFilters, column = {}) {
      console.log(selectedKeys, column, 'handleReset');
      const { backFilterKey } = column;
      if (!backFilterKey) {
        // 前端过滤
        clearFilters();
      } else {
        // 后端过滤
        const matchItem =
          this.localSearchFields.find(item => item.key == backFilterKey) || {};
        const trigger = _.get(matchItem, `listeners.${matchItem.trigger}`);
        if (trigger) {
          trigger();
        } else {
          this.setSearchInfo(
            Object.assign({}, this.searchParams, {
              [backFilterKey]: undefined
            })
          );
        }
        clearFilters();
        this.setSorter(null);
        this.refresh();
        this.$emit('filter-reset', this.searchParams, column);
      }
    },
    // 获取SearchItem
    getSearchItem(key) {
      const { localSearchFields = [] } = this;
      const searchItem =
        localSearchFields.find(
          itm => itm.sourceKey === key || itm.key === key
        ) || {};

      return searchItem;
    },
    getFilteredValue(value) {
      let res = value;
      if (typeof res === 'object') {
        res = JSON.stringify(res);
      }
      return res;
    },
    setSearchInfo(data = {}) {
      this.searchParams = data;
      this.localSearchFields = this.getLocalSearchFields(this.searchFields);
      this.$attrs.columns.forEach(item => {
        const filterValue = this.searchParams[item.backFilterKey];
        const filterSavingValue = this.getFilteredValue(filterValue);
        if (item.backFilterKey) {
          this.$set(
            item,
            'filteredValue',
            filterSavingValue ? [filterSavingValue] : []
          );
          this.$set(item, 'filtered', !!filterSavingValue);
        }
      });
      if (this.needSearchArea) {
        this.$refs.SearchArea.setData(this.searchParams);
      }
    },
    // tools Search
    onSearch(data = {}) {
      const _data = _.isPlainObject(data) ? data : {};
      this.setSearchInfo(Object.assign({}, this.searchParams, _data));
      this.refresh();
    },
    // tools Reset
    onReset() {
      this.setSearchInfo({});
      this.setSorter(null);
      this.refresh();
      this.$emit('reset');
    },
    // tools Filter
    onFilter() {},
    // tools Filter columns
    onFilterColumns(checkedKeys) {
      this.checkedColumnKeys = checkedKeys;
      localStorage.setItem(
        this.uniqKey + '_filter_columns',
        checkedKeys.join(',')
      );
    },
    // 搜索值保存
    searchSaving(data = {}) {
      let res = {};
      _.forEach(data, (item, key) => {
        res[key] = item === null ? undefined : item;
      });
      this.setSearchInfo(Object.assign({}, this.searchParams, res));
    },
    setSorter(sorter) {
      if (sorter == null) {
        // 清空
        let column = null;
        this.$attrs.columns.map(itm => {
          itm.sortOrder = false;
          if (itm.key === (this.sorter || {}).columnKey) column = itm;
        });
        column && this.$set(column, 'sortOrder', false);
      } else {
        let column = null;
        this.$attrs.columns.map(itm => {
          itm.sortOrder = false;
          if (itm.key === sorter.columnKey) column = itm;
        });
        column && this.$set(column, 'sortOrder', sorter.order);
        this.$emit('sorter', sorter);
      }
      this.sorter = sorter;
    },
    // 初始化columns
    initColumns() {
      const columns = this.$attrs.columns || [];
      columns.forEach(item => {
        const searchItem = this.getSearchItem(item.key);
        if (item && item.key) {
          if (item.sourceWidth || item.width) {
            item.sourceWidth = item.sourceWidth || item.width;
          }
          if (item.fixed) {
            item.sourceFixed = item.fixed;
          }
          if (item._hasInitFilter == null) {
            item._hasInitFilter = !!_.get(item, 'scopedSlots.filterDropdown');
          }
          // if (!item.align) {
          //   item.align = 'center';
          // }
          if (!_.isEmpty(searchItem)) {
            if (
              searchItem.mainSearch !== true &&
              !_.get(item, 'scopedSlots.filterDropdown')
            ) {
              if (item.scopedSlots) {
                item.scopedSlots.filterDropdown = 'defaultFilterDropdown';
              } else {
                item.scopedSlots = { filterDropdown: 'defaultFilterDropdown' };
              }
              item.backFilterKey = searchItem.key;
              const filterValue = this.searchParams[searchItem.key];
              const filterSavingValue = this.getFilteredValue(filterValue);
              item.filteredValue = filterValue ? [filterSavingValue] : [];
              item.filtered = !!filterValue;
            }
          } else {
            if (item._hasInitFilter === false && item.scopedSlots) {
              delete item.scopedSlots['filterDropdown'];
              item.filteredValue = undefined;
              item.filtered = false;
            }
          }
        }
      });
    },
    getAdjustScrollHeight() {
      const _adjustScrollY = _.isFunction(this.adjustScrollY) ? this.adjustScrollY() : this.adjustScrollY;
      const _y = document.body.offsetHeight - _adjustScrollY;
      return this.adjustScrollY
        ? _y < 200 ? 200 : _y
        : undefined
    },
    // 添加屏幕适配
    addAdapter() {
      this.$bus.$on(
        'contentResize',
        (this.resize = _.debounce(() => {
          const screenType = this.setInfoByWindow();
          if (screenType !== this.screenType) {
            this.screenType = screenType;
          }
          if (this.adjustScrollY) {
            this.adjustScrollHeight = this.getAdjustScrollHeight();
          }
        }, 300))
      );
    },
    // 根据屏幕设置信息
    setInfoByWindow() {
      const bodyWidth = document.body.clientWidth;
      const { scroll } = this.$attrs;
      let screenType = 'normal';
      let newScroll;
      const columns = this.$attrs.columns;
      let actionItem = columns.find(
        (item, index) =>
          item.key &&
          item.key.includes('action') &&
          index === columns.length - 1
      );
      if (bodyWidth <= 1366) {
        screenType = 'small';
        // const totalWidth = _.sumBy(columns, function(o) {
        //   return o.width || 0;
        // });
        if (_.isEmpty(scroll)) {
          if (columns.length > 6) {
            newScroll = {
              x: 1200
            };
            this.localScroll = newScroll;

            if (actionItem && !actionItem.noFixed) {
              this.$set(actionItem, 'fixed', 'right');
              this.$set(
                actionItem,
                'width',
                actionItem.fixedWidth
                  ? actionItem.fixedWidth
                  : actionItem.sourceWidth > 150
                  ? 150
                  : actionItem.sourceWidth
              );
            }
          }
        }
      } else {
        if (_.isEmpty(scroll)) {
          this.localScroll = newScroll;
          if (actionItem && !actionItem.noFixed) {
            this.$set(actionItem, 'fixed', actionItem.sourceFixed);
            this.$set(actionItem, 'width', actionItem.sourceWidth);
          }
        }
      }
      // console.log(screenType, newScroll, 6666);
      this.ajustTrHeight();
      return screenType;
    },
    // 处理特殊结构
    dealSpecialStruct() {
      if (!this.scroll) return;
      const { x, y } = this.scroll;
      if (x === 'max-content' && y) {
        if (CSS && CSS.supports('position', 'sticky')) {
          // 特殊处理
          let table = this.$el.querySelectorAll('.ant-table-fixed');
          [...table].forEach(fixed => {
            fixed.style.tableLayout = 'auto';
          });
          let head = this.$el.querySelector(
            '.ant-table-scroll .ant-table-header .ant-table-thead'
          );
          if (head) {
            const th = head.querySelectorAll('th');
            [...th].forEach(item => {
              item.style.position = 'sticky';
              item.style.top = 0;
              item.style.zIndex = 1;
            });
            let hideBar = this.$el.querySelector('.ant-table-hide-scrollbar');
            hideBar.style.display = 'none';
            const body = this.$el.querySelector(
              '.ant-table-scroll .ant-table-body .ant-table-fixed .ant-table-tbody'
            );
            body.parentNode.insertBefore(head, body);
          }
          head = this.$el.querySelector('.ant-table-scroll .ant-table-thead');
          const { height: headHeight } = head.getBoundingClientRect();
          const fixedCols = this.$el.querySelectorAll('.ant-table-body-inner');
          [...fixedCols].forEach(fixed => {
            if (y > headHeight) {
              // const fixedTable = fixed.querySelector('.ant-table-fixed');
              const _h = y - headHeight;
              // if (fixedTable.offsetHeight - y > 20 && _h > 0) {
              if (_h > 0) {
                fixed.style.maxHeight = _h + 'px';
              }
            }
          });
        }
      }
    }
  },
  watch: {
    reqParams: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.refresh();
        }
      }
      // immediate: true,
      // deep: true
    },
    dataSource: {
      handler(newVal, oldVal) {
        // console.log('Table new dataSource: ', newVal);
        this.data = newVal || [];
        // 非请求且有分页，当数据变少需要特殊处理pageNum
        const { url, pageNum, pageSize } = this;
        const { pagination } = this.$attrs;
        const length = newVal.length;
        if (!url && pagination !== false && pageNum > 1) {
          const totalPage = Math.ceil(length / pageSize);
          if (pageNum > totalPage) {
            this.pageNum = totalPage;
          }
        }
        if (!url) {
          this.total = this.data.length;
        } else {
          if (this.data.length > this.total) {
            this.total = this.data.length;
          }
        }
      },
      immediate: true
      // deep: true
    },
    '$attrs.columns': {
      handler(newVal = [], oldVal) {
        this.$nextTick(() => {
          this.scopedSlots = Object.keys(this.$scopedSlots);
          this.slots = Object.keys(this.$slots);
        });
        // 根据window设置信息
        this.initColumns();
        const screenType = this.setInfoByWindow();
        this.screenType = screenType;
      }
      // immediate: true
      // deep: true
    },
    '$attrs.rowSelection': {
      handler(newVal = {}, oldVal) {
        const { selectedRowKeys } = newVal;
        if (selectedRowKeys != null) {
          this.selectedRowKeys = selectedRowKeys || [];
        }
      }
      // immediate: true
      // deep: true
    },
    searchFields(newVal) {
      this.localSearchFields = this.getLocalSearchFields(newVal);
      this.initColumns();
    }
  }
};
</script>

<style lang="less" scoped>
.custom-table {
  position: relative;
  .custom-table-top-left,
  .custom-table-top-right {
    display: flex;
    align-items: flex-end;
  }
  /deep/ .ant-table {
    .ant-table-tbody > tr {
      &:nth-child(even) {
        background: #fafafa;
      }
    }
    .ant-table-tbody > tr > td {
      &:last-child {
        > span:not(.limit-label) {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          // justify-content: center;
        }
        a {
          white-space: nowrap;
        }
      }
    }
  }
  /deep/ .ant-table-fixed-right,
  /deep/ .ant-table-fixed-left {
    z-index: 10;
    .ant-table-expanded-row {
      td {
        opacity: 0;
      }
    }
  }

  /deep/ .ant-table-bordered.ant-table-empty .ant-table-placeholder {
    border-top: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
  }

  // /deep/ .custom-table-filter-form {
  //   .ant-form-item {
  //     margin-bottom: 8px;
  //   }
  //   .ant-form-item-label {
  //     display: none;
  //   }
  // }

  .search-area-wrapper {
    // overflow:hidden;
    display: flex;
    justify-content: space-between;
    /deep/ .custom-table-tools {
      // position: absolute;
      // right: 0;
      // top: -32px;
      align-self: flex-end;
    }

    &.no-search-area {
      justify-content: flex-end;
      /deep/ .custom-table-tools {
        margin-top: 8px;
      }
    }
  }

  &.empty-no-border {
    /deep/ .ant-table-placeholder {
      border-bottom: 0;
    }
  }
  &.is-multi-header {
    /deep/ .ant-table-thead > tr > th {
      padding: 8px;
    }
  }
  &.need-search-area {
    /deep/ .custom-table-tools {
      top: 28px;
    }
  }
  &.no-scroll {
    /deep/ .ant-table-fixed-right {
      display: none;
    }
    /deep/ .ant-table-fixed-columns-in-body {
      > * {
        visibility: visible !important;
      }
    }
  }

  &.x-scroll-max-content {
    /deep/ table {
      width: max-content !important;
    }
  }
}
</style>
<style lang="less">
.filter-content-wrapper {
  &.lr {
    display: flex;
    width: 300px !important;
    .filter-content {
      flex-grow: 1;
    }
    .filter-btns {
      display: flex;
      flex-direction: column;
      margin-top: 4px;
      padding-left: 8px;

      > .ant-btn {
        margin-bottom: 4px;
      }
    }
  }
}
.custom-table-filter-form {
  .ant-form-item {
    margin-bottom: 8px;
  }
  .ant-form-item-label {
    display: none;
  }
}
</style>
