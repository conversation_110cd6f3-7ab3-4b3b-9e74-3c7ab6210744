export default function (ctx) {
  const columns = [
    {
      title: '实例名称',
      dataIndex: 'name',
      key: 'name',
      scopedSlots: { customRender: 'name' },
      width: 180
    },
    // {
    //   title: '数据源名称',
    //   key: 'data_source',
    //   dataIndex: 'data_source',
    //   width: 200,
    //   scopedSlots: { customRender: 'data_source' }
    // },
    // {
    //   title: '数据库类型',
    //   dataIndex: 'db_type',
    //   key: 'db_type',
    //   scopedSlots: { customRender: 'db_type' },
    //   width: 120
    // },
    // {
    //   title: '环境',
    //   dataIndex: 'env',
    //   key: 'env',
    //   width: 100,
    //   scopedSlots: { customRender: 'env' }
    // },
    {
      title: '定时任务状态',
      dataIndex: 'scheduler_status',
      key: 'scheduler_status',
      scopedSlots: { customRender: 'scheduler_status' },
      width: 130
    },
    {
      title: '类型',
      key: 'type',
      dataIndex: 'type',
      scopedSlots: { customRender: 'type' },
      // customRender: (text, record, index) => {
      //   return text == 1 ? '单次' : '多次';
      // },
      width: 120
    },
    {
      title: '最近执行时间',
      dataIndex: 'last_time',
      key: 'last_time',
      sorter: true,
      width: 200
    },
    {
      title: '审核规则',
      key: 'rule_set_name',
      dataIndex: 'rule_set_name',
      width: 300,
      scopedSlots: { customRender: 'rule_set_name' }
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      key: 'created_by',
      scopedSlots: { customRender: 'created_by' },
      width: 120
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 170,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const searchFields = (dbType) => {
    return [
      {
        type: 'Select',
        label: '数据源名称',
        key: 'data_source_name',
        sourceKey: 'data_source',
        mainSearch: true,
        props: {
          url: '/sqlreview/review/afterwards_review_list',
          reqParams: {
            data_source_name: 'data_source_name',
            db_type: dbType
          },
          placeholder: '请选择数据源名称',
          allowSearch: true,
          backSearch: true,
          limit: 50
        }
      },
      {
        type: 'Select',
        label: '数据库类型',
        key: 'db_type',
        props: {
          options: [
            { label: 'MYSQL', value: 'MYSQL' },
            { label: 'ORACLE', value: 'ORACLE' },
            { label: 'POSTGRE', value: 'POSTGRE' }
          ]
        }
      },
      {
        type: 'Select',
        label: '定时任务状态',
        key: 'scheduler_status',
        props: {
          options: [
            { label: '运行', value: 1 },
            { label: '关闭', value: 0 }
          ]
        }
      },
      {
        type: 'Select',
        label: '类型',
        key: 'type',
        props: {
          options: [
            { label: '单次', value: 1 },
            { label: '多次', value: 2 }
          ]
        }
      },
      {
        type: 'Select',
        label: '创建人',
        key: 'created_by',
        props: {
          url: '/sqlreview/review/afterwards_review_list',
          reqParams: {
            created_by: 'created_by'
          },
          allowSearch: true,
          backSearch: true,
          limit: 50
        }
      },
      {
        type: 'Select',
        label: '环境',
        key: 'env',
        props: {
          options: [
            {
              label: '生产',
              value: 'prod'
            },
            {
              label: '测试',
              value: 'test'
            }
          ]
        }
      }
    ];
  };

  return {
    columns,
    searchFields
  };
}
