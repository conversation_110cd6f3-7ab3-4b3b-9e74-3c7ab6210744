import moment from 'moment';
export default function (ctx) {
  const statusColor = {
    待评审: '#FC925E',
    评审中: '#4F96FB',
    已通过: '#6EC84A',
    未通过: '#F9676B',
    未提交: '#AFADAD'
  };
  const cardColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sorter: true,
      width: 100,
      colSpan: 10,
      scopedSlots: { customRender: 'cardTable' }
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });

  const listColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sorter: true,
      scopedSlots: { customRender: 'id' },
      width: 100
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      key: 'project_name',
      scopedSlots: { customRender: 'project_name' },
      width: 150
    },
    {
      title: '项目分支',
      dataIndex: 'review_point',
      key: 'review_point',
      scopedSlots: { customRender: 'review_point' },
      width: 150
    },
    {
      title: 'AI审核',
      key: 'status',
      dataIndex: 'status',
      scopedSlots: { customRender: 'status' },
      width: 150
    },
    {
      title: '人工审核',
      key: 'dba_status',
      dataIndex: 'dba_status',
      scopedSlots: { customRender: 'dba_status' },
      width: 150
    },
    {
      title: 'DBA负责人',
      dataIndex: 'dba_leader',
      key: 'dba_leader',
      scopedSlots: { customRender: 'dba_leader' },
      width: 100
    },
    {
      title: '项目组',
      dataIndex: 'project_group',
      key: 'project_group',
      scopedSlots: { customRender: 'project_group' },
      width: 150
    },
    {
      title: '项目组负责人',
      dataIndex: 'project_group_leader',
      key: 'project_group_leader',
      scopedSlots: { customRender: 'project_group_leader' },
      width: 100
    },
    {
      title: 'SQL总数',
      key: 'sql_count',
      dataIndex: 'sql_count',
      sorter: true,
      width: 120
    },
    {
      title: 'AI通过率',
      key: 'passing_rate',
      sorter: true,
      dataIndex: 'passing_rate',
      width: 130
    },
    {
      title: '审核方式',
      key: 'mode',
      dataIndex: 'mode',
      customRender: (text, record, index) => {
        return text == '1' ? '增量' : '全量';
      },
      width: 120
    },
    {
      title: '审核人',
      key: 'review_by',
      dataIndex: 'review_by',
      width: 120
    },
    {
      title: '审核耗时',
      key: 'ai_review_total_time',
      dataIndex: 'ai_review_total_time',
      customRender: (text) => {
        return text + '分钟';
      },
      width: 120
    },
    {
      title: '触发规则',
      key: 'rule_count',
      dataIndex: 'rule_count',
      width: 120
    },
    {
      title: '历史标准基线',
      key: 'history_baseline',
      dataIndex: 'history_baseline',
      customRender: (text) => {
        return text == '1' ? '是' : '否';
      },
      width: 120
    },
    {
      title: '发起人',
      dataIndex: 'created_by',
      key: 'created_by',
      scopedSlots: { customRender: 'created_by' },
      width: 180
    },
    {
      title: '发起时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 180,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = [
    {
      type: 'Input',
      label: '项目组名称',
      key: 'groups_name',
      props: {
        placeholder: '项目组名称'
      }
    },
    {
      type: 'Input',
      label: '项目名称/ID',
      key: 'id',
      props: {
        placeholder: '搜索项目名称/ID'
      }
    },
    {
      type: 'RangePicker',
      label: '发起时间',
      key: 'created_at',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        },
        ranges: {
          '昨天': [moment().subtract(48, 'hour'), moment().subtract(24, 'hour')],
          '近7天': [moment().subtract(7, 'day'), moment()],
          '近10天': [moment().subtract(10, 'day'), moment()],
          '近15天': [moment().subtract(15, 'day'), moment()],
          '近30天': [moment().subtract(30, 'day'), moment()]
        }
      }
    },
    {
      type: 'Input',
      label: '项目分支',
      key: 'review_point'
    },
    {
      type: 'Select',
      label: '审核方式',
      key: 'mode',
      props: {
        options: [
          {
            label: '增量',
            value: '1'
          },
          {
            label: '全量',
            value: '0'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'AI审核',
      key: 'status',
      props: {
        // mode: 'multiple',
        options: [
          {
            label: '队列中',
            value: '0'
          },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '未通过',
            value: '-1,2'
          },
          {
            label: 'Review失败',
            value: '9'
          },
          {
            label: '拉取代码中',
            value: '3'
          },
          {
            label: '代码解析中',
            value: '4'
          },
          {
            label: '代码审核中',
            value: '5'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'DBA评审',
      key: 'dba_status',
      props: {
        options: [
          {
            label: '未提交',
            value: '0'
          },
          {
            label: '待评审',
            value: '1'
          },
          {
            label: '评审中',
            value: '2'
          },
          {
            label: '已通过',
            value: '3'
          },
          {
            label: '未通过',
            value: '4'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '历史标准基线',
      key: 'history_baseline',
      props: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'DBA负责人',
      key: 'mgr',
      props: {
        url: '/sqlreview/review/select_user',
        reqParams: {
          only_director: 1
        },
        allowSearch: true,
        backSearch: true,
        limit: 50
      }
    },
    {
      type: 'Select',
      label: '发起人',
      key: 'created_by',
      props: {
        url: '/sqlreview/review/select_user',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 50
      }
    },
    {
      type: 'Input',
      label: '',
      key: 'btns',
      hideComponent: true,
      slots: [{ key: 'btns' }],
      rules: []
    }
  ];
  return {
    cardColumns,
    listColumns,
    statusColor,
    searchFields: fields
  };
}
