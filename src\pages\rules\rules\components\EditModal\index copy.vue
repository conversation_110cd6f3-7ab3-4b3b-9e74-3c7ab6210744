<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    :title="'参数选择'"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Table ref="table" v-bind="params" :dataSource="dataSource" />
    </a-spin>
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
import config from './config';
import { getTargetList } from '@/api/config/rule';

export default {
  components: { Table },
  props: {
    dbType: {
      type: String,
      default: 'oracle'
    }
  },
  data() {
    this.config = config(this);
    return {
      type: 'add',
      visible: false,
      spinning: false,
      dataSource: [],
      params: {
        url: '',
        method: 'post',
        reqParams: {
          pageSize: undefined
        },
        columns: this.config.columns,
        rowSelection: {
          type: 'radio'
        },
        pagination: false,
        scroll: { y: 500 },
        rowKey: 'id'
      },
      node: {}
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.node = data.node;
      this.type = data.type;
      const dbType = this.dbType.toLowerCase();
      let reqArr = [];
      const loop = item => {
        if (item.role_type == 'item') {
          const indexCode = item.index_code;
          const prevOperator = item.prev_operator;
          const targetOperator = item.target_operator;
          const targetValue = item.target_value;

          // reqArr.unshift([
          //   indexCode ? indexCode.key : '',
          //   indexCode ? indexCode.label : '',
          //   prevOperator ? prevOperator.key : '',
          //   targetOperator ? targetOperator.key : '',
          //   targetValue || ''
          // ]);
          if (item.level >= 2) {
            reqArr.unshift([
              indexCode ? indexCode.label : '',
              prevOperator ? prevOperator.key : '',
              targetOperator ? targetOperator.key : '',
              targetValue || ''
            ]);
          } else {
            reqArr.unshift([
              indexCode ? indexCode.key : '',
              indexCode ? indexCode.label : '',
              prevOperator ? prevOperator.key : '',
              targetOperator ? targetOperator.key : '',
              targetValue || ''
            ]);
          }
          reqArr.splice(-1, 1, []);
        }
        if (item._parent) {
          loop(item._parent);
        }
      };
      loop(this.node);
      const params = {
        db_type: dbType,
        pageSize: undefined,
        value_list: reqArr,
        _t: +new Date()
      };
      this.spinning = true;
      getTargetList(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.spinning = false;
            this.$hideLoading({ duration: 0 });
            const resData = _.get(res, 'data.data');
            this.dataSource = resData.map(item => {
              return {
                ...item,
                id: _.uniqueId('init_')
              };
            });
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    hide() {
      this.visible = false;
      this.spinning = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { table } = this.$refs;
      const selectedRows = table.selectedRows[0];
      this.$set(this.node, 'index_code', {
        key: selectedRows.source,
        label: selectedRows.option
      });
      this.$set(this.node, 'source_code', selectedRows.source);
      this.$emit('save', table.selectedRows);
    }
  }
};
</script>

<style lang="less" scoped>
</style>
