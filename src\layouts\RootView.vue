<template>
  <!-- <LeftRightNew :layout="layout" :mode="mode" :logo="logo"></LeftRightNew> -->
  <TopBottomNew :layout="layout" :mode="mode" :logo="logo"></TopBottomNew>
</template>
<script>
// import LeftRightNew from './subLayouts/LeftRightNew/index.vue';
import TopBottomNew from './subLayouts/TopBottomNew/index.vue';
import './private/index';

export default {
  components: { TopBottomNew },
  data() {
    // let mode = this.$store.state.common.mode;
    let layout = this.$store.state.common.layout;
    // let theme = this.$store.state.common.theme;
    let logo = '';
    let isBiGuiYuan = process.channel === 'BiGuiYuan';
    if (isBiGuiYuan) {
      logo = require(`@/assets/img/private/login/logo-bgy.svg`);
    } else {
      logo = require(`@/assets/img/private/sqlreview-logo.svg`);
    }
    return {
      layout,
      logo
    };
  },
  computed: {
    mode() {
      const _mode = this.$store.state.common.mode;
      return _mode ? 'frame-' + _mode : '';
    }
  },
  created() {},
  mounted() {},
  destroyed() {},
  methods: {},
  watch: {}
};
</script>
<style lang="less" scoped>
</style>
