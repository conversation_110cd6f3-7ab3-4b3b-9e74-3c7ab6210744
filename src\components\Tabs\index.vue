<template>
  <a-tabs :class="className" v-bind="tabsProps" v-on="tabsListeners" :activeKey="activeKey" :animated="animated">
    <a-tab-pane :key="item.key" v-for="item in tabsList">
      <a-badge slot="tab" v-if="mode == 'badge'" :count="item.count" :overflowCount="overflowCount">
        <span>{{item.tab}}</span>
      </a-badge>
      <span v-else slot="tab">
        <DbImg v-if="needDatabaseIcon" :value="item.dbType" :schemaName="item.dbType" />
        <span v-else>{{item.tab}}</span>
      </span>
      <slot :name="item.key" v-bind="{ item }"></slot>
    </a-tab-pane>
    <template slot="tabBarExtraContent">
      <slot name="tabBarExtraContent" v-bind="{ activeKey }"></slot>
    </template>
  </a-tabs>
</template>

<script>
import DbImg from '@/components/CustomImg/DbImg';
const defaultProps = {};
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {
    DbImg
  },
  props: {
    needDatabaseIcon: Boolean,
    value: String,
    tabsList: {
      type: Array,
      default: () => []
    },
    mode: {
      type: String,
      default: '' // divider：分割线模式; tag: tag模式; scale: scale模式, badge: badge模式
    },
    overflowCount: {
      type: Number,
      default: 99
    },
    animated: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeKey: this.value
    };
  },
  computed: {
    tabsProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    tabsListeners() {
      return { ...this.$listeners, ...{ change: this.handleChange } };
    },
    className() {
      return ['custom-tabs', this.mode];
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleChange(e) {
      this.activeKey = e;
      this.$emit('change', e);
    }
  },
  watch: {
    value(newVal, oldVal) {
      this.activeKey = newVal;
    }
  }
};
</script>

<style lang="less">
.custom-tabs {
  .ant-tabs-bar {
    border: none !important;
  }
  .ant-tabs-ink-bar {
    width: 0 !important;
  }
  &.divider {
    > .ant-tabs-bar
      > .ant-tabs-nav-container
      > .ant-tabs-nav-wrap
      > .ant-tabs-nav-scroll
      > .ant-tabs-nav {
      > div {
        > .ant-tabs-tab-active {
          color: #262626;
          .font-bold();
        }
        > .ant-tabs-tab {
          padding: 0;
          margin: 0;
          font-size: 16px;
          &::after {
            content: '';
            padding: 0;
            font-size: 14px;
            line-height: 1.5;
            list-style: none;
            background: #ddd;
            position: relative;
            top: -0.06em;
            display: inline-block;
            width: 1px;
            height: 0.9em;
            margin: 0 15px;
            vertical-align: middle;
          }
          &:last-child {
            &::after {
              display: none;
            }
          }
          &:hover {
            color: #262626;
          }
        }
      }
    }
  }
  &.tag {
    > .ant-tabs-bar
      > .ant-tabs-nav-container
      > .ant-tabs-nav-wrap
      > .ant-tabs-nav-scroll
      > .ant-tabs-nav {
      > div {
        > .ant-tabs-tab-active {
          color: rgba(0, 0, 0, 0.85) !important;
          background: #f2f4f5;
          border-radius: 8px;
          .font-bold();
        }
        > .ant-tabs-tab {
          padding: 5px 12px;
          margin: 0;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          margin-right: 16px;
          &:last-child {
            &::after {
              display: none;
            }
          }
          &:hover {
            color: #417ff2;
          }
        }
      }
    }
  }
  &.scale {
    > .ant-tabs-bar
      > .ant-tabs-nav-container
      > .ant-tabs-nav-wrap
      > .ant-tabs-nav-scroll
      > .ant-tabs-nav {
      > div {
        > .ant-tabs-tab-active {
          color: rgba(0, 0, 0, 0.85) !important;
          .font-bold();
          font-size: 20px !important;
        }
        > .ant-tabs-tab {
          padding: 5px;
          margin: 0;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          margin-right: 16px;
          &:last-child {
            &::after {
              display: none;
            }
          }
          &:hover {
            // color: #417ff2;
          }
        }
      }
    }
  }
  &.badge {
    > .ant-tabs-bar
      > .ant-tabs-nav-container
      > .ant-tabs-nav-wrap
      > .ant-tabs-nav-scroll
      > .ant-tabs-nav {
      > div {
        > .ant-tabs-tab-active {
          color: #262626;
          .font-bold();
        }
        > .ant-tabs-tab {
          padding: 5px 24px;
          margin: 0;
          font-size: 16px;
          padding-right: 30px;
          &:first-child {
            padding-left: 5px;
          }
          .ant-badge {
            font-size: 16px;
            .ant-badge-count {
              left: 85%;
              top: 1px;
              font-size: 12px;
              padding: 0;
              text-align: center;
              min-width: 25px;
              background-color: #f9fafa;
              color: @font-color-normal !important;
            }
          }
          &:hover {
            color: #262626;
          }
        }
      }
    }
  }
}
</style>
