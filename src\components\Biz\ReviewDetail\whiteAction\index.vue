<template>
  <a-modal
    v-model="visible"
    :title="title"
    @ok="saveSqlMapParams"
    width="45%"
    :dialogStyle="{ 'minWidth': '800px', 'maxWidth': '900px' }"
    wrapClassName="biz-white-action-modal"
  >
    <!-- <a-card type="small" :bordered="false" class="sqlmap-params"> -->
    <div slot="title">
      <a-icon type="read" />
      <span style="margin-left: 4px;">SQLMAP运行状态评估:</span>
    </div>
    <div slot="extra" @click="onExpandSqlMapParams()">
      <!-- <a-icon
          :class="{
              'sqlmap-params-expand-icon': true,
              expanded: sqlMapParamsShow
            }"
          type="double-right"
      ></a-icon>-->
      <!-- <a-button type="primary" @click.stop="saveSqlMapParams">保存</a-button> -->
    </div>
    <div class="biz-white-action-content">
      <Form
        v-if="isSqlReview"
        v-bind="passTypeParams"
        ref="passTypeParams"
        :formData="sqlMapParamsData"
        class="pass-type"
      ></Form>
      <div v-if="catShow">
        <!-- <span>以下数值为从CAT上获取的调用信息。</span> -->
        <a-alert class="alert-massage small" message="以下数值为从CAT上获取的调用信息" />
        <Form ref="topFormParams" v-bind="topFormParams" :formData="catData"></Form>
      </div>
      <a-alert class="alert-massage small" message="为了帮助DBA评估SQL使用情况，请填写SQL所在表的调用情况" />
      <Form ref="sqlMapParams" v-bind="sqlMapParams" :formData="sqlMapParamsData">
        <!-- <a
          slot="average_private"
          slot-scope="{ data }"
          @click="openFrequencyModal('average', data)"
        >
          自定义输入{{
          !isNaN(+data) && data !== null ? `【QPS: ${data}】` : ''
          }}
        </a>
        <a slot="max_private" slot-scope="{ data }" @click="openFrequencyModal('max', data)">
          自定义输入{{
          !isNaN(+data) && data !== null ? `【QPS: ${data}】` : ''
          }}
        </a>-->
      </Form>
      <!-- <div class="dba-name">
          <h4>
            DBA负责人：
            <span
              :style="{'color': (operator_dba ? '' : '#bfbfbf')}"
            >{{operator_dba || '待分配'}}</span>
          </h4>
      </div>-->
    </div>
    <!-- </a-card> -->
    <a-alert v-if="promptMessage" style="text-align:center" :message="promptMessage" type="info" />
  </a-modal>
</template>
<script>
import Form from '@/components/Form';
import config from './config';
export default {
  props: {
    operator_dba: {
      type: String,
      default: ''
    },
    sqlMapParamsData: {
      type: Object,
      default: () => {}
    },
    title: {
      type: String,
      default: ''
    },
    promptMessage: {
      type: String,
      default: ''
    },
    isSqlReview: {
      type: Boolean,
      default: false
    }
  },
  components: { Form },
  data() {
    this.config = config(this);
    return {
      visible: false,
      catData: {},
      catShow: false,
      sqlMapParams: {
        fields: [],
        fixedLabel: true,
        layout: 'horizontal',
        colon: true,
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      },
      sqlMapParamsShow: false,
      topFormParams: {
        multiCols: 3,
        layout: 'horizontal',
        labelCol: { span: 12 },
        wrapperCol: { span: 10 },
        fields: this.config.fieldsTop
      },
      passTypeParams: {
        fields: this.config.passType,
        fixedLabel: true,
        layout: 'horizontal',
        colon: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 20 }
      }
    };
  },
  methods: {
    show(data) {
      this.visible = true;
      this.catData = data.catData || {};
      this.catShow = data.catShow;
      const fields =
        this.catShow == true
          ? this.config.fields().filter(item => item.visible != true)
          : this.config.fields();
      this.$set(this.sqlMapParams, 'fields', fields);
    },
    onExpandSqlMapParams() {},
    saveSqlMapParams() {
      const { sqlMapParams, topFormParams, passTypeParams } = this.$refs;
      let data = {};
      data = sqlMapParams.getData() || {};
      let passTypeObj = {};
      passTypeObj = passTypeParams.getData() || {};
      if (!passTypeObj.sqlmap_white_list) {
        passTypeObj.sqlmap_white_list = 1;
      }
      data = { ...data, ...passTypeObj };
      if (this.catShow) {
        const dataTop = {};
        const obj = topFormParams.getData();
        dataTop.sqlmap_max_frequency = obj.max_count.replace('次/分钟', '');
        dataTop.avg_excute_time = obj.avg_time.replace('毫秒', '');
        dataTop.call_time_per_day = obj.total_count.replace('次', '');
        data = { ...data, ...dataTop };
      }
      this.$emit('saveSqlMapParams', data);
      this.visible = false;
      this.$refs.sqlMapParams.resetFields();
      this.$refs.passTypeParams.resetFields();
    }
  },
  watch: {
    // catShow: {
    //   handler(val) {
    //     let index = this.config.fields.findIndex(
    //       item => item.label === '峰值调用频率'
    //     );
    //     if (val) {
    //       this.config.fields.splice(index, 1);
    //     } else {
    //       if (index === -1) {
    //         this.config.fields.unshift({
    //           type: 'RadioGroup',
    //           label: '峰值调用频率',
    //           key: 'sqlmap_max_frequency',
    //           props: {
    //             options: [
    //               { label: '低档（10次以下/分钟）', value: 'low' },
    //               { label: '中档（10-100次/分钟）', value: 'middle' },
    //               { label: '高档（100次以上/分钟）', value: 'high' }
    //             ]
    //           },
    //           width: 'auto',
    //           slots: [
    //             {
    //               key: 'max_private',
    //               wrapperStyle: { display: 'inline-block' }
    //             }
    //           ]
    //         });
    //       }
    //     }
    //   },
    //   immediate: true
    // }
  }
};
</script>

<style lang="less" scoped>
.components-biz-white-action {
  min-width: 500px;
}
.ant-col-12 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 45%;
}

/deep/ .pass-type {
  .ant-form-item-control {
    padding-top: 8px;
  }
  .ant-radio-group {
    display: flex !important;
    flex-direction: column;
    label:nth-child(odd) {
      span {
        color: #333333;
      }
    }
    label:nth-child(even) {
      padding-bottom: 8px;
      .ant-radio {
        display: none;
      }
      span {
        color: #7f7f7f;
        padding-left: 12px;
      }
    }
  }
}
/deep/ .ant-form-item {
  margin-bottom: 12px;
}
/deep/ .form .multi-cols-item {
  margin: 0 !important;
}
.alert-massage.small {
  margin: 12px 0 4px 0;
}
/deep/.form.fixed-label .ant-form-item-label label {
  justify-content: flex-start !important;
}

.biz-white-action-content {
  margin-left: 40px;
}
</style>