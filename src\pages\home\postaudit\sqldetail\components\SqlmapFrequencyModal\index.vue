<template>
  <a-modal
    v-model="visible"
    :width="500"
    :title="title"
    :maskClosable="false"
    @ok="onOk"
    @cancel="onCancel"
  >
    <Form ref="form" v-bind="params" :formData="data" class="frequency-form" />
  </a-modal>
</template>

<script>
import Form from '@/components/Form';

export default {
  components: { Form },
  props: {},
  data() {
    const checkInputNumber = (rule, value, callback) => {
      if (!value || value < 0) {
        callback(new Error('该项为必填项，且只能输入正整数'));
      } else {
        let regExp = /^\d+$/g;
        if (!regExp.test(value)) {
          callback(new Error('该项为必填项，且只能输入正整数'));
        } else {
          callback();
        }
      }
    };
    return {
      visible: false,
      title: '',
      type: '',
      params: {
        fields: [
          {
            type: 'InputNumber',
            label: '调用频率',
            key: 'frequency',
            props: {
              // min: 1
            },
            rules: [
              { validator: checkInputNumber, required: true, trigger: 'blur' }
            ]
          }
        ],
        layout: 'horizontal',
        fixedLabel: true,
        labelCol: { span: 6 },
        wrapperCol: { span: 16 },
        colon: true
      },
      data: {}
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      const { title, type, value } = data;
      this.title = title;
      this.type = type;
      this.data = {
        frequency: !isNaN(+value) ? value : undefined
      };
      this.visible = true;
    },
    hide() {
      this.visible = false;
      this.$refs.form.resetFields();
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          this.$emit('save', {
            type: this.type,
            value: form.getData().frequency
          });
          this.hide();
        }
      });
    }
  }
};
</script>

<style lang="less">
</style>
<style lang="less" scoped>
.frequency-form {
  /deep/ .ant-form-item {
    margin-bottom: 0;
  }
}
</style>
