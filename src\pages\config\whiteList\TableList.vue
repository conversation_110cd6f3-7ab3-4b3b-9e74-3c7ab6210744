<template>
  <div class="white-list-page page-list-single">
    <!-- <div class="frame-button-wrapper">
      <a-button type="danger" @click="batchDelete">批量删除</a-button>
      <a-button type="primary" @click="batchAdd">添加白名单</a-button>
    </div>-->
    <!-- <SearchArea v-bind="searchParams" :needCache="true" @reset="reset" @search="search"></SearchArea> -->
    <Table ref="table" v-bind="tableParams || {}">
      <span slot="data_source_id" slot-scope="{ record }">{{record.data_source_name}}</span>
      <span slot="action" slot-scope="{ record }">
        <a-popconfirm
          :title="activeTab === 'table' ? '确定移除此表?' : '确定移除此白名单?'"
          @confirm="() => removeItem(record)"
        >
          <a class="remove">移除</a>
        </a-popconfirm>
      </span>
      <div slot="sql_text" slot-scope="{text}">
        <LimitLabel
          format="sql"
          :contentStyle="{width:'450px', height: 'auto',overflow: 'auto'}"
          :label="text"
          :limit="20"
        ></LimitLabel>
      </div>
    </Table>
    <AddModal ref="addModal" @save="save"></AddModal>
  </div>
</template>

<script>
import Table from '@/components/Table';
import {
  removeWhiteList,
  batchRemove,
  removeWhiteListTable,
  addWhiteListTable
} from '@/api/config/whiteList';
import LimitLabel from '@/components/LimitLabel';
import AddModal from './components/AddModal';
import config from './config';
export default {
  components: {
    Table,
    LimitLabel,
    AddModal
  },
  props: {
    activeTab: {
      type: String,
      default: ''
    }
  },
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url:
          this.activeTab === 'mysql'
            ? '/sqlreview/review/white-list/'
            : '/sqlreview/review/white-list-table/',
        reqParams: {},
        columns:
          this.activeTab === 'mysql'
            ? this.config.columns
            : this.config.tableColumns,
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        rowSelection: {},
        searchFields:
          this.activeTab === 'mysql'
            ? this.config.searchFields
            : this.config.tableSearchFields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 1450 },
        cacheKey: 'white-list-' + this.activeTab
      },
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 4,
        cacheKey: 'white-list-' + this.activeTab
      },
      title: ''
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    removeItem(record) {
      this.$showLoading();
      // const { table } = this.$refs;
      if (this.activeTab === 'mysql') {
        removeWhiteList({ id: record.id })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.search(this.searchData, { keep: true, type: 'remove' });
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      } else {
        removeWhiteListTable({ ids: record.id })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.search(this.searchData, { keep: true, type: 'remove' });
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      }
    },
    // 查询
    search(data, params = {}) {
      const { table } = this.$refs;
      const { keep, type } = params;
      this.searchData = data || {};
      if (keep) {
        table.refreshKeep(type, data);
      } else {
        table.refresh(null, data);
      }
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      this.searchData = {};
      table.refresh();
    },
    // 批量删除
    del() {
      const { table } = this.$refs;
      if (table.selectedRows.length == 0) {
        this.$message.warning('请选择所要删除的项目');
        return;
      }
      const ids = table.selectedRows.length
        ? table.selectedRows.map(item => {
            return item.id;
          })
        : [];
      const params = {
        id_list: ids
      };
      this.$showLoading();
      if (this.activeTab === 'mysql') {
        batchRemove(params)
          .then(res => {
            if (res.data.code == 0) {
              this.$hideLoading({ tips: '删除成功' });
              this.search(this.searchData, { keep: true, type: 'remove' });
              table.selectedRows = [];
            } else {
              this.$hideLoading({ method: 'error', tips: res.data.message });
            }
          })
          .catch(e => {
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      } else {
        removeWhiteListTable({ ids: params.id_list })
          .then(res => {
            if (res.data.code == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.search(this.searchData, { keep: true, type: 'remove' });
              table.selectedRows = [];
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      }
    },
    // 添加白名单
    show() {
      this.$refs.addModal.show();
    },
    // save
    save(payload) {
      // const { table } = this.$refs;
      addWhiteListTable(payload)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.search(this.searchData, { keep: true });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    }
  }
};
</script>

<style scoped lang="scss">
</style>
