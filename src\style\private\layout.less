// logo位置样式
.ant-layout-sider {
  .logo {
    padding: 8px 12px 8px 48px !important;
    transition: all 0.3s;
    > img {
      width: 100px !important;
      transition: all 0.3s;
    }
    > span {
      display: none;
    }
  }

  &.menu-collapsed {
    .logo {
      padding: 8px 12px 8px 12px !important;
      > img {
        width: 56px !important;
      }
    }
  }
}

#layout-root.up-down {
  .ant-layout-header {
    background: linear-gradient(
      90deg,
      #003b72 51.04%,
      #2e76ab 100%
    ) !important;
    box-shadow: none;
    .logo {
      > img {
        width: 120px !important;
      }

      > span {
        top: 0 !important;
      }
    }
    .header-tools {
      .icon-refresh {
        color: #fff;
        &:hover {
          color: @primary-3;
        }
      }
    }
  }
}

// #layout-root.left-right-new {
//   // 左侧
//   .ant-layout-sider {
//     padding-top: 64px;
//     .ant-layout-sider-children {
//       .logo {
//         display: flex;
//         flex-direction: column;
//         padding: 8px 12px 8px 32px !important;
//         white-space: nowrap;
//         text-overflow: ellipsis;
//         overflow: hidden;
//         > img {
//           width: 120px !important;
//           margin: 8px 0;
//         }

//         > span {
//           display: inline;
//           top: 0 !important;
//           font-size: 12px;
//         }
//       }
//     }
//     .ant-menu-item {
//       &.has-divider {
//         border-bottom: 1px solid #bfbfbf;
//       }
//     }

//     // 折叠
//     &.menu-collapsed {
//       .ant-layout-sider-children {
//         .logo {
//           padding: 16px 12px 8px 12px !important;
//           > img {
//             width: 56px !important;
//           }
//         }
//       }
//     }
//   }

//   // 右侧
//   .ant-layout.layout-part-right {
//     .ant-layout-header {
//       padding: 0 20px 0 220px;
//       transition: all 0.2s;
//       display: flex;
//       align-items: center;
//     }
//     .ant-layout.content {
//       @pt: 54px;
//       padding: @pt 24px 0;

//       .navi-wrapper {
//         position: absolute;
//         left: 24px;
//         height: @pt;
//         top: 0;
//       }

//       .page-title {
//         font-size: 20px;
//         font-weight: 500;
//       }
//       // 忽略page上面高间距
//       &.ignore-page-top-space {
//         padding: 24px 24px 0;
//       }
//     }

//     // 折叠
//     &.menu-collapsed {
//       .ant-layout-header {
//         padding: 0 20px 0 100px;
//       }
//     }
//   }

//   &.show-header-menu-page {
//     .ant-layout-sider {
//       // display: none !important;
//       // left: 0;
//       // top: 60px;
//       // bottom: 0;
//     }
//     // .layout-sider-mask {
//     //   display: none !important;
//     // }
//     .layout-part-right {
//       // padding-left: 0;
//       // padding-left: 170px;
//       #rootContent {
//         // padding: 0;
//         padding: 2px 0 0 8px;
//       }
//       .navi-wrapper {
//         display: none !important;
//       }

//       &.menu-collapsed {
//         padding-left: 80px;
//       }
//     }
//     .footer {
//       display: none;
//     }
//   }
// }
