<template>
  <!-- 用户 -->
  <div style="padding:0;">
    <a-dropdown v-model="visible" placement="bottomCenter" overlayClassName="tool-user-dropdown">
      <div class="tool-user">
        <span>
          <a-avatar icon="user" />
          {{chName || userName}}
          <a-icon type="caret-down" />
        </span>
      </div>
      <a-menu slot="overlay">
        <a-menu-item key="1" @click="onModifyPwd">修改密码</a-menu-item>
      </a-menu>
    </a-dropdown>
    <!-- 修改密码弹窗 -->
    <ModifyPwd ref="modifyPwd" @save="onSave" />
  </div>
</template>

<script>
import { modifyPwd } from '@/api/common';
import ModifyPwd from '../ModifyPwd';
import { Base64 } from 'js-base64';
// import common from '@/utils/common';

export default {
  components: { ModifyPwd },
  props: {},
  data() {
    return {
      visible: false
    };
  },
  computed: {
    userName() {
      return this.$store.state.account.user.name;
    },
    chName() {
      return this.$store.state.account.user.ch_name;
    }
  },
  mounted() {},
  created() {},
  methods: {
    // 打开修改密码弹窗
    onModifyPwd() {
      const { modifyPwd } = this.$refs;
      this.visible = false;
      modifyPwd.show();
    },
    // 保存
    onSave(data = {}) {
      const { userPassword, password, passwordConfirm } = data;
      if (password !== passwordConfirm) {
        this.$message.error('新密码和确认密码不一致！');
        return;
      }
      // 请求
      this.$showLoading();
      modifyPwd({
        password: Base64.encode(password),
        user_password: Base64.encode(userPassword)
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: '修改密码成功！' });
            this.visible = false;
            this.$refs.modifyPwd.hide();
          } else {
            this.$hideLoading({ method: 'error', tips: _.get(res, 'data.message') });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    }
  }
};
</script>

<style lang="less">
.tool-user-dropdown {
  top: 64px !important;
  // width: 80px;
  > ul {
    border-radius: 0;
    li {
      text-align: center;
    }
  }
}
</style>
<style lang="less" scoped>
.tool-user {
  padding: 0 16px;
  .ant-avatar {
    margin-right: 8px;
    background: @primary-4;
  }
}
</style>
