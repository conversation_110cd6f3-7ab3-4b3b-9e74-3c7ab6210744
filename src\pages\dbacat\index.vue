<template>
  <div class="page-dbacat">
    <TimeFilter />
    <div class="page-dbacat-base-info">
      <span
        >数据库面板服务<a-tag style="margin-left: 4px;" color="green"
          >正常</a-tag
        ></span
      >
      <span style="margin-left: 24px;">
        <label>实例名称：</label>
        <DataBaseChoose style="width: 350px;" v-bind="instanceParams" />
      </span>
    </div>
    <Detail />
  </div>
</template>

<script>
// import {
//   getTask,
//   getReportIndex,
//   getReportVersion,
//   getFeedBack,
//   getReportCodeReview,
//   getReportTopSql,
//   getReportDatabaseAudit,
//   getDatabaseMonitorData
// } from '@/api/dataview';
// import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import DataBaseChoose from '@/components/Biz/DataBaseChoose';
import TimeFilter from './components/TimeFilter';
import Detail from './components/Detail';

export default {
  mixins: [bodyMinWidth(1440)],
  components: { TimeFilter, Detail, DataBaseChoose },
  data() {
    // this.config = config(this);
    return {
      instanceParams: {
        url: '/sqlreview/project/data_source_choices',
        reqParams: {},
        placeholder: '数据源',
        class: 'selected-children',
        loaded(data) {
          // ctx.dataSourceOption = data;
        },
        beforeLoaded(data) {
          return data.map(item => {
            return {
              ...item,
              instance_usage: item.env,
              showText: item.label + '(' + item.db_url + ')'
            };
          });
        },
        getPopupContainer: el => {
          return document.body;
        },
        mode: 'default',
        optionLabelProp: 'children',
        allowSearch: true,
        backSearch: true,
        limit: 50,
        allowClear: false
      }
    };
  },
  created() {},
  mounted() {
    // this.init();
  },
  beforeDestroy() {},
  methods: {
    init() {}
  }
};
</script>

<style lang="less" scoped>
.page-dbacat {
  background: #ffffff;
  padding: 12px;
}
@media screen and(max-width: 1700px) {
  .page-dbacat {
  }
}
</style>
