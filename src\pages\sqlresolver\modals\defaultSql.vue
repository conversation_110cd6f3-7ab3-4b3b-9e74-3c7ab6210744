<template>
  <a-modal
    v-model="visible"
    :title="title"
    okText="保存"
    :maskClosable="false"
    :width="'50%'"
    :dialogStyle="{ 'minWidth': '400px', 'maxWidth': '800px' }"
    wrapClassName="page-sqlresolver-query-default-sql"
    @cancel="onCancel"
    @ok="onOk"
  >
    <custom-form ref="form" v-bind="formParams" :formData="formData"></custom-form>
  </a-modal>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    this.instanceList = [];
    return {
      visible: false,
      isEdit: false,
      title: '请输入信息',
      formParams: {
        fields: this.getFields(),
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        colon: true
      },
      formData: {},
      data: {}
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.data = data;
      if (data.title) {
        this.title = data.title;
      }
      // this.$set(this.formParams, 'fields', this.getFields());
      // this.isEdit = data.id != null;
    },
    hide() {
      this.visible = false;
      this.formData = {};
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      // const { data } = this;
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          this.hide();
          this.$emit('save', { formData: form.getData(), item: this.data.item });
        }
      });
    },
    getFields() {
      return [
        {
          type: 'Input',
          label: '名称',
          key: 'name',
          // width: 300,
          props: {},
          listeners: {},
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ]
        },
        {
          type: 'Textarea',
          label: '注释',
          key: 'comment',
          // width: 300,
          props: {},
          listeners: {},
          rules: [
            // { required: true, message: '该项为必填项', trigger: 'change' }
          ]
        }
      ];
    }
  }
};
</script>

<style lang="less">
.page-sqlresolver-query-default-sql {
}
</style>