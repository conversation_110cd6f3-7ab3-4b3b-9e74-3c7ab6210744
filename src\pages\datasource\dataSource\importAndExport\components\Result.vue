<template>
  <div class="result-page">
    <div :class="['text-content', `status-${status}`]">
      <custom-icon :type="statusIcon[status]" />
      <div>
        <div class="bold">{{type + statusText[status]}}</div>
        <div class="dark">
          <span>{{createdBy}}</span>
          <span>{{createdAt}}</span>
        </div>
      </div>
    </div>
    <div class="btn-area">
      <a-button class="highlight" @click="importAgain">继续{{this.type == '导入' ? '导入' : '导出'}}</a-button>
      <a-button type="primary" @click="toDataSourcePage">数据源配置</a-button>
    </div>
    <div class="result-table">
      <div class="header-info">
        <div class="left">
          <custom-icon type="calendar" />
          <div>{{type + '结果'}}</div>
        </div>
        <div class="right">
          <a-button class="highlight" @click="downloadAll" v-if="this.type == '导出文件'">全部下载</a-button>
          <span v-if="sDbType" class="target-database">{{'源数据库: '}}</span>
          <InstanceItem
            view="new"
            mode="ellipsis"
            :tagText="sEnv"
            :src="sDbType"
            :text="sName"
            :isNeedTips="false"
            v-if="sDbType"
          />
          <span v-if="tDbType" class="target-database">{{'目标数据库: '}}</span>
          <InstanceItem
            view="new"
            mode="ellipsis"
            :tagText="tEnv"
            :src="tDbType"
            :text="tName"
            :isNeedTips="false"
            v-if="tDbType"
          />
        </div>
      </div>
      <Table ref="table" v-bind="tableParams || {}" :dataSource="dataSource">
        <template slot="status" slot-scope="{text}">
          <span :class="['status-info', `status-${text}`]">{{statusText[text]}}</span>
        </template>
        <template slot="file_path" slot-scope="{text, record}">
          <span>{{text}}</span>
          <custom-icon type="download" @click="downLoad(record)" class="download-icon" />
        </template>
      </Table>
    </div>
  </div>
</template>
<script>
import config from './config';
import Table from '@/components/Table';
import InstanceItem from '@/components/Biz/InstanceItem';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import common from '@/utils/common';
import { downloadSqlFile } from '@/api/config/dataSource';
export default {
  mixins: [bodyMinWidth(1280)],
  name: 'import-and-export',
  components: { Table, InstanceItem },
  props: {
    resultData: {
      type: Object,
      default: () => {}
    },
    type: String,
    taskId: Number | String
  },
  data() {
    this.config = config(this);
    return {
      dataSource: [],
      tableParams: {
        url: '',
        reqParams: {},
        columns: this.getColumns(),
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      createdAt: '',
      createdBy: '',
      tDbType: null,
      tEnv: null,
      tName: null,
      sDbType: null,
      sEnv: null,
      sName: null,
      status: 1,
      statusText: {
        1: '成功',
        2: '失败'
      },
      statusIcon: {
        1: 'lu-icon-success',
        2: 'lu-icon-error'
      }
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    getColumns() {
      if (this.type == '导入') {
        return this.config.columns;
      } else if (this.type == '导出到目标数据库') {
        return this.config.resColumns.filter(item => item.visible !== true);
      } else if (this.type == '导出到文件') {
        return this.config.resColumns.filter(item => item.visible !== false);
      }
      return this.config.columns;
    },
    importAgain() {
      this.$emit('carryOn');
    },
    downLoad(record) {
      this.$showLoading({
        tips: `下载中...`
      });
      const params = { id: record.id, type: 'single' };
      downloadSqlFile(params)
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    downloadAll() {
      this.$showLoading({
        tips: `下载中...`
      });
      const params = { id: this.taskId, type: 'batch' };
      downloadSqlFile(params)
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 去数据源配置页面
    toDataSourcePage() {
      this.$router.push({
        name: 'data-source-config'
      });
    }
  },
  watch: {
    resultData: {
      handler(newVal) {
        this.dataSource = newVal.results;
        this.createdAt = newVal.created_at;
        this.createdBy = newVal.created_by;
        this.tDbType = newVal.t_db_type;
        this.tEnv = newVal.t_env;
        this.tName = newVal.t_name;
        this.status = newVal.status;
        this.sDbType = newVal.s_db_type;
        this.sEnv = newVal.s_env;
        this.sName = newVal.s_name;
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.result-page {
  padding: 50px 80px;
  .text-content {
    display: flex;
    padding: 20px;
    align-items: center;
    background: #ecf5ee;
    &.status-2 {
      background: #fdeff1;
    }
    .anticon {
      font-size: 48px;
      color: #008adc;
      margin-right: 20px;
    }
    > div {
      .bold {
        font-size: 13px;
        color: #27272a;
        font-weight: 600;
        margin-bottom: 8px;
      }
      .dark {
        span {
          font-size: 13px;
          color: #a1a1a1;
          font-weight: 400;
        }
      }
    }
  }
  /deep/.btn-area {
    margin-top: 32px;
    display: flex;
    .ant-btn {
      display: flex;
      align-items: center;
      border: 1px solid #008adc;
      margin-right: 32px;
      > span {
        padding: 0 24px;
        font-size: 14px;
        font-weight: 600;
      }
    }
    .ant-btn-primary {
      border: none;
    }
  }
  /deep/.result-table {
    margin-top: 54px;
    .header-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      flex-wrap: wrap;
      > div {
        display: flex;
        align-items: center;
        &.left {
          white-space: nowrap;
          font-size: 14px;
          color: #000000;
          .anticon-calendar {
            margin-right: 4px;
          }
        }
        &.right {
          .ant-btn {
            display: flex;
            align-items: center;
            border: 1px solid #008adc;
            height: 24px;
            > span {
              font-size: 12px;
            }
          }
          > span {
            font-size: 12px;
            white-space: nowrap;
            color: rgba(0, 0, 0, 0.25);
          }
          .target-database:last-child {
            margin-left: 12px;
          }
          .biz-instance-item {
            .ant-tag {
              border: none;
            }
          }
        }
      }
    }
    .status-info {
      &.status-2 {
        color: #ef6173;
      }
    }
    .download-icon {
      color: #008adc;
      margin-left: 8px;
      font-size: 16px;
    }
    .custom-table {
      .ant-table-content {
        .ant-table-body {
          border: 1px solid #ebebec;
          .ant-table-thead {
            > tr {
              > th {
                border-right: 1px solid #ebebec;
                &:last-child {
                  border-right: none;
                }
              }
            }
          }
          .ant-table-tbody {
            > tr {
              > td {
                border-bottom: 1px solid #ebebec;
                border-right: 1px solid #ebebec;
                &:last-child {
                  border-right: none;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
