<template>
  <div
    :class="
      `search-area theme-${mode} ${needToggle && 'need-toggle'} ${isToggling &&
        'is-toggling'} ${expanded && 'expanded'} ${isFullLine &&
        'is-full-line'}`
    "
    :style="{ height: needToggle ? getToggleStartHeight() : 'auto' }"
  >
    <Form
      ref="form"
      :fields="localFields"
      v-bind="searchProps"
      :formData="data"
      class="fixed-label-left"
      @submit.prevent="search"
    >
      <div class="seach-area-btns" slot="_searchBtns">
        <a-button @click="reset">重置</a-button>
        <a-button html-type="submit" type="primary">查询</a-button>
      </div>
    </Form>
    <div class="toggle" v-if="needToggle" @click="onToggle">
      <a-icon :class="`${expanded && 'expanded'}`" type="double-right"></a-icon>
    </div>
  </div>
</template>

<script>
import Form from '@/components/Form';
// import common from '@/utils/common';
import _ from 'lodash';
const defaultSearchProps = {
  fixedLabel: true,
  layout: 'horizontal',
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
  multiCols: 3,
  gutter: 48
  // colon: true
};

export default {
  components: { Form },
  props: {
    fields: {
      type: Array,
      default: function() {
        return [];
      }
    },
    mode: {
      type: String,
      default: 'multi' // 'multi' | 'inline'
    },
    // 多列布局（只支持能整除24的数字）
    multiCols: {
      type: Number,
      default: 3
    },
    gutter: {
      type: Number,
      default: 48
    },
    searchData: {
      type: Object,
      default: () => ({})
    },
    needCache: {
      type: Boolean,
      default: false
    },
    cacheKey: {
      type: String,
      default: ''
    },
    maxLine: {
      type: Number,
      default: 999
    },
    adapted: {
      type: Boolean,
      default: false
    },
    needAdapter: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const { needCache, cacheKey } = this;
    let initData = {};
    if (needCache) {
      const _key = cacheKey || this.$route.name;
      initData = _.merge({}, this.$store.state.common.searchCache[_key] || {});
    }
    return {
      data: initData,
      cols: this.multiCols,
      localMaxLine: this.maxLine,
      localFields: this.fields,
      expanded: false,
      isToggling: false,
      labelOverflow: false,
      screenType: 'normal'
    };
  },
  computed: {
    isInline() {
      return this.mode === 'inline';
    },
    searchProps() {
      const { cols, gutter, labelOverflow } = this;
      const { labelCol, wrapperCol, colon } = this.$attrs;
      const tmp = {
        multiCols: cols,
        gutter,
        labelCol,
        wrapperCol,
        labelOverflow,
        colon
      };
      let props = {};

      _.forEach(tmp, (item, key) => {
        if (item) {
          props[key] = item;
        }
      });

      if (this.isInline) {
        props.multiCols = null;
        props.free = true;
      }
      return { ...defaultSearchProps, ...props };
    },
    isFullLine() {
      const { fields, cols } = this;
      return fields.length % cols === 0;
    },
    totalLines() {
      const { fields, cols } = this;
      const lines = Math.ceil(fields.length / cols);
      return lines;
    },
    needToggle() {
      return !this.isInline && this.localMaxLine < this.totalLines;
    }
  },
  created() {
    const screenType = this.setInfoByWindow();
    this.setFormItemsVisible();
    this.screenType = screenType;
  },
  mounted() {
    if (this.needCache) {
      // 默认需要向外emit，配合外部表格的isInitReq=false
      this.emitSearchData(this.data);
    }
    // 添加适配
    this.needAdapter && this.addAdapter();
  },
  destroyed() {
    if (this.resize) {
      this.$bus.$off('contentResize', this.resize);
      this.resize = null;
    }
  },
  methods: {
    reset() {
      const { form } = this.$refs;
      this.data = {};
      form.resetFields();
      this.$emit('reset', form.getData());
      this.emitCacheData({});
    },
    search() {
      console.log(this.data);
      this.emitCacheData();
      this.emitSearchData();
    },
    emitSearchData(data) {
      const { fields } = this;
      const { form } = this.$refs;
      const formData = data || form.getData();
      let res = {};

      // 处理数据
      _.forEach(formData, (item, key) => {
        if (key === '_searchBtns') return;
        let match = fields.find(f => f.key === key) || {};
        if (match.type === 'RangePicker') {
          res[key] = item
            ? item.map(time => time.format('YYYY-MM-DD HH:mm:ss')).join(',')
            : item;
        } else if (match.type === 'Input') {
          res[key] = _.isString(item) ? item.trim() : item;
        } else {
          res[key] = item;
        }
      });
      this.$emit('search', res);
    },
    emitCacheData(data) {
      const { needCache, cacheKey } = this;
      const { form } = this.$refs;
      const formData = data || form.getData();
      // 全局存储
      if (needCache) {
        const _key = cacheKey || this.$route.name;
        this.$store.commit('common/setSearchCache', {
          [_key]: formData
        });
      }
    },
    getToggleStartHeight() {
      return this.localMaxLine * 52 + 44 + 'px';
    },
    setFormItemsVisible(expanded) {
      const { needToggle, localMaxLine, cols, fields } = this;
      let showNums = expanded || !needToggle ? 999 : localMaxLine * cols - 1;
      let arr = fields.map((item, index) => {
        !this.isInline && (item.hidden = index > showNums - 1);
        if (!item.props) {
          item.props = {};
        }
        Object.assign(item.props, {
          getPopupContainer: el => {
            return this.$el.parentNode;
          }
        });
        return item;
      });
      let btnItem = {
        type: 'Input',
        key: '_searchBtns',
        label: '',
        hideComponent: true,
        slots: [{ key: '_searchBtns' }],
        className: 'search-btns-item'
      };
      if (showNums === 999) {
        arr.push(btnItem);
      } else {
        arr.splice(showNums, 0, btnItem);
      }

      this.localFields = arr;
    },
    onToggle() {
      const Action = expanded => {
        let dom = this.$el;
        CommonUtil.toggleDom({
          element: dom,
          time: 0.3,
          startHeight: this.getToggleStartHeight(),
          show: expanded
        });
      };

      this.isToggling = true;
      this.$nextTick(() => {
        if (!this.expanded) {
          Action(true);
          setTimeout(() => {
            this.setFormItemsVisible(true);
          }, 300);
        } else {
          this.setFormItemsVisible(false);
          Action(false);
        }
        this.expanded = !this.expanded;
      });

      setTimeout(() => {
        this.isToggling = false;
      }, 300);
    },
    addAdapter() {
      this.$bus.$on(
        'contentResize',
        (this.resize = _.debounce(() => {
          this.isToggling = true;
          const screenType = this.setInfoByWindow();
          if (screenType !== this.screenType) {
            this.screenType = screenType;
            this.setFormItemsVisible(screenType !== 'small');
          }
          setTimeout(() => {
            this.isToggling = false;
          }, 300);
        }, 300))
      );
    },
    setInfoByWindow() {
      // if (!this.adapted) {
      //   return;
      // }
      const bodyWidth = document.body.clientWidth;
      // let labelOverflow = false;
      let cols = this.multiCols;
      let maxLine = this.maxLine;
      let screenType = 'normal';
      if (bodyWidth <= 1366) {
        // labelOverflow = true;
        cols = 2;
        maxLine = 1;
        screenType = 'small';
      } else {
        this.expanded = false;
      }
      // this.labelOverflow = labelOverflow;
      this.cols = cols;
      this.localMaxLine = maxLine;
      // console.log(smallScreen, labelOverflow, cols, maxLine, 898);
      return screenType;
    }
  },
  watch: {
    searchData: {
      handler(newVal, oldVal) {
        const { needCache, cacheKey } = this;
        const _key = cacheKey || this.$route.name;
        const initData = _.merge(
          {},
          this.$store.state.common.searchCache[_key] || {}
        );
        if (!needCache || _.isEmpty(initData)) {
          this.data = newVal || {};
        }
      },
      immediate: true
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.search-area {
  position: relative;
  padding: 12px 16px 0 16px;
  margin-bottom: 12px;
  border-radius: 2px;
  border-radius: 2px;
  // border: 1px solid #eef2fb;
  // box-shadow: 2px 2px 2px #f1f5ff;
  border: 1px solid #f2f2f2;
  box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.3);
  /deep/ .form.fixed-label {
    .ant-col {
      vertical-align: top;
    }
    .ant-form-item-label {
      // text-align: left;
      // justify-content: flex-start;
      max-width: 120px;
      min-width: 80px;

      > label {
        // justify-content: flex-start;
      }
    }
    .ant-form-item-control-wrapper {
      flex-grow: 1;
      flex: auto;
      width: auto;
    }
    .multi-cols-item {
      display: flex;
      margin: 0 0 12px 0;
    }

    // 操作按钮项
    .search-btns-item {
      float: right;
      .ant-form-item {
        .ant-form-item-label {
          display: none;
        }
        .ant-form-item-control-wrapper {
        }
      }
    }
  }

  /deep/ .form.free {
    display: flex;
    flex-wrap: wrap;
    > .ant-form-item {
      display: inline-flex !important;
      margin-bottom: 12px;
    }
    .ant-form-item-control {
      width: 220px;
    }
    .ant-form-item-label {
      max-width: 80px;
      min-width: 80px;
      > label {
        justify-content: flex-end;
      }
    }

    // 操作按钮项
    .search-btns-item {
      flex-grow: 1;
      margin-right: 8px;
      > .ant-form-item-control-wrapper {
        display: flex;
        justify-content: flex-end;
        .ant-form-item-control {
          width: auto;
        }
      }
    }
  }

  .seach-area-btns {
    // position: absolute;
    // right: 16px;
    // bottom: 16px;
    text-align: right;

    .ant-btn {
      margin-left: 8px;
    }
  }

  .toggle {
    // margin-right: 8px;
    cursor: pointer;
    line-height: 32px;
    text-align: center;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fdfdfd;
    .anticon {
      transform: rotate(90deg);
      &.expanded {
        transform: rotate(270deg);
      }
    }
    &:hover {
      color: @primary-color;
      // background: @primary-1;
    }
  }

  &.is-full-line {
    // padding-bottom: 48px;
  }
  &.need-toggle {
    padding-bottom: 24px;
    overflow: hidden;

    // .seach-area-btns {
    //   bottom: 46px;
    // }
  }
  // &.need-toggle.expanded {
  //   .seach-area-btns {
  //     bottom: 40px;
  //   }
  // }
  // &.need-toggle.is-full-line {
  //   padding-bottom: 72px;
  // }
  &.is-toggling {
    /deep/ .ant-form {
      * {
        transition: none;
      }
    }
  }
  &.theme-inline {
    padding: 20px 12px 8px 12px;
    box-shadow: none;
    border: 0;
    background: transparent;
  }
}
</style>
