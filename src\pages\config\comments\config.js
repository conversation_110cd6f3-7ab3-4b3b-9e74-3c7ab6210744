export default function (ctx) {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '应用分类',
      dataIndex: 'review_type_name',
      key: 'review_type_name',
      width: 100
    },
    {
      title: '意见类型',
      dataIndex: 'type',
      key: 'type',
      customRender: (text, record, index) => {
        return text === 'PASS' ? '通过' : '不通过';
      },
      width: 100
    },
    {
      title: '意见描述',
      dataIndex: 'desc',
      key: 'desc',
      scopedSlots: { customRender: 'desc' },
      width: 150
    },
    {
      title: '排序',
      dataIndex: 'order',
      key: 'order',
      width: 100
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 100
    }
  ];
  const searchFields = [
    {
      type: 'Select',
      label: '应用分类',
      key: 'review_type',
      mainSearch: true,
      props: {
        placeholder: '请选择应用分类',
        url: '/sqlreview/common/item-list/',
        reqParams: {
          parent_item_key: 'app_classify'
        },
        loading: true
      }
    },
    {
      type: 'Select',
      label: '意见类型',
      key: 'type',
      props: {
        options: [
          { label: '通过', value: 'PASS' },
          { label: '不通过', value: 'NOT_PASS' }
        ]
      }
    },
    {
      type: 'Input',
      label: '意见描述',
      key: 'search'
    }
  ];
  return {
    columns,
    searchFields
  };
}
