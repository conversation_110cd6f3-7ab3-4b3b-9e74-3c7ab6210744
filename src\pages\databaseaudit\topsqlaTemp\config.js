import moment from 'moment';
export default function (ctx) {
  const columns = [
    {
      title: '标签',
      dataIndex: 'label_attribute',
      key: 'label_attribute',
      scopedSlots: { customRender: 'label_attribute' },
      width: 180
    },
    {
      title: 'SQL_ID',
      dataIndex: 'sql_id',
      key: 'sql_id',
      scopedSlots: { customRender: 'sql_id' },
      width: 180
    },
    {
      title: '执行时间',
      dataIndex: 'execution_time',
      key: 'execution_time',
      scopedSlots: { customRender: 'execution_time' },
      width: 120
    },
    {
      title: '执行用户',
      dataIndex: 'execution_user',
      key: 'execution_user',
      scopedSlots: { customRender: 'execution_user' },
      width: 120
    },
    {
      title: 'SQL文本',
      key: 'sql_text',
      dataIndex: 'sql_text',
      scopedSlots: { customRender: 'sql_text' },
      width: 120
    },
    {
      title: '执行Schema',
      key: 'executive_schema',
      dataIndex: 'executive_schema',
      width: 120
    },
    {
      title: '执行计划',
      key: 'plan_hash_value',
      dataIndex: 'plan_hash_value',
      scopedSlots: { customRender: 'plan_hash_value' },
      width: 120
    },
    {
      title: '数据来源',
      dataIndex: 'source_type',
      key: 'source_type',
      scopedSlots: { customRender: 'source_type' },
      width: 200
    },
    {
      title: '执行账号',
      dataIndex: 'executive_user',
      key: 'executive_user',
      width: 200
    },
    {
      title: '执行总耗时',
      key: 'execution_total_time',
      dataIndex: 'execution_total_time',
      width: 300,
      scopedSlots: { customRender: 'execution_total_time' }
    },
    {
      title: '执行平均耗时',
      dataIndex: 'execution_average_time',
      key: 'execution_average_time',
      scopedSlots: { customRender: 'execution_average_time' },
      width: 120
    },
    {
      title: '最大耗时',
      key: 'execution_maximum_time',
      dataIndex: 'execution_maximum_time',
      width: 300,
      scopedSlots: { customRender: 'execution_maximum_time' }
    },
    {
      title: '执行次数',
      key: 'execution_number',
      dataIndex: 'execution_number',
      width: 300,
      scopedSlots: { customRender: 'execution_number' }
    },
    {
      title: '执行时间95值',
      dataIndex: 'execution_time_95',
      key: 'execution_time_95',
      scopedSlots: { customRender: 'execution_time_95' },
      width: 120
    },
    {
      title: '锁时间',
      key: 'lock_time_95',
      dataIndex: 'lock_time_95',
      width: 300,
      scopedSlots: { customRender: 'lock_time_95' }
    },
    {
      title: '返回行数95值',
      dataIndex: 'returns_rows_sent_95',
      key: 'returns_rows_sent_95',
      scopedSlots: { customRender: 'returns_rows_sent_95' },
      width: 120
    },
    {
      title: '返回总行数',
      key: 'returns_total_rows',
      dataIndex: 'returns_total_rows',
      width: 300,
      scopedSlots: { customRender: 'returns_total_rows' }
    },
    {
      title: '平均返回行数',
      key: 'returns_average_rows',
      dataIndex: 'returns_average_rows',
      width: 300,
      scopedSlots: { customRender: 'returns_average_rows' }
    },
    {
      title: '风险等级',
      key: 'risk',
      dataIndex: 'risk',
      width: 300,
      scopedSlots: { customRender: 'risk' }
    },
    {
      title: '平均逻辑读',
      key: 'logical_avg_read',
      dataIndex: 'logical_avg_read',
      width: 300,
      scopedSlots: { customRender: 'logical_avg_read' }
    },
    {
      title: '平均物理读',
      key: 'physical_avg_read',
      dataIndex: 'physical_avg_read',
      width: 300,
      scopedSlots: { customRender: 'returns_average_rows' }
    },
    {
      title: '平均排序消耗',
      key: 'sort_avg_cost',
      dataIndex: 'sort_avg_cost',
      width: 300,
      scopedSlots: { customRender: 'sort_avg_cost' }
    },
    {
      title: '平均IO等待时间',
      key: 'io_avg_wait',
      dataIndex: 'io_avg_wait',
      width: 300,
      scopedSlots: { customRender: 'io_avg_wait' }
    },
    {
      title: '平均集群等待时间(ms)',
      key: 'cluster_avg_wait',
      dataIndex: 'cluster_avg_wait',
      width: 300,
      scopedSlots: { customRender: 'cluster_avg_wait' }
    },
    {
      title: '快照开始时间',
      key: 'start_time',
      dataIndex: 'start_time',
      width: 300,
      scopedSlots: { customRender: 'start_time' }
    },
    {
      title: '快照结束时间',
      key: 'end_time',
      dataIndex: 'end_time',
      width: 300,
      scopedSlots: { customRender: 'end_time' }
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 170,
      fixed: 'right',
      visible: $permissionBatch.some([
        { module: 'topSql', values: ['explain'] }
      ])
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  })

  const searchFields = (dbType) => {
    return [
      {
        type: 'RangePicker',
        // label: '时间范围',
        key: 'execution_time',
        compIcon: 'calendar',
        props: {
          showTime: {
            format: 'HH:mm'
          },
          format: 'YYYY-MM-DD HH:mm',
          ranges: {
            '昨天': [moment().subtract(48, 'hour'), moment().subtract(24, 'hour')],
            '近7天': [moment().subtract(7, 'day'), moment()],
            '近10天': [moment().subtract(10, 'day'), moment()],
            '近15天': [moment().subtract(15, 'day'), moment()],
            '近30天': [moment().subtract(30, 'day'), moment()]
          },
          disabledDate: (current) => {
            return (
              current <= moment().subtract(120, 'days') ||
              current > moment().endOf('day')
            );
          }
        },
        listeners: {
          change: (dates) => {
            const first = dates[0]
            const second = dates[1]
            let days = first.diff(second, 'days');
            if (days > 30 || days < -30) {
              ctx.$message.warning('所选值范围不能超过30天');
              ctx.$refs.search.saving({ execution_time: [] })
            }
          }
        },
        visible: dbType == 'DB2' ? ctx.displayType !== '汇总' : true
      },
      {
        type: 'Select',
        // label: '执行数据库',
        key: 'executive_schema',
        compIcon: 'lu-icon-database',
        props: {
          placeholder: '执行数据库',
          url: '/sqlreview/after_audit/select_data',
          reqParams: {
            data_source_id: ctx.id,
            select_type: 'schema'
          },
          allowSearch: true,
          backSearch: true,
          limit: 50
        },
        visible: dbType !== 'ORACLE' && dbType !== 'DB2'
      },
      {
        type: 'Select',
        // label: '执行用户',
        key: 'executive_user',
        compIcon: 'user',
        props: {
          placeholder: '执行用户',
          url: '/sqlreview/after_audit/select_data',
          reqParams: {
            data_source_id: ctx.id,
            select_type: 'user'
          },
          allowSearch: true,
          backSearch: true,
          limit: 50
        },
        visible: dbType !== 'DB2'
      },
      {
        type: 'Input',
        // label: 'SQL_ID',
        compIcon: 'lu-icon-ID',
        key: 'sql_id',
        props: {
          placeholder: 'SQL_ID'
        }
      },
      {
        type: 'Input',
        // label: 'SQL文本',
        compIcon: 'lu-icon-field',
        key: 'sql_text',
        props: {
          placeholder: 'SQL文本'
        }
      },
      {
        type: 'Select',
        // label: 'AI风险等级',
        compIcon: 'lu-icon-alarm',
        key: 'risk',
        props: {
          placeholder: 'AI风险等级',
          options: [
            { label: '待审核', value: 0 },
            { label: '高风险', value: -1 },
            { label: '无风险', value: 1 },
            { label: '低风险', value: 2 },
            { label: '异常', value: 9 },
            { label: '审核中', value: 3 }
          ]
        },
        visible: !['DB2', 'GAUSSDB'].includes(dbType)
      }
    ];
  };

  const obColumns = [
    {
      title: '标签',
      dataIndex: 'label_attribute',
      key: 'label_attribute',
      scopedSlots: { customRender: 'label_attribute' },
      width: 180
    },
    {
      title: 'SQL_ID',
      dataIndex: 'sql_id',
      key: 'sql_id',
      scopedSlots: { customRender: 'sql_id' },
      width: 180
    },
    {
      title: 'OB SQL_ID',
      dataIndex: 'ob_id',
      key: 'ob_id',
      scopedSlots: { customRender: 'ob_id' },
      width: 180
    },
    {
      title: '执行时间',
      dataIndex: 'execution_time',
      key: 'execution_time',
      scopedSlots: { customRender: 'execution_time' },
      width: 120
    },
    {
      title: '执行用户',
      dataIndex: 'execution_user',
      key: 'execution_user',
      scopedSlots: { customRender: 'execution_user' },
      width: 120
    },
    {
      title: 'SQL文本',
      key: 'sql_text',
      dataIndex: 'sql_text',
      scopedSlots: { customRender: 'sql_text' },
      width: 120
    },
    {
      title: '执行平均耗时',
      dataIndex: 'avg_execute_time',
      key: 'avg_execute_time',
      scopedSlots: { customRender: 'avg_execute_time' },
      width: 120
    },
    {
      title: '执行次数',
      key: 'execution_number',
      dataIndex: 'execution_number',
      width: 300,
      scopedSlots: { customRender: 'execution_number' }
    },
    {
      title: '风险等级',
      key: 'risk',
      dataIndex: 'risk',
      width: 300,
      scopedSlots: { customRender: 'risk' }
    },
    {
      title: '平均物理读次数',
      key: 'avg_disk_reads',
      dataIndex: 'avg_disk_reads',
      width: 300,
      scopedSlots: { customRender: 'avg_disk_reads' }
    },
    {
      title: '统计时间',
      key: 'statistics_time',
      dataIndex: 'statistics_time',
      width: 300,
      scopedSlots: { customRender: 'statistics_time' }
    },
    {
      title: '服务器IP',
      key: 'svr_ip',
      dataIndex: 'svr_ip',
      width: 300,
      scopedSlots: { customRender: 'svr_ip' }
    },
    {
      title: 'OB端口',
      key: 'svr_port',
      dataIndex: 'svr_port',
      width: 300,
      scopedSlots: { customRender: 'svr_port' }
    },
    {
      title: '数据库名',
      key: 'db_name',
      dataIndex: 'db_name',
      width: 300,
      scopedSlots: { customRender: 'db_name' }
    },
    {
      title: '租户名',
      key: 'tenant_name',
      dataIndex: 'tenant_name',
      width: 300,
      scopedSlots: { customRender: 'tenant_name' }
    },
    {
      title: '用户名',
      key: 'user_name',
      dataIndex: 'user_name',
      width: 300,
      scopedSlots: { customRender: 'user_name' }
    },
    {
      title: '是否全表扫描',
      key: 'is_full_scan',
      dataIndex: 'is_full_scan',
      width: 300,
      customRender: (text) => {
        return text ? '是' : '否';
      }
    },
    {
      title: '最大响应耗时(ms)',
      key: 'max_elapsed_time',
      dataIndex: 'max_elapsed_time',
      width: 300,
      scopedSlots: { customRender: 'max_elapsed_time' }
    },
    {
      title: '最大网络等待耗时(ms)',
      key: 'max_net_time',
      dataIndex: 'max_net_time',
      width: 300,
      scopedSlots: { customRender: 'max_net_time' }
    },
    {
      title: '最大DECODE耗时(ms)',
      key: 'max_decode_time',
      dataIndex: 'max_decode_time',
      width: 300,
      scopedSlots: { customRender: 'max_decode_time' }
    },
    {
      title: '最大执行计划生成耗时(ms)',
      key: 'max_get_plan_time',
      dataIndex: 'max_get_plan_time',
      width: 300,
      scopedSlots: { customRender: 'max_get_plan_time' }
    },
    {
      title: '最大SQL执行耗时(ms)',
      key: 'max_execute_time',
      dataIndex: 'max_execute_time',
      width: 300,
      scopedSlots: { customRender: 'max_execute_time' }
    },
    {
      title: '最大扫描分区数量',
      key: 'max_partition_cnt',
      dataIndex: 'max_partition_cnt',
      width: 300,
      scopedSlots: { customRender: 'max_partition_cnt' }
    },
    {
      title: '最大影响行数',
      key: 'max_affected_rows',
      dataIndex: 'max_affected_rows',
      width: 300,
      scopedSlots: { customRender: 'max_affected_rows' }
    },
    {
      title: '最大返回行数',
      key: 'max_return_rows',
      dataIndex: 'max_return_rows',
      width: 300,
      scopedSlots: { customRender: 'max_return_rows' }
    },
    {
      title: '最大物理读次数(ms)',
      key: 'max_disk_reads',
      dataIndex: 'max_disk_reads',
      width: 300,
      scopedSlots: { customRender: 'max_disk_reads' }
    },
    {
      title: '最大等待（网络/队列）耗时(ms)',
      key: 'max_wait_time',
      dataIndex: 'max_wait_time',
      width: 300,
      scopedSlots: { customRender: 'max_wait_time' }
    },
    {
      title: '最大并发等待耗时(ms)',
      key: 'max_concurrency_wait_time',
      dataIndex: 'max_concurrency_wait_time',
      width: 300,
      scopedSlots: { customRender: 'max_concurrency_wait_time' }
    },
    {
      title: '最大CPU耗时(ms)',
      key: 'max_cpu_time',
      dataIndex: 'max_cpu_time',
      width: 300,
      scopedSlots: { customRender: 'max_cpu_time' }
    },
    {
      title: '总响应耗时(ms)',
      key: 'total_elapsed_time',
      dataIndex: 'total_elapsed_time',
      width: 300,
      scopedSlots: { customRender: 'total_elapsed_time' }
    },
    {
      title: '总网络耗时(ms)',
      key: 'total_net_time',
      dataIndex: 'total_net_time',
      width: 300,
      scopedSlots: { customRender: 'total_net_time' }
    },
    {
      title: '总DECODE耗时(ms)',
      key: 'total_decode_time',
      dataIndex: 'total_decode_time',
      width: 300,
      scopedSlots: { customRender: 'total_decode_time' }
    },
    {
      title: '总执行计划生成时间(ms)',
      key: 'total_get_plan_time',
      dataIndex: 'total_get_plan_time',
      width: 300,
      scopedSlots: { customRender: 'total_get_plan_time' }
    },
    {
      title: '总SQL执行耗时(ms)',
      key: 'total_execute_time',
      dataIndex: 'total_execute_time',
      width: 300,
      scopedSlots: { customRender: 'total_execute_time' }
    },
    {
      title: '总分区扫描行数',
      key: 'total_partition_cnt',
      dataIndex: 'total_partition_cnt',
      width: 300,
      scopedSlots: { customRender: 'total_partition_cnt' }
    },
    {
      title: 'SQL影响行数',
      key: 'total_affected_rows',
      dataIndex: 'total_affected_rows',
      width: 300,
      scopedSlots: { customRender: 'total_affected_rows' }
    },
    {
      title: 'SQL返回行数',
      key: 'total_return_rows',
      dataIndex: 'total_return_rows',
      width: 300,
      scopedSlots: { customRender: 'total_return_rows' }
    },
    {
      title: '并发等待耗时(ms)',
      key: 'total_concurrency_wait_time',
      dataIndex: 'total_concurrency_wait_time',
      width: 300,
      scopedSlots: { customRender: 'total_concurrency_wait_time' }
    },
    {
      title: 'CPU等待耗时(ms)',
      key: 'total_cpu_time',
      dataIndex: 'total_cpu_time',
      width: 300,
      scopedSlots: { customRender: 'total_cpu_time' }
    },
    {
      title: '平均响应耗时(ms)',
      key: 'avg_elapsed_time',
      dataIndex: 'avg_elapsed_time',
      width: 300,
      scopedSlots: { customRender: 'avg_elapsed_time' }
    },
    {
      title: '平均网络请求耗时(ms)',
      key: 'avg_net_time',
      dataIndex: 'avg_net_time',
      width: 300,
      scopedSlots: { customRender: 'avg_net_time' }
    },
    {
      title: '平均DECODE耗时(ms)',
      key: 'avg_decode_time',
      dataIndex: 'avg_decode_time',
      width: 300,
      scopedSlots: { customRender: 'avg_decode_time' }
    },
    {
      title: '平均执行计划生产耗时(ms)',
      key: 'avg_get_plan_time',
      dataIndex: 'avg_get_plan_time',
      width: 300,
      scopedSlots: { customRender: 'avg_get_plan_time' }
    },
    {
      title: '平均分区扫描数量',
      key: 'avg_partition_cnt',
      dataIndex: 'avg_partition_cnt',
      width: 300,
      scopedSlots: { customRender: 'avg_partition_cnt' }
    },
    {
      title: '平均影响行数',
      key: 'avg_affected_rows',
      dataIndex: 'avg_affected_rows',
      width: 300,
      scopedSlots: { customRender: 'avg_affected_rows' }
    },
    {
      title: '平均等待耗时',
      key: 'avg_wait_time',
      dataIndex: 'avg_wait_time',
      width: 300,
      scopedSlots: { customRender: 'avg_wait_time' }
    },
    {
      title: '平均并发耗时(ms)',
      key: 'avg_concurrency_wait_time',
      dataIndex: 'avg_concurrency_wait_time',
      width: 300,
      scopedSlots: { customRender: 'avg_concurrency_wait_time' }
    },
    {
      title: '平均CPU执行耗时(ms)',
      key: 'avg_cpu_time',
      dataIndex: 'avg_cpu_time',
      width: 300,
      scopedSlots: { customRender: 'avg_cpu_time' }
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 170,
      fixed: 'right',
      visible: $permissionBatch.some([
        { module: 'topSql', values: ['explain'] }
      ])
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  })

  const obSearchFields = (dbType) => {
    return [
      {
        type: 'RangePicker',
        key: 'execution_time',
        compIcon: 'calendar',
        props: {
          showTime: {
            format: 'HH:mm'
          },
          // ranges: {
          //   '1分钟': [moment().subtract(1, 'minutes'), moment()],
          //   '5分钟': [moment().subtract(5, 'minutes'), moment()],
          //   '15分钟': [moment().subtract(15, 'minutes'), moment()],
          //   '30分钟': [moment().subtract(30, 'minutes'), moment()]
          // },
          ranges: {
            '昨天': [moment().subtract(48, 'hour'), moment().subtract(24, 'hour')],
            '近7天': [moment().subtract(7, 'day'), moment()],
            '近10天': [moment().subtract(10, 'day'), moment()],
            '近15天': [moment().subtract(15, 'day'), moment()],
            '近30天': [moment().subtract(30, 'day'), moment()]
          },
          format: 'YYYY-MM-DD HH:mm:ss',
          disabledDate: (current) => {
            return (
              current <= moment().subtract(120, 'days') ||
              current > moment().endOf('day')
            );
          }
        },
        listeners: {
          change: (dates) => {
            const first = dates[0]
            const second = dates[1]
            let days = first.diff(second, 'days');
            if (days > 30 || days < -30) {
              ctx.$message.warning('所选值范围不能超过30天');
              ctx.$refs.search.saving({ execution_time: [] })
            }
          }
        }
      },
      {
        type: 'Select',
        key: 'ob_server',
        compIcon: 'lu-icon-oceanbase',
        props: {
          placeholder: 'OBServer',
          url: '/sqlreview/after_audit/select_data',
          reqParams: {
            data_source_id: ctx.id,
            select_type: 'server'
          },
          allowSearch: true,
          backSearch: true,
          limit: 50
        }
      },
      {
        type: 'Select',
        key: 'executive_user',
        compIcon: 'user',
        props: {
          placeholder: '执行用户',
          url: '/sqlreview/after_audit/select_data',
          reqParams: {
            data_source_id: ctx.id,
            select_type: 'user'
          },
          allowSearch: true,
          backSearch: true,
          limit: 50
        },
        visible: dbType !== 'OB_ORACLE'
      },
      {
        type: 'Input',
        key: 'executive_user',
        compIcon: 'user',
        props: {
          placeholder: '执行用户'
        },
        visible: dbType == 'OB_ORACLE'
      },
      {
        type: 'Input',
        compIcon: 'lu-icon-ID',
        key: 'sql_id',
        props: {
          placeholder: 'SQL_ID'
        }
      },
      {
        type: 'Input',
        compIcon: 'lu-icon-field',
        key: 'sql_text',
        props: {
          placeholder: 'SQL文本'
        }
      },
      {
        type: 'Select',
        compIcon: 'lu-icon-alarm',
        key: 'risk',
        props: {
          placeholder: 'AI风险等级',
          options: [
            { label: '待审核', value: 0 },
            { label: '高风险', value: -1 },
            { label: '无风险', value: 1 },
            { label: '低风险', value: 2 },
            { label: '异常', value: 9 },
            { label: '审核中', value: 3 }
          ]
        }
      },
      // {
      //   type: 'Select',
      //   compIcon: 'lu-icon-config1',
      //   key: 'zone',
      //   props: {
      //     placeholder: 'Zone',
      //     url: '/sqlreview/after_audit/select_data',
      //     reqParams: {
      //       data_source_id: ctx.id,
      //       select_type: 'zone'
      //     },
      //     allowSearch: true,
      //     backSearch: true,
      //     limit: 50
      //   }
      // },
      {
        type: 'Select',
        key: 'tenant_name',
        compIcon: 'lu-icon-usernew',
        props: {
          placeholder: '租户名',
          url: '/sqlreview/after_audit/select_data',
          reqParams: {
            data_source_id: ctx.id,
            select_type: 'tenant'
          },
          allowSearch: true,
          backSearch: true,
          limit: 50
        }
      }
    ];
  };

  const db2Columns = [
    {
      title: '标签',
      dataIndex: 'label_attribute',
      key: 'label_attribute',
      scopedSlots: { customRender: 'label_attribute' },
      width: 180
    },
    {
      title: 'AI风险等级',
      dataIndex: 'risk',
      key: 'risk',
      scopedSlots: { customRender: 'risk' },
      width: 180
    },
    {
      title: 'STMTID',
      dataIndex: 'STMTID',
      key: 'STMTID',
      scopedSlots: { customRender: 'STMTID' },
      width: 120
    },
    {
      title: 'PLANID',
      dataIndex: 'PLANID',
      key: 'PLANID',
      scopedSlots: { customRender: 'PLANID' },
      width: 120
    },
    {
      title: 'SQL文本',
      key: 'sql_text',
      dataIndex: 'sql_text',
      scopedSlots: { customRender: 'sql_text' },
      width: 120
    },
    {
      title: '时间范围',
      key: 'execution_time',
      dataIndex: 'execution_time',
      scopedSlots: { customRender: 'execution_time' },
      width: 120
    },
    {
      title: 'NUM_EXECUTIONS',
      dataIndex: 'NUM_EXECUTIONS',
      key: 'NUM_EXECUTIONS',
      scopedSlots: { customRender: 'NUM_EXECUTIONS' },
      width: 120
    },
    {
      title: 'TOTAL_CPU_TIME',
      key: 'TOTAL_CPU_TIME',
      dataIndex: 'TOTAL_CPU_TIME',
      width: 300,
      scopedSlots: { customRender: 'TOTAL_CPU_TIME' }
    },
    {
      title: 'TOTAL_SORTS',
      key: 'TOTAL_SORTS',
      dataIndex: 'TOTAL_SORTS',
      width: 300,
      scopedSlots: { customRender: 'TOTAL_SORTS' }
    },
    {
      title: 'ROWS_READ',
      key: 'ROWS_READ',
      dataIndex: 'ROWS_READ',
      width: 300,
      scopedSlots: { customRender: 'ROWS_READ' }
    },
    {
      title: 'DIRECT_READS',
      key: 'DIRECT_READS',
      dataIndex: 'DIRECT_READS',
      width: 300,
      scopedSlots: { customRender: 'DIRECT_READS' }
    },
    {
      title: 'NUM_EXEC_WITH_METRICS',
      key: 'NUM_EXEC_WITH_METRICS',
      dataIndex: 'NUM_EXEC_WITH_METRICS',
      width: 300,
      scopedSlots: { customRender: 'NUM_EXEC_WITH_METRICS' }
    },
    {
      title: 'TOTAL_ACT_TIME',
      key: 'TOTAL_ACT_TIME',
      dataIndex: 'TOTAL_ACT_TIME',
      width: 300,
      scopedSlots: { customRender: 'TOTAL_ACT_TIME' }
    },
    {
      title: 'TOTAL_ACT_WAIT_TIME',
      key: 'TOTAL_ACT_WAIT_TIME',
      dataIndex: 'TOTAL_ACT_WAIT_TIME',
      width: 300,
      scopedSlots: { customRender: 'TOTAL_ACT_WAIT_TIME' }
    },
    {
      title: 'POOL_READ_TIME',
      key: 'POOL_READ_TIME',
      dataIndex: 'POOL_READ_TIME',
      width: 300,
      scopedSlots: { customRender: 'POOL_READ_TIME' }
    },
    {
      title: 'POOL_WRITE_TIME',
      key: 'POOL_WRITE_TIME',
      dataIndex: 'POOL_WRITE_TIME',
      width: 300,
      scopedSlots: { customRender: 'POOL_WRITE_TIME' }
    },
    {
      title: 'DIRECT_READ_TIME',
      key: 'DIRECT_READ_TIME',
      dataIndex: 'DIRECT_READ_TIME',
      width: 300,
      scopedSlots: { customRender: 'DIRECT_READ_TIME' }
    },
    {
      title: 'LOCK_WAIT_TIME',
      key: 'LOCK_WAIT_TIME',
      dataIndex: 'LOCK_WAIT_TIME',
      width: 300,
      scopedSlots: { customRender: 'LOCK_WAIT_TIME' }
    },
    {
      title: 'LOCK_ESCALS',
      key: 'LOCK_ESCALS',
      dataIndex: 'LOCK_ESCALS',
      width: 300,
      scopedSlots: { customRender: 'LOCK_ESCALS' }
    },
    {
      title: 'LOCK_WAITS',
      key: 'LOCK_WAITS',
      dataIndex: 'LOCK_WAITS',
      width: 300,
      scopedSlots: { customRender: 'LOCK_WAITS' }
    },
    {
      title: 'ROWS_MODIFIED',
      key: 'ROWS_MODIFIED',
      dataIndex: 'ROWS_MODIFIED',
      width: 300,
      scopedSlots: { customRender: 'ROWS_MODIFIED' }
    },
    {
      title: 'ROWS_RETURNED',
      key: 'ROWS_RETURNED',
      dataIndex: 'ROWS_RETURNED',
      width: 300,
      scopedSlots: { customRender: 'ROWS_RETURNED' }
    },
    {
      title: 'DIRECT_READ_REQS',
      key: 'DIRECT_READ_REQS',
      dataIndex: 'DIRECT_READ_REQS',
      width: 300,
      scopedSlots: { customRender: 'DIRECT_READ_REQS' }
    },
    {
      title: 'DIRECT_WRITES',
      key: 'DIRECT_WRITES',
      dataIndex: 'DIRECT_WRITES',
      width: 300,
      scopedSlots: { customRender: 'DIRECT_WRITES' }
    },
    {
      title: 'DIRECT_WRITE_REQS',
      key: 'DIRECT_WRITE_REQS',
      dataIndex: 'DIRECT_WRITE_REQS',
      width: 300,
      scopedSlots: { customRender: 'DIRECT_WRITE_REQS' }
    },
    {
      title: 'SORT_OVERFLOWS',
      key: 'SORT_OVERFLOWS',
      dataIndex: 'SORT_OVERFLOWS',
      width: 300,
      scopedSlots: { customRender: 'SORT_OVERFLOWS' }
    },
    {
      title: 'WLM_QUEUE_TIME_TOTAL',
      key: 'WLM_QUEUE_TIME_TOTAL',
      dataIndex: 'WLM_QUEUE_TIME_TOTAL',
      width: 300,
      scopedSlots: { customRender: 'WLM_QUEUE_TIME_TOTAL' }
    },
    {
      title: 'WLM_QUEUE_ASSIGNMENTS_TOTAL',
      key: 'WLM_QUEUE_ASSIGNMENTS_TOTAL',
      dataIndex: 'WLM_QUEUE_ASSIGNMENTS_TOTAL',
      width: 300,
      scopedSlots: { customRender: 'WLM_QUEUE_ASSIGNMENTS_TOTAL' }
    },
    {
      title: 'DEADLOCKS',
      key: 'DEADLOCKS',
      dataIndex: 'DEADLOCKS',
      width: 300,
      scopedSlots: { customRender: 'DEADLOCKS' }
    },
    {
      title: 'LOCK_TIMEOUTS',
      key: 'LOCK_TIMEOUTS',
      dataIndex: 'LOCK_TIMEOUTS',
      width: 300,
      scopedSlots: { customRender: 'LOCK_TIMEOUTS' }
    },
    {
      title: 'LOG_BUFFER_WAIT_TIME',
      key: 'LOG_BUFFER_WAIT_TIME',
      dataIndex: 'LOG_BUFFER_WAIT_TIME',
      width: 300,
      scopedSlots: { customRender: 'LOG_BUFFER_WAIT_TIME' }
    },
    {
      title: 'NUM_LOG_BUFFER_FULL',
      key: 'NUM_LOG_BUFFER_FULL',
      dataIndex: 'NUM_LOG_BUFFER_FULL',
      width: 300,
      scopedSlots: { customRender: 'NUM_LOG_BUFFER_FULL' }
    },
    {
      title: 'LOG_DISK_WAIT_TIME',
      key: 'LOG_DISK_WAIT_TIME',
      dataIndex: 'LOG_DISK_WAIT_TIME',
      width: 300,
      scopedSlots: { customRender: 'LOG_DISK_WAIT_TIME' }
    },
    {
      title: 'LOG_DISK_WAITS_TOTAL',
      key: 'LOG_DISK_WAITS_TOTAL',
      dataIndex: 'LOG_DISK_WAITS_TOTAL',
      width: 300,
      scopedSlots: { customRender: 'LOG_DISK_WAITS_TOTAL' }
    },
    {
      title: 'SQL_ID',
      key: 'sql_id',
      dataIndex: 'sql_id',
      width: 300,
      scopedSlots: { customRender: 'sql_id' }
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 170,
      fixed: 'right',
      visible: $permissionBatch.some([
        { module: 'topSql', values: ['explain'] }
      ])
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  })

  const gaussdbColumns = [
    {
      title: '标签',
      dataIndex: 'label_attribute',
      key: 'label_attribute',
      scopedSlots: { customRender: 'label_attribute' },
      width: 180
    },
    {
      title: '执行时间',
      dataIndex: 'execution_time',
      key: 'execution_time'
    },
    {
      title: 'SQL_ID',
      dataIndex: 'sql_id',
      key: 'sql_id'
    },
    {
      title: '执行用户',
      dataIndex: 'executive_user',
      key: 'executive_user'
    },
    {
      title: 'SQL文本',
      key: 'sql_text',
      dataIndex: 'sql_text'
    },
    {
      title: '执行计划',
      key: 'query_plan',
      dataIndex: 'query_plan'
    },
    {
      title: '执行的库',
      key: 'executive_db',
      dataIndex: 'executive_db'
    },
    {
      title: 'Schema',
      key: 'executive_schema',
      dataIndex: 'executive_schema'
    },
    {
      title: '有效的DB时间花费',
      dataIndex: 'db_time',
      key: 'db_time'
    },
    {
      title: 'CPU时间',
      key: 'cpu_time',
      dataIndex: 'cpu_time'
    },
    {
      title: 'SQL生成计划时间',
      key: 'plan_time',
      dataIndex: 'plan_time'
    },
    {
      title: '执行器内执行时间',
      key: 'actuator_time',
      dataIndex: 'actuator_time'
    },
    {
      title: 'SQL重写时间',
      key: 'rewrite_time',
      dataIndex: 'rewrite_time'
    },
    {
      title: 'plpgsql上的执行时间',
      key: 'pl_execution_time',
      dataIndex: 'pl_execution_time'
    },
    {
      title: 'plpgsql上的编译时间',
      key: 'pl_compilation_time',
      dataIndex: 'pl_compilation_time'
    },
    {
      title: 'IO上的时间花费',
      key: 'data_io_time',
      dataIndex: 'data_io_time'
    },
    {
      title: '加锁次数',
      key: 'lock_count',
      dataIndex: 'lock_count'
    },
    {
      title: '最大持锁数量',
      key: 'lock_max_count',
      dataIndex: 'lock_max_count'
    }
  ]

  return {
    columns,
    db2Columns,
    searchFields,
    obColumns,
    obSearchFields,
    gaussdbColumns
  };
}
