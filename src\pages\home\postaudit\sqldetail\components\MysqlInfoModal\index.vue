<template>
  <a-modal
    v-model="visible"
    width="65%"
    title="表结构信息"
    :maskClosable="false"
    :mask="false"
    :footer="null"
    wrapClassName="mysql-review-total-info-modal"
    :maskStyle="{ 'pointerEvents': 'none' }"
    :bodyStyle="{ 'maxHeight': '600px', 'overflow': 'auto' }"
    :dialogStyle="{ 'minWidth': '1200px', 'maxWidth': '1200px' }"
    @cancel="onCancel"
  >
    <a-spin :spinning="loading">
      <a-tabs default-active-key="table">
        <a-tab-pane key="table" tab="表信息">
          <Table v-bind="tableParams" :dataSource="tableData" bordered>
            <template slot="is_onlie" slot-scope="{ record }">
              <span>{{record.is_onlie}}</span>
              <a-popover v-if="record.is_onlie === '未知'">
                <template slot="content">
                  <span>没有可用的生产数据库链接，无法获取到表上线状态。</span>
                </template>
                <a-icon type="question-circle" />
              </a-popover>
              <!-- :color="statusColor[record.dba_status]" -->
              <a-tooltip placement="bottom">
                <template slot="title">生效时间:{{record.take_effect}} 失效时间:{{record.expire}}</template>
                <a-tag v-if="record.is_white === 1" color="rgba(35, 190, 108, 1)">已加入白名单</a-tag>
              </a-tooltip>
            </template>
            <span slot="action" slot-scope="{ record }">
              <a @click="showAddModal(record)">申请表白名单</a>
            </span>
          </Table>
        </a-tab-pane>
        <a-tab-pane key="index" tab="索引信息">
          <div class="table-name-wrapper" v-for="item in tableData" :key="item.table_name">
            <div class="table-name">
              <span>表名：{{item.table_name}}</span>
            </div>
            <Table
              v-bind="indexParams"
              :columns="getColumns(item.table_name)"
              :dataSource="indexData.filter(itm => itm.table_name === item.table_name)"
              bordered
            ></Table>
          </div>
          <div v-if="tableData.length <= 0">
            <custom-empty />
          </div>
        </a-tab-pane>
        <a-tab-pane key="field" tab="字段信息">
          <div v-if="table_name" class="table-name-wrapper">
            <div class="table-name">
              <span>{{ table_name }}</span>
            </div>
          </div>
          <Table v-bind="fieldParams" :dataSource="fieldData" bordered>
            <span slot="titleNdv">
              {{ 'NDV值' }}
              <a-popover>
                <template slot="content">
                  <p>对表字段唯一值个数的统计</p>
                </template>
                <a-icon type="question-circle" />
              </a-popover>
            </span>
          </Table>
          <!-- <custom-empty v-if="fieldData.length <= 0" /> -->
        </a-tab-pane>
      </a-tabs>
    </a-spin>
    <AddModal ref="addModal" @save="save"></AddModal>
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
import AddModal from '@/pages/whiteList/components/AddModal';

import { getTableInfo } from '@/api/review';
import { addWhiteListTable } from '@/api/config/whiteList';
import config from './config';

export default {
  components: { Table, AddModal },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      loading: false,
      tableParams: {
        columns: this.config.tableColumns,
        // pagination: false,
        rowKey: 'id',
        scroll: {x: 'max-content'}
      },
      tableData: [],
      indexParams: {
        columns: this.config.indexColumns(),
        // pagination: false,
        rowKey: 'id'
      },
      fieldParams: {
        columns: this.config.fieldColumns,
        // pagination: false,
        rowKey: 'id',
        scroll: {x: 'max-content'}
      },
      scroll: { x: 1550 },
      indexData: [],
      fieldData: [],
      table_name: null
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      this.visible = true;
      // 发起请求
      this.loading = true;
      ReqUtil.req({
        ctx: this,
        reqInstance: getTableInfo,
        params: {
          detail_id: data.id
        },
        needLoading: false,
        cbk: data => {
          data = data || {};
          this.tableData = (data.table_list || []).map((item, index) => ({
            ...item,
            id: index
          }));
          this.table_name = _.get(this.tableData, '0.table_name');
          this.indexData = (data.index_list || []).map((item, index) => ({
            ...item,
            id: index
          }));
          this.fieldData = (data.column_info || []).map((item, index) => ({
            ...item,
            id: index
          }));
          // this.index_name = this.indexData[0].table_name || '';
          // 处理索引合并
          // this.combineIndexTable(this.indexData);
        },
        err: res => {
          this.tableData = [];
          this.indexData = [];
        }
      })
        .then(() => {
          this.$hideLoading({ duration: 0 });
          this.loading = false;
        })
        .catch(e => {
          this.loading = false;
        });
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    getColumns(tableName) {
      const data = this.indexData.filter(itm => itm.table_name === tableName);
      return this.config.indexColumns({
        combineInfo: this.combineIndexTable(data)
      });
    },
    combineIndexTable(data = []) {
      const combineColumns = ['table_name', 'index_name'];
      let map = { table_name: {}, index_name: {} };
      data.forEach((item, index) => {
        combineColumns.forEach(key => {
          let colVal = item[key];
          let colMap = map[key];
          if (colVal) {
            let uid = colVal + '_' + index;
            colMap[uid] = {
              index,
              rowSpan: 1
            };
            // 和前面值相同
            if (index > 0 && data[index - 1][key] == colVal) {
              colMap[uid].rowSpan = 0;
              let pid = colVal + '_' + (index - 1);
              if (!colMap[pid].parent) {
                colMap[pid].rowSpan += 1;
                colMap[uid].parent = colMap[pid];
              } else {
                colMap[pid].parent.rowSpan += 1;
                colMap[uid].parent = colMap[pid].parent;
              }
            }
          }
        });
      });
      return map;
    },
    showAddModal(data) {
      this.$refs.addModal.show(data);
    },
    save(payload) {
      // const { table } = this.$refs;
      addWhiteListTable(payload)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    }
  }
};
</script>

<style lang="less">
.mysql-review-total-info-modal {
  pointer-events: none;
  .ant-modal-header {
    cursor: move;
  }
  .ant-modal-content {
    position: absolute;
    width: 100%;
    .table-name-wrapper {
      padding: 8px 0;
      &:first-child {
        padding-top: 0;
      }
      .table-name {
        color: #1890ff;
        font-size: 14px;
        font-weight: 700;
        margin: 8px 0 16px 0;
        span {
          padding: 4px 8px;
          border: 1px solid #1890ff;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
