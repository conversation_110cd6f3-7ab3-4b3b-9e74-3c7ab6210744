<template>
  <!-- 新建项目抽屉 -->
  <a-drawer
    :title="title"
    :visible="visible"
    @close="hide"
    width="740px"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="config-datasource-edit-drawer"
  >
    <!-- 内容区域 -->
    <a-spin :spinning="spinning">
      <DataSourceBlocks
        @choose="choose"
        :dbType="dbType"
        :itemWidth="100"
        :disabled="!!id"
      />
      <div>
        <Form class="form" v-bind="formParams" :formData="data" ref="form">
          <div
            :class="`file-upload-area ${sslCaName && 'ssl-file'}`"
            slot="ssl_ca"
          >
            <a-upload
              name="file"
              :before-upload="sslCaBeforeUpload"
              :fileList="sslCaFileList"
              :remove="() => (this.sslCaFileList = [])"
            >
              <a-button class="file-import-btn">点击上传 </a-button>
            </a-upload>
            <a v-if="sslCaName"> {{ sslCaName }} </a>
          </div>
          <div
            :class="`file-upload-area ${sslCertName && 'ssl-file'}`"
            slot="ssl_cert"
          >
            <a-upload
              name="file"
              :before-upload="sslCertBeforeUpload"
              :fileList="sslCertFileList"
              :remove="() => (this.sslCertFileList = [])"
            >
              <a-button class="file-import-btn">点击上传 </a-button>
            </a-upload>
            <a v-if="sslCertName"> {{ sslCertName }} </a>
          </div>
          <div
            :class="`file-upload-area ${sslKeyName && 'ssl-file'}`"
            slot="ssl_key"
          >
            <a-upload
              name="file"
              :before-upload="sslKeyBeforeUpload"
              :fileList="sslKeyFileList"
              :remove="() => (this.sslKeyFileList = [])"
            >
              <a-button class="file-import-btn">点击上传 </a-button>
            </a-upload>
            <a v-if="sslKeyName"> {{ sslKeyName }} </a>
          </div>
        </Form>
      </div>
      <!-- 数据源选择OB时 新增选项 -->
      <div v-if="dbType == 'OCEANBASE'" class="ob-extra-form">
        <div class="des">
          <custom-icon type="info-circle" theme="filled" />
          <span
            >OceanBase数据库审核需要连接到sys系统租户进行数据库执行SQL采集，如需使用数据库审核功能，请配置sys系统租户账号密码</span
          >
        </div>
        <Form
          class="ob-form"
          ref="obForm"
          v-bind="obParams"
          :formData="obData"
        ></Form>
      </div>
      <!-- 测试连接显示内容 -->
      <div
        v-if="alertMessage !== null"
        style="margintop: 16px; display: flex; justifycontent: center"
      >
        <a-tag color="red">{{ alertMessage }}</a-tag>
      </div>
    </a-spin>
    <!-- 按钮区域 -->
    <div
      class="btns-area"
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 10
      }"
    >
      <a-button @click="pullPasswordFn" v-if="isTaiLong">拉取密码</a-button>
      <!-- <a-button @click="checkOpenApi" v-if="openApiType == 0">阿里云校验</a-button> -->
      <a-button @click="check" :disabled="checking">测试连接</a-button>
      <a-divider type="vertical" />
      <a-button @click="hide" class="hide-btn">取消</a-button>
      <a-popconfirm
        v-if="connectStatus == 0"
        title="你未进行数据源连接测试，是否直接保存?"
        @confirm="() => save()"
      >
        <a-button type="primary" :disabled="openApiType == 0 && !flag"
          >保存</a-button
        >
      </a-popconfirm>
      <a-button
        v-else
        @click="save"
        type="primary"
        :disabled="openApiType == 0 && !flag"
        >保存</a-button
      >
    </div>
  </a-drawer>
</template>

<script>
import DataSourceBlocks from '@/components/Biz/DataSourceBlocks';
import TableEdit from '@/components/TableEdit';
import config from './config';
import Form from '@/components/Form';
import {
  testDataSource1,
  testDataSource2,
  testDataSource3,
  testDataSource4,
  pullPassword
} from '@/api/config/dataSource';

export default {
  components: { DataSourceBlocks, Form, TableEdit },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      spinning: false,
      checking: false,
      dbType: '',
      type: '',
      data: {},
      formParams: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 16 },
        fields: this.config.fields(this.type)
      },
      obData: {},
      obParams: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 16 },
        fields: this.config.obInfo(this.type)
      },
      alertMessage: null,
      connectStatus: 0,
      id: null,
      title: null,
      openApiType: null,
      flag: false, // 阿里云校验成功 才能保存
      extraStatus: 0,
      options: [
        { label: '不采集', value: 0 },
        { label: '采集', value: 1 }
      ],
      captureEnumOptions: [],
      sslCertFileList: [],
      sslCertFileSize: 0,
      sslCaFileList: [],
      sslCaFileSize: 0,
      sslKeyFileList: [],
      sslKeyFileSize: 0,
      sslCaName: null,
      sslCertName: null,
      sslKeyName: null
    };
  },
  computed: {
    // 泰隆银行特有
    isTaiLong() {
      const specificConfig = process.channel === 'TaiLongBank';
      return specificConfig;
    }
  },
  mounted() {},
  created() {},
  methods: {
    show(type, data = {}) {
      this.type = type;
      this.sslCaName = data.ssl_ca_name;
      this.sslCertName = data.ssl_cert_name;
      this.sslKeyName = data.ssl_key_name;
      this.title = type === 'add' ? '新增数据源' : '编辑数据源';
      this.visible = true;
      this.id = data.id;
      this.dbType = data.db_type;
      const examineUser = _.get(data, 'collect_config.examine_user');
      this.data = {
        type: data.url_type || 0, // 暂时只有0
        ...data,
        _t: +new Date()
      };
      this.obData = {
        ...data.collect_config
      };
      // cybrebark的开关 1：开，0 ：关。
      // 开的话数据源配置 新增数据源默认选择密钥 且密码不作必填 切换密码后做必填校验
      let cybrearkSwitch = _.get(this.$store.state, 'project.cybrearkSwitch');
      if (cybrearkSwitch == 1 && type === 'add') this.data.decode_type = 1;

      if (this.data.db_type == 'OB_MYSQL') {
        this.dbType = 'OCEANBASE';
        this.data.db_type = 'OCEANBASE';
        this.data.ob_mode = 'OB_MYSQL';
      }
      if (this.data.db_type == 'OB_ORACLE') {
        this.dbType = 'OCEANBASE';
        this.data.db_type = 'OCEANBASE';
        this.data.ob_mode = 'OB_ORACLE';
      }
      if (this.data.db_type == 'TD_MYSQL') {
        this.dbType = 'TDSQL';
        this.data.db_type = 'TDSQL';
        this.data.ob_mode = 'TD_MYSQL';
      }
      if (this.data.db_type == 'TD_PGSQL') {
        this.dbType = 'TDSQL';
        this.data.db_type = 'TDSQL';
        this.data.ob_mode = 'TD_PGSQL';
      }
      this.$set(
        this.obParams,
        'fields',
        this.config.obInfo(this.type, examineUser)
      );
      this.formParams.fields = this.config.fields(this.type, cybrearkSwitch);
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
      this.checking = false;
      this.data = {};
      this.alertMessage = null;
      this.sslCertFileSize = 0;
      this.sslKeyFileSize = 0;
      this.sslCaFileSize = 0;
      this.sslCertFileList = [];
      this.sslKeyFileList = [];
      this.sslCaFileList = [];
      this.sslCaName = null;
      this.sslCertName = null;
      this.sslKeyName = null;
    },
    getData() {
      const { form, obForm } = this.$refs;
      const resData = form.getData();
      const obData = obForm && obForm.getData();
      this.data = { ...this.data, ...resData, ...obData };
      return resData;
    },
    // 数据库选择
    choose(value) {
      this.dbType = value;
      this.$refs.form.saving({
        db_type: value,
        ob_mode: null,
        collect_type: null,
        catalog_name: null,
        ssl: null,
        ssl_mode: null
      });
      let dbUrL = this.formParams.fields.find(item => item.key === 'db_url');
      if (value === 'ORACLE') {
        dbUrL.props.placeholder = '0.0.0.0:1521/service name';
      } else if (value === 'MYSQL') {
        dbUrL.props.placeholder = '0.0.0.0:3306';
      } else if (value === 'TIDB') {
        dbUrL.props.placeholder = '0.0.0.0:4000';
      } else if (value === 'POSTGRE') {
        dbUrL.props.placeholder = '0.0.0.0:5432/database';
      } else if (value === 'TDSQL') {
        dbUrL.props.placeholder = '0.0.0.0:15001/database';
      } else if (value === 'OCEANBASE') {
        dbUrL.props.placeholder = '0.0.0.0:2883';
      } else if (value === 'DB2') {
        dbUrL.props.placeholder = '0.0.0.0:50000/service name';
      } else if (value === 'SQLSERVER') {
        dbUrL.props.placeholder = '0.0.0.0:1433';
      } else if (value === 'KINGBASE') {
        dbUrL.props.placeholder = '0.0.0.0:54321/database';
      } else if (value === 'HIVE') {
        dbUrL.props.placeholder = '0.0.0.0:10000';
      } else if (value === 'IMPALA') {
        dbUrL.props.placeholder = '0.0.0.0:21050';
      } else if (value === 'PRESTO') {
        dbUrL.props.placeholder = '0.0.0.0:8080';
      } else if (value === 'GOLDENDB') {
        dbUrL.props.placeholder = '0.0.0.0:3306';
      } else if (value === 'MOGDB') {
        dbUrL.props.placeholder = '0.0.0.0:152/database';
      } else if (value === 'GAUSSDB') {
        dbUrL.props.placeholder = '0.0.0.0:5432/database';
      } else if (value === 'OPENGAUSS') {
        dbUrL.props.placeholder = '0.0.0.0:5432/database';
      } else if (value === 'DM') {
        dbUrL.props.placeholder = '0.0.0.0:5432/database';
      } else {
        dbUrL.props.placeholder = '请输入';
      }
      this.formParams.fields = [...this.formParams.fields];
    },
    // 链接测试
    check() {
      if (!this.dbType) {
        this.$message.warning('请选择数据库');
        return;
      }
      const { form } = this.$refs;
      Promise.all([form.validate()]).then(valid => {
        if (valid) {
          this.testDataSourceFn1();
          this.alertMessage = null;
          this.spinning = true;
          this.checking = true;
        }
      });
    },
    paramsFn() {
      this.getData();
      let res = {};
      let submitData = new FormData();
      if (this.dbType == 'MYSQL') {
        this.data.ssl_ca &&
          this.data.ssl_ca.forEach(item => {
            submitData.append('ssl_ca', item);
          });
        this.data.ssl_cert &&
          this.data.ssl_cert.forEach(item => {
            submitData.append('ssl_cert', item);
          });
        this.data.ssl_key &&
          this.data.ssl_key.forEach(item => {
            submitData.append('ssl_key', item);
          });
        this.data.ssl_mode && submitData.append('ssl_mode', this.data.ssl_mode);
        this.data.id &&
          submitData.append('data_source_id', String(this.data.id));
        submitData.append(
          'ssl_key_password',
          Base64.encode(this.data.ssl_key_password || '')
        );
        submitData.append('env', this.data.env);
        submitData.append('db_url', this.data.db_url);
        submitData.append('user', this.data.user);
        submitData.append('password', Base64.encode(this.data.password || ''));
        submitData.append('name', this.data.name);
        submitData.append('db_type', this.data.db_type);
        submitData.append('decode_type', String(this.data.decode_type));
        submitData.append('ssl', String(this.data.ssl || 0));
        res = submitData;
      } else {
        res = {
          data_source_id: this.data.id,
          env: this.data.env,
          db_url: this.data.db_url,
          user: this.data.user,
          password: Base64.encode(this.data.password || ''),
          examine_pwd: Base64.encode(this.data.examine_pwd || ''),
          examine_pwd_type: this.data.examine_pwd_type,
          examine_user: this.data.examine_user,
          name: this.data.name,
          db_type: this.data.ob_mode ? this.data.ob_mode : this.data.db_type,
          decode_type: this.data.decode_type,
          cluster: this.data.cluster,
          proxy_status: this.data.proxy_status,
          tenant: this.data.tenant
        };
      }
      return res;
    },
    testDataSourceFn1() {
      let params = {
        step: '1',
        ...this.paramsFn()
      };
      let res = new FormData();
      if (this.dbType == 'MYSQL') {
        res = this.paramsFn();
        res.append('step', '1');
        params = res;
      }
      testDataSource1(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.Timer = setTimeout(() => {
              this.connectStatus = res.data.data.connect_status;
              this.testDataSourceFn2();
            }, 1000);
          } else {
            this.alertMessage = '! 服务器连通性检查失败，请检查连接信息';
            this.connectStatus = res.data.data.connect_status;
            this.spinning = false;
            this.checking = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.checking = false;
          this.connectStatus = 2;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    testDataSourceFn2() {
      let params = {
        step: '2',
        ...this.paramsFn()
      };
      let res = new FormData();
      if (this.dbType == 'MYSQL') {
        res = this.paramsFn();
        res.append('step', '2');
        params = res;
      }
      testDataSource2(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.Timer1 = setTimeout(() => {
              this.connectStatus = res.data.data.connect_status;
              this.testDataSourceFn3();
            }, 1000);
          } else {
            this.alertMessage = '! 用户名密码检查失败，请检查账号信息';
            this.connectStatus = res.data.data.connect_status;
            this.spinning = false;
            this.checking = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.checking = false;
          this.connectStatus = 2;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    testDataSourceFn3() {
      let params = {
        step: '3',
        ...this.paramsFn()
      };
      let res = new FormData();
      if (this.dbType == 'MYSQL') {
        res = this.paramsFn();
        res.append('step', '3');
        params = res;
      }
      testDataSource3(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            if (this.dbType == 'OCEANBASE') {
              this.Timer1 = setTimeout(() => {
                this.connectStatus = res.data.data.connect_status;
                this.testDataSourceFn4();
              }, 1000);
              return;
            }
            this.spinning = false;
            this.checking = false;
            this.connectStatus = res.data.data.connect_status;
            this.$message.success('连接测试成功');
          } else {
            this.connectStatus = res.data.data.connect_status;
            this.alertMessage = res.data.message;
            this.spinning = false;
            this.checking = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.checking = false;
          this.connectStatus = 2;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // OCEANBASE才调用
    testDataSourceFn4() {
      const params = {
        step: '4',
        ...this.paramsFn()
      };
      testDataSource4(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.spinning = false;
            this.checking = false;
            this.connectStatus = res.data.data.connect_status;
            this.$message.success('连接测试成功');
          } else {
            this.connectStatus = res.data.data.connect_status;
            this.alertMessage = res.data.message;
            this.spinning = false;
            this.checking = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.checking = false;
          this.connectStatus = 2;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 保存
    save() {
      if (!this.dbType) {
        this.$message.warning('请选择数据库');
        return;
      }
      const { form, obForm } = this.$refs;
      Promise.all([form.validate(), obForm && obForm.validate()]).then(
        valid => {
          if (valid) {
            const paramsData = form.getData();
            const obData = (obForm && obForm.getData()) || {};
            paramsData.type = this.data.url_type || 0;
            paramsData.db_type = this.dbType;
            paramsData.connect_status = this.connectStatus;
            if (this.data.ob_mode) paramsData.db_type = this.data.ob_mode;
            delete paramsData.download_status;
            this.$emit('save', {
              type: this.type,
              data: {
                id: this.id,
                ...paramsData,
                ...obData,
                examine_pwd: Base64.encode(obData.examine_pwd || ''),
                collect_config: {
                  collect_type: paramsData.collect_type,
                  extra_status: this.extraStatus
                }
              }
            });
          }
        }
      );
    },
    // 拉取密码
    pullPasswordFn() {
      const data = this.$refs.form.getData();
      data.db_type = this.dbType;
      if (data.db_type == undefined) {
        return this.$message.warn('拉取密码时数据库类型不能为空');
      }
      if (data.db_url == undefined) {
        return this.$message.warn('拉取密码时连接串不能为空');
      }
      if (data.agent_ip == undefined) {
        return this.$message.warn('拉取密码时angle ip不能为空');
      }
      if (data.user == undefined) {
        return this.$message.warn('拉取密码时账号不能为空');
      }

      this.$showLoading();
      const params = {
        db_url: data.db_url,
        account: data.user,
        agent_ip: data.agent_ip,
        db_type: data.db_type
      };
      pullPassword(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              useMessage: true,
              tips: '拉取密码成功'
            });
            const password = _.get(res, 'data.data');
            const obj = {
              password: password,
              decode_type: 0
            };
            this.data = Object.assign({}, this.data, obj);
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    sslCaBeforeUpload(file) {
      this.commonBeforeUpload(
        file,
        this.sslCaFileSize,
        this.sslCaFileList,
        'ssl_ca'
      );
      this.sslCaRemove();
      return false;
    },
    sslCertBeforeUpload(file) {
      this.commonBeforeUpload(
        file,
        this.sslCertFileSize,
        this.sslCertFileList,
        'ssl_cert'
      );
      this.sslCertRemove();
      return false;
    },
    sslKeyBeforeUpload(file) {
      this.commonBeforeUpload(
        file,
        this.sslKeyFileSize,
        this.sslKeyFileList,
        'ssl_key'
      );
      this.sslKeyRemove();
      return false;
    },
    // 上传之前
    commonBeforeUpload(file, fileSize, fileList, key) {
      const maxSize = 200 * 1024 * 1024; // byte
      fileSize = fileSize + file.size;

      // const test =
      //   key == 'ssl_key'
      //     ? /\.(key)$/.test(file.name)
      //     : /\.(pem)$/.test(file.name);

      if (fileSize > maxSize) {
        this.$message.error('文件大小错误，文件总大小不超过200MB');
        return;
      }

      // if (!test) {
      //   this.$message.error(
      //     key == 'ssl_key'
      //       ? '文件格式错误，文件类型支持.key'
      //       : '文件格式错误，文件类型支持.pem'
      //   );
      //   return;
      // }

      this.$set(fileList, 0, file);
      const { form } = this.$refs;
      form.saving({ [key]: fileList });
    },
    sslCaRemove() {
      this.sslCaName = null;
    },
    sslCertRemove() {
      this.sslCertName = null;
    },
    sslKeyRemove() {
      this.sslKeyName = null;
    }
  }
};
</script>

<style lang="less" scoped>
.config-datasource-edit-drawer {
  /deep/ .ant-drawer-content-wrapper {
    width: 720px;
    .ant-spin-container {
      .hidden-label > .ant-form-item-label {
        opacity: 0;
      }
      // .password {
      //   padding-bottom: 8px;
      // }
      .collect-type {
        padding-top: 16px;
        border-top: 1px solid #e8e8e8;
      }
      .form {
        padding: 16px 0;
        background: #fff;

        .ant-row {
          .ant-col-4 {
            padding-right: 16px;
            width: 160px;
            min-height: 36px;
          }
          .ant-form-item-control-wrapper {
            flex-grow: 1;
            .ant-form-item-control {
              line-height: 36px;
              .ant-form-item-children {
                .ant-input,
                .ant-input-number,
                .ant-select-selection {
                  height: 36px;
                  border: 1px solid rgba(217, 217, 217, 1);
                }
                .file-upload-area {
                  > span {
                    display: flex;
                    align-items: center;
                    .ant-upload {
                      .ant-upload {
                        .file-import-btn {
                          height: 36px;
                        }
                      }
                    }
                    .ant-upload-list {
                      display: inline-block;
                      margin-left: 12px;
                      .ant-upload-list-item {
                        height: 26px;
                        margin-top: 0;
                        .ant-upload-list-item-info {
                          width: 240px;
                          .ant-upload-list-item-name {
                            width: 180px;
                          }
                          .ant-upload-list-item-card-actions {
                            margin-left: 12px;
                          }
                        }
                      }
                    }
                  }
                  &.ssl-file {
                    display: flex;
                    align-items: center;
                    > span {
                      display: inline-block;
                    }
                    > a {
                      width: 180px;
                      display: flex;
                      align-items: center;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    }
                  }
                }
              }
            }
          }
          .ant-radio-group {
            display: flex;
            .ant-radio-wrapper {
              padding: 6px 10px;
              border: 1px solid rgba(228, 228, 231, 1);
              &.ant-radio-wrapper-checked {
                border: 1px solid rgba(77, 181, 242, 1);
              }
            }
          }
        }
        .extra-columns-box {
          background: #fff;
          .extra-columns-radio {
            display: flex;
            justify-content: space-between;
            padding: 8px 10px;
            span {
              font-size: 13px;
              color: #27272a;
              font-weight: 400;
            }
          }
          .extra-columns-desc {
            padding: 8px 8px;
            background: #003b72;
            display: flex;
            justify-content: center;
            // align-items: center;
            span {
              font-size: 12px;
              color: #ffffff;
              text-align: justify;
              font-weight: 400;
            }
            .anticon {
              color: #fff;
              margin-right: 4px;
              margin-top: 2px;
            }
          }
        }
      }
      .ob-extra-form {
        padding: 16px 32px;
        background: #eff5ff;
        .des {
          display: flex;
          margin-bottom: 16px;
          .anticon {
            color: #008adc;
            font-size: 16px;
            margin-right: 8px;
            position: relative;
            top: 3px;
          }
          > span {
            font-size: 14px;
            color: #000000;
          }
        }
        .ant-form {
          .ant-row {
            .ant-col-4 {
              padding-right: 16px;
              width: 160px;
              min-height: 36px;
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              .ant-form-item-children {
                .ant-input,
                .ant-input-number,
                .ant-select-selection {
                  height: 36px;
                  border: 1px solid rgba(217, 217, 217, 1);
                }
              }
            }
          }
        }
      }
    }
  }
}
.btns-area {
  display: flex;
  justify-content: flex-end;
  .ant-btn {
    margin-left: 10px;
    &.hide-btn {
      margin-left: 0px;
    }
  }
  .ant-divider {
    height: 32px;
    margin: 0 12px;
  }
}
.ant-steps {
  padding: 24px;
}
.ant-divider-horizontal {
  margin: 0 0 24px 0;
}
.ant-tag-red {
  font-size: 14px;
  padding: 8px;
}

/deep/.ant-row-flex-space-around {
  justify-content: flex-start;
  .backgroundBlock {
    margin-right: 12px;
  }
}
</style>
