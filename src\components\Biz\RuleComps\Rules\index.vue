<template>
  <div class="rule-tree">
    <Group :value="data" :dbType="dbType"></Group>
  </div>
</template>

<script>
import Group from './Group/index';
import Item from './Item/index';

export default {
  inheritAttrs: false,
  components: { Group, Item },
  props: {
    ruleData: {
      type: Object,
      default: () => {}
    },
    dbType: {
      type: String,
      default: 'ORACLE'
    }
  },
  data() {
    return {
      data: this.initData(this.ruleData)
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    initData(data = {}) {
      const loop = (item, level) => {
        // 处理当前节点
        item.level = level;
        item.id = _.uniqueId('init_');
        if (!item.children || item.children.length <= 0) {
          item.isLeaf = true;
        }
        if (item.children && item.children.length > 0) {
          item.children.forEach((child, index) => {
            // if (index === 0) {
            //   child.isFirst = true;
            // }
            // if (index === list.length - 1) {
            //   child.isLast = true;
            // }
            if (
              child.role_type == 'item' &&
              child.children &&
              child.children.length > 0
            ) {
              child.hasChildren = true;
            }
            child.item_order = index;
            child._parent = item;
            loop(child, ++level - index);
          });
        }
      };
      loop(data, 0);
      return data;
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.rule-tree {
  /deep/.rule-tree-group {
    position: relative;
    display: flex;
    cursor: default;
    .rule-tree-relation {
      display: flex;
      padding: 10px 24px 6px 4px;
      z-index: 1;
      &.only-icon {
        padding: 6px 6px 0 0;
      }
      // 图标
      .tree-relation-add-icon {
        margin-right: 4px;
        font-size: 16px;
        color: #71717a;
        line-height: 28px;
      }
      // 文案
      .tree-relation-label {
        height: 24px;
        &.ant-select {
          .ant-select-selection {
            border: 1px solid #4cbb3a;
            border-radius: 12px;
            width: 66px;
            height: 24px;
            box-shadow: none;
            .ant-select-selection__rendered {
              line-height: 0;
              .ant-select-selection-selected-value {
                text-align: center;
                font-size: 12px;
                color: #4cbb3a;
                height: 23px;
                line-height: 23px;
              }
            }
            .ant-select-arrow {
              .anticon {
                color: #4cbb3a;
              }
            }
          }
          &.relation-or {
            .ant-select-selection {
              border: 1px solid #f29339;
              .ant-select-selection__rendered {
                .ant-select-selection-selected-value {
                  color: #f29339;
                }
              }
              .ant-select-arrow {
                .anticon {
                  color: #f29339;
                }
              }
            }
          }
        }
      }
    }
    .rule-tree-relation-children {
      width: 100%;
    }
  }
  /deep/ .rule-tree-item {
    position: relative;
    // 子节点
    &.has-children {
      display: flex;
      flex-direction: column;
      padding: 4px 0 4px 4px;
      .rule-tree-item-children {
        padding: 0 0 0 24px;
        width: 100%;
      }
    }
  }
}
</style>
