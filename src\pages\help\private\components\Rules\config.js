export default function (ctx) {
  const condition = (params = {}) => {
    return [
      {
        type: 'InputModal',
        props: {
          style: 'width: 100%',
          suffixIcon: 'down'
        },
        rules: [{ required: true, message: '该项为必填项' }]
      },
      {
        type: 'Select',
        props: {
          url: '/sqlreview/common/item-list/',
          size: 'default'
        },
        cellProps: (row, record = {}) => {
          let reqParams = {
            enable: 1,
            parent_item_key: 'rule_conditions'
          };
          if (record.value_operator) {
            reqParams.filter = record.value_operator;
          }
          // console.log(row, record, record.value_operator, reqParams, 888)
          return {
            reqParams
          };
        },
        rules: [{ required: true, message: '该项为必填项' }]
      },
      (row, record = {}) => {
        const { target } = record;
        let type = 'Select';
        let props = {};
        const valueProps = JSON.parse(record.value_props || '{}');

        if (record.value_type === 'range') {
          type = 'RuleCompsRange';
          props = {
            code: target
          };
        } else if (record.value_type === 'number') {
          type = 'InputNumber';
          const unit = valueProps.unit;
          if (unit) {
            props = {
              formatter: (value) => `${value}${unit}`,
              parser: (value) => value.replace(unit, '')
            };
          }
        } else if (record.value_type === 'boolean') {
          props = {
            options: [
              {
                label: '是',
                value: 'TRUE'
              },
              {
                label: '否',
                value: 'FALSE'
              }
            ]
          };
        } else {
          props = {
            url: '/sqlreview/common/item-list/',
            reqParams: {
              enable: 1,
              parent_item_key: target
            }
          };
        }
        return {
          type,
          props: {
            ...(valueProps || {}),
            ...props,
            style: 'width: 100%',
            size: 'default'
          },
          rules: [{ required: true, message: '该项为必填项' }]
        };
      }
    ];
  };
  return { condition };
}
