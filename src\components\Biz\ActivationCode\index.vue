<template>
  <a-modal
    v-model="visible"
    title="账号延期"
    wrapClassName="components-biz-activation-code"
    width="50%"
    @cancel="onCancel"
    :maskClosable="false"
    :closable="closable"
    :keyboard="false"
    :dialogStyle="{ 'minWidth': '500px', 'maxWidth': '500px' }"
  >
    <a-spin :spinning="spinning">
      <div style="padding: 24px 0;">
        <div>您好，您的账号已到期。</div>
        <div>请联系SQLReview平台获取新的激活码，进行续期。</div>
      </div>
      <div class="seach-area-content">
        <Form ref="form" v-bind="formParams" :formData="formData"></Form>
      </div>
    </a-spin>
    <template slot="footer">
      <a-button type="default" @click="active">激活</a-button>
    </template>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import { verifyActivationCode } from '@/api/login';
export default {
  components: {
    Form
  },
  props: {},
  data() {
    return {
      data: {},
      visible: false,
      closable: false,
      spinning: false,
      formData: {},
      formParams: {
        fields: [
          {
            type: 'Textarea',
            label: '激活码',
            key: 'code',
            rules: [{ required: true, message: '该项为必填项' }]
          }
        ],
        layout: 'horizontal',
        labelCol: { span: 24 },
        wrapperCol: { span: 24 },
        multiCols: 1
      }
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.data = data;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
      this.closable = false;
      sessionStorage.removeItem('isActivation');
    },
    onCancel() {
      this.hide();
    },
    active() {
      const { form } = this.$refs;
      const data = form.getData();
      form.validate((valid, error) => {
        if (valid) {
          this.spinning = true;
          verifyActivationCode(data)
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.closable = true;
                this.spinning = false;
                this.$hideLoading({ tips: _.get(res, 'data.message') });
                const bool = this.data && this.data.is_third_login;
                if (bool) {
                  window.Login.go(this.data);
                }
                this.hide();
                sessionStorage.removeItem('isActivation');
              } else {
                this.spinning = false;
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.spinning = false;
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
</style>
