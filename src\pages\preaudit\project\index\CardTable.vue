<template>
  <div class="card-table">
    <div class="card-table-main-info">
      <div class="left-block">
        <div class="project-name">
          <a @click="toDetail">
            <LimitLabel
              :label="cardData.project_name || ''"
              mode="ellipsis"
              :noWrap="true"
            ></LimitLabel>
          </a>
        </div>
        <div class="info-box">
          <div class="status-box">
            <span class="review-type">
              <span>审核方式</span>
              <a-tag
                color="#E6F7FF"
                style="color: #1890ff; border: 1px solid rgba(145, 213, 255, 1)"
                >{{ cardData.mode == 0 ? '全量' : '增量' }}</a-tag
              >
            </span>
            <span>
              <a-tooltip>
                <template slot="title" v-if="cardData.operator_dba">
                  <span>{{ cardData.operator_dba }}</span>
                </template>
                <span v-if="cardData.dba_status == '待评审'">人工审核</span>
                <span v-else>审核人({{ cardData.ch_dba || '--' }})</span>
              </a-tooltip>
              <a-tooltip v-if="cardData.dba_status == '待评审'">
                <template slot="title" v-if="cardData.operator_dba">
                  <span
                    >任务提交评审后可联系DBA负责人或者项目负责人进行审批。</span
                  >
                </template>
                <custom-icon type="question-circle"></custom-icon>
              </a-tooltip>
              <a-tooltip
                v-if="
                  cardData.dba_status == '未通过' && cardData.comment_content
                "
              >
                <template slot="title">
                  <span>驳回原因：{{ cardData.comment_content }}</span>
                </template>
                <StatusTag type="dba" :status="cardData.dba_status">
                  <a-icon style="marginleft: 4px" type="question-circle" />
                </StatusTag>
              </a-tooltip>
              <StatusTag type="dba" :status="cardData.dba_status" v-else />
            </span>
            <span class="ai-review">
              <span>AI审核</span>
              <a-progress
                v-if="[0, 3, 4, 5].includes(cardData.status)"
                :strokeWidth="8"
                :percent="Number(cardData.progress)"
                size="small"
              />
              <a-tooltip
                v-if="cardData.status == '9' && cardData.error_message"
              >
                <template slot="title">
                  <span>{{ cardData.error_message }}</span>
                </template>
                <StatusTag
                  type="review"
                  :status="cardData.status"
                  fromPath="sqlreview"
                >
                  <a-icon style="marginleft: 4px" type="question-circle" />
                </StatusTag>
              </a-tooltip>
              <StatusTag
                type="review"
                :status="cardData.status"
                fromPath="sqlreview"
                v-else
              />
            </span>
          </div>
        </div>

        <div class="tag-box">
          <a-tag>ID：{{ cardData.id }}</a-tag>
          <a-tag>分支：{{ cardData.review_point }}</a-tag>
          <a-tag
            class="project-group tooltip"
            v-if="cardData.project_group && cardData.project_group.length > 0"
          >
            <span>项目组：</span>
            <span v-if="cardData.project_group.length == 1">{{
              cardData.project_group[0]
            }}</span>
            <a-tooltip v-else>
              <template slot="title">
                <span>{{ cardData.project_group.toString() }}</span>
              </template>
              <span>{{ cardData.project_group[0] + '; ' + '...' }}</span>
            </a-tooltip>
          </a-tag>
          <a-tag
            class="tooltip"
            v-if="
              cardData.project_group_leader &&
              cardData.project_group_leader.length > 0
            "
          >
            <span>项目组负责人：</span>
            <span v-if="cardData.project_group_leader.length > 0">
              <span>
                <a-tooltip>
                  <template slot="title">
                    <span>
                      {{ cardData.project_group_leader | handleTitle }}</span
                    >
                  </template>
                  <span>{{
                    cardData.project_group_leader | handleContent
                  }}</span>
                </a-tooltip>
              </span>
            </span>
          </a-tag>
          <a-tag
            class="tooltip"
            v-if="cardData.dba_leader && cardData.dba_leader.length > 0"
          >
            <span>DBA负责人：</span>
            <span v-if="cardData.dba_leader.length > 0">
              <span>
                <a-tooltip>
                  <template slot="title">
                    <span>{{ cardData.dba_leader | handleTitle }}</span>
                  </template>
                  <span>{{ cardData.dba_leader | handleContent }}</span>
                </a-tooltip>
              </span>
            </span>
          </a-tag>
          <a-tag
            v-if="
              cardData.passing_rate && !['--'].includes(cardData.passing_rate)
            "
            >AI通过率： {{ cardData.passing_rate }}</a-tag
          >
          <a-tag v-if="cardData.history_baseline == 1">历史标准基线</a-tag>
        </div>

        <div class="side-info-box">
          <span class="avatar-part">
            <a-avatar icon="user" />
          </span>
          <a-tooltip v-if="cardData.ch_creater">
            <template slot="title">
              <span>{{ cardData.created_by }}</span>
            </template>
            <span class="created-by">{{ cardData.ch_creater || '--' }}</span>
          </a-tooltip>
          <span class="created-by" v-else>{{ cardData.created_by }}</span>
          <span>于{{ cardData.created_at + ' ' }}</span>
          <span class="event">发起审核</span>
          <span class="audit"
            >审核时长：
            {{
              cardData.ai_review_total_time
                ? cardData.ai_review_total_time + '分钟'
                : '--'
            }}</span
          >
        </div>
      </div>
      <div class="right-block">
        <div class="right-block-rules">
          <div>
            <span>触发规则</span>
            <span class="loading" v-if="[0, 3, 4, 5].includes(cardData.status)">
              <custom-icon type="lu-icon-loading1"></custom-icon>
            </span>
            <span class="rules" v-else>{{ cardData.rule_count }}</span>
          </div>
        </div>
        <a-divider type="vertical" />
        <div class="right-block-sql-text">
          <div>
            <span>SQL语句</span>
            <span class="loading" v-if="[0, 3, 4, 5].includes(cardData.status)">
              <custom-icon type="lu-icon-loading1"></custom-icon>
            </span>
            <span v-else>{{ cardData.sql_count }}</span>
          </div>
        </div>
        <a-divider class="right-block-divider" type="vertical" />
        <div class="right-block-botton">
          <div class="btns-wrapper-nomal">
            <a
              v-if="[1, 2].includes(cardData.review_status)"
              :disabled="cardData.review_status == 1"
              @click="authSubmit(cardData.id)"
            >
              提交评审</a
            >
            <a
              v-if="
                cardData.dba_status === '评审中' ||
                cardData.dba_status === '待评审'
              "
              @click="urge"
              >催办</a
            >
            <a @click="showReport"> 报表 </a>
            <a-popconfirm
              title="重新发起审核任务?"
              @confirm="() => reReview(cardData)"
            >
              <a
                v-if="
                  ![1, 2].includes(cardData.type) &&
                  [-1, 9].includes(cardData.status) &&
                  ['未提交', '未通过'].includes(cardData.dba_status) &
                    (cardData.version_control == 1)
                "
              >
                重新review</a
              >
            </a-popconfirm>
            <a
              v-if="[0, 3, 4, 5].includes(cardData.status)"
              @click="terminate(cardData)"
            >
              终止
            </a>
            <a-popconfirm
              title="确定移除该数据?"
              @confirm="() => remove(cardData)"
            >
              <a
                class="remove"
                v-if="canDo && ![0, 3, 4, 5].includes(cardData.status)"
              >
                删除
              </a>
            </a-popconfirm>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';
import StatusTag from '@/components/Biz/Status/Tag';
export default {
  components: { LimitLabel, StatusTag },
  props: {
    cardData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  computed: {
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  created() {},
  methods: {
    // 详情
    toDetail() {
      this.$emit('toDetail');
    },
    // 报表
    showReport() {
      this.$emit('showReport');
    },
    // 催办
    urge() {
      this.$emit('urge');
    },
    // 提交评审
    authSubmit() {
      this.$emit('authSubmit');
    },
    // 删除
    remove() {
      this.$emit('remove');
    },
    // 终止
    terminate() {
      this.$emit('terminate');
    },
    reReview() {
      this.$emit('reReview');
    }
  },
  filters: {
    handleTitle(data) {
      const title = data.map(item => {
        return item.name + '(' + item.ch_name + ')';
      });
      return title.join();
    },
    handleContent(data) {
      const names =
        data &&
        data.map(item => {
          return item.ch_name;
        });
      const res = data && data.length > 1 ? names[0] + '...' : names[0];
      return res;
    }
  }
};
</script>

<style lang="less" scoped>
.card-table {
  .card-table-main-info {
    display: flex;
    justify-content: space-between;
    .left-block {
      display: flex;
      flex-direction: column;
      margin-right: 32px;
      .project-name {
        margin-bottom: 8px;
        &:hover {
          cursor: pointer;
        }
        /deep/.limit-label {
          pre {
            font-family: PingFangSC-Semibold;
            font-size: 16px !important;
            color: #4db5f2 !important;
            font-weight: 500 !important;
          }
        }
      }
      .info-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .status-box > span,
        > span {
          margin-right: 28px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          font-weight: 400;
        }
        /deep/.status-box {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          .review-type {
            white-space: nowrap;
            .ant-tag {
              font-size: 14px;
              margin-right: 0;
              margin-left: 4px;
              padding: 0 8px;
            }
          }
          .ai-review {
            display: flex;
            align-items: center;
            white-space: nowrap;
            .ant-progress {
              margin: 0 4px 0 8px;
              width: 100px;
              .ant-progress-outer {
                // width: 84px;
                .ant-progress-inner {
                  // width: 54px;
                  border-radius: 0;
                  .ant-progress-bg {
                    border-radius: 0 !important;
                  }
                }
              }
            }
            .ant-tag {
              margin-left: 8px;
            }
          }
        }
      }
      .tag-box {
        margin: 12px 0;
        display: flex;
        flex-wrap: wrap;
        .ant-tag {
          margin-bottom: 4px;
        }
        .tooltip:hover {
          cursor: pointer;
        }
      }
      .side-info-box {
        display: flex;
        align-items: center;
        > span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          letter-spacing: 0;
          font-weight: 400;
          &.avatar-part {
            width: 20px;
            .ant-avatar {
              width: 20px;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              margin-bottom: 2px;
              color: #fff;
              background: #4ec3f5;
            }
          }
        }
        .created-by {
          margin: 0 4px 0 8px;
          color: rgb(113, 113, 122);
        }
        .event {
          color: rgb(113, 113, 122);
        }
        .audit {
          color: #71717a;
          margin-left: 12px;
          white-space: nowrap;
        }
      }
    }
    .right-block {
      display: flex;
      align-items: center;
      .ant-divider-vertical {
        width: 1px;
        height: 32px;
        margin: 0 12px;
      }
      .right-block-rules,
      .right-block-sql-text {
        padding: 0 18px;
        display: flex;
        align-items: center;
        > div {
          display: flex;
          flex-direction: column;
          text-align: center;
          & span:first-child {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #8c8c8c;
            letter-spacing: 0;
            font-weight: 400;
            margin-bottom: 8px;
            white-space: nowrap;
          }
          & span:last-child {
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: #27272a;
            letter-spacing: 0;
            font-weight: 400;
          }
          @keyframes rotate {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
          .loading {
            animation: rotate 2s linear infinite;
          }
        }
      }
      .right-block-rules {
        .rules {
          color: #e71d36 !important;
        }
      }
      .right-block-botton {
        padding-left: 18px;
        width: 160px;
        display: flex;
        align-items: flex-start;
        .btns-wrapper-nomal {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          a {
            margin: 6px 12px 8px 0;
          }
        }
        a {
          height: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #4db5f2;
          letter-spacing: 0;
          line-height: 16px;
          font-weight: 400;
          display: flex;
          align-items: center;
          // margin-right: 12px;
          > .anticon {
            margin-right: 4px;
          }
          &.highlight {
            padding: 14px 6px 14px 8px;
            background: #4db5f2;
            border-radius: 4px;
            color: #fff;
            margin-right: 0;
            > .anticon {
              font-size: 12px;
              margin-right: 0;
              margin-left: 4px;
            }
          }
          &.remove {
            color: #e26148;
          }
          &:hover {
            color: @primary-color;
            &.highlight {
              color: #fff;
              background: @primary-color;
            }
          }
        }
        a[disabled] {
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .card-table {
    .card-table-main-info {
      .right-block {
        display: flex;
        justify-content: space-between;
        .right-block-divider {
          display: none;
        }
        .right-block-botton {
          width: 160px;
          display: none;
        }
      }
      .left-block {
        .info-box {
          .project-name {
            width: 100%;
            > span {
              max-width: 300px;
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1560px) {
  .card-table {
    .card-table-main-info {
      .left-block {
        .info-box {
          .project-name {
            > span {
              max-width: 270px;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less" scoped></style>
