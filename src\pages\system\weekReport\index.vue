<template>
  <div class="page-week-report page-list-single">
    <Table
      ref="table"
      v-bind="tableParams || {}"
      class="new-view-table small-size"
    >
      <LimitLabel
        slot="limit"
        slot-scope="{ record, text }"
        :limit="30"
        :label="text || '--'"
      ></LimitLabel>
      <a
        class="file-name"
        slot="fileName"
        slot-scope="{ record, text }"
        @click="download(record)"
      >
        <LimitLabel mode="ellipsis" :label="text || '--'"></LimitLabel>
      </a>
      <custom-btns-wrapper slot="action" slot-scope="{ record }">
        <a @click="edit(record)" actionBtn>编辑</a>
        <!-- <a-popconfirm title="确定发送?" @confirm="() => send(record)" actionBtn> -->
        <a @click="send(record)" actionBtn>发送</a>
        <!-- </a-popconfirm> -->
      </custom-btns-wrapper>
    </Table>
    <EditDrawer ref="EditDrawer" @success="refresh"></EditDrawer>
    <SendModal ref="SendModal" @success="refresh"></SendModal>
  </div>
</template>

<script>
import _ from 'lodash';
import Table from '@/components/Table';
import { weeklyReportDownload } from '@/api/weekReport';
import LimitLabel from '@/components/LimitLabel';
import EditDrawer from './components/EditDrawer';
import SendModal from './components/SendModal';
import config from './config';

export default {
  name: 'system-config',
  props: {},
  components: { Table, LimitLabel, EditDrawer, SendModal },
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: '/sqlreview/review/weekly-report',
        rowKey: 'id',
        columns: this.config.columns,
        searchFields: this.config.searchFields,
        needSearchArea: true,
        needTools: true,
        scroll: { x: 1600 },
        searchParamsFilter(res = {}) {
          const date = (res.start_date || '').split(',');
          if (date) {
            res.start_date = date[0];
            res.end_date = date[1];
          }
          return res;
        }
      }
    };
  },
  computed: {},
  mounted() {},
  methods: {
    refresh() {
      this.$refs.table.refresh();
    },
    edit(record) {
      this.$refs.EditDrawer.show(record);
    },
    send(record) {
      this.$refs.SendModal.show(record);
    },
    download(record) {
      this.$showLoading({
        tips: `下载中...`
      });
      weeklyReportDownload({ record_id: record.id })
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less">
.page-week-report {
  .file-name {
    display: inline-block;
    width: 100%;
    > .limit-label {
      display: inline-block;
      width: 100%;
    }
    pre {
      color: @primary-color !important;
    }
  }
}
</style>
