import Http from '@/utils/request'
export function getDbData(params = {}, getCancel) {
  return Http({
    // url: `/sqlreview/getSqlresolverTree`,
    url: '/sqlreview/sql_tool/get_db_elements',
    method: 'get',
    params,
    getCancel(cancel) {
      getCancel && getCancel(cancel);
    }
  });
}

export function getExecuteLog(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/get_execute_log`,
    method: 'get',
    params
  });
}

export function checkSqlText(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/check_sql_text`,
    method: 'post',
    data: params
  });
}

export function executeSql(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/execute_sql`,
    method: 'post',
    data: params
  });
}

export function examineSql(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/examine_sql`,
    method: 'post',
    data: params
  });
}

export function createSql(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/create_sql`,
    method: 'post',
    data: params
  });
}

// export function addSqlSheet(params = {}) {
//   return Http({
//     url: `/sqlreview/sql_tool/add_sql_sheet`,
//     method: 'post',
//     data: params
//   });
// }
// export function editSqlSheet(params = {}) {
//   return Http({
//     url: `/sqlreview/sql_tool/edit_sql_sheet`,
//     method: 'post',
//     data: params
//   });
// }

export function saveSqlSheet(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/save_sql_sheet`,
    method: 'post',
    data: params
  });
}
export function delSqlSheet(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/del_sql_sheet`,
    method: 'post',
    data: params
  });
}
export function getSqlSheet(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/get_sql_sheet`,
    method: 'get',
    params
  });
}

export function getTableColumnList(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/get_table_column_list`,
    method: 'get',
    params
  });
}
export function getTableTrigger(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/get_table_trigger`,
    method: 'get',
    params
  });
}
export function getTableIndex(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/get_table_index`,
    method: 'get',
    params
  });
}

export function addColumnPrivilege(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/add_column_privilege`,
    method: 'post',
    data: params
  });
}

export function dropTable(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/drop_table/`,
    method: 'post',
    data: params
  });
}

export function truncateTable(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/truncate_table/`,
    method: 'post',
    data: params
  });
}

export function getCreateTableSql(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/get_create_table_sql/`,
    method: 'post',
    data: params
  });
}

export function getFirstRows(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/get_first_200_rows/`,
    method: 'post',
    data: params
  });
}

export function tableScripts(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/write_table_scripts/`,
    method: 'post',
    data: params
  });
}

export function deleteDbElements(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/del_db_element/`,
    method: 'post',
    data: params
  });
}

export function getGlobalSettings(params = {}) {
  return Http({
    url: `/sqlreview/sql_tool/get_global_settings`,
    method: 'get',
    params: params
  });
}
