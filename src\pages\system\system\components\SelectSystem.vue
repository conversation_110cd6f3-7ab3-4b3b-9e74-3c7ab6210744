<template>
  <div class="config-system-select">
    <Form ref="form" v-bind="params" :formData="formData"></Form>
  </div>
</template>

<script>
import Form from '@/components/Form';

export default {
  components: { Form },
  props: {
    data: Object
  },
  data() {
    return {
      inputValue: null,
      id: null,
      params: {
        fields: [
          {
            type: 'Select',
            label: 'MYSQL',
            key: 'MYSQL',
            props: {
              url: 'sqlreview/project/dml_ddl_rule_set_list',
              reqParams: {
                db_type: 'MYSQL',
                rule_set_type: 'DML',
                source: 'config'
              }
            },
            width: '300',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'change' }
            ]
          },
          {
            type: 'Select',
            label: 'POSTGRE',
            key: 'POSTGRE',
            props: {
              url: 'sqlreview/project/dml_ddl_rule_set_list',
              reqParams: {
                db_type: 'POSTGRE',
                rule_set_type: 'DML',
                source: 'config'
              }
            },
            width: '300',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'change' }
            ]
          },
          {
            type: 'Select',
            label: 'TIDB',
            key: 'TIDB',
            props: {
              url: 'sqlreview/project/dml_ddl_rule_set_list',
              reqParams: {
                db_type: 'TIDB',
                rule_set_type: 'DML',
                source: 'config'
              }
            },
            width: '300',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'change' }
            ]
          },
          {
            type: 'Select',
            label: 'ORACLE',
            key: 'ORACLE',
            props: {
              url: 'sqlreview/project/dml_ddl_rule_set_list',
              reqParams: {
                db_type: 'ORACLE',
                rule_set_type: 'DML',
                source: 'config'
              }
            },
            width: '300',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'change' }
            ]
          }
        ],
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        colon: true
      },
      formData: {}
    };
  },
  watch: {
    data: {
      handler(val) {
        const res = _.get(val, 'sql_review_dynamic_form_element');
        const value = {};
        const ids = [];
        res.forEach(item => {
          value[item.element_label] = item.element_value;
          ids.push(item.id);
        });
        this.formData = value;
        this.id = ids;
      },
      immediate: true
    }
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    getData() {
      let res = {};
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          res = this.$refs.form.getData();
        }
      });
      return res;
    },
    reset() {
      this.$refs.form.resetFields();
    }
  }
};
</script>

<style lang="less" scoped>
.config-system-select {
  margin-top: 20px;
}
</style>
