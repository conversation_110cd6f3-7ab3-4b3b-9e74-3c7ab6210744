<template>
  <div>
    <!-- 标题 branch name 区分是否有比对 -->
    <div class="review-wraper">
      <div class="tag-info" v-if="!detaliData.compare_point">
        <span>{{ detaliData.review_point }}</span>
      </div>
      <div class="review-diff-wraper" v-else>
        <div class="tag-info">
          <!-- 当前版本号&nbsp;&nbsp; -->
          <span>{{ detaliData.review_point }}</span>
        </div>
        <div class="tag-info" :style="{ textAlign: 'right' }">
          <span>{{ detaliData.compare_point }}</span>
          <!-- &nbsp;&nbsp;历史版本号 -->
        </div>
      </div>
    </div>
    <!-- 第一部分：1、最差sql+执行计划 2、sqlmap -->
    <div class="part-1 sql-list-item review-wraper" v-if="sqlPlan" :id="`sql-item-${sqlPlan.id}`">
      <!-- 1. 根据 format: sql | xml 展示 -->
      <div :class="{ 'review-wraper2': true, 'sql-detail-item': true }">
        <div v-if="!sqlPlan.compare_sql_text">
          <h4>
            <a-icon type="exception" />
            {{
            sqlPlan.has_dynamic_splicing
            ? '当前动态条件组合执行效率最差SQL'
            : '当前SQL信息'
            }}
          </h4>
          <template v-if="sqlPlan.ai_comment">
            <template v-if="sqlPlan.ai_comment.length > 0">
              <!-- 原逻辑只展示第一条 -->
              <!-- <pre >{{ item.ai_comment[0] ? item.ai_comment[0].ai_comment : '' }}</pre> -->
              <!-- 现在全部展示 -->
              <div class="pre-ai-comment">
                <pre v-for="(elem, index) in sqlPlan.ai_comment" :key="index">
                  <!-- 1是低风险 0高风险 -->
                  <custom-icon
  class="rule-icon high"
  type="lu-icon-alarm"
  v-if="elem.rule_result == 0"
/>
                  <custom-icon
  class="rule-icon low"
  type="lu-icon-alarm"
  v-if="elem.rule_result == 1"
/>
                  <span>{{ elem.ai_comment }}</span>
                </pre>
              </div>
              <!-- <a-divider style="margin: 0px;" /> -->
            </template>
            <template v-if="sqlPlan.error_message && sqlPlan.error_message.length > 0">
              <div class="pre-error-message">
                <pre
                  v-for="(item, index) in sqlPlan.error_message"
                  :key="`${index}_${item.schema}`"
                  style="padding: 0 0 8px 16px; color: rgb(176, 174, 174); width: 100%;"
                >{{ item.data_source_name }}: {{ item.error_message }}</pre>
              </div>
            </template>
          </template>
          <sql-highlight :sql="sqlPlan.sql_text" title="[SQL文本]"></sql-highlight>
        </div>
        <div v-else>
          <code-mirror
            class="sql-detail-compare"
            :value="sqlPlan.sql_text"
            :valueTitle="
              sqlPlan.has_dynamic_splicing
              ? '当前动态条件组合执行效率最差SQL'
              : '当前SQL信息'
            "
            :orig="sqlPlan.compare_sql_text"
            :origTitle="'上一次review的SQL信息'"
            :format="sqlPlan.sql_format"
          >
            <div
              style="
                position: relative;
                top: 8px;
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                line-height: 22px;
                background: rgba(15, 120, 251, 0.06);
              "
              slot="head-right-extend"
              v-if="sqlPlan.ai_comment && sqlPlan.ai_comment.length > 0"
            >
              <div style="padding: 8px 0; flex-grow: 1; display: flex;">
                <div class="pre-error-message">
                  <div class="pre-rule">
                    <pre
                      v-for="(item, index) in sqlPlan.ai_comment"
                      :key="index"
                      style="padding: 4px 16px; background: transparent;"
                    >
                      <custom-icon
  class="rule-icon high"
  type="lu-icon-alarm"
  v-if="item.rule_result == 0"
/>
                      <custom-icon
  class="rule-icon low"
  type="lu-icon-alarm"
  v-if="item.rule_result == 1"
/>
                      <span>{{ item.ai_comment || '暂无触发规则' }}</span>
                   </pre>
                  </div>
                  <div v-if="sqlPlan.ai_comment.all_wrong == 1">
                    <pre
                      v-for="(item, index) in sqlPlan.error_message"
                      :key="`${index}_${item.schema}`"
                      style="padding: 0 0 8px 16px; color: rgb(176, 174, 174); background: transparent"
                    >{{ item.schema }}: {{ item.error_message }}</pre>
                  </div>
                </div>
              </div>
              <!-- <a-divider style="margin: 0px;" /> -->
            </div>
            <div
              style="
                position: relative;
                top: 8px;
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                line-height: 22px;
                background: rgba(15, 120, 251, 0.06);
              "
              slot="head-right-extend"
              v-else
            >
              <div style="padding: 8px 0; flex-grow: 1; display: flex;">
                <div class="pre-error-message">
                  <div class="pre-rule">
                    <pre style="padding: 4px 16px; background: transparent;">{{'暂无触发规则' }}</pre>
                  </div>
                </div>
              </div>
            </div>
            <div
              style="
                position: relative;
                top: 8px;
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                line-height: 22px;
                background: rgba(15, 120, 251, 0.06);
              "
              slot="head-left-extend"
              v-if="sqlPlan.compare_ai_comment && sqlPlan.compare_ai_comment.length > 0"
            >
              <div style="padding: 8px 0; flex-grow: 1; display: flex;">
                <div class="pre-error-message">
                  <div class="pre-rule">
                    <pre
                      v-for="(item, index) in sqlPlan.compare_ai_comment"
                      :key="index"
                      style="padding: 4px 16px; background: transparent;"
                    >
                      <custom-icon
  class="rule-icon high"
  type="lu-icon-alarm"
  v-if="item.rule_result == 0"
/>
                      <custom-icon
  class="rule-icon low"
  type="lu-icon-alarm"
  v-if="item.rule_result == 1"
/>
                      <span>{{ item.ai_comment || '暂无触发规则' }}</span></pre>
                  </div>
                  <div v-for="(item, index) in sqlPlan.compare_ai_comment" :key="index">
                    <div v-if="item.all_wrong == 1">
                      <pre
                        v-for="(item, index) in sqlPlan.error_message"
                        :key="`${index}_${item.schema}`"
                        style="padding: 0 16px 8px 16px; color: rgb(176, 174, 174); background: transparent; display: inline-block;"
                      >{{ item.schema }}: {{ item.error_message }}</pre>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <a-divider style="margin: 0px;" /> -->
            </div>
            <div
              style="
                position: relative;
                top: 8px;
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                line-height: 22px;
                background: rgba(15, 120, 251, 0.06);
              "
              slot="head-left-extend"
              v-else
            >
              <div style="padding: 8px 0; flex-grow: 1; display: flex;">
                <div class="pre-error-message">
                  <div class="pre-rule">
                    <pre style="padding: 4px 16px; background: transparent;">{{ '暂无触发规则' }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </code-mirror>
        </div>
        <!-- 2. 当前执行计划信息：有则展示无则隐藏 -->
        <div class style="margin-top: 24px;" v-if="sqlPlan.sql_plan">
          <template v-if="!sqlPlan.compare_sql_plan">
            <div>
              <h4>
                <a-icon type="exception" />
                {{
                sqlPlan.has_dynamic_splicing
                ? '当前动态组合最差执行计划'
                : '当前执行计划信息'
                }}
                <span
                  v-if="sqlPlan.data_source_name"
                >
                  (
                  <span
                    style="
                      color: #0f78fb;
                      font-size: 16px;
                      font-weight: bold;
                      margin-right: 4px;
                    "
                  >{{ sqlPlan.data_source_name.split('|')[0] }}</span>
                  <span>{{ sqlPlan.data_source_name.split('|')[1] }}</span>
                  )
                </span>
              </h4>
            </div>
            <div class="redo">
              <pre>{{ sqlPlan.sql_plan }}</pre>
            </div>
          </template>
          <template v-else>
            <code-mirror
              :orig="sqlPlan.compare_sql_plan || ''"
              origTitle="上一次review的执行计划信息"
              :value="sqlPlan.sql_plan || ''"
              :valueTitle="sqlPlan.has_dynamic_splicing
                ? '当前动态组合最差执行计划'
                : '当前执行计划信息'"
              :format="'txt'"
            >
              <span slot="head-right-extend" v-if="sqlPlan.compare_data_source_name">
                (
                <span
                  style="
                    color: #0f78fb;
                    font-size: 16px;
                    font-weight: bold;
                    margin-right: 4px;
                  "
                >{{ sqlPlan.compare_data_source_name.split('|')[0] }}</span>
                <span>{{ sqlPlan.compare_data_source_name.split('|')[1] }}</span>
                )
              </span>
              <span slot="head-left-extend" v-if="sqlPlan.data_source_name">
                (
                <span
                  style="
                    color: #0f78fb;
                    font-size: 16px;
                    font-weight: bold;
                    margin-right: 4px;
                  "
                >{{ sqlPlan.data_source_name.split('|')[0] }}</span>
                <span>{{ sqlPlan.data_source_name.split('|')[1] }}</span>
                )
              </span>
            </code-mirror>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CodeMirror from '@/components/CodeMirror';
import SqlHighlight from '@/components/SqlHighlight';
export default {
  components: { CodeMirror, SqlHighlight },
  props: {
    detaliData: {
      type: Object,
      default: () => {}
    },
    sqlPlan: {
      type: Object,
      default: () => {}
    }
  },
  methods: {}
};
</script>

<style lang="less" scoped>
@import './commonClass.less';
.home-review-wraper {
  .review-wraper {
    padding-left: 0 !important;
    .tag-info {
      padding: 8px 0 0 0;
      background-color: rgba(15, 120, 251, 0);
      span {
        background: rgba(91, 147, 255, 0.15);
        color: rgb(33, 74, 192);
        &:before {
          background: rgb(33, 74, 192);
        }
      }
    }
    .review-diff-wraper {
      .tag-info {
        &:last-child {
          background: rgba(35, 190, 108, 0);
          > span {
            background: rgb(220, 252, 231);
            color: rgb(22, 101, 52);
            &:before {
              background: rgb(22, 101, 52);
            }
          }
        }
      }
    }
    .redo {
      border-radius: 10px;
      background: #f4f5f7;
    }
  }
  .part-1 {
    padding: 0 !important;
    /deep/ .review-wraper2 {
      margin-top: 16px;
      .pre-error-message > pre:last-child {
        padding-bottom: 16px !important;
      }
      .pre-error-message > pre:first-child {
        padding-top: 16px !important;
      }
      .pre-error-message {
        display: flex;
        flex-direction: column;
        overflow: auto;
        width: 100%;
        max-height: 200px;
        // height: 140px;
        // padding: 0 8px;
        pre {
          overflow: visible;
        }
      }
      .pre-ai-comment {
        background: rgba(0, 59, 114, 0.13);
        border-radius: 10px 10px 0 0;
        overflow: auto;
        width: 100%;
        pre {
          padding: 8px 16px 0 16px;
          overflow: auto;
          background: rgba(0, 59, 114, 0.13);
          // font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #3e60c1;
          font-weight: 500;
          display: flex;
          align-items: center;
          &:last-child {
            padding-bottom: 8px;
          }
          .rule-icon {
            margin-right: 8px;
            color: #e4e4e7;
            font-size: 12px;
            align-self: self-start;
            padding-top: 4px;
            &.high {
              color: #e71d36;
              margin-right: 4px;
            }
            &.low {
              color: #f29339;
              margin-right: 4px;
            }
            &.error {
              color: #71717a;
              margin-right: 4px;
            }
          }
          > span {
            padding-right: 16px;
          }
        }
      }
      .custom-code-mirror-diff {
        .ccm-diff-title-wrapper {
          .ccm-diff-title {
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            .anticon {
              font-size: 16px;
              color: #27272a;
            }
            > span {
              > span {
                font-size: 14px;
                color: #27272a;
              }
            }
          }
        }
        .ccm-diff-container {
          background: #f4f5f7;
          .CodeMirror-merge-pane {
            border-radius: 10px;
            background: #f4f5f7;
          }
        }
        &.sql-detail-compare {
          .ccm-diff-title-wrapper {
            .ccm-diff-title {
              display: flex;
              flex-direction: column;
              > div {
                // margin: 0 0 8px 0;
                > div {
                  padding: 0 !important;
                  border-radius: 10px 10px 0 0 !important;
                  background: rgba(0, 59, 114, 0.13) !important;
                  .pre-error-message {
                    overflow: auto;
                    .pre-rule {
                      padding: 8px 16px 0 16px;
                      > pre {
                        // font-family: PingFangSC-Medium;
                        font-size: 14px;
                        color: #3e60c1;
                        font-weight: 500;
                        margin-bottom: 8px;
                        padding: 0 !important;
                        display: flex;
                        align-items: center;
                        .rule-icon {
                          margin-right: 8px;
                          color: #e4e4e7;
                          font-size: 12px;
                          align-self: self-start;
                          padding-top: 5px;
                          &.high {
                            color: #e71d36;
                            margin-right: 4px;
                          }
                          &.low {
                            color: #f29339;
                            margin-right: 4px;
                          }
                          &.error {
                            color: #71717a;
                            margin-right: 4px;
                          }
                        }
                        > span {
                          padding-right: 16px;
                        }
                      }
                    }
                  }
                }
              }
              .ant-divider {
                display: none;
              }
            }
          }
          .ccm-diff-container {
            // background: rgba(0, 59, 114, 0.06);
            .CodeMirror-merge-pane {
              border-radius: 0 0 10px 10px;
              background: rgba(0, 59, 114, 0.06) !important;
            }
          }
        }
      }
    }
    h4 {
      font-family: PingFangSC-Semibold;
      font-size: 14px;
      color: #27272a;
      font-weight: 600;
      .anticon {
        font-size: 16px;
        color: #27272a;
      }
      > span {
        > span {
          font-size: 14px;
          color: #27272a;
        }
      }
    }
  }
}
/deep/.sql-format {
  background: rgba(0, 59, 114, 0.06) !important;
  border-radius: 0 0 10px 10px !important;
}
</style>
