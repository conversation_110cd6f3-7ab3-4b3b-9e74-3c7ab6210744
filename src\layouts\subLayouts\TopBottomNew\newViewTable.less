#layout-root.top-bottom-new {
  .new-view-table {
    .search-area-wrapper {
      .custom-table-top-right {
        .custom-table-tools {
          .anticon {
            font-size: 16px;
            color: #27272a;
            &:hover {
              color: #fff;
            }
          }
        }
      }
    }
    .ant-table-wrapper {
      .ant-table {
        .ant-table-content {
          .ant-table-tbody {
            tr {
              > td {
                padding: 16px 32px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #1f1f1f !important;
                font-weight: 400;
                .custom-btns-wrapper {
                  > a {
                    font-size: 14px;
                    font-weight: 400;
                  }
                }
                .limit-label {
                  pre {
                    font-size: 14px;
                    color: #27272a;
                    font-weight: 400;
                  }
                }
                .ant-tag {
                  font-family: PingFangSC-Regular;
                  font-size: 12px;
                  color: #27272a;
                  font-weight: 400;
                }
              }
            }
          }
          .ant-table-thead {
            border-top: 1px solid #e8e8e8;
            tr {
              > th {
                height: 56px;
                padding: 16px 32px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #1f1f1f !important;
                background: #fafafa;
                position: relative;
                &::after {
                  content: '';
                  position: absolute;
                  display: inline-block;
                  width: 1px;
                  height: 20px;
                  right: 0;
                  top: 18px;
                  background: #e8e8e8;
                }
                &:last-child::after {
                  content: '';
                  position: absolute;
                  width: 0;
                  right: 0;
                }
                .ant-table-column-sorters {
                  .ant-table-column-sorter {
                    .ant-table-column-sorter-inner-full {
                      margin-top: -0.35em;
                    }
                  }
                }
                .anticon-filter {
                  top: -2px;
                }
              }
            }
          }
        }
      }
      .ant-pagination {
        margin-right: 32px;
      }
    }
    &.small-size {
      // .ant-table-wrapper {
      //   .ant-table {
      //     .ant-table-content {
      //       .ant-table-tbody {
      //         tr {
      //           > td {
      //             padding: 12px 24px;
      //             font-family: PingFangSC-Regular;
      //             font-size: 13px;
      //             color: #27272a !important;
      //             font-weight: 400;
      //             .custom-btns-wrapper {
      //               > a {
      //                 font-size: 13px;
      //                 font-weight: 400;
      //               }
      //             }
      //             .limit-label {
      //               pre {
      //                 font-family: PingFangSC-Regular;
      //                 font-size: 13px;
      //                 color: #27272a;
      //                 font-weight: 400;
      //               }
      //             }
      //             .ant-tag {
      //               font-family: PingFangSC-Regular;
      //               font-size: 13px;
      //               color: #27272a;
      //               font-weight: 400;
      //             }
      //           }
      //         }
      //       }
      //       .ant-table-thead {
      //         tr {
      //           > th {
      //             padding: 12px 24px;
      //             height: 48px;
      //             font-family: PingFangSC-Regular;
      //             font-size: 13px;
      //             color: #a1a1aa;
      //             font-weight: 400;
      //             &::after {
      //               top: 14px;
      //             }
      //           }
      //         }
      //       }
      //     }
      //   }
      // }
    }
  }
}
