<template>
  <div class="page-sqlresolver-content">
    <!-- <SplitPanel :min-size="230" :max-size="400" :size="250"> -->
    <SplitPanel :min-size="230" :max-size="600" :size="300">
      <!-- 左侧 -->
      <div class="psc-left-wrapper" slot="first">
        <!-- <SplitPanel split-to="rows" primary="second" :min-size="100" :max-size="400" :size="200"> -->
        <SplitPanel split-to="rows" primary="second" :min-size="0" :max-size="400" :size="0">
          <div class="psc-left-source-wrapper" slot="first">
            <Source />
          </div>
          <div class="psc-left-source-detail-wrapper" slot="second">
            <SourceDetail />
          </div>
        </SplitPanel>
      </div>
      <!-- 右侧 -->
      <div class="psc-right-wrapper" slot="second">
        <Query />
      </div>
    </SplitPanel>
  </div>
</template>

<script>
import SplitPanel from '@/components/SplitPanel';
import Source from '../Source';
import SourceDetail from '../SourceDetail';
import Query from '../Query';
import QueryDetail from '../QueryDetail';

export default {
  components: { SplitPanel, Source, SourceDetail, Query, QueryDetail },
  props: {
    instanceItem: Object
  },
  provide() {
    return {
      instanceItem: this.instanceItem
    };
  },
  data() {
    return {};
  },
  computed: {},
  mounted() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.page-sqlresolver-content {
  height: 100%;
  position: relative;

  /deep/ .custom-split-panel-new > .resize-line > .tip > .line {
    background-color: #d9d9d9 !important;
  }
  .psc-left-wrapper {
    height: 100%;
    .psc-left-source-wrapper {
      height: 100%;
    }
    .psc-left-source-detail-wrapper {
      height: 100%;
    }

    /deep/ .Resizer {
      pointer-events: none;
    }
  }
  .psc-right-wrapper {
    height: 100%;
  }
}
</style>
