<template>
  <div class="order-detail">
    <div class="project-info">
      <span class="circle"></span>
      <LimitLabel
        :label="appName || '--'"
        :limit="20"
        class="title-project-name"
      ></LimitLabel>
      <custom-icon type="lu-icon-metadata" />
    </div>
    <!-- </div> -->
    <div class="frame-button-wrapper">
      <a-button @click="toBack" class="highlight">返回</a-button>
      <a-button
        class="review-detail-btn highlight"
        @click="aKeyPass"
        v-if="['评审中', '待评审'].includes(dbaStatus)"
        >一键通过</a-button
      >
      <a-button icon="download" @click="download" class="highlight"
        >导出</a-button
      >
    </div>
    <!-- 上面基础信息区域 -->
    <a-card style="width: 100%" class="common-page-card" :bordered="false">
      <div class="review-detail-info">
        <div class="review-detail-main-info">
          <div class="left-block">
            <span
              class="left-avatar-box"
              :style="{ background: avatarBkg[topFormData.analy_status] }"
            >
              <custom-icon :type="avatarIcon[topFormData.analy_status]" />
            </span>

            <div class="left-info-side-box">
              <div class="info-box">
                <span class="project-name">{{ appName || '--' }}</span>
                <span class="review-point"
                  >Tag: {{ topFormData.review_point || '--' }}</span
                >
                <span
                  class="project-group"
                  v-if="projectGroup && projectGroup.length > 0"
                >
                  <span>项目组：</span>
                  <span v-if="projectGroup.length == 1">{{
                    projectGroup[0]
                  }}</span>
                  <a-tooltip v-else>
                    <template slot="title">
                      <span>{{ projectGroup.toString() }}</span>
                    </template>
                    <span>{{ projectGroup[0] + '; ' + '...' }}</span>
                  </a-tooltip>
                </span>
                <span class="no-wrap">
                  <span class="status">
                    <span class="review-name">项目审核</span>
                    <StatusTag
                      type="review"
                      :status="topFormData.analy_status"
                    ></StatusTag>
                  </span>
                  <span class="status">
                    <span class="review-name">DBA审核</span>
                    <StatusTag type="dba" :status="topFormData.dba_status" />
                  </span>
                </span>
              </div>

              <div class="side-info-box">
                <span class="created-by">
                  <a-tooltip>
                    <template slot="title">
                      <span>{{ topFormData.create_by }}</span>
                    </template>
                    <span style="color: #71717a">{{
                      topFormData.ch_name
                    }}</span>
                  </a-tooltip>
                  <span style="color: #a1a1a1"
                    >于{{ topFormData.creat_at }} 发起审核</span
                  >
                </span>
                <span
                  style="color: #71717a"
                  class="no-wrap"
                  v-if="topFormData.ai_review_total_time"
                  >审核耗时
                  {{ topFormData.ai_review_total_time + '分钟' }}</span
                >
              </div>
            </div>
          </div>

          <div class="right-block no-wrap">
            <div class="right-block-content">
              <div>
                <span>SQL语句</span>
                <span>{{ reviewInfo.all_count || '--' }}</span>
              </div>
              <a-divider type="vertical" />
              <div class="hover-operation-area">
                <div class="hover-switch">
                  <span>AI审核通过</span>
                  <span class="pass" v-if="reviewInfo.all_count"
                    >{{
                      (
                        (reviewInfo.ai_pass_count / reviewInfo.all_count) *
                        100
                      ).toFixed(2)
                    }}%</span
                  >
                  <span class="pass" v-else>{{ '--' }}</span>
                </div>
                <div class="hover-detail">
                  <span>通过条数</span>
                  <span>
                    <span class="pass">{{ reviewInfo.ai_pass_count }}</span>
                  </span>
                </div>
              </div>
              <a-divider type="vertical" />
              <div class="hover-operation-area">
                <div class="hover-switch">
                  <span>审核异常</span>
                  <span class="error" v-if="reviewInfo.all_count"
                    >{{
                      (
                        (reviewInfo.error_count / reviewInfo.all_count) *
                        100
                      ).toFixed(2)
                    }}%</span
                  >
                  <span class="error" v-else>{{ '--' }}</span>
                </div>
                <div class="hover-detail">
                  <span>异常条数</span>
                  <span class="error">{{ reviewInfo.error_count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>
    <!-- 下面表格区域 -->
    <a-card style="width: 100%" class="common-page-card" :bordered="false">
      <a-row
        class="review-false"
        v-if="
          topFormData.analy_status == 'Review失败' && topFormData.error_message
        "
      >
        <div class="picture">
          <img src="@/assets/img/private/error.svg" alt />
        </div>
        <div class="title">Review失败</div>
        <div class="error-message">{{ topFormData.error_message }}</div>
        <a-button class="highlight re-review-btn" @click="reReview('task')"
          >重新review</a-button
        >
      </a-row>
      <a-row class="review-detail-table-block" v-else>
        <!-- 触发规则列表 -->
        <div
          class="risk"
          v-if="
            errorOptions.length > 0 ||
            lowRiskOptions.length > 0 ||
            highRiskOptions.length > 0
          "
        >
          <div class="main-title">风险提示</div>
          <div class="scroll-box">
            <div v-if="highRiskOptions.length > 0">
              <div class="risk-header">
                <div class="title">
                  <custom-icon type="lu-icon-alarm" class="high" />高风险
                </div>
                <div>
                  <a-checkbox
                    :indeterminate="highIndeterminate"
                    :checked="highCheckAll"
                    @change="onCheckAllHigh"
                  ></a-checkbox>
                </div>
              </div>
              <a-spin :spinning="spinning">
                <custom-empty v-if="highRiskOptions.length <= 0" />
                <a-checkbox-group
                  :class="[
                    'custom-checkbox-group',
                    highRiskOptions.length > 0 ? 'has-exception' : 'rule'
                  ]"
                  v-model="highRiskCheckedList"
                  v-else
                >
                  <template v-for="item in highRiskOptions || []">
                    <a-checkbox
                      :key="item.value"
                      v-bind="item || {}"
                      @change="onClickHighRisk"
                    >
                      <div class="ccg-icon-label">
                        <custom-icon class="ccg-icon" type="lu-icon-right1" />
                        <span>{{ item.label }}</span>
                      </div>
                      <span class="ccg-num">
                        <span>{{ item.num }}</span>
                      </span>
                    </a-checkbox>
                  </template>
                </a-checkbox-group>
              </a-spin>
            </div>
            <div
              class="divider-line"
              v-if="highRiskOptions.length > 0 && lowRiskOptions.length > 0"
            ></div>
            <div v-if="lowRiskOptions.length > 0">
              <div class="risk-header">
                <div class="title">
                  <custom-icon type="lu-icon-alarm" class="low" />低风险
                </div>
                <div>
                  <a-checkbox
                    :indeterminate="lowIndeterminate"
                    :checked="lowCheckAll"
                    @change="onCheckAllLow"
                  ></a-checkbox>
                </div>
              </div>

              <a-spin :spinning="spinning">
                <custom-empty v-if="lowRiskOptions.length <= 0" />
                <a-checkbox-group
                  :class="[
                    'custom-checkbox-group',
                    lowRiskOptions.length > 0 ? 'has-exception' : 'rule'
                  ]"
                  v-model="lowRiskCheckedList"
                  v-else
                >
                  <template v-for="item in lowRiskOptions || []">
                    <a-checkbox
                      :key="item.value"
                      v-bind="item || {}"
                      @change="onClickLowRisk"
                    >
                      <div class="ccg-icon-label">
                        <custom-icon class="ccg-icon" type="lu-icon-right1" />
                        <span>{{ item.label }}</span>
                      </div>
                      <span class="ccg-num">
                        <span>{{ item.num }}</span>
                      </span>
                    </a-checkbox>
                  </template>
                </a-checkbox-group>
              </a-spin>
            </div>
            <!-- 无风险 -->
            <div
              class="divider-line"
              v-if="
                noRiskOptions.length > 0 &&
                (lowRiskOptions.length > 0 || highRiskOptions.length > 0)
              "
            ></div>
            <div v-if="noRiskOptions.length > 0">
              <div class="risk-header">
                <div class="title">
                  <custom-icon type="lu-icon-success" class="low" />无风险
                </div>
                <div>
                  <a-checkbox
                    :indeterminate="indeterminate"
                    :checked="checkAll"
                    @change="onCheckAll"
                  ></a-checkbox>
                </div>
              </div>

              <a-spin :spinning="spinning">
                <custom-empty v-if="noRiskOptions.length <= 0" />
                <a-checkbox-group
                  :class="[
                    'custom-checkbox-group',
                    noRiskOptions.length > 0 ? 'has-exception' : 'rule'
                  ]"
                  v-model="noRiskCheckedList"
                  v-else
                >
                  <template v-for="item in noRiskOptions || []">
                    <a-checkbox
                      :key="item.value"
                      v-bind="item || {}"
                      @change="onClickNoRisk"
                    >
                      <div class="ccg-icon-label">
                        <custom-icon class="ccg-icon" type="lu-icon-right1" />
                        <span>{{ item.label }}</span>
                      </div>
                      <span class="ccg-num">
                        <span>{{ item.num }}</span>
                      </span>
                    </a-checkbox>
                  </template>
                </a-checkbox-group>
              </a-spin>
            </div>
            <div
              class="divider-line"
              v-if="
                errorOptions.length > 0 &&
                (lowRiskOptions.length > 0 ||
                  highRiskOptions.length > 0 ||
                  noRiskOptions.length > 0)
              "
            ></div>
            <div v-if="errorOptions.length > 0">
              <div class="risk-header">
                <div class="title">
                  <custom-icon type="lu-icon-unusual" class="error" />审核异常
                </div>
                <div>
                  <a-checkbox
                    :indeterminate="errorIndeterminate"
                    :checked="errorCheckAll"
                    @change="onCheckAllError"
                  ></a-checkbox>
                </div>
              </div>

              <a-spin :spinning="spinning">
                <custom-empty v-if="errorOptions.length <= 0" />
                <a-checkbox-group
                  :class="[
                    'custom-checkbox-group',
                    errorOptions.length > 0 ? 'has-rule' : 'exception'
                  ]"
                  v-model="errorCheckedList"
                  v-else
                >
                  <template v-for="item in errorOptions || []">
                    <a-checkbox
                      :key="item.value"
                      v-bind="item || {}"
                      @change="onClickError"
                    >
                      <div class="ccg-icon-label">
                        <custom-icon class="ccg-icon" type="lu-icon-right1" />
                        <span>{{ item.label }}</span>
                      </div>
                      <span class="ccg-num">
                        <span>{{ item.num }}</span>
                      </span>
                    </a-checkbox>
                  </template>
                </a-checkbox-group>
              </a-spin>
            </div>
          </div>
        </div>
        <div
          class="divider-line"
          v-if="
            errorOptions.length > 0 ||
            lowRiskOptions.length > 0 ||
            highRiskOptions.length > 0
          "
        ></div>
        <!-- 审核SQL列表 -->
        <div
          :class="[
            'review-sql-table',
            errorOptions.length <= 0 &&
            lowRiskOptions.length <= 0 &&
            highRiskOptions.length <= 0
              ? 'no-risk'
              : ''
          ]"
        >
          <Table
            ref="table"
            v-bind="tableParams || {}"
            @reset="reset"
            class="new-card-table"
            @selectChange="selectChange"
            :dataSource="dataSource"
            :rowSelection="rowSelection"
          >
            <!-- 表格头上插槽 -->
            <template slot="tableTopLeft">
              <div>
                <a-radio-group v-model="value" @change="onChange">
                  <a-radio-button value="sql-text"> SQL文本 </a-radio-button>
                  <a-radio-button value="sql-source"> SQL代码 </a-radio-button>
                </a-radio-group>
              </div>
              <div class="review-search">
                <InputSearch
                  :placeholder="placeholder"
                  @search="tableSearch"
                  ref="inputSearch"
                ></InputSearch>
              </div>
            </template>

            <template slot="tableTopRight">
              <div class="review-box" v-if="false">
                <a-popconfirm
                  title="重新review会对SQL重新进行AI风险等级判定，是否确认?"
                  @confirm="() => reReview()"
                >
                  <a-tooltip placement="top">
                    <template slot="title">
                      <span>重新review</span>
                    </template>
                    <custom-icon type="lu-icon-re-review" class="review-btn" />
                  </a-tooltip>
                </a-popconfirm>
              </div>
              <a-tooltip placement="top">
                <template slot="title">
                  <span>打标</span>
                </template>
                <div class="batch-tag-box">
                  <custom-icon
                    type="tags"
                    class="batch-tag-btn"
                    @click="batchAddTag"
                  />
                </div>
              </a-tooltip>
              <a-divider type="vertical" />
            </template>

            <template slot="tableMiddle">
              <div
                class="des"
                v-if="errorOptions.length > 0 || highRiskOptions.length > 0"
              >
                <custom-icon type="exclamation-circle" />
                <span>存在高风险SQL，请评估整改，或为SQL添加标签和备注。</span>
              </div>
              <div
                class="des"
                v-if="fileError && fileError.error_count > 0"
                style="padding: 8px 0"
              >
                <custom-icon type="exclamation-circle" />
                <span
                  >{{ fileError.error + '。' }} (<span @click="onOpen"
                    >查看详情</span
                  >)</span
                >
              </div>
            </template>

            <!-- table插槽 -->
            <template slot="sql_text" slot-scope="{ record, text }">
              <a class="sql-text-box" @click="toDetail(text, record, $event)">
                <custom-icon
                  class="sql-text-icon"
                  :type="iconMap[record.sql_format]"
                  mode="svg"
                />
                <LimitLabel
                  :label="text || ''"
                  mode="ellipsis"
                  format="sql"
                  :needCopy="true"
                  :ignoreFirstLineComment="true"
                  :popoverProps="{
                    overlayClassName: 'home-sqlreview-small-size'
                  }"
                ></LimitLabel>
                <LabelCard
                  v-if="record.label_obj_id"
                  ref="labelCard"
                  mode="icon"
                  :id="record.label_obj_id"
                  :labelStatus="record.label_status"
                  @refresh="refresh"
                />
                <!-- <span v-else>{{ '--' }}</span> -->
                <!-- <a-popover
                  overlayClassName="order-label-type"
                  v-if="record.label_type"
                >
                  <template slot="content">
                    <a
                      :class="[
                        'text',
                        record.label_info.audit_status == -1
                          ? 'audit-status-red'
                          : ''
                      ]"
                    >
                      <span>{{
                        record.label_type == 1 ? '白名单' : '整改中'
                      }}</span>
                    </a>
                    <div class="label-type-content">
                      <div>
                        <span>申请人：</span>
                        <span>{{ record.label_info.created_by }}</span>
                      </div>
                      <div>
                        <span>申请时间：</span>
                        <span>{{ record.label_info.created_at }}</span>
                      </div>
                      <div>
                        <span>申请版本：</span>
                        <span>{{ record.label_info.tag || '--' }}</span>
                      </div>
                      <div>
                        <span>状态：</span>
                        <span
                          class="status-0"
                          v-if="record.label_info.audit_status == 0"
                          >待审核</span
                        >
                        <span
                          class="status-1"
                          v-else-if="record.label_info.audit_status == 1"
                          >审核通过</span
                        >
                        <span
                          class="status-2"
                          v-else-if="record.label_info.audit_status == -1"
                          >审核不通过</span
                        >
                      </div>
                    </div>
                  </template>
                  <custom-icon
                    type="tags"
                    class="label-icon"
                    :style="{
                      color: labelColor[record.label_info.audit_status]
                    }"
                  />
                </a-popover> -->
              </a>
            </template>
            <!-- 风险等级 -->
            <template slot="risk" slot-scope="{ record, text }">
              <RuleLabel
                :aiComment="record.ai_comment || {}"
                :value="text"
                v-if="record.ai_status !== 0"
              />
              <custom-icon type="lu-icon-loading" class="loading" v-else />
            </template>
            <div slot="review_status" slot-scope="{ text, record }">
              <custom-icon
                v-if="record.ai_status !== 0"
                :class="text == 1 ? 'review-pass' : 'review-no-pass'"
                :type="text == 1 ? 'lu-icon-success' : 'lu-icon-disable'"
              />
              <span
                style="color: #a1a1aa; fontsize: 12px; marginleft: 4px"
                v-if="record.ai_status !== 0"
                >{{ record.review_desc }}</span
              >
              <custom-icon
                v-if="record.ai_status == 0"
                type="lu-icon-loading"
                class="loading"
              />
            </div>
          </Table>
        </div>
      </a-row>
      <!-- 审核弹窗 -->
      <Audit ref="audit" @refresh="refresh"></Audit>
      <!-- 打标弹窗 -->
      <TagModal ref="tag" @saveLabel="onSaveLabel"></TagModal>
      <!-- SQL备注弹窗 -->
      <NoteModal ref="note" @saveNote="onSaveNote"></NoteModal>
    </a-card>
    <Report ref="Report"></Report>
    <!-- 一键通过弹窗 -->
    <GetPassModal ref="getPass" @refresh="refresh"></GetPassModal>
    <!-- 错误信息查看详情弹窗 -->
    <DetailModal ref="detailModal"></DetailModal>
  </div>
</template>

<script>
import {
  exportOrderReviewDetail,
  batchUpdateDetail,
  getUpdateProgress,
  reviewRetry,
  beforeReviewDetailPage,
  saveLabel,
  deleteLabel,
  saveSqlMap
} from '@/api/home';
import Table from '@/components/Table';
import LimitTags from '@/components/LimitTags';
import Form from '@/components/Form';
import Status from '@/components/Biz/Status';
import LimitLabel from '@/components/LimitLabel';
import RuleLabel from './components/ruleLabel';
import CheckboxGroup from '@/components/CheckboxGroup';
import InputSearch from '@/components/InputSearch';
import StatusTag from '@/components/Biz/Status/Tag';
import config from './config';
import common from '@/utils/common';
import GetPassModal from '@/pages/order/index/components/GetPassModal';
import MarkdownViewer from '@/components/Markdown/viewer';
import TagModal from '@/components/Biz/ReviewDetail/TagModal';
import NoteModal from '@/components/Biz/ReviewDetail/NoteModal';
import Audit from '@/components/Biz/AuditModel';
import Button from '@/components/Biz/Button';
import Report from '../index/components/Report';
import LabelCard from '@/components/Biz/LabelCard';
import DetailModal from '@/components/Biz/ReviewDetail/DetailModal';

import tableSelectAll from '@/mixins/tableSelectAll';

const getCheckboxProps = record => ({
  // 选择框的默认属性配置
  props: {
    disabled: record.label_status == 1
  }
});
export default {
  components: {
    Table,
    Status,
    RuleLabel,
    LimitTags,
    LimitLabel,
    CheckboxGroup,
    Form,
    InputSearch,
    StatusTag,
    GetPassModal,
    Report,
    MarkdownViewer,
    TagModal,
    NoteModal,
    Audit,
    Button,
    LabelCard,
    DetailModal
  },
  mixins: [tableSelectAll({ getCheckboxProps })],
  props: {},
  data() {
    this.config = config(this);
    this.id = this.$route.params.id;
    this.searchData = {};
    return {
      btnsData: {
        dba_status: this.$route.query.status
      },
      tableParams: {
        url: '/sqlreview/review/review-detail-list/',
        method: 'post',
        reqParams: {
          review_id: this.id
        },
        columns: this.config.columns.filter(item => item.key !== 'source_text'),
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 700 },
        pagination: {},
        ignoreFastReqTips: true,
        extraTools: [
          {
            key: 'pie-chart',
            tips: '查看报表',
            icon: 'pie-chart',
            callback: this.checkReport
          }
        ],
        // rowSelection: {
        //   type: 'checkbox',
        //   columnWidth: '40',
        //   getCheckboxProps: record => ({
        //     // 选择框的默认属性配置
        //     props: {
        //       disabled: record.label_info.audit_status == 1
        //     }
        //   })
        // },
        loaded: this.onTableLoaded
      },
      // selectedRowKeys: [],
      // dataSource: [],
      formData: {},
      appName: '',
      fileList: [],
      detail: 'homedetail',
      topFormData: {}, // 上方表单数据
      spinning: false,
      placeholder: '搜索SQL/ID',
      reviewInfo: {},
      analyStatus: null,
      noRiskOptions: [],
      noRiskCheckedList: [],
      noRiskSelects: [],
      lowRiskOptions: [],
      lowRiskCheckedList: [],
      lowRiskSelects: [],
      highRiskOptions: [],
      highRiskCheckedList: [],
      highRiskSelects: [],
      errorOptions: [],
      errorCheckedList: [],
      errorSelects: [],
      riskSelects: [],
      avatarIcon: {
        队列中: 'lu-icon-unknown',
        通过: 'lu-icon-pass1',
        未通过: 'lu-icon-nopass',
        拉取代码中: 'lu-icon-unknown',
        代码解析中: 'lu-icon-unknown',
        代码审核中: 'lu-icon-unknown',
        Review失败: 'lu-icon-nopass'
      },
      avatarBkg: {
        队列中: '#F8C99C',
        通过: '#9CCBA5',
        未通过: '#F38E9B',
        拉取代码中: '#F8C99C',
        代码解析中: '#F8C99C',
        代码审核中: '#F8C99C',
        Review失败: '#F38E9B'
      },
      riskIcon: {
        high: 'lu-icon-alarm',
        low: 'lu-icon-alarm',
        null: 'lu-icon-success',
        error: 'lu-icon-unusual'
      },
      iconMap: {
        xml: 'lu-icon-xml',
        sql: 'lu-icon-sql',
        java: 'lu-icon-java'
      },
      labelColor: {
        0: '#F29339',
        1: '#3A974C',
        '-1': '#EF6173'
      },
      dba_status: this.$route.query.status,
      indeterminate: false,
      checkAll: false,
      highIndeterminate: false,
      highCheckAll: false,
      lowIndeterminate: false,
      lowCheckAll: false,
      errorIndeterminate: false,
      errorCheckAll: false,
      isSubmitFlag: false,
      percent: 0,
      projectGroup: [],
      value: 'sql-text',
      // count: null, // 表格总数
      // includes: [], // 表格单选所有值
      // excludes: [], // 表格全选后排除的值
      // pageKeys: [], // 表格当前页所有key
      // tableCheckAll: false, // 表格全选状态true/false
      // tableIndeterminate: false, // 表格半选状态true/false
      // isSelectAll: false,
      // timestamp: null,
      commentContent: '',
      dbaStatus: null,
      fileError: {}
    };
  },
  computed: {
    // rowSelection() {
    //   return {
    //     type: 'checkbox',
    //     columnWidth: '40',
    //     columnTitle: (
    //       <a-checkbox
    //         indeterminate={this.tableIndeterminate}
    //         checked={this.tableCheckAll}
    //         onChange={this.onCheckAllChange}
    //       />
    //     ),
    //     getCheckboxProps: record => ({
    //       // 选择框的默认属性配置
    //       props: {}
    //     }),
    //     selectedRowKeys: this.selectedRowKeys,
    //     onSelect: this.onSelect
    //   };
    // }
  },
  mounted() {
    this.getbeforeReviewDetailData();
  },
  created() {},
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },
  methods: {
    // 提交评审
    authSubmit() {
      this.$refs.audit.show(this.id);
    },
    // onTableLoaded(req, res) {
    //   const resData = _.get(res, 'data.data');
    //   const results = resData.results;
    //   const ids = results.map(item => item.id);
    //   this.pageKeys = ids;
    //   this.percent = resData.label_rate;
    //   this.dataSource = results;
    //   this.count = resData.count;
    //   this.timestamp = resData.timestamp;
    //   if (this.isSelectAll) {
    //     ids.forEach(item => {
    //       if (!this.excludes.includes(item)) {
    //         this.selectedRowKeys.push(item);
    //       }
    //     });
    //   }
    // },
    onChange(e) {
      this.value = e.target.value;
      const columns =
        this.value == 'sql-text'
          ? this.config.columns.filter(item => item.key !== 'source_text')
          : this.config.columns.filter(item => item.key !== 'sql_text');
      this.$set(this.tableParams, 'columns', columns);
    },
    toDetail(text, record, e) {
      const searchData = this.$refs.table.searchParams;
      this.$router.push({
        name: 'orderReview',
        params: { id: record.id, searchData: searchData },
        query: { activeKey: 'sqlreview' }
      });
    },
    // 返回
    toBack() {
      this.$router.push({
        name: 'orderList',
        query: { activeKey: 'sqlreview' }
      });
    },
    download() {
      this.$showLoading({
        tips: `下载中...`
      });
      const searchData = {
        ...this.$refs.table.searchParams
      };
      exportOrderReviewDetail({
        sqlId: this.id,
        ...searchData
      })
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 一键通过
    aKeyPass() {
      this.$refs.getPass.show(this.id);
    },
    beforeUpload(file) {
      if (
        file &&
        ['.xls', '.xlsx'].filter(item => file.name.endsWith(item)).length <= 0
      ) {
        this.$message.error('请选择正确格式！');
      } else {
        this.batchUpdate(file);
      }

      return false;
    },
    batchUpdate(file) {
      this.$showLoading({
        tips: `上传中...`
      });
      let formData = new FormData();
      formData.append('file', file);
      formData.append('task_name', 'review_detail_from_excel');
      batchUpdateDetail(formData)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const data = _.get(res, 'data.data') || {};
            if (data.task_id != null) {
              this.updateProgress(data, '批量更新');
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    updateProgress(params, action) {
      this.$showLoading({
        tips: `正在${action}...`
      });
      getUpdateProgress(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const data = _.get(res, 'data.data') || {};
            const { task_status: taskStatus } = data;
            if (taskStatus == 2) {
              this.$hideLoading({ tips: `${action}成功` });
              this.$refs.table.refresh(null, this.searchData);
            } else if (taskStatus == 4) {
              this.$hideLoading({
                method: 'error',
                tips: `${action}失败`
              });
            } else {
              this.timer = setTimeout(() => {
                this.updateProgress(params, action);
              }, 5000);
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    reReview(type) {
      if (type !== 'task' && _.isEmpty(this.selectedRowKeys)) {
        this.$message.warning('未选择重新review数据！');
        return;
      }
      const table = this.$refs.table;
      const searchParams = table ? table.searchParams : {};
      const params =
        data == 'task'
          ? { review_id: this.id }
          : {
              review_id: this.id,
              retry_list: this.isSelectAll ? this.excludes : this.includes,
              re_audit_list: data,
              is_select_all: this.isSelectAll ? 1 : 0,
              timestamp: this.timestamp,
              ...searchParams
            };
      reviewRetry(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            if (type == 'task') {
              this.getbeforeReviewDetailData();
            }
            this.reset();
            this.$refs.table && this.$refs.table.refreshClear();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      this.searchData = { ...data };
      if (this.searchData.status && this.searchData.status.length > 0) {
        this.searchData.status = this.searchData.status.join(',');
      }
      table.refresh(null, this.searchData);
    },
    // 重置
    reset(data) {
      const inputSearch = this.$refs.inputSearch;
      inputSearch && (inputSearch.searchVal = '');
      this.isSelectAll = false;
      this.tableCheckAll = false;
      this.tableIndeterminate = false;
      this.selectedRowKeys = [];
      this.count = null;
      this.includes = [];
      this.excludes = [];
      this.pageKeys = [];
    },
    refresh() {
      const { table } = this.$refs;
      table && table.refreshKeep();
      this.reset();
      this.getbeforeReviewDetailData();
    },
    // 查看报表
    checkReport() {
      const data = { id: this.id };
      this.$refs.Report.show(data);
    },
    // 打开详情弹窗
    onOpen() {
      this.$refs.detailModal.show(this.id);
    },
    // 获取事前审核页面数据
    getbeforeReviewDetailData() {
      const obj = { review_id: this.id };
      beforeReviewDetailPage(obj)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            let obj = res.data.data.header;
            this.projectGroup = obj.project_group;
            this.dbaStatus = obj.dba_status;
            let formData = _.get(res, 'data.data');
            this.reviewInfo = _.get(res, 'data.data.review_info');
            this.isSubmitFlag = _.get(res, 'data.data.is_submit_flag');
            this.fileError = _.get(res, 'data.data.file_error');
            const sqlSourceList = [];
            const devIdList = [];
            formData.sql_source_list.map(item => {
              sqlSourceList.push({ label: item, value: item });
            });
            formData.dev_id_list.map(item => {
              devIdList.push({ label: item, value: item });
            });
            this.appName = formData.project_name;
            const resFormData = {
              url: formData.url,
              dev_id_list: devIdList,
              sql_source_list: sqlSourceList,
              project_name: formData.project_name,
              is_svn: formData.is_svn
            };
            this.$set(this, 'formData', resFormData);
            this.topFormData = this.statusToText(obj);

            let arr = _.get(res, 'data.data.header.rule_sort_dict');
            const noRiskArr = arr.no_risk;
            const lowRiskArr = arr.low_risk;
            const highRiskArr = arr.high_risk;
            const errorArr = arr.error;
            noRiskArr &&
              noRiskArr.forEach(item => {
                this.noRiskOptions.push({
                  label: item.name,
                  value: item.id,
                  num: item.rule_count
                });
              });
            lowRiskArr &&
              lowRiskArr.forEach(item => {
                this.lowRiskOptions.push({
                  label: item.name,
                  value: item.id,
                  num: item.rule_count
                });
              });

            highRiskArr &&
              highRiskArr.forEach(item => {
                this.highRiskOptions.push({
                  label: item.name,
                  value: item.id,
                  num: item.rule_count
                });
              });

            errorArr &&
              errorArr.forEach(item => {
                this.errorOptions.push({
                  label: item.name,
                  value: item.id,
                  num: item.rule_count
                });
              });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 分析状态数字转文字
    statusToText(obj) {
      for (let key in obj) {
        if (key === 'analy_status') {
          if (obj[key] === 0) {
            // obj[key] = '进行中';
            obj[key] = '队列中';
          } else if (obj[key] === 1) {
            obj[key] = '通过';
          } else if (obj[key] === -1 || obj[key] === 2) {
            obj[key] = '未通过';
          } else if (obj[key] === 3) {
            obj[key] = '拉取代码中';
          } else if (obj[key] === 4) {
            obj[key] = '代码解析中';
          } else if (obj[key] === 5) {
            obj[key] = '代码审核中';
          } else if (obj[key] === 9) {
            obj[key] = 'Review失败';
          }
        }
      }
      return obj;
    },
    tableSearch(params) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        sql_text: params
      });
      table.refresh();
    },
    onClickHighRisk(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.highRiskSelects.includes(value)) {
        this.highRiskSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.highRiskSelects = this.highRiskSelects.filter(item => {
          return item !== value;
        });
      }
      this.highRiskCheckedList = [...this.highRiskSelects];
      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      this.highIndeterminate =
        !!this.highRiskCheckedList.length &&
        this.highRiskCheckedList.length < this.highRiskOptions.length;
      this.highCheckAll =
        this.highRiskCheckedList.length === this.highRiskOptions.length;
      // 刷新表格
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        rule_id: this.riskSelects,
        _t: +new Date()
      });
      // downLoad导出接口参数是 table.searchParams
      this.$set(this.$refs.table.searchParams, 'rule_id', this.riskSelects);
    },
    onClickLowRisk(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.lowRiskSelects.includes(value)) {
        this.lowRiskSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.lowRiskSelects = this.lowRiskSelects.filter(item => {
          return item !== value;
        });
      }
      this.lowRiskCheckedList = [...this.lowRiskSelects];
      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      // 判定全选与否
      this.lowIndeterminate =
        !!this.lowRiskCheckedList.length &&
        this.lowRiskCheckedList.length < this.lowRiskOptions.length;
      this.lowCheckAll =
        this.lowRiskCheckedList.length === this.lowRiskOptions.length;

      // 刷新表格
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        rule_id: this.riskSelects,
        _t: +new Date()
      });
      // downLoad导出接口参数是 table.searchParams
      this.$set(this.$refs.table.searchParams, 'rule_id', this.riskSelects);
    },
    onClickNoRisk(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.noRiskSelects.includes(value)) {
        this.noRiskSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.noRiskSelects = this.noRiskSelects.filter(item => {
          return item !== value;
        });
      }
      this.noRiskCheckedList = [...this.noRiskSelects];
      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      // 判定全选与否
      this.indeterminate =
        !!this.noRiskCheckedList.length &&
        this.noRiskCheckedList.length < this.noRiskOptions.length;
      this.checkAll =
        this.noRiskCheckedList.length === this.noRiskOptions.length;

      // 刷新表格
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        rule_id: this.riskSelects,
        _t: +new Date()
      });
      // downLoad导出接口参数是 table.searchParams
      this.$set(this.$refs.table.searchParams, 'rule_id', this.riskSelects);
    },
    onClickError(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.errorSelects.includes(value)) {
        this.errorSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.errorSelects = this.errorSelects.filter(item => {
          return item !== value;
        });
      }
      this.errorCheckedList = [...this.errorSelects];
      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      // 判定全选与否
      this.errorIndeterminate =
        !!this.errorCheckedList.length &&
        this.errorCheckedList.length < this.errorOptions.length;
      this.errorCheckAll =
        this.errorCheckedList.length === this.errorOptions.length;
      // 刷新表格
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        rule_id: this.riskSelects,
        _t: +new Date()
      });
      // downLoad导出接口参数是 table.searchParams
      this.$set(this.$refs.table.searchParams, 'rule_id', this.riskSelects);
    },
    // 全选 反选
    onCheckAllHigh(e) {
      let checked = _.get(e, 'target.checked');

      const _options = this.highRiskOptions.map(item => item.value);
      this.highRiskSelects = checked ? [..._options] : [];

      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      Object.assign(this, {
        highRiskCheckedList: checked ? _options : [],
        highIndeterminate: false,
        highCheckAll: checked
      });
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        rule_id: checked ? [] : this.riskSelects,
        _t: +new Date()
      });
      this.$set(this.$refs.table.searchParams, 'rule_id', this.riskSelects);
    },
    // 全选 反选
    onCheckAllLow(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.lowRiskOptions.map(item => item.value);
      this.lowRiskSelects = checked ? [..._options] : [];

      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      Object.assign(this, {
        lowRiskCheckedList: checked ? _options : [],
        lowIndeterminate: false,
        lowCheckAll: checked
      });
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        rule_id: checked ? [] : this.riskSelects,
        _t: +new Date()
      });
      this.$set(this.$refs.table.searchParams, 'rule_id', this.riskSelects);
    },
    // 无风险 全选 反选
    onCheckAll(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.noRiskOptions.map(item => item.value);
      this.noRiskSelects = checked ? [..._options] : [];

      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      Object.assign(this, {
        noRiskCheckedList: checked ? _options : [],
        indeterminate: false,
        checkAll: checked
      });
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        rule_id: checked ? [] : this.riskSelects,
        _t: +new Date()
      });
      this.$set(this.$refs.table.searchParams, 'rule_id', this.riskSelects);
    },
    // 全选 反选
    onCheckAllError(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.errorOptions.map(item => item.value);
      this.errorSelects = checked ? [..._options] : [];

      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      Object.assign(this, {
        errorCheckedList: checked ? _options : [],
        errorIndeterminate: false,
        errorCheckAll: checked
      });
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        rule_id: checked ? [] : this.riskSelects,
        _t: +new Date()
      });
      this.$set(this.$refs.table.searchParams, 'rule_id', this.riskSelects);
    },
    // 打标
    addSqlTag(record) {
      this.$refs.tag.show(record);
    },
    // 打备注
    addNote(record) {
      this.$refs.note.show(record);
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    // 用户手动选择/取消选择某列的回调
    // onSelect(record, selected) {
    //   if (selected) {
    //     this.includes.push(record.id);
    //     this.excludes = this.excludes.filter(item => item !== record.id);

    //     this.selectedRowKeys.push(record.id);

    //     this.tableCheckAll = this.includes.length == this.count;
    //     this.tableIndeterminate = this.includes.length !== this.count;
    //   }
    //   if (!selected) {
    //     this.excludes.push(record.id);
    //     this.includes = this.includes.filter(item => item !== record.id);

    //     this.selectedRowKeys = this.selectedRowKeys.filter(
    //       item => item !== record.id
    //     );

    //     this.tableCheckAll = false;
    //     this.tableIndeterminate = this.excludes.length !== this.count;
    //   }
    //   if (this.isSelectAll && _.isEmpty(this.excludes)) {
    //     this.tableIndeterminate = false;
    //     this.tableCheckAll = true;
    //   }
    // },
    // 表格全选
    // onCheckAllChange(e) {
    //   const checked = _.get(e, 'target.checked');
    //   this.tableCheckAll = checked;
    //   this.isSelectAll = checked;
    //   this.tableIndeterminate = false;
    //   if (checked) {
    //     const arr = [...this.selectedRowKeys, ...this.pageKeys];
    //     this.selectedRowKeys = [...new Set(arr)];
    //     this.excludes = [];
    //   }
    //   if (!checked) {
    //     this.selectedRowKeys = [];
    //   }
    // },
    getData() {
      return this.selectedRowKeys;
    },
    // 保存打标
    onSaveLabel(data) {
      this.$showLoading();
      const { searchParams } = this.$refs.table;
      // const ids = !_.isEmpty(this.selectedRowKeys)
      //   ? this.selectedRowKeys
      //   : [data.id];
      const params = {
        label_attribute: data.label_attribute,
        permanent_day: data.permanent_day,
        is_select_all: this.isSelectAll ? 1 : 0,
        id: this.isSelectAll ? this.excludes : this.includes,
        count: this.count,
        timestamp: this.timestamp,
        review_id: this.$route.params.id,
        ...searchParams
      };
      saveLabel(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            if (!_.isEmpty(this.selectedRowKeys)) {
              this.$refs.table.refreshClear();
            } else {
              this.$refs.table.refreshKeep();
            }
            this.reset();
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 保存SQL备注
    onSaveNote(data) {
      this.$showLoading();
      saveSqlMap(data)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$refs.table.refreshKeep();
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    batchAddTag() {
      if (_.isEmpty(this.selectedRowKeys) && !this.isSelectAll) {
        this.$message.warning('未选择打标数据！');
        return;
      }
      this.$refs.tag.show();
    },
    // 删除打标
    onDeleteLabel(id) {
      this.$showLoading();
      deleteLabel({ id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$refs.table.refreshKeep();
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  },
  watch: {}
};
</script>

<style lang="less">
// popover美化
.ant-popover {
  &.order-detail-small-size {
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 400px;
        max-height: 200px;
      }
    }
  }
}
.ant-popover {
  &.order-label-type {
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 400px;
        max-height: 200px;
        .text {
          padding: 2px 16px;
          border-radius: 16px;
          border: 1px solid #e4e4e7;
          display: inline-block;
          &.audit-status-red {
            background: #e71d36;
            border: 1px solid #e71d36;
            > span {
              color: #fff;
            }
          }
          > span {
            font-size: 12px;
            color: #27272a;
          }
          .anticon {
            display: none;
            color: #fff;
            width: 16px;
            height: 16px;
            line-height: 18px;
            font-size: 12px;
            border-radius: 50%;
            margin-left: 4px;
            border: none;
          }
          &:hover {
            cursor: pointer;
            .anticon {
              display: inline-flex;
              background: #008adc;
              justify-content: center;
              align-items: center;
            }
          }
        }

        .label-type-content {
          > div {
            display: flex;
            justify-content: space-between;
            padding: 4px 10px;
            > span {
              font-size: 12px;
              font-weight: 400;
              &:first-child {
                color: #27272a;
              }
              &:last-child {
                color: #a1a1aa;
              }
              &.status-0 {
                color: #f29339;
              }
              &.status-1 {
                color: #4cbb3a;
              }
              &.status-2 {
                color: #e71d36;
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less" scoped>
/deep/.ant-table-pagination.ant-pagination {
  margin: 24px 0 0 0;
}
.form-box {
  .form-item {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #27272a;
    line-height: 22px;
    font-weight: 400;
    display: flex;
    margin-bottom: 4px;
    .name {
      width: 66px;
      display: flex;
      justify-content: flex-end;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
// .project-info {
//   display: flex;
//   align-items: center;
//   padding: 0 8px;
//   .circle {
//     display: inline-block;
//     width: 6px;
//     height: 6px;
//     border-radius: 50%;
//     background: @primary-color;
//   }
//   .title-project-name {
//     display: inline-block;
//     padding: 0 8px;
//     font-size: 12px;
//     line-height: 28px;
//   }
//   .custom-icon {
//     color: @primary-color;
//   }
// }
.order-detail {
  .project-info {
    .frame-fixed-top-left(15px, 168px);
    display: flex;
    align-items: center;
    padding: 0 8px;
    border: 1px solid #e4e4e7;
    border-radius: 24px;
    .circle {
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: @primary-color;
    }
    .title-project-name {
      display: inline-block;
      padding: 0 8px;
      font-size: 12px;
      line-height: 28px;
    }
    .custom-icon {
      color: @primary-color;
    }
  }
  // .page-header-content {
  //   .title-content {
  //     display: flex;
  //     align-items: center;
  //     .project-info {
  //       display: flex;
  //       align-items: center;
  //       padding: 0 8px;
  //       border: 1px solid #e4e4e7;
  //       border-radius: 24px;
  //       .circle {
  //         display: inline-block;
  //         width: 6px;
  //         height: 6px;
  //         border-radius: 50%;
  //         background: @primary-color;
  //       }
  //       .title-project-name {
  //         display: inline-block;
  //         padding: 0 8px;
  //         font-size: 12px;
  //         line-height: 28px;
  //       }
  //       .custom-icon {
  //         color: @primary-color;
  //       }
  //     }
  //   }
  // }
  .common-page-card {
    margin-bottom: 12px;
    border-radius: 8px;
    padding: 24px;
  }
  .review-detail-info {
    .review-detail-main-info {
      display: flex;
      justify-content: space-around;
      .left-block {
        width: 55%;
        display: flex;
        align-items: center;
        .left-avatar-box {
          margin-right: 12px;
          width: 68px;
          height: 68px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-shrink: 0;
          > .anticon {
            font-size: 48px;
            color: #fff;
          }
        }
        .left-info-side-box {
          .info-box {
            > span {
              display: inline-block;
              margin-bottom: 12px;
              margin-right: 16px;
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #a1a1aa;
              font-weight: 400;
            }
            .project-name {
              color: #27272a;
              font-weight: 600;
              margin-right: 4px;
            }
            .project-group,
            .review-point {
              background-color: rgba(228, 228, 231, 0.5);
              border-radius: 12px;
              padding: 2px 12px;
              color: #a1a1aa;
              font-size: 14px;
            }
            .ant-tag {
              border-radius: 12px;
            }
            .status {
              margin-right: 30px;
            }
          }
          .no-wrap {
            .review-name {
              margin-right: 4px;
            }
          }
        }
        .side-info-box {
          > span {
            height: 22px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #a1a1aa;
            line-height: 22px;
            font-weight: 400;
          }
          .created-by {
            margin-right: 12px;
          }
        }
      }
      .right-block {
        width: 45%;
        display: flex;
        justify-content: flex-end;
        margin-left: 32px;
        .right-block-content {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          > div {
            display: flex;
            flex-direction: column;
            text-align: center;
            height: 56px;
            & span {
              line-height: 22px;
              font-weight: 400;
              margin-bottom: 8px;
              font-family: PingFangSC-Regular;
              font-size: 14px;
            }
            & span:first-child {
              color: #71717a;
            }
            & span:last-child {
              font-size: 24px;
              color: #27272a;
            }
          }
          .hover-operation-area {
            display: block;
            text-align: center;
            height: 56px;
            overflow: hidden;
            cursor: default;
            &:hover {
              > div {
                transform: translateY(-56px);
              }
            }
            > div {
              display: flex;
              flex-direction: column;
              text-align: center;
              height: 56px;
              transition: transform 0.2s;
              .error {
                color: #e71d36 !important;
                font-size: 24px;
                font-weight: 400;
              }
              .pass {
                font-size: 24px;
                font-weight: 400;
                color: #3a974c !important;
              }
            }
          }
          .ant-divider-vertical {
            margin: 0 30px;
            width: 1px;
            height: 32px;
          }
        }
      }
    }
  }
  .review-false {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 130px;
    margin-bottom: 400px;
    .title {
      font-size: 24px;
      color: #27272a;
      font-weight: 400;
      margin: 20px 0 27px 0;
    }
    .error-message {
      background: #fdeff1;
      border-radius: 6px;
      font-weight: 400;
      font-size: 14px;
      padding: 8px 16px;
      color: #e71d36;
      text-align: center;
    }
    .re-review-btn {
      margin-top: 24px;
      color: #008adc;
      border-color: #7fc4ed;
      &:hover {
        color: #25a7e8;
        border-color: #008adc;
      }
    }
  }
  .review-detail-table-block {
    display: flex;
    .risk {
      width: 25%;
      .scroll-box {
        height: 660px;
        padding: 0 24px 0 0;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 4px;
          height: 4px;
        }
        &::-webkit-scrollbar-track {
          background: #fff;
        }
        &::-webkit-scrollbar-thumb {
          background: #fff;
        }
        &:hover::-webkit-scrollbar-track {
          background: #e8e8e8;
        }
        &:hover::-webkit-scrollbar-thumb {
          background: rgb(209, 209, 209);
        }
      }
      .divider-line {
        height: 1px;
        width: auto;
        background: #f4f5f7;
        margin: 10px 0 0 0;
      }
      .main-title {
        font-size: 16px;
        color: #27272a;
        font-weight: 600;
        margin-bottom: 6px;
      }
      .risk-header {
        display: flex;
        justify-content: space-between;
        padding: 16px 8px 16px 0;
        .title {
          font-family: PingFangSC-Semibold;
          font-size: 14px;
          color: #27272a;
          font-weight: 600;
          margin-left: 12px;
          .anticon {
            font-size: 16px;
            margin-right: 9px;
          }
          .high {
            color: #e71d36;
          }
          .low {
            color: #f29339;
          }
          .error {
            color: #71717a;
          }
        }
        .ant-checkbox-wrapper {
          width: 16px;
          height: 16px;
        }
      }

      .ant-table {
        .ant-table-thead {
          border: none;
          background: transparent;
        }
      }
      .custom-checkbox-group {
        display: block;
        overflow-y: auto;
        // &.rule {
        //   max-height: 600px;
        // }
        // &.has-exception {
        //   max-height: 380px;
        // }

        // &.exception {
        //   max-height: 600px;
        // }
        // &.has-rule {
        //   max-height: 200px;
        // }
        // &::-webkit-scrollbar {
        //   display: none;
        // }
        /deep/ .ant-checkbox-wrapper {
          display: block;
          margin: 0 0 5px 0;
          padding: 4px 8px 4px 0;
          font-size: 12px;
          border: none;
          .ant-checkbox {
            display: none;
          }
          .ant-checkbox + span {
            padding-right: 0;
            padding-left: 0;
          }
          > span:last-child {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .ccg-icon-label {
              display: flex;
              align-items: center;
              margin-left: 12px;
              .ccg-icon {
                margin-right: 8px;
                color: #e4e4e7;
                font-size: 12px;
                &.circle {
                  color: #e71d36;
                  margin-right: 0px;
                }
                &.bell {
                  color: #f29339;
                  margin-right: 0px;
                }
                &.robot {
                  color: #4db5f2;
                  margin-right: 10px;
                }
              }
              .limit-label {
                > pre {
                  color: #71717a;
                  font-size: 12px;
                }
              }
            }
            .ccg-num {
              text-align: right;
              color: #71717a;
              font-size: 12px;
              margin-left: 16px;
              white-space: nowrap;
              > span {
                display: inline-block;
                min-width: 16px;
                text-align: center;
              }
            }
          }
          &:hover {
            color: @primary-color;
            .ccg-icon-label {
              .ccg-icon {
                &.circle {
                  color: #e71d36;
                }
                &.bell {
                  color: #f29339;
                }
                &.robot {
                  color: #4db5f2;
                }
              }
            }
          }
          &.ant-checkbox-wrapper-checked {
            background: #ebf2ff;
            color: #008adc;
            border-radius: 8px;
            > span:last-child {
              .ccg-icon-label {
                .ccg-icon {
                  color: #008adc;
                  &.circle {
                    color: #e71d36;
                  }
                  &.bell {
                    color: #f29339;
                  }
                  &.robot {
                    color: #4db5f2;
                  }
                }
                .limit-label {
                  > pre {
                    color: #008adc;
                  }
                }
              }
              .ccg-num {
                color: #008adc;
              }
            }
          }
        }
      }
    }
    .divider-line {
      width: 1px;
      height: 690px;
      background: #e4e4e7;
    }
    .review-sql-table {
      width: 75%;
      padding-left: 24px;
      &.no-risk {
        width: 100%;
      }
      .title {
        display: flex;
        margin-bottom: 8px;
        align-items: center;
        > div {
          font-size: 16px;
          color: #27272a;
          font-weight: 600;
        }
        .table-middle-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 0 0 16px;
          > div {
            .anticon {
              font-size: 14px;
              color: #008adc;
            }
            > span {
              font-size: 14px;
              color: #27272a;
              font-weight: 400;
            }
            .ant-progress-inner {
              background: #e4e4e7;
            }
          }
        }
      }
      /deep/.custom-table {
        .search-area-wrapper {
          height: 32px;
          line-height: 32px;
          display: flex;
          justify-content: space-between;
          .custom-table-top-left {
            display: flex;
            justify-content: space-between;
            flex-grow: 1;
            .search-area-simple {
              display: none;
            }
            .review-search {
              display: flex;
              align-items: center;
              margin-right: 8px;
              .custom-input-search {
                background: #ffffff;
                .ant-input-affix-wrapper {
                  .ant-input {
                    width: 300px;
                    height: 30px;
                    background: #ffffff;
                    border: 1px solid rgba(161, 161, 161, 0.5);
                  }
                  .ant-input-suffix {
                    right: 8px;
                    color: rgba(24, 24, 27, 0.5);
                  }
                }
              }
            }
          }
          .custom-table-top-right {
            flex-grow: 0;
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
            .batch-tag-box {
              .batch-tag-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 26px;
                height: 26px;
                &:hover {
                  color: #fff;
                  background: #4ec3f5;
                  border-radius: 50%;
                }
              }
            }
            .review-box {
              margin-left: 8px;
              .review-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 26px;
                height: 26px;
                &:hover {
                  color: #fff;
                  background: #4ec3f5;
                  border-radius: 50%;
                  cursor: pointer;
                }
              }
            }
            .custom-table-tools {
              padding-bottom: 2px;
            }
          }
        }
        .des {
          padding: 16px 0 0 0;
          .anticon {
            font-size: 14px;
            color: #e71d36;
            margin-right: 4px;
          }
          > span {
            font-size: 14px;
            color: #27272a;
            font-weight: 400;
            > span {
              color: #008adc;
              cursor: pointer;
            }
          }
        }
        .ant-table-thead > tr > th .anticon-filter,
        .ant-table-thead > tr > th .ant-table-filter-icon {
          top: -3px;
        }
        .sql-text-box {
          display: flex;
          align-items: center;
          // max-width: 500px;
          .sql-text-icon {
            display: inline-block;
            margin-top: 4px;
            font-size: 16px;
            margin-right: 8px;
            opacity: 0.5;
          }
          .label-icon {
            margin-left: 8px;
          }
        }
        .limit-label {
          > pre {
            // color: #27272a;
            font-weight: 400;
            color: #008adc;
          }
        }
        .review-no-pass {
          font-size: 16px;
          color: #e71d36;
        }
        .review-pass {
          font-size: 16px;
          color: #3a974c;
        }
        @keyframes rotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
        .loading {
          margin-left: 8px;
          animation: rotate 2s linear infinite;
        }
      }
    }
  }
}

@media screen and (max-width: 1250px) {
  .order-detail {
    .review-detail-info {
      .review-detail-main-info {
        .right-block {
          .right-block-content {
            > div {
              margin-left: 16px;
              & span:first-child {
                font-size: 14px;
              }
              & span:last-child {
                font-size: 20px;
              }
              .error {
                font-size: 20px;
              }
              .pass {
                font-size: 20px;
              }
            }
            .ant-divider-vertical {
              margin-left: 16px;
            }
          }
        }
      }
    }
    // .review-detail-table-block {
    //   .review-sql-table {
    //     /deep/.custom-table {
    //       .sql-text-box {
    //         max-width: 150px;
    //       }
    //     }
    //   }
    // }
  }
}
// @media screen and (max-width: 1560px) {
//   .order-detail {
//     .review-detail-table-block {
//       .review-sql-table {
//         /deep/.custom-table {
//           .sql-text-box {
//             max-width: 250px;
//           }
//         }
//       }
//     }
//   }
// }
</style>
