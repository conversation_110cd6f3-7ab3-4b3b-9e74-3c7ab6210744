<template>
  <div class="resource-edit-wrapper">
    <a-card style="width:100%" class="common-page-card" title="新增/编辑资源" :bordered="false">
      <a-row class="resource-edit-content">
        <a-col :span="10">
          <a-spin :spinning="loading">
            <a-input-search style="margin-bottom: 8px" placeholder="Search" @change="onSearch" />
            <a-tree
              :treeData="treeData"
              :selectedKeys="selectedKeys"
              :autoExpandParent="autoExpandParent"
              :expandedKeys="expandedKeys"
              :show-line="true"
              show-icon
              @expand="onExpand"
              @select="selectNode"
            >
              <span slot="title" slot-scope="node">
                <!-- 文案 -->
                <span v-if="searchValue && node.source_name.indexOf(searchValue) > -1">
                  {{ node.source_name.substr(0, node.source_name.indexOf(searchValue)) }}
                  <span
                    style="color: #f50"
                  >{{ searchValue }}</span>
                  {{ node.source_name.substr(node.source_name.indexOf(searchValue) + searchValue.length) }}
                </span>
                <span :style="`${node.isAdd ? 'color:green;' : ''}`" v-else>{{node.source_name}}</span>

                <!-- 操作区 -->
                <span class="operate-area">
                  <a-popconfirm
                    title="确定删除?"
                    @confirm="() => removeNode(node)"
                    trigger="hover"
                    :destroyTooltipOnHide="true"
                    :getPopupContainer="(trigger) => trigger"
                    v-if="!node.isRoot && !isAdd"
                  >
                    <a-icon type="minus-circle" style="color:#ff7b7b;" @click.stop="() => { }" />
                  </a-popconfirm>
                  <a-icon
                    type="plus-circle"
                    style="color:#42a042;"
                    @click.stop="addNode(node)"
                    v-if="(typeRelation[node.source_type] || node.isRoot) && !isAdd"
                  />
                </span>
              </span>
              <!-- icons -->
              <template v-for="(icon, key) in iconMap">
                <a-icon
                  :key="key"
                  :type="icon.type"
                  :slot="key"
                  :style="`color: ${icon.color}`"
                  theme="filled"
                />
              </template>
            </a-tree>
          </a-spin>
        </a-col>
        <a-col class="part-right" :span="14">
          <template v-if="formParams.fields.length > 0">
            <div class="form-wrapper">
              <Form ref="form" v-bind="formParams" :formData="data"></Form>
            </div>
            <div class="part-right-btns">
              <a-button v-if="selectedNode.isAdd" @click="onCancel">取消</a-button>
              <a-button type="primary" @click="onSave">保存</a-button>
            </div>
          </template>
          <custom-empty :description="false" style="margin-top:20px;" v-else />
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script>
import _ from 'lodash';
import {
  resourceSave,
  getResourceData,
  resourceRemove,
  resourceUpdate
} from '@/api/resource';
import resources from '@/router/resources.js';
import Table from '@/components/Table';
import Form from '@/components/Form';
import config from './config';
// import authElements from '@/router/authElements.js';
// const { getAuthElementsByPage } = authElements;

export default {
  name: 'userList',
  props: {},
  components: { Table, Form },
  data() {
    this.config = config(this);
    const id = this.$route.params.id;
    let expandedKeys = ['root_resource', 'root_data'];
    id != null && expandedKeys.push(id);
    this.id = id;
    this.resources = resources;
    // 元素资源
    // this.getAuthElementsByPage = getAuthElementsByPage;

    return {
      // 左侧树
      treeData: [
        {
          key: 'root_resource',
          source_name: '资源权限',
          isRoot: true,
          selectable: false,
          scopedSlots: { title: 'title' },
          source_type: 'root_resource',
          source_category: 'resource',
          children: []
        }
        // {
        //   key: 'root_data',
        //   source_name: '数据权限',
        //   isRoot: true,
        //   selectable: false,
        //   scopedSlots: { title: 'title' },
        //   source_category: 'data',
        //   children: []
        // }
      ],
      nodeMap: {},
      selectedNode: {},
      selectedKeys: [],
      expandedKeys,
      typeRelation: this.config.typeRelation,
      isAdd: false,
      searchValue: '',
      autoExpandParent: true,
      iconMap: this.config.iconMap,
      loading: false,
      // 右侧表单
      formParams: {
        layout: 'vertical',
        fields: []
      },
      data: {},
      importVisible: false,
      sourceKeys: [
        { key: 'data', type: 'json' },
        { key: 'init_value', type: 'json' },
        { key: 'deal_type', type: 'string' }
      ]
    };
  },
  created() {
    this.initData();
  },
  computed: {},
  mounted() {},
  methods: {
    initData(res = {}) {
      // this.loading = true;
      getResourceData()
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.loading = false;
            // const { resource = [], data = [] } = _.get(res, 'data.data');
            const list = _.get(res, 'data.data.results') || [];
            const resource = list.filter(
              item => item.source_category === 'resource'
            );
            // const data = list.filter(item => item.source_category === 'data');
            let treeData = [...this.treeData];
            treeData[0].children = this.toTree(
              'resource',
              'root_resource',
              resource
            );
            // treeData[1].children = this.toTree('data', 'root_data', data);
            this.treeData = [...treeData];

            this.expandedKeys = [...this.expandedKeys];
            if (this.id != null) {
              this.selectedNode = list.find(item => item.id == this.id) || {};
              this.selectedKeys = [this.id];
              this.switchForm(this.selectedNode);
            }
          } else {
            this.loading = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    },
    toTree(category, rootKey, list = []) {
      let obj = {};
      let result = [];
      // 将数组中数据转为键值对结构 (这里的数组和obj会相互引用)
      list.map(el => {
        obj[el.id] = el;
        el.key = el.id;
        el.source_category = category;
        el.scopedSlots = { title: 'title' };
        el.slots = { icon: el.source_type };
        if (el.parent_id === 0) {
          el.parent_id = rootKey;
        }
      });
      for (let i = 0, len = list.length; i < len; i++) {
        let pid = list[i].parent_id;
        if ((pid + '').startsWith('root_')) {
          result.push(list[i]);
          result.sort((a, b) => a.sort_num - b.sort_num);
          continue;
        }
        if (obj[pid]) {
          if (obj[pid].children) {
            obj[pid].children.push(list[i]);
            if (list[i].source_type === 'element') {
              obj[pid].children.sort((a, b) =>
                (a.source_name || '').localeCompare(b.source_name || '')
              );
            } else {
              obj[pid].children.sort((a, b) => a.sort_num - b.sort_num);
            }
          } else {
            obj[pid].children = [list[i]];
          }
        }
      }
      return result;
    },
    getNodeMap() {
      if (!_.isEmpty(this.nodeMap)) {
        return this.nodeMap;
      }
      let map = {};
      const loop = (list = []) => {
        list.forEach(item => {
          map[item.key] = item;

          if (item.children && item.children.length > 0) {
            loop(item.children);
          }
        });
      };
      loop(this.treeData);
      this.nodeMap = map;
      return map;
    },
    switchForm(node) {
      const { form } = this.$refs;
      form && form.resetFields();
      // 设置结构
      this.formParams = Object.assign({}, this.formParams, {
        fields: this.config.getFields(node)
      });
      // 设置数据
      // let params = [];
      // _.forEach(node.source_ext || {}, (val, key) => {
      //   params.push({
      //     _key: key,
      //     _value: val
      //   });
      // });
      // 处理元数据source_base
      let base = {};
      this.sourceKeys.forEach((keyItem, index) => {
        const { key, type } = keyItem;
        const val = _.get(node.source_ext || {}, key);
        base[`source_${key}`] =
          type === 'json' && val != null ? JSON.stringify(val) : val;
      });
      // console.log(node, base)
      this.data = {
        id: node.id,
        source_name: node.source_name,
        source_type: node.source_type,
        role: node.role,
        sort_num: node.sort_num,
        source_ext: [],
        ...base
      };
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    selectNode(selectedKeys, e) {
      let node = e.node.dataRef;
      this.selectedKeys =
        selectedKeys.length <= 0 ? [...this.selectedKeys] : selectedKeys;
      this.selectedNode = node;

      this.switchForm(node);
    },
    addNode(node, e) {
      // console.log(node)
      this.isAdd = true;
      const key = _.uniqueId('add_');
      let newNode = {
        key,
        isAdd: true,
        parent_id: node.key,
        parentType: node.source_type,
        source_name: '--新增节点--',
        source_category: node.source_category,
        scopedSlots: { title: 'title' }
      };
      node.dataRef.children = [...(node.children || []), newNode];
      this.treeData = [...this.treeData];
      this.selectedKeys = [key];
      this.expandedKeys = [...this.expandedKeys, node.key];
      this.selectedNode = newNode;

      // 右侧表单
      this.switchForm(newNode);
    },
    removeNode(node) {
      // 请求
      this.$showLoading();
      resourceRemove({
        id: node.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: '删除成功' });

            let parentNode = this.getNodeMap()[node.parent_id];
            parentNode.children = parentNode.children.filter(
              item => item.key !== node.key
            );
            const dealNode = node => {
              if (node.key === this.selectedNode.key) {
                // 右侧表单
                this.formParams = Object.assign({}, this.formParams, {
                  fields: []
                });
                this.data = {};
              }
              delete this.getNodeMap()[node.key];
              if (node.children && node.children.length > 0) {
                node.children.forEach(item => {
                  dealNode(item);
                });
              }
            };
            dealNode(node);
            this.treeData = [...this.treeData];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    },
    onSearch(e) {
      const value = e.target.value;

      let expandedKeys = [];
      _.forEach(this.getNodeMap(), item => {
        if (item.source_name.includes(value)) {
          expandedKeys.push(item.key);
        }
      });
      this.expandedKeys = expandedKeys;
      this.searchValue = value;
      this.autoExpandParent = true;
    },
    onCancel() {
      this.$confirm({
        title: '确认取消?',
        content: '取消将会移除该新增节点',
        onOk: () => {
          let parentNode = this.getNodeMap()[this.selectedNode.parent_id];
          parentNode.children = parentNode.children.filter(
            item => item.key !== this.selectedNode.key
          );
          this.treeData = [...this.treeData];

          // 右侧表单
          this.formParams = Object.assign({}, this.formParams, {
            fields: []
          });
          this.data = {};
          this.isAdd = false;
        },
        onCancel() {}
      });
    },
    onSave() {
      const { form } = this.$refs;
      form.validate(valid => {
        if (!valid) {
          return;
        }
        let data = form.getData();
        let params = { ...data };
        let ext = {};
        if (data.source_type === 'element') {
          try {
            this.sourceKeys.forEach(keyItem => {
              const { key, type } = keyItem;
              const val = data[`source_${key}`];
              ext[key] =
                type === 'json' && val != null && val != ''
                  ? JSON.parse(val)
                  : val || null;
              delete params[`source_${key}`];
            });
          } catch (e) {
            console.log(e);
            this.$message.warning('格式有误，请检查');
            return;
          }
        }

        // (data.source_ext || []).forEach(item => {
        //   if (item._key !== undefined) {
        //     ext[item._key] = item._value;
        //   }
        // });
        params.source_ext = ext;
        params.sort_num = params.sort_num || 0;
        console.log(data, params, this.selectedNode);

        // 请求
        this.$showLoading();
        (this.isAdd ? resourceSave : resourceUpdate)({
          ...this.selectedNode,
          ...params
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ useMessage: true, tips: '保存成功' });
              // const data = _.get(res, 'data.data') || {};
              if (this.isAdd) {
                Object.assign(this.selectedNode, {
                  ...params,
                  key: params.id,
                  slots: { icon: params.source_type },
                  isAdd: false
                });
                this.$set(this.selectedNode, 'isAdd', false);

                // 设置结构
                this.formParams = Object.assign({}, this.formParams, {
                  fields: this.config.getFields(this.selectedNode)
                });
                this.isAdd = false;
                this.getNodeMap()[this.selectedNode.key] = this.selectedNode;
                this.selectedKeys = [params.id];
              } else {
                let currNode = this.getNodeMap()[this.selectedNode.key];
                Object.assign(currNode, params);
              }
              // 处理排序
              let parentNode = this.getNodeMap()[this.selectedNode.parent_id];
              if (params.source_type === 'element') {
                parentNode.children.sort((a, b) =>
                  (a.source_name || '').localeCompare(b.source_name || '')
                );
              } else {
                parentNode.children.sort((a, b) => a.sort_num - b.sort_num);
              }

              this.treeData = [...this.treeData];
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: '请求失败' });
          });
      });
    },
    exportData() {}
  }
};
</script>

<style lang="less" scoped>
.resource-edit-wrapper {
  display: flex;
  flex-grow: 1;
  .ant-card-extra {
    button {
      margin-left: 4px;
    }
  }

  /deep/ .ant-card {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    .ant-card-body {
      display: flex;
      flex-grow: 1;

      .resource-edit-content {
        display: flex;
        flex-grow: 1;
        align-content: stretch;

        > .ant-col {
          border-radius: 2px;
          border: 1px solid #eef2fb;
          box-shadow: 2px 2px 2px #f1f5ff;
          > ul,
          > div,
          > div .ant-spin-container {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            padding: 20px 10px;
          }
          .ant-spin-container {
            display: flex;
            flex-direction: column;
          }
        }
        // 左侧树
        .ant-tree {
          overflow: auto;
        }
        .ant-tree li {
          padding: 4px 10px;
          &::before {
            left: 22px;
          }
          .ant-tree-node-content-wrapper {
            &:hover {
              .operate-area {
                display: inline !important;
              }
            }
          }
          .ant-tree-title {
            > span {
              position: relative;
              padding-right: 48px;
              &::after {
                content: '';
                background: #ffffff;
                position: absolute;
                right: -5px;
                display: inline-block;
                width: 48px;
                height: 24px;
              }
              .operate-area {
                position: absolute;
                right: -5px;
                color: #bfbfbf;
                display: none;
                line-height: 24px;
                z-index: 1;
                width: 48px;
                padding-left: 8px;
              }

              .ant-popover {
                width: 150px;
              }
            }
          }
        }

        // 右侧表单
        .part-right {
          > div {
            // padding: 20px 40px;
            overflow: auto;
            // .ant-select {
            //   transition: none !important;
            //   animation: none !important;
            //   * {
            //     transition: none !important;
            //     animation: none !important;
            //   }
            // }

            .ant-table {
              th {
                font-size: 12px;
                font-weight: normal;
              }
              td {
                // padding-left: 0;
                .ant-input {
                  margin-bottom: 0 !important;
                }
              }
              tr {
                td:last-child {
                  > span {
                    position: relative;
                    // top: 4px;
                    white-space: nowrap;
                  }
                }
              }
            }
          }

          .form-wrapper {
            padding: 20px 40px 64px 40px;
          }

          .part-right-btns {
            text-align: right;
            padding: 12px 24px;
            background: white;
            border-top: 1px solid #eef2fb;
            top: auto;
            bottom: 0;
            right: 0;
            z-index: 10;
            button {
              margin-left: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
