<template>
  <div>
    <!-- <div class="application-content-title">我的应用</div> -->
    <a-alert
      class="content-info"
      message="您可以添加关注的应用，添加后，事前审核列表内将只展示您关注的应用任务。"
      type="info"
      v-if="isFollow"
    />
    <TableList ref="table" :isFollow="isFollow"></TableList>
    <div class="frame-button-wrapper" v-if="isFollow">
      <a-button @click="batchDelete">批量取消关注</a-button>
      <a-button type="primary" @click="batchAttention">批量关注</a-button>
    </div>
  </div>
</template>
<script>
import TableList from './TableList';
import { getHasSubscribe } from '@/api/application';
import bodyMinWidth from '@/mixins/bodyMinWidth';
export default {
  mixins: [bodyMinWidth(1280)],
  components: { TableList },
  props: {},
  data() {
    return {};
  },
  created() {
    this.getHasSubscribeFn();
  },
  mounted() {},
  computed: {
    permissionManagement() {
      return this.$store.state.project.permissionManagement;
    },
    // 关注
    isFollow() {
      return this.permissionManagement == 1;
    }
  },
  methods: {
    getHasSubscribeFn() {
      getHasSubscribe()
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            // this.$hideLoading({
            //   method: 'success',
            //   tips: _.get(res, 'data.message')
            // });
            let hasSubscribe = _.get(res, 'data.data.has_subscribe');
            this.$store.commit('project/setHasSubscribe', hasSubscribe);
            // console.log(this.$store.state.project.hasSubscribe, '222222222222');
          } else {
            // this.$hideLoading({
            //   method: 'error',
            //   tips: _.get(res, 'data.message')
            // });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    batchAttention() {
      this.$refs.table.attention(true);
    },
    batchDelete() {
      this.$refs.table.attention(false);
    }
  }
};
</script>

<style lang="less" scoped>
.application-content-title {
  font-size: 18px;
  font-weight: bold;
}
.content-info {
  margin: 8px 0 24px 0;
  border: none;
  width: 550px;
}
/deep/.search-area {
  .form {
    .ant-form-item-label {
      max-width: 130px !important;
    }
  }
}
</style>
