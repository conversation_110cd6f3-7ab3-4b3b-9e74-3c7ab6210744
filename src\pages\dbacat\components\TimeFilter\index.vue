<template>
  <div class="page-dbacat-time-filter">
    <div class="top-part">
      <span class="curr-time">2025-0203 12:18:00 - 2025-0203 12:18:00</span>
      <RadioGroup
        :value="topVal"
        :options="topOptions"
        mode="button"
        @change="onTopChange"
        size="small"
      />
    </div>
    <div class="center-part">
      <RadioGroup
        :value="centerVal"
        :options="centerOptions"
        mode="button"
        @change="onCenterChange"
      />
    </div>
    <div class="bottom-part">
      <RadioGroup
        :value="bottomVal"
        :options="bottomOptions"
        mode="button"
        @change="onBottomChange"
      />
    </div>
  </div>
</template>

<script>
import RadioGroup from '@/components/RadioGroup';

export default {
  components: { RadioGroup },
  data() {
    return {
      topOptions: [
        { label: '-7d', value: '-7d' },
        { label: '-1d', value: '-1d' },
        { label: '-1h', value: '-1h' },
        { label: '+1h', value: '+1h' },
        { label: '+1d', value: '+1d' },
        { label: '+7d', value: '+7d' },
        { label: 'now', value: 'now' }
      ],
      topVal: 'now',
      centerOptions: [
        { label: 'ALL', value: 'ALL' },
        { label: 'Postgresql', value: 'Postgresql' },
        { label: 'Mysql', value: 'Mysql' },
        { label: 'Oracle', value: 'Oracle' }
      ],
      centerVal: 'ALL',
      bottomOptions: Array(60)
        .fill()
        .map((item, index) => {
          return { label: index + 1, value: index + 1 };
        }),
      bottomVal: undefined
    };
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    onTopChange(val) {
      this.topVal = val;
      console.log(this.topVal);
    },
    onCenterChange(val) {
      this.centerVal = val;
    },
    onBottomChange(val) {
      this.bottomVal = val;
    }
  }
};
</script>

<style lang="less" scoped>
.page-dbacat-time-filter {
  .top-part {
    margin-bottom: 24px;
    .curr-time {
      margin-right: 16px;
    }
  }
  .center-part {
    /deep/ .ant-radio-group {
      > label {
        &:first-child {
          border-radius: 4px 0 0 0;
        }
        &:last-child {
          border-radius: 0 4px 0 0;
        }
      }
    }
  }
  .bottom-part {
    margin-bottom: 24px;
    width: 940px;
    /deep/ .ant-radio-group {
      > label {
        width: 24px;
        display: inline-flex;
        justify-content: center;
        border-radius: 0;
      }
    }
  }
}
</style>
