<template>
  <div>
    <RelationGroup :value="groupData" :fields="fields" :rowKey="rowKey" ref="relationGroup"></RelationGroup>
    <a-dropdown v-model="visible">
      <a-menu slot="overlay" @click="add">
        <a-menu-item key="single">添加单条</a-menu-item>
        <a-menu-item key="group">添加组</a-menu-item>
      </a-menu>
      <a-button>添加+</a-button>
    </a-dropdown>
    <!-- <VirtualListScroll :data="vlist" :height="300" :pageMode="false" :flxedBlockHeight="20">
      <span slot-scope="{data}">{{data.id}}</span>
    </VirtualListScroll>-->
    <a-button @click="test">打印data</a-button>
  </div>
</template>

<script>
import RelationGroup from '@/components/Biz/RelationGroup';
// import common from '@/utils/common';
import config from './config';
const groupData = [
  {
    code: 'ServiceA',
    type: 'ServiceA',
    id: _.uniqueId('rg'),
    group_type: 'group',
    children: [
      {
        code: 'functionA',
        type: 'functionA',
        id: _.uniqueId('rg'),
        group_type: 'group',
        children: [
          {
            code: 'TODO1',
            type: 'TODO1',
            id: _.uniqueId('rg'),
            group_type: 'single'
          },
          {
            code: 'TODO2',
            type: 'TODO2',
            id: _.uniqueId('rg'),
            group_type: 'single'
          },
          {
            code: 'TODO3',
            type: 'TODO3',
            id: _.uniqueId('rg'),
            group_type: 'single'
          }
        ]
      },
      {
        code: 'functionB',
        type: 'functionB',
        id: _.uniqueId('rg'),
        group_type: 'single'
      }
    ]
  },
  {
    code: 'xxx',
    type: 'xxx',
    id: _.uniqueId('rg'),
    group_type: 'single'
  }
];

const loop = (list, pId) => {
  list.forEach((item, index) => {
    item.parent_id = pId;
    if (item.children && item.children.length > 0) {
      loop(item.children, item['id']);
    }
  });
};
loop(groupData);

export default {
  components: { RelationGroup },
  props: {},
  data() {
    this.config = config(this);
    return {
      groupData,
      fields: this.config.fields,
      rowKey: 'id',
      visible: false,
      vlist: Array(100)
        .fill('')
        .map((item, index) => ({ id: index, label: 'ssss ' + index }))
    };
  },
  mounted() {},
  created() {},
  methods: {
    add(e) {
      console.log(e);
      this.visible = false;
      this.$refs.relationGroup.add(e.key);
    },
    test() {
      console.log(this.$refs.relationGroup.getData());
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>