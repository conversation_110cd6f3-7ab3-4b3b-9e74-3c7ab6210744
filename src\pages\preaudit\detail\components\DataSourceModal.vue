<template>
  <a-modal
    v-model="visible"
    @ok="onOk"
    width="800px"
    wrapClassName="code-review-detail-modal"
  >
    <div slot="title" class="title">
      <div>指定数据源</div>
    </div>
    <div class="des">
      系统按照数据源顺序进行执行计划获取并审核，当在某个数据源执行成功后，将不会再对后续数据源进行操作。您可以指定数据源，或调整数据源顺序。
    </div>
    <TableEdit
      ref="table"
      v-bind="tableParams"
      :dataSource="dataSource"
    ></TableEdit>
  </a-modal>
</template>
<script>
import TableEdit from '@/components/TableEdit';
import config from './config';
import { getSchemaEdit } from '@/api/config/project';
export default {
  props: {
    projectId: Number
  },
  components: { TableEdit },
  data() {
    this.config = config(this);
    return {
      visible: false,
      dataSource: [],
      tableParams: {
        editConfig: this.config.editConfig(),
        columns: this.config.columns,
        rowKey: 'id',
        initEditStatus: true,
        pagination: false,
        draggable: true,
        leastNum: 1,
        actionBtns: ['add', 'remove'],
        actionBtnsIcons: {
          add: 'plus-circle',
          remove: 'close-circle'
        }
      },
      id: null
    };
  },
  methods: {
    show() {
      this.visible = true;
      getSchemaEdit({ project_id: this.projectId })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            this.dataSource = resData;
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    hide() {
      this.visible = false;
      this.dataSource = [];
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { table } = this.$refs;
      const data = table.getData();
      table.validate((valid, error) => {
        if (valid) {
          const list = data.map(item => ({
            datasource_id: item.datasource_id,
            schema: item.schema
          }));
          this.$emit('reviewRetry', list);
          this.hide();
        }
      });
    }
  },
  watch: {
    projectId: {
      handler(newVal) {
        this.$set(
          this.tableParams,
          'editConfig',
          this.config.editConfig(newVal)
        );
      },
      immediate: true
    }
  }
};
</script>

<style lang="less">
.code-review-detail-modal {
  .ant-modal-header {
    background: #fff !important;
    padding: 21px 24px;
    .title {
      > div {
        font-size: 20px;
        color: #27272a;
        font-weight: 600;
      }
    }
  }
  .ant-modal-close {
    .ant-modal-close-x {
      color: #27272a;
    }
  }
  .ant-modal-body {
    padding: 0 24px;
    .des {
      font-size: 12px;
      color: #a1a1aa;
      font-weight: 400;
      margin: 0 0 12px 0;
    }
    .table-edit {
      .ant-form {
        .ant-table-wrapper {
          .ant-table {
            .ant-table-body {
              .ant-table-tbody {
                > tr {
                  > td {
                    .ant-row {
                      .ant-col {
                        .biz-data-base-choose {
                          width: 240px !important;
                        }
                        .biz-instance-item {
                          .ant-tag {
                            border: none !important;
                          }
                          .instance-item-tag {
                            padding: 4px 16px !important;
                            width: 200px;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .ant-modal-footer {
    .ant-btn {
      width: 76px;
      height: 36px;
      background: #ffffff;
      border: 1px solid #008adc;
      font-size: 14px;
      color: #008adc;
      font-weight: 600;
      border-radius: 6px;
    }
    .ant-btn-primary {
      background: #008adc;
      color: #ffffff;
      font-weight: 600;
    }
  }
}
</style>
