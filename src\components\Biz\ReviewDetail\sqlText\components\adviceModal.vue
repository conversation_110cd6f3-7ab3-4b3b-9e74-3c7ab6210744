<template>
  <a-modal
    v-model="visible"
    width="50%"
    centered
    :mask="false"
    :footer="null"
    wrapClassName="sql-info-advice-modal"
    :maskStyle="{ pointerEvents: 'none' }"
    :dialogStyle="{ minWidth: '1100px', maxWidth: '1100px' }"
    @cancel="onCancel"
  >
    <template slot="title">
      <span>优化建议</span>
      <a-icon type="drag" />
    </template>
    <a-spin :spinning="loading">
      <MarkdownViewer v-model="content" />
    </a-spin>
  </a-modal>
</template>

<script>
import Cookie from 'js-cookie';
import MarkdownViewer from '@/components/Markdown/viewer';
import Config from '@/utils/config.js'
import { getReviewAISuggestions } from '@/api/optimizeDirective'

export default {
  components: { MarkdownViewer },
  data() {
    return {
      visible: false,
      content: '',
      loading: false,
      abortController: null
    }
  },
  methods: {
    async getContentByStream(detailId, detailExtId) {
      this.loading = true;
      const param = {
        detail_id: detailId,
        detail_ext_id: detailExtId,
        is_stream: true
      }
      this.abortController = new AbortController();
      const res = await fetch('/sqlreview/review/ai_suggestions_stream', {
        method: 'post',
        signal: this.abortController.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          'Custom-Header': 'value', // 添加自定义头以触发预检请求
          'Authorization': Cookie.get(Config.TokenKey) || Cookie.get(Config.LuTokenKey)
        },
        body: JSON.stringify(param),
        cache: 'no-cache'
      }).finally(() => { this.loading = false })
      const reader = res.body.getReader();
      const textDecoder = new TextDecoder('utf-8');
      let buffer = ''
      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          this.abortController = null
          break
        }
        buffer += textDecoder.decode(value, { stream: true })
        console.log(buffer)
        while (buffer.includes('\n')) {
          const lineEnd = buffer.indexOf('\n')
          const line = buffer.slice(0, lineEnd).trim()
          buffer = buffer.slice(lineEnd + 1)
          if (line.length > 1) {
            try {
              const res = JSON.parse(line)
              this.content += res.answer || ''
            } catch (error) {
              console.error(error)
            }
          }
        }
      }
    },
    async getContentByAll(detailId, detailExtId) {
      this.loading = true
      const param = {
        detail_id: detailId,
        detail_ext_id: detailExtId,
        is_stream: false
      }
      const res = await getReviewAISuggestions(param).finally(() => { this.loading = false })
      if (_.get(res, 'data.code') == 0) {
        this.$hideLoading({ tips: _.get(res, 'data.message') });
        const { data: { answer } } = res.data
        this.content = answer
      } else {
        this.$hideLoading({
          method: 'error',
          tips: _.get(res, 'data.message')
        });
      }
    },
    async show(detailId, detailExtId) {
      this.visible = true;
      this.getContentByStream(detailId, detailExtId)
    },
    hide() {
      if (this.abortController) {
        this.abortController.abort()
        this.abortController = null
      }
      this.content = '';
      this.loading = false;
      this.visible = false;
    },
    onCancel() {
      this.hide();
    }
  }
}
</script>

<style lang="less">
.sql-info-advice-modal {
  pointer-events: none;
  .ant-modal-content {
    .ant-modal-close {
      .ant-modal-close-x {
        color: #27272a;
        font-size: 20px;
      }
    }
    .ant-modal-header {
      padding: 18px 24px;
      cursor: move;
      background: #ffff;
      .ant-modal-title {
        font-size: 20px;
        color: #27272a;
        font-weight: 600;
        margin-right: 36px;
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .ant-modal-body {
    max-height: 60vh;
    overflow: auto;
  }
}
</style>
