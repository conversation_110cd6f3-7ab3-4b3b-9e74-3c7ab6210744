<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="新建审核对象"
    okText="保存"
    width="640px"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="params" :formData="data" class="add-form">
      </Form>
    </a-spin>
    <TipsModal ref="tips" />
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import TipsModal from './TipsModal';
const formParams = (ctx, _t, bool, type) => {
  return {
    layout: 'vertical',
    fields: [
      // 存储过程
      {
        type: 'Input',
        label: '任务名称',
        key: 'review_name',
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        return {
          type: 'DataBaseChoose',
          label: '数据源选择',
          key: 'datasource_id',
          props: {
            url: '/sqlreview/project/data_source_choices',
            loaded(data) {
              ctx.dataSourceOption = data;
            },
            beforeLoaded(data) {
              return data.map(item => {
                return {
                  ...item,
                  instance_usage: item.env,
                  showText: item.label + '(' + item.db_url + ')'
                };
              });
            },
            reqParams: {
              type: 'ORACLE'
            },
            mode: 'default',
            optionLabelProp: 'children',
            backSearchOnlyOnSearch: true,
            allowSearch: true,
            backSearch: true,
            limit: 30
          },
          listeners: {
            change: value => {
              console.log(value);
              ctx.dataSourceOption.map(item => {
                if (item.value === value) {
                  ctx.changeValueArr = item.db_type;
                }
              });
              ctx.$refs.form.saving({
                datasource_id: value,
                schema_id: null,
                procedure: null
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'schema',
          key: 'schema_id',
          props: {
            url: '/sqlreview/project/get_schema_list',
            reqParams: {
              datasource_id: formData.datasource_id
            },
            backSearchOnlyOnSearch: true,
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                schema_id: value || undefined,
                procedure: null
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'TreeSelect',
          label: '审核对象',
          key: 'procedure',
          props: {
            url: '/sqlreview/review/procedure-list/',
            reqParams: {
              data_source_id: formData.datasource_id,
              schema_id: formData.schema_id
            },
            treeCheckable: true,
            maxTagCount: 4,
            loadedError(res) {
              if (_.get(res, 'data.code') == 4001) {
                const label = _.get(res, 'data.data.sql_text');
                const message = _.get(res, 'data.message');
                ctx.$refs.tips.show(label, message);
              }
            },
            beforeLoaded(res) {
              return [
                {
                  title: '存储过程',
                  value: 'PROCEDURE',
                  key: 'PROCEDURE',
                  disableCheckbox: (res.PROCEDURE || []).length <= 0,
                  children: (res.PROCEDURE || []).map(item => {
                    return {
                      ...item,
                      title: item.label,
                      key: item.value
                    };
                  })
                },
                {
                  title: '函数',
                  value: 'FUNCTION',
                  key: 'FUNCTION',
                  disableCheckbox: (res.FUNCTION || []).length <= 0,
                  children: (res.FUNCTION || []).map(item => {
                    return {
                      ...item,
                      title: item.label,
                      key: item.value
                    };
                  })
                },
                {
                  title: '包',
                  value: 'PACKAGE',
                  key: 'PACKAGE',
                  disableCheckbox: (res.PACKAGE || []).length <= 0,
                  children: (res.PACKAGE || []).map(item => {
                    return {
                      ...item,
                      title: item.label,
                      key: item.value
                    };
                  })
                }
              ];
            }
          },
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '规则集',
          key: 'rule_set',
          props: {
            url: '/sqlreview/project/rule_set_all',
            mode: 'multiple',
            reqParams: { db_type: ctx.changeValueArr }
          },
          listeners: {
            change: value => {}
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      }
    ]
  };
};

export default {
  components: { Form, TipsModal },
  props: {},
  data() {
    return {
      spinning: false,
      visible: false,
      data: {},
      params: formParams(this),
      fileList: [],
      fileSize: 0
    };
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      this.data = {};
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData();
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .add-form {
  .ant-row .ant-form-item-control-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        // width: 150px;
      }
    }
    .biz-data-base-choose {
      .ant-select-selection {
        .ant-select-selection__rendered {
          .ant-select-selection-selected-value {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
