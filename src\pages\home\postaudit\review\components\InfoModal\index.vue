<template>
  <a-modal
    v-model="visible"
    width="55%"
    title="统计信息"
    :maskClosable="false"
    :mask="false"
    :footer="null"
    wrapClassName="review-total-info-modal"
    :maskStyle="{ 'pointerEvents': 'none' }"
    :bodyStyle="{ 'maxHeight': '600px', 'overflow': 'auto' }"
    @cancel="onCancel"
  >
    <a-spin :spinning="loading">
      <a-tabs default-active-key="table">
        <a-tab-pane key="table" tab="表信息">
          <Table v-bind="tableParams" :dataSource="tableData" bordered></Table>
        </a-tab-pane>
        <a-tab-pane key="index" tab="索引信息">
          <div class="table-name-wrapper" v-for="item in tableData" :key="item.table_name">
            <div class="table-name">
              <span>表名：{{item.table_name}}</span>
            </div>
            <Table
              v-bind="indexParams"
              :dataSource="indexData.filter(itm => itm.table_name === item.table_name)"
              bordered
            ></Table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
import { getTableInfo } from '@/api/review';
import config from './config';

export default {
  components: { Table },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      loading: false,
      tableParams: {
        columns: this.config.tableColumns,
        pagination: false,
        rowKey: 'id'
      },
      tableData: [],
      indexParams: {
        columns: this.config.indexColumns(),
        pagination: false,
        rowKey: 'id'
      },
      indexData: [],
      table_name: null
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(record = {}) {
      this.visible = true;

      // 发起请求
      this.loading = true;
      ReqUtil.req({
        ctx: this,
        reqInstance: getTableInfo,
        params: {
          detail_id: record.id
        },
        needLoading: false,
        cbk: data => {
          data = data || {};
          this.tableData = (data.table_list || []).map((item, index) => ({
            ...item,
            id: index
          }));
          this.table_name = this.tableData[0].table_name || '';
          this.indexData = (data.index_list || []).map((item, index) => ({
            ...item,
            id: index
          }));
          // this.index_name = this.indexData[0].table_name || '';
          // 处理索引合并
          this.combineIndexTable(this.indexData);
        },
        err: res => {
          this.tableData = [];
          this.indexData = [];
        }
      })
        .then(() => {
          this.$hideLoading({ duration: 0 });
          this.loading = false;
        })
        .catch(e => {
          this.loading = false;
        });
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    combineIndexTable(data) {
      const combineColumns = ['table_name', 'index_name'];
      let map = { table_name: {}, index_name: {} };
      data.forEach((item, index) => {
        combineColumns.forEach(key => {
          let colVal = item[key];
          let colMap = map[key];
          if (colVal) {
            let uid = colVal + '_' + index;
            colMap[uid] = {
              index,
              rowSpan: 1
            };
            // 和前面值相同
            if (index > 0 && data[index - 1][key] == colVal) {
              colMap[uid].rowSpan = 0;
              let pid = colVal + '_' + (index - 1);
              if (!colMap[pid].parent) {
                colMap[pid].rowSpan += 1;
                colMap[uid].parent = colMap[pid];
              } else {
                colMap[pid].parent.rowSpan += 1;
                colMap[uid].parent = colMap[pid].parent;
              }
            }
          }
        });
      });

      this.$set(
        this.indexParams,
        'columns',
        this.config.indexColumns({ combineInfo: map })
      );
    }
  }
};
</script>

<style lang="less">
.review-total-info-modal {
  pointer-events: none;
  .ant-modal-header {
    cursor: move;
  }
  .ant-modal-content {
    position: absolute;
    width: 100%;
    .table-name-wrapper {
      padding: 8px 0;
      &:first-child {
        padding-top: 0;
      }
      .table-name {
        color: #1890ff;
        font-size: 14px;
        font-weight: 700;
        margin: 8px 0 16px 0;
        span {
          padding: 4px 8px;
          border: 1px solid #1890ff;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
