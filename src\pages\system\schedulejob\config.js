export default function (ctx) {
  const columns = (value) => {
    return [
      // {
      //   title: 'UUID',
      //   dataIndex: 'job_uuid',
      //   key: 'job_uuid',
      //   scopedSlots: { customRender: 'job_uuid' }
      // },
      {
        title: '任务名称',
        dataIndex: 'job_name',
        key: 'job_name',
        scopedSlots: { customRender: 'job_name' }
      },
      {
        title: '数据源ID',
        dataIndex: 'service_id',
        key: 'service_id',
        width: 120,
        scopedSlots: { customRender: 'service_id' }
      },
      {
        title: '开始时间',
        dataIndex: 'start_time',
        key: 'start_time',
        width: 250,
        scopedSlots: { customRender: 'start_time' }
      },
      {
        title: '任务状态',
        dataIndex: 'status',
        key: 'status',
        width: 120,
        scopedSlots: { customRender: 'status' }
      },
      {
        title: '调度频率',
        key: 'interval',
        dataIndex: 'interval',
        width: 120,
        visible: value == 'JDBC'
      },
      {
        title: '并行度',
        dataIndex: 'parallel',
        key: 'parallel',
        width: 120,
        visible: value == 'JDBC'
      },
      {
        title: '执行次数',
        dataIndex: 'count',
        width: 120,
        key: 'count'
      },
      {
        title: '失败次数',
        dataIndex: 'error_count',
        width: 120,
        key: 'error_count'
      },
      {
        title: '信息',
        dataIndex: 'msg',
        key: 'msg',
        // width: 200,
        scopedSlots: { customRender: 'msg' }
      },
      {
        title: '操作',
        key: 'action',
        scopedSlots: { customRender: 'action' },
        width: 120,
        fixed: 'right'
      }
    ].map((item) => {
      return {
        ...item,
        width: undefined
      };
    });
  };
  const systemColumns = [
    // {
    //   title: 'UUID',
    //   dataIndex: 'job_uuid',
    //   key: 'job_uuid',
    //   scopedSlots: { customRender: 'job_uuid' }
    // },
    {
      title: '任务名称',
      dataIndex: 'job_name',
      key: 'job_name',
      scopedSlots: { customRender: 'job_name' }
    },
    // {
    //   title: '数据源ID',
    //   dataIndex: 'service_id',
    //   key: 'service_id',
    //   width: 120,
    //   scopedSlots: { customRender: 'service_id' }
    // },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 250,
      scopedSlots: { customRender: 'start_time' }
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      scopedSlots: { customRender: 'status' }
    },
    {
      title: '调度频率',
      key: 'interval',
      width: 120,
      dataIndex: 'interval'
    },
    {
      title: '执行次数',
      dataIndex: 'count',
      width: 120,
      key: 'count'
    },
    {
      title: '失败次数',
      dataIndex: 'error_count',
      width: 120,
      key: 'error_count'
    },
    {
      title: '信息',
      dataIndex: 'msg',
      key: 'msg',
      // width: 200,
      scopedSlots: { customRender: 'msg' }
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 120,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const searchFields = (type, extra) => {
    return [
      {
        type: 'Input',
        key: 'service_id',
        compIcon: 'lu-icon-ID',
        props: {
          placeholder: '数据源ID'
        },
        visible: extra !== 'ClearData'
      },
      {
        type: 'Input',
        key: 'job_name',
        compIcon: 'highlight',
        props: {
          placeholder: '任务名称'
        }
      },
      {
        type: 'Select',
        key: 'status',
        compIcon: 'bg-colors',
        props: {
          placeholder: '任务状态',
          url:
            type == 'job_jdbc_choose'
              ? '/sqlreview/api/v1/schedules/job/status'
              : '/sqlreview/after_audit/get_sql_collect_enum',
          reqParams:
            type == 'job_jdbc_choose'
              ? { is_sub: 0 }
              : {
                enum_name: type
              },
          getPopupContainer: (el) => {
            return document.body;
          }
        }
      }
    ];
  };

  const agentMasterColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      scopedSlots: { customRender: 'name' }
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip'
    },
    {
      title: 'agent worker',
      dataIndex: 'agent_worker_count',
      key: 'agent_worker_count'
    },
    {
      title: 'java进程',
      dataIndex: 'java_process_count',
      key: 'java_process_count'
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      scopedSlots: { customRender: 'agent-status' }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true
    },
    {
      title: '最后采集时间',
      dataIndex: 'heartbeat_time',
      key: 'heartbeat_time',
      sorter: true
    }
  ];

  const agentMasterSearchFields = [
    {
      type: 'Input',
      key: 'name',
      compIcon: 'highlight',
      props: {
        placeholder: 'agent master名称'
      }
    },
    {
      type: 'Input',
      key: 'ip',
      compIcon: 'italic',
      props: {
        placeholder: 'IP'
      }
    },
    {
      type: 'Select',
      key: 'status',
      compIcon: 'bg-colors',
      props: {
        placeholder: '任务状态',
        options: [
          { label: '在线', value: 1 },
          { label: '离线', value: 0 }
        ],
        getPopupContainer: (el) => {
          return document.body;
        }
      }
    }
  ];

  return {
    columns,
    systemColumns,
    searchFields,
    agentMasterColumns,
    agentMasterSearchFields
  };
}
