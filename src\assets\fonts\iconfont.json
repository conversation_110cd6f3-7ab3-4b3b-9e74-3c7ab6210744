{"id": "2809617", "name": "hubsdb", "font_family": "luicon", "css_prefix_text": "lu-icon-", "description": "", "glyphs": [{"icon_id": "39633808", "name": "realtimesql", "font_class": "realtimesql", "unicode": "e6d2", "unicode_decimal": 59090}, {"icon_id": "39633807", "name": "topsql", "font_class": "topsql", "unicode": "e6d3", "unicode_decimal": 59091}, {"icon_id": "39603180", "name": "awspgsql", "font_class": "awspgsql", "unicode": "e6d0", "unicode_decimal": 59088}, {"icon_id": "39603178", "name": "awsmysql", "font_class": "awsmysql", "unicode": "e6d1", "unicode_decimal": 59089}, {"icon_id": "38097673", "name": "mogdb", "font_class": "mogdb", "unicode": "e776", "unicode_decimal": 59254}, {"icon_id": "39056757", "name": "re-review", "font_class": "re-review", "unicode": "e6cf", "unicode_decimal": 59087}, {"icon_id": "11771338", "name": "orderdown", "font_class": "orderdown", "unicode": "e6cd", "unicode_decimal": 59085}, {"icon_id": "11771339", "name": "orderup", "font_class": "orderup", "unicode": "e6ce", "unicode_decimal": 59086}, {"icon_id": "38775323", "name": "goldendb", "font_class": "goldendb", "unicode": "e6cb", "unicode_decimal": 59083}, {"icon_id": "38739737", "name": "node", "font_class": "node", "unicode": "e6ca", "unicode_decimal": 59082}, {"icon_id": "5517432", "name": "loading1", "font_class": "loading1", "unicode": "e962", "unicode_decimal": 59746}, {"icon_id": "15644387", "name": "folder", "font_class": "folder", "unicode": "e6cc", "unicode_decimal": 59084}, {"icon_id": "13519360", "name": "cpu", "font_class": "cpu", "unicode": "e6c9", "unicode_decimal": 59081}, {"icon_id": "38293766", "name": "release", "font_class": "release-copy", "unicode": "ed50", "unicode_decimal": 60752}, {"icon_id": "1305071", "name": "memo", "font_class": "memo", "unicode": "e6c8", "unicode_decimal": 59080}, {"icon_id": "37434644", "name": "doris", "font_class": "<PERSON>", "unicode": "e78a", "unicode_decimal": 59274}, {"icon_id": "587352", "name": "rds-mysql", "font_class": "rds", "unicode": "e6c7", "unicode_decimal": 59079}, {"icon_id": "37568153", "name": "polardb", "font_class": "polardb", "unicode": "e6c6", "unicode_decimal": 59078}, {"icon_id": "37425368", "name": "presto", "font_class": "presto", "unicode": "e6c5", "unicode_decimal": 59077}, {"icon_id": "37299118", "name": "index", "font_class": "index", "unicode": "e6ba", "unicode_decimal": 59066}, {"icon_id": "37299119", "name": "primarykey", "font_class": "primarykey", "unicode": "e6bb", "unicode_decimal": 59067}, {"icon_id": "37299120", "name": "indexgroup", "font_class": "indexgroup", "unicode": "e6bc", "unicode_decimal": 59068}, {"icon_id": "37299121", "name": "fieldgroup", "font_class": "fieldgroup", "unicode": "e6bd", "unicode_decimal": 59069}, {"icon_id": "37299122", "name": "tablegroup", "font_class": "table1", "unicode": "e6be", "unicode_decimal": 59070}, {"icon_id": "37299123", "name": "viewgroup", "font_class": "viewgroup", "unicode": "e6bf", "unicode_decimal": 59071}, {"icon_id": "37299124", "name": "function2", "font_class": "function2", "unicode": "e6c0", "unicode_decimal": 59072}, {"icon_id": "37299125", "name": "functiongroup", "font_class": "functiongroup", "unicode": "e6c1", "unicode_decimal": 59073}, {"icon_id": "37299126", "name": "view1", "font_class": "view1", "unicode": "e6c2", "unicode_decimal": 59074}, {"icon_id": "37299127", "name": "schema", "font_class": "schema1", "unicode": "e6c3", "unicode_decimal": 59075}, {"icon_id": "37299128", "name": "triggergroup", "font_class": "triggergroup", "unicode": "e6c4", "unicode_decimal": 59076}, {"icon_id": "37097407", "name": "ID", "font_class": "ID", "unicode": "e6b9", "unicode_decimal": 59065}, {"icon_id": "15378139", "name": "apacherocketmq", "font_class": "apacherocketmq", "unicode": "eb40", "unicode_decimal": 60224}, {"icon_id": "36693038", "name": "amazon", "font_class": "amazon", "unicode": "e6b8", "unicode_decimal": 59064}, {"icon_id": "36693040", "name": "dameng", "font_class": "dameng", "unicode": "e6b7", "unicode_decimal": 59063}, {"icon_id": "36503279", "name": "filter", "font_class": "filter1", "unicode": "e6b6", "unicode_decimal": 59062}, {"icon_id": "36501772", "name": "unusual", "font_class": "unusual", "unicode": "e699", "unicode_decimal": 59033}, {"icon_id": "36501773", "name": "notice", "font_class": "notice", "unicode": "e6b5", "unicode_decimal": 59061}, {"icon_id": "36390494", "name": "nopass", "font_class": "nopass", "unicode": "e693", "unicode_decimal": 59027}, {"icon_id": "36390495", "name": "unknown", "font_class": "unknown", "unicode": "e694", "unicode_decimal": 59028}, {"icon_id": "36390498", "name": "pass", "font_class": "pass1", "unicode": "e695", "unicode_decimal": 59029}, {"icon_id": "36139633", "name": "download", "font_class": "download", "unicode": "e6ae", "unicode_decimal": 59054}, {"icon_id": "36139634", "name": "confirm", "font_class": "confirm", "unicode": "e6af", "unicode_decimal": 59055}, {"icon_id": "36139635", "name": "code", "font_class": "code", "unicode": "e6b0", "unicode_decimal": 59056}, {"icon_id": "36139636", "name": "upload", "font_class": "upload", "unicode": "e6b2", "unicode_decimal": 59058}, {"icon_id": "36135560", "name": "warning1", "font_class": "warning1", "unicode": "e6b4", "unicode_decimal": 59060}, {"icon_id": "36091146", "name": "day", "font_class": "day", "unicode": "e6b1", "unicode_decimal": 59057}, {"icon_id": "36090725", "name": "database", "font_class": "database", "unicode": "e6b3", "unicode_decimal": 59059}, {"icon_id": "36090252", "name": "night", "font_class": "night", "unicode": "e6ad", "unicode_decimal": 59053}, {"icon_id": "36073380", "name": "alarm", "font_class": "alarm", "unicode": "e6ab", "unicode_decimal": 59051}, {"icon_id": "36073381", "name": "ring", "font_class": "ring", "unicode": "e6ac", "unicode_decimal": 59052}, {"icon_id": "35968234", "name": "impala", "font_class": "impala1", "unicode": "e6a9", "unicode_decimal": 59049}, {"icon_id": "35968235", "name": "hive", "font_class": "hive", "unicode": "e6aa", "unicode_decimal": 59050}, {"icon_id": "35967605", "name": "dba", "font_class": "DBA", "unicode": "e6a3", "unicode_decimal": 59043}, {"icon_id": "35967606", "name": "developer", "font_class": "developer", "unicode": "e6a4", "unicode_decimal": 59044}, {"icon_id": "35967607", "name": "if", "font_class": "if", "unicode": "e6a5", "unicode_decimal": 59045}, {"icon_id": "35967609", "name": "do", "font_class": "do", "unicode": "e6a6", "unicode_decimal": 59046}, {"icon_id": "35968015", "name": "admin", "font_class": "admin", "unicode": "e6a7", "unicode_decimal": 59047}, {"icon_id": "35968016", "name": "enable", "font_class": "enable", "unicode": "e6a8", "unicode_decimal": 59048}, {"icon_id": "35913885", "name": "kingbase", "font_class": "kingbase", "unicode": "e6a2", "unicode_decimal": 59042}, {"icon_id": "35817645", "name": "hudi", "font_class": "hudi", "unicode": "e6a1", "unicode_decimal": 59041}, {"icon_id": "35635521", "name": "viewlist", "font_class": "viewlist", "unicode": "e696", "unicode_decimal": 59030}, {"icon_id": "35635522", "name": "viewgrid", "font_class": "viewgrid", "unicode": "e697", "unicode_decimal": 59031}, {"icon_id": "35578533", "name": "java", "font_class": "java", "unicode": "e6a0", "unicode_decimal": 59040}, {"icon_id": "35574012", "name": "sql", "font_class": "sql", "unicode": "e69d", "unicode_decimal": 59037}, {"icon_id": "35574013", "name": "xml", "font_class": "xml", "unicode": "e69e", "unicode_decimal": 59038}, {"icon_id": "35573862", "name": "annotation", "font_class": "annotation", "unicode": "e69b", "unicode_decimal": 59035}, {"icon_id": "35573865", "name": "mybatis", "font_class": "mybatis", "unicode": "e69f", "unicode_decimal": 59039}, {"icon_id": "35544895", "name": "clean", "font_class": "clean", "unicode": "e640", "unicode_decimal": 58944}, {"icon_id": "35537258", "name": "storage", "font_class": "storage", "unicode": "e629", "unicode_decimal": 58921}, {"icon_id": "35535619", "name": "dbreview", "font_class": "dbreview", "unicode": "e69a", "unicode_decimal": 59034}, {"icon_id": "35535621", "name": "codereview", "font_class": "codereview", "unicode": "e69c", "unicode_decimal": 59036}, {"icon_id": "35534894", "name": "log", "font_class": "log", "unicode": "e692", "unicode_decimal": 59026}, {"icon_id": "35534895", "name": "whitelist", "font_class": "whitelist", "unicode": "e698", "unicode_decimal": 59032}, {"icon_id": "35514799", "name": "config", "font_class": "config1", "unicode": "e634", "unicode_decimal": 58932}, {"icon_id": "35514800", "name": "PC", "font_class": "PC", "unicode": "e63c", "unicode_decimal": 58940}, {"icon_id": "35514802", "name": "order", "font_class": "order", "unicode": "e644", "unicode_decimal": 58948}, {"icon_id": "35514803", "name": "rule", "font_class": "rule", "unicode": "e64e", "unicode_decimal": 58958}, {"icon_id": "35514804", "name": "user", "font_class": "user1", "unicode": "e68e", "unicode_decimal": 59022}, {"icon_id": "35514806", "name": "apps", "font_class": "apps", "unicode": "e68f", "unicode_decimal": 59023}, {"icon_id": "35514807", "name": "qreview", "font_class": "qreview", "unicode": "e690", "unicode_decimal": 59024}, {"icon_id": "35514808", "name": "sqledit", "font_class": "sqledit", "unicode": "e691", "unicode_decimal": 59025}, {"icon_id": "35025577", "name": "opengauss", "font_class": "opengauss", "unicode": "e628", "unicode_decimal": 58920}, {"icon_id": "35025044", "name": "data-sensitive", "font_class": "data-sensitive", "unicode": "e616", "unicode_decimal": 58902}, {"icon_id": "35025045", "name": "gaussdb", "font_class": "gaussdb", "unicode": "e618", "unicode_decimal": 58904}, {"icon_id": "34539617", "name": "mapping", "font_class": "mapping", "unicode": "e601", "unicode_decimal": 58881}, {"icon_id": "34539618", "name": "function1", "font_class": "function1", "unicode": "e602", "unicode_decimal": 58882}, {"icon_id": "34539619", "name": "router", "font_class": "router", "unicode": "e603", "unicode_decimal": 58883}, {"icon_id": "34539620", "name": "store", "font_class": "store", "unicode": "e604", "unicode_decimal": 58884}, {"icon_id": "17780636", "name": "gbase", "font_class": "gbase", "unicode": "e68d", "unicode_decimal": 59021}, {"icon_id": "31601852", "name": "GBASE", "font_class": "gbase1", "unicode": "e627", "unicode_decimal": 58919}, {"icon_id": "4273490", "name": "impala", "font_class": "impala", "unicode": "e70b", "unicode_decimal": 59147}, {"icon_id": "33236046", "name": "capacity", "font_class": "capacity", "unicode": "e68c", "unicode_decimal": 59020}, {"icon_id": "32900022", "name": "batch", "font_class": "batch", "unicode": "e68b", "unicode_decimal": 59019}, {"icon_id": "32869852", "name": "operation", "font_class": "operation", "unicode": "e689", "unicode_decimal": 59017}, {"icon_id": "32869853", "name": "safe", "font_class": "safe", "unicode": "e68a", "unicode_decimal": 59018}, {"icon_id": "32869550", "name": "applyfor1", "font_class": "applyfor1", "unicode": "e687", "unicode_decimal": 59015}, {"icon_id": "32869551", "name": "seal", "font_class": "seal", "unicode": "e688", "unicode_decimal": 59016}, {"icon_id": "32585604", "name": "tdsql-1", "font_class": "tdsql-1", "unicode": "e685", "unicode_decimal": 59013}, {"icon_id": "32585605", "name": "sql-server", "font_class": "sql-server", "unicode": "e686", "unicode_decimal": 59014}, {"icon_id": "32397109", "name": "scan", "font_class": "scan", "unicode": "e684", "unicode_decimal": 59012}, {"icon_id": "32016826", "name": "db2", "font_class": "db2", "unicode": "e682", "unicode_decimal": 59010}, {"icon_id": "31869349", "name": "tdsql", "font_class": "tdsql", "unicode": "e683", "unicode_decimal": 59011}, {"icon_id": "31112100", "name": "oceanbase", "font_class": "oceanbase", "unicode": "e681", "unicode_decimal": 59009}, {"icon_id": "30905242", "name": "loading", "font_class": "loading", "unicode": "e680", "unicode_decimal": 59008}, {"icon_id": "30797307", "name": "app", "font_class": "app", "unicode": "e67f", "unicode_decimal": 59007}, {"icon_id": "30769944", "name": "disable", "font_class": "disable", "unicode": "e679", "unicode_decimal": 59001}, {"icon_id": "30769945", "name": "sensitive", "font_class": "sensitive", "unicode": "e67c", "unicode_decimal": 59004}, {"icon_id": "30769946", "name": "impower", "font_class": "impower", "unicode": "e67d", "unicode_decimal": 59005}, {"icon_id": "30769947", "name": "applyfor", "font_class": "applyfor", "unicode": "e67e", "unicode_decimal": 59006}, {"icon_id": "30636459", "name": "person1", "font_class": "person2", "unicode": "e672", "unicode_decimal": 58994}, {"icon_id": "30629860", "name": "joint", "font_class": "joint", "unicode": "e66e", "unicode_decimal": 58990}, {"icon_id": "30334331", "name": "idea", "font_class": "idea", "unicode": "e668", "unicode_decimal": 58984}, {"icon_id": "30313297", "name": "star", "font_class": "star", "unicode": "e67b", "unicode_decimal": 59003}, {"icon_id": "30295412", "name": "Filter", "font_class": "filter", "unicode": "e659", "unicode_decimal": 58969}, {"icon_id": "30295414", "name": "bell", "font_class": "bell", "unicode": "e673", "unicode_decimal": 58995}, {"icon_id": "30295415", "name": "cost", "font_class": "cost", "unicode": "e674", "unicode_decimal": 58996}, {"icon_id": "30295416", "name": "rollback", "font_class": "rollback", "unicode": "e675", "unicode_decimal": 58997}, {"icon_id": "30295417", "name": "pass", "font_class": "pass", "unicode": "e676", "unicode_decimal": 58998}, {"icon_id": "30295418", "name": "exempt", "font_class": "exempt", "unicode": "e677", "unicode_decimal": 58999}, {"icon_id": "30295419", "name": "details", "font_class": "details", "unicode": "e678", "unicode_decimal": 59000}, {"icon_id": "30295455", "name": "Maintain", "font_class": "tool", "unicode": "e67a", "unicode_decimal": 59002}, {"icon_id": "30060413", "name": "roam", "font_class": "roam", "unicode": "e66f", "unicode_decimal": 58991}, {"icon_id": "30054223", "name": "import", "font_class": "import", "unicode": "e670", "unicode_decimal": 58992}, {"icon_id": "30054224", "name": "pull", "font_class": "pull", "unicode": "e671", "unicode_decimal": 58993}, {"icon_id": "29967753", "name": "robot", "font_class": "robot", "unicode": "e66a", "unicode_decimal": 58986}, {"icon_id": "29967754", "name": "person1", "font_class": "person1", "unicode": "e66b", "unicode_decimal": 58987}, {"icon_id": "29967755", "name": "staring", "font_class": "staring", "unicode": "e66c", "unicode_decimal": 58988}, {"icon_id": "29967756", "name": "wrong", "font_class": "wrong", "unicode": "e66d", "unicode_decimal": 58989}, {"icon_id": "29967752", "name": "right1", "font_class": "right1", "unicode": "e669", "unicode_decimal": 58985}, {"icon_id": "29784526", "name": "format", "font_class": "format", "unicode": "e660", "unicode_decimal": 58976}, {"icon_id": "29784527", "name": "user-platform", "font_class": "user-platform", "unicode": "e661", "unicode_decimal": 58977}, {"icon_id": "29784529", "name": "check", "font_class": "check", "unicode": "e662", "unicode_decimal": 58978}, {"icon_id": "29784530", "name": "vs", "font_class": "vs", "unicode": "e663", "unicode_decimal": 58979}, {"icon_id": "29784531", "name": "user-role", "font_class": "user-role", "unicode": "e664", "unicode_decimal": 58980}, {"icon_id": "29784546", "name": "full", "font_class": "full", "unicode": "e665", "unicode_decimal": 58981}, {"icon_id": "29784547", "name": "quit", "font_class": "quit", "unicode": "e667", "unicode_decimal": 58983}, {"icon_id": "29784524", "name": "stop", "font_class": "stop", "unicode": "e65e", "unicode_decimal": 58974}, {"icon_id": "29784525", "name": "run", "font_class": "run", "unicode": "e65f", "unicode_decimal": 58975}, {"icon_id": "29552221", "name": "search", "font_class": "search", "unicode": "e65b", "unicode_decimal": 58971}, {"icon_id": "29533805", "name": "person", "font_class": "person", "unicode": "e65d", "unicode_decimal": 58973}, {"icon_id": "29533783", "name": "trigger", "font_class": "trigger", "unicode": "e65c", "unicode_decimal": 58972}, {"icon_id": "29533623", "name": "minus", "font_class": "minus", "unicode": "e641", "unicode_decimal": 58945}, {"icon_id": "29533544", "name": "edit", "font_class": "edit", "unicode": "e643", "unicode_decimal": 58947}, {"icon_id": "29531875", "name": "end", "font_class": "end", "unicode": "e652", "unicode_decimal": 58962}, {"icon_id": "29531876", "name": "error", "font_class": "error", "unicode": "e657", "unicode_decimal": 58967}, {"icon_id": "29531877", "name": "check", "font_class": "success", "unicode": "e658", "unicode_decimal": 58968}, {"icon_id": "29531878", "name": "pending", "font_class": "pending", "unicode": "e659", "unicode_decimal": 58969}, {"icon_id": "29531879", "name": "warning", "font_class": "warning", "unicode": "e65a", "unicode_decimal": 58970}, {"icon_id": "29531730", "name": "lock new", "font_class": "locknew", "unicode": "e647", "unicode_decimal": 58951}, {"icon_id": "29531731", "name": "unlock", "font_class": "unlocknew", "unicode": "e64b", "unicode_decimal": 58955}, {"icon_id": "29531567", "name": "sort", "font_class": "sort", "unicode": "e656", "unicode_decimal": 58966}, {"icon_id": "29531223", "name": "business", "font_class": "business", "unicode": "e633", "unicode_decimal": 58931}, {"icon_id": "29531225", "name": "copy", "font_class": "copy", "unicode": "e635", "unicode_decimal": 58933}, {"icon_id": "29531226", "name": "ddl", "font_class": "ddl", "unicode": "e636", "unicode_decimal": 58934}, {"icon_id": "29531227", "name": "dml", "font_class": "dml", "unicode": "e637", "unicode_decimal": 58935}, {"icon_id": "29531228", "name": "delete", "font_class": "delete", "unicode": "e638", "unicode_decimal": 58936}, {"icon_id": "29531229", "name": "editor", "font_class": "editor", "unicode": "e639", "unicode_decimal": 58937}, {"icon_id": "29531231", "name": "function", "font_class": "function", "unicode": "e63a", "unicode_decimal": 58938}, {"icon_id": "29531233", "name": "field", "font_class": "field", "unicode": "e63b", "unicode_decimal": 58939}, {"icon_id": "29531235", "name": "exit", "font_class": "exit", "unicode": "e63d", "unicode_decimal": 58941}, {"icon_id": "29531236", "name": "entity", "font_class": "entity", "unicode": "e63e", "unicode_decimal": 58942}, {"icon_id": "29531237", "name": "home new", "font_class": "homenew", "unicode": "e63f", "unicode_decimal": 58943}, {"icon_id": "29531240", "name": "reset", "font_class": "reset", "unicode": "e642", "unicode_decimal": 58946}, {"icon_id": "29531244", "name": "right", "font_class": "right", "unicode": "e645", "unicode_decimal": 58949}, {"icon_id": "29531245", "name": "list", "font_class": "list", "unicode": "e646", "unicode_decimal": 58950}, {"icon_id": "29531247", "name": "plus new", "font_class": "plusnew", "unicode": "e648", "unicode_decimal": 58952}, {"icon_id": "29531248", "name": "key", "font_class": "key", "unicode": "e649", "unicode_decimal": 58953}, {"icon_id": "29531249", "name": "memory", "font_class": "memory", "unicode": "e64a", "unicode_decimal": 58954}, {"icon_id": "29531251", "name": "sequence", "font_class": "sequence", "unicode": "e64c", "unicode_decimal": 58956}, {"icon_id": "29531252", "name": "sql resolver", "font_class": "sqlresolver", "unicode": "e64d", "unicode_decimal": 58957}, {"icon_id": "29531255", "name": "user new", "font_class": "usernew", "unicode": "e64f", "unicode_decimal": 58959}, {"icon_id": "29531256", "name": "up", "font_class": "up", "unicode": "e650", "unicode_decimal": 58960}, {"icon_id": "29531257", "name": "view", "font_class": "view", "unicode": "e651", "unicode_decimal": 58961}, {"icon_id": "29531259", "name": "system", "font_class": "system", "unicode": "e653", "unicode_decimal": 58963}, {"icon_id": "29531260", "name": "task", "font_class": "task", "unicode": "e654", "unicode_decimal": 58964}, {"icon_id": "29531261", "name": "schema", "font_class": "schema", "unicode": "e655", "unicode_decimal": 58965}, {"icon_id": "29387784", "name": "rosesql", "font_class": "rosesql", "unicode": "e631", "unicode_decimal": 58929}, {"icon_id": "29387785", "name": "ubisql", "font_class": "ubisql", "unicode": "e632", "unicode_decimal": 58930}, {"icon_id": "28657710", "name": "starrocks", "font_class": "starrocks", "unicode": "e630", "unicode_decimal": 58928}, {"icon_id": "28212396", "name": "flink", "font_class": "flink", "unicode": "e62f", "unicode_decimal": 58927}, {"icon_id": "27011406", "name": "tidb", "font_class": "tidb", "unicode": "e62e", "unicode_decimal": 58926}, {"icon_id": "26553808", "name": "清空缓存", "font_class": "free", "unicode": "e666", "unicode_decimal": 58982}, {"icon_id": "26515413", "name": "clickhouse", "font_class": "clickhouse", "unicode": "e62a", "unicode_decimal": 58922}, {"icon_id": "26515414", "name": "kafka", "font_class": "kafka", "unicode": "e62b", "unicode_decimal": 58923}, {"icon_id": "26515415", "name": "HBASE", "font_class": "hbase", "unicode": "e62c", "unicode_decimal": 58924}, {"icon_id": "26515416", "name": "elasticserch", "font_class": "elasticsearch", "unicode": "e62d", "unicode_decimal": 58925}, {"icon_id": "13008191", "name": "Oracle", "font_class": "oracle", "unicode": "e622", "unicode_decimal": 58914}, {"icon_id": "26142595", "name": "lock", "font_class": "lock", "unicode": "e623", "unicode_decimal": 58915}, {"icon_id": "26125254", "name": "partition", "font_class": "partition", "unicode": "e61b", "unicode_decimal": 58907}, {"icon_id": "26125303", "name": "table", "font_class": "table", "unicode": "e61d", "unicode_decimal": 58909}, {"icon_id": "26125308", "name": "mysql", "font_class": "mysql", "unicode": "e624", "unicode_decimal": 58916}, {"icon_id": "26125309", "name": "pgsql", "font_class": "pgsql", "unicode": "e625", "unicode_decimal": 58917}, {"icon_id": "26125310", "name": "unlock", "font_class": "unlock", "unicode": "e626", "unicode_decimal": 58918}, {"icon_id": "24632917", "name": "extend-info", "font_class": "extend-info", "unicode": "e617", "unicode_decimal": 58903}, {"icon_id": "24632725", "name": "data-change", "font_class": "data-change", "unicode": "e613", "unicode_decimal": 58899}, {"icon_id": "24632727", "name": "user", "font_class": "user", "unicode": "e614", "unicode_decimal": 58900}, {"icon_id": "24632345", "name": "refresh", "font_class": "refresh", "unicode": "e610", "unicode_decimal": 58896}, {"icon_id": "24632348", "name": "config", "font_class": "config", "unicode": "e619", "unicode_decimal": 58905}, {"icon_id": "24632349", "name": "publish", "font_class": "publish", "unicode": "e61a", "unicode_decimal": 58906}, {"icon_id": "24632351", "name": "expand", "font_class": "expand", "unicode": "e61c", "unicode_decimal": 58908}, {"icon_id": "24632353", "name": "metadata", "font_class": "metadata", "unicode": "e61e", "unicode_decimal": 58910}, {"icon_id": "24632354", "name": "change-order", "font_class": "change-order", "unicode": "e61f", "unicode_decimal": 58911}, {"icon_id": "24632355", "name": "base-info", "font_class": "base-info", "unicode": "e620", "unicode_decimal": 58912}, {"icon_id": "24632356", "name": "databus", "font_class": "databus", "unicode": "e621", "unicode_decimal": 58913}]}