
export default function (ctx) {
  const tableColumns = [
    {
      title: 'Schema',
      dataIndex: 'schema_name',
      key: 'schema_name',
      width: 150
    },
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name'
      // width: 200
    },
    {
      title: '数据量',
      dataIndex: 'table_rows',
      key: 'table_rows',
      width: 100
    },
    {
      title: '分区类型',
      dataIndex: 'partition_type',
      key: 'partition_type',
      width: 150
    },
    {
      title: '分区键',
      key: 'part_column',
      dataIndex: 'part_column',
      width: 200
    },
    {
      title: '表状态',
      key: 'is_onlie',
      dataIndex: 'is_onlie',
      scopedSlots: { customRender: 'is_onlie' },
      width: 150
    },
    {
      title: '统计信息收集时间',
      key: 'last_collect_time',
      dataIndex: 'last_collect_time'
      // width: 250
    },
    {
      title: '表注释',
      key: 'comment',
      dataIndex: 'comment',
      scopedSlots: { customRender: 'comment' }
      // width: 200
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 150,
      fixed: 'right'
    }
  ];

  const indexColumns = (params = {}) => {
    const { combineInfo = {} } = params;
    const { index_name: indexName } = combineInfo;
    return [
      // {
      //   title: '表名',
      //   dataIndex: 'table_name',
      //   key: 'table_name',
      //   width: 200,
      //   customRender: (value, row, index) => {
      //     let obj = {
      //       children: value,
      //       attrs: {}
      //     };
      //     if (tableName) {
      //       let uid = value + '_' + index;
      //       let matchItem = tableName[uid];
      //       if (matchItem) {
      //         obj.attrs.rowSpan = matchItem.rowSpan;
      //       }
      //     }
      //     return obj;
      //   }
      // },
      {
        title: '索引名称',
        dataIndex: 'index_name',
        key: 'index_name',
        // width: 200,
        customRender: (value, row, index) => {
          let obj = {
            children: value,
            attrs: {}
          };
          if (indexName) {
            let uid = value + '_' + index;
            let matchItem = indexName[uid];
            if (matchItem) {
              obj.attrs.rowSpan = matchItem.rowSpan;
            }
          }
          return obj;
        }
      },
      {
        title: '索引类型',
        key: 'unique_name',
        dataIndex: 'unique_name',
        width: 200
      },
      {
        title: '索引字段',
        dataIndex: 'column_name',
        key: 'column_name',
        width: 100
      },
      {
        title: '字段类型',
        key: 'data_type',
        dataIndex: 'data_type',
        width: 100
      },
      {
        title: '字段位置',
        key: 'column_position',
        dataIndex: 'column_position',
        width: 100
      },
      {
        title: '区分度',
        key: 'cardinality',
        dataIndex: 'cardinality',
        width: 100
      }
    ];
  };
  const fieldColumns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id',
    //   width: 60
    // },
    {
      title: '字段名',
      dataIndex: 'column_name',
      key: 'column_name'
      // width: 200
    },
    {
      title: '数据类型',
      dataIndex: 'column_type',
      key: 'column_type',
      width: 200
    },
    {
      title: '可空',
      dataIndex: 'is_nullable',
      key: 'is_nullable',
      customRender: (text, record, index) => {
        return text == 'YES' ? '是' : '否'
      },
      width: 60
    },
    {
      title: '自增',
      key: 'auto_increment',
      dataIndex: 'auto_increment',
      customRender: (text, record, index) => {
        return text == 'YES' ? '是' : '否'
      },
      width: 60
    },
    {
      title: '缺省值',
      key: 'column_default',
      dataIndex: 'column_default'
      // scopedSlots: { customRender: 'is_onlie' },
      // width: 200
    },
    {
      title: '备注',
      key: 'column_comment',
      dataIndex: 'column_comment'
      // width: 200
    }
  ];

  return {
    tableColumns,
    indexColumns,
    fieldColumns
  };
};
