<template>
  <a-modal
    v-model="visible"
    title="详情"
    wrapClassName="audit-log-tracking-detail"
    width="50%"
    @cancel="onCancel"
    :footer="null"
  >
    <a-spin :spinning="spinning">
      <div class="content">
        <json-viewer :value="jsonData" :expand-depth="5" sort expanded copyable boxed>
        </json-viewer>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import JsonViewer from 'vue-json-viewer';
export default {
  components: { JsonViewer },
  props: {},
  data() {
    return {
      jsonData: [],
      visible: false,
      spinning: false
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    show(record = {}) {
      this.visible = true;
      this.jsonData = record.request_params;
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    }
  }
};
</script>

<style scoped lang="less">
</style>
