<template>
  <!-- 用户 -->
  <div style="padding: 0">
    <a-dropdown
      v-model="visible"
      placement="bottomRight"
      overlayClassName="tool-user-dropdown"
    >
      <div class="tool-user">
        <span>
          <a-avatar icon="user" size="small" />
        </span>
      </div>
      <a-menu slot="overlay">
        <a-menu-item key="1">
          <div class="title-box">
            <a-avatar icon="user" />
            <div>
              <span>{{ chName }}</span>
              <span>{{ userName }}</span>
            </div>
          </div>
          <div class="button-box">
            <a @click="onModifyPwd" v-if="!isThirdLogin">修改密码</a>
            <a-divider type="vertical" v-if="!isThirdLogin" />
            <a @click="onLogout">退出登录</a>
          </div>
        </a-menu-item>
      </a-menu>
    </a-dropdown>
    <!-- 修改密码弹窗 -->
    <ModifyPwd ref="modifyPwd" @save="onSave" />
  </div>
</template>

<script>
import { modifyPwd } from '@/api/common';
import ModifyPwd from '../ModifyPwd';
import { Base64 } from 'js-base64';
import { getLogout } from '@/api/login';
export default {
  components: { ModifyPwd },
  props: {},
  data() {
    return {
      visible: false
    };
  },
  computed: {
    userName() {
      return this.$store.state.account.user.name;
    },
    chName() {
      return this.$store.state.account.user.ch_name;
    },
    isThirdLogin() {
      const bool = _.get(this.$store.state.project, 'isThirdLogin');
      return bool;
    }
  },
  mounted() {},
  created() {},
  methods: {
    // 打开修改密码弹窗
    onModifyPwd() {
      const { modifyPwd } = this.$refs;
      this.visible = false;
      modifyPwd.show();
    },
    // 保存
    onSave(data = {}) {
      const { userPassword, password, passwordConfirm } = data;
      if (password !== passwordConfirm) {
        this.$message.error('新密码和确认密码不一致！');
        return;
      }
      // 请求
      this.$showLoading();
      modifyPwd({
        password: Base64.encode(password),
        user_password: Base64.encode(userPassword)
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: '修改密码成功！' });
            this.visible = false;
            this.$refs.modifyPwd.hide();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 登出
    onLogout() {
      getLogout().then(res => {
        if (_.get(res, 'data.code') == 0) {
          let data = _.get(res, 'data.data');
          // 跳转回登录
          window.Login.go(data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.tool-user-dropdown {
  top: 64px !important;
  > ul {
    border-radius: 10px;
    transform: translateX(-24px);
    li {
      text-align: center;
    }
    .ant-dropdown-menu-item:hover,
    .ant-dropdown-menu-submenu-title:hover {
      background-color: #fff;
      cursor: default;
    }
  }
}
.tool-user {
  padding: 0 16px;
  > span {
    display: flex;
    align-items: center;

    .ant-avatar {
      // margin-right: 8px;
      background: @primary-4;
    }
  }
}
.title-box {
  display: flex;
  justify-content: center;
  align-items: center;
  .ant-avatar.ant-avatar-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
  }
  > div {
    margin-left: 8px;
    display: flex;
    flex-direction: column;
    > span {
      font-size: 14px;
      &:last-child {
        color: #a1a1a1;
      }
    }
  }
}
.button-box {
  margin-top: 16px;
  font-size: 12px;
  > a {
    cursor: pointer;
  }
}
/deep/ .ant-dropdown {
  width: 180px;
}
</style>
