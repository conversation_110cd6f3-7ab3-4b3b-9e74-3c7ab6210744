<template>
  <div class="order-detail">
    <div class="frame-button-wrapper">
      <a-button @click="toBack" class="highlight">返回</a-button>
    </div>
    <!-- </div> -->
    <!-- 上面基础信息区域 -->
    <a-card style="width: 100%" class="common-page-card" :bordered="false">
      <div class="review-detail-info">
        <div class="left-block">
          <span class="datasource-content">
            <span>数据源</span>
            <div v-if="statisticsInfo.data_source_name">
              <span :class="statisticsInfo.env == 'TEST' ? 'test' : 'prod'">{{
                statisticsInfo.env == 'TEST' ? '测试' : '生产'
              }}</span>
              <DbImg
                :value="statisticsInfo.db_type"
                :schemaName="
                  statisticsInfo.data_source_name +
                  '(' +
                  statisticsInfo.db_url +
                  ')'
                "
                mode="ellipsis"
              />
            </div>
            <div v-else>{{ '--' }}</div>
          </span>
          <span
            class="tag-box"
            v-if="statisticsInfo.projects && statisticsInfo.projects.length > 0"
          >
            <span>关联项目</span>
            <a-tag class="tooltip">
              <span v-if="statisticsInfo.projects.length == 1">{{
                statisticsInfo.projects[0]
              }}</span>
              <a-tooltip v-else>
                <template slot="title">
                  <span>{{ statisticsInfo.projects.toString() }}</span>
                </template>
                <span>{{ statisticsInfo.projects[0] + '; ' + '...' }}</span>
              </a-tooltip>
            </a-tag>
          </span>
          <span class="tag-box">
            <span>DBA负责人</span>
            <a-tag
              class="tooltip"
              v-if="
                statisticsInfo.dba_leader &&
                statisticsInfo.dba_leader.length > 0
              "
            >
              <span v-if="statisticsInfo.dba_leader.length == 1">
                {{ statisticsInfo.dba_leader[0] }}
              </span>
              <span v-else>
                <a-tooltip>
                  <template slot="title">
                    <span>{{ statisticsInfo.dba_leader | handleTitle }}</span>
                  </template>
                  <span>{{ statisticsInfo.dba_leader | handleContent }}</span>
                </a-tooltip>
              </span>
            </a-tag>
            <span v-else>{{ '--' }}</span>
          </span>
          <span class="tag-box">
            <span>项目组负责人</span>
            <a-tag
              class="tooltip"
              v-if="
                statisticsInfo.project_group_leader &&
                statisticsInfo.project_group_leader.length > 0
              "
            >
              <span v-if="statisticsInfo.project_group_leader.length == 1">
                {{ statisticsInfo.project_group_leader[0] }}
              </span>
              <span v-else>
                <a-tooltip>
                  <template slot="title">
                    <span>
                      {{
                        statisticsInfo.project_group_leader | handleTitle
                      }}</span
                    >
                  </template>
                  <span>{{
                    statisticsInfo.project_group_leader | handleContent
                  }}</span>
                </a-tooltip>
              </span>
            </a-tag>
            <span v-else>{{ '--' }}</span>
          </span>
        </div>
        <div class="right-block">
          <div class="right-block-count">
            <span> <custom-icon type="clock-circle" />待审核</span>
            <span class="wait_audit">{{
              statisticsInfo.wait_audit_count || 0
            }}</span>
          </div>

          <div class="right-block-count">
            <span> <custom-icon type="lu-icon-whitelist" />白名单</span>
            <span class="white-list">{{
              statisticsInfo.white_count || 0
            }}</span>
          </div>

          <div class="right-block-count">
            <span> <custom-icon type="tool" />整改中</span>
            <span class="rectifying">{{
              statisticsInfo.rectifying_count || 0
            }}</span>
          </div>

          <!-- <div class="right-block-count">
            <span> <custom-icon type="stop" />已失效</span>
            <span>{{ statisticsInfo.expired_count || 0 }}</span>
          </div> -->
        </div>
      </div>
    </a-card>

    <!-- 搜索内容 -->
    <div class="search-block">
      <div :class="['summary', isCollapse && 'collapse']" @click="onCollapse">
        <div class="title">
          <span>查询</span>
          <div>
            <a-input-search
              placeholder="搜索标签ID"
              @click="(e) => e.stopPropagation()"
              @search="onSearch"
              v-if="isCollapse"
            ></a-input-search>
            <span class="search" v-if="isCollapse">高级查询</span>
            <custom-icon :type="isCollapse ? 'down' : 'up'"></custom-icon>
          </div>
        </div>
      </div>

      <div
        :class="['search-content', isCollapse && 'collapse']"
        v-show="!isCollapse"
      >
        <SearchArea
          v-bind="searchParams || {}"
          @reset="refresh"
          @search="search"
          ref="search"
          searchMode="switch"
          :iconCombine="false"
          :isShowExpandIcon="false"
          :searchData="searchData || {}"
        ></SearchArea>
      </div>
    </div>
    <!-- 表格内容 -->
    <div class="review-detail-table-block">
      <Table
        ref="table"
        v-bind="tableParams"
        :dataSource="dataSource"
        class="new-view-table"
        @selectChange="selectChange"
        :rowSelection="rowSelection"
      >
        <template slot="tableTopLeft">
          <div>SQL列表</div>
        </template>

        <template slot="tableTopRight">
          <a-button class="review-detail-btn" type="primary" @click="bacthPass"
            >批量通过</a-button
          >
          <a-button @click="bacthReject" class="highlight">批量驳回</a-button>
        </template>

        <a
          slot="label_id"
          slot-scope="{ text, record }"
          @click="toDetail(record)"
          class="label-id"
          ><LimitLabel :limit="24" :label="text" :needCopy="true"></LimitLabel
        ></a>
        <template slot="limit-tag" slot-scope="{ text }">
          <LimitTags
            :tags="text ? text.map((item) => ({ label: item })) : []"
            :limit="1"
            mode="numTag"
          ></LimitTags>
        </template>
        <template slot="label_type" slot-scope="{ text, record }">
          <span> {{ text | lableTypeText }}</span>
        </template>
        <template slot="risk" slot-scope="{ record, text }">
          <RiskLevel :aiComment="record.ai_comment || {}" :value="text" />
        </template>
        <template slot="schema" slot-scope="{ text }">
          <span>
            <custom-icon type="database" style="margin-right: 8px" />
            <span>{{ text }}</span>
          </span>
        </template>
        <div slot="label_status" slot-scope="{ text }" class="dba-status">
          <StatusTag type="label" :status="text" />
        </div>
        <div slot="sql_source" slot-scope="{ text }" class="sql-source">
          <LimitLabel
            :limit="24"
            :label="text"
            :needCopy="true"
            format="sql"
          ></LimitLabel>
        </div>
        <template slot="audit_user" slot-scope="{ record, text }">
          <a-tooltip v-if="record.ch_audit_user" placement="topLeft">
            <template slot="title">
              <span>{{ text }}</span>
            </template>
            <div class="des">
              <a-icon type="user" />
              <span>{{ record.ch_audit_user }}</span>
            </div>
          </a-tooltip>
          <span v-else>{{ text || '--' }}</span>
        </template>
        <template slot="created_by" slot-scope="{ record, text }">
          <a-tooltip v-if="record.ch_created_by" placement="topLeft">
            <template slot="title">
              <span>{{ text }}</span>
            </template>
            <div class="des">
              <a-icon type="user" />
              <span>{{ record.ch_created_by }}</span>
            </div>
          </a-tooltip>
          <span v-else>{{ text || '--' }}</span>
        </template>
        <custom-btns-wrapper slot="action" slot-scope="{ record }" :limit="3">
          <a
            @click="pass(record.id)"
            :disabled="record.label_status !== 0"
            actionBtn
            >通过</a
          >
          <a
            @click="reject(record.id)"
            :disabled="record.label_status !== 0"
            actionBtn
          >
            驳回
          </a>
        </custom-btns-wrapper>
      </Table>
    </div>
  </div>
</template>

<script>
import SearchArea from '@/components/Biz/SearchArea/new';
import Table from '@/components/Table';
import LimitTags from '@/components/LimitTags';
import Status from '@/components/Biz/Status';
import LimitLabel from '@/components/LimitLabel';
import RiskLevel from '@/components/Biz/ReviewDetail/RiskLevel';
import StatusTag from '@/components/Biz/Status/Tag';
import config from './config';
import {
  databaseReviewDetailPass,
  databaseReviewDetailReject,
  databaseReviewDetailBatch
} from '@/api/order';

import tableSelectAll from '@/mixins/tableSelectAll';
const getCheckboxProps = record => ({
  props: {
    disabled: record.label_status !== 0
  }
});
export default {
  mixins: [tableSelectAll({ getCheckboxProps })],
  components: {
    Table,
    Status,
    RiskLevel,
    LimitTags,
    LimitLabel,
    StatusTag,
    SearchArea
  },
  props: {},
  data() {
    this.config = config(this);
    this.id = this.$route.params.id;
    return {
      isCollapse: false,
      searchData: {},
      searchParams: {
        fields: this.config.dateSourceFields
      },
      dataSource: [],
      tableParams: {
        url: '/sqlreview/review/database_review/detail',
        reqParams: { data_source_id: this.id },
        columns: this.config.dateSourceColumns,
        rowKey: 'id',
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' },
        loaded: this.onTableLoaded
      },
      spinning: false,
      placeholder: '搜索SQL',
      dba_status: this.$route.query.status,
      statisticsInfo: {}
    };
  },
  computed: {},
  mounted() {},
  created() {},
  beforeDestroy() {},
  methods: {
    onTableLoaded(req, res) {
      this.statisticsInfo = res.data.data.statistics_info;
      const resData = _.get(res, 'data.data');
      const results = resData.results;
      const ids = results.map(item => item.id);
      this.pageKeys = ids;
      this.percent = resData.label_rate;
      this.dataSource = results;
      this.count = resData.count;
      this.timestamp = resData.timestamp;
      if (this.isSelectAll) {
        ids.forEach(item => {
          if (!this.excludes.includes(item)) {
            this.selectedRowKeys.push(item);
          }
        });
      }
    },
    onCollapse() {
      this.isCollapse = !this.isCollapse;
    },
    bacthReject() {
      const params = {
        data_source_id: this.id,
        examine_list: this.isSelectAll ? this.excludes : this.includes,
        is_select_all: this.isSelectAll ? 1 : 0,
        audit_status: -1
      };
      databaseReviewDetailBatch(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    bacthPass() {
      const params = {
        data_source_id: this.id,
        examine_list: this.isSelectAll ? this.excludes : this.includes,
        is_select_all: this.isSelectAll ? 1 : 0,
        audit_status: 1
      };
      databaseReviewDetailBatch(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    toDetail(record, e) {
      this.$router.push({
        name: 'orderReview',
        params: { id: record.id },
        query: { activeKey: 'datasource', data_source_id: this.id }
      });
    },
    // 返回
    toBack() {
      this.$router.push({
        name: 'orderList',
        query: { activeKey: 'datasource' }
      });
    },
    // 通过
    pass(id) {
      this.$showLoading();
      databaseReviewDetailPass({ id: id, audit_status: 1 })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    reject(id) {
      this.$showLoading();
      databaseReviewDetailReject({ id, audit_status: -1 })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 重置
    reset(data) {
      this.isSelectAll = false;
      this.tableCheckAll = false;
      this.tableIndeterminate = false;
      this.selectedRowKeys = [];
      this.count = null;
      this.includes = [];
      this.excludes = [];
      this.pageKeys = [];
    },
    refresh() {
      const { table } = this.$refs;
      table.onReset();
      this.reset();
    },
    tableSearch(params) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        sql_text: params
      });
      table.refresh();
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
      this.$store.commit('common/setSearchCache', {
        orderDetail: data
      });
    },
    onSearch(value, e) {
      e.stopPropagation();
      const data = { label_id: value };
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    }
  },
  filters: {
    lableTypeText(value) {
      let obj = {
        0: 'SQL_ID',
        1: 'XML_TEXT',
        2: 'XML_ID',
        3: 'TABLE',
        4: 'JAVA',
        5: 'PROCEDURE'
      };
      return obj[value];
    },
    handleTitle(data) {
      return data.join();
    },
    handleContent(data) {
      const res = data && data.length > 1 ? data[0] + '...' : data[0];
      return res;
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
// /deep/.ant-table-pagination.ant-pagination {
//   margin: 24px 0 0 0;
// }

.order-detail {
  .search-block {
    border-radius: 16px;
    margin-bottom: 16px;
    background-color: #fff;
    .summary {
      padding: 16px 24px;
      border-radius: 16px;
      cursor: pointer;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        > div {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          .ant-input-search {
            width: 200px;
          }
          .search {
            font-size: 14px;
            color: #1f1f1f;
            font-weight: 400;
            padding: 0 16px;
            cursor: pointer;
          }
          > .anticon {
            font-size: 14px;
            color: #8c8c8c;
            margin-right: 0;
          }
        }
      }
    }
    .search-content {
      border-top: 1px solid #f0f0f0;
      &.collapse {
        .title {
          border-bottom: none;
        }
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        > .anticon {
          font-size: 14px;
          color: #8c8c8c;
          margin-right: 0;
        }
      }
      /deep/.search-area {
        box-shadow: none;
        border: none;
      }
    }
  }
  .common-page-card {
    margin-bottom: 12px;
    border-radius: 8px;
    padding: 16px 24px;
    .review-detail-info {
      display: flex;
      justify-content: space-between;
      .left-block {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        .datasource-content {
          display: flex;
          align-items: center;
          margin-right: 24px;
          padding: 0 0 4px 0;
          > span {
            color: #8c8c8c;
          }
          > div {
            display: flex;
            align-items: center;
            margin-left: 8px;
            > span {
              font-family: PingFangSC-Regular;
              font-size: 12px;
              border-radius: 4px;
              margin: 0 4px 0 0;
              padding: 0 4px;
            }
            .test {
              background: #f6ffed;
              border: 1px solid rgba(183, 235, 143, 1);
              color: #52c41a;
            }
            .prod {
              background: #fff7e6;
              border: 1px solid rgba(255, 213, 145, 1);
              color: #fa8c16;
            }
            /deep/.limit-label {
              width: 120px;
            }
          }
        }
        .tag-box {
          margin-right: 24px;
          padding: 2px 0;
          > span {
            font-family: PingFangSC-Regular;
            color: #8c8c8c;
          }
          .ant-tag {
            color: #1f1f1f;
            cursor: pointer;
          }
        }
      }
      .right-block {
        display: flex;
        border: 1px solid rgba(240, 240, 240, 1);
        border-radius: 4px;
        height: 48px;
        .right-block-count {
          background: #ffffff;
          &:not(:last-child) {
            border-right: 1px solid rgba(240, 240, 240, 1);
          }
          padding: 0 16px;
          // height: 48px;
          // margin-left: 16px;
          white-space: nowrap;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-radius: 4px;
          span {
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: #8c8c8c;
            &:first-child {
              display: flex;
              align-items: center;
              margin-right: 24px;
              .anticon {
                margin-right: 4px;
                font-size: 18px;
              }
            }
            &:last-child {
              font-family: PingFangSC-Regular;
              font-size: 24px;
            }
          }
          .wait_audit {
            color: #1677ff;
          }
          .white-list {
            color: #52c41a;
          }
          .rectifying {
            color: #9254de;
          }
        }
      }
    }
  }

  .review-detail-table-block {
    background: #fff;
    border-radius: 16px;
    /deep/.custom-table {
      .search-area-wrapper {
        display: flex;
        justify-content: space-between;
        padding: 16px 24px;
        .custom-table-top-left {
          display: flex;
        }
        .custom-table-top-right {
          display: flex;
          align-items: center;
          .ant-btn {
            margin-left: 12px;
            // background: @primary-6;
            // border-radius: 6px;
            // border: 1px solid rgba(22, 119, 255, 1);
            // box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
            // color: #ffffff;
            // &:last-child {
            //   background: #ffffff;
            //   border: 1px solid rgba(217, 217, 217, 1);
            //   box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.02);
            //   color: #1f1f1f;
            // }
          }
        }
      }
      .label-id {
        .limit-label {
          pre {
            color: @primary-6 !important;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1440px) {
  .order-detail {
    .review-detail-info {
      .review-detail-main-info {
        .right-block {
          .right-block-content {
            > div {
              margin-left: 16px;
              & span:first-child {
                font-size: 14px;
              }
              & span:last-child {
                font-size: 20px;
              }
              .error {
                font-size: 20px;
              }
              .pass {
                font-size: 20px;
              }
            }
            .ant-divider-vertical {
              margin-left: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
