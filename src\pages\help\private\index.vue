<template>
  <a-tabs tabPosition="left" v-model="activeKey" v-if="params.tabs.length > 0">
    <a-tab-pane v-for="item in params.tabs" :key="item.key" :tab="item.title">
      <component :is="item.key" />
    </a-tab-pane>
  </a-tabs>
  <custom-empty v-else />
</template>

<script>
import BigFileUpload from './components/BigFileUpload';
import Rules from './components/Rules';
export default {
  components: { BigFileUpload, Rules },
  props: {},
  data() {
    return {
      activeKey: 'BigFileUpload',
      params: {
        title: '测试页',
        tabs: [
          {
            title: 'BigFileUpload',
            key: 'BigFileUpload'
          },
          {
            title: 'Rules',
            key: 'Rules'
          }
        ]
      }
    };
  },
  mounted() {},
  created() {},
  methods: {}
};
</script>

<style lang="less" scoped></style>
