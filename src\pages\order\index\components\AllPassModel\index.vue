<template>
  <a-modal
    v-model="visible"
    okText="确定"
    cancelText="取消"
    @ok="save"
    @cancel="onCancel"
    width="45%"
    :dialogStyle="{ minWidth: '300px', maxWidth: '400px' }"
    wrapClassName="order-index-all-pass-modal"
  >
    <a-spin :spinning="spinning" class="content-box">
      <div class="title">
        <a-icon type="question-circle" />
        <span>确认全部通过吗？</span>
      </div>
      <div class="subtitle">将{{count}}条SQL全部审批通过吗？</div>
    </a-spin>
  </a-modal>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      visible: false,
      spinning: false,
      count: null,
      searchParams: {}
    };
  },
  methods: {
    show(num, params) {
      this.visible = true;
      this.count = num;
      this.searchParams = params;
    },
    onCancel() {
      this.visible = false;
    },
    save() {
      this.onCancel();
      this.$emit('onSave');
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.order-index-all-pass-modal {
  .content-box {
    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      margin-bottom: 16px;
      color: @font-color-strong;
      vertical-align: middle;
      .anticon-question-circle {
        margin-right: 8px;
        // color: rgb(246, 176, 84);
        color: rgb(244, 153, 35);
        font-size: 32px;
      }
    }
    .subtitle {
      margin-bottom: 8px;
      margin-left: 40px;
    }
  }
}
/deep/ .ant-radio-wrapper {
  border: none !important;
  padding: 12px 40px !important;
}
</style>
