<template>
  <!-- 新建项目抽屉 -->
  <a-drawer
    title="SQL审核记录"
    :visible="visible"
    @close="hide"
    width="720px"
    wrapClassName="quick-audit-history-drawer"
  >
    <div class="export-all">
      <!-- <a-button @click="download">导出全部</a-button> -->
      <a-dropdown
        overlayClassName="pre-audit-history-overlay"
        :getPopupContainer="getPopupContainer"
        class="download"
      >
        <a-menu
          class="download-menu"
          slot="overlay"
          @click="(event) => handleMenuClick(event)"
        >
          <a-menu-item key="1"> 导出excel </a-menu-item>
          <a-menu-item key="2"> 导出html </a-menu-item>
        </a-menu>
        <a-button icon="download" class="highlight"> 导出全部 </a-button>
      </a-dropdown>
    </div>
    <!-- 内容区域 -->
    <a-spin :spinning="spinning">
      <div class="search-area">
        <Form
          ref="form"
          v-bind="formParams"
          :formData="formData"
          :iconCombine="true"
          class="search-form"
        ></Form>
        <div class="seach-area-btns">
          <a-button @click="search" type="primary">查询</a-button>
          <a-tooltip>
            <template slot="title">重置</template>
            <custom-icon type="lu-icon-clean" @click="reset" />
          </a-tooltip>
        </div>
      </div>
      <Table
        ref="table"
        class="new-view-table small-size"
        v-bind="tableParams || {}"
        :dataSource="dataSource || []"
      >
        <template slot="sql_text" slot-scope="{ record, text }">
          <a class="sql-text-box" @click="getDetail(record)">
            <custom-icon
              v-if="record.risk == 'low'"
              type="lu-icon-alarm"
              style="color: #f29339"
            />
            <custom-icon
              v-else-if="record.risk == 'high'"
              type="lu-icon-alarm"
              style="color: #e71d36"
            />
            <custom-icon
              v-else-if="record.risk == 'error'"
              type="lu-icon-unusual"
              style="color: #71717a"
            />
            <custom-icon v-else type="lu-icon-success" />
            <LimitLabel
              :label="text || ''"
              :ignoreFirstLineComment="true"
              mode="ellipsis"
              format="sql"
              :popoverProps="{
                overlayClassName: 'quik-audit-small-size'
              }"
            ></LimitLabel>
          </a>
        </template>
        <template slot="audit_type" slot-scope="{ text, record }">
          <a-tooltip>
            <template slot="title">
              {{ text == 'online' ? '在线审核' : '离线审核' }}
            </template>
            <custom-icon
              :type="text == 'online' ? 'link' : 'disconnect'"
              style="cursor: pointer"
            ></custom-icon>
          </a-tooltip>
        </template>
      </Table>
    </a-spin>

    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        minHeight: '60px',
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1
      }"
    ></div>
  </a-drawer>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
import Table from '@/components/Table';
import Form from '@/components/Form';
import config from './config';
import { historyDownload } from '@/api/quick';
export default {
  components: {
    Form,
    Table,
    LimitLabel
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      spinning: false,
      formData: {},
      formParams: {
        layout: 'horizontal',
        multiCols: 3,
        fields: this.config.searchFields
      },
      dataSource: [],
      tableParams: {
        url: '/sqlreview/quick_audit/quick_audit_hst_list',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id',
        pagination: {
          pageSize: 20
        },
        scroll: { x: 500 }
      }
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(record) {
      this.visible = true;
      this.$set(this.tableParams, 'reqParams', { _t: +new Date() });
    },
    hide() {
      this.visible = false;
      this.reset();
    },
    // 查询
    search() {
      const { table, form } = this.$refs;
      const data = form.getData();
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    },
    // 重置
    reset() {
      this.formData = {};
      const { table } = this.$refs;
      table.searchParams = {};
      table.refresh();
    },
    getPopupContainer() {
      return document.getElementById('rootContent');
    },
    handleMenuClick(e) {
      this.download(e.key);
    },
    download(key) {
      this.$showLoading({
        tips: `下载中...`
      });
      const { form } = this.$refs;
      const data = form.getData();
      const params = { ...data, export_type: 1, export_file_type: key };
      historyDownload(params)
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    },
    getDetail(record) {
      this.$emit('getDetail', record);
    }
  }
};
</script>
<style lang="less">
// popover美化
.ant-popover {
  &.quik-audit-small-size {
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 400px;
        max-height: 200px;
      }
    }
  }
}
</style>
<style lang="less">
.quick-audit-history-drawer {
  .ant-drawer-content-wrapper {
    .ant-drawer-content {
      overflow: inherit;
      .ant-drawer-header {
        background: #fff;
        .ant-drawer-title {
          font-size: 20px;
          color: #27272a;
          font-weight: 600;
          margin-left: 32px;
        }
        .ant-drawer-close {
          left: 8px;
          .anticon {
            font-size: 16px;
            color: #27272a;
          }
        }
      }
      .ant-drawer-body {
        padding: 0 24px;
        overflow: auto;
        height: 94%;
        .export-all {
          position: absolute;
          right: 0;
          top: 0;
          width: auto;
          padding: 10px 16px;
          background: #fff;
          text-align: right;
          .ant-btn {
            border-radius: 8px;
          }
        }
        .ant-spin-container {
          .search-area {
            display: flex;
            justify-content: space-between;
            margin-bottom: -4px;
            margin-top: 8px;

            .ant-form {
              flex-grow: 1;
              overflow: hidden;
              * {
                transition: none;
              }
              > .ant-row {
                display: flex;
                > .ant-col {
                  vertical-align: top;
                  .ant-form-item {
                    display: flex;
                    margin: 0;
                    .ant-form-item-label {
                      display: none;
                    }
                    .ant-form-item-control-wrapper {
                      flex-grow: 1;
                      flex: auto;
                      width: auto;
                    }
                  }
                }
              }
            }
            .seach-area-btns {
              padding-top: 4px;
              white-space: nowrap;
              // .ant-btn {
              //   padding: 2px 12px;
              //   border-radius: 4px !important;
              //   border: 1px solid #a5d9f8;
              //   > span {
              //     font-size: 14px;
              //     color: #fff;
              //     font-weight: 400;
              //   }
              // }
              .anticon {
                font-size: 16px;
                margin: 0 12px;
                border-radius: 50%;
                &:hover {
                  color: #000;
                  cursor: pointer;
                }
              }
            }

            &.icon-combine {
              .ant-form {
                > .ant-row {
                  > .ant-col {
                    padding-left: 24px !important;
                    padding-right: 0 !important;
                    .ant-form-item {
                      .ant-form-item-label {
                        max-width: 0;
                        min-width: 0;
                      }
                      .ant-form-item-control-wrapper {
                        flex-grow: 1;
                        flex: auto;
                        width: auto;
                        max-width: 100%;
                        min-width: 310px;
                      }
                    }
                  }
                }
              }
              .seach-area-btns {
                margin-left: 24px;
              }
            }
          }
          .sql-text-box {
            display: flex;
            align-items: center;
            .anticon {
              margin-right: 8px;
            }
          }
          .ant-pagination {
            position: fixed;
            bottom: 0;
            right: 16px;
            z-index: 10;
            // .ant-pagination-options {
            //   .ant-pagination-options-quick-jumper {
            //     display: none;
            //   }
            // }
          }
          .anticon-disconnect {
            color: #bfbfbf;
          }
        }
      }
    }
  }
}
</style>
