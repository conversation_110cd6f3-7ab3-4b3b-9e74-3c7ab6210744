<template>
  <span :class="['custom-btns-wrapper', `limit${limit}`]" :ukey="ukey">
    <slot></slot>
    <a-popover
      placement="bottom"
      :overlayClassName="`custom-btns-wrapper-popover limit${limit}`"
      v-if="len > limit"
      @visibleChange="onVisibleChange"
    >
      <div slot="content" class="more-container" :ukey="ukey">
        <slot></slot>
      </div>
      <a>
        <VNode :node="() => more($createElement)" v-if="more" />
        <a-icon type="ellipsis" v-else />
        <!-- 更多
        <a-icon type="down"></a-icon>-->
      </a>
    </a-popover>
  </span>
</template>

<script>
// import _ from 'lodash';

export default {
  props: {
    limit: {
      type: Number,
      default: 2
    }
  },
  components: {},
  data() {
    return {
      len: 0,
      authInfo: null,
      popoverLoaded: false,
      ukey: _.uniqueId('custom-btns-wrapper'),
      more: _.get(window.COMP_CONFIG, 'BtnsWrapper.more')
    };
  },
  computed: {},
  mounted() {
    const el = this.$el;
    const btns = el.querySelectorAll('[actionBtn]:not(.auth-hide)');
    this.len = btns.length;
    this.setBtnsVisible();

    // 监听children变化
    this.observe();
  },
  updated() {
    this.setBtnsVisible();
  },
  destroyed() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  },
  methods: {
    $setAuthInfo(params = {}) {
      this.authInfo = params;
    },
    setBtnsVisible() {
      this.$nextTick(() => {
        let btns = this.$el.querySelectorAll('[actionBtn]');
        let count = 1;
        btns &&
          [...btns].forEach((item, index) => {
            item.classList.remove('hide');
            if (!item.classList.contains('auth-hide')) {
              if (count > this.limit) {
                item.classList.add('hide');
              }
              count++;
            }
          });

        const moreContainer = document.querySelector(
          `.more-container[ukey=${this.ukey}]`
        );
        if (moreContainer) {
          btns = moreContainer.querySelectorAll('[actionBtn]');
          count = 1;
          btns &&
            [...btns].forEach((item, index) => {
              item.classList.remove('hide');
              if (!item.classList.contains('auth-hide')) {
                if (count <= this.limit) {
                  item.classList.add('hide');
                }
                count++;
              }
            });
        }
      });
    },
    onVisibleChange(visible) {
      // console.log('onVisibleChange', this.authInfo, visible);
      if (!this.popoverLoaded) {
        if (this.authInfo) {
          this.$nextTick(() => {
            $Auth(this.authInfo, null, data => {
              const { type } = data;
              switch (type) {
                case 'ArrayMerge':
                  const ukey = this.ukey;
                  const popover = document.querySelector(
                    `.custom-btns-wrapper-popover .more-container[ukey=${ukey}]`
                  );
                  // console.log(ukey, popover, data, 888);
                  if (popover) {
                    AuthArrayMergeFilter(
                      popover.querySelectorAll('[authKey]'),
                      data
                    );
                  }
                  break;
                default:
                  break;
              }
            });
          });
        }

        this.popoverLoaded = visible;
      }
      this.$nextTick(() => {
        this.$emit(
          'popover',
          visible,
          document.querySelector(
            `.custom-btns-wrapper-popover .more-container[ukey=${this.ukey}]`
          )
        );
      });
      this.setBtnsVisible();
    },
    observe() {
      var el = this.$el;
      const MutationObserver =
        window.MutationObserver ||
        window.webkitMutationObserver ||
        window.MozMutationObserver;

      if (el && MutationObserver) {
        this.observer = new MutationObserver(m => {
          const btns = el.querySelectorAll('[actionBtn]:not(.auth-hide)');
          if (this.len !== btns.length) {
            this.len = btns.length;
          }
        });
        this.observer.observe(el, {
          attributes: true,
          childList: true,
          characterData: true,
          subtree: true
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.custom-btns-wrapper {
  .ant-divider {
    display: none;
  }
  [actionBtn] {
    display: flex !important;
    align-items: center;
    &.hide,
    &.auth-hide {
      display: none !important;
    }
    &::after {
      content: '';
      padding: 0;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      line-height: 1.5;
      list-style: none;
      background: #e8e8e8;
      position: relative;
      top: -0.06em;
      display: inline-block;
      width: 1px;
      height: 0.9em;
      margin: 0 8px;
      vertical-align: middle;
    }
    &:last-child {
      &::after {
        display: none;
      }
    }
  }
}
</style>
<style lang="less">
.custom-btns-wrapper-popover {
  .more-container {
    // text-align: center;
    .ant-divider {
      display: none;
    }
    [actionBtn] {
      display: block;
      &.hide,
      &.auth-hide {
        display: none !important;
      }
    }
  }
}
</style>
