<template>
  <a-card style="width:100%" class="common-page-card" title="xxxxxx" :bordered="false">
    <template slot="extra">
      <a-button type="primary" icon="plus" @click="add">新增数据库</a-button>
    </template>
    <SearchArea v-bind="searchParams" @reset="reset" @search="search"></SearchArea>
    <TableEdit ref="table" v-bind="tableParams"></TableEdit>
  </a-card>
</template>

<script>
import SearchArea from '@/components/SearchArea';
import TableEdit from '@/components/TableEdit';
import config from './config';
export default {
  components: {
    SearchArea,
    TableEdit
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 3
      },
      tableParams: {
        tyoe: '',
        url: '',
        modalEdit: true,
        method: 'post',
        rowKey: 'id',
        columns: this.config.columns,
        editConfig: this.config.editConfig,
        modalConfig: {
          title: 'xxxxx',
          width: 650
        }
      }
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    add() {
      this.$refs.table.addLine();
    },
    reset() {},
    search() {}
  }
};
</script>

<style scoped lang="less">
</style>
