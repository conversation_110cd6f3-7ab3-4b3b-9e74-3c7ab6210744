<!--
 * @Descripttion: asset
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2020-12-16 19:11:37
-->
<template>
  <span :class="truth ? 'report-truth' : 'report-truth report-truth-error'">
    <a-icon v-if="truth" type="check"/>
    <a-icon v-if="!truth" type="close" /> <span v-if="!truth" @click="onClick" >{{this.message}}</span>
  </span>
</template>
<script>
export default {
  name: 'report-truth',
  computed: {
    truth: function () {
      if (this.value) {
        if (['0', 'false', 'FALSE'].indexOf(this.value) > -1) {
          return false
        }
        return true
      }
      return false
    }
  },
  props: {
    action: {
      type: Function,
      required: true,
      default: () => () => {}
    },
    type: {
      type: String,
      required: true,
      default: () => ''
    },
    search: {
      type: Object,
      required: false,
      default: function () {
        return {}
      }
    },
    value: {
      type: [<PERSON><PERSON><PERSON>, String, Number],
      required: true,
      default: () => true
    },
    message: {
      type: String,
      required: false,
      default: () => '错误详情'
    }
  },
  methods: {
    getType () {
      let type = this.type.replace(/component:truth_/, '')
      return type
    },
    onClick () {
      if (this.action) {
        this.action({
          type: this.getType(),
          data: this.search,
          value: this.value
        })
      }
    }
  }
}
</script>
<style lang="less">
.report-truth {
  color: #00BF80;
  font-size: 18px;
  font-weight: bold;
}
.report-truth-error {
  color:#E9473A;
  i {
    vertical-align: middle;
  }
  span {
    cursor: pointer;
    font-size: 12px;
    padding: 5px 10px;
    background: rgba(255,115,104 ,.21);
    color: #E9473A;
    border-radius: 14px;
    vertical-align: middle;
    margin-left: 8px;
    &:hover {
      background: rgba(255,115,104 ,.41);
    }
  }
}
</style>
