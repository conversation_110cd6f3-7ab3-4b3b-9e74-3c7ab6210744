import Http from '@/utils/request';
import common from '@/utils/common';

const cache = {};
const httpCache = {};

// 请求回调
const httpCbk = function(key, resolve, reject) {
  const HttpPromise = httpCache[key];
  HttpPromise &&
    HttpPromise.then(res => {
      // const code = _.get(res, 'data.code')
      if (common.isSuccessCode(res)) {
        let data = _.get(res, 'data.data') || [];
        cache[key] = data;
        resolve(data);
      } else {
        reject(_.get(res, 'data.message'));
      }
    })
      .catch(e => {
        reject(_.get(e, 'response.data.message'));
      })
      .finally(() => {
        httpCache[key] = null;
      });
};

const request = function(params) {
  const { url, method, reqParams, noCache = false, refresh = false } = params;
  const key = JSON.stringify(url) + '_' + JSON.stringify(params);

  return new Promise((resolve, reject) => {
    // 有缓存，用缓存值
    if (noCache !== true && !refresh && cache[key]) {
      resolve(cache[key]);
      httpCache[key] = null;
      return;
    }
    // 已经发起请求，直接回调
    if (httpCache[key]) {
      httpCbk(key, resolve, reject);
      return;
    }
    const _method = method || 'get';
    httpCache[key] = Http(
      Object.assign(
        {
          url,
          method: _method
        },
        _method !== 'get'
          ? {
              data: {
                ...reqParams
              }
            }
          : {
              params: {
                ...reqParams
              }
            }
      )
    );
    httpCbk(key, resolve, reject);
  });
};

export default request;
