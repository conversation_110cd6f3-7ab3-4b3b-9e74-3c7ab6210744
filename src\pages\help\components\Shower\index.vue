<template>
  <div class="help-shower">
    <div class="help-shower-header">
      <span>shower + 表格loadMore</span>
      <a-button @click="onRefresh">刷新列表</a-button>
    </div>
    <Table class="table-new-mode" v-bind="tableParams" ref="table">
      <!-- 自定义显示列 -->
      <Shower
        slot="Shower"
        slot-scope="{ text, record, index, column }"
        v-bind="{ showerValue: text, showerData: record, config: column.shower }"
      ></Shower>
      <!-- 操作列 -->
      <Actions slot="action" :btnList="btnList"></Actions>
    </Table>
    <a-divider />
    <Table class="table-new-mode" v-bind="fixedTableParams" ref="fixedTable">
      <!-- 自定义显示列 -->
      <Shower
        slot="Shower"
        slot-scope="{ text, record, index, column }"
        v-bind="{ showerValue: text, showerData: record, config: column.shower }"
      ></Shower>
      <!-- 操作列 -->
      <Actions slot="action" :btnList="btnList"></Actions>
    </Table>
    <a-divider orientation="left">
      固定某几列宽度，其余自适应
    </a-divider>
    <Table class="table-new-mode" v-bind="fixedWidthTableParams" ref="fixedTable">
      <!-- 自定义显示列 -->
      <Shower
        slot="Shower"
        slot-scope="{ text, record, index, column }"
        v-bind="{ showerValue: text, showerData: record, config: column.shower }"
      ></Shower>
      <!-- 操作列 -->
      <Actions slot="action" :btnList="btnList"></Actions>
    </Table>
    <a-divider></a-divider>
    <Table class="table-new-mode" v-bind="fixedWidthTableParams1" ref="fixedTable">
      <!-- 自定义显示列 -->
      <Shower
        slot="Shower"
        slot-scope="{ text, record, index, column }"
        v-bind="{ showerValue: text, showerData: record, config: column.shower }"
      ></Shower>
      <!-- 操作列 -->
      <Actions slot="action" :btnList="btnList"></Actions>
    </Table>
  </div>
</template>

<script>
import Shower from '@/components/Shower';
import Table from '@/components/Table';
import Actions from '@/components/Actions';
import config from './config';

export default {
  components: { Shower, Table, Actions },
  props: {},
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: '/api/home/<USER>/table',
        reqParams: {
          from: 'pageList'
        },
        columns: this.config.columns,
        showHeader: false,
        paginationMode: 'loadMore'
      },
      fixedTableParams: {
        url: '/api/home/<USER>/table',
        reqParams: {
          from: 'pageList'
        },
        columns: this.config.fixedColumns,
        showHeader: false,
        paginationMode: 'loadMore',
        scroll: {
          x: 1000
        }
      },
      fixedWidthTableParams: {
        url: '/api/home/<USER>/table',
        reqParams: {
          from: 'pageList'
        },
        columns: this.config.fixedWidthColumns,
        // showHeader: false,
        paginationMode: 'loadMore',
        scroll: {
          x: 'max-content'
        }
      },
      fixedWidthTableParams1: {
        url: '/api/home/<USER>/table',
        reqParams: {
          from: 'pageList'
        },
        columns: this.config.fixedWidthColumns1,
        // showHeader: false,
        paginationMode: 'loadMore',
        scroll: {
          x: 'max-content'
        }
      },
      btnList: [
        {
          name: '编辑',
          type: 'edit',
          icon: 'edit'
        },
        {
          name: '删除',
          type: 'delete',
          icon: 'delete',
          confirm: '确定删除'
        },
        {
          name: '下载',
          type: 'download',
          icon: 'download'
        },
        {
          name: '设置',
          type: 'setting',
          icon: 'setting'
        },
        {
          name: '关闭',
          type: 'close',
          icon: 'close',
          confirm: '确定关闭'
        }
      ]
    };
  },
  mounted() {},
  created() {},
  methods: {
    onRefresh() {
      this.$refs.table.refresh();
    }
  }
};
</script>

<style lang="less" scoped>
.help-shower-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
  padding: 8px;
}
</style>
