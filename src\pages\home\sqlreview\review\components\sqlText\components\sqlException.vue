<template>
  <div class="review-wraper">
    <!-- 第二部分：其他sql集合 -->
    <div
      :class="['part-2', 'review-wraper', part2Show && 'expanded']"
      v-if="dynamicSplicing.length > 0"
    >
      <!-- <h4 @click="onExpand()"> -->
      <!-- <a-icon type="exception" /> -->
      <!-- <span>SQL信息集合</span> -->
      <!-- 折叠按钮 -->
      <!-- <a-icon :class="{ 'icon-sql-expand': true, expanded: part2Show }" type="double-right"></a-icon> -->
      <!-- </h4> -->
      <div
        class="sql-list-item"
        :id="`sql-item-${item.id}`"
        v-for="(item, idx) in dynamicSplicing"
        v-bind:key="item.id || `item-${idx}`"
      >
        <!-- 1. 根据 format: sql | xml 展示 -->
        <div
          :class="{
            'review-wraper2': true,
            'sql-detail-item': true,
            'custom-bg': true
          }"
          v-if="item.sql_format === 'sql'"
        >
          <div v-if="!item.compare_sql_text">
            <h4>
              <a-icon type="exception" style="margin-right: 4px" />当前SQL信息
            </h4>
            <template v-if="item.ai_comment">
              <!-- <pre>{{item.ai_comment}}</pre> -->
              <div class="pre-error-message">
                <pre v-for="(elem, index) in item.ai_comment" :key="index" style="flex-grow: 1;">
                  <custom-icon
                    class="rule-icon hign"
                    type="lu-icon-alarm"
                    v-if="elem.rule_result == 0"
                  />
                  <custom-icon
                    class="rule-icon low"
                    type="lu-icon-alarm"
                    v-if="elem.rule_result == 1"
                  />
                  <span>{{ elem.ai_comment }}</span>
                </pre>
                <div v-if="item.all_wrong == 1">
                  <pre
                    v-for="(elem, index) in item.error_message"
                    :key="`${index}_${elem.schema}`"
                    style="padding: 0 0 8px 16px; color: rgb(176, 174, 174);"
                  >
                    {{ elem.schema }}: {{ elem.error_message }}
                  </pre>
                </div>
              </div>
              <a-divider style="margin: 0px;" />
            </template>
            <sql-highlight :sql="item.sql_text" title="[SQL文本]"></sql-highlight>
          </div>
          <div v-else>
            <code-mirror
              class="sql-detail-compare"
              :orig="item.sql_text"
              :origTitle="'当前SQL信息'"
              :value="item.compare_sql_text"
              valueTitle="最后一次通过SQL信息"
              :format="item.sql_format"
            >
              <div
                style="
                  position: relative;
                  top: 8px;
                  flex-grow: 1;
                  display: flex;
                  flex-direction: column;
                "
                slot="head-right-extend"
                v-if="item.compare_ai_comment || item.ai_comment"
              >
                <pre style="flex-grow: 1;">{{
                  item.compare_ai_comment || '暂无触发规则'
                }}</pre>
                <a-divider style="margin: 0px;" />
              </div>
              <div
                style="
                  position: relative;
                  top: 8px;
                  flex-grow: 1;
                  display: flex;
                  flex-direction: column;
                "
                slot="head-left-extend"
                v-if="item.compare_ai_comment || item.ai_comment"
              >
                <pre style="flex-grow: 1;">{{
                  item.ai_comment || '暂无触发规则'
                }}</pre>
                <a-divider style="margin: 0px;" />
              </div>
            </code-mirror>
          </div>
          <!-- 2. 当前执行计划信息：有则展示无则隐藏 -->
          <div class style="margin-top: 24px;" v-if="item.sql_plan">
            <template v-if="!item.compare_sql_plan">
              <div>
                <h4>
                  <a-icon type="exception" />
                  {{ '当前执行计划信息' }}
                  <span v-if="item.data_source_name">
                    (
                    <span
                      style="
                        color: #0f78fb;
                        font-size: 16px;
                        font-weight: bold;
                        margin-right: 4px;
                      "
                    >{{ item.data_source_name.split('|')[0] }}</span>
                    <span>{{ item.data_source_name.split('|')[1] }}</span>
                    )
                  </span>
                </h4>
              </div>
              <div class="redo">
                <pre>{{ item.sql_plan }}</pre>
              </div>
            </template>
            <!-- 以前有左右对比，现在没有了 -->
            <template v-else>
              <code-mirror
                :orig="item.sql_plan"
                :origTitle="'当前执行计划信息'"
                :value="item.compare_sql_plan || ''"
                valueTitle="最后一次通过执行计划信息"
                :format="'txt'"
              >
                <span slot="head-right-extend" v-if="item.compare_data_source_name">
                  (
                  <span
                    style="
                      color: #0f78fb;
                      font-size: 16px;
                      font-weight: bold;
                      margin-right: 4px;
                    "
                  >{{ item.compare_data_source_name.split('|')[0] }}</span>
                  <span>{{ item.compare_data_source_name.split('|')[1] }}</span>
                  )
                </span>
                <span slot="head-left-extend" v-if="item.data_source_name">
                  (
                  <span
                    style="
                      color: #0f78fb;
                      font-size: 16px;
                      font-weight: bold;
                      margin-right: 4px;
                    "
                  >{{ item.data_source_name.split('|')[0] }}</span>
                  <span>{{ item.data_source_name.split('|')[1] }}</span>
                  )
                </span>
              </code-mirror>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div v-if="dynamicSplicing.length <= 0" class="ai-comment-part">暂无数据</div>
  </div>
</template>

<script>
import CodeMirror from '@/components/CodeMirror';
import SqlHighlight from '@/components/SqlHighlight';
export default {
  components: { CodeMirror, SqlHighlight },
  props: {
    dynamicSplicing: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      part2Show: true
    };
  },
  mounted() {
    // let dom = this.$el.querySelector(`.part-2`);
    // if (dom) this.onExpand();
  },
  methods: {
    onExpand() {
      this.part2Show = !this.part2Show;
      let dom = this.$el.querySelector(`.part-2`);
      CommonUtil.toggleDom({
        element: dom,
        time: 0.3,
        startHeight: '48px',
        show: this.part2Show
      });
    }
  }
};
</script>

<style lang="less" scoped>
@import './commonClass.less';
// .pre-error-message > pre:last-child {
//   padding-bottom: 16px !important;
// }
.pre-error-message {
  background-color: rgba(125, 125, 125, 0.1);
  overflow: auto;
  width: 100%;
  // height: 120px;
  padding-top: 8px;
  pre {
    padding: 0 16px 8px 16px;
    overflow: auto;
    background-color: rgba(125, 125, 125, 0);
    display: flex;
    align-items: center;
    .rule-icon {
      margin-right: 8px;
      color: #e4e4e7;
      font-size: 12px;
      align-self: self-start;
      padding-top: 4px;
      &.high {
        color: #e71d36;
        margin-right: 4px;
      }
      &.low {
        color: #f29339;
        margin-right: 4px;
      }
      &.error {
        color: #71717a;
        margin-right: 4px;
      }
    }
  }
}
.home-review-wraper {
  .review-wraper {
    padding: 0;
    .part-2 {
      // > h4 {
      //   font-family: PingFangSC-Semibold;
      //   font-size: 14px;
      //   color: #27272a;
      //   font-weight: 600;
      //   .anticon {
      //     font-size: 16px;
      //     color: #27272a;
      //   }
      //   > span {
      //     font-size: 14px;
      //     color: #27272a;
      //   }
      // }
      .sql-list-item {
        &:last-child {
          .review-wraper2 {
            border-bottom: none;
            margin-bottom: 0;
            padding: 0;
          }
        }
        .review-wraper2 {
          padding: 0 0 24px 0;
          margin-top: 0px;
          border: none;
          border-bottom: 1px solid #e8e8e8;
          box-shadow: none;
          border-radius: 0;
          margin-bottom: 48px;

          > div {
            h4 {
              font-family: PingFangSC-Semibold;
              font-size: 14px;
              color: #27272a;
              font-weight: 600;
              .anticon {
                font-size: 16px;
                color: #27272a;
              }
              > span {
                font-size: 14px;
                color: #27272a;
              }
            }
          }
          > div:first-child {
            .pre-error-message {
              background: rgba(0, 59, 114, 0.13) !important;
              overflow: auto;
              border-radius: 10px 10px 0 0;
              > pre {
                // font-family: PingFangSC-Medium;
                font-size: 14px;
                color: #3e60c1;
                font-weight: 500;
                > span {
                  padding-right: 16px;
                }
              }
            }
            .sql-format {
              background: rgba(0, 59, 114, 0.06) !important;
              border-radius: 0 0 10px 10px !important;
            }
          }
          .ant-divider {
            display: none;
          }
          > div:last-child {
            margin-top: 12px !important;
            .redo {
              border-radius: 10px;
              background: #f4f5f7;
            }
          }
        }
      }
    }
  }
}
</style>
