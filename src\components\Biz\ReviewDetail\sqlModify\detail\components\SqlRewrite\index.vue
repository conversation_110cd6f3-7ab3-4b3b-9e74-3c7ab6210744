<template>
  <div class="content">
    <!-- 最佳索引建议-->
    <a-card type="small" class="index-advice-card a-card" v-if="advice.length > 0">
      <div class="title" slot="title">
        <a-icon type="robot" />
        <span style="margin-left: 4px">最佳索引建议</span>
      </div>
      <div class="index-advice">
        <div v-for="(item, index) in advice" :key="index">
          <div class="word-des">{{ item.message }}</div>
          <!-- <pre>{{ item.sql }}</pre> -->
          <sql-highlight :sql="item.sql || ''"></sql-highlight>
        </div>
      </div>
      <a slot="extra" class="btn-shadow" @click="onCopyIndex">
        <a-icon type="copy" style="margin-right: 8px" />复制
      </a>
    </a-card>
    <!-- 使用生成的列索引函数调用 -->
    <div class="advice-case">
      <a-card
        type="small"
        class="a-card"
        :title="item.TITLE"
        v-for="(item, index) in info.card_list"
        :key="index"
        style="font-size: 13px"
      >
        <div>{{ item.DESC }}</div>
        <!-- <a-badge status="success" text="自动应用" /> -->
        <div class="like-dislike">
          <a class="des btn-shadow" @click="checkCase(item)">查看示例</a>
        </div>
      </a-card>
    </div>
    <!-- SQL文本结果 -->
    <a-card type="small" class="sql-text-card a-card body-12">
      <div class="title" slot="title">
        <a-icon type="robot" />
        <span style="margin-left: 4px">SQL改写优化建议</span>
      </div>
      <a slot="extra" class="btn-shadow" @click="onCopyRewriteSql">
        <a-icon type="copy" style="margin-right: 8px" />复制
      </a>
      <Diff :list="diffList" :format="true" v-if="diffList.length > 0" />
      <custom-empty v-else />
    </a-card>
    <div class="rule-content">
      <div class="rule-content-half">
        <h4>原触发规则</h4>
        <a-card
          v-for="(item, index) in sql.ai_message"
          :key="index"
          type="small"
          class="rule-a-card"
          :style="{
            background: backgroundColor[item.level],
            'border-radius': '8px ',
            'margin-bottom': '16px',
            'font-size': '13px'
          }"
        >
          <a-icon :type="item.pass_status | type" :style="{ color: levelColor[item.level] }" />
          <span>{{ item.ai_comment }}</span>
        </a-card>
      </div>
      <div class="rule-content-half">
        <h4>改写后触发规则</h4>
        <a-card
          v-for="(item, index) in sqlRewrite.ai_message"
          :key="index"
          type="small"
          class="rule-a-card"
          :style="{
            background: backgroundColor[item.level],
            'border-radius': '8px ',
            'margin-bottom': '16px',
            'font-size': '13px'
          }"
        >
          <a-icon :type="item.pass_status | type" :style="{ color: levelColor[item.level] }" />
          <span>{{ item.ai_comment }}</span>
        </a-card>
      </div>
    </div>
    <CaseModal ref="case"></CaseModal>
  </div>
</template>

<script>
import Coder from '@/components/Coder';
import CaseModal from './CaseModal';
import DiffModal from './DiffModal';
import SqlHighlight from '@/components/SqlHighlight';
import config from './config';
import { sqlRewrite } from '@/api/review';
import Diff from '@/components/Diff';
import common from '@/utils/common';
export default {
  components: { Coder, CaseModal, DiffModal, SqlHighlight, Diff },
  props: {
    advice: {
      type: Array,
      default: () => []
    }
    // info: {
    //   type: Object,
    //   default: () => ({})
    // }
  },
  data() {
    this.config = config(this);
    return {
      info: {
        ai_message: [],
        card_list: []
      },
      loading: false,
      des: '表关联次数小于3，使用二级索引等值扫描，且扫描行数<100',
      sqlText: '',
      sqlPlan: '',
      action: null,
      likeNum: 10,
      dislikeNum: 10,
      coderParams: {
        height: '500',
        options: {
          theme: 'default',
          readOnly: true
        },
        formatOptions: {
          keywordCase: 'upper'
        },
        needFormat: true
      },
      expandIconPosition: 'right',
      levelColor: {
        1: '#ff4d4f',
        2: '#ff9358',
        3: '#1edfa9',
        9: '#52c41a',
        0: '#b0aeae'
      },
      backgroundColor: {
        1: '#FDD4D5',
        2: '#FEE4D5',
        3: '#dbf5ce'
      },
      diffList: [],
      sql: {},
      sqlRewrite: {},
      detailId: null,
      reviewId: null
    };
  },
  computed: {},
  mounted() {
    this.sqlRewriteFn();
  },
  methods: {
    sqlRewriteFn() {
      sqlRewrite({
        detail_id: this.$route.params.id
        // sql_rewrite_detail_id: this.info.sql_rewrite_detail_id
      })
        .then(res => {
          this.loading = false;
          if (_.get(res, 'data.code') == 0) {
            const sql = _.get(res, 'data.data.sql') || {};
            const sqlRewrite = _.get(res, 'data.data.sql_rewrite') || {};
            const resData = _.get(res, 'data.data') || {};
            this.info = resData;
            this.sql = sql;
            this.sqlRewrite = sqlRewrite;
            this.diffList = [
              {
                filename: '原SQL → 改写后的SQL',
                oldStr: sql.sql || '',
                newStr: sqlRewrite.sql || '',
                // oldStr: 'select1\n' + sql.sql || '',
                // newStr: 'select2\n' + sqlRewrite.sql || '',
                type: 'sql'
              },
              {
                filename: '原执行计划 → 改写后的执行计划',
                oldStr: sql.sql_plan || '',
                newStr: sqlRewrite.sql_plan,
                // oldStr: 'select1\n' + sql.sql_plan,
                // newStr: 'select2\n' + sqlRewrite.sql_plan,
                type: 'txt'
              }
            ];
            this.detailId = resData.record_id;
            this.reviewId = resData.review_detail_id;
            this.setNavi();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.loading = false;
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'home-sqlreview-detail') {
          path = sourcePath.replace(':id', this.detailId);
        }
        if (key === 'home-sqlreview-review') {
          path = sourcePath.replace(':id', this.reviewId);
        }
        if (key === 'orderDetail') {
          path = sourcePath.replace(':id', this.detailId);
        }
        if (key === 'orderReview') {
          path = sourcePath.replace(':id', this.reviewId);
        }
        return path;
      });

      // if (this.id !== this.$route.params.id) {
      //   this.$router.push({
      //     name: 'home-sqlreview-review',
      //     params: { id: this.id, params: this.searchData }
      //   });
      // }
    },
    onLike() {
      this.likeNum += 1;
      this.dislikeNum -= 1;
      this.action = 'liked';
    },
    onDislike() {
      this.likeNum -= 1;
      this.dislikeNum += 1;
      this.action = 'disliked';
    },
    checkCase(item) {
      this.$refs.case.show(item);
    },
    openDiffModal() {
      this.$refs.DiffModal.show(this.info);
    },
    // 复制最佳索引建议alter
    onCopyIndex() {
      const sql = this.advice.map(item => item.sql); // 设置复制内容
      CommonUtil.copy({
        value: sql.join(),
        callback: () => {
          this.$message.success('成功复制alter语句');
        },
        allowEmpty: false
      });
      // var input = document.createElement('input'); // 创建input对象
      // const sql = this.advice.map(item => item.sql);
      // input.value = sql.join(); // 设置复制内容
      // document.body.appendChild(input); // 添加临时实例
      // input.select(); // 选择实例内容
      // document.execCommand('Copy'); // 执行复制
      // document.body.removeChild(input); // 删除临时实例
      // this.$message.success('成功复制alter语句');
    },
    // 复制改写后的SQL
    onCopyRewriteSql() {
      CommonUtil.copy({
        value: this.sqlRewrite.sql || '',
        callback: () => {
          this.$message.success('成功复制改写sql');
        }
      });
      // var input = document.createElement('input'); // 创建input对象
      // const rewriteSql = this.info.rewrite_sql;
      // input.value = rewriteSql; // 设置复制内容
      // document.body.appendChild(input); // 添加临时实例
      // input.select(); // 选择实例内容
      // document.execCommand('Copy'); // 执行复制
      // document.body.removeChild(input); // 删除临时实例
      // this.$message.success('成功复制改写后的SQL');
    }
  },
  filters: {
    aiStatus(value) {
      let obj = {
        0: '未知',
        1: '通过',
        2: '白名单通过',
        9: '错误',
        '-1': '不通过'
      };
      return obj[value];
    },
    levelStatus(value) {
      let obj = {
        0: '未知',
        1: '高风险',
        2: '中风险',
        3: '低风险',
        9: '无风险'
      };
      return obj[value];
    },
    type(value) {
      let obj = {
        0: 'close-circle',
        1: 'check-circle',
        2: 'close-circle',
        9: 'close-circle',
        '-1': 'close-circle'
      };
      return obj[value];
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.content {
  // display: flex;
  // justify-content: space-between;
  margin-bottom: 24px;

  // 统一阴影按钮样式
  .btn-shadow {
    background: #ffffff;
    // box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
    border-radius: 4px;
    padding: 4px 8px;
    color: rgba(0, 0, 0, 0.85);
    border: 1px solid rgba(0, 0, 0, 0.15);
    &:hover {
      color: #1890ff;
    }
  }
  .rewrite-sql-btn-shadow {
    display: flex;
    > a {
      background: #ffffff;
      // box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
      border-radius: 4px;
      padding: 4px 8px;
      margin-left: 16px;
      color: rgba(0, 0, 0, 0.85);
      border: 1px solid rgba(0, 0, 0, 0.15);
      &:hover {
        color: #1890ff;
      }
    }
  }
  // ant-card-body padding:12px
  .body-12 {
    // border: none !important;
    /deep/ .ant-card-body {
      padding: 12px;
    }
  }
  .a-card {
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
    border-radius: 8px;
    margin-bottom: 24px;
  }
  .a-card:last-child {
    margin-bottom: 0px;
  }
  .index-advice-card {
    .index-advice {
      .word-des {
        font-size: 13px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        font-weight: 400;
      }
      > pre {
        margin-top: 16px;
      }
    }
  }
  .advice-case {
    display: flex;
    overflow-x: scroll;
    margin-bottom: 24px;
    .a-card {
      background: #ffffff;
      box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
      border-radius: 8px;
      margin: 0 24px 8px 0;
      width: 400px;
      min-width: 400px;
      .like-dislike {
        display: flex;
        justify-content: space-between;
        margin-top: 16px;
        .like {
          margin-right: 16px;
        }
      }
    }
  }
  .rule-content {
    display: flex;
    justify-content: space-between;
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
    border-radius: 8px;
    padding: 0 16px;
    .rule-content-half {
      width: 48%;
      > h4 {
        padding: 16px 0;
      }
    }
    .rule-a-card {
      /deep/.ant-card-body {
        padding: 12px;
      }
    }
  }
}
</style>
