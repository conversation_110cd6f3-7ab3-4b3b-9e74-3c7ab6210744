<template>
  <div class="index-rule-content">
    <!-- 预加载括号 -->
    <Bracket color="#F29339" style="display: none;" />
    <!-- 基础信息 -->
    <div class="index-rule-info">
      <a-card style="width: 100%;" class="common-pure-card" :bordered="false">
        <span class="title">
          <a-icon type="export" />基本信息
        </span>
        <Form
          ref="baseInfo"
          class="base-info-form"
          v-bind="baseInfoParams"
          :formData="baseInfoData"
        />
      </a-card>
    </div>
    <!-- 条件 -->
    <div class="index-rule-condition" :data-row="buttonNumber">
      <a-card style="width: 100%;" class="common-pure-card" :bordered="false">
        <span class="title">
          <a-icon type="export" />条件区域
        </span>
        <TableEdit
          @change="onChangeNumber"
          ref="tableEdit"
          v-bind="tableParams || {}"
          :dataSource="tableData"
        ></TableEdit>
        <div class="btn-add-condition">
          <a-button @click="addCondition" icon="plus" v-if="type !== 'detail'"></a-button>
        </div>
      </a-card>
    </div>
    <EditModal ref="EditModal" :dbType="dbType" @save="onConditionSave" />
  </div>
</template>
<script>
import Form from '@/components/Form';
import TableEdit from '@/components/TableEdit';
import Select from '@/components/Select';
import Bracket from '@/components/Biz/Bracket';
import EditModal from '../EditModal';
import config from './config';

export default {
  components: { Form, TableEdit, Select, EditModal, Bracket },
  props: {
    type: {
      type: String,
      default: 'add'
    },
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    this.config = config(this);
    return {
      baseInfoData: {},
      buttonNumber: 0,
      baseInfoParams: {
        fixedLabel: true,
        gutter: 24,
        multiCols: 2,
        layout: 'horizontal',
        fields: this.config.ruleBaseInfo
      },
      tableData: [],
      tableParams: {
        initEditStatus: true,
        actionBtns: this.type !== 'detail' ? ['remove'] : [],
        editConfig: this.config.condition.editConfig(),
        columns: this.config.condition.columns,
        pagination: false,
        rowKey: 'id'
      },
      dbType: ''
      // outputParams: this.config.outputConfig,
      // outputData: {
      //   key: 0,
      //   value: ''
      // }
    };
  },
  created() {},
  mounted() {
    const { EditModal } = this.$refs;
    this.$bus.$on('input-modal', data => {
      EditModal.show(this.type, data);
    });
  },
  destroyed() {
    this.$bus.$off('input-modal');
  },
  methods: {
    onChangeNumber(data) {
      this.buttonNumber = data.length;
    },
    getRowNumber() {
      const { tableEdit } = this.$refs;
      console.log(tableEdit);
    },
    addCondition() {
      const { tableEdit } = this.$refs;
      tableEdit.addLine();
    },
    onConditionSave(params) {
      const { type, data = {} } = params;
      const { selectedRows, sourceData = {} } = data;
      const { editRecord, editRow } = sourceData;
      const { tableEdit, EditModal } = this.$refs;
      console.log(params, type, editRow, 666);

      tableEdit.saving({
        // ...editRecord,
        id: editRecord.id,
        target: selectedRows[0].code,
        desc: selectedRows[0].desc,
        value_operator: selectedRows[0].value_operator,
        value_type: selectedRows[0].value_type,
        condition: null,
        target_value: null
      });
      EditModal.hide();
    },
    getData() {
      const { tableEdit, baseInfo } = this.$refs;
      // console.log(tableEdit.getData(), baseInfo.data, this.outputData);
      return new Promise((resolve, reject) => {
        Promise.all([
          tableEdit.validate(),
          baseInfo.validate()
          // output.validate()
        ])
          .then(values => {
            // console.log(values)
            const conditions = tableEdit.getData();
            if (!conditions || conditions.length <= 0) {
              this.$message.error('条件不能为空！');
              reject(new Error('条件不能为空！'));
            }
            resolve({
              ...baseInfo.data,
              // rule_result: this.outputData.value,
              details: conditions.map(item => {
                if (this.type === 'add') {
                  delete item['id'];
                }
                return item;
              })
            });
          })
          .catch(e => {
            // console.log(e);
            reject(e);
          });
      });
    }
  },
  watch: {
    dataSource: {
      handler(newVal, oldVal) {
        const { tableEdit } = this.$refs;
        this.baseInfoData = {
          name: newVal.name,
          desc: newVal.desc,
          level: newVal.level,
          type: newVal.type
        };
        this.dbType = newVal.db_type;
        this.tableData = newVal.details;
        this.buttonNumber = newVal.details.length;
        tableEdit.editAll();
      }
      // immediate: true,
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.index-rule-content {
  .index-rule-info {
    /deep/.ant-card-body {
      padding: 24px;
      .base-info-form {
        padding: 0 102px 0 28px;
        > .ant-row {
          > .ant-col {
            padding: 0 !important;
            width: 50%;
            .ant-row {
              .ant-form-item-label {
                > label {
                  justify-content: flex-start;
                }
              }
              .ant-form-item-control-wrapper {
                .ant-form-item-children {
                  .ant-input {
                    height: 48px;
                    box-shadow: 4px 4px 3px 0px #f5f5f5;
                    border-color: #ebebec;
                    border-right: none;
                    border-radius: 8px 0 0 8px;
                  }
                  .ant-select-selection {
                    height: 48px;
                    box-shadow: 4px 4px 3px 0px #f5f5f5;
                    border-color: #ebebec;
                    border-right: none;
                    border-radius: 8px 0 0 8px;
                    .ant-select-selection__rendered {
                      line-height: 46px;
                    }
                  }
                }
              }
            }
            &:nth-child(2) {
              .ant-row {
                .ant-form-item-control-wrapper {
                  .ant-form-item-children {
                    .ant-input {
                      border-radius: 0 8px 8px 0;
                      border-right: 1px solid #d9d9d9;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .index-rule-condition {
    /deep/.common-pure-card {
      .ant-card-body {
        padding: 24px;
        .table-edit {
          /** 修改条件区域样式 */
          .ant-table-tbody > tr {
            .ant-select-selection {
              transition: none;
            }
            > td {
              border-bottom: 0 !important;
              &:not(:first-child):not(:last-child) {
                padding: 12px 0;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  height: 50px;
                  box-shadow: 4px 4px 3px 0px #f5f5f5;
                  border-color: #ebebec;
                }
                .ant-select-selection {
                  .ant-select-selection__rendered {
                    line-height: 48px;
                    .ant-select-selection-selected-value {
                      height: 48px;
                    }
                  }
                }
              }
              &:nth-child(2) {
                padding-left: 12px !important;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 8px 0 0 8px;
                  border-right: 0;
                  box-shadow: 0px 0px 6px 2px #e8e8e8;
                }
              }
              &:nth-child(3) {
                // padding-right: 12px !important;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 0;
                  border-right: 0;
                }
              }
              &:nth-child(4) {
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 0 8px 8px 0;
                }
              }
              .biz-rule-range {
                top: 0;
                > *:not(:last-child) {
                  margin-right: 0;
                }
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  box-shadow: 0;
                }
                .ant-select:nth-child(1) {
                  .ant-select-selection {
                    border-radius: 0;
                    border-right: 0;
                  }
                }
                .ant-select:nth-child(2) {
                  .ant-select-selection {
                    border-radius: 0;
                    border-top: 1px solid #e8e8e8;
                    border-bottom: 1px solid #e8e8e8;
                    border-left: 1px solid #f5f5f5;
                    border-right: 1px solid #f5f5f5;
                  }
                }
              }
            }
            &:first-child {
              td:first-child {
                .ant-form-item {
                  visibility: hidden;
                  display: none;
                }
              }
            }
            td:first-child {
              padding-right: 35px;
              background-color: #fff !important;
              .ant-form-item {
                position: relative;
                .ant-form-item-control {
                  position: relative;
                  top: -34px;
                }
                .ant-select {
                  width: 75px;
                  .new-style(@color) {
                    .ant-select-selection {
                      border-color: @color;
                      color: @color;
                      box-shadow: none;
                      border-radius: 16px;
                      .ant-select-arrow {
                        color: @color;
                      }
                    }
                  }
                  &.relation-and {
                    .new-style(#4cbb3a);
                  }
                  &.relation-or {
                    .new-style(#F29339);
                  }
                }
                .biz-bracket {
                  position: absolute;
                  right: -36px;
                  top: -15px;
                }
              }
            }
          }
          thead {
            display: none;
          }
          .ant-table-tbody
            > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
            > td {
            background: transparent;
          }
          .ant-table-content td {
            padding: 10px 16px;
          }
          .ant-table-placeholder {
            display: none;
          }
        }
      }
    }
    &[data-row='1'] {
      .btn-add-condition {
        position: absolute;
        bottom: 44px;
      }
      /deep/ .table-edit {
        colgroup {
          > col:first-child {
            width: 75px !important;
          }
        }
        .ant-table-tbody {
          > tr {
            &:first-child {
              td:first-child {
                padding: 0;
              }
            }
          }
        }
      }
    }
    /deep/.btn-add-condition {
      border-radius: 16px;
      margin: 10px 16px;
      font-size: 14px;
      .ant-btn {
        height: 28px;
        width: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #4db5f2;
        border-radius: 4px !important;
        transition: none;
        font-size: 14px;
        .anticon {
          line-height: 0;
          color: #4db5f2;
        }

        &:hover {
          background: #4db5f2;
          .anticon {
            color: #fff;
          }
        }
      }
    }
    /deep/.custom-table .ant-table .ant-table-tbody > tr:nth-child(even) {
      background: #fff;
    }
  }
}
</style>