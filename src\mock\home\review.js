/*
 * @Author: your name
 * @Date: 2021-01-29 09:30:37
 * @LastEditTime: 2021-01-29 17:14:09
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/mock/home/<USER>
 */
// import Mock from '@/utils/mock'

// Mock.mock(/\/sqlreview\/review\/review-detail\/(\?[\s\S]*)?$/, 'get', () => {
//   return {
//     'code': 0,
//     'message': 'eee',
//     'data': {
//       'id': 'dd',
//       'compare_point': 'reg_20210114_11',
//       'review_point': 'reg_20210114_02',
//       'ai_status': 1,
//       'ai_comment': '[xxx]asfasdfxxx',
//       'sql_text': 'select *, count(a.num) from  table a where a.id=\'adf\' and 1 =1 and a.name=322;',
//       'compare_sql_text': 'select *, count(a.num) from  table a where a.id=\'asdf\' or a.name=323;',
//       'sql_plan': 'asfasdf:assdf\nadsfasf:asfdasdf\nasf:asdf',
//       'sql_plan_tree': 'asfasfddsa:asfads',
//       'dba_comment': 'asfdadsfasf',
//       'review_status': Math.random() > 0.5 ? 1 : 2,
//       'review_id': 1,
//       'review_comment': [
//         {
//           'item_key': 'sdfasd1',
//           'item_value': 'sql存在严重性能瓶颈'
//         },
//         {
//           'item_key': 'sdfasd2',
//           'item_value': '不允许使用跨表查询'
//         },
//         {
//           'item_key': 'sdfasd3',
//           'item_value': 'sql条件使用没限制长度'
//         }
//       ]
//     }
//   }
// });

// Mock.mock(/\/sqlreview\/review\/review-detail-(fail|pass)\/(\?[\s\S]*)?$/, 'post', () => {
//   return {
//     'code': 0,
//     'message': 'eee',
//     'data': {
//       'id': 'dd',
//       'review_point': 'reg_20210114_02',
//       'ai_status': 1,
//       'ai_comment': '[xxx]asfasdfxxx',
//       'sql_text': 'select *, count(a.num) from  table a where a.id=\'adf\' and a.name=322;',
//       'sql_plan': 'asfasdf:assdf\nadsfasf:asfdasdf\nasf:asdf',
//       'sql_plan_tree': 'asfasfddsa:asfads',
//       'dba_comment': 'asfdadsfasf',
//       'review_status': 1,
//       'review_comment': [
//         {
//           'item_key': 'sdfasd2',
//           'item_value': 'sql存在严重性能瓶颈'
//         },
//         {
//           'item_key': 'sdfasd3',
//           'item_value': '不允许使用跨表查询'
//         },
//         {
//           'item_key': 'sdfasd4',
//           'item_value': 'sql条件使用没限制长度'
//         }
//       ]
//     }
//   }
// })