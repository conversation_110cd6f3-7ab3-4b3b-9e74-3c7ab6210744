<template>
  <a-spin tip="加载中" :spinning="loading">
    <div :class="['review-project-id', isProcedure && 'procedure']">
      ID: {{ id }}
    </div>
    <div class="frame-button-wrapper">
      <div class="page-change-container">
        <span class="page-info">{{ current }}/{{ count }}</span>
        <a-button-group>
          <a-button @click="onPrev" :disabled="count < 0 || current === 1">
            <custom-icon type="left" />
          </a-button>
          <a-button @click="onNext" :disabled="count < 0 || current === count">
            <custom-icon type="right" />
          </a-button>
        </a-button-group>
      </div>
      <div class="line"></div>
      <a-button class="back-btn highlight" @click="toBack" v-if="hasBack"
        >返回</a-button
      >
      <a-popover placement="top">
        <template slot="content">
          <span>{{ this.plan }}</span>
        </template>
        <a-button v-if="false" @click="onAudit" class="highlight"
          >提交评审</a-button
        >
      </a-popover>
    </div>
    <div class="preaudit-review-wraper">
      <!-- 头部信息 -->
      <HeaderInfo
        @addSqlTag="addSqlTag"
        @refresh="refresh"
        @saveAdvice="onSaveAdvice"
        :headerInfo="headerInfo"
        :isWhite="isWhite"
        :sqlLabelStatus="sqlLabelStatus"
        :dataSourceList="dataSourceList"
        :dataInfo="dataInfo"
        :pieOption="pieOption"
        :sqlPlanInfo="sqlPlanInfo"
        :sqlErrorMessage="sqlErrorMessage"
        :sqlSuggest="sqlSuggest"
        :commentStatus="commentStatus"
        :dbaComment="dbaComment"
        :id="id"
      />
      <!-- SQL文本和执行计划 -->
      <SqlText
        @activeChange="activeChange"
        @saveNote="onSaveNote"
        :sqlMapParamsData="sqlMapParamsData"
        :tableExistFlag="tableExistFlag"
        :detaliData="detaliData"
        :activeKey="activeKey"
        :sqlList="sqlList"
        :sqlMap="sqlMap"
        :sqlHistoryCompare="sqlHistoryCompare"
        :auditType="auditType"
        :id="id"
        ref="sqlText"
      ></SqlText>

      <!-- 审核弹窗 -->
      <Audit ref="audit"></Audit>
      <!-- 打标弹窗 -->
      <TagModal ref="tag" @saveLabel="onSaveLabel"></TagModal>
    </div>
  </a-spin>
</template>

<script>
import HeaderInfo from '@/components/Biz/ReviewDetail/HeaderInfo';
import SqlText from '@/components/Biz/ReviewDetail/sqlText';
import TagModal from '@/components/Biz/ReviewDetail/TagModal';
import Audit from '@/components/Biz/AuditModel';
import common from '@/utils/common';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import {
  sqlAdviceInfo,
  saveSqlmapConfig
  // getEveryDetailError
} from '@/api/review';
import { saveLabel, saveSqlMap } from '@/api/home';
import { getDetail, saveAdvice } from '@/api/preaudit';
export default {
  components: {
    HeaderInfo,
    SqlText,
    Audit,
    TagModal
  },
  mixins: [bodyMinWidth(1366)],
  data() {
    this.config = config(this);
    return {
      hasBack: true,
      canDo: false,
      // 顶部数据
      headerInfo: {},
      // AI判定结果
      dataInfo: {
        ai_comment: [],
        rule_category: [] // 风险等级数据
      },
      sqlSuggest: [],
      sqlPlanInfo: [], // 索引建议
      labelInfo: {}, // dba标签
      dbaComment: [],
      // SQL文本和执行计划
      activeKey: 'sqlInfo',
      id: null,
      searchData: {}, // 上一页搜索参数
      detaliData: {}, // SQL文本和执行计划
      sqlList: [],
      sqlMap: {},
      sqlHistoryCompare: {}, // 历史版本比对
      operatorDba: null, // DBA负责人
      sqlMapParamsData: {}, // 申请白名单/申请通过数据
      pieOption: null, // 风险环形图数据
      review_id: null,
      loading: false,
      isWhite: true,
      filterKey: '',
      plan: '', // 进度
      isSqlReview: true, // 评审详情显示,
      reviewId: null,
      sqlErrorMessage: [],
      tableExistFlag: false,
      commentStatus: null,
      risk: '',
      routeName: 'project-review-review',
      sqlLabelStatus: null,
      dataSourceList: [],
      current: 0,
      count: 0,
      prevId: null,
      nextId: null,
      auditType: null,
      timestamp: null,
      isCache: 0
    };
  },
  computed: {
    isLeader() {
      const user = this.$store.state.account.user || {};
      return user.role === 'leader';
    },
    isAdmin() {
      const user = this.$store.state.account.user || {};
      return user.role === 'admin';
    },
    isProcedure() {
      const name = this.$route.name;
      return name == 'procedure-review-review';
    }
  },
  created() {},
  mounted() {
    // this.getEveryDetailErrorFn({ detail_id: this.$route.params.id });
    this.searchData = this.$route.params.searchData;
    this.id = this.$route.params.id;
    this.refresh({ id: this.id });
  },
  methods: {
    // 获取详情数据
    refresh(params) {
      this.loading = true;
      params.params = this.searchData;
      params.is_cache = this.isCache;
      params.timestamp = this.timestamp;
      getDetail(params)
        .then(reuslts => {
          if (_.get(reuslts, 'data.code') == 0) {
            const res = _.get(reuslts, 'data.data');
            this.loading = false;
            this.auditType = res.audit_type;
            // 上下条数据
            this.nextId = res.next_id;
            this.prevId = res.prev_id;
            this.current = res.curr_idx;
            this.count = res.count;
            this.timestamp = res.timestamp;
            this.isCache = 1;
            this.reviewId = res.review_id;
            this.plan = `${res.left}/${res.right}已评估`;
            // headerInfo 组件数据
            this.isWhite = res.is_white_list;
            this.sqlLabelStatus = res.sql_label_status;
            this.dataSourceList = res.datasource_list;
            this.headerInfo = {
              label_attribute: res.label_attribute,
              label_status: res.label_status,
              label_obj_id: res.label_obj_id,
              project_name: res.project_name,
              file_path: res.file_path,
              file_name: res.file_name,
              index: res.index,
              count: res.count,
              comment_status: res.comment_status,
              audit_status: res.audit_status,
              db_type: res.db_type,
              sql_frame: res.sql_frame,
              rule_set: res.rule_set
            };
            this.dbaComment = res.dba_comment;
            // this.labelInfo = {
            //   ...res.label_info,
            //   label_attribute: res.label_attribute,
            //   ai_status: res.ai_status,
            //   operatorDba: res.operator_dba,
            //   comment_content: res.comment_content,
            //   dba_comment: res.dba_comment,
            //   updated_at: res.updated_at,
            //   comment_status: res.comment_status
            // };
            this.pieOption = this.config.pieOption({
              data: res.rule_category || []
            });
            this.id = res.id;
            this.commentStatus = res.comment_status;
            this.sqlSuggest = _.get(res, 'sql_suggest') || [];
            this.dataInfo.ai_comment = _.get(res, 'ai_comment') || [];
            this.dataInfo.ai_status = res.ai_status;
            this.dataInfo.risk = res.risk;
            this.dataInfo.rule_category = res.rule_category;

            // sqlText组件数据
            this.detaliData = res;
            this.sqlList = res.sql_list || [];
            this.tableExistFlag = res.table_exist_flag;
            this.operatorDba = res.operator_dba;
            this.sqlMap = res.sql_map || {};
            this.sqlHistoryCompare = res.sql_history_compare || {};
            // sqlmap参数
            let sqlMapParamsData = {};
            // 申请白名单数据
            res.sql_map_config
              ? (sqlMapParamsData = res.sql_map_config)
              : (sqlMapParamsData.sqlmap_white_list = 1);
            if (
              !sqlMapParamsData.sqlmap_monthly_increase ||
              sqlMapParamsData.sqlmap_monthly_increase.length <= 0
            ) {
              sqlMapParamsData.sqlmap_monthly_increase =
                res.sql_map_table || [];
            }
            this.sqlMapParamsData = { ...sqlMapParamsData, id: this.id };
            this.review_id = res.review_id;

            this.getSqlAdviceInfoData(this.id);
            this.setNavi();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.loading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 获取索引建议数据
    getSqlAdviceInfoData(id) {
      sqlAdviceInfo({ id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            this.sqlPlanInfo = res.data.data || [];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    activeChange(data) {
      this.activeKey = data;
    },
    // 上一条
    onPrev() {
      this.activeKey = 'sqlInfo';
      this.refresh({ id: this.prevId, is_cache: 1, timestamp: this.timestamp });
    },
    // 下一条
    onNext() {
      this.activeKey = 'sqlInfo';
      this.refresh({ id: this.nextId, is_cache: 1, timestamp: this.timestamp });
    },
    onAudit() {
      this.$refs.audit.show(this.reviewId);
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        const routeArr = [
          'project-review-detail',
          'file-review-detail',
          'procedure-review-detail'
        ];
        if (routeArr.includes(key)) {
          path = sourcePath.replace(':id', this.review_id);

          // 处理query
          const { query } = this.$route;
          if (query) {
            const _qstr = Object.keys(query)
              .map(key => `${key}=${query[key]}`)
              .join('&');
            _qstr && (path += `?${_qstr}`);
          }
        }
        return path;
      });
      if (this.id !== this.$route.params.id) {
        this.$router.push({
          name: this.routeName,
          params: { id: this.id, params: this.searchData },
          query: this.$route.query
        });
      }
    },
    saveSqlMapParams(data) {
      saveSqlmapConfig({
        ...data,
        id: this.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            setTimeout(() => {
              if (this.headerInfo.index < this.headerInfo.count) {
                this.onNext();
              } else {
                this.refresh({ id: this.id });
              }
            }, 2000);
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 返回
    toBack() {
      const { navi = [] } = this.$route.meta || {};
      const prevKey = navi[navi.length - 2];
      const prevItem = window.routesMap[prevKey];
      let path = '';
      if (prevItem) {
        path = prevItem.path.replace(':id', this.review_id);

        // 处理query
        const { query } = this.$route;
        if (query) {
          const _qstr = Object.keys(query)
            .map(key => `${key}=${query[key]}`)
            .join('&');
          _qstr && (path += `?${_qstr}`);
        }
      }
      if (path) {
        this.$router.push({
          path
        });
        return;
      }
      this.$router.back();
    },
    // 获取每一条错误详情
    // getEveryDetailErrorFn(params = {}) {
    //   getEveryDetailError(params)
    //     .then(res => {
    //       if (_.get(res, 'data.code') == 0) {
    //         const resData = _.get(res, 'data.data');
    //         if (resData.sql_format == 'sql' && resData.all_wrong == 1) {
    //           this.$set(this, 'sqlErrorMessage', resData.error_message);
    //         } else {
    //           this.$set(this, 'sqlErrorMessage', []);
    //         }
    //       } else {
    //         this.$hideLoading({
    //           method: 'error',
    //           tips: _.get(res, 'data.message')
    //         });
    //       }
    //     })
    //     .catch(e => {
    //       this.$hideLoading({
    //         method: 'error',
    //         tips: _.get(e || {}, 'response.data.message') || '请求失败'
    //       });
    //     });
    // },
    getPopupContainer() {
      return document.getElementById('rootContent');
    },
    // 建议保存
    onSaveAdvice(data) {
      this.loading = true;
      saveAdvice({
        id: this.id,
        dba_comment: data
      })
        .then(e => {
          this.loading = false;
          if (e.data.code === 0) {
            this.loading = false;
            this.$message.success(e.data.message || '成功');
            this.refresh({ id: this.id });
          } else {
            this.loading = false;
            this.$message.warn(e.data.message || '系统错误');
          }
        })
        .catch(e => {
          this.loading = false;
        });
    },
    // 打标
    addSqlTag() {
      this.$refs.tag.show();
    },
    // 保存打标
    onSaveLabel(data) {
      this.$showLoading();
      const params = {
        label_attribute: data.label_attribute,
        permanent_day: data.permanent_day,
        id: [this.id]
      };
      saveLabel(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.refresh({ id: this.id });
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 保存SQL备注
    onSaveNote(data = {}) {
      this.$showLoading({ useProgress: true });
      data.id = this.id;
      saveSqlMap(data)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.refresh({ id: this.id });
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  },
  watch: {
    '$store.state.account.user': {
      handler(newVal = {}) {
        let roleArr = ['dba', 'virtual_dev'];
        this.canDo = roleArr.includes(newVal.role);
      },
      immediate: true
    },
    // '$route.params.id': {
    //   handler(newVal) {
    //     this.getEveryDetailErrorFn({ detail_id: newVal });
    //   }
    // },
    '$route.name': {
      handler(newVal) {
        this.routeName = newVal;
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.review-agree {
  border-color: rgba(35, 190, 108, 1);
  background: rgba(35, 190, 108, 1);
  color: #fff;
}
.review-agree-overlay {
  .ant-dropdown-menu {
    background: rgba(35, 190, 108, 1);
    .ant-dropdown-menu-item {
      color: #fff;
      &.ant-dropdown-menu-item-active {
        background: #52c287;
      }
    }
  }
}
/deep/.review-disagree {
  border-color: rgba(255, 83, 84, 1);
  background: rgba(255, 83, 84, 1);
  color: #fff;
}
.review-disagree-overlay {
  .ant-dropdown-menu {
    background: rgba(255, 83, 84, 1);
    .ant-dropdown-menu-item {
      color: #fff;
      &.ant-dropdown-menu-item-active {
        background: #fb8283;
      }
    }
  }
}
.review-project-id {
  .frame-fixed-top-left(-43px, 224px);
  background: #ffffff;
  border: 1px solid #e4e4e7;
  border-radius: 24px;
  display: block;
  padding: 2px 8px;
  &.procedure {
    .frame-fixed-top-left(-43px, 254px);
  }
}
.frame-button-wrapper {
  display: flex;
  top: -45px !important;
  right: 0 !important;
  .page-change-container {
    .page-info {
      margin-right: 12px;
    }
    .ant-btn-group {
      .ant-btn {
        width: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        &:first-child {
          border-radius: 6px 0 0 6px !important;
        }
        &:last-child {
          border-radius: 0 6px 6px 0 !important;
        }
        .anticon {
          font-size: 12px;
        }
      }
    }
  }
  .line {
    width: 1px;
    height: 32px;
    margin: 0 8px 0 16px;
    background: #f0f0f0;
  }
}
.preaudit-review-wraper {
  color: rgba(86, 87, 89, 1);
  .ai-result-and-splan-info {
    display: flex;
    justify-content: space-between;
    &.has-splan-info {
      .ai-result {
        width: 60%;
      }
      .splan-info {
        width: 38%;
        display: block;
      }
    }
    .ai-result {
      width: 100%;
    }
    .splan-info {
      display: none;
    }
  }
}

/deep/ .chart-name span {
  font-size: 16px;
}

/deep/ .chart-name span:nth-child(2) {
  color: rgba(0, 0, 0, 0.85);
}

.review-detail-toback-btn {
  position: absolute;
  top: 24px;
  right: 250px;
}
</style>