<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    :title="'参数选择'"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Table ref="table" v-bind="params" />
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
// import _ from 'lodash';
import config from './config';
// import common from '@/utils/common';

export default {
  components: { Table },
  props: {
    dbType: {
      type: String,
      default: 'oracle'
    }
  },
  data() {
    this.config = config(this);
    return {
      type: 'add',
      visible: false,
      params: {
        url: '',
        reqParams: {
          pageSize: undefined
        },
        columns: this.config.columns,
        rowSelection: {
          type: 'radio'
        },
        pagination: false,
        scroll: { y: 500 },
        rowKey: 'id'
      },
      data: {}
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(type, data = {}) {
      this.visible = true;
      this.type = type;
      this.data = data;
      const dbType = this.dbType.toLowerCase();
      this.$set(
        this.params,
        'url',
        `/sqlreview/project/rule/metrics/?db_type=${dbType}`
      );
      this.$set(this.params, 'reqParams', {
        pageSize: undefined,
        _t: +new Date()
      });
      const { editRecord = {} } = data;
      this.$nextTick(() => {
        const { table } = this.$refs;
        table.setSelectedInfo([editRecord.target]);
      });
    },
    hide() {
      this.visible = false;
      const { table } = this.$refs;
      table.setSelectedInfo([]);
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { table } = this.$refs;
      this.$emit('save', {
        type: this.type,
        data: {
          sourceData: this.data,
          selectedRows: table.selectedRows
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
