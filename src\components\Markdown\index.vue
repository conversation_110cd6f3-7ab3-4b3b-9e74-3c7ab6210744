<template>
  <div :class="['custom-markdown']">
    <div class="custom-markdown-container"></div>
  </div>
</template>

<script>
import Editor from '@toast-ui/editor';
import '@toast-ui/editor/dist/toastui-editor.css';
import '@toast-ui/editor/dist/i18n/zh-cn.js';

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: String,
    options: {
      type: Object,
      default: () => ({})
    }
    // mode: {
    //   type: String,
    //   default: 'Markdown' // Markdown, HTML
    // }
  },
  data() {
    this.defaultOptions = {};
    return {};
  },
  created() {},
  mounted() {
    this.init();
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
    }
  },
  methods: {
    init() {
      this.editor = new Editor(
        Object.assign(
          {
            el: this.$el.querySelector('.custom-markdown-container'),
            height: '500px',
            initialEditType: 'markdown',
            previewStyle: 'vertical',
            language: 'zh-CN',
            autofocus: false
          },
          this.options,
          {
            initialValue: this.value || ''
          }
        )
      );
      this.editor.on('change', e => {
        this.$emit('change', this.editor.getMarkdown());
        // console.log(this.editor.getMarkdown())
      });
    },
    format(data = '') {},
    isFocus() {
      return this.$el.querySelector('.ProseMirror-focused') != null;
    }
  },
  watch: {
    value(newVal, oldVal) {
      if (this.editor && !this.isFocus() && oldVal == null && newVal !== oldVal) {
        this.editor.setMarkdown(newVal || '', false);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.custom-markdown {
  width: 100%;
  /deep/ .custom-markdown-container {
    .ProseMirror {
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin-top: 0;
        margin-bottom: 0.6em;
        font-weight: 500;
      }
    }

    .toastui-editor-mode-switch {
      line-height: 18px;
    }
  }
}
</style>