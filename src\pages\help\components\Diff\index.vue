<template>
  <div :style="{ position: 'relative' }">
    <Diff :list="list" :isShowSingleWhenNoDiff="true" />
  </div>
</template>

<script>
import Diff from '@/components/Diff';

export default {
  components: { Diff },
  props: {},
  data() {
    return {
      list: [
        {
          filename: '原sql → 目标sql',
          oldStr: `SELECT
 t3.id as knowledgeId,
 t1.knowledge_category as knowledgeCategory,
 t3.title,
 t3.context
FROM
 cd_knowledge_base t1
 RIGHT JOIN cd_knowledge_relationship t2 on t1.id = t2.category_id
WHERE
 t1.is_deleted = '0'
 and t2.is_deleted = '0'
 and t3.is_deleted = '0'
 and common_question = 1
 AND t3.title like concat('%', 1, '%')
 or t3.context like concat('%', 1, '%')
limit
 1 offset 1`,
          newStr: `SELECT
 t3.id as knowledgeId,
 t1.knowledge_category as knowledgeCategory,
 t1.knowledge_category as knowledgeCategoryssss,
 t3.title,
 t3.context
FROM
 cd_knowledge_base t1
WHERE
 t1.is_deleted = '0'
 and t2.is_deleted = '0'
 and t3.is_deleted = '0'
 and common_question = 1
 AND t3.title like concat('%', 444, '%')
 or t3.context like concat('%', 1, '%')
limit
 1 offset 1`,
          type: 'sql',
          keywords: ['t3.title']
        },
        {
          oldStr: `console.log('123')`,
          newStr: `console.log('1sss3 dfdfdfdf')`,
          type: 'js'
        },
        {
          oldStr: `private List<NetOffDTO> levelInfoParam(List<Long> uniqueIdList, List<NameListBaseInfoModel> baseInfoModels, List<ProcessCacheDTO> list) {
    List<WxLevelInfoEntity> levelInfoEntityList = wxLevelInfoMapper.selectList(new EntityWrapper<WxLevelInfoEntity>().in("unique_id", uniqueIdList));
    List<PreRiskSubCenterDTO> preRiskConstants = syncCisCustInfoMapper.selectPreRiskByQualificationCache(list);
    List<NetOffDTO> offDTOList = Lists.newArrayListWithCapacity(uniqueIdList.size());
    levelInfoEntityList.forEach(levelInfo -> {
        NetOffDTO dto = new NetOffDTO();
        BeanUtils.copyProperties(levelInfo, dto);
        dto.setIsCredit(stringConversion(levelInfo.getIsCredit()));
        dto.setIsLife(stringConversion(levelInfo.getIsLife()));
        dto.setMediaSource(levelInfo.getSource());
        dto.setMediaCode(levelInfo.getMediaSubId());
        dto.setPloanCode(levelInfo.getPLoanCode());
        dto.setApplyStatus(RuleFieldConvertUtil.resetApplyStatus(levelInfo.getApplyStatus()));
        dto.setGbdTag(RuleFieldConvertUtil.resetGbdTag(levelInfo.getGbdTag()));
        dto.setNextProcess(levelInfo.getFlowTo());
        dto.setYztCxTag(RuleFieldConvertUtil.resetYztCxTag(levelInfo.getOneAndCrmType()));
        dto.setOffNode(levelInfo.getPOffStatusCode());
        dealNetOffPartParam(dto, preRiskConstants);
        baseInfoModels.forEach(baseInfo -> {
            if (levelInfo.getUniqueId().intValue() == baseInfo.getIdSlmNameListBaseInfo().intValue()) {
                dto.setCity(baseInfo.getCity());
                dto.setAge(ageConversion(baseInfo.getAge()));
                dto.setCustName(baseInfo.getCustName());
                dto.setMobile(baseInfo.getMobile());
                dto.setPhone(baseInfo.getMobile());
                dto.setIdNo(baseInfo.getIdNo());
                dto.setGender(baseInfo.getGender());
                return;
            }
        });
        offDTOList.add(dto);
    });
    return offDTOList;
}`,
          newStr: `private List<NetOffDTO> levelInfoParam(List<Long> uniqueIdList, List<NameListBaseInfoModel> baseInfoModels, List<ProcessCacheDTO> list) {
    List<WxLevelInfoEntity> levelInfoEntityList = wxLevelInfoMapper.selectList(new EntityWrapper<WxLevelInfoEntity>().in("unique_id", uniqueIdList));
    List<PreRiskSubCenterDTO> preRiskConstants = syncCisCustInfoMapper.selectPreRiskByQualificationCache(list);
    List<NetOffDTO> offDTOList = Lists.newArrayListWithCapacity(uniqueIdList.size());
    levelInfoEntityList.forEach(levelInfo -> {
        NetOffDTO dto = new NetOffDTO();
        BeanUtils.copyProperties(levelInfo, dto);
        dto.setIsCredit(stringConversion(levelInfo.getIsCredit()));
        dto.setIsLife(stringConversion(levelInfo.getIsLife()));
        dto.setMediaSource(levelInfo.getSource());
        dto.setMediaCode(levelInfo.getMediaSubId());
        dto.setPloanCode(levelInfo.getPLoanCode());
        dto.setApplyStatus(RuleFieldConvertUtil.resetApplyStatus(levelInfo.getApplyStatus()));
        dto.setGbdTag(RuleFieldConvertUtil.resetGbdTag(levelInfo.getGbdTag()));
        dto.setNextProcess(levelInfo.getFlowTo());
        dto.setYztCxTag(RuleFieldConvertUtil.resetYztCxTag(levelInfo.getOneAndCrmType()));
        dto.setOffNode(levelInfo.getPOffStatusCode());
        dealNetOffPartParam(dto, preRiskConstants);
        baseInfoModels.forEach(baseInfo -> {
            if (levelInfo.getUniqueId().intValue() == baseInfo.getIdSlmNameListBaseInfo().intValue()) {
                dto.setCity(baseInfo.getCity());
                dto.setAge(ageConversion(baseInfo.getAge()));
                dto.setCustName(baseInfo.getCustName());
                dto.setMobile(baseInfo.getMobile());
                dto.setPhone(baseInfo.getMobile());
                dto.setIdNo(baseInfo.getIdNo());
                dto.setGender(baseInfo.getGender());
                return;
            }
        });
        offDTOList.add(dto);
    });
    return offDTOList;
}`,
          type: 'java'
        }
      ]
    };
  },
  mounted() {},
  created() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
</style>