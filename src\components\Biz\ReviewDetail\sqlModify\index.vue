<template>
  <!-- <a-card type="small" :bordered="false" class="small-card"> -->
  <!-- <div slot="title">
      <a-icon type="exception" />
      <span style="margin-left: 4px;">SQL改写</span>
  </div>-->
  <div class="sql-modify-content">
    <!-- <div>AI已为您生成智能SQL改写方案，可一键复制，轻松解决SQL问题。</div> -->
    <a @click="onClick" class="sql-modify-button">
      <span>查看改写方案</span>
      <custom-icon type="lu-icon-right" />
    </a>
  </div>
  <!-- </a-card> -->
</template>

<script>
import config from './config';
export default {
  components: {},
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  computed: {},
  data() {
    this.config = config(this);
    return {};
  },
  methods: {
    onClick() {
      this.$emit('viewModifyPlan');
    }
  },
  filters: {}
};
</script>

<style lang="less" scoped>
.small-card {
  margin-bottom: 24px;

  .sql-modify-content {
    .sql-modify-button {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #008adc;
      font-weight: 400;
    }
    // .ant-btn {
    //   margin-top: 12px;
    // }
  }
}
</style>