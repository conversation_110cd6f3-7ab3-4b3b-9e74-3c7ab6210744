import Mock from '@/utils/mock'
import common from '@/utils/common'

Mock.mock(/\/api\/user\/test\/table(\?[\s\S]*)?$/, 'get', (options) => {
  const query = common.getQueryParams(options.url);
  const { pageNum = 1, pageSize = 10 } = query;
  const data = Array(18)
    .fill()
    .map((item, i) => ({
      key: i,
      username: '<PERSON>' + i,
      userRole: 'dba',
      email: '<EMAIL>'
    }));
  return {
    resCode: '0',
    resMsg: 'ok',
    data: {
      dataSource: _.merge([], data).slice(
        (pageNum - 1) * pageSize,
        (pageNum - 1) * pageSize + pageSize
      ),
      pageNum,
      pageSize,
      total: data.length
    }
  };
})

Mock.mock(/\/api\/user\/test\/select(\?[\s\S]*)?$/, 'get', (options) => {
  // const query = common.getQueryParams(options.url);
  // const {} = query;
  const data = Array(10)
    .fill()
    .map((item, i) => ({
      label: 'New York No. 1 Lake Park' + i,
      value: i
    }));
  return {
    resCode: '0',
    resMsg: 'ok',
    data: data
  };
})

Mock.mock(/\/api\/user\/table(\?[\s\S]*)?$/, 'get', (options) => {
  const query = common.getQueryParams(options.url);
  const { page = 1, pageSize = 10 } = query;
  const data = Array(18)
    .fill()
    .map((item, i) => ({
      key: i,
      name: 'John Brown' + i,
      role: 'dba',
      email: '<EMAIL>',
      id: '15',
      password: '1111'
    }));
  return {
    code: '0',
    message: 'ok',
    data: {
      results: _.merge([], data).slice(
        (page - 1) * pageSize,
        (page - 1) * pageSize + pageSize
      ),
      page,
      pageSize,
      count: data.length
    }
  };
})
