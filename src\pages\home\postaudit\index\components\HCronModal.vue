<template>
  <div>
    <div style="margin-bottom: 15px">
      <a-radio-group v-model="radioValue" @change="onChange">
        <a-radio :style="radioStyle" :value="0">
          一直运行
          <a-popover placement="right">
            <template slot="content">
              <span>运行时将间隔10分钟进行一次数据采集</span>
            </template>
            <a-icon type="question-circle" style="margin-left: 5px" />
          </a-popover>
        </a-radio>
        <a-radio :style="radioStyle" :value="1">
          每天
          <a-time-picker
            style="margin-left: 20px"
            placeholder="开始时间"
            v-bind="startPickerAttrs"
            v-model="timeStart"
            :format="type == 'hour' ? 'HH:mm' : 'HH:mm:ss'"
            @change="
                (val, dateStrings) => changeTime(val, dateStrings, 'timeStart')
              "
            :disabled="radioValue == 0"
          />~
          <a-time-picker
            placeholder="结束时间"
            v-model="timeEnd"
            v-bind="endPickerAttrs"
            :format="type == 'hour' ? 'HH:mm' : 'HH:mm:ss'"
            @change="
                (val, dateStrings) => changeTime(val, dateStrings, 'timeEnd')
              "
            :disabled="radioValue == 0"
          />
        </a-radio>
      </a-radio-group>
    </div>
  </div>
</template>
<script>
// import config from './config';
import Form from '@/components/Form';
import LimitLabel from '@/components/LimitLabel';

export default {
  props: {
    pgData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'default' // hour整点
    }
  },
  components: { Form, LimitLabel },
  data() {
    // this.config = config(this);
    return {
      id: '',
      radioValue: 0,
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
        marginBottom: '15px'
      },
      timeStart: '',
      timeEnd: ''
    };
  },
  computed: {
    startPickerAttrs() {
      let res = {
        valueFormat: 'H:m:s'
      };
      switch (this.type) {
        case 'hour':
          res = {
            ...res,
            hideDisabledOptions: true,
            disabledMinutes: this.chooseNull,
            disabledSeconds: this.chooseNull
          };
          break;
        default:
          res = {
            ...res,
            hideDisabledOptions: false
          };
          break;
      }
      return res;
    },
    endPickerAttrs() {
      let res = {
        valueFormat: 'H:m:s'
      };
      switch (this.type) {
        case 'hour':
          res = {
            ...res,
            hideDisabledOptions: true,
            disabledHours: () => {
              let hours = [];
              let time = this.timeStart;
              let timeArr = time.split(':');
              for (var i = 0; i < parseInt(timeArr[0]); i++) {
                hours.push(i);
              }
              return hours;
            },
            disabledMinutes: selectedHour => {
              let time = this.timeStart;
              let timeArr = time.split(':');
              let minutes = [];
              if (selectedHour == parseInt(timeArr[0])) {
                for (var i = 0; i <= parseInt(timeArr[1]); i++) {
                  minutes.push(i);
                }
              }
              return minutes;
            },
            disabledSeconds: this.chooseNull
          };
          break;
        default:
          res = {
            ...res,
            hideDisabledOptions: false,
            disabledHours: () => {
              let hours = [];
              let time = this.timeStart;
              let timeArr = time.split(':');
              for (var i = 0; i < parseInt(timeArr[0]); i++) {
                hours.push(i);
              }
              return hours;
            },
            disabledMinutes: selectedHour => {
              let time = this.timeStart;
              let timeArr = time.split(':');
              let minutes = [];
              if (selectedHour == parseInt(timeArr[0])) {
                for (var i = 0; i < parseInt(timeArr[1]); i++) {
                  minutes.push(i);
                }
              }
              return minutes;
            },
            disabledSeconds: (selectedHour, selectedMinute) => {
              let time = this.timeStart;
              let timeArr = time.split(':');
              let second = [];
              if (
                selectedHour == parseInt(timeArr[0]) &&
                selectedMinute == parseInt(timeArr[1])
              ) {
                for (var i = 0; i <= parseInt(timeArr[2]); i++) {
                  second.push(i);
                }
              }
              return second;
            }
          };
          break;
      }
      return res;
    }
  },
  methods: {
    onChange(e) {
      this.radioValue = e.target.value;
      this.clear();
    },
    clear() {
      this.timeStart = '';
      this.timeEnd = '';
    },
    save() {
      this.$showLoading();
    },
    weekChange(value) {
      this.radioValue = value;
    },
    changeTime(val, dateStrings, type) {
      this.radioValue = 1;
      if (type === 'timeStart') {
        this.timeStart = dateStrings;
      } else {
        this.timeEnd = dateStrings;
      }

      if (this.timeStart && this.timeEnd) {
        // 前面小于后面， 则清空后面
        // replaceAll()浏览器版本低不支持，用replace(/:/g, '')替换
        if (
          this.timeStart.replace(/:/g, '') >= this.timeEnd.replace(/:/g, '')
        ) {
          this.$message.warning('抱歉，结束时间不能小于或者等于开始时间')
          this.timeEnd = '';
        }
      }
    },
    chooseNull() {
      let res = [];
      for (var i = 1; i < 60; i++) {
        res.push(i);
      }
      return res;
    },
    getData() {
      return {
        timeStart: this.timeStart,
        timeEnd: this.timeEnd,
        runningStatus: this.radioValue
      };
    }
  },
  watch: {
    pgData: {
      handler(newVal, oldeVal) {
        this.radioValue = newVal.runningStatus || 0;
        this.timeStart = newVal.runStartTime || '';
        this.timeEnd = newVal.runEndTime || '';
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ant-radio-wrapper {
  border: none !important;
  padding: 12px 40px !important;
}
</style>
