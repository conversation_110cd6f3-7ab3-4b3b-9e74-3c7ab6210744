<template>
  <a-modal v-model="visible" title="申请白名单" @ok="saveSqlMapParams" width="45%">
    <!-- <a-card type="small" :bordered="false" class="sqlmap-params"> -->
    <div slot="title">
      <a-icon type="read" />
      <span style="margin-left: 4px;">SQLMAP运行状态评估:</span>
    </div>
    <div slot="extra" @click="onExpandSqlMapParams()">
      <!-- <a-icon
          :class="{
              'sqlmap-params-expand-icon': true,
              expanded: sqlMapParamsShow
            }"
          type="double-right"
      ></a-icon>-->
      <!-- <a-button type="primary" @click.stop="saveSqlMapParams">保存</a-button> -->
    </div>
    <div class="content">
      <Form ref="sqlMapParams" v-bind="sqlMapParams" :formData="sqlMapParamsData">
        <!-- <a
          slot="average_private"
          slot-scope="{ data }"
          @click="openFrequencyModal('average', data)"
        >
          自定义输入{{
          !isNaN(+data) && data !== null ? `【QPS: ${data}】` : ''
          }}
        </a>
        <a slot="max_private" slot-scope="{ data }" @click="openFrequencyModal('max', data)">
          自定义输入{{
          !isNaN(+data) && data !== null ? `【QPS: ${data}】` : ''
          }}
        </a>-->
      </Form>
      <!-- <div class="dba-name">
          <h4>
            DBA负责人：
            <span
              :style="{'color': (operator_dba ? '' : '#bfbfbf')}"
            >{{operator_dba || '待分配'}}</span>
          </h4>
      </div>-->
    </div>
    <!-- </a-card> -->
  </a-modal>
</template>
<script>
import Form from '@/components/Form';
import config from './config';
export default {
  props: {
    operator_dba: {
      type: String,
      default: ''
    },
    sqlMapParamsData: {
      type: Object,
      default: () => {}
    }
  },
  components: { Form },
  data() {
    this.config = config(this);
    return {
      visible: false,
      sqlMapParams: {
        fields: this.config.fields,
        fixedLabel: true,
        layout: 'horizontal',
        colon: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 20 }
      },
      sqlMapParamsShow: false
    };
  },
  methods: {
    show() {
      this.visible = true;
    },
    onExpandSqlMapParams() {},
    saveSqlMapParams() {
      const { sqlMapParams } = this.$refs;
      const data = sqlMapParams.getData();
      this.$emit('saveSqlMapParams', data);
      this.visible = false;
      this.$refs.sqlMapParams.resetFields();
    }
  }
};
</script>