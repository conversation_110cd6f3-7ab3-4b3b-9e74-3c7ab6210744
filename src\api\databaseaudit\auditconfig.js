import Http from '@/utils/request';
// 保存采集配置
export function saveCollectConfig(params) {
  return Http({
    url: `/sqlreview/after_audit/save_collect_config`,
    method: 'post',
    data: params
  });
}

export function collectSwitch(params) {
  return Http({
    url: `/sqlreview/after_audit/db_collect/switch`,
    method: 'post',
    data: params
  });
}

// 批量关闭//开启
export function batchSave(params) {
  return Http({
    url: `/sqlreview/after_audit/db_collect/batch_save`,
    method: 'post',
    data: params
  });
}
export default {};
