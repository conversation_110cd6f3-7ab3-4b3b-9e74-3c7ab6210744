export default function (ctx) {
  const tableColumns = [
    {
      title: 'schema',
      dataIndex: 'schema_name',
      key: 'schema_name'
    },
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name'
    },
    {
      title: '数据量',
      dataIndex: 'table_rows',
      key: 'table_rows'
    },
    {
      title: '分区类型',
      dataIndex: 'partition_type',
      key: 'partition_type'
    },
    {
      title: '分区键',
      key: 'part_column',
      dataIndex: 'part_column'
    },
    {
      title: '表状态',
      key: 'is_onlie',
      dataIndex: 'is_onlie',
      scopedSlots: { customRender: 'is_onlie' }
    },
    {
      title: '统计信息收集时间',
      key: 'last_collect_time',
      dataIndex: 'last_collect_time'
    },
    {
      title: '表注释',
      key: 'comment',
      dataIndex: 'comment',
      scopedSlots: { customRender: 'comment' }
    }
  ];

  const indexColumns = () => {
    return [
      {
        title: '索引名称',
        dataIndex: 'index_name',
        key: 'index_name'
      },
      {
        title: '索引类型',
        key: 'unique_name',
        dataIndex: 'unique_name'
      },
      {
        title: '索引字段',
        dataIndex: 'column_name',
        key: 'column_name'
      },
      {
        title: '字段类型',
        key: 'data_type',
        dataIndex: 'data_type'
      },
      {
        title: '字段位置',
        key: 'column_position',
        dataIndex: 'column_position'
      },
      {
        title: '区分度',
        key: 'cardinality',
        dataIndex: 'cardinality'
      }
    ];
  };
  const fieldColumns = [
    {
      title: '字段名',
      dataIndex: 'column_name',
      key: 'column_name'
    },
    {
      title: '数据类型',
      dataIndex: 'column_type',
      key: 'column_type'
    },
    {
      title: '可空',
      dataIndex: 'is_nullable',
      key: 'is_nullable',
      customRender: (text, record, index) => {
        return text == 'YES' ? '是' : '否'
      }
    },
    {
      title: '自增',
      key: 'auto_increment',
      dataIndex: 'auto_increment',
      customRender: (text, record, index) => {
        return text == 'YES' ? '是' : '否'
      }
    },
    {
      title: '缺省值',
      key: 'column_default',
      dataIndex: 'column_default'
    },
    {
      title: '备注',
      key: 'column_comment',
      dataIndex: 'column_comment'
    }
  ];
  return {
    tableColumns,
    indexColumns,
    fieldColumns
  };
}
