export default function (ctx) {
  const fields = () => {
    return [
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: '任务类型',
          key: 'mission_type',
          props: {
            options: [
              {
                label: '仅一次',
                value: 0
              },
              {
                label: '定时同步',
                value: 1
              }
            ]
          },
          listeners: {
            change: (value) => {
              ctx.$refs.form.saving({
                mission_type: value
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '同步时间',
          key: 'sync_time',
          hideComponent: true,
          slots: [{ key: 'sync_interval_once' }],
          visible: formData.mission_type == 0,
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '同步时间',
          key: 'sync_time',
          hideComponent: true,
          slots: [{ key: 'sync_interval_setting' }],
          visible: formData.mission_type == 1,
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      {
        type: 'Label',
        label: '最近同步时间',
        key: 'last_time',
        hideComponent: true,
        slots: [{ key: 'last_time' }],
        rules: [{ required: true, message: '该项为必填项' }]
      }
    ];
  };
  const cornInfo = [
    // 天 周 月
    {
      type: 'Select',
      label: '',
      key: 'type',
      width: '100%',
      props: {
        options: [
          {
            label: '每天',
            value: '每天'
          },
          {
            label: '每周',
            value: '每周'
          }
          // {
          //   label: '每月',
          //   value: '每月'
          // }
        ]
      },
      listeners: {
        change: (value) => {
          ctx.type = value;
          ctx.$refs.form.saving({
            type: value
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    },
    // 每周
    (formData = {}) => {
      return {
        type: 'Select',
        label: '',
        key: 'week',
        width: '100%',
        props: {
          options: ctx.weekOption
        },
        visible: formData.type === '每周',
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              week: value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      };
    },
    // 每月
    (formData = {}) => {
      return {
        type: 'Select',
        label: '',
        key: 'month',
        width: '100%',
        props: {
          options: ctx.monthOption
        },
        visible: formData.type === '每月',
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              month: value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      };
    },
    // 时间
    {
      type: 'TimePicker',
      label: '',
      key: 'time',
      width: '100%',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      },
      listeners: {
        change: (value) => {
          ctx.$refs.form.saving({
            time: value
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    }
  ];
  return {
    fields,
    cornInfo
  };
}
