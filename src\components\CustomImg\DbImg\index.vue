<!--
 * @Author: your name
 * @Date: 2021-11-30 15:25:42
 * @LastEditTime: 2021-11-30 16:58:30
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /packages/Users/<USER>/work/dbcm-web/src/components/CustomImg/DbImg/index.vue
-->
<template>
  <div class="database-image" v-if="UseIconOnly !== true">
    <Icon
      :imgStyle="imgStyle"
      :imgSrc="imgType[dbTxt]"
      :MergeIcon="MergeIcon"
      type="image"
      v-if="schemaName == ''"
    />
    <span class="iconClass" v-else>
      <Icon :iconType="iconType[dbTxt]" :MergeIcon="MergeIcon" type="icon" />
      <!-- <span :style="{ marginLeft: '6px' }">{{ schemaNameText }}</span> -->
      <LimitLabel
        :label="schemaNameText"
        :mode="mode"
        :limit="limit"
        :isNeedTips="isNeedTips"
        v-bind="LimitLabelProps"
        class="iconText"
      />
    </span>
  </div>
  <Icon :iconType="iconType[dbTxt]" :MergeIcon="MergeIcon" type="icon" v-else />
</template>

<script>
import Icon from './Icon.vue';
import LimitLabel from '@/components/LimitLabel';
export default {
  components: { Icon, LimitLabel },
  props: {
    type: String,
    schemaName: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    imgStyle: {
      type: Object,
      default: () => {
        return {};
      }
    },
    limit: Number,
    mode: String,
    UseIconOnly: {
      type: Boolean,
      default: false
    },
    isNeedTips: {
      type: Boolean,
      default: true
    },
    MergeIcon: {
      type: Boolean,
      default: false
    },
    LimitLabelProps: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      imgType: {
        ORACLE: require('@/assets/img/oracle.svg'),
        MYSQL: require('@/assets/img/mysql.svg'),
        PGSQL: require('@/assets/img/postgresql.svg'),
        HBASE: require('@/assets/img/hbase.svg'),
        CLICKHOUSE: require('@/assets/img/clickhouse.svg'),
        KAFKA: require('@/assets/img/kafka.svg'),
        ELASTIC: require('@/assets/img/elastic.svg'),
        ELASTICSEARCH: require('@/assets/img/elastic.svg'),
        TIDB: require('@/assets/img/tidb.svg'),
        POSTGRESQL: require('@/assets/img/postgresql.svg'),
        POSTGRES: require('@/assets/img/postgresql.svg'),
        POSTGRE: require('@/assets/img/postgresql.svg'),
        UBISQL: require('@/assets/img/ubisql.svg'),
        RASESQL: require('@/assets/img/rasesql.svg'),
        OB_MYSQL: require('@/assets/img/oceanbase.svg'),
        DB2: require('@/assets/img/db2.svg'),
        OCEANBASE: require('@/assets/img/oceanbase.svg'),
        STARROCKS: require('@/assets/img/starrocks.svg'),
        SQLSERVER: require('@/assets/img/sqlserver.svg'),
        TD_MYSQL: require('@/assets/img/tdsql.svg'),
        TDSQL: require('@/assets/img/tdsql1.svg'),
        OB_ORA: require('@/assets/img/oceanbase.svg'),
        OB_ORACLE: require('@/assets/img/oceanbase.svg'),
        // IMPALA: require('@/assets/img/impala.svg'),
        GBASE: require('@/assets/img/gbase.svg'),
        GAUSSDB: require('@/assets/img/gaussdb.svg'),
        OPENGAUSS: require('@/assets/img/opengauss.svg'),
        HUDI: require('@/assets/img/hudi.svg'),
        KINGBASE: require('@/assets/img/kingbase.svg'),
        HIVE: require('@/assets/img/hive.svg'),
        IMPALA: require('@/assets/img/impala.svg'),
        DM: require('@/assets/img/dameng.svg'),
        ROCKETMQ: require('@/assets/img/rocketmq.svg'),
        PRESTO: require('@/assets/img/presto.svg'),
        RDS_MYSQL: require('@/assets/img/rds-mysql.svg'),
        GOLDENDB: require('@/assets/img/goldendb.svg'),
        MOGDB: require('@/assets/img/mogdb.svg'),
        DORIS: require('@/assets/img/doris.svg'),
        TD_PGSQL: require('@/assets/img/tidb.svg')
      },
      iconType: {
        ORACLE: 'lu-icon-oracle',
        MYSQL: 'lu-icon-mysql',
        PGSQL: 'lu-icon-pgsql',
        HBASE: 'lu-icon-hbase',
        CLICKHOUSE: 'lu-icon-clickhouse',
        KAFKA: 'lu-icon-kafka',
        ELASTICSEARCH: 'lu-icon-elasticsearch',
        POSTGRES: 'lu-icon-pgsql',
        POSTGRE: 'lu-icon-pgsql',
        TIDB: 'lu-icon-tidb',
        STARROCKS: 'lu-icon-starrocks',
        UBISQL: 'lu-icon-ubisql',
        RASESQL: 'lu-icon-rosesql',
        OB_MYSQL: 'lu-icon-oceanbase',
        DB2: 'lu-icon-db2',
        OCEANBASE: 'lu-icon-oceanbase',
        SQLSERVER: 'lu-icon-sql-server',
        TD_MYSQL: 'lu-icon-tdsql-1',
        FILESYSTEM: 'folder-open',
        TDSQL: 'lu-icon-tdsql-1',
        OB_ORA: 'lu-icon-oceanbase',
        OB_ORACLE: 'lu-icon-oceanbase',
        // IMPALA: 'lu-icon-impala',
        GBASE: 'lu-icon-gbase',
        GAUSSDB: 'lu-icon-gaussdb',
        OPENGAUSS: 'lu-icon-opengauss',
        HUDI: 'lu-icon-hudi',
        KINGBASE: 'lu-icon-kingbase',
        HIVE: 'lu-icon-hive',
        IMPALA: 'lu-icon-impala1',
        DM: 'lu-icon-dameng',
        ROCKETMQ: 'lu-icon-apacherocketmq',
        PRESTO: 'lu-icon-presto',
        RDS_MYSQL: 'lu-icon-rds',
        GOLDENDB: 'lu-icon-goldendb',
        MOGDB: 'lu-icon-mogdb',
        DORIS: 'lu-icon-Doris',
        TD_PGSQL: 'lu-icon-tdsql-1'
      },
      dbNameMap: {
        ORACLE: 'Oracle',
        MYSQL: 'MySQL',
        MONGODB: 'MongoDB',
        TIDB: 'TiDB',
        KAFKA: 'Kafka',
        HBASE: 'HBase',
        CLICKHOUSE: 'ClickHouse',
        POSTGRES: 'PostgreSQL',
        POSTGRE: 'PostgreSQL',
        ELASTICSEARCH: 'Elasticsearch',
        STARROCKS: 'StarRocks',
        UBISQL: 'UbiSQL',
        RASESQL: 'RaseSQL',
        OB_MYSQL: 'OB_MySQL',
        DB2: 'DB2',
        TDSQL: 'TDSQL',
        OB_ORA: 'OB_ORA',
        OB_ORACLE: 'OB_ORACLE',
        IMPALA: 'IMPALA',
        GBASE: 'GBASE',
        GAUSSDB: 'GAUSSDB',
        OPENGAUSS: 'OPENGAUSS',
        HUDI: 'HUDI',
        KINGBASE: 'KINGBASE',
        HIVE: 'HIVE',
        DM: 'DM',
        AURORA_MYSQL: 'AURORA_MYSQL',
        AURORA_POSTGRES: 'AURORA_POSTGRES',
        ROCKETMQ: 'ROCKETMQ',
        PRESTO: 'PRESTO',
        RDS_MYSQL: 'RDS_MYSQL',
        GOLDENDB: 'GOLDENDB',
        MOGDB: 'MOGDB',
        DORIS: 'DORIS',
        TD_PGSQL: 'TD_PGSQL'
      }
    };
  },
  computed: {
    dbTxt() {
      const { type = '', value = '' } = this;
      return type || value;
    },
    schemaNameText() {
      const upperName = this.schemaName + '' || '';
      const text = this.dbNameMap[upperName];
      return text || this.schemaName || '';
    }
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>
<style lang="less" scoped>
.database-image {
  /deep/ img {
    opacity: 0.9;
    filter: alpha(opacity=90);
    height: 32px;
    &:hover {
      opacity: 1;
      filter: alpha(opacity=100);
    }
    transition: opacity 0.3s;
    -webkit-transition: opacity 0.3s;
    // width: 100%;
  }
  .iconClass {
    display: flex;
    align-items: center;
  }
  /deep/ .iconStyle {
    // padding-top: 4px;
  }
  .iconText {
    margin-left: 6px;
  }
  // max-width: 200px;
  // min-width: 100px;
}
</style>