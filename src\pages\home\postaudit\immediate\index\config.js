export default function (ctx) {
  const columns = [
    {
      title: '数据源名称',
      dataIndex: 'name',
      key: 'name',
      width: 200
    },
    {
      title: '数据库类型',
      dataIndex: 'db_type',
      key: 'db_type',
      scopedSlots: { customRender: 'db_type' },
      width: 150
    },
    {
      title: '环境',
      key: 'env',
      dataIndex: 'env',
      scopedSlots: { customRender: 'env' },
      width: 150
    },
    {
      title: '状态',
      key: 'running_status',
      dataIndex: 'running_status',
      scopedSlots: { customRender: 'running_status' },
      width: 150
    },
    {
      title: '运行时间',
      key: 'running_time',
      dataIndex: 'running_time',
      scopedSlots: { customRender: 'running_time' },
      width: 180
    },
    // {
    //   title: '数据采集频率',
    //   key: 'collection_frequency',
    //   dataIndex: 'collection_frequency',
    //   width: 150
    // },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 180,
      fixed: 'right'
    }
  ]
  const fields = [
    {
      type: 'Input',
      label: '数据源名称',
      key: 'name',
      mainSearch: true,
      props: {
        placeholder: '请输入数据源名称'
      }
    },
    {
      type: 'Select',
      label: '数据源类型',
      key: 'db_type',
      props: {
        options: [
          {
            label: 'DB2',
            value: 'db2'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '环境',
      key: 'env',
      props: {
        options: [
          {
            label: '测试',
            value: 'test'
          },
          {
            label: '生产',
            value: 'prod'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '状态',
      key: 'running_status',
      props: {
        options: [
          {
            label: '运行',
            value: '1'
          },
          {
            label: '停止',
            value: '0'
          }
        ]
      }
    }
  ];
  return {
    columns,
    searchFields: fields
  };
}
