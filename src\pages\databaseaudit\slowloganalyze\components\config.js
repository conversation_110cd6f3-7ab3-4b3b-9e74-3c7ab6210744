export default function (ctx) {
  const fields = [
    {
      type: 'DataBaseChoose',
      label: '数据源',
      key: 'data_source_id',
      props: {
        url: `/sqlreview/after_audit/get_slow_mysql_list`,
        reqParams: {},
        mode: 'default',
        placeholder: '请选择数据源',
        allowSearch: true,
        backSearch: true,
        loaded: (data = []) => {
          ctx.instanceList = data;
        },
        beforeLoaded(data) {
          return data.map((item) => {
            return {
              ...item,
              db_type: item.db_type,
              instance_usage: item.env,
              showText: item.label + '(' + item.db_url + ')'
            };
          });
        }
      },
      listeners: {
        change: (value) => { }
      },
      // rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      rules: []
    }
  ];

  return {
    fields
  };
}
