<template>
  <div class="review-header">
    <a-card type="small" class="a-card">
      <div class="content">
        <div class="title" v-if="false">
          <div class="title-project">SQLID</div>
          <div class="title-project-name">{{ headerInfo.project_name || '--' }}</div>
        </div>
        <div class="btns">
          <a-button class="back-btn" @click="toBack">返回</a-button>
          <a-button
            :disabled="idList.length > 0 ? currentNum === 0 : headerInfo.index === 1"
            @click="reviewHeaderBtns(5)"
          >
            <a-icon type="double-left" />上一条
          </a-button>
          <a-button
            :disabled="idList.length > 0 ? currentNum == idList.length-1 :headerInfo.count === headerInfo.index"
            @click="reviewHeaderBtns(6)"
          >
            下一条
            <a-icon type="double-right" />
          </a-button>
          <span class="pageInfo" v-if="idList.length > 0">{{ currentNum + 1}}/{{ idList.length }}</span>
          <span class="pageInfo" v-else>{{ headerInfo.index }}/{{ headerInfo.count }}</span>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script>
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    idList: {
      type: Array,
      default: () => []
    },
    currentNum: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    reviewHeaderBtns(number) {
      this.$emit('reviewHeaderBtns', number);
    },
    toBack() {
      this.$emit('toBack');
    }
  },
  computed: {}
};
</script>

<style lang="less" scoped>
.review-header {
  .a-card {
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
    border-radius: 8px;
  }
  margin-bottom: 24px;
  .content {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: right;
    .title {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .title-project {
        font-size: 15px;
        font-weight: 600;
        color: #565759;
      }
      .title-project-name {
        display: inline-block;
        padding: 0 24px;
        color: @primary-color;
        font-size: 12px;
        border: 1px solid @primary-color;
        border-radius: 24px;
        line-height: 24px;
        margin: 0 24px 0 12px;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &::before {
          content: '';
          display: inline-block;
          position: relative;
          left: -8px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: @primary-color;
        }
      }
    }
    .btns {
      .ant-btn {
        margin-right: 8px;
      }
    }
  }
}
</style>
