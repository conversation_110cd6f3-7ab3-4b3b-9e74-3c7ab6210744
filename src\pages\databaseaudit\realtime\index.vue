<template>
  <div class="page-real-time up-20">
    <a-tabs
      class="page-real-time-instance"
      v-model="activeKey"
      type="editable-card"
      @edit="onEdit"
      @change="onChange"
      v-if="panes.length > 0"
    >
      <a-tab-pane
        v-for="pane in panes"
        :key="pane.key"
        :closable="pane.closable"
      >
        <a-tooltip slot="tab" placement="bottom">
          <template slot="title">
            <div>{{ pane.label + '(' + pane.db_url + ')' }}</div>
          </template>
          <InstanceItem
            view="new"
            mode="ellipsis"
            :tagText="pane.instance_usage"
            :src="pane.db_type"
            :text="pane.label"
            :isNeedTips="false"
          />
          <!-- <div style="display: inline-block" v-show="activeKey == pane.key">
            <custom-icon class="icon-select-arrow" type="lu-icon-up" />
            <DataBaseChoose
              :value="pane.value"
              v-bind="instanceParams"
              @change="onInstanceChange"
            />
          </div> -->
        </a-tooltip>
        <OracleContent
          ref="pageList"
          :pane="pane"
          :id="pane.value"
          :paneKey="pane.key"
          v-if="pane.db_type == 'ORACLE'"
        />
        <DB2OrGaussDBContent
          ref="pageList"
          :pane="pane"
          :id="pane.value"
          :paneKey="pane.key"
          v-else-if="['DB2', 'GAUSSDB'].includes(pane.db_type)"
        />
        <PostgresqlContent
          ref="pageList"
          :pane="pane"
          :id="pane.value"
          :paneKey="pane.key"
          v-else-if="['POSTGRE'].includes(pane.db_type)"
        />
        <Content
          ref="pageList"
          :pane="pane"
          :id="pane.value"
          :paneKey="pane.key"
          v-else
        />
      </a-tab-pane>
    </a-tabs>
    <div class="ps-empty" v-else>
      <div>
        <div class="left-block">
          <div class="img">
            <img src="~@/assets/img/private/info.svg" alt />
          </div>
          <div class="title">操作手册</div>
        </div>
        <div class="middle-block"></div>
        <div class="right-block">
          <a-steps direction="vertical" size="small">
            <a-step title="请选择数据库数据源">
              <a-button slot="description" type="primary" @click="add"
                >选择数据源</a-button
              >
            </a-step>
            <a-step title="查询该数据源的Real-Time SQL信息" />
          </a-steps>
        </div>
      </div>
    </div>
    <InstanceModal ref="InstanceModal" @save="onAddPanel" :type="1" />
    <TipsModal ref="tips" />
  </div>
</template>

<script>
import InstanceItem from '@/components/Biz/InstanceItem';
import InstanceModal from '../components/Instance';
import TipsModal from './TipsModal';
import Content from './Content';
import OracleContent from './OracleContent';
import PostgresqlContent from './PostgresqlContent';
import DB2OrGaussDBContent from './DB2OrGaussDBContent';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import { checkPermission } from '@/api/databaseaudit/topsql';
export default {
  mixins: [bodyMinWidth(1366)],
  components: {
    Content,
    InstanceItem,
    InstanceModal,
    OracleContent,
    PostgresqlContent,
    DB2OrGaussDBContent,
    TipsModal
  },
  provide() {
    return {
      rootActiveKey: () => this.activeKey
    }
  },
  data() {
    let panes = [];
    let activeKey;
    const user = this.$store.state.account.user;
    const userName = user.name;
    const databseInfo = localStorage.getItem('realtime');
    const lastUserName = _.get(JSON.parse(databseInfo), 'name') || '';
    try {
      if (databseInfo && lastUserName == userName) {
        const info = JSON.parse(databseInfo) || {};
        panes = (info.panes || []).filter(item =>
          (item.key + '').startsWith('instanceId_')
        );
        if ((info.activeKey + '').startsWith('instanceId_')) {
          activeKey = info.activeKey;
        }
      }
    } catch (e) {}
    this.instanceList = [];
    return {
      instanceParams: this.getInstanceParams(),
      activeKey,
      panes,
      id: null,
      newTabIndex: 0,
      authInfo: null
    };
  },

  mounted() {
    // panes为空，直接跳出选择数据源
    if (this.panes.length <= 0) {
      this.add();
    }
  },
  methods: {
    getInstanceKey() {
      return `instanceId_${Math.random()
        .toString(36)
        .substr(2)}`;
    },
    getInstanceParams() {
      return {
        url: `/sqlreview/after_audit/get_data_source`,
        reqParams: { type: 1 },
        mode: 'default',
        placeholder: '请选择数据源',
        backSearch: false,
        dropdownMatchSelectWidth: false,
        allowClear: false,
        getPopupContainer: el => {
          return document.body;
        },
        loaded: (data = []) => {
          this.instanceList = data;
        },
        beforeLoaded(data) {
          return data.map(item => {
            return {
              ...item,
              db_type: item.db_type,
              instance_usage: item.env,
              showText: item.label
            };
          });
        }
      };
    },
    onInstanceChange(val) {
      // 验证instance
      checkPermission({ data_source_id: val })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const instanceItem = this.instanceList.find(
              item => item.value == val
            );
            this.replacePanel(instanceItem);
          } else if (_.get(res, 'data.code') == 4001) {
            const label = _.get(res, 'data.data.sql_text');
            const message = _.get(res, 'data.message');
            this.$refs.tips.show(label, message);
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
      // const instanceItem = this.instanceList.find(item => item.value == val);
      // this.replacePanel(instanceItem);
    },
    onChange(val) {
      this.cache();
    },
    onEdit(targetKey, action) {
      this[action](targetKey);
    },
    add() {
      this.$refs.InstanceModal.show();
    },
    onAddPanel(data) {
      // 验证instance
      checkPermission({ data_source_id: data.value })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            if (data) {
              const panes = this.panes;
              const activeKey = this.getInstanceKey();
              panes.push({ ...data, key: activeKey });
              this.panes = panes;
              this.activeKey = activeKey;
              this.onChange();
            }
          } else if (_.get(res, 'data.code') == 4001) {
            const label = _.get(res, 'data.data.sql_text');
            const message = _.get(res, 'data.message');
            this.$refs.tips.show(label, message);
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
      // if (data) {
      //   const panes = this.panes;
      //   const activeKey = this.getInstanceKey();
      //   panes.push({ ...data, key: activeKey });
      //   this.panes = panes;
      //   this.activeKey = activeKey;
      //   this.onChange();
      // }
    },
    replacePanel(data) {
      const existPane = this.panes.find(pane => pane.key === this.activeKey);
      if (existPane && data) {
        const activeKey = this.getInstanceKey();
        Object.assign(existPane, data, {
          key: activeKey
        });
        this.activeKey = activeKey;
        this.panes = [...this.panes];
        this.cache();
      }
    },
    remove(targetKey) {
      let activeKey = this.activeKey;
      let lastIndex;
      this.panes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1;
        }
      });
      const panes = this.panes.filter(pane => pane.key !== targetKey);
      if (panes.length && activeKey === targetKey) {
        if (lastIndex >= 0) {
          activeKey = panes[lastIndex].key;
        } else {
          activeKey = panes[0].key;
        }
      }
      this.panes = panes;
      this.activeKey = activeKey;
      this.cache();
    },
    cache() {
      const user = this.$store.state.account.user;
      const userName = user.name;
      localStorage.setItem(
        'realtime',
        JSON.stringify({
          activeKey: this.activeKey,
          panes: this.panes,
          name: userName
        })
      );
    }
  },
  watch: {
    activeKey: {
      handler(newVal, oldVal) {
        let _refs = this.$refs.pageList;
        (_refs || []).forEach(item => {
          if (newVal == item.paneKey) {
            item.resume();
          } else {
            item.pause();
          }
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.page-real-time {
  display: flex;
  flex-grow: 1;
  // background: #ffffff;+
  /deep/ .page-real-time-instance {
    border-radius: 12px 12px 0 0;
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    width: 100%;
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    // tab-bar
    > .ant-tabs-bar {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-end;
      align-items: flex-end;
      margin: 0;
      background: #f2f2f2;
      border-bottom: 0;
      .ant-tabs-tab {
        background: #f2f2f2;
        border-left: 1px solid #fff;
        border-radius: 0;
        margin: 0;
        border-right: 0;
        border-top: 0;
        padding: 0;
        &:first-child {
          border: none;
        }
        > div {
          display: flex;
          align-items: center;
          > span {
            display: flex;
          }
        }

        .biz-instance-item {
          display: inline-block;
          .instance-item-tag {
            background: #f2f2f2;
            border: 0;

            &::after {
              display: none;
            }

            .database-image {
              margin-left: 0;

              > span > .custom-icon {
                margin-right: 0;
              }
              > span > .iconText {
                overflow: hidden;
                max-width: 100px;
                > pre {
                  font-size: 13px;
                  color: #71717a;
                  font-weight: 400;
                  white-space: nowrap;
                }
              }
            }
          }

          &:hover {
            .iconText {
              color: @primary-color;
              font-weight: 500;
            }
          }
        }

        .icon-select-arrow {
          transform: rotate(180deg);
          transition: all 0.3s;
          color: #27272a;
        }

        .biz-data-base-choose {
          position: absolute;
          left: 0;
          right: 24px;
          top: 0;
          bottom: 0;
          opacity: 0;
          width: auto;

          > .ant-select-selection {
            height: 40px;
          }
        }

        &.ant-tabs-tab-active {
          background: #ffffff;
          // border: 1px solid #f0f0f0;

          .biz-instance-item {
            .instance-item-tag {
              background: #ffffff;
              .database-image {
                .iconClass {
                  .iconText {
                    color: @primary-color;
                    font-weight: 500;

                    > pre {
                      color: #27272a;
                      font-weight: 600;
                    }
                  }
                }
              }
            }
          }
        }

        .ant-tabs-close-x {
          margin-right: 8px;
          height: 12px;
        }
      }

      .ant-tabs-extra-content {
        height: 40px;
        margin-bottom: -1px;

        .ant-tabs-new-tab {
          width: 40px;
          height: 40px;
          background: #f2f2f2;
          border: none;
          border-left: 1px solid #fff;
        }
      }
    }
    // tab-content
    > .ant-tabs-content {
      display: flex;
      flex-grow: 1;
      > .ant-tabs-tabpane {
        &.ant-tabs-tabpane-active {
          flex-grow: 1;
        }
        &.ant-tabs-tabpane-inactive {
          // width: 0;
          display: none;
        }
      }
    }
  }
  .ps-empty {
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 800px;
    > div {
      display: flex;
      justify-content: center;
      .left-block {
        display: flex;
        flex-direction: column;
        align-items: center;
        .img {
          margin-bottom: 24px;
        }
        .title {
          text-align: center;
          font-size: 24px;
          color: #27272a;
        }
      }
      .middle-block {
        width: 64px;
      }
      .right-block {
        /deep/.ant-steps {
          .ant-steps-item {
            .ant-steps-item-container {
              .ant-steps-item-icon {
                background: #4db5f2;
                .ant-steps-icon {
                  color: #fff;
                }
              }
              .ant-steps-item-content {
                min-height: 125px;
                display: flex;
                overflow: visible;
                .ant-steps-item-title {
                  font-size: 16px;
                  color: #27272a;
                }
                .ant-steps-item-description {
                  .ant-btn {
                    // height: 24px;
                    // line-height: 24px;
                    position: relative;
                    top: -4px;
                    > span {
                      padding: 0;
                      font-size: 14px;
                      color: #ffffff;
                      font-weight: 600;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
