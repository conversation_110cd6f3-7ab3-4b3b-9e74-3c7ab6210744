<template>
  <div class="psc-left-source-database">
    <a-spin class="psc-lsd-tree-wrapper" :spinning="treeLoading">
      <VirtualTree
        ref="tree"
        v-bind="treeParams"
        :searchValue="searchValue"
        @search="(val) => onSearch('tree', val)"
        @update-visible-data="onUpdateTree"
        @node-click="onNodeClick"
      >
        <!-- <a-dropdown
          style="flex-grow:1"
          :trigger="['contextmenu']"
          slot="title"
          slot-scope="{item}"
          @visibleChange="e => onVisibleChange(e, item)"
          @contextmenu.prevent="onContextmenu"
        >-->
        <div
          style="flex-grow:1"
          slot="title"
          slot-scope="{item}"
          @contextmenu.prevent.stop="(e) => onContextmenu(e, item, (getNodeInfo(item).menus || []))"
          @mousedown="(e) => onMousedown(e, item)"
        >
          <div
            :class="['psc-lsd-tree-item', item.errTips && 'FAILURE', item.has_privilege === false && 'no_privilege']"
          >
            <template v-if="getNodeInfo(item)">
              <!-- 普通节点 -->
              <custom-icon
                class="psc-lsd-tree-item-icon"
                :type="getNodeInfo(item).icon"
                :style="{ color: 'rgba(0,0,0,0.65)' }"
              />
              <span
                class="psc-lsd-tree-item-title"
                v-tooltip="item.has_privilege === false && '该数据无查询权限，可右键申请'"
                v-if="searchValue && getSearchMatch(item.title, searchValue)"
              >
                {{ item.title.substr(0, getSearchMatch(item.title, searchValue).index) }}
                <span
                  style="color: #f50 !important;"
                >{{ getSearchMatch(item.title, searchValue)[0] }}</span>
                {{ item.title.substr(getSearchMatch(item.title, searchValue).index + searchValue.length) }}
              </span>
              <span
                class="psc-lsd-tree-item-title"
                v-tooltip="item.has_privilege === false && '该数据无查询权限，可右键申请'"
                v-else
              >{{ item.title }}</span>
            </template>
            <template v-else>
              <span class="psc-lsd-tree-item-title">{{ item.title }}</span>
            </template>
            <!-- 字段类型 -->
            <span
              class="psc-lsd-tree-item-column-type"
              v-if="item.element_type=='column'"
            >{{item.type}}</span>
            <!-- 锁定 -->
            <custom-icon
              style="color: #F49923;font-size: 14px;"
              type="lu-icon-locknew"
              v-if="item.permission_lock == 1"
            />
            <!-- 未锁定 -->
            <custom-icon
              style="font-size: 14px;"
              type="lu-icon-unlocknew"
              v-if="item.permission_lock == 0"
            />
            <!-- 敏感 -->
            <custom-icon
              style="color: #5E1BE4;font-size: 14px;"
              type="lu-icon-data-sensitive"
              v-tooltip="'敏感数据'"
              v-if="item.is_sensitive"
            />
            <!-- 无查看权限 -->
            <!-- <custom-icon
              style="color: rgba(0,0,0,.35);font-size: 13px;"
              type="stop"
              v-tooltip="'该数据无查询权限，可右键申请'"
              v-if="item.has_privilege === false"
            />-->
          </div>
          <!-- <a-menu slot="overlay">
            <template v-for="(menuGroup, index) in (getNodeInfo(item).menus || [])">
              <a-menu-divider
                :key="index"
                v-if="index > 0 && getMenuItems(menuGroup, item).length > 0"
              />
              <template v-for="menuItem in getMenuItems(menuGroup, item)">
                <a-menu-item
                  :key="menuItem.key"
                  @click="onContextMenuClick(item, menuItem)"
                  :disabled="menuItem.disabled"
                >{{menuItem.title}}</a-menu-item>
              </template>
            </template>
          </a-menu>-->
        </div>
      </VirtualTree>
    </a-spin>
    <DefaultSqlModal ref="DefaultSqlModal" @save="onDefaultSqlSave" />
    <!-- ludms元数据授权弹窗 -->
    <template v-if="project === 'ludms'">
      <component :is="'MetadataAuthModal'" ref="MetadataAuthModal" @save="onMetadataAuthSave"></component>
    </template>
  </div>
</template>

<script>
import {
  getDbData,
  createSql,
  addColumnPrivilege,
  dropTable,
  truncateTable,
  getCreateTableSql,
  tableScripts,
  deleteDbElements
} from '@/api/sqlresolver';
import VirtualTree from '@/components/VirtualTree';
import DefaultSqlModal from '@/pages/sqlresolver/modals/defaultSql';
import Drag from './drag';
// import MetadataAuthModal from '@/pages/business/metadataAuthApplication/auth';

// schema/table/column/view/procedure/function/trigger/sequence
const nodeInfo = {
  // schema
  schema: {
    icon: 'lu-icon-schema',
    menus: [
      { label: '新建查询', key: 'addQuery' },
      { label: '新建表', key: 'addTable' },
      { label: '刷新', key: 'refresh' }
      // [{ label: '元数据权限申请', key: 'apply', disabled: true }]
    ]
  },
  // 表
  table_group: {
    icon: 'lu-icon-list',
    next: 'table',
    menus: [
      { label: '新建查询', key: 'addQuery' },
      { label: '新建表', key: 'addTable' },
      { label: '刷新', key: 'refresh' }
      // [{ label: '元数据权限申请', key: 'apply' }]
    ]
  },
  table: {
    icon: 'lu-icon-list',
    next: 'column',
    menus: [
      { label: '打开表', key: 'openTable' },
      {
        label: '删除表',
        key: 'dropTable',
        _authKey: 'database_contextmenu_table_drop'
      },
      {
        label: '清空表',
        key: 'truncateTable',
        _authKey: 'database_contextmenu_table_truncate'
      },
      {
        label: '复制表',
        key: 'copyTable',
        children: [{ label: '仅结构', key: 'copyTableStruct' }]
      },
      { label: '选择前200行', key: 'getFirstRows' },
      {
        label: '编写表脚本',
        key: 'tableScripts',
        children: [
          { label: 'SELECT', key: 'tableScript' },
          { label: 'INSERT', key: 'tableScript' },
          { label: 'UPDATE', key: 'tableScript' },
          { label: 'DELETE', key: 'tableScript' }
        ]
      },
      { label: '刷新', key: 'refresh', divided: true },
      {
        label: '元数据权限申请',
        key: 'apply',
        visible: item => item.has_privilege === false
      }
    ]
  },
  // 字段
  column: {
    icon: 'lu-icon-field',
    menus: [
      {
        label: '元数据权限申请',
        key: 'apply',
        visible: item => item.has_privilege === false
      }
    ]
  },
  // 视图
  view_group: {
    icon: 'lu-icon-view',
    next: 'view',
    menus: [{ label: '新建视图', key: 'addQuery', useDefault: true }]
  },
  view: {
    icon: 'lu-icon-view',
    menus: [{ label: '删除视图', type: 'view', key: 'dropElement' }]
  },
  // 存储过程
  procedure_group: {
    icon: 'lu-icon-editor',
    next: 'procedure',
    menus: [{ label: '新建存储过程', key: 'addQuery', useDefault: true }]
  },
  procedure: {
    icon: 'lu-icon-editor',
    menus: [{ label: '删除存储过程', type: 'procedure', key: 'dropElement' }]
  },
  // 函数
  function_group: {
    icon: 'lu-icon-function',
    next: 'function',
    menus: [{ label: '新建函数', key: 'addQuery', useDefault: true }]
  },
  function: {
    icon: 'lu-icon-function',
    menus: [{ label: '删除函数', type: 'function', key: 'dropElement' }]
  },
  // 触发器
  trigger_group: {
    icon: 'lu-icon-trigger',
    next: 'trigger',
    menus: [{ label: '新建触发器', key: 'addQuery', useDefault: true }]
  },
  trigger: {
    icon: 'lu-icon-trigger'
  },
  // 序列
  sequence_group: {
    icon: 'lu-icon-sequence',
    next: 'sequence',
    menus: [{ label: '新建序列', key: 'addQuery', useDefault: true }]
  },
  sequence: {
    icon: 'lu-icon-sequence'
  }
};

export default {
  components: { VirtualTree, DefaultSqlModal },
  inject: ['instanceItem', 'authInfo'],
  data() {
    return {
      project: GLOBAL_CONFIG.ProjectCode,
      nodeInfo,
      treeParams: {
        // checkable: true,
        expandIcon: 'right',
        option: {
          // visibleHeight: 400, // 可视区域的高度
          itemHeight: 32 // 单个item的高度
          // indentOffset: 24
        },
        // autoHeight: true,
        onLoad: node => this.loadNext(node)
      },
      searchValue: '',
      treeLoading: false
    };
  },
  computed: {
    auth() {
      return this.authInfo() || {};
    }
  },
  created() {
    this.refresh();
  },
  beforeDestroy() {
    this.dragInstance && this.dragInstance.destroy();
  },
  mounted() {
    this.$bus.$on('sqlresolver-instance-change', instanceId => {
      if (instanceId == _.get(this.instanceItem, 'key')) {
        // 唤醒当前instance tab
        this.$nextTick(() => {
          this.updateTree();
        });
      }
    });
    this.dragInstance = new Drag({
      onStart: () => {
        document.body.classList.add('no-select');
      },
      onMove: (e, data) => {
        console.log('mv!!');
        this.$bus.$emit('sqlresolver-drag-node-move', {
          event: e,
          data: data,
          instanceId: _.get(this.instanceItem, 'key')
        });
        document.body.classList.add('cursor-copy');
      },
      onEnd: (e, data) => {
        this.$bus.$emit('sqlresolver-drag-node-end', {
          event: e,
          data: data,
          instanceId: _.get(this.instanceItem, 'key')
        });
        document.body.classList.remove('no-select');
        document.body.classList.remove('cursor-copy');
      }
    });
  },
  methods: {
    getDbData(params = {}) {
      return ReqUtil.req({
        ctx: this,
        reqInstance: getDbData,
        params,
        needLoading: false,
        cbk: data => {}
      }).then(res => res);
    },
    refresh() {
      this.treeLoading = true;
      this.getDbData({
        instance_id: _.get(this.instanceItem, 'value'),
        element_type: 'schema'
      })
        .then(res => {
          this.treeLoading = false;
          let _data = (res || []).map(item => {
            return {
              ...item,
              key: item.uid,
              title: item.name
            };
          });
          this.$refs.tree.refresh(_data);

          // 自动加载第二层
          // _data.forEach((item, index) => {
          //   this.$refs.tree.toggleExpand(null, item, index);
          // });

          this.$bus.$emit('sqlresolver-database-loaded', {
            data: _data,
            instanceId: _.get(this.instanceItem, 'key')
          });
        })
        .catch(e => {
          this.treeLoading = false;
        });
    },
    loadNext(node) {
      return new Promise((resolve, reject) => {
        if (node.element_type === 'schema') {
          const data = (this.project === 'bettle'
            ? [
                {
                  title: '表',
                  element_type: 'table'
                }
              ]
            : [
                {
                  title: '表',
                  element_type: 'table'
                },
                {
                  title: '视图',
                  element_type: 'view'
                },
                {
                  title: '存储过程',
                  element_type: 'procedure'
                },
                {
                  title: '函数',
                  element_type: 'function'
                },
                {
                  title: '触发器',
                  element_type: 'trigger'
                },
                {
                  title: '序列',
                  element_type: 'sequence',
                  visible: node.db_type == 'MYSQL'
                }
              ]
          )
            .filter(item => item.visible != true)
            .map(item => {
              return {
                ...item,
                element_type: item.element_type + '_group',
                key: node.name + '_' + item.element_type + '_group',
                schema_name: node.name,
                schema_id: node.id,
                schema_key: node.key,
                selectable: false,
                _virtual: true,
                disabled:
                  item.element_type != 'table' &&
                  ['MYSQL', 'POSTGRE'].includes(node.db_type)
              };
            });
          resolve(data);
        } else {
          const elementType = (node.element_type + '').split('_')[0];
          const nextType = (this.getNodeInfo(node) || {}).next || elementType;
          this.getDbData({
            instance_id: _.get(this.instanceItem, 'value'),
            element_type: nextType,
            schema_name: node.schema_name,
            // schema_id: node.schema_id,
            table_id: node.table_id,
            table_name: node.table_name
          })
            .then(res => {
              // const sourceData = res || [];
              // if (sourceData.length <= 0) {
              //   node.is_leaf = true;
              // }
              let _data = (res || []).map(item => {
                let childNode = {
                  ...item,
                  key: item.uid,
                  title: item.name,
                  schema_name: node.schema_name,
                  schema_id: node.schema_id,
                  schema_key: node.schema_key,
                  table_name: node.table_name,
                  table_id: node.table_id,
                  [`${nextType}_id`]: item.id,
                  [`${nextType}_name`]: item.name,
                  selectable: false
                };
                return childNode;
              });
              resolve(_data);
            })
            .catch(e => {
              reject(new Error(e));
            });
        }
      });
    },
    onSearch(ref, val) {
      let searchValue = ref === 'tree' ? 'searchValue' : 'targetSearchValue';
      this[searchValue] = val;
      let tree = this.$refs[ref];
      let list = tree.getFlattenTree();
      tree.autoExpandParent(
        list.filter(item => {
          return item.title && this.getSearchMatch(item.title, val);
        })
      );
    },
    getSearchMatch(title, searchVal) {
      return (title + '').match(new RegExp(searchVal + '', 'i'));
    },
    getMenuItems(menus, node) {
      const res = (menus || []).filter(itm => {
        if (this.auth[itm._authKey] === false) return false;
        if (itm.key) {
          itm.onClick = () => {
            this.onContextMenuClick(node, itm);
          };
          if (itm.children) {
            itm.children = this.getMenuItems(itm.children, node);
          }
        }
        return itm.visible ? itm.visible(node) : true;
      });
      const last = _.last(res);
      last && (last.divided = false);
      return res;
    },
    getNodeInfo(node) {
      // const elementType = (node.element_type + '').split('_')[0];
      return nodeInfo[node.element_type];
    },
    onVisibleChange(e, item) {
      // console.log(e, item.name, item, 'ssssss');
      this.$set(item, 'className', e ? { contextmenu: true } : {});
    },
    onContextmenu(event, item, menus) {
      // console.log(this.$contextmenu, '666666')
      const items = this.getMenuItems(menus, item);
      if (items.length <= 0) return;

      this.$contextmenu({
        items,
        event,
        customClass: 'custom-class',
        zIndex: 1051
        // minWidth: 180
      });
      return false;
    },
    async onContextMenuClick(item, menuItem) {
      const { tree, DefaultSqlModal } = this.$refs;
      console.log(item, menuItem, 6666);
      // 隐藏右键弹窗
      this.onVisibleChange(false, item);

      switch (menuItem.key) {
        case 'addQuery':
          // 选中对应database
          if (menuItem.useDefault) {
            DefaultSqlModal.show({
              title: menuItem.title,
              item
            });
            return;
          }
          // this.addQuery(item, `SELECT * FROM ${item.schema_name}`);
          this.addQuery(item);
          break;
        case 'refresh':
          this.$set(item, 'children', []);
          this.$set(item, 'loading', true);
          // this.$set(item, 'expand', false);
          const children = await this.loadNext(item);
          this.$set(item, 'loading', false);
          this.$set(item, 'children', children);
          // this.$set(item, 'expand', true);
          tree.expand(item);
          tree.updateView();
          break;
        case 'openTable':
          this.openTable(item);
          break;
        case 'addTable':
          this.addTable(item);
          break;
        case 'dropTable':
          this.dropTable(item);
          break;
        case 'truncateTable':
          this.truncateTable(item);
          break;
        case 'copyTableStruct':
          this.copyTableStruct(item);
          break;
        case 'getFirstRows':
          this.getFirstRows(item);
          break;
        case 'tableScript':
          this.tableScript(item, menuItem);
          break;
        case 'dropElement':
          this.dropElement(item, menuItem);
          break;
        case 'apply':
          this.apply(item);
          break;
        default:
          break;
      }
    },
    onMousedown(e, item) {
      if (this.dragInstance) {
        if (item && !item._virtual) {
          this.dragInstance.setData(item);
        }
      }
    },
    addQuery(item, defaultSql, sheetName) {
      const database = this.getDatabase(item);
      this.$refs.tree.nodeClick(database);
      this.$bus.$emit('sqlresolver-add-query', {
        database,
        item,
        defaultSql,
        sheetName,
        instanceId: _.get(this.instanceItem, 'key')
      });
    },
    addTable(item) {
      this.addQuery(
        item,
        'CREATE TABLE tablename();',
        `<span style="color:red;margin-right:2px;">*</span>
              <span>新建表</span>`
      );
    },
    dropTable(item) {
      this.$confirm({
        title: '确认删除表',
        content: `表名：${item.name}`,
        onOk: () => {
          // 请求
          // this.$showLoading();
          const params = {
            instance_id: _.get(this.instanceItem, 'value'),
            schema_name: item.schema_name,
            table_name: item.name
          };
          return dropTable(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({ useMessage: true, tips: '删除成功' });
                const { tree } = this.$refs;
                tree.remove(item);
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message'),
                  response: res
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    truncateTable(item) {
      this.$confirm({
        title: '确认清空表',
        content: `表名：${item.name}`,
        onOk: () => {
          // 请求
          // this.$showLoading();
          const params = {
            instance_id: _.get(this.instanceItem, 'value'),
            schema_name: item.schema_name,
            table_name: item.name
          };
          return truncateTable(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({ useMessage: true, tips: '清空成功' });
                // const { tree } = this.$refs;
                // tree.remove(item);
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message'),
                  response: res
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    copyTableStruct(item) {
      // 请求
      this.$showLoading({ useProgress: true });
      const params = {
        instance_id: _.get(this.instanceItem, 'value'),
        schema_name: item.schema_name,
        table_name: item.name
      };
      return getCreateTableSql(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ useProgress: true, tips: '复制成功' });
            this.addQuery(
              item,
              _.get(res, 'data.data') || '',
              `<span style="color:red;margin-right:2px;">*</span>
              <span>复制表</span>`
            );
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getFirstRows(item) {
      const database = this.getDatabase(item);
      this.$refs.tree.nodeClick(database);
      this.$bus.$emit('sqlresolver-view-table-first-rows', {
        database,
        item,
        sheetName: () => {
          return (
            <span>
              <custom-icon type="lu-icon-list" />
              {item.title}@{database.title}
            </span>
          );
        },
        instanceId: _.get(this.instanceItem, 'key')
      });
    },
    tableScript(item, menuItem) {
      // 请求
      this.$showLoading({ useProgress: true });
      const params = {
        instance_id: _.get(this.instanceItem, 'value'),
        schema_name: item.schema_name,
        table_name: item.name,
        type: menuItem.label
      };
      return tableScripts(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ useProgress: true, tips: '生成成功' });
            this.addQuery(
              item,
              _.get(res, 'data.data') || '',
              `<span style="color:red;margin-right:2px;">*</span>
              <span>表脚本【${menuItem.label}】</span>`
            );
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    openTable(item) {
      const database = this.getDatabase(item);
      this.$refs.tree.nodeClick(database);
      this.$bus.$emit('sqlresolver-view-node', {
        database,
        item,
        sheetName: () => {
          return (
            <span>
              <custom-icon type="lu-icon-list" />
              {item.title}@{database.title}
            </span>
          );
        },
        instanceId: _.get(this.instanceItem, 'key')
      });
    },
    dropElement(item, menuItem) {
      this.$confirm({
        title: `确认${menuItem.label}`,
        content: `名称：${item.name}`,
        onOk: () => {
          // 请求
          // this.$showLoading();
          const params = {
            instance_id: _.get(this.instanceItem, 'value'),
            schema_name: item.schema_name,
            name: item.name,
            type: menuItem.type.toUpperCase()
          };
          return deleteDbElements(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({ useMessage: true, tips: '删除成功' });
                const { tree } = this.$refs;
                tree.remove(item);
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message'),
                  response: res
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    apply(item) {
      this.$refs.MetadataAuthModal.show({
        from: 'sql',
        info: {
          node: item,
          instance_id: _.get(this.instanceItem, 'value')
        }
      });
    },
    onDefaultSqlSave(data) {
      const { formData = {}, item = {} } = data;
      const elementType = (item.element_type + '').split('_')[0] || '';
      // 请求
      this.$showLoading();
      const params = {
        ...formData,
        instance_id: _.get(this.instanceItem, 'value'),
        schema_name: item.schema_name,
        sql_type: elementType.toUpperCase()
      };
      return createSql(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: '新建成功' });
            this.addQuery(
              item,
              _.get(res, 'data.data') || '',
              `<span style="color:red;margin-right:2px;">*</span>
              <span>新建${item.title}</span>`
            );
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onMetadataAuthSave(data = {}, node) {
      // 请求
      this.$showLoading();
      const params = {
        ...data,
        instance_id: _.get(this.instanceItem, 'value'),
        schema_name: node.schema_name,
        schema_id: node.schema_id
      };
      return addColumnPrivilege(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ useMessage: true, tips: '授权成功' });
            this.$refs.MetadataAuthModal.hide();
            const { table_columns: tableColumns } = data;
            const treeData = this.$refs.tree.getFlattenTree();
            const affects = [];
            tableColumns.forEach(tb => {
              const findTb = treeData.find(
                item => item.element_type == 'table' && item.id == tb.table_id
              );
              affects.push(findTb);
              (tb.columns || []).forEach(col => {
                const findCol = treeData.find(
                  item => item.element_type == 'column' && item.id == col
                );
                affects.push(findCol);
              });
            });
            affects
              .filter(item => item)
              .forEach(item => {
                item.has_privilege = true;
              });
            // this.$set(node, 'has_privilege', true);
            this.$refs.tree.updateView();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onUpdateTree({ visibleData = [] }) {
      const tree = this.$refs.tree;
      const allData = tree.getFlattenTree();
      // console.log('update tree');

      allData.forEach(item => {
        if (!visibleData.find(vItem => vItem.key == item.key)) {
          this.$set(item, 'className', {});
        }
      });
    },
    onNodeClick(node) {
      this.$bus.$emit('sqlresolver-database-change', {
        node,
        instanceId: _.get(this.instanceItem, 'key')
      });
    },
    getNode(key) {
      const { tree } = this.$refs;
      return tree.getFlattenTree().find(item => item.key == key);
    },
    getDatabase(node) {
      return node.element_type === 'schema'
        ? node
        : this.getNode(node.schema_key);
    },
    updateTree() {
      const { tree } = this.$refs;
      tree && tree.updateView();
    }
  }
};
</script>

<style lang="less" scoped>
.psc-left-source-database {
  height: 100%;

  /deep/ .psc-lsd-tree-wrapper {
    height: 100%;

    .ant-spin-container {
      height: 100%;

      .virtual-tree-wrapper {
        border: none;

        .ant-input-search input {
          border: 1px solid @border-color;
          border-radius: 4px;
        }

        .virtual-tree {
          padding-right: 3px;
          // &::-webkit-scrollbar-thumb {
          //   background: #ffffff;
          // }
          // &::-webkit-scrollbar-track {
          //   background: #ffffff;
          // }

          // &:hover {
          //   &::-webkit-scrollbar-thumb {
          //     background: #d2d2d2;
          //   }
          //   &::-webkit-scrollbar-track {
          //     // background: #f2f2f2;
          //   }
          // }

          .vt-item {
            &.contextmenu {
              background-color: @primary-1;
            }
            &.item-disabled {
              opacity: 0.6;
            }
          }
          .virtual-tree-icon {
            > .anticon {
              color: rgba(0, 0, 0, 0.65);
            }
          }

          .tree-item-tag {
            padding: 0 2px;
            font-size: 12px;
            border-radius: 2px;
            transform: scale(0.7);
            display: inline-block;
            // height: 20px;
            line-height: 20px;

            &.red {
              border: 1px solid #f5222d;
              /* color: red; */
              color: #f5222d;
            }
          }

          .psc-lsd-tree-item {
            .psc-lsd-tree-item-title {
              font-size: 13px;
            }
            &.no_privilege {
              * {
                color: rgba(0, 0, 0, 0.35) !important;
              }
            }
          }

          .psc-lsd-tree-item-column-type {
            background: #f5f5f5;
            padding: 2px 8px;
            font-size: 12px;
            border-radius: 8px;
          }
        }
      }
    }
  }
}
</style>
