<template>
  <a-modal
    title="corn表达式"
    width="450px"
    :visible="visible"
    @ok="handleSubmit"
    @cancel="close"
    cancelText="关闭"
  >
    <!-- <Form ref="form" v-bind="params" :formData="formData"></Form> -->
    <div class="form">
      <div>
        <a-select v-model="type" placeholder="请选择" style="width:100px;" @change="typeChange">
          <a-select-option value="每天">每天</a-select-option>
          <a-select-option value="每周">每周</a-select-option>
          <a-select-option value="每月">每月</a-select-option>
        </a-select>
      </div>
      <div>
        <a-select
          v-if="type == '每周'"
          v-model="week"
          value-key="cron"
          placeholder="请选择"
          style="width:100px;"
        >
          <a-select-option
            v-for="item in weekOption"
            :key="item.cron"
            :value="item.cron"
          >{{item.title}}</a-select-option>
        </a-select>
      </div>
      <div>
        <a-select
          v-if="type == '每月'"
          v-model="month"
          value-key="cron"
          placeholder="请选择"
          style="width:100px;"
        >
          <a-select-option
            v-for="(item) in monthOption"
            :key="item.cron"
            :value="item.cron"
          >{{item.title}}</a-select-option>
        </a-select>
      </div>
      <div>
        <a-time-picker
          @change="onChange"
          placeholder="请选择"
          v-model="time"
          value-format="H:m:s"
          style="width: 100%;"
        ></a-time-picker>
      </div>
    </div>
  </a-modal>
</template>
<script>
import Form from '@/components/Form';
import SearchArea from '@/components/SearchArea';
import config from './config';
import moment from 'moment';
export default {
  components: { Form, SearchArea },
  name: 'VueCron',
  props: {
    data: {
      type: String,
      default: ''
    }
  },
  data() {
    this.config = config(this);
    return {
      params: {
        fields: this.config.cornInfo,
        multiCols: 3
      },
      formData: {},
      visible: false,
      value: '',
      type: '每天', // 天\周\月
      week: null, // 星期几
      month: null, // 几号
      time: '', // 时间
      weekOption: [
        {
          title: '星期一',
          value: '星期一',
          cron: 2
        },
        {
          title: '星期二',
          value: '星期二',
          cron: 3
        },
        {
          title: '星期三',
          value: '星期三',
          cron: 4
        },
        {
          title: '星期四',
          value: '星期四',
          cron: 5
        },
        {
          title: '星期五',
          value: '星期五',
          cron: 6
        },
        {
          title: '星期六',
          value: '星期六',
          cron: 7
        },
        {
          title: '星期日',
          value: '星期日',
          cron: 1
        }
      ],
      monthOption: []
    };
  },
  created() {
    this.initData();
  },
  mounted() {},
  methods: {
    moment,
    show() {
      this.visible = true;
    },
    initData() {
      let arr = [];
      for (let i = 1; i < 32; i++) {
        arr.push({
          title: i + '号',
          value: i + '号',
          cron: i
        });
      }
      this.monthOption = arr;
      if (this.timeCronStr) {
        this.value = this.timeCronStr;
        let valueArr = this.timeCronStr.split(',') || [];
        let valueArrLen = valueArr.length;
        this.type = valueArr[0];
        this.time = valueArr[valueArrLen - 1];
        if (valueArrLen > 2) {
          // 说明是每月 或 每周
          if (valueArr[1].indexOf('星期') > -1) {
            // 每周
            this.weekOption.map(v => {
              console.log(v);
              if (v.title == valueArr[1]) {
                this.week = v;
              }
            });
          } else {
            this.monthOption.map(v => {
              if (v.title == valueArr[1]) {
                this.month = v;
              }
            });
          }
        }
      }
    },
    typeChange(t) {
      if (t == '每周' && !this.week) {
        this.week = this.weekOption[0].value;
      }
      if (t == '每月' && !this.month) {
        this.month = this.monthOption[0].value;
      }
    },
    handleSubmit() {
      if (!this.time) {
        this.$message.error('请选择时间!');
        return;
      }
      let dataArr = [];
      let timeCron;
      let clockCornArr = this.time.split(':').reverse();
      if (this.type == '每天') {
        dataArr.push(this.type, this.time);
        timeCron = clockCornArr.join(' ') + ' * * ?';
      } else {
        if (this.type == '每月') {
          dataArr.push(this.type, this.month, this.time);
          timeCron = clockCornArr.join(' ') + ' ' + this.month + ' * ?';
        } else {
          // 每周
          console.log(this.week);
          dataArr.push(this.type, this.week, this.time);
          timeCron = clockCornArr.join(' ') + ' ? * ' + this.week;
        }
      }
      this.value = dataArr.join(',');
      this.visible = false;
      this.$emit('ok', timeCron); // 每月,1号,14:52:36 和 36 52 14 1 * ?
      this.close();
    },
    close() {
      this.visible = false;
      this.type = '每天';
      this.time = '';
      this.week = null;
      this.month = null;
    },
    onChange(time) {
      if (this.data) {
        this.time = time;
      }
    }
  },
  watch: {
    visible: {
      handler() {
        let labelArr = this.data.split(' ');
        if (labelArr) {
          let time =
            `${labelArr[2]}` + ':' + `${labelArr[1]}` + ':' + `${labelArr[0]}`;
          this.onChange(time);
          if (labelArr[3] === '*' && labelArr[4] === '*') {
            console.log('每天');
            this.type = '每天';
          }
          if (labelArr[3] === '?' && labelArr[4] === '*') {
            this.type = '每周';
            this.weekOption.find(val => {
              if (val.cron === Number(labelArr[5])) {
                this.week = val.title;
              }
            });
          }
          if (labelArr[4] === '*' && labelArr[5] === '?') {
            this.type = '每月';
            this.monthOption.find(val => {
              if (val.cron === Number(labelArr[3])) {
                this.month = val.title;
              }
            });
          }
          // this.secondsReverseExp(label);
          // this.minutesReverseExp(label);
          // this.hoursReverseExp(label);
          // this.daysReverseExp(label);
          // this.daysReverseExp(label);
          // this.monthsReverseExp(label);
          // this.yearReverseExp(label);
          // JSON.parse(JSON.stringify(label));
        }
        // else {
        //   // this.result = JSON.parse(JSON.stringify(this.defaultValue));
        // }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.form {
  display: flex;
  justify-content: space-between;
}
.footer {
  text-align: right;
  margin-top: 10px;
}
</style>