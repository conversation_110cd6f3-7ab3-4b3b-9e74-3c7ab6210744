<template>
  <div class="rgi-form-item">
    <Form
      v-bind="params"
      :class="['rgi-form-item-idx-' + index]"
      :fields="realFields"
      :formData="data"
      ref="form"
    ></Form>
    <!-- 操作 -->
    <div class="rgi-form-item-operate">
      <a-popconfirm title="确定删除该行?" @confirm="remove">
        <a-icon type="minus-circle" style="color:red;" class="rg-remove"></a-icon>
      </a-popconfirm>
    </div>
  </div>
</template>

<script>
import Form from '@/components/Form';
// import common from '@/utils/common';
// import _ from 'lodash';

export default {
  inheritAttrs: false,
  components: { Form },
  props: {
    value: {
      type: Object,
      default: () => {}
    },
    fields: {
      type: Array,
      default: () => []
    },
    rowKey: {
      type: String,
      default: 'id'
    },
    index: Number,
    setInstance: Function
  },
  data() {
    const { value, fields = [] } = this;
    return {
      data: value,
      params: {
        multiCols: fields.length,
        layout: 'horizontal'
      }
    };
  },
  computed: {
    realFields() {
      let arr = [
        // {
        //   type: 'Select',
        //   key: 'relation',
        //   props: {
        //     options: [
        //       { label: 'or', value: 'or' },
        //       { label: 'and', value: 'and' }
        //     ],
        //     style: {
        //       width: '80px'
        //     },
        //     allowClear: false,
        //     placeholder: '关系'
        //   },
        //   rules: [{ required: true, message: '该项为必填项' }]
        // },
        ...(this.fields || [])
      ];

      return arr;
    }
  },
  created() {},
  mounted() {},
  methods: {
    remove() {
      this.$emit('remove', this.data);
    },
    getData() {
      return this.$refs.form.getData();
    },
    validate() {
      return this.$refs.form.validate();
    }
  },
  watch: {
    value(newVal) {
      this.data = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
.rgi-form-item {
  display: flex;
  align-items: center;

  > .ant-form {
    flex-grow: 1;

    //   /deep/ .ant-row > .ant-col:first-child:not(.ant-form-item-control-wrapper) {
    //     position: absolute;
    //     left: -100px;
    //     top: -26px;
    //     width: 90px;

    //     &::before {
    //       content: '';
    //       position: absolute;
    //       width: 32px;
    //       left: 78px;
    //       top: 4px;
    //       height: 2px;
    //       background: #e8e8e8;
    //       transform: rotate(-20deg);
    //       transform-origin: left;
    //     }
    //     &::after {
    //       content: '';
    //       position: absolute;
    //       width: 32px;
    //       left: 78px;
    //       top: 34px;
    //       height: 2px;
    //       background: #e8e8e8;
    //       transform: rotate(20deg);
    //       transform-origin: left;
    //     }
    //   }

    //   &.rgi-form-item-idx-0 {
    //     /deep/
    //       .ant-row
    //       > .ant-col:first-child:not(.ant-form-item-control-wrapper) {
    //       display: none !important;
    //     }
    //   }
  }

  > .rgi-form-item-operate {
    // padding: 0 8px;
    // height: 40px;
    display: none;
    cursor: pointer;
    position: absolute;
    right: -4px;
    top: -4px;
    background: #ffffff
  }

  &:hover {
    > .rgi-form-item-operate {
      display: block;
    }
  }
}
</style>
