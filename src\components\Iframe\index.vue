<template>
  <div class="custom-iframe">
    <iframe
      :src="url"
      width="100%"
      height="100%"
      @load="onLoad"
      v-show="loaded"
    />
    <a-skeleton :paragraph="{ rows: 8 }" active v-show="loading && !loaded">
    </a-skeleton>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    url: String
  },
  data() {
    return {
      loading: false,
      loaded: false
    };
  },
  created() {
    this.showLoading();
  },
  mounted() {},
  beforeDestroy() {},
  activated() {
    this.showLoading();
  },
  deactivated() {
    this.loaded = false;
  },
  methods: {
    showLoading() {
      if (!this.loaded) {
        this.timeout = setTimeout(() => {
          this.loading = true;
        }, 200);
      }
    },
    onLoad(e) {
      if (this.timeout) {
        clearTimeout(this.timeout);
        this.timeout = null;
      }
      this.loaded = true;
      this.loading = false;
    }
  }
};
</script>

<style lang="less" scoped>
.custom-iframe {
  position: absolute;
  inset: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 12px;
  background: #f4f4f4;
  iframe {
    border: none;
    vertical-align: top;
  }
}
</style>
