<template>
  <!-- 退出 -->
  <div class="sys-logout" @click="onLogout">
    <a-icon type="logout" />
    <span>退出</span>
  </div>
</template>

<script>
import { getLogout } from '@/api/login';
import _ from 'lodash';
// import common from '@/utils/common';

export default {
  components: {},
  props: {},
  data() {
    return {};
  },
  mounted() {},
  created() {},
  methods: {
    onLogout() {
      getLogout().then(res => {
        if (_.get(res, 'data.code') == 0) {
          let data = _.get(res, 'data.data');
          // 跳转回登录
          window.Login.go(data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
div.sys-logout {
  border-left: 1px solid #f5f5f5;
  padding: 0 24px;
}
</style>
