<template>
  <svg
    class="biz-bracket"
    width="30"
    height="54"
    viewBox="0 0 30 54"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_1524_1330)">
      <mask
        id="mask0_1524_1330"
        style="mask-type:luminance"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="30"
        height="54"
      >
        <path d="M30 0H0V54H30V0Z" fill="white" />
      </mask>
      <g mask="url(#mask0_1524_1330)">
        <path
          d="M17 1.5H30V0.5H17C13.9624 0.5 11.5 2.96243 11.5 6V26H0V27H11.5V48C11.5 51.0376 13.9624 53.5 17 53.5H30V52.5H17C14.5147 52.5 12.5 50.4853 12.5 48V6C12.5 3.51472 14.5147 1.5 17 1.5Z"
          :fill="color"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_1524_1330">
        <rect width="30" height="54" fill="white" />
      </clipPath>
    </defs>
  </svg>
</template>

<script>
export default {
  components: {},
  props: {
    color: {
      type: String,
      default: '#000'
    }
  },
  data() {
    return {};
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {}
};
</script>

<style scoped lang="less"></style>
