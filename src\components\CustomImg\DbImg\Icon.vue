<template>
  <!-- 合并 -->
  <div :class="['db-img-merge', 'mode-' + type]" v-if="MergeIcon">
    <CustomImg :imgStyle="imgStyle" :src="imgSrc" v-if="type === 'image'" />
    <custom-icon
      class="iconStyle"
      :type="iconType"
      mode="svg"
      v-else-if="type === 'icon'"
    />
    <custom-icon class="merge-icon" :type="`lu-icon-amazon`" mode="svg" />
  </div>
  <!-- 纯image -->
  <CustomImg :imgStyle="imgStyle" :src="imgSrc" v-else-if="type === 'image'" />
  <!-- 纯icon -->
  <custom-icon
    class="iconStyle"
    :type="iconType"
    mode="svg"
    v-else-if="type === 'icon'"
  />
</template>

<script>
import CustomImg from '../index.vue';
export default {
  components: { CustomImg },
  props: {
    MergeIcon: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'icon' // icon, image
    },
    imgSrc: String,
    imgStyle: Object,
    iconType: String
  },
  data() {
    return {};
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>
<style lang="less" scoped>
.db-img-merge {
  display: inline-flex;
  flex-direction: column;
  align-items: center;

  .merge-icon {
    font-size: 20px;
  }
  &.mode-image {
    .merge-icon {
      font-size: 28px;
      transform: translateY(-4px);
    }
  }
}
</style>
