<template>
  <div class="psc-right-query-detail-result">
    <div class="sql-info">
      <div class="sql-test">
        <LimitLabel mode="ellipsis" :label="info.sql_text"></LimitLabel>
        <span
          style="margin-left: 8px;"
          v-if="info.affect_rows != null"
        >【共{{info.affect_rows || 0}}条】</span>
      </div>
      <div
        class="sql-limit-tips"
        v-if="info.affect_rows != null && info.affect_rows > 100"
      >*最多查询100行数据，更多查询数据正在努力开放中</div>
    </div>
    <Table
      class="table-new-mode-style"
      v-bind="tableParams"
      :scroll="scroll"
      bordered
    />
  </div>
</template>

<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import config from './config';

export default {
  components: { Table, LimitLabel },
  props: {
    info: {
      type: Object,
      default: () => ({})
    },
    resize: Number
  },
  data() {
    this.config = config(this);
    return {
      tableParams: this.getTableParams(),
      scroll: {
        x: 'max-content'
      }
    };
  },
  computed: {},
  mounted() {
    this.setScroll();
  },
  methods: {
    getTableParams(info) {
      const res = {
        size: 'small',
        columns: [],
        dataSource: [],
        pagination: {
          size: 'small',
          hideOnSinglePage: true,
          showTotal: (total, range) => {
            return ``;
          },
          pageSize: 20,
          // showSizeChanger: false,
          showQuickJumper: false
        }
      };
      const _info = info || this.info;
      const { column_names: columnNames = [], data = [] } = _info;
      // 赋值columns
      res.columns = [
        {
          title: '',
          dataIndex: '_index',
          key: '_index',
          // width: 60,
          customRender: (text, record, index) => {
            return index + 1;
          }
        },
        ...columnNames.map((columnName, index) => {
          return {
            title: columnName,
            key: columnName,
            dataIndex: columnName
            // customRender: (text, record, index) => {
            //   return <div title={text}>{text}</div>;
            // }
            // width: 100
          };
        })
      ];
      // 赋值data
      res.dataSource = data.map(item => {
        let dataItem = {};
        columnNames.map((columnName, index) => {
          dataItem[columnName] = item[index];
        });
        return dataItem;
      });
      return res;
    },
    setScroll() {
      const y = this.$el.offsetHeight - 90;
      this.scroll = {
        x: 'max-content'
        // y: y < 40 ? 40 : y
      };
      const tableBody = this.$el.querySelector('.ant-table-body');
      tableBody && (tableBody.style.height = y + 'px');
    }
  },
  watch: {
    info(newVal = {}) {
      this.tableParams = this.getTableParams(newVal);
    },
    resize() {
      this.setScroll();
    }
  }
};
</script>

<style lang="less" scoped>
.psc-right-query-detail-result {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  padding: 12px 0;
  .sql-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 0 8px;
    .sql-test {
      display: flex;
    }

    .limit-label {
      max-width: 300px;
      display: inline-block;
    }

    .sql-limit-tips {
      font-size: 12px;
      color: @font-color-weak;
      // margin-top: 4px;
      // position: absolute;
      // right: 0;
      // bottom: 12px;
    }
  }

  /deep/ .table-new-mode-style {
    th, td {
      font-size: 12px;
      white-space: nowrap;
    }
    td {
      // max-width: 200px;
      // overflow: hidden;
      // text-overflow: ellipsis;
    }
    th {
      position: sticky;
      top: 0;
      background-color: #f5f5f5 !important;
    }
    .ant-pagination {
      float: left;
      margin: 8px 0 0 0;
    }
  }
}
</style>
