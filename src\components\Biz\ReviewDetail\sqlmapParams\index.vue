<template>
  <a-spin class="spin" :spinning="false">
    <a-card type="small" :bordered="false" class="sqlmap-params" v-if="!isShow">
      <div class="top-box">
        <div class="title">
          <custom-icon type="line-chart" />
          <span>峰值调用频率</span>
        </div>
        <span
          v-if="sqlMapParamsData && sqlMapParamsData.sqlmap_max_frequency_number"
          :style="{color: sqlMapParamsData && optionsColor[sqlMapParamsData.sqlmap_max_frequency]}"
        >{{ sqlMapParamsData.sqlmap_max_frequency_number}}</span>
        <span v-else style="color: #a1a1aa">{{'暂无数据'}}</span>

        <div class="edit" @click="addNote">
          <custom-icon type="edit" />
          <span>编辑</span>
        </div>
      </div>

      <div class="sqlmap-monthly-increase">
        <div class="title">
          <custom-icon type="pie-chart" />
          <h4>每月数量增加</h4>
        </div>
        <Table
          ref="table"
          v-bind="tableParams"
          :dataSource="dataSource"
          class="sqlmap-monthly-increase-table"
        >
          <template slot="frequency" slot-scope="{text}">
            <span
              :style="{color: optionsColor[text]}"
            >{{ !isNaN(+text) ? `【QPS: ${text}】` : options[text] || '--' }}</span>
          </template>
        </Table>
      </div>

      <div class="sqlmap-note">
        <div class="header">
          <div class="title">
            <custom-icon type="lu-icon-table" />
            <h4>备注</h4>
          </div>
          <div class="edit" @click="addNote">
            <custom-icon type="edit" />
            <span>编辑</span>
          </div>
        </div>
        <div class="text">{{ sqlMapParamsData && sqlMapParamsData.sqlmap_note || '暂无备注' }}</div>
      </div>
    </a-card>
    <!-- 点击编辑展示 -->
    <a-card type="small" :bordered="false" class="biz-review-detail-note-edit" v-else>
      <div slot="title" class="title">
        <custom-icon type="lu-icon-ring" />
        <div class="des">请填写SQL备注，说明运行情况，便于DBA评审。</div>
      </div>
      <Form ref="form" v-bind="params" :formData="formData"></Form>
      <div class="btn-box">
        <a-button class="" @click="onCancel">取消</a-button>
        <a-button type="primary" @click="onSave">确定</a-button>
      </div>
    </a-card>
  </a-spin>
</template>

<script>
import config from './config';
import Table from '@/components/Table';
import Form from '@/components/Form';
import { getSqlmapTable } from '@/api/home';
export default {
  components: { Table, Form },
  props: {
    sqlMapParamsData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    sqlMapParamsData: {
      handler(newVal) {
        newVal && (this.dataSource = newVal.sqlmap_monthly_increase || []);
        this.id = newVal && newVal.id;
        this.isShow = false;
        this.formData = {
          sqlmap_monthly_increase: []
        };
      },
      immediate: true
    }
  },
  data() {
    this.config = config(this);
    const img = require('@/assets/img/private/table-empty.svg');
    return {
      tableParams: {
        url: '',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id',
        pagination: false,
        locale: {
          emptyText: <a-empty image={img}></a-empty>
        }
      },
      dataSource: [],
      options: {
        low: '低档（低于1000条/天）',
        middle: '中档（1000-10000条/天）',
        high: '高档（大于10000条/天）'
      },
      optionsColor: {
        1: '#3A974C',
        2: '#F29339',
        3: '#E71D36',
        low: '#3A974C',
        middle: '#F29339',
        high: '#E71D36',
        '低档（低于1000条/天）': '#3A974C',
        '中档（1000-10000条/天）': '#F29339',
        '高档（10-100次/分钟）': '#E71D36'
      },
      formData: {
        sqlmap_monthly_increase: []
      },
      params: {
        fields: [],
        fixedLabel: true,
        layout: 'vertical',
        labelCol: { span: 24 },
        wrapperCol: { span: 24 }
      },
      id: null,
      isShow: false
    };
  },
  computed: {
    canDo() {
      const user = this.$store.state.account.user || {};
      return ['leader', 'admin', 'dba'].includes(user.role);
    }
  },
  mounted() {},
  methods: {
    addNote() {
      this.isShow = true;
      const fields = this.config.fields();
      this.$set(this.params, 'fields', fields);
      this.$showLoading({ useProgress: true });
      getSqlmapTable({ id: this.id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const sqlMapData = _.get(res, 'data.data');
            this.$set(this.formData, 'sqlmap_monthly_increase', sqlMapData);
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onCancel() {
      this.isShow = false;
    },
    onSave() {
      this.isShow = false;
      const data = this.$refs.form.getData() || {};
      data.id = this.id;
      this.$emit('saveNote', data);
    }
  }
};
</script>

<style lang="less" scoped>
.sqlmap-params {
  height: auto !important;
  // margin-top: 6px;
  /deep/ .ant-card-body {
    padding: 0 !important;
  }
  /deep/.ant-card-head {
    border: none;
    padding: 0;
    min-height: auto;
    .ant-card-head-title {
      padding: 0;
    }
  }
  /deep/ .ant-card-extra {
    padding: 0 0 12px 0;
  }
  .top-box {
    padding: 8px 7px;
    background: #eff5ff;
    border-radius: 4px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    position: relative;
    .title {
      display: flex;
      align-items: center;
      margin-right: 20px;
      > .anticon {
        color: #27272a;
        font-size: 16px;
        margin-right: 4px;
      }
      > span {
        font-size: 14px;
        color: #27272a;
        font-weight: 600;
      }
    }
    > span {
      font-family: PingFangSC-Semibold;
      font-size: 14px;
      color: #27272a;
      font-weight: 600;
      margin-right: 36px;
    }
    .edit {
      position: absolute;
      right: 16px;
      .anticon {
        font-size: 13px;
        color: #008adc;
        margin-right: 4px;
      }
      span {
        font-size: 13px;
        color: #008adc;
      }
      &:hover {
        cursor: pointer;
      }
    }
  }

  .sqlmap-monthly-increase {
    margin-bottom: 20px;
    .title {
      display: flex;
      align-items: center;
      padding: 8px 7px;
      background: #f4f5f7;

      > .anticon {
        color: #27272a;
        font-size: 16px;
        margin-right: 4px;
        margin-bottom: 1px;
      }
      > h4 {
        // padding: 8px 7px;
        border-radius: 4px;
        background: #f4f5f7;
        font-family: PingFangSC-Semibold;
        font-size: 14px;
        color: #27272a;
        font-weight: 600;
        margin-bottom: 0;
      }
    }

    // width: 100%;
    /deep/.sqlmap-monthly-increase-table
      .ant-table-wrapper
      .ant-table
      .ant-table-content {
      border-left: 1px solid #e8e8e8;
      border-right: 1px solid #e8e8e8;
      .ant-table-thead {
        tr > th {
          padding: 8px 7px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          font-weight: 400;
        }
      }
      .ant-table-tbody tr > td {
        font-family: PingFangSC-Regular;
        padding: 8px 7px;
        font-size: 14px;
        color: #27272a !important;
        font-weight: 400;
      }
      .ant-table-placeholder {
        border-bottom: 1px solid #e8e8e8 !important;
      }
    }
  }

  .sqlmap-note {
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #f4f5f7;
      padding: 8px 7px;
      .title {
        display: flex;
        align-items: center;
        .anticon {
          color: #27272a;
          font-size: 16px;
          margin-right: 4px;
        }
        h4 {
          border-radius: 4px;
          background: #f4f5f7;
          font-family: PingFangSC-Semibold;
          font-size: 14px;
          color: #27272a;
          font-weight: 600;
          margin-bottom: 0;
        }
      }
      .edit {
        margin-right: 16px;
        .anticon {
          font-size: 13px;
          color: #008adc;
          margin-right: 4px;
        }
        span {
          font-size: 13px;
          color: #008adc;
        }
        &:hover {
          cursor: pointer;
        }
      }
    }
    .text {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #27272a;
      font-weight: 400;
      margin-top: 8px;
      margin-left: 7px;
      word-wrap: break-word;
    }
  }
}

.biz-review-detail-note-edit {
  /deep/.ant-card-head {
    background: #fef5ec;
    border: 1px solid rgba(255, 220, 187, 1);
    margin-bottom: 40px;
    min-height: auto;
    .ant-card-head-title {
      padding: 12px 0;
    }
    .title {
      display: flex;
      align-items: center;
      .anticon {
        font-size: 16px;
        color: #f29339;
        margin-right: 4px;
      }
      .des {
        font-size: 14px;
        color: #f29339;
      }
    }
  }
  /deep/.ant-card-body {
    padding: 0 100px;
    .ant-form {
      .ant-form-item-label {
        min-height: 0 !important;
        line-height: 0 !important;
        > label {
          justify-content: flex-start !important;
          > span {
            color: #27272a;
            font-weight: 600;
            > .custom-icon {
              font-size: 16px;
              color: #27272a;
              margin-right: 2px;
            }
          }
        }
      }
      .ant-form-item:nth-child(2) {
        .ant-form-item-label {
          padding: 10px;
          background: #f4f5f7;
        }
        .ant-form-item-control-wrapper {
          .ant-form-item-children {
            .table-edit {
              .ant-table-content {
                border: 1px solid #e8e8e8;
                border-top: none;
                .ant-table-thead {
                  tr {
                    th {
                      font-size: 14px;
                      color: #a1a1aa !important;
                      font-weight: 400;
                      background: #fff !important;
                    }
                  }
                }
              }
            }
          }
        }
      }
      .ant-form-item-control-wrapper {
        .ant-form-item-children {
          .ant-radio-group {
            display: flex;
            // justify-content: space-between;
            margin-top: 4px;
            .ant-radio-wrapper {
              margin-right: 24px;
              &:last-child {
                margin-right: 0;
              }
              > span {
                padding-right: 0;
                color: #71717a;
              }
              .ant-radio {
                .ant-radio-input {
                  background: #a1a1aa;
                }
              }
            }
          }
          .ant-input {
            min-height: 100px;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .btn-box {
    display: flex;
    justify-content: flex-end;
    .ant-btn {
      // width: 76px;
      // height: 36px;
      // background: #ffffff;
      // border: 1px solid #008adc;
      // font-size: 14px;
      // color: #008adc;
      // font-weight: 600;
      // border-radius: 6px;
    }
    .ant-btn-primary {
      // background: #008adc;
      // color: #ffffff;
      // font-weight: 600;
      margin-left: 12px;
    }
  }
}
</style>