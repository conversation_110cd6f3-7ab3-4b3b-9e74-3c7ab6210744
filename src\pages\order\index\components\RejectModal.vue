<template>
  <div>
    <a-modal
      title="一键驳回"
      width="640px"
      :visible="visible"
      @cancel="onCancel"
      @ok="onOk"
      wrapClassName="order-reject-modal"
    >
      <div class="info-body">
        <div class="des">
          <a-icon type="question-circle" />
          <span> 是否确认一键驳回当前工单？</span>
        </div>
        <div class="data">
          <div>待处理数量：{{ dealCount }}</div>
          <div>申请整改中：{{ rectifingCount }}</div>
          <div>申请白名单：{{ whiteCount }}</div>
        </div>
        <div class="reject-reason">
          <Form ref="form" v-bind="params" :formData="formData"></Form>
        </div>
      </div>
      <!-- <div slot="footer">
        <a-button @click="onCancel" class="highlight">关闭</a-button>
        <a-button @click="onOk" class="highlight">确定</a-button>
      </div> -->
    </a-modal>
  </div>
</template>

<script>
import Form from '@/components/Form';
import { allReject, getReviewInformation } from '@/api/order';
const formParams = ctx => {
  return {
    layout: 'horizontal',
    fields: [
      {
        type: 'Textarea',
        label: '驳回原因',
        key: 'comment',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' },
          {
            validator: (rules, value, callback) => {
              const bool = /^\S/.test(value);
              if (!bool) {
                callback(new Error('不能以空格开始'));
              } else {
                callback();
              }
            }
          }
        ]
      }
    ]
  };
};
export default {
  name: '',
  components: { Form },
  props: {
    activeKey: String
  },
  data() {
    return {
      visible: false,
      formData: {},
      params: formParams(this),
      dealCount: 0,
      rectifingCount: 0,
      whiteCount: 0
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    show(record) {
      this.id = record.id || record.data_source_id;
      getReviewInformation({ record_id: this.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.visible = true;
            const resData = _.get(res, 'data.data');
            this.dealCount = resData.deal_count;
            this.rectifingCount = resData.rectifing_count;
            this.whiteCount = resData.white_count;
          } else {
            this.visible = true;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.visible = true;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onCancel() {
      this.hide();
    },
    hide() {
      this.visible = false;
      this.formData = {};
    },
    onOk() {
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          const data = form.getData();
          this.$showLoading();
          // if (this.activeKey == 'datasource') {
          //   databaseReviewPassOrReject({
          //     data_source_id: this.id,
          //     audit_status: -1,
          //     ...data
          //   })
          //     .then(res => {
          //       if (CommonUtil.isSuccessCode(res)) {
          //         this.$hideLoading({ duration: 0 });
          //         this.hide();
          //         this.$emit('refresh');
          //       } else {
          //         this.$hideLoading({
          //           method: 'error',
          //           tips: _.get(res, 'data.message')
          //         });
          //       }
          //     })
          //     .catch(e => {
          //       this.$hideLoading({
          //         method: 'error',
          //         tips: _.get(e || {}, 'response.data.message') || '请求失败'
          //       });
          //     });
          // } else {
          allReject({ review_id: this.id, ...data })
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({ duration: 0 });
                this.hide();
                this.$emit('refresh');
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
        // }
      });
    }
  }
};
</script>

<style lang="less">
.order-reject-modal {
  .ant-modal-content {
    // .ant-modal-close {
    //   .ant-modal-close-x {
    //     color: #000;
    //   }
    // }
    // .ant-modal-header {
    //   background: #fff;
    //   border-bottom: 1px solid #e8e8e8;
    //   .ant-modal-title {
    //     color: #000;
    //   }
    //   border: none;
    // }
    .ant-modal-body {
      .info-body {
        .des {
          .anticon {
            font-size: 16px;
            color: #f29339;
            margin-right: 8px;
          }
        }
        .data {
          padding: 24px 28px;
          > div {
            padding: 8px 0;
          }
        }
        .reject-reason {
          padding: 0 28px;
          .ant-form {
            .ant-row {
              .ant-col {
                .ant-input {
                  min-height: 100px;
                }
              }
            }
          }
        }
      }
    }
    // .ant-modal-footer {
    //   border: none;
    //   padding: 10px 24px;
    //   > div {
    //     .ant-btn {
    //       height: 32px;
    //       font-size: 12px;
    //       margin-right: 0;
    //       margin-left: 8px;
    //       border-radius: 6px;
    //       &.highlight {
    //         color: #008adc;
    //         border-color: #7fc4ed;
    //         &:hover {
    //           color: @primary-5;
    //           border-color: #008adc;
    //         }
    //       }

    //       &.ant-btn-primary {
    //         background: #008adc;
    //         color: #ffffff;
    //         &:hover {
    //           background: #219be3;
    //           color: #ffffff;
    //         }
    //       }
    //     }
    //   }
    // }
  }
}
</style>