export default function (ctx) {
  const statusColor = {
    0: '#B0AEAE',
    1: '#52C41A',
    '-1': '#FF4D4F',
    2: '#52C41A',
    9: '#FF4D4F',
    '-2': '#FF4D4F'
  };
  const statusText = {
    '0': '未知',
    '1': '已通过',
    '2': '白名单通过',
    '9': '错误',
    '-1': '未通过',
    '-2': '待审核'
  };
  const initiatedColumns = [
    {
      title: '审核方式',
      dataIndex: 'audit_type',
      key: 'audit_type',
      customRender: (text) => {
        return text == 1 ? '在线审核' : '离线审核';
      },
      width: 200
    },
    {
      title: '应用',
      dataIndex: 'project_name',
      key: 'project_name',
      width: 120
    },
    {
      title: '数据库',
      dataIndex: 'datasource_name',
      key: 'datasource_name',
      scopedSlots: { customRender: 'datasource_name' },
      width: 180
    },
    {
      title: 'SQL条数',
      dataIndex: 'sql_count',
      key: 'sql_count',
      scopedSlots: { customRender: 'sql_count' },
      width: 120
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'status' },
      width: 120
    },
    // {
    //   title: '通过率',
    //   dataIndex: 'success_rate',
    //   key: 'success_rate',
    //   customRender: (text) => {
    //     return text + '%';
    //   },
    //   width: 120
    // },
    {
      title: '发起人',
      dataIndex: 'created_by',
      key: 'created_by',
      width: 120
    },
    {
      title: '发起时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 150,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const initiatedSearchFields = [
    {
      type: 'Select',
      label: '审核方式',
      key: 'audit_type',
      // mainSearch: true,
      props: {
        options: [
          { label: '在线审核', value: 1 },
          { label: '离线审核', value: 0 }
        ]
      }
    },
    {
      type: 'Input',
      label: '应用',
      key: 'project_name'
    },
    {
      type: 'Select',
      label: '状态',
      key: 'status',
      props: {
        options: [
          {
            label: '未  知',
            value: '0'
          },
          {
            label: '已通过',
            value: '1'
          },
          {
            label: '白名单通过',
            value: '2'
          },
          {
            label: '错误',
            value: '9'
          },
          {
            label: '未通过',
            value: '-1'
          },
          {
            label: '待审核',
            value: '-2'
          }
        ]
      }
    },
    {
      type: 'Input',
      label: '发起人',
      key: 'created_by',
      props: {
        placeholder: '请输入'
      }
    },
    {
      type: 'Select',
      label: '数据库类型',
      key: 'db_type',
      props: {
        url: '/sqlreview/project/rule_support_db'
        // options: [
        //   { label: 'MYSQL', value: 'MYSQL' },
        //   { label: 'ORACLE', value: 'ORACLE' },
        //   { label: 'POSTGRE', value: 'POSTGRE' },
        //   { label: 'TIDB', value: 'TIDB' },
        //   { label: 'DB2', value: 'DB2' },
        //   { label: 'SQLSERVER', value: 'SQLSERVER' },
        //   { label: 'KINGBASE', value: 'KINGBASE' },
        //   { label: 'IMPALA', value: 'IMPALA' },
        //   { label: 'HIVE', value: 'HIVE' },
        //   { label: 'OB_MYSQL', value: 'OB_MYSQL' },
        //   { label: 'PRESTO', value: 'PRESTO' },
        //   { label: 'GOLDENDB', value: 'GOLDENDB' },
        //   { label: 'MOGDB', value: 'MOGDB' }
        // ]
      }
    }
  ];

  const allColumns = [
    {
      title: '数据库名称',
      dataIndex: 'name',
      key: 'name',
      scopedSlots: { customRender: 'name' },
      width: 200
    },
    {
      title: '所属实例',
      dataIndex: 'datasource_name',
      key: 'datasource_name',
      scopedSlots: { customRender: 'datasource_name' },
      width: 200
    },
    {
      title: '所属应用',
      key: 'project_name',
      dataIndex: 'project_name',
      width: 200,
      scopedSlots: { customRender: 'project_name' }
    },
    {
      title: '表数量',
      dataIndex: 'table_count',
      key: 'table_count',
      scopedSlots: { customRender: 'table_count' },
      width: 100
    },
    {
      title: '权限控制',
      dataIndex: 'permission_status',
      key: 'permission_status',
      scopedSlots: { customRender: 'permission_status' },
      width: 100
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 150,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const allSearchFields = [
    {
      type: 'Input',
      label: '数据库名称',
      key: 'name',
      mainSearch: true,
      props: {
        placeholder: '请输入数据库名称'
      }
    },
    {
      type: 'Input',
      label: '实例名称',
      key: 'datasource_name',
      props: {
        placeholder: '请输入实例名称'
      }
    },
    {
      type: 'Select',
      label: '环境',
      key: 'env',
      props: {
        options: [
          {
            label: '测试',
            value: 'TEST'
          },
          {
            label: '生产',
            value: 'PROD'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '权限控制',
      key: 'permission_status',
      props: {
        options: [
          {
            label: '开',
            value: '1'
          },
          {
            label: '关',
            value: '0'
          }
        ]
      }
    }
  ];
  return {
    statusColor,
    statusText,
    initiatedColumns,
    initiatedSearchFields,
    allColumns,
    allSearchFields
  };
}
