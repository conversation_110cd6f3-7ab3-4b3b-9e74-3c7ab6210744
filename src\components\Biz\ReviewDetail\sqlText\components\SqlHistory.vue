<template>
  <div class="sql-history">
    <!-- 分支信息 -->
    <div class="header-info">
      <div class="left">
        <span class="tag">{{ base.review_point }}</span>
        <span class="des">
          <custom-icon type="code"></custom-icon>
          <span> (当前) 动态条件组合执行效率最差SQL </span>
        </span>
      </div>
      <div class="right">
        <span class="tag">{{ history.compare_review_point }}</span>
        <span class="des">
          <custom-icon type="code"></custom-icon>
          <span> (历史) 动态条件组合执行效率最差SQL</span>
        </span>
      </div>
    </div>

    <!-- 规则，错误报告 -->
    <div class="rule-and-error-message-container">
      <div class="rule-and-error-message left">
        <div class="rule-container" v-if="base.ai_comment.length > 0">
          <a class="expand-action" @click="onExpand"
            >{{ !ruleExpand ? '查看全部' : '收起' }}({{
              base.ai_comment.length
            }})</a
          >
          <div class="shrink-content" v-if="!ruleExpand">
            <span v-for="(item, index) in base.ai_comment" :key="index">
              <custom-icon
                class="rule-icon high"
                type="lu-icon-alarm"
                v-if="item.rule_result == 0"
              />
              <custom-icon
                class="rule-icon low"
                type="lu-icon-alarm"
                v-if="item.rule_result == 1"
              />
              {{ item.ai_comment }}</span
            >
          </div>
          <div class="expand-content rule" v-if="ruleExpand">
            <div v-for="(item, index) in base.ai_comment" :key="index">
              <custom-icon
                class="rule-icon high"
                type="lu-icon-alarm"
                v-if="item.rule_result == 0"
              />
              <custom-icon
                class="rule-icon low"
                type="lu-icon-alarm"
                v-if="item.rule_result == 1"
              />
              <span>{{ item.ai_comment }}</span>
            </div>
          </div>
        </div>
        <div
          class="error-message-container"
          v-if="base.error_message.length > 0"
        >
          <a class="expand-action" @click="onErrorExpand"
            >{{ !errorExpand ? '查看全部' : '收起' }}({{
              base.error_message.length
            }})</a
          >
          <div class="shrink-content" v-if="!errorExpand">
            <span v-for="(item, index) in base.error_message" :key="index">
              <custom-icon class="rule-icon error" type="lu-icon-unusual" />{{
                item.error_message
              }}
            </span>
          </div>
          <div class="expand-content error-message" v-if="errorExpand">
            <div v-for="(item, index) in base.error_message" :key="index">
              <custom-icon class="rule-icon error" type="lu-icon-unusual" />
              <span>{{ item.error_message }}</span>
              <div
                v-if="item.data_source_list && item.data_source_list.length > 0"
                class="data-source-content"
              >
                <span v-for="(db, index) in item.data_source_list" :key="index">
                  <a-tag :class="db.env.toLowerCase()">{{
                    db.env.toLowerCase() == 'test' ? '测试' : '生产'
                  }}</a-tag>
                  <DbImg
                    :value="db.db_type"
                    :schemaName="db.name + '(' + db.db_url + ')'"
                    :limit="32"
                    mode="simple"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="rule-and-error-message right">
        <div
          class="rule-container"
          v-if="history.compare_ai_comment.length > 0"
        >
          <a class="expand-action" @click="onExpand"
            >{{ !ruleExpand ? '查看全部' : '收起' }}({{
              history.compare_ai_comment.length
            }})</a
          >
          <div class="shrink-content" v-if="!ruleExpand">
            <span
              v-for="(item, index) in history.compare_ai_comment"
              :key="index"
            >
              <custom-icon
                class="rule-icon high"
                type="lu-icon-alarm"
                v-if="item.rule_result == 0"
              />
              <custom-icon
                class="rule-icon low"
                type="lu-icon-alarm"
                v-if="item.rule_result == 1"
              />
              {{ item.ai_comment }}</span
            >
          </div>
          <div class="expand-content rule" v-if="ruleExpand">
            <div
              v-for="(item, index) in history.compare_ai_comment"
              :key="index"
            >
              <custom-icon
                class="rule-icon high"
                type="lu-icon-alarm"
                v-if="item.rule_result == 0"
              />
              <custom-icon
                class="rule-icon low"
                type="lu-icon-alarm"
                v-if="item.rule_result == 1"
              />
              <span>{{ item.ai_comment }}</span>
            </div>
          </div>
        </div>
        <div
          class="error-message-container"
          v-if="history.compare_error_message.length > 0"
        >
          <a class="expand-action" @click="onErrorExpand"
            >{{ !errorExpand ? '查看全部' : '收起' }}({{
              history.compare_error_message.length
            }})</a
          >
          <div class="shrink-content" v-if="!errorExpand">
            <span
              v-for="(item, index) in history.compare_error_message"
              :key="index"
            >
              <custom-icon class="rule-icon error" type="lu-icon-unusual" />{{
                item.error_message
              }}
            </span>
          </div>
          <div class="expand-content error-message" v-if="errorExpand">
            <div
              v-for="(item, index) in history.compare_error_message"
              :key="index"
            >
              <custom-icon class="rule-icon error" type="lu-icon-unusual" />
              <span>{{ item.error_message }}</span>
              <div
                v-if="item.data_source_list && item.data_source_list.length > 0"
                class="data-source-content"
              >
                <span v-for="(db, index) in item.data_source_list" :key="index">
                  <a-tag :class="db.env.toLowerCase()">{{
                    db.env.toLowerCase() == 'test' ? '测试' : '生产'
                  }}</a-tag>
                  <DbImg
                    :value="db.db_type"
                    :schemaName="db.name + '(' + db.db_url + ')'"
                    :limit="32"
                    mode="simple"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="sql-text-container">
      <Diff :list="diffList" />
    </div>

    <div class="compare-sql-plan-container">
      <div class="title">
        <span>
          <custom-icon type="code" />
          <span>(当前) 动态组合最差执行计划</span>
        </span>
        <span>
          <custom-icon type="code" />
          <span>(历史) 动态组合最差执行计划</span>
        </span>
      </div>
      <div class="compare-sql-plan-content">
        <Diff :list="sqlPlanList" />
      </div>
    </div>
  </div>
</template>

<script>
import Diff from '@/components/Diff';
import Coder from '@/components/Coder';
export default {
  components: { Coder, Diff },
  props: {
    detaliData: {
      type: Object,
      default: () => {}
    },
    formatTitle: {
      type: Object,
      default: () => {}
    },
    sqlHistoryCompare: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    diffList() {
      const res = { oldStr: '', newStr: '', filename: '' };
      // res.newStr = this.base.sql_text || '';
      // res.oldStr = this.history.compare_sql_text || '';
      res.newStr = this.history.compare_sql_text || '';
      res.oldStr = this.base.sql_text || '';
      const list = [
        {
          filename: '',
          oldStr: res.oldStr || '',
          newStr: res.newStr || res.oldStr
        }
      ];
      return list;
    },
    sqlPlanList() {
      const res = { oldStr: '', newStr: '', filename: '' };
      // res.newStr = this.base.sql_plan || '';
      // res.oldStr = this.history.compare_sql_plan || '';
      res.newStr = this.history.compare_sql_plan || '';
      res.oldStr = this.base.sql_plan || '';
      const list = [
        {
          filename: '',
          oldStr: res.oldStr || '',
          newStr: res.newStr || res.oldStr
        }
      ];
      return list;
    }
  },
  data() {
    return {
      base: {},
      history: {},
      ruleExpand: false,
      errorExpand: false,
      str: ''
    };
  },
  mounted() {},
  methods: {
    onExpand() {
      this.ruleExpand = !this.ruleExpand;
    },
    onErrorExpand() {
      this.errorExpand = !this.errorExpand;
    }
  },
  watch: {
    sqlHistoryCompare: {
      handler(newVal) {
        if (newVal) {
          this.base = newVal.base;
          this.history = newVal.history;
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.sql-history {
  .header-info {
    display: flex;
    justify-content: space-between;

    .left,
    .right {
      width: 50%;
      display: flex;
      flex-direction: column;

      .tag {
        width: fit-content;
        background: #e6f4ff;
        border: 1px solid rgba(145, 202, 255, 1);
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        color: #1677ff;
        padding: 6px 16px 6px 32px;
        margin-bottom: 16px;
        position: relative;
        &::before {
          display: block;
          content: '';
          width: 8px;
          height: 8px;
          position: absolute;
          top: 14px;
          left: 16px;
          border-radius: 50%;
          background: @primary-color;
        }
      }
      .des {
        font-family: PingFangSC-Regular;
        color: #1f1f1f;
        margin-bottom: 8px;
        .anticon {
          margin-right: 4px;
        }
      }
    }
    .left {
      padding: 0 8px 0 0;
    }
    .right {
      padding: 0 0 0 8px;
      .tag {
        background: #fafafa;
        border: 1px solid rgba(217, 217, 217, 1);
        color: #1f1f1f;
        &::before {
          display: block;
          content: '';
          width: 8px;
          height: 8px;
          position: absolute;
          top: 14px;
          left: 16px;
          border-radius: 50%;
          background: #1f1f1f;
        }
      }
    }
  }

  .rule-and-error-message-container {
    display: flex;
    .rule-and-error-message {
      width: 50%;
      .rule-icon {
        color: #e4e4e7;
        font-size: 12px;
        align-self: self-start;
        padding-top: 4px;
        &.high {
          color: #e71d36;
          margin-right: 4px;
        }
        &.low {
          color: #f29339;
          margin-right: 4px;
        }
        &.error {
          color: #71717a;
          margin-right: 4px;
        }
      }
      &.left {
        padding: 0 8px 0 0;
      }
      &.right {
        padding: 0 0 0 8px;
      }
      .error-message-container,
      .rule-container {
        width: 100%;
        background: #fffbe6;
        border: 1px solid rgba(255, 229, 143, 1);
        margin-bottom: 16px;
        position: relative;
        .expand-action {
          position: absolute;
          top: 12px;
          right: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #1890ff;
        }
        .shrink-content {
          width: 80%;
          display: flex;
          justify-content: space-between;
          // overflow: hidden;
          // text-overflow: ellipsis;
          // word-break: normal;
          // white-space: normal;
          padding: 12px 16px;
          span {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #1f1f1f;
            white-space: nowrap;
            margin-right: 16px;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
        .expand-content {
          display: flex;
          flex-direction: column;
          width: 90%;
          &.rule {
            > div {
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #1f1f1f;
              padding: 12px 16px;
            }
          }
          &.error-message {
            > div {
              padding: 12px 16px 16px 16px;
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #1f1f1f;
              &:last-child {
                padding: 8px 16px;
              }
              .data-source-content {
                > span {
                  display: flex;
                  padding: 8px 0;
                  .ant-tag {
                    margin-right: 8px;
                    &.test {
                      background: #f6ffed;
                      border: 1px solid rgba(183, 235, 143, 1);
                      font-family: PingFangSC-Regular;
                      font-size: 12px;
                      color: #52c41a;
                    }
                    &.prod {
                      background: #fff7e6;
                      border: 1px solid rgba(255, 213, 145, 1);
                      font-family: PingFangSC-Regular;
                      font-size: 12px;
                      color: #fa8c16;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  /deep/.sql-text-container {
    .custom-diff {
      .d2h-wrapper {
        .d2h-file-wrapper {
          .d2h-file-header {
            display: none;
          }
        }
      }
    }
  }
  .compare-sql-plan-container {
    margin-top: 32px;
    .title {
      display: flex;
      margin-bottom: 16px;
      > span {
        display: inline-block;
        width: 50%;
        font-family: PingFangSC-Regular;
        color: #1f1f1f;
        &:first-child {
          padding: 0 8px 0 0;
        }
        &:last-child {
          padding: 0 0 0 8px;
        }
        .anticon {
          margin-right: 4px;
          color: #27272a;
        }
      }
    }
    /deep/.compare-sql-plan-content {
      .custom-diff {
        .d2h-wrapper {
          .d2h-file-wrapper {
            .d2h-file-header {
              display: none;
            }
          }
        }
      }
    }
  }
}
</style>