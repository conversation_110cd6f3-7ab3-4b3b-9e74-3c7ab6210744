<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2809617" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon luicon">&#xe6d2;</span>
                <div class="name">realtimesql</div>
                <div class="code-name">&amp;#xe6d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6d3;</span>
                <div class="name">topsql</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6d0;</span>
                <div class="name">awspgsql</div>
                <div class="code-name">&amp;#xe6d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6d1;</span>
                <div class="name">awsmysql</div>
                <div class="code-name">&amp;#xe6d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe776;</span>
                <div class="name">mogdb</div>
                <div class="code-name">&amp;#xe776;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6cf;</span>
                <div class="name">re-review</div>
                <div class="code-name">&amp;#xe6cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6cd;</span>
                <div class="name">orderdown</div>
                <div class="code-name">&amp;#xe6cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6ce;</span>
                <div class="name">orderup</div>
                <div class="code-name">&amp;#xe6ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6cb;</span>
                <div class="name">goldendb</div>
                <div class="code-name">&amp;#xe6cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6ca;</span>
                <div class="name">node</div>
                <div class="code-name">&amp;#xe6ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe962;</span>
                <div class="name">loading1</div>
                <div class="code-name">&amp;#xe962;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6cc;</span>
                <div class="name">folder</div>
                <div class="code-name">&amp;#xe6cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c9;</span>
                <div class="name">cpu</div>
                <div class="code-name">&amp;#xe6c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xed50;</span>
                <div class="name">release</div>
                <div class="code-name">&amp;#xed50;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c8;</span>
                <div class="name">memo</div>
                <div class="code-name">&amp;#xe6c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe78a;</span>
                <div class="name">doris</div>
                <div class="code-name">&amp;#xe78a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c7;</span>
                <div class="name">rds-mysql</div>
                <div class="code-name">&amp;#xe6c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c6;</span>
                <div class="name">polardb</div>
                <div class="code-name">&amp;#xe6c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c5;</span>
                <div class="name">presto</div>
                <div class="code-name">&amp;#xe6c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6ba;</span>
                <div class="name">index</div>
                <div class="code-name">&amp;#xe6ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6bb;</span>
                <div class="name">primarykey</div>
                <div class="code-name">&amp;#xe6bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6bc;</span>
                <div class="name">indexgroup</div>
                <div class="code-name">&amp;#xe6bc;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6bd;</span>
                <div class="name">fieldgroup</div>
                <div class="code-name">&amp;#xe6bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6be;</span>
                <div class="name">tablegroup</div>
                <div class="code-name">&amp;#xe6be;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6bf;</span>
                <div class="name">viewgroup</div>
                <div class="code-name">&amp;#xe6bf;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c0;</span>
                <div class="name">function2</div>
                <div class="code-name">&amp;#xe6c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c1;</span>
                <div class="name">functiongroup</div>
                <div class="code-name">&amp;#xe6c1;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c2;</span>
                <div class="name">view1</div>
                <div class="code-name">&amp;#xe6c2;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c3;</span>
                <div class="name">schema</div>
                <div class="code-name">&amp;#xe6c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6c4;</span>
                <div class="name">triggergroup</div>
                <div class="code-name">&amp;#xe6c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b9;</span>
                <div class="name">ID</div>
                <div class="code-name">&amp;#xe6b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xeb40;</span>
                <div class="name">apacherocketmq</div>
                <div class="code-name">&amp;#xeb40;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b8;</span>
                <div class="name">amazon</div>
                <div class="code-name">&amp;#xe6b8;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b7;</span>
                <div class="name">dameng</div>
                <div class="code-name">&amp;#xe6b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b6;</span>
                <div class="name">filter</div>
                <div class="code-name">&amp;#xe6b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe699;</span>
                <div class="name">unusual</div>
                <div class="code-name">&amp;#xe699;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b5;</span>
                <div class="name">notice</div>
                <div class="code-name">&amp;#xe6b5;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe693;</span>
                <div class="name">nopass</div>
                <div class="code-name">&amp;#xe693;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe694;</span>
                <div class="name">unknown</div>
                <div class="code-name">&amp;#xe694;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe695;</span>
                <div class="name">pass</div>
                <div class="code-name">&amp;#xe695;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6ae;</span>
                <div class="name">download</div>
                <div class="code-name">&amp;#xe6ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6af;</span>
                <div class="name">confirm</div>
                <div class="code-name">&amp;#xe6af;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b0;</span>
                <div class="name">code</div>
                <div class="code-name">&amp;#xe6b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b2;</span>
                <div class="name">upload</div>
                <div class="code-name">&amp;#xe6b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b4;</span>
                <div class="name">warning1</div>
                <div class="code-name">&amp;#xe6b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b1;</span>
                <div class="name">day</div>
                <div class="code-name">&amp;#xe6b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6b3;</span>
                <div class="name">database</div>
                <div class="code-name">&amp;#xe6b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6ad;</span>
                <div class="name">night</div>
                <div class="code-name">&amp;#xe6ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6ab;</span>
                <div class="name">alarm</div>
                <div class="code-name">&amp;#xe6ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6ac;</span>
                <div class="name">ring</div>
                <div class="code-name">&amp;#xe6ac;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a9;</span>
                <div class="name">impala</div>
                <div class="code-name">&amp;#xe6a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6aa;</span>
                <div class="name">hive</div>
                <div class="code-name">&amp;#xe6aa;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a3;</span>
                <div class="name">dba</div>
                <div class="code-name">&amp;#xe6a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a4;</span>
                <div class="name">developer</div>
                <div class="code-name">&amp;#xe6a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a5;</span>
                <div class="name">if</div>
                <div class="code-name">&amp;#xe6a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a6;</span>
                <div class="name">do</div>
                <div class="code-name">&amp;#xe6a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a7;</span>
                <div class="name">admin</div>
                <div class="code-name">&amp;#xe6a7;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a8;</span>
                <div class="name">enable</div>
                <div class="code-name">&amp;#xe6a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a2;</span>
                <div class="name">kingbase</div>
                <div class="code-name">&amp;#xe6a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a1;</span>
                <div class="name">hudi</div>
                <div class="code-name">&amp;#xe6a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe696;</span>
                <div class="name">viewlist</div>
                <div class="code-name">&amp;#xe696;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe697;</span>
                <div class="name">viewgrid</div>
                <div class="code-name">&amp;#xe697;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe6a0;</span>
                <div class="name">java</div>
                <div class="code-name">&amp;#xe6a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe69d;</span>
                <div class="name">sql</div>
                <div class="code-name">&amp;#xe69d;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe69e;</span>
                <div class="name">xml</div>
                <div class="code-name">&amp;#xe69e;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe69b;</span>
                <div class="name">annotation</div>
                <div class="code-name">&amp;#xe69b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe69f;</span>
                <div class="name">mybatis</div>
                <div class="code-name">&amp;#xe69f;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe640;</span>
                <div class="name">clean</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe629;</span>
                <div class="name">storage</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe69a;</span>
                <div class="name">dbreview</div>
                <div class="code-name">&amp;#xe69a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe69c;</span>
                <div class="name">codereview</div>
                <div class="code-name">&amp;#xe69c;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe692;</span>
                <div class="name">log</div>
                <div class="code-name">&amp;#xe692;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe698;</span>
                <div class="name">whitelist</div>
                <div class="code-name">&amp;#xe698;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe634;</span>
                <div class="name">config</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe63c;</span>
                <div class="name">PC</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe644;</span>
                <div class="name">order</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe64e;</span>
                <div class="name">rule</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe68e;</span>
                <div class="name">user</div>
                <div class="code-name">&amp;#xe68e;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe68f;</span>
                <div class="name">apps</div>
                <div class="code-name">&amp;#xe68f;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe690;</span>
                <div class="name">qreview</div>
                <div class="code-name">&amp;#xe690;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe691;</span>
                <div class="name">sqledit</div>
                <div class="code-name">&amp;#xe691;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe628;</span>
                <div class="name">opengauss</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe616;</span>
                <div class="name">data-sensitive</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe618;</span>
                <div class="name">gaussdb</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe601;</span>
                <div class="name">mapping</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe602;</span>
                <div class="name">function1</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe603;</span>
                <div class="name">router</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe604;</span>
                <div class="name">store</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe68d;</span>
                <div class="name">gbase</div>
                <div class="code-name">&amp;#xe68d;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe627;</span>
                <div class="name">GBASE</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe70b;</span>
                <div class="name">impala</div>
                <div class="code-name">&amp;#xe70b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe68c;</span>
                <div class="name">capacity</div>
                <div class="code-name">&amp;#xe68c;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe68b;</span>
                <div class="name">batch</div>
                <div class="code-name">&amp;#xe68b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe689;</span>
                <div class="name">operation</div>
                <div class="code-name">&amp;#xe689;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe68a;</span>
                <div class="name">safe</div>
                <div class="code-name">&amp;#xe68a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe687;</span>
                <div class="name">applyfor1</div>
                <div class="code-name">&amp;#xe687;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe688;</span>
                <div class="name">seal</div>
                <div class="code-name">&amp;#xe688;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe685;</span>
                <div class="name">tdsql-1</div>
                <div class="code-name">&amp;#xe685;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe686;</span>
                <div class="name">sql-server</div>
                <div class="code-name">&amp;#xe686;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe684;</span>
                <div class="name">scan</div>
                <div class="code-name">&amp;#xe684;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe682;</span>
                <div class="name">db2</div>
                <div class="code-name">&amp;#xe682;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe683;</span>
                <div class="name">tdsql</div>
                <div class="code-name">&amp;#xe683;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe681;</span>
                <div class="name">oceanbase</div>
                <div class="code-name">&amp;#xe681;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe680;</span>
                <div class="name">loading</div>
                <div class="code-name">&amp;#xe680;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe67f;</span>
                <div class="name">app</div>
                <div class="code-name">&amp;#xe67f;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe679;</span>
                <div class="name">disable</div>
                <div class="code-name">&amp;#xe679;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe67c;</span>
                <div class="name">sensitive</div>
                <div class="code-name">&amp;#xe67c;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe67d;</span>
                <div class="name">impower</div>
                <div class="code-name">&amp;#xe67d;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe67e;</span>
                <div class="name">applyfor</div>
                <div class="code-name">&amp;#xe67e;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe672;</span>
                <div class="name">person1</div>
                <div class="code-name">&amp;#xe672;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe66e;</span>
                <div class="name">joint</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe668;</span>
                <div class="name">idea</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe67b;</span>
                <div class="name">star</div>
                <div class="code-name">&amp;#xe67b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe659;</span>
                <div class="name">Filter</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe673;</span>
                <div class="name">bell</div>
                <div class="code-name">&amp;#xe673;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe674;</span>
                <div class="name">cost</div>
                <div class="code-name">&amp;#xe674;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe675;</span>
                <div class="name">rollback</div>
                <div class="code-name">&amp;#xe675;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe676;</span>
                <div class="name">pass</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe677;</span>
                <div class="name">exempt</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe678;</span>
                <div class="name">details</div>
                <div class="code-name">&amp;#xe678;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe67a;</span>
                <div class="name">Maintain</div>
                <div class="code-name">&amp;#xe67a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe66f;</span>
                <div class="name">roam</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe670;</span>
                <div class="name">import</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe671;</span>
                <div class="name">pull</div>
                <div class="code-name">&amp;#xe671;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe66a;</span>
                <div class="name">robot</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe66b;</span>
                <div class="name">person1</div>
                <div class="code-name">&amp;#xe66b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe66c;</span>
                <div class="name">staring</div>
                <div class="code-name">&amp;#xe66c;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe66d;</span>
                <div class="name">wrong</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe669;</span>
                <div class="name">right1</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe660;</span>
                <div class="name">format</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe661;</span>
                <div class="name">user-platform</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe662;</span>
                <div class="name">check</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe663;</span>
                <div class="name">vs</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe664;</span>
                <div class="name">user-role</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe665;</span>
                <div class="name">full</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe667;</span>
                <div class="name">quit</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe65e;</span>
                <div class="name">stop</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe65f;</span>
                <div class="name">run</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe65b;</span>
                <div class="name">search</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe65d;</span>
                <div class="name">person</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe65c;</span>
                <div class="name">trigger</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe641;</span>
                <div class="name">minus</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe643;</span>
                <div class="name">edit</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe652;</span>
                <div class="name">end</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe657;</span>
                <div class="name">error</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe658;</span>
                <div class="name">check</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe659;</span>
                <div class="name">pending</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe65a;</span>
                <div class="name">warning</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe647;</span>
                <div class="name">lock new</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe64b;</span>
                <div class="name">unlock</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe656;</span>
                <div class="name">sort</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe633;</span>
                <div class="name">business</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe635;</span>
                <div class="name">copy</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe636;</span>
                <div class="name">ddl</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe637;</span>
                <div class="name">dml</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe638;</span>
                <div class="name">delete</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe639;</span>
                <div class="name">editor</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe63a;</span>
                <div class="name">function</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe63b;</span>
                <div class="name">field</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe63d;</span>
                <div class="name">exit</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe63e;</span>
                <div class="name">entity</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe63f;</span>
                <div class="name">home new</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe642;</span>
                <div class="name">reset</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe645;</span>
                <div class="name">right</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe646;</span>
                <div class="name">list</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe648;</span>
                <div class="name">plus new</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe649;</span>
                <div class="name">key</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe64a;</span>
                <div class="name">memory</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe64c;</span>
                <div class="name">sequence</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe64d;</span>
                <div class="name">sql resolver</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe64f;</span>
                <div class="name">user new</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe650;</span>
                <div class="name">up</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe651;</span>
                <div class="name">view</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe653;</span>
                <div class="name">system</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe654;</span>
                <div class="name">task</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe655;</span>
                <div class="name">schema</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe631;</span>
                <div class="name">rosesql</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe632;</span>
                <div class="name">ubisql</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe630;</span>
                <div class="name">starrocks</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe62f;</span>
                <div class="name">flink</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe62e;</span>
                <div class="name">tidb</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe666;</span>
                <div class="name">清空缓存</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe62a;</span>
                <div class="name">clickhouse</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe62b;</span>
                <div class="name">kafka</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe62c;</span>
                <div class="name">HBASE</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe62d;</span>
                <div class="name">elasticserch</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe622;</span>
                <div class="name">Oracle</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe623;</span>
                <div class="name">lock</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe61b;</span>
                <div class="name">partition</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe61d;</span>
                <div class="name">table</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe624;</span>
                <div class="name">mysql</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe625;</span>
                <div class="name">pgsql</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe626;</span>
                <div class="name">unlock</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe617;</span>
                <div class="name">extend-info</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe613;</span>
                <div class="name">data-change</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe614;</span>
                <div class="name">user</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe610;</span>
                <div class="name">refresh</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe619;</span>
                <div class="name">config</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe61a;</span>
                <div class="name">publish</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe61c;</span>
                <div class="name">expand</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe61e;</span>
                <div class="name">metadata</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe61f;</span>
                <div class="name">change-order</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe620;</span>
                <div class="name">base-info</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon">&#xe621;</span>
                <div class="name">databus</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'luicon';
  src: url('iconfont.eot?t=1710999200839'); /* IE9 */
  src: url('iconfont.eot?t=1710999200839#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('iconfont.woff2?t=1710999200839') format('woff2'),
       url('iconfont.woff?t=1710999200839') format('woff'),
       url('iconfont.ttf?t=1710999200839') format('truetype'),
       url('iconfont.svg?t=1710999200839#luicon') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.luicon {
  font-family: "luicon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="luicon"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"luicon" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon luicon lu-icon-realtimesql"></span>
            <div class="name">
              realtimesql
            </div>
            <div class="code-name">.lu-icon-realtimesql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-topsql"></span>
            <div class="name">
              topsql
            </div>
            <div class="code-name">.lu-icon-topsql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-awspgsql"></span>
            <div class="name">
              awspgsql
            </div>
            <div class="code-name">.lu-icon-awspgsql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-awsmysql"></span>
            <div class="name">
              awsmysql
            </div>
            <div class="code-name">.lu-icon-awsmysql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-mogdb"></span>
            <div class="name">
              mogdb
            </div>
            <div class="code-name">.lu-icon-mogdb
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-re-review"></span>
            <div class="name">
              re-review
            </div>
            <div class="code-name">.lu-icon-re-review
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-orderdown"></span>
            <div class="name">
              orderdown
            </div>
            <div class="code-name">.lu-icon-orderdown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-orderup"></span>
            <div class="name">
              orderup
            </div>
            <div class="code-name">.lu-icon-orderup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-goldendb"></span>
            <div class="name">
              goldendb
            </div>
            <div class="code-name">.lu-icon-goldendb
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-node"></span>
            <div class="name">
              node
            </div>
            <div class="code-name">.lu-icon-node
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-loading1"></span>
            <div class="name">
              loading1
            </div>
            <div class="code-name">.lu-icon-loading1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-folder"></span>
            <div class="name">
              folder
            </div>
            <div class="code-name">.lu-icon-folder
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-cpu"></span>
            <div class="name">
              cpu
            </div>
            <div class="code-name">.lu-icon-cpu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-release-copy"></span>
            <div class="name">
              release
            </div>
            <div class="code-name">.lu-icon-release-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-memo"></span>
            <div class="name">
              memo
            </div>
            <div class="code-name">.lu-icon-memo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-Doris"></span>
            <div class="name">
              doris
            </div>
            <div class="code-name">.lu-icon-Doris
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-rds"></span>
            <div class="name">
              rds-mysql
            </div>
            <div class="code-name">.lu-icon-rds
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-polardb"></span>
            <div class="name">
              polardb
            </div>
            <div class="code-name">.lu-icon-polardb
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-presto"></span>
            <div class="name">
              presto
            </div>
            <div class="code-name">.lu-icon-presto
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-index"></span>
            <div class="name">
              index
            </div>
            <div class="code-name">.lu-icon-index
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-primarykey"></span>
            <div class="name">
              primarykey
            </div>
            <div class="code-name">.lu-icon-primarykey
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-indexgroup"></span>
            <div class="name">
              indexgroup
            </div>
            <div class="code-name">.lu-icon-indexgroup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-fieldgroup"></span>
            <div class="name">
              fieldgroup
            </div>
            <div class="code-name">.lu-icon-fieldgroup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-table1"></span>
            <div class="name">
              tablegroup
            </div>
            <div class="code-name">.lu-icon-table1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-viewgroup"></span>
            <div class="name">
              viewgroup
            </div>
            <div class="code-name">.lu-icon-viewgroup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-function2"></span>
            <div class="name">
              function2
            </div>
            <div class="code-name">.lu-icon-function2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-functiongroup"></span>
            <div class="name">
              functiongroup
            </div>
            <div class="code-name">.lu-icon-functiongroup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-view1"></span>
            <div class="name">
              view1
            </div>
            <div class="code-name">.lu-icon-view1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-schema1"></span>
            <div class="name">
              schema
            </div>
            <div class="code-name">.lu-icon-schema1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-triggergroup"></span>
            <div class="name">
              triggergroup
            </div>
            <div class="code-name">.lu-icon-triggergroup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-ID"></span>
            <div class="name">
              ID
            </div>
            <div class="code-name">.lu-icon-ID
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-apacherocketmq"></span>
            <div class="name">
              apacherocketmq
            </div>
            <div class="code-name">.lu-icon-apacherocketmq
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-amazon"></span>
            <div class="name">
              amazon
            </div>
            <div class="code-name">.lu-icon-amazon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-dameng"></span>
            <div class="name">
              dameng
            </div>
            <div class="code-name">.lu-icon-dameng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-filter1"></span>
            <div class="name">
              filter
            </div>
            <div class="code-name">.lu-icon-filter1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-unusual"></span>
            <div class="name">
              unusual
            </div>
            <div class="code-name">.lu-icon-unusual
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-notice"></span>
            <div class="name">
              notice
            </div>
            <div class="code-name">.lu-icon-notice
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-nopass"></span>
            <div class="name">
              nopass
            </div>
            <div class="code-name">.lu-icon-nopass
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-unknown"></span>
            <div class="name">
              unknown
            </div>
            <div class="code-name">.lu-icon-unknown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-pass1"></span>
            <div class="name">
              pass
            </div>
            <div class="code-name">.lu-icon-pass1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-download"></span>
            <div class="name">
              download
            </div>
            <div class="code-name">.lu-icon-download
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-confirm"></span>
            <div class="name">
              confirm
            </div>
            <div class="code-name">.lu-icon-confirm
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-code"></span>
            <div class="name">
              code
            </div>
            <div class="code-name">.lu-icon-code
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-upload"></span>
            <div class="name">
              upload
            </div>
            <div class="code-name">.lu-icon-upload
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-warning1"></span>
            <div class="name">
              warning1
            </div>
            <div class="code-name">.lu-icon-warning1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-day"></span>
            <div class="name">
              day
            </div>
            <div class="code-name">.lu-icon-day
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-database"></span>
            <div class="name">
              database
            </div>
            <div class="code-name">.lu-icon-database
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-night"></span>
            <div class="name">
              night
            </div>
            <div class="code-name">.lu-icon-night
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-alarm"></span>
            <div class="name">
              alarm
            </div>
            <div class="code-name">.lu-icon-alarm
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-ring"></span>
            <div class="name">
              ring
            </div>
            <div class="code-name">.lu-icon-ring
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-impala1"></span>
            <div class="name">
              impala
            </div>
            <div class="code-name">.lu-icon-impala1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-hive"></span>
            <div class="name">
              hive
            </div>
            <div class="code-name">.lu-icon-hive
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-DBA"></span>
            <div class="name">
              dba
            </div>
            <div class="code-name">.lu-icon-DBA
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-developer"></span>
            <div class="name">
              developer
            </div>
            <div class="code-name">.lu-icon-developer
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-if"></span>
            <div class="name">
              if
            </div>
            <div class="code-name">.lu-icon-if
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-do"></span>
            <div class="name">
              do
            </div>
            <div class="code-name">.lu-icon-do
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-admin"></span>
            <div class="name">
              admin
            </div>
            <div class="code-name">.lu-icon-admin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-enable"></span>
            <div class="name">
              enable
            </div>
            <div class="code-name">.lu-icon-enable
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-kingbase"></span>
            <div class="name">
              kingbase
            </div>
            <div class="code-name">.lu-icon-kingbase
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-hudi"></span>
            <div class="name">
              hudi
            </div>
            <div class="code-name">.lu-icon-hudi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-viewlist"></span>
            <div class="name">
              viewlist
            </div>
            <div class="code-name">.lu-icon-viewlist
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-viewgrid"></span>
            <div class="name">
              viewgrid
            </div>
            <div class="code-name">.lu-icon-viewgrid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-java"></span>
            <div class="name">
              java
            </div>
            <div class="code-name">.lu-icon-java
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-sql"></span>
            <div class="name">
              sql
            </div>
            <div class="code-name">.lu-icon-sql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-xml"></span>
            <div class="name">
              xml
            </div>
            <div class="code-name">.lu-icon-xml
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-annotation"></span>
            <div class="name">
              annotation
            </div>
            <div class="code-name">.lu-icon-annotation
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-mybatis"></span>
            <div class="name">
              mybatis
            </div>
            <div class="code-name">.lu-icon-mybatis
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-clean"></span>
            <div class="name">
              clean
            </div>
            <div class="code-name">.lu-icon-clean
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-storage"></span>
            <div class="name">
              storage
            </div>
            <div class="code-name">.lu-icon-storage
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-dbreview"></span>
            <div class="name">
              dbreview
            </div>
            <div class="code-name">.lu-icon-dbreview
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-codereview"></span>
            <div class="name">
              codereview
            </div>
            <div class="code-name">.lu-icon-codereview
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-log"></span>
            <div class="name">
              log
            </div>
            <div class="code-name">.lu-icon-log
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-whitelist"></span>
            <div class="name">
              whitelist
            </div>
            <div class="code-name">.lu-icon-whitelist
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-config1"></span>
            <div class="name">
              config
            </div>
            <div class="code-name">.lu-icon-config1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-PC"></span>
            <div class="name">
              PC
            </div>
            <div class="code-name">.lu-icon-PC
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-order"></span>
            <div class="name">
              order
            </div>
            <div class="code-name">.lu-icon-order
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-rule"></span>
            <div class="name">
              rule
            </div>
            <div class="code-name">.lu-icon-rule
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-user1"></span>
            <div class="name">
              user
            </div>
            <div class="code-name">.lu-icon-user1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-apps"></span>
            <div class="name">
              apps
            </div>
            <div class="code-name">.lu-icon-apps
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-qreview"></span>
            <div class="name">
              qreview
            </div>
            <div class="code-name">.lu-icon-qreview
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-sqledit"></span>
            <div class="name">
              sqledit
            </div>
            <div class="code-name">.lu-icon-sqledit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-opengauss"></span>
            <div class="name">
              opengauss
            </div>
            <div class="code-name">.lu-icon-opengauss
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-data-sensitive"></span>
            <div class="name">
              data-sensitive
            </div>
            <div class="code-name">.lu-icon-data-sensitive
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-gaussdb"></span>
            <div class="name">
              gaussdb
            </div>
            <div class="code-name">.lu-icon-gaussdb
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-mapping"></span>
            <div class="name">
              mapping
            </div>
            <div class="code-name">.lu-icon-mapping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-function1"></span>
            <div class="name">
              function1
            </div>
            <div class="code-name">.lu-icon-function1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-router"></span>
            <div class="name">
              router
            </div>
            <div class="code-name">.lu-icon-router
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-store"></span>
            <div class="name">
              store
            </div>
            <div class="code-name">.lu-icon-store
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-gbase"></span>
            <div class="name">
              gbase
            </div>
            <div class="code-name">.lu-icon-gbase
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-gbase1"></span>
            <div class="name">
              GBASE
            </div>
            <div class="code-name">.lu-icon-gbase1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-impala"></span>
            <div class="name">
              impala
            </div>
            <div class="code-name">.lu-icon-impala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-capacity"></span>
            <div class="name">
              capacity
            </div>
            <div class="code-name">.lu-icon-capacity
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-batch"></span>
            <div class="name">
              batch
            </div>
            <div class="code-name">.lu-icon-batch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-operation"></span>
            <div class="name">
              operation
            </div>
            <div class="code-name">.lu-icon-operation
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-safe"></span>
            <div class="name">
              safe
            </div>
            <div class="code-name">.lu-icon-safe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-applyfor1"></span>
            <div class="name">
              applyfor1
            </div>
            <div class="code-name">.lu-icon-applyfor1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-seal"></span>
            <div class="name">
              seal
            </div>
            <div class="code-name">.lu-icon-seal
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-tdsql-1"></span>
            <div class="name">
              tdsql-1
            </div>
            <div class="code-name">.lu-icon-tdsql-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-sql-server"></span>
            <div class="name">
              sql-server
            </div>
            <div class="code-name">.lu-icon-sql-server
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-scan"></span>
            <div class="name">
              scan
            </div>
            <div class="code-name">.lu-icon-scan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-db2"></span>
            <div class="name">
              db2
            </div>
            <div class="code-name">.lu-icon-db2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-tdsql"></span>
            <div class="name">
              tdsql
            </div>
            <div class="code-name">.lu-icon-tdsql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-oceanbase"></span>
            <div class="name">
              oceanbase
            </div>
            <div class="code-name">.lu-icon-oceanbase
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-loading"></span>
            <div class="name">
              loading
            </div>
            <div class="code-name">.lu-icon-loading
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-app"></span>
            <div class="name">
              app
            </div>
            <div class="code-name">.lu-icon-app
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-disable"></span>
            <div class="name">
              disable
            </div>
            <div class="code-name">.lu-icon-disable
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-sensitive"></span>
            <div class="name">
              sensitive
            </div>
            <div class="code-name">.lu-icon-sensitive
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-impower"></span>
            <div class="name">
              impower
            </div>
            <div class="code-name">.lu-icon-impower
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-applyfor"></span>
            <div class="name">
              applyfor
            </div>
            <div class="code-name">.lu-icon-applyfor
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-person2"></span>
            <div class="name">
              person1
            </div>
            <div class="code-name">.lu-icon-person2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-joint"></span>
            <div class="name">
              joint
            </div>
            <div class="code-name">.lu-icon-joint
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-idea"></span>
            <div class="name">
              idea
            </div>
            <div class="code-name">.lu-icon-idea
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-star"></span>
            <div class="name">
              star
            </div>
            <div class="code-name">.lu-icon-star
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-filter"></span>
            <div class="name">
              Filter
            </div>
            <div class="code-name">.lu-icon-filter
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-bell"></span>
            <div class="name">
              bell
            </div>
            <div class="code-name">.lu-icon-bell
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-cost"></span>
            <div class="name">
              cost
            </div>
            <div class="code-name">.lu-icon-cost
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-rollback"></span>
            <div class="name">
              rollback
            </div>
            <div class="code-name">.lu-icon-rollback
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-pass"></span>
            <div class="name">
              pass
            </div>
            <div class="code-name">.lu-icon-pass
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-exempt"></span>
            <div class="name">
              exempt
            </div>
            <div class="code-name">.lu-icon-exempt
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-details"></span>
            <div class="name">
              details
            </div>
            <div class="code-name">.lu-icon-details
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-tool"></span>
            <div class="name">
              Maintain
            </div>
            <div class="code-name">.lu-icon-tool
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-roam"></span>
            <div class="name">
              roam
            </div>
            <div class="code-name">.lu-icon-roam
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-import"></span>
            <div class="name">
              import
            </div>
            <div class="code-name">.lu-icon-import
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-pull"></span>
            <div class="name">
              pull
            </div>
            <div class="code-name">.lu-icon-pull
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-robot"></span>
            <div class="name">
              robot
            </div>
            <div class="code-name">.lu-icon-robot
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-person1"></span>
            <div class="name">
              person1
            </div>
            <div class="code-name">.lu-icon-person1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-staring"></span>
            <div class="name">
              staring
            </div>
            <div class="code-name">.lu-icon-staring
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-wrong"></span>
            <div class="name">
              wrong
            </div>
            <div class="code-name">.lu-icon-wrong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-right1"></span>
            <div class="name">
              right1
            </div>
            <div class="code-name">.lu-icon-right1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-format"></span>
            <div class="name">
              format
            </div>
            <div class="code-name">.lu-icon-format
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-user-platform"></span>
            <div class="name">
              user-platform
            </div>
            <div class="code-name">.lu-icon-user-platform
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-check"></span>
            <div class="name">
              check
            </div>
            <div class="code-name">.lu-icon-check
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-vs"></span>
            <div class="name">
              vs
            </div>
            <div class="code-name">.lu-icon-vs
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-user-role"></span>
            <div class="name">
              user-role
            </div>
            <div class="code-name">.lu-icon-user-role
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-full"></span>
            <div class="name">
              full
            </div>
            <div class="code-name">.lu-icon-full
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-quit"></span>
            <div class="name">
              quit
            </div>
            <div class="code-name">.lu-icon-quit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-stop"></span>
            <div class="name">
              stop
            </div>
            <div class="code-name">.lu-icon-stop
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-run"></span>
            <div class="name">
              run
            </div>
            <div class="code-name">.lu-icon-run
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-search"></span>
            <div class="name">
              search
            </div>
            <div class="code-name">.lu-icon-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-person"></span>
            <div class="name">
              person
            </div>
            <div class="code-name">.lu-icon-person
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-trigger"></span>
            <div class="name">
              trigger
            </div>
            <div class="code-name">.lu-icon-trigger
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-minus"></span>
            <div class="name">
              minus
            </div>
            <div class="code-name">.lu-icon-minus
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-edit"></span>
            <div class="name">
              edit
            </div>
            <div class="code-name">.lu-icon-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-end"></span>
            <div class="name">
              end
            </div>
            <div class="code-name">.lu-icon-end
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-error"></span>
            <div class="name">
              error
            </div>
            <div class="code-name">.lu-icon-error
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-success"></span>
            <div class="name">
              check
            </div>
            <div class="code-name">.lu-icon-success
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-pending"></span>
            <div class="name">
              pending
            </div>
            <div class="code-name">.lu-icon-pending
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-warning"></span>
            <div class="name">
              warning
            </div>
            <div class="code-name">.lu-icon-warning
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-locknew"></span>
            <div class="name">
              lock new
            </div>
            <div class="code-name">.lu-icon-locknew
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-unlocknew"></span>
            <div class="name">
              unlock
            </div>
            <div class="code-name">.lu-icon-unlocknew
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-sort"></span>
            <div class="name">
              sort
            </div>
            <div class="code-name">.lu-icon-sort
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-business"></span>
            <div class="name">
              business
            </div>
            <div class="code-name">.lu-icon-business
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-copy"></span>
            <div class="name">
              copy
            </div>
            <div class="code-name">.lu-icon-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-ddl"></span>
            <div class="name">
              ddl
            </div>
            <div class="code-name">.lu-icon-ddl
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-dml"></span>
            <div class="name">
              dml
            </div>
            <div class="code-name">.lu-icon-dml
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-delete"></span>
            <div class="name">
              delete
            </div>
            <div class="code-name">.lu-icon-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-editor"></span>
            <div class="name">
              editor
            </div>
            <div class="code-name">.lu-icon-editor
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-function"></span>
            <div class="name">
              function
            </div>
            <div class="code-name">.lu-icon-function
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-field"></span>
            <div class="name">
              field
            </div>
            <div class="code-name">.lu-icon-field
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-exit"></span>
            <div class="name">
              exit
            </div>
            <div class="code-name">.lu-icon-exit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-entity"></span>
            <div class="name">
              entity
            </div>
            <div class="code-name">.lu-icon-entity
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-homenew"></span>
            <div class="name">
              home new
            </div>
            <div class="code-name">.lu-icon-homenew
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-reset"></span>
            <div class="name">
              reset
            </div>
            <div class="code-name">.lu-icon-reset
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-right"></span>
            <div class="name">
              right
            </div>
            <div class="code-name">.lu-icon-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-list"></span>
            <div class="name">
              list
            </div>
            <div class="code-name">.lu-icon-list
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-plusnew"></span>
            <div class="name">
              plus new
            </div>
            <div class="code-name">.lu-icon-plusnew
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-key"></span>
            <div class="name">
              key
            </div>
            <div class="code-name">.lu-icon-key
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-memory"></span>
            <div class="name">
              memory
            </div>
            <div class="code-name">.lu-icon-memory
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-sequence"></span>
            <div class="name">
              sequence
            </div>
            <div class="code-name">.lu-icon-sequence
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-sqlresolver"></span>
            <div class="name">
              sql resolver
            </div>
            <div class="code-name">.lu-icon-sqlresolver
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-usernew"></span>
            <div class="name">
              user new
            </div>
            <div class="code-name">.lu-icon-usernew
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-up"></span>
            <div class="name">
              up
            </div>
            <div class="code-name">.lu-icon-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-view"></span>
            <div class="name">
              view
            </div>
            <div class="code-name">.lu-icon-view
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-system"></span>
            <div class="name">
              system
            </div>
            <div class="code-name">.lu-icon-system
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-task"></span>
            <div class="name">
              task
            </div>
            <div class="code-name">.lu-icon-task
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-schema"></span>
            <div class="name">
              schema
            </div>
            <div class="code-name">.lu-icon-schema
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-rosesql"></span>
            <div class="name">
              rosesql
            </div>
            <div class="code-name">.lu-icon-rosesql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-ubisql"></span>
            <div class="name">
              ubisql
            </div>
            <div class="code-name">.lu-icon-ubisql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-starrocks"></span>
            <div class="name">
              starrocks
            </div>
            <div class="code-name">.lu-icon-starrocks
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-flink"></span>
            <div class="name">
              flink
            </div>
            <div class="code-name">.lu-icon-flink
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-tidb"></span>
            <div class="name">
              tidb
            </div>
            <div class="code-name">.lu-icon-tidb
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-free"></span>
            <div class="name">
              清空缓存
            </div>
            <div class="code-name">.lu-icon-free
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-clickhouse"></span>
            <div class="name">
              clickhouse
            </div>
            <div class="code-name">.lu-icon-clickhouse
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-kafka"></span>
            <div class="name">
              kafka
            </div>
            <div class="code-name">.lu-icon-kafka
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-hbase"></span>
            <div class="name">
              HBASE
            </div>
            <div class="code-name">.lu-icon-hbase
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-elasticsearch"></span>
            <div class="name">
              elasticserch
            </div>
            <div class="code-name">.lu-icon-elasticsearch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-oracle"></span>
            <div class="name">
              Oracle
            </div>
            <div class="code-name">.lu-icon-oracle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-lock"></span>
            <div class="name">
              lock
            </div>
            <div class="code-name">.lu-icon-lock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-partition"></span>
            <div class="name">
              partition
            </div>
            <div class="code-name">.lu-icon-partition
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-table"></span>
            <div class="name">
              table
            </div>
            <div class="code-name">.lu-icon-table
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-mysql"></span>
            <div class="name">
              mysql
            </div>
            <div class="code-name">.lu-icon-mysql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-pgsql"></span>
            <div class="name">
              pgsql
            </div>
            <div class="code-name">.lu-icon-pgsql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-unlock"></span>
            <div class="name">
              unlock
            </div>
            <div class="code-name">.lu-icon-unlock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-extend-info"></span>
            <div class="name">
              extend-info
            </div>
            <div class="code-name">.lu-icon-extend-info
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-data-change"></span>
            <div class="name">
              data-change
            </div>
            <div class="code-name">.lu-icon-data-change
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-user"></span>
            <div class="name">
              user
            </div>
            <div class="code-name">.lu-icon-user
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-refresh"></span>
            <div class="name">
              refresh
            </div>
            <div class="code-name">.lu-icon-refresh
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-config"></span>
            <div class="name">
              config
            </div>
            <div class="code-name">.lu-icon-config
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-publish"></span>
            <div class="name">
              publish
            </div>
            <div class="code-name">.lu-icon-publish
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-expand"></span>
            <div class="name">
              expand
            </div>
            <div class="code-name">.lu-icon-expand
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-metadata"></span>
            <div class="name">
              metadata
            </div>
            <div class="code-name">.lu-icon-metadata
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-change-order"></span>
            <div class="name">
              change-order
            </div>
            <div class="code-name">.lu-icon-change-order
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-base-info"></span>
            <div class="name">
              base-info
            </div>
            <div class="code-name">.lu-icon-base-info
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon lu-icon-databus"></span>
            <div class="name">
              databus
            </div>
            <div class="code-name">.lu-icon-databus
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="luicon lu-icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            luicon" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-realtimesql"></use>
                </svg>
                <div class="name">realtimesql</div>
                <div class="code-name">#lu-icon-realtimesql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-topsql"></use>
                </svg>
                <div class="name">topsql</div>
                <div class="code-name">#lu-icon-topsql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-awspgsql"></use>
                </svg>
                <div class="name">awspgsql</div>
                <div class="code-name">#lu-icon-awspgsql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-awsmysql"></use>
                </svg>
                <div class="name">awsmysql</div>
                <div class="code-name">#lu-icon-awsmysql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-mogdb"></use>
                </svg>
                <div class="name">mogdb</div>
                <div class="code-name">#lu-icon-mogdb</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-re-review"></use>
                </svg>
                <div class="name">re-review</div>
                <div class="code-name">#lu-icon-re-review</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-orderdown"></use>
                </svg>
                <div class="name">orderdown</div>
                <div class="code-name">#lu-icon-orderdown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-orderup"></use>
                </svg>
                <div class="name">orderup</div>
                <div class="code-name">#lu-icon-orderup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-goldendb"></use>
                </svg>
                <div class="name">goldendb</div>
                <div class="code-name">#lu-icon-goldendb</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-node"></use>
                </svg>
                <div class="name">node</div>
                <div class="code-name">#lu-icon-node</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-loading1"></use>
                </svg>
                <div class="name">loading1</div>
                <div class="code-name">#lu-icon-loading1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-folder"></use>
                </svg>
                <div class="name">folder</div>
                <div class="code-name">#lu-icon-folder</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-cpu"></use>
                </svg>
                <div class="name">cpu</div>
                <div class="code-name">#lu-icon-cpu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-release-copy"></use>
                </svg>
                <div class="name">release</div>
                <div class="code-name">#lu-icon-release-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-memo"></use>
                </svg>
                <div class="name">memo</div>
                <div class="code-name">#lu-icon-memo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-Doris"></use>
                </svg>
                <div class="name">doris</div>
                <div class="code-name">#lu-icon-Doris</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-rds"></use>
                </svg>
                <div class="name">rds-mysql</div>
                <div class="code-name">#lu-icon-rds</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-polardb"></use>
                </svg>
                <div class="name">polardb</div>
                <div class="code-name">#lu-icon-polardb</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-presto"></use>
                </svg>
                <div class="name">presto</div>
                <div class="code-name">#lu-icon-presto</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-index"></use>
                </svg>
                <div class="name">index</div>
                <div class="code-name">#lu-icon-index</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-primarykey"></use>
                </svg>
                <div class="name">primarykey</div>
                <div class="code-name">#lu-icon-primarykey</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-indexgroup"></use>
                </svg>
                <div class="name">indexgroup</div>
                <div class="code-name">#lu-icon-indexgroup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-fieldgroup"></use>
                </svg>
                <div class="name">fieldgroup</div>
                <div class="code-name">#lu-icon-fieldgroup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-table1"></use>
                </svg>
                <div class="name">tablegroup</div>
                <div class="code-name">#lu-icon-table1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-viewgroup"></use>
                </svg>
                <div class="name">viewgroup</div>
                <div class="code-name">#lu-icon-viewgroup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-function2"></use>
                </svg>
                <div class="name">function2</div>
                <div class="code-name">#lu-icon-function2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-functiongroup"></use>
                </svg>
                <div class="name">functiongroup</div>
                <div class="code-name">#lu-icon-functiongroup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-view1"></use>
                </svg>
                <div class="name">view1</div>
                <div class="code-name">#lu-icon-view1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-schema1"></use>
                </svg>
                <div class="name">schema</div>
                <div class="code-name">#lu-icon-schema1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-triggergroup"></use>
                </svg>
                <div class="name">triggergroup</div>
                <div class="code-name">#lu-icon-triggergroup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ID"></use>
                </svg>
                <div class="name">ID</div>
                <div class="code-name">#lu-icon-ID</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-apacherocketmq"></use>
                </svg>
                <div class="name">apacherocketmq</div>
                <div class="code-name">#lu-icon-apacherocketmq</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-amazon"></use>
                </svg>
                <div class="name">amazon</div>
                <div class="code-name">#lu-icon-amazon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-dameng"></use>
                </svg>
                <div class="name">dameng</div>
                <div class="code-name">#lu-icon-dameng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-filter1"></use>
                </svg>
                <div class="name">filter</div>
                <div class="code-name">#lu-icon-filter1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-unusual"></use>
                </svg>
                <div class="name">unusual</div>
                <div class="code-name">#lu-icon-unusual</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-notice"></use>
                </svg>
                <div class="name">notice</div>
                <div class="code-name">#lu-icon-notice</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-nopass"></use>
                </svg>
                <div class="name">nopass</div>
                <div class="code-name">#lu-icon-nopass</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-unknown"></use>
                </svg>
                <div class="name">unknown</div>
                <div class="code-name">#lu-icon-unknown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-pass1"></use>
                </svg>
                <div class="name">pass</div>
                <div class="code-name">#lu-icon-pass1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-download"></use>
                </svg>
                <div class="name">download</div>
                <div class="code-name">#lu-icon-download</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-confirm"></use>
                </svg>
                <div class="name">confirm</div>
                <div class="code-name">#lu-icon-confirm</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-code"></use>
                </svg>
                <div class="name">code</div>
                <div class="code-name">#lu-icon-code</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-upload"></use>
                </svg>
                <div class="name">upload</div>
                <div class="code-name">#lu-icon-upload</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-warning1"></use>
                </svg>
                <div class="name">warning1</div>
                <div class="code-name">#lu-icon-warning1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-day"></use>
                </svg>
                <div class="name">day</div>
                <div class="code-name">#lu-icon-day</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-database"></use>
                </svg>
                <div class="name">database</div>
                <div class="code-name">#lu-icon-database</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-night"></use>
                </svg>
                <div class="name">night</div>
                <div class="code-name">#lu-icon-night</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-alarm"></use>
                </svg>
                <div class="name">alarm</div>
                <div class="code-name">#lu-icon-alarm</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ring"></use>
                </svg>
                <div class="name">ring</div>
                <div class="code-name">#lu-icon-ring</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-impala1"></use>
                </svg>
                <div class="name">impala</div>
                <div class="code-name">#lu-icon-impala1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-hive"></use>
                </svg>
                <div class="name">hive</div>
                <div class="code-name">#lu-icon-hive</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-DBA"></use>
                </svg>
                <div class="name">dba</div>
                <div class="code-name">#lu-icon-DBA</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-developer"></use>
                </svg>
                <div class="name">developer</div>
                <div class="code-name">#lu-icon-developer</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-if"></use>
                </svg>
                <div class="name">if</div>
                <div class="code-name">#lu-icon-if</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-do"></use>
                </svg>
                <div class="name">do</div>
                <div class="code-name">#lu-icon-do</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-admin"></use>
                </svg>
                <div class="name">admin</div>
                <div class="code-name">#lu-icon-admin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-enable"></use>
                </svg>
                <div class="name">enable</div>
                <div class="code-name">#lu-icon-enable</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-kingbase"></use>
                </svg>
                <div class="name">kingbase</div>
                <div class="code-name">#lu-icon-kingbase</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-hudi"></use>
                </svg>
                <div class="name">hudi</div>
                <div class="code-name">#lu-icon-hudi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-viewlist"></use>
                </svg>
                <div class="name">viewlist</div>
                <div class="code-name">#lu-icon-viewlist</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-viewgrid"></use>
                </svg>
                <div class="name">viewgrid</div>
                <div class="code-name">#lu-icon-viewgrid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-java"></use>
                </svg>
                <div class="name">java</div>
                <div class="code-name">#lu-icon-java</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-sql"></use>
                </svg>
                <div class="name">sql</div>
                <div class="code-name">#lu-icon-sql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-xml"></use>
                </svg>
                <div class="name">xml</div>
                <div class="code-name">#lu-icon-xml</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-annotation"></use>
                </svg>
                <div class="name">annotation</div>
                <div class="code-name">#lu-icon-annotation</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-mybatis"></use>
                </svg>
                <div class="name">mybatis</div>
                <div class="code-name">#lu-icon-mybatis</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-clean"></use>
                </svg>
                <div class="name">clean</div>
                <div class="code-name">#lu-icon-clean</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-storage"></use>
                </svg>
                <div class="name">storage</div>
                <div class="code-name">#lu-icon-storage</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-dbreview"></use>
                </svg>
                <div class="name">dbreview</div>
                <div class="code-name">#lu-icon-dbreview</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-codereview"></use>
                </svg>
                <div class="name">codereview</div>
                <div class="code-name">#lu-icon-codereview</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-log"></use>
                </svg>
                <div class="name">log</div>
                <div class="code-name">#lu-icon-log</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-whitelist"></use>
                </svg>
                <div class="name">whitelist</div>
                <div class="code-name">#lu-icon-whitelist</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-config1"></use>
                </svg>
                <div class="name">config</div>
                <div class="code-name">#lu-icon-config1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-PC"></use>
                </svg>
                <div class="name">PC</div>
                <div class="code-name">#lu-icon-PC</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-order"></use>
                </svg>
                <div class="name">order</div>
                <div class="code-name">#lu-icon-order</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-rule"></use>
                </svg>
                <div class="name">rule</div>
                <div class="code-name">#lu-icon-rule</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-user1"></use>
                </svg>
                <div class="name">user</div>
                <div class="code-name">#lu-icon-user1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-apps"></use>
                </svg>
                <div class="name">apps</div>
                <div class="code-name">#lu-icon-apps</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-qreview"></use>
                </svg>
                <div class="name">qreview</div>
                <div class="code-name">#lu-icon-qreview</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-sqledit"></use>
                </svg>
                <div class="name">sqledit</div>
                <div class="code-name">#lu-icon-sqledit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-opengauss"></use>
                </svg>
                <div class="name">opengauss</div>
                <div class="code-name">#lu-icon-opengauss</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-data-sensitive"></use>
                </svg>
                <div class="name">data-sensitive</div>
                <div class="code-name">#lu-icon-data-sensitive</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-gaussdb"></use>
                </svg>
                <div class="name">gaussdb</div>
                <div class="code-name">#lu-icon-gaussdb</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-mapping"></use>
                </svg>
                <div class="name">mapping</div>
                <div class="code-name">#lu-icon-mapping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-function1"></use>
                </svg>
                <div class="name">function1</div>
                <div class="code-name">#lu-icon-function1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-router"></use>
                </svg>
                <div class="name">router</div>
                <div class="code-name">#lu-icon-router</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-store"></use>
                </svg>
                <div class="name">store</div>
                <div class="code-name">#lu-icon-store</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-gbase"></use>
                </svg>
                <div class="name">gbase</div>
                <div class="code-name">#lu-icon-gbase</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-gbase1"></use>
                </svg>
                <div class="name">GBASE</div>
                <div class="code-name">#lu-icon-gbase1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-impala"></use>
                </svg>
                <div class="name">impala</div>
                <div class="code-name">#lu-icon-impala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-capacity"></use>
                </svg>
                <div class="name">capacity</div>
                <div class="code-name">#lu-icon-capacity</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-batch"></use>
                </svg>
                <div class="name">batch</div>
                <div class="code-name">#lu-icon-batch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-operation"></use>
                </svg>
                <div class="name">operation</div>
                <div class="code-name">#lu-icon-operation</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-safe"></use>
                </svg>
                <div class="name">safe</div>
                <div class="code-name">#lu-icon-safe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-applyfor1"></use>
                </svg>
                <div class="name">applyfor1</div>
                <div class="code-name">#lu-icon-applyfor1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-seal"></use>
                </svg>
                <div class="name">seal</div>
                <div class="code-name">#lu-icon-seal</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-tdsql-1"></use>
                </svg>
                <div class="name">tdsql-1</div>
                <div class="code-name">#lu-icon-tdsql-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-sql-server"></use>
                </svg>
                <div class="name">sql-server</div>
                <div class="code-name">#lu-icon-sql-server</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-scan"></use>
                </svg>
                <div class="name">scan</div>
                <div class="code-name">#lu-icon-scan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-db2"></use>
                </svg>
                <div class="name">db2</div>
                <div class="code-name">#lu-icon-db2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-tdsql"></use>
                </svg>
                <div class="name">tdsql</div>
                <div class="code-name">#lu-icon-tdsql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-oceanbase"></use>
                </svg>
                <div class="name">oceanbase</div>
                <div class="code-name">#lu-icon-oceanbase</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-loading"></use>
                </svg>
                <div class="name">loading</div>
                <div class="code-name">#lu-icon-loading</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-app"></use>
                </svg>
                <div class="name">app</div>
                <div class="code-name">#lu-icon-app</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-disable"></use>
                </svg>
                <div class="name">disable</div>
                <div class="code-name">#lu-icon-disable</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-sensitive"></use>
                </svg>
                <div class="name">sensitive</div>
                <div class="code-name">#lu-icon-sensitive</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-impower"></use>
                </svg>
                <div class="name">impower</div>
                <div class="code-name">#lu-icon-impower</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-applyfor"></use>
                </svg>
                <div class="name">applyfor</div>
                <div class="code-name">#lu-icon-applyfor</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-person2"></use>
                </svg>
                <div class="name">person1</div>
                <div class="code-name">#lu-icon-person2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-joint"></use>
                </svg>
                <div class="name">joint</div>
                <div class="code-name">#lu-icon-joint</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-idea"></use>
                </svg>
                <div class="name">idea</div>
                <div class="code-name">#lu-icon-idea</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-star"></use>
                </svg>
                <div class="name">star</div>
                <div class="code-name">#lu-icon-star</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-filter"></use>
                </svg>
                <div class="name">Filter</div>
                <div class="code-name">#lu-icon-filter</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-bell"></use>
                </svg>
                <div class="name">bell</div>
                <div class="code-name">#lu-icon-bell</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-cost"></use>
                </svg>
                <div class="name">cost</div>
                <div class="code-name">#lu-icon-cost</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-rollback"></use>
                </svg>
                <div class="name">rollback</div>
                <div class="code-name">#lu-icon-rollback</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-pass"></use>
                </svg>
                <div class="name">pass</div>
                <div class="code-name">#lu-icon-pass</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-exempt"></use>
                </svg>
                <div class="name">exempt</div>
                <div class="code-name">#lu-icon-exempt</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-details"></use>
                </svg>
                <div class="name">details</div>
                <div class="code-name">#lu-icon-details</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-tool"></use>
                </svg>
                <div class="name">Maintain</div>
                <div class="code-name">#lu-icon-tool</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-roam"></use>
                </svg>
                <div class="name">roam</div>
                <div class="code-name">#lu-icon-roam</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-import"></use>
                </svg>
                <div class="name">import</div>
                <div class="code-name">#lu-icon-import</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-pull"></use>
                </svg>
                <div class="name">pull</div>
                <div class="code-name">#lu-icon-pull</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-robot"></use>
                </svg>
                <div class="name">robot</div>
                <div class="code-name">#lu-icon-robot</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-person1"></use>
                </svg>
                <div class="name">person1</div>
                <div class="code-name">#lu-icon-person1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-staring"></use>
                </svg>
                <div class="name">staring</div>
                <div class="code-name">#lu-icon-staring</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-wrong"></use>
                </svg>
                <div class="name">wrong</div>
                <div class="code-name">#lu-icon-wrong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-right1"></use>
                </svg>
                <div class="name">right1</div>
                <div class="code-name">#lu-icon-right1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-format"></use>
                </svg>
                <div class="name">format</div>
                <div class="code-name">#lu-icon-format</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-user-platform"></use>
                </svg>
                <div class="name">user-platform</div>
                <div class="code-name">#lu-icon-user-platform</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-check"></use>
                </svg>
                <div class="name">check</div>
                <div class="code-name">#lu-icon-check</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-vs"></use>
                </svg>
                <div class="name">vs</div>
                <div class="code-name">#lu-icon-vs</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-user-role"></use>
                </svg>
                <div class="name">user-role</div>
                <div class="code-name">#lu-icon-user-role</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-full"></use>
                </svg>
                <div class="name">full</div>
                <div class="code-name">#lu-icon-full</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-quit"></use>
                </svg>
                <div class="name">quit</div>
                <div class="code-name">#lu-icon-quit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-stop"></use>
                </svg>
                <div class="name">stop</div>
                <div class="code-name">#lu-icon-stop</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-run"></use>
                </svg>
                <div class="name">run</div>
                <div class="code-name">#lu-icon-run</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-search"></use>
                </svg>
                <div class="name">search</div>
                <div class="code-name">#lu-icon-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-person"></use>
                </svg>
                <div class="name">person</div>
                <div class="code-name">#lu-icon-person</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-trigger"></use>
                </svg>
                <div class="name">trigger</div>
                <div class="code-name">#lu-icon-trigger</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-minus"></use>
                </svg>
                <div class="name">minus</div>
                <div class="code-name">#lu-icon-minus</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-edit"></use>
                </svg>
                <div class="name">edit</div>
                <div class="code-name">#lu-icon-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-end"></use>
                </svg>
                <div class="name">end</div>
                <div class="code-name">#lu-icon-end</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-error"></use>
                </svg>
                <div class="name">error</div>
                <div class="code-name">#lu-icon-error</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-success"></use>
                </svg>
                <div class="name">check</div>
                <div class="code-name">#lu-icon-success</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-pending"></use>
                </svg>
                <div class="name">pending</div>
                <div class="code-name">#lu-icon-pending</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-warning"></use>
                </svg>
                <div class="name">warning</div>
                <div class="code-name">#lu-icon-warning</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-locknew"></use>
                </svg>
                <div class="name">lock new</div>
                <div class="code-name">#lu-icon-locknew</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-unlocknew"></use>
                </svg>
                <div class="name">unlock</div>
                <div class="code-name">#lu-icon-unlocknew</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-sort"></use>
                </svg>
                <div class="name">sort</div>
                <div class="code-name">#lu-icon-sort</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-business"></use>
                </svg>
                <div class="name">business</div>
                <div class="code-name">#lu-icon-business</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-copy"></use>
                </svg>
                <div class="name">copy</div>
                <div class="code-name">#lu-icon-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ddl"></use>
                </svg>
                <div class="name">ddl</div>
                <div class="code-name">#lu-icon-ddl</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-dml"></use>
                </svg>
                <div class="name">dml</div>
                <div class="code-name">#lu-icon-dml</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-delete"></use>
                </svg>
                <div class="name">delete</div>
                <div class="code-name">#lu-icon-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-editor"></use>
                </svg>
                <div class="name">editor</div>
                <div class="code-name">#lu-icon-editor</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-function"></use>
                </svg>
                <div class="name">function</div>
                <div class="code-name">#lu-icon-function</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-field"></use>
                </svg>
                <div class="name">field</div>
                <div class="code-name">#lu-icon-field</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-exit"></use>
                </svg>
                <div class="name">exit</div>
                <div class="code-name">#lu-icon-exit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-entity"></use>
                </svg>
                <div class="name">entity</div>
                <div class="code-name">#lu-icon-entity</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-homenew"></use>
                </svg>
                <div class="name">home new</div>
                <div class="code-name">#lu-icon-homenew</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-reset"></use>
                </svg>
                <div class="name">reset</div>
                <div class="code-name">#lu-icon-reset</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-right"></use>
                </svg>
                <div class="name">right</div>
                <div class="code-name">#lu-icon-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-list"></use>
                </svg>
                <div class="name">list</div>
                <div class="code-name">#lu-icon-list</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-plusnew"></use>
                </svg>
                <div class="name">plus new</div>
                <div class="code-name">#lu-icon-plusnew</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-key"></use>
                </svg>
                <div class="name">key</div>
                <div class="code-name">#lu-icon-key</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-memory"></use>
                </svg>
                <div class="name">memory</div>
                <div class="code-name">#lu-icon-memory</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-sequence"></use>
                </svg>
                <div class="name">sequence</div>
                <div class="code-name">#lu-icon-sequence</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-sqlresolver"></use>
                </svg>
                <div class="name">sql resolver</div>
                <div class="code-name">#lu-icon-sqlresolver</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-usernew"></use>
                </svg>
                <div class="name">user new</div>
                <div class="code-name">#lu-icon-usernew</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-up"></use>
                </svg>
                <div class="name">up</div>
                <div class="code-name">#lu-icon-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-view"></use>
                </svg>
                <div class="name">view</div>
                <div class="code-name">#lu-icon-view</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-system"></use>
                </svg>
                <div class="name">system</div>
                <div class="code-name">#lu-icon-system</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-task"></use>
                </svg>
                <div class="name">task</div>
                <div class="code-name">#lu-icon-task</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-schema"></use>
                </svg>
                <div class="name">schema</div>
                <div class="code-name">#lu-icon-schema</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-rosesql"></use>
                </svg>
                <div class="name">rosesql</div>
                <div class="code-name">#lu-icon-rosesql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ubisql"></use>
                </svg>
                <div class="name">ubisql</div>
                <div class="code-name">#lu-icon-ubisql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-starrocks"></use>
                </svg>
                <div class="name">starrocks</div>
                <div class="code-name">#lu-icon-starrocks</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-flink"></use>
                </svg>
                <div class="name">flink</div>
                <div class="code-name">#lu-icon-flink</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-tidb"></use>
                </svg>
                <div class="name">tidb</div>
                <div class="code-name">#lu-icon-tidb</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-free"></use>
                </svg>
                <div class="name">清空缓存</div>
                <div class="code-name">#lu-icon-free</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-clickhouse"></use>
                </svg>
                <div class="name">clickhouse</div>
                <div class="code-name">#lu-icon-clickhouse</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-kafka"></use>
                </svg>
                <div class="name">kafka</div>
                <div class="code-name">#lu-icon-kafka</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-hbase"></use>
                </svg>
                <div class="name">HBASE</div>
                <div class="code-name">#lu-icon-hbase</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-elasticsearch"></use>
                </svg>
                <div class="name">elasticserch</div>
                <div class="code-name">#lu-icon-elasticsearch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-oracle"></use>
                </svg>
                <div class="name">Oracle</div>
                <div class="code-name">#lu-icon-oracle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-lock"></use>
                </svg>
                <div class="name">lock</div>
                <div class="code-name">#lu-icon-lock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-partition"></use>
                </svg>
                <div class="name">partition</div>
                <div class="code-name">#lu-icon-partition</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-table"></use>
                </svg>
                <div class="name">table</div>
                <div class="code-name">#lu-icon-table</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-mysql"></use>
                </svg>
                <div class="name">mysql</div>
                <div class="code-name">#lu-icon-mysql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-pgsql"></use>
                </svg>
                <div class="name">pgsql</div>
                <div class="code-name">#lu-icon-pgsql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-unlock"></use>
                </svg>
                <div class="name">unlock</div>
                <div class="code-name">#lu-icon-unlock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-extend-info"></use>
                </svg>
                <div class="name">extend-info</div>
                <div class="code-name">#lu-icon-extend-info</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-data-change"></use>
                </svg>
                <div class="name">data-change</div>
                <div class="code-name">#lu-icon-data-change</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-user"></use>
                </svg>
                <div class="name">user</div>
                <div class="code-name">#lu-icon-user</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-refresh"></use>
                </svg>
                <div class="name">refresh</div>
                <div class="code-name">#lu-icon-refresh</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-config"></use>
                </svg>
                <div class="name">config</div>
                <div class="code-name">#lu-icon-config</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-publish"></use>
                </svg>
                <div class="name">publish</div>
                <div class="code-name">#lu-icon-publish</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-expand"></use>
                </svg>
                <div class="name">expand</div>
                <div class="code-name">#lu-icon-expand</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-metadata"></use>
                </svg>
                <div class="name">metadata</div>
                <div class="code-name">#lu-icon-metadata</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-change-order"></use>
                </svg>
                <div class="name">change-order</div>
                <div class="code-name">#lu-icon-change-order</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-base-info"></use>
                </svg>
                <div class="name">base-info</div>
                <div class="code-name">#lu-icon-base-info</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-databus"></use>
                </svg>
                <div class="name">databus</div>
                <div class="code-name">#lu-icon-databus</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
