<template>
  <SplitPanel
    :class="className"
    v-bind="panelProps"
    v-on="panelListeners"
    :distribute="splitRate === 0 || splitRate === 1 ? 0.5 : splitRate"
  >
    <template v-slot:first>
      <slot name="first"></slot>
    </template>
    <template v-slot:second>
      <slot name="second"></slot>
    </template>
  </SplitPanel>
</template>

<script>
import SplitPanel from 'vue-page-split';

export default {
  props: {
    firstInitValue: {
      type: Number,
      default: 0
    },
    secondInitValue: {
      type: Number,
      default: 0
    }
  },
  components: { SplitPanel },
  data() {
    return {
      splitRate: this.$attrs.distribute
    };
  },
  computed: {
    panelProps() {
      return { ...this.$attrs };
    },
    panelListeners() {
      return { ...this.$listeners };
    },
    className() {
      const { splitRate } = this;
      const { hasLineTip } = this.$attrs;
      return [
        'custom-split-panel',
        splitRate === 0 && 'hide-1st-panel',
        splitRate === 1 && 'hide-2nd-panel',
        hasLineTip === false && 'hideLineTip'
      ];
    }
  },
  destroyed() {},
  mounted() {
    this.setInitValue();
  },
  methods: {
    setInitValue() {
      const { firstInitValue, secondInitValue } = this;
      const { isVertical, lineThickness = 6 } = this.$attrs;
      const totalWidth = this.$el.offsetWidth;
      const totalHeight = this.$el.offsetHeight;
      const totalValue = isVertical ? totalWidth : totalHeight;

      if (firstInitValue > 0 && !secondInitValue) {
        this.splitRate = firstInitValue / (totalValue - lineThickness);
      }
      if (secondInitValue > 0 && !firstInitValue) {
        this.splitRate = 1 - secondInitValue / (totalValue - lineThickness);
      }
    }
  },
  watch: {
    '$attrs.distribute'(newVal, oldVal) {
      this.splitRate = newVal;
    }
  }
};
</script>

<style lang="less">
.custom-split-panel {
  > .resize-line {
    // background-color: #fafafa !important;
    background-color: #f9f9f9 !important;
    > .tip > .line {
      background-color: #d9d9d9 !important;
    }

    &:hover {
      // background-color: #e8e8e8 !important;
      background-color: #f9f9f9 !important;
    }
    &.resize-vertical {
      cursor: col-resize !important;

      > .tip {
        width: 100% !important;
      }
    }
    &.resize-horizontal {
      cursor: row-resize !important;

      > .tip {
        height: 100% !important;
      }
    }
  }

  &.hide-1st-panel {
    > .pane-1st {
      display: none;
    }

    > .resize-line {
      display: none;
    }

    > .pane-2nd {
      width: 100% !important;
    }
  }

  &.hide-2nd-panel {
    > .pane-1st {
      width: 100% !important;
    }

    > .resize-line {
      display: none;
    }

    > .pane-2nd {
      display: none;
    }
  }
  &.hideLineTip {
    > .resize-line {
      > .tip {
        display: none;
      }
    }
  }
}
</style>
