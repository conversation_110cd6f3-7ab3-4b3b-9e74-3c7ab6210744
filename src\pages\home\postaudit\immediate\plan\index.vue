<template>
  <div>
    <div class="review-header">
      <div>
        <a-button class="review-detail-toback-btn" @click="toBack">返回</a-button>
        <a-button :disabled="dataInfo.index === 1" @click="onPrev">
          <a-icon type="double-left" />prev
        </a-button>
        <a-button
          :disabled="dataInfo.count === dataInfo.index"
          style="margin-left: 8px"
          @click="onNext"
        >
          next
          <a-icon type="double-right" />
        </a-button>
        <span class="pageInfo">{{ dataInfo.index }}/{{ dataInfo.count }}</span>
      </div>
    </div>
    <!-- AI判定结果 -->
    <aiResult :dataInfo="dataInfo"></aiResult>
    <!-- 优化建议 -->
    <a-card v-if="sqlPlanInfo.length > 0" type="small" class="a-card">
      <div slot="title" class="title">
        <a-icon type="like" />
        <span style="margin-left: 4px;">优化建议</span>
      </div>
      <div class="ai-comment-part" v-for="(item, index) in sqlPlanInfo" :key="index">
        <div style="color: #0f78fb;">{{ item.message }}</div>
        <span style="color: #0f78fb;">{{ item.sql }}</span>
      </div>
    </a-card>
    <!-- SQL执行指标 -->
    <a-card type="small" class="sql-text-card a-card body-12">
      <div class="title" slot="title">
        <a-icon type="solution" />
        <span style="margin-left: 4px">SQL执行指标</span>
      </div>
      <div>
        <div class="common-content system-info">
          <div>
            <span>开始时间：</span>
            {{ moment(systemInfo.INSERT_TIMESTAMP).format('YYYY-MM-DD HH:mm:ss')}}
          </div>
          <div>
            <span>执行的次数：</span>
            {{ systemInfo.NUM_EXECUTIONS }}
          </div>
          <div>
            <span>总时长：</span>
            {{ systemInfo.STMT_EXEC_TIME }}ms
          </div>
          <div>
            <span>SQL执行时长：</span>
            {{ systemInfo.TOTAL_ACT_TIME }}ms
          </div>
          <div>
            <span>CPU总消耗：</span>
            {{ systemInfo.TOTAL_CPU_TIME }}ms
          </div>
          <div>
            <span>直接读：</span>
            {{ systemInfo.DIRECT_READS }}
          </div>
          <div>
            <span>直接写：</span>
            {{ systemInfo.DIRECT_WRITES }}
          </div>
          <div>
            <span>锁等待时间：</span>
            {{ systemInfo.LOCK_WAIT_TIME }}ms
          </div>
          <div>
            <span>返回总行数：</span>
            {{ systemInfo.ROWS_READ }}
          </div>
          <div>
            <span>逻辑读：</span>
            {{ systemInfo.POOL_DATA_L_READS }}
          </div>
          <div>
            <span>物理读：</span>
            {{ systemInfo.POOL_DATA_P_READS }}
          </div>
          <div>
            <span>索引逻辑读：</span>
            {{ systemInfo.POOL_INDEX_L_READS }}
          </div>
          <div>
            <span>索引物理读：</span>
            {{ systemInfo.POOL_INDEX_P_READS }}
          </div>
        </div>
      </div>
    </a-card>
    <!-- SQL文本结果 -->
    <a-card type="small" class="a-card">
      <div slot="title">
        <a-icon type="edit" />
        <span style="margin-left: 4px">SQL文本</span>
      </div>
      <sql-highlight v-if="dataInfo.sql_text" :sql="dataInfo.sql_text"></sql-highlight>
      <div class="ai-comment-part" v-else>
        <span>暂无数据</span>
      </div>
    </a-card>
    <!--  执行计划 -->
    <a-card type="small" class="a-card">
      <div class="title" slot="title">
        <custom-icon type="lu-icon-table" />
        <span style="margin-left: 4px">执行计划</span>
      </div>
      <div v-if="dataInfo.plan">
        <pre>{{ dataInfo.plan }}</pre>
      </div>
      <div v-else>
        <span>暂无数据</span>
      </div>
    </a-card>
  </div>
</template>

<script>
import SqlHighlight from '@/components/SqlHighlight';
import reviewHeader from './compnents/reviewHeader/index';
import aiResult from './compnents/aiResult/index';
import { getCheckDetail } from '@/api/immediate';
import { sqlAdviceInfo } from '@/api/home';
import common from '@/utils/common';
import moment from 'moment';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    SqlHighlight,
    reviewHeader,
    aiResult
  },
  data() {
    this.ai_status = this.$route.query.ai_status || '';
    this.start_time = this.$route.query.start_time || '';
    this.end_time = this.$route.query.end_time || '';
    this.activeKey = this.$route.query.activeKey || '';
    this.click_time = this.$route.query.click_time || '';
    return {
      loading: false,
      // AI判定结果
      dataInfo: {
        ai_comment: [],
        rule_category: [] // 风险等级数据
      },
      // 顶部数据
      headerInfo: {},
      info: {
        rewrite_sql: '',
        sql_plan: ''
      },
      searchData: {}, // 上一页搜索参数
      isWhite: true,
      idList: [],
      currentNum: 0,
      isInfo: false,
      coderParams: {
        height: '500',
        options: {
          theme: 'default',
          readOnly: true
        },
        formatOptions: {
          keywordCase: 'upper'
        },
        needFormat: true
      },
      id: this.$route.query.id,
      aiCmoment: [],
      systemInfo: {},
      data_source_id: null,
      db_type: null,
      name: null,
      env: null,
      sqlPlanInfo: []
    };
  },
  created() {
    // document.body.style.minWidth = '1366px';
  },
  destroyed() {
    // document.body.style.minWidth = '1024px';
  },
  mounted() {
    this.getCheckDetail({
      db2_detail_id: this.id,
      ai_status: this.ai_status,
      start_time: this.start_time,
      end_time: this.end_time,
      click_time: this.click_time || ''
    });
  },
  methods: {
    moment,
    getCheckDetail(params) {
      this.$showLoading();
      getCheckDetail(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            this.dataInfo = resData;
            this.chartList = resData.chart_list || [];
            this.systemInfo = resData.system_info || [];
            this.id = resData.id;
            this.data_source_id = resData.data_source_id;
            this.db_type = resData.db_type;
            this.name = resData.name;
            this.env = resData.env;
            this.setNavi();
            this.$hideLoading({ duration: 0 });
            if (this.dataInfo.detail_id) {
              this.getSqlAdviceInfoData(this.dataInfo.detail_id);
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // prev
    onPrev() {
      this.getCheckDetail({
        db2_detail_id: this.id,
        paging: 'prev',
        ai_status: this.ai_status,
        click_time: this.click_time,
        start_time: this.start_time,
        end_time: this.end_time
      });
    },
    // next
    onNext() {
      this.getCheckDetail({
        db2_detail_id: this.id,
        paging: 'next',
        ai_status: this.ai_status,
        click_time: this.click_time,
        start_time: this.start_time,
        end_time: this.end_time
      });
    },
    // 返回
    toBack() {
      this.$router.push({
        name: 'immediateReport',
        query: {
          id: this.data_source_id,
          db_type: this.db_type,
          name: this.name,
          env: this.env,
          activeKey: this.activeKey
        }
      });
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'immediateReport') {
          path =
            sourcePath +
            '?id=' +
            this.data_source_id +
            '&db_type=' +
            this.db_type +
            '&name=' +
            this.name +
            '&env=' +
            this.env +
            '&activeKey=' +
            this.activeKey;
        }
        return path;
      });

      // if (this.id !== this.$route.query.id) {
      //   this.$router.push({
      //     name: 'immediateReportPlan',
      //     query: { id: this.id }
      //   });
      // }
    },
    // 优化建议
    getSqlAdviceInfoData(id) {
      sqlAdviceInfo({ id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            this.sqlPlanInfo = res.data.data || [];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.a-card {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
  border-radius: 8px;
  margin-bottom: 24px;
}
.sql_index {
  width: 100%;
  font-size: 12px;
  li {
    list-style: none;
    height: 30px;
    line-height: 30px;
    width: 30%;
    text-align: center;
    display: inline-block;
    span {
      width: 140px;
      display: inline-block;
    }
  }
}
.common-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  div {
    width: 25%;
    &:nth-child(n + 5) {
      margin-top: 10px;
    }
  }

  &.system-info {
    > div {
      color: rgba(0, 0, 0, 0.6);
      > span {
        color: #000000;
      }
    }
  }
}
.review-detail-toback-btn {
  margin-right: 8px;
}
.review-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .card-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    .title-project {
      margin-left: 40px;
    }
    .title-project-name {
      display: inline-block;
      padding: 0 24px;
      color: @primary-color;
      font-size: 12px;
      border: 1px solid @primary-color;
      border-radius: 24px;
      line-height: 24px;
      margin-left: 12px;
      &::before {
        content: '';
        display: inline-block;
        position: relative;
        left: -8px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: @primary-color;
      }
    }
  }
  .pageInfo {
    margin-left: 16px;
  }
}
.review-header,
.ant-spin-container > * {
  background: #fff;
  // padding: 16px 24px;
  margin-bottom: 16px;
  .ant-card {
    .card-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      .title-project-name {
        display: inline-block;
        padding: 0 24px;
        color: @primary-color;
        font-size: 12px;
        border: 1px solid @primary-color;
        border-radius: 24px;
        line-height: 24px;
        margin-left: 8px;
        &::before {
          content: '';
          display: inline-block;
          position: relative;
          left: -8px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: @primary-color;
        }
      }
    }
  }
}
</style>
