<!--
 * @Descripttion: select
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2020-12-05 09:53:35
-->
<template>
  <a-select
    v-bind:value="value"
    @change="onChange"
  >
  <a-select-option
    v-for="item in dataList"
    v-bind:key="item.value"
    :value="item.value"
  >
    {{ item.label }}
  </a-select-option>
  </a-select>
</template>

<script>
export default {
  name: 'FSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: String,
    dataSource: {
      type: [Array, Function],
      required: false,
      default: () => []
    }
  },
  data (vm) {
    return {
      dataList: Array.isArray(vm.dataSource) ? vm.dataSource : []
    }
  },
  mounted () {
    if (typeof this.dataSource === 'function') {
      let promise = this.dataSource()
      if (Array.isArray(promise)) {
        this.dataList = promise
      } else {
        promise.then((it) => {
          if (Array.isArray(it)) {
            this.dataList = it
          }
        })
      }
    }
  },
  methods: {
    onChange (value) {
      this.$emit('change', value)
    }
  }
}
</script>
