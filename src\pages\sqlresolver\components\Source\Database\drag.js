class Draggable {
  constructor(options = {}) {
    this.options = options;
    this.data = {};
    this.init();
  }

  init() {
    document.addEventListener('mousedown', this.start = (e) => this.onStart(e));
    document.addEventListener('mousemove', this.move = (e) => this.onMove(e));
    document.addEventListener('mouseup', this.end = (e) => this.onEnd(e));
  }

  onStart(e) {
    if (this.flag) {
      this.startPos = {
        x: e.pageX,
        y: e.pageY
      };

      const { onStart } = this.options;
      onStart && onStart(e);
    }
  }

  onMove(e) {
    if (this.flag) {
      if (Math.abs(e.pageX - this.startPos.x) < 5 && Math.abs(e.pageY - this.startPos.y) < 5) {
        // 移动距离过小
        return;
      }

      const { onMove } = this.options;
      onMove && onMove(e);
      e.preventDefault();
    }
  }

  onEnd(e) {
    if (this.flag) {
      this.flag = false;

      const { onEnd } = this.options;
      onEnd && onEnd(e, this.data);
    }
  }

  destroy() {
    document.removeEventListener('mousedown', this.start);
    document.removeEventListener('mousemove', this.move);
    document.removeEventListener('mouseup', this.end);
  }

  setData(data) {
    this.flag = true;
    this.data = data;
  }
}
export default Draggable;