<template>
  <div class="config-datasource-database-tablelist">
    <Table ref="table" v-bind="tableParams || {}" @selectChange="selectChange" class="new-view-table small-size">
      <!-- table插槽 -->
      <div slot="name" slot-scope="{ record, text }" class="datasource-name">
        <Tag type="Env" :text="record.env.toUpperCase()" />
        <DbImg :value="record.db_type" :schemaName="text" :limit="16" />
      </div>
      <template slot="permission_status" slot-scope="{ text, record }">
        <a-switch
          checked-children="开"
          un-checked-children="关"
          :checked="text === 1"
          @change="onChange(record)"
        />
      </template>
      <div slot="datasource_name" slot-scope="{ text, record }" class="datasource-name">
        <custom-icon type="lu-icon-entity" />
        <a style="margin-left: 12px" @click="toInstanceDetail(record)">{{text}}</a>
      </div>
      <div slot="table_count" slot-scope="{ text }" class="table-count">
        <custom-icon type="lu-icon-list" />
        <span style="margin-left: 12px">{{text}}</span>
      </div>
      <custom-btns-wrapper slot="action" slot-scope="{ text, record }" :limit="3">
        <a
          @click="toTableList(record)"
          :disabled="typeof(record.table_count) == 'string'"
          actionBtn
        >表列表</a>
        <!-- <a
          v-if="!isTaiLong"
          @click="authManager(record)"
          :disabled="record.permission_status !== 1"
          actionBtn
        >权限管理</a> -->
      </custom-btns-wrapper>
    </Table>
    <!-- 项目抽屉 -->
    <AuthManager :DropdownSearchList="DropdownSearchList" :activeKey="activeKey" ref="authManager"></AuthManager>
    <BatchAuthDrawer :activeKey="activeKey" ref="batchAuth" @refresh="reset"></BatchAuthDrawer>
    <!-- 实例详情抽屉 -->
    <InstanceDetail ref="instanceDetail"></InstanceDetail>
    <!-- 数据库同步抽屉 -->
    <DatabaseSync ref="databaseSync"></DatabaseSync>
  </div>
</template>

<script>
import {
  addDataSource,
  editDataSource,
  schemaPermissionSwitch
} from '@/api/config/dataSource';
import Table from '@/components/Table';
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import DatabaseSync from './components/databaseSync';
import AuthManager from './components/authManager';
import BatchAuthDrawer from './components/batchAuthDrawer';
import InstanceDetail from './components/instanceDetail';
import config from './config';
import { Base64 } from 'js-base64';
import _ from 'lodash';
// import common from '@/utils/common';

export default {
  components: {
    Tag,
    DbImg,
    Table,
    AuthManager,
    BatchAuthDrawer,
    InstanceDetail,
    DatabaseSync
  },
  props: {
    activeKey: String,
    datasourceName: String
  },
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: '/sqlreview/project/schema_list',
        reqParams: {},
        columns: this.config.databaseColumns,
        rowKey: 'id',
        // isInitReq: false,
        // needCache: true,
        needTools: true,
        needSearchArea: true,
        searchFields: this.config.databaseSearchFields,
        scroll: { x: 'max-content' },
        rowSelection: {
          type: 'checkbox', // 多选单选
          columnWidth: '50',
          onSelectAll: this.onSelectAll,
          getCheckboxProps: record => ({
            // 选择框的默认属性配置
            props: {
              disabled: record.permission_status == 0 // 自由操作不需要选中
            }
          })
        }
      },
      isSelectAll: false,
      selectedRowKeys: [],
      DropdownSearchList: [
        {
          key: 'user_type',
          title: '用户类型',
          default: '全部',
          options: [
            { label: '全部', value: -1 },
            { label: '用户', value: 0 },
            {
              label: '用户组',
              value: 1
            }
          ]
        },
        {
          key: 'permission_type',
          title: '权限类型',
          default: '全部',
          options: [
            { label: '全部', value: -1 },
            { label: '查询', value: 0 },
            {
              label: 'DDL',
              value: 1
            },
            {
              label: 'DML',
              value: 2
            }
          ]
        }
      ]
    };
  },
  computed: {
    // 泰隆银行特有
    isTaiLong() {
      const specificConfig = process.channel === 'TaiLongBank';
      return specificConfig;
    }
  },
  mounted() {},
  created() {},
  methods: {
    // 搜索
    search() {},
    // 重置
    reset() {
      const { table } = this.$refs;
      table.refreshClear();
      this.selectedRowKeys = [];
    },
    // 权限控制 启用禁用
    onChange(data) {
      this.$showLoading();
      schemaPermissionSwitch({
        value: data.permission_status === 0 ? 1 : 0,
        id: data.id,
        type: 'schema'
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const { table } = this.$refs;
            table.refreshKeep(null, { _clear: true });
            this.selectedRowKeys = [];
            // table.refreshClear();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 权限管理
    authManager(record) {
      this.$refs.authManager.show(record, null, 'database');
    },
    batchAuthShow() {
      if (!this.selectedRowKeys || this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择数据库');
        return;
      }
      this.$refs.batchAuth.show(
        this.selectedRowKeys,
        this.isSelectAll,
        'database'
      );
    },
    // 表格全选
    onSelectAll(e) {
      this.isSelectAll = e;
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    // 跳转到表结构
    toTableList(data = {}) {
      this.$router.push({
        name: 'table-detail',
        query: { id: data.id }
      });
    },
    // 打开实例详情抽屉
    toInstanceDetail(record) {
      this.$refs.instanceDetail.show(record);
    },
    // 项目保存
    saveProject(params = {}) {
      const { editModal, table } = this.$refs;
      const { type, data } = params;
      const reqMethod = type === 'add' ? addDataSource : editDataSource;
      // 请求
      this.$showLoading();
      reqMethod({
        url_type: data.type,
        ...data,
        password: Base64.encode(data.password || '')
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            editModal.hide();
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 数据库同步
    databaseSyncFn() {
      const { databaseSync } = this.$refs;
      databaseSync.show();
    }
  },
  watch: {
    datasourceName: {
      handler(newVal) {
        this.$set(this.tableParams, 'reqParams', { datasource_name: newVal });
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.table-count,
.datasource-name {
  display: flex;
  align-items: center;
  .ant-tag {
    border-radius: 6px;
    color: #fff !important;
  }
}
</style>
