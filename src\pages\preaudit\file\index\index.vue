<template>
  <div class="file-review-content">
    <!-- 页面概述 -->
    <div class="header">
      <div :class="['summary', isCollapse && 'collapse']" @click="onCollapse">
        <div class="title">
          <span><custom-icon type="file-search"></custom-icon>文件审核</span>
          <div>
            <a-input-search
              placeholder="搜索任务名称/ID"
              @click="(e) => e.stopPropagation()"
              @search="onSearch"
              v-if="isCollapse"
            ></a-input-search>
            <span class="search" v-if="isCollapse">高级查询</span>
            <custom-icon :type="isCollapse ? 'down' : 'up'"></custom-icon>
          </div>
        </div>
        <div class="des" v-if="!isCollapse">
          该项目审核菜单提供文件上传审核功能。 允许用户上传多种类型的文件，包括
          .zip代码包、XML代码文件、SQL文件和存储过程文件。
          系统从用户上传的文件中解析并提取SQL，并根据用户是否指定数据源进行在线或离线审核。
          在指定数据源时，系统将对提取的SQL进行语法语义审核、执行计划审核；在未指定数据源时，仅进行SQL语法和语义审核。
        </div>
        <div class="supports" v-if="!isCollapse">
          <span>功能支持</span>
          <span
            >代码框架：.xml文件、.sql 文件
            <a-tooltip>
              <template slot="title"
                >(可执行SQL脚本、存储过程、函数、包)、zip代码文件(与代码审核功能支持的代码框架一致)</template
              >
              <a-icon type="exclamation-circle" />
            </a-tooltip>
          </span>
          <span>SQL类型：DML、DDL、DQL</span>
          <span
            >离线/在线支持：在线|离线
            <a-tooltip>
              <template slot="title">(在线支持选择项目、数据源)</template>
              <a-icon type="exclamation-circle" />
            </a-tooltip>
          </span>
        </div>
      </div>
      <!-- 搜索内容 -->
      <div
        :class="['search-content', isCollapse && 'collapse']"
        v-show="!isCollapse"
      >
        <!-- <div class="title" @click="onCollapse">
          <span>高级查询</span>
          <custom-icon :type="isCollapse ? 'down' : 'up'"></custom-icon>
        </div> -->
        <SearchArea
          layout="up-down"
          v-bind="searchParams || {}"
          @reset="reset"
          @search="search"
          ref="search"
          searchMode="switch"
          :iconCombine="false"
          :isShowExpandIcon="false"
          :searchData="searchData || {}"
        ></SearchArea>
      </div>
    </div>
    <PageList :mode="'single'">
      <div class="page-list-single">
        <SwitchTable
          ref="switchTable"
          v-bind="tableParams"
          :cardColumns="cardColumns"
          :listColumns="listColumns"
        >
          <template slot="tableTopLeft">
            <div>任务列表</div>
          </template>
          <template slot="tableTopRight">
            <a-button
              slot="extra"
              class="new-button"
              icon="plus"
              type="primary"
              @click="addProject"
              >新建</a-button
            >
            <div class="sql-count">
              <a-tooltip>
                <template slot="title"> SQL总数不为0 </template>
                <a-switch
                  size="small"
                  @change="onChange"
                  :checked="sql_count"
                />
              </a-tooltip>
            </div>
            <div class="switch-view">
              <a-tooltip>
                <template slot="title"> 切换视图 </template>
                <custom-icon
                  type="lu-icon-viewlist"
                  @click="switchView"
                ></custom-icon>
              </a-tooltip>
            </div>
          </template>
          <!-- 卡片模式 -->
          <CardTable
            slot="cardTable"
            slot-scope="{ record }"
            v-bind="{ cardData: record }"
            @showReport="showReport(record)"
            @toDetail="toDetail(record)"
            @remove="remove(record)"
            @terminate="terminate(record)"
            @refresh="refresh"
          ></CardTable>
          <!-- 列表模式 -->
          <template slot="review_name" slot-scope="{ text, record }">
            <a @click="toDetail(record, $event)">{{ text }}</a>
          </template>
          <div slot="review_point" slot-scope="{ text }" class="review-point">
            <LimitTags
              :tags="
                text ? text.split(',').map((item) => ({ label: item })) : []
              "
              :limit="1"
              mode="numTag"
            ></LimitTags>
          </div>
          <template slot="status" slot-scope="{ text, record }">
            <div class="status">
              <a-tooltip v-if="record.status == '9' && record.error_message">
                <template slot="title">
                  <span>{{ record.error_message }}</span>
                </template>
                <StatusTag
                  type="review"
                  :status="record.status"
                  fromPath="sqlreview"
                >
                  <a-icon style="marginleft: 4px" type="question-circle" />
                </StatusTag>
              </a-tooltip>
              <StatusTag
                type="review"
                :status="record.status"
                fromPath="sqlreview"
                v-else
              />
              <a-progress
                v-if="[0, 3, 4, 5].includes(record.status)"
                :strokeWidth="8"
                :percent="Number(record.progress)"
                size="small"
              />
            </div>
          </template>
          <template slot="created_by" slot-scope="{ record, text }">
            <a-tooltip v-if="record.ch_creater" placement="topLeft">
              <template slot="title">
                <span>{{ text }}</span>
              </template>
              <div class="des">
                <a-icon type="user" />
                <span>{{ record.ch_creater }}</span>
              </div>
            </a-tooltip>
            <span v-else>{{ text || '--' }}</span>
          </template>
          <!-- 操作列 -->
          <custom-btns-wrapper
            slot="action"
            slot-scope="{ text, record }"
            :limit="3"
          >
            <a @click="showReport(record)" actionBtn>报表</a>
            <a
              v-if="[0, 3, 4, 5].includes(record.status)"
              @click="terminate(record)"
              actionBtn
              >终止</a
            >
            <a-popconfirm
              title="确定移除该数据?"
              @confirm="() => remove(record)"
              v-if="canDo && ![0, 3, 4, 5].includes(record.status)"
            >
              <a actionBtn class="remove">删除</a>
            </a-popconfirm>
          </custom-btns-wrapper>
        </SwitchTable>
        <!-- 新建项目弹窗 -->
        <AddModal ref="addModal" @save="saveProject"></AddModal>
        <!-- 报表抽屉 -->
        <Report ref="Report"></Report>
        <!-- 审核弹窗 -->
        <Audit ref="audit" @refresh="refresh"></Audit>
      </div>
    </PageList>
  </div>
</template>
<script>
import { fileReview, removeRecord, terminationRecord } from '@/api/preaudit';
import LimitTags from '@/components/LimitTags';
import Table from '@/components/Table';
import StatusTag from '@/components/Biz/Status/Tag';
import Status from '@/components/Biz/Status';
import AddModal from './AddModal';
import Report from '../../components/Report';
import Audit from '@/components/Biz/AuditModel';
import CardTable from './CardTable';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import PageList from '@/components/PageListNew/SwitchTable/index.vue';
import SwitchTable from '@/components/PageListNew/SwitchTable/table.vue';
import SearchArea from '@/components/Biz/SearchArea/new';
export default {
  name: 'file-review',
  components: {
    CardTable,
    Table,
    Status,
    AddModal,
    Report,
    Audit,
    LimitTags,
    StatusTag,
    PageList,
    SwitchTable,
    SearchArea
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    this.config = config(this);
    // keep-alive是否激活
    this.activated = null;
    return {
      isCollapse: true,
      isShow: false,
      record: {},
      statusColor: this.config.statusColor,
      searchData: {},
      searchParams: {
        fields: this.config.searchFields
      },
      tableParams: {
        url: '/sqlreview/review/list1',
        reqParams: {
          sql_count: '0',
          type: 2
        },
        method: 'post',
        columns: this.config.cardColumns,
        rowKey: 'id',
        showHeader: false,
        cacheKey: 'file-review',
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' }
      },
      cardColumns: this.config.cardColumns,
      listColumns: this.config.listColumns
    };
  },
  created() {},
  mounted() {},
  activated() {
    if (this.activated === false) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      this.$store.commit('common/setSearchCache', {
        'file-review': { sql_count: searchParams.sql_count }
      });
      this.activated = true;
    }
  },
  deactivated() {
    this.activated = false;
  },
  computed: {
    // sql总数为0是否被选中，读取vuex中保存的缓存数值
    sql_count() {
      const data = this.$store.state.common.searchCache['file-review'];
      return data ? data.sql_count === '1' : false;
    },
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  methods: {
    // 筛选sql是否为0
    onChange(bool) {
      this.$store.commit('common/setSearchCache', {
        'file-review': { sql_count: bool ? '1' : '0' }
      });
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { sql_count: bool ? '1' : '0' });
      this.$set(this.tableParams, 'reqParams', {
        sql_count: bool ? '1' : '0',
        type: 2
      });
      // this.$set(table.searchParams, 'sql_count', bool ? '1' : '0');
    },
    // 切换视图
    switchView() {
      const { switchTable } = this.$refs;
      // console.log('--------------');
      switchTable.switchView();
    },
    // 新建项目
    addProject() {
      const { addModal } = this.$refs;
      addModal.show();
    },
    // 新建项目保存
    saveProject(data) {
      const { addModal, switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const filesList = data.files_list;
      // 请求
      this.$showLoading();
      if (filesList && filesList.length > 0) {
        const submitData = new FormData();
        data.files_list.forEach(item => {
          submitData.append('files_list', item);
        });
        submitData.append('review_name', String(data.review_name));
        submitData.append('audit_type', String(data.audit_type));
        data.db_type && submitData.append('db_type', String(data.db_type));
        submitData.append('rule_set', String(data.rule_set));
        data.schema_list &&
          submitData.append('schema_list', JSON.stringify(data.schema_list));
        fileReview(submitData)
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              addModal.hide();
              table.refresh();
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      }
    },
    toDetail(record, e) {
      this.$router.push({
        name: 'file-review-detail',
        params: { id: record.id }
      });
    },
    showReport(record) {
      this.record = record;
      this.$refs.Report.show(record, 'file');
    },
    // 查询
    search(data) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.onReset();
    },
    refresh() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refreshKeep();
    },
    remove(record) {
      this.$showLoading();
      removeRecord({ id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 终止
    terminate(record) {
      this.$showLoading();
      terminationRecord({ id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onSearch(value, e) {
      e.stopPropagation();
      const data = { id: value };
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    },
    onCollapse() {
      this.isCollapse = !this.isCollapse;
    },
    onShow() {
      this.isShow = !this.isShow;
    }
  }
};
</script>

<style lang="less" scoped>
@assetsUrl: '~@/assets';
.file-review-content {
  .header {
    border-radius: 16px;
    margin-bottom: 16px;
    background-color: #fff;
    .summary {
      background-image: url('@{assetsUrl}/img/private/file-review.svg');
      background-size: 560px 178px;
      background-repeat: no-repeat;
      background-position: right;
      padding: 0 24px 0 24px;
      border-radius: 16px;
      cursor: pointer;
      &.collapse {
        background-image: none;
        background-color: #fff;
        background-size: 0;
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 600;
        padding: 16px 0;
        > span {
          .anticon {
            font-size: 24px;
            margin-right: 12px;
          }
        }
        > div {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          .ant-input-search {
            width: 200px;
          }
          .search {
            font-size: 14px;
            color: #1f1f1f;
            font-weight: 400;
            padding: 0 16px;
            cursor: pointer;
          }
          > .anticon {
            font-size: 14px;
            color: #8c8c8c;
            margin-right: 0;
          }
        }
      }
      .des {
        width: 64%;
        padding-bottom: 16px;
        word-break: break-all;
        font-family: PingFangSC-Regular;
        color: #1f1f1f;
      }
      .supports {
        padding-bottom: 18px;
        span {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #8c8c8c;
          margin-right: 22px;
          &:first-child {
            background: #e6f7ff;
            border: 1px solid rgba(145, 213, 255, 1);
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #1890ff;
            padding: 2px 6px;
            margin-right: 16px;
            border-radius: 4px;
          }
          .anticon {
            color: #1f1f1f;
            font-size: 12px;
            &:hover {
              cursor: pointer;
            }
          }
        }
      }
    }
    .search-content {
      // padding: 18px 24px 0 24px;
      border-top: 1px solid #f0f0f0;
      &.collapse {
        .title {
          border-bottom: none;
        }
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        > .anticon {
          font-size: 14px;
          color: #8c8c8c;
          margin-right: 0;
        }
      }
      /deep/.search-area {
        box-shadow: none;
        border: none;
        // .ant-form {
        //   > .ant-row {
        //     > .ant-col {
        //       width: 25%;
        //       .ant-form-item {
        //         .ant-form-item-label {
        //           width: auto;
        //           min-width: 88px;
        //         }
        //         .ant-form-item-control-wrapper {
        //           width: auto;
        //           min-width: 110px;
        //         }
        //       }
        //     }
        //   }
        // }
      }
    }
  }
  .page-list-switch-wrapper {
    .page-list-single {
      /deep/.custom-table {
        .search-area-wrapper {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 24px;
          .custom-table-top-left {
            > div {
              font-family: PingFangSC-Semibold;
              font-size: 16px;
              color: #1f1f1f;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  .sql-count {
    margin: 0 16px;
    display: flex;
    align-items: center;
    .sql-count-text {
      margin-right: 4px;
    }
  }
  .switch-view {
    font-size: 16px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    &:hover {
      cursor: pointer;
      background: #7adcff;
      color: #ffffff;
    }
  }
  /deep/.status {
    display: flex;
    align-items: center;
    white-space: nowrap;
    .ant-tag {
      width: auto !important;
    }
    .ant-progress {
      margin-left: 8px;
      margin-right: 12px;
      top: -2px;
      width: 84px;
      .ant-progress-outer {
        .ant-progress-inner {
          // width: 32px;
          border-radius: 0;
          .ant-progress-bg {
            border-radius: 0 !important;
          }
        }
      }
    }
  }
  /deep/ .review-point {
    .limit-tags {
      .ant-tag {
        margin-bottom: 0;
      }
    }
  }
}
@media screen and (max-width: 1560px) {
  .file-review-content {
    // .search-content {
    //   /deep/.search-area {
    //     .ant-form {
    //       > .ant-row {
    //         > .ant-col {
    //           width: 33.33%;
    //         }
    //       }
    //     }
    //   }
    // }
    .header {
      .summary {
        background-image: none;
        background-color: #fff;
        .des {
          width: 100%;
        }
      }
    }
  }
}
</style>
