<template>
  <div class="custom-load-more">
    <a-button :disabled="loading" @click="onLoadMore" v-if="!hasLoaded">{{!loading ? '加载更多' : '加载中...'}}</a-button>
    <span class="loaded-tips" v-else>{{loading ? '加载中...' : '已加载完成'}}</span>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    loading: Boolean,
    pageNum: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {};
  },
  computed: {
    hasLoaded() {
      return (
        // !this.loading &&
        this.total === 0 ||
        (this.total > 0 && this.pageSize * this.pageNum >= this.total)
      );
    }
  },
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    onLoadMore() {
      this.$emit('next', this.pageNum + 1);
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.custom-load-more {
  display: flex;
  justify-content: center;
  // margin-top: 12px;
  padding: 12px 0;
  font-size: 14px;

  .ant-btn {
    border-radius: 4px;
    // padding: 0 48px;
    width: 200px;
    &[disabled] {
      background: #fdfdfd !important;
    }
  }
  .loaded-tips {
    color: rgba(0, 0, 0, 0.45);
  }
}
</style>
