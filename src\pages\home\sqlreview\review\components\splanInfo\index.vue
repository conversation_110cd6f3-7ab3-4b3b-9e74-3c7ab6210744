<template>
  <a-card type="small" :bordered="false" class="small-card">
    <div class="enlarge-icon-box">
      <a-icon type="fullscreen" @click="onFullscreen" />
    </div>
    <a-tabs
      :active-key="activeKey"
      @change="activeChange"
      :animated="false"
      :class="[tabsNum == 1 ? 'single' : 'couple']"
    >
      <a-tab-pane key="dbaSuggest" tab="DBA建议" v-if="labelInfo.comment_status !== 0">
        <main v-if="!isShow" class="dba-suggest-box">
          <div class="apply" v-if="labelInfo.label_type">
            <div class="info">
              <custom-icon type="lu-icon-applyfor1" />
              <span>{{labelInfo.created_by ? labelInfo.created_by + '：' : ''}}</span>
              <span v-if="labelInfo.label_type == 1">{{'白名单'}}</span>
              <span v-else-if="labelInfo.label_type == 2">{{'整改中'}}</span>
            </div>
            <span class="time">{{labelInfo.created_at}}</span>
          </div>
          <div
            class="line"
            v-if="[2,3,4,5,8].includes(labelInfo.comment_status) && !!labelInfo.label_type"
          ></div>
          <div class="audit" v-if="[2,3,4,5,8].includes(labelInfo.comment_status)">
            <div class="info">
              <custom-icon
                v-if="[2,4,5,8].includes(labelInfo.comment_status)"
                type="lu-icon-right1"
              />
              <custom-icon v-if="labelInfo.comment_status == 3" type="lu-icon-wrong" />
              <span>{{labelInfo.operator_dba + '：'}}</span>
              <span v-if="[2,4,5,8].includes(labelInfo.comment_status)">{{'同意'}}</span>
              <span v-if="labelInfo.comment_status == 3">{{'不同意'}}</span>
            </div>
            <span class="time">{{labelInfo.updated_at}}</span>
          </div>
          <div class="suggest" v-if="labelInfo.comment_content || labelInfo.dba_comment">
            <div>{{labelInfo.comment_content }}</div>
            <div>{{labelInfo.dba_comment}}</div>
          </div>
          <div
            class="empty"
            v-if="!labelInfo.label_type && ![2,3,4,5,8].includes(labelInfo.comment_status)"
          ></div>
          <div v-if="isDba" class="action-box">
            <a @click="edit">编辑</a>
          </div>
        </main>
        <main v-if="isShow">
          <a-textarea placeholder="请输入建议" class="dba-suggest" :rows="6" v-model="value" />
          <div class="btn-box">
            <a-button @click="onCancel" class="highlight" style="margin-right: 8px">取消</a-button>
            <a-button @click="save" type="primary">保存</a-button>
          </div>
        </main>
      </a-tab-pane>
      <a-tab-pane key="indexSuggest" tab="索引建议" v-if="sqlPlanInfo.length > 0">
        <div v-for="(item, index) in sqlPlanInfo" :key="index">
          <div class="suggest">{{ item.message }}</div>
          <div class="suggest-sql" v-if="item.sql">
            <span>{{ item.sql }}</span>
          </div>
          <div class="index-suggest-btn" v-if="item.sql">
            <custom-icon type="copy" />
            <span @click="onCopy(item.sql)">复制</span>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="optimzationSuggest" tab="优化建议" v-if="sqlSuggest.length > 0">
        <div>
          <a-collapse :bordered="false" expandIconPosition="right">
            <a-collapse-panel
              v-for="(item, index) in sqlSuggest"
              :key="index"
              :header="item.ai_comment"
            >
              <MarkdownViewer class="rich-editor-preview" v-model="item.suggest"></MarkdownViewer>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </a-tab-pane>
    </a-tabs>
    <EnlargeModal
      ref="modal"
      :sqlPlanInfo="sqlPlanInfo"
      :sqlSuggest="sqlSuggest"
      :labelInfo="labelInfo"
    />
  </a-card>
</template>
<script>
import MarkdownViewer from '@/components/Markdown/viewer';
import EnlargeModal from './EnlargeModal';
export default {
  components: { MarkdownViewer, EnlargeModal },
  props: {
    sqlPlanInfo: {
      type: Array,
      default: () => []
    },
    sqlSuggest: {
      type: Array,
      default: () => []
    },
    labelInfo: {
      type: Object,
      default: () => {}
    },
    id: String | Number
  },
  computed: {
    isDba() {
      const user = this.$store.state.account.user || {};
      return user.role === 'dba';
    },
    tabsNum() {
      let num = 0;
      if (this.sqlPlanInfo.length > 0) {
        num += 1;
      }
      if (this.sqlSuggest.length > 0) {
        num += 1;
      }
      if (this.labelInfo.comment_status !== 0) {
        num += 1;
      }
      return num;
    }
  },
  data() {
    return {
      activeKey: null,
      value: '',
      tableParams: {
        url: '',
        reqParams: {},
        columns: [],
        rowKey: 'id'
      },
      isShow: false
    };
  },
  mounted() {},
  updated() {
    this.getActiveKey();
  },
  methods: {
    getActiveKey() {
      if (this.activeKey) {
        return;
      }
      if (this.labelInfo.comment_status !== 0) {
        this.activeKey = 'dbaSuggest';
      } else if (this.sqlPlanInfo.length > 0) {
        this.activeKey = 'indexSuggest';
      } else if (this.sqlSuggest.length > 0) {
        this.activeKey = 'optimzationSuggest';
      }
    },
    activeChange(activeKey) {
      this.activeKey = activeKey;
    },
    onChangeMessage(event) {
      this.$emit('onChangeMessage', event.target.value);
    },
    onCancel() {
      this.value = '';
      this.isShow = !this.isShow;
    },
    edit() {
      this.isShow = !this.isShow;
    },
    save() {
      this.$emit('saveAdvice', this.value);
      this.value = '';
      this.isShow = !this.isShow;
    },
    onCopy(sql) {
      CommonUtil.copy({
        value: sql,
        callback: () => {
          this.$message.success('成功复制sql语句');
        }
      });
    },
    onFullscreen() {
      this.$refs.modal.show();
    }
  },
  watch: {
    // id: {
    //   handler(newVal) {
    //     if (newVal) this.getActiveKey();
    //   },
    //   immediate: true
    // }
  }
};
</script>

<style lang="less" scoped>
.small-card {
  margin-bottom: 24px;
  border-radius: 8px;
  /deep/.ant-card-body {
    padding: 32px;
    .enlarge-icon-box {
      position: absolute;
      top: 6px;
      right: 12px;
      color: #e4e4e7;
      font-size: 20px;
    }
    .ant-tabs {
      .ant-tabs-bar {
        .ant-tabs-nav-container {
          .ant-tabs-nav-wrap {
            .ant-tabs-nav-scroll {
              display: flex;
              justify-content: space-around;
              .ant-tabs-tab {
                font-size: 16px;
                color: #71717a;
                font-weight: 400;
                &.ant-tabs-tab-active {
                  font-size: 16px;
                  color: #27272a;
                  font-weight: 600;
                }
                &:last-child {
                  &::after {
                    display: none;
                  }
                }
              }
              .ant-tabs-ink-bar {
                height: 3px;
                // height: 3px !important;
              }
            }
          }
        }
      }
      &.single {
        .ant-tabs-bar {
          .ant-tabs-nav-container {
            .ant-tabs-nav-wrap {
              .ant-tabs-nav-scroll {
                justify-content: flex-start;
              }
            }
          }
        }
      }
    }
  }
  .suggest {
    font-size: 12px;
    color: #71717a;
    text-align: justify;
    font-weight: 400;
  }
  .suggest-sql {
    padding: 8px 16px;
    background: #f4f5f7;
    margin: 12px 0;
    border-radius: 6px;
    > span {
      font-size: 12px;
      color: #27272a;
      text-align: justify;
    }
  }
  .index-suggest-btn {
    // position: absolute;
    // right: 16px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .anticon {
      font-size: 12px;
      color: #008adc;
      margin-right: 4px;
    }
    span {
      font-size: 13px;
      color: #008adc;
    }
    &:hover {
      cursor: pointer;
    }
  }
  .dba-suggest-box {
    min-height: 100px;
    .apply,
    .audit {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      .info {
        display: flex;
        align-items: center;
        .anticon {
          font-size: 18px;
          color: #000000;
          margin-right: 4px;
        }
        .span {
          font-size: 12px;
          color: #27272a;
          text-align: justify;
          font-weight: 400;
          margin-right: 4px;
        }
      }
    }
    .line {
      height: 1px;
      width: 100%;
      background: #e8e8e8;
    }
    .suggest {
      background: #f4f5f7;
      padding: 8px 16px;
      > div:first-child {
        margin-bottom: 8px;
      }
    }
    .empty {
      background: #f4f5f7;
      height: 100px;
    }
    .action-box {
      display: flex;
      justify-content: flex-end;
      margin-top: 12px;
      > a {
        font-size: 12px;
        color: #008adc;
      }
    }
  }
  .dba-suggest {
    background: #f4f5f7;
    height: 100px;
    &.ant-input:focus {
      border-color: #fff;
      border-right-width: 1px !important;
      outline: 0;
      box-shadow: 0 0 0 0;
    }
    &.ant-input:hover {
      border-color: #fff;
    }
  }
  .btn-box {
    display: flex;
    justify-content: flex-end;
    margin-top: 12px;
    .ant-btn {
      height: 24px;
    }
  }
}

/deep/ textarea.review-advice {
  background-color: rgba(15, 120, 251, 0.06);
  border: 1px solid transparent;
}
/deep/.ant-collapse-borderless {
  overflow: auto;
  max-height: 200px;
  background-color: #fff;
  .ant-collapse-item {
    &:nth-child(1) {
      .ant-collapse-header {
        padding: 0 0 10px 0;
      }
      .anticon {
        top: 10px;
      }
    }
    .ant-collapse-header {
      font-size: 12px;
      color: #27272a;
      font-weight: 600;
      padding: 10px 0;
      .anticon {
        svg {
          transform: rotate(-90deg);
        }
      }
    }
  }
}

@media screen and (max-width: 1400px) {
  .small-card {
    /deep/.ant-card-body {
      .ant-tabs {
        .ant-tabs-bar {
          .ant-tabs-nav-container {
            .ant-tabs-nav-wrap {
              .ant-tabs-nav-scroll {
                display: flex;
                justify-content: flex-start;
              }
            }
          }
        }
      }
    }
  }
}
</style>