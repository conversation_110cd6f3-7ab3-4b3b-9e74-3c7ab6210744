<!--
 * @Author: your name
 * @Date: 2021-01-29 17:56:15
 * @LastEditTime: 2021-02-03 14:26:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/index.vue
-->
<template>
  <div class="home-efficiency-reivew">
    <div class="frame-button-wrapper">
      <a-button
        slot="extra"
        class="button"
        icon="plus"
        type="primary"
        @click="addTimedAudit"
        >创建任务</a-button
      >
    </div>
    <Table ref="table" v-bind="tableParams || {}" class="new-view-table">
      <template slot="task_name" slot-scope="{ record, text }">
        <a @click="toTaskList(record)">{{ text }}</a>
      </template>
      <template slot="project_name" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </template>
      <template slot="project_group" slot-scope="{ text }">
        <span class="project-group" v-if="text && text.length > 0">
          <span>{{ text[0] }}</span>
          <span v-if="text.length > 1">
            <a-tooltip>
              <template slot="title">
                <span>{{ text.toString() }}</span>
              </template>
              <span>...</span>
            </a-tooltip>
          </span>
        </span>
      </template>
      <template slot="review_point" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </template>
      <!-- DBA评审状态 -->
      <template slot="dba_status" slot-scope="{ text, record }">
        <StatusTag type="dba" :status="record.dba_status" />
      </template>
      <template slot="status" slot-scope="{ text, record }">
        <div class="status">
          <Status :status="text" :message="record.error_message"></Status>
        </div>
      </template>
      <template slot="created_by" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_creater" placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{ record.ch_creater }}</span>
          </div>
        </a-tooltip>
        <span v-else>{{ text || '--' }}</span>
      </template>
      <template slot="operator_dba" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_dba" placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{ record.ch_dba }}</span>
          </div>
        </a-tooltip>
        <span v-else>{{ text || '--' }}</span>
      </template>
      <custom-btns-wrapper
        slot="action"
        slot-scope="{ text, record }"
        :limit="3"
      >
        <a @click="toEdit(text, record, $event)" actionBtn>编辑</a>
        <a-popconfirm
          title="确定移除该数据?"
          @confirm="() => remove(record)"
          v-if="canDo"
        >
          <!--  && ![0, 3, 4, 5].includes(record.status) -->
          <a actionBtn>删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </Table>
    <!-- 创建任务弹窗 -->
    <EditTimedAuditModal
      ref="editTimedAuditModal"
      @save="saveTimedAudit"
    ></EditTimedAuditModal>
  </div>
</template>

<script>
import {
  regularTaskCreate,
  regularTaskEdit,
  regularTaskDelete
} from '@/api/extraReview/cqxly';
import LimitTags from '@/components/LimitTags';
import Table from '@/components/Table';
import StatusTag from '@/components/Biz/Status/Tag';
import Status from '@/components/Biz/Status';
import SearchArea from '@/components/SearchArea';
import Report from '@/pages/home/<USER>/Report';
import Audit from '@/components/Biz/AuditModel';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import EditTimedAuditModal from './components/EditTimedAuditModal';
export default {
  name: 'timing-review',
  components: {
    Table,
    Status,
    SearchArea,
    Report,
    Audit,
    LimitTags,
    StatusTag,
    EditTimedAuditModal
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    this.config = config(this);
    return {
      record: {},
      statusColor: this.config.statusColor,
      tableParams: {
        url: '/sqlreview/review/regular_task/list',
        reqParams: {},
        method: 'get',
        columns: this.config.columns,
        rowKey: 'id',
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' }
      }
    };
  },
  created() {},
  mounted() {},
  computed: {
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  methods: {
    // 新建定时审核
    addTimedAudit() {
      const { editTimedAuditModal } = this.$refs;
      editTimedAuditModal.show();
    },
    // 新建编辑项目保存
    saveTimedAudit(data = {}) {
      // 请求
      this.$showLoading();
      let reqFun = data.id ? regularTaskEdit : regularTaskCreate;
      reqFun(data)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const { editTimedAuditModal } = this.$refs;
            editTimedAuditModal.hide();
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    toTaskList(record) {
      this.$router.push({
        name: 'timing-review-tasklist',
        params: { id: record.id }
      });
    },
    toEdit(record, e) {
      const { editTimedAuditModal } = this.$refs;
      editTimedAuditModal.show({
        ...record,
        time_range: record.review_time_range
      });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.refresh();
    },
    refresh() {
      const { table } = this.$refs;
      table.refreshKeep();
    },
    remove(record) {
      this.$showLoading();
      regularTaskDelete({ task_id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.home-efficiency-reivew {
  background: #fff;
  border-radius: 16px;
  /deep/.new-view-table {
    .search-area-wrapper {
      padding: 8px 24px 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f4f4f5;
      .custom-table-top-right {
        align-items: center;
      }
    }
    .sql-count {
      margin-right: 12px;
      display: flex;
      align-items: center;
      .sql-count-text {
        margin-right: 4px;
      }
    }
    .project-group {
      display: flex;
      > span {
        margin-right: 16px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #27272a;
        letter-spacing: 0;
        font-weight: 400;
        text-align: center;
        border: 1px solid #e4e4e7;
        border-radius: 4px;
        padding: 4px 7px;
        white-space: nowrap;
      }
    }
  }
}
</style>
