<template>
  <a-modal v-model="visible" width="600px" wrapClassName="home-sqlreview-list-audit-modal">
    <div slot="title" class="title">
      <custom-icon type="lu-icon-alarm" />
      <span>{{ message || '...' }}</span>
    </div>
    <div class="content">
      <div>
        <div class="des">强制提交</div>
        <div class="sub-des">
          <span>DBA审核通过后，本次可以豁免。下个版本会继续进行扫描。</span>
        </div>
      </div>
      <div>
        <div class="des">添加SQL标签</div>
        <div class="sub-des">
          <span class="color-area">整改中</span>
          <span>标记为整改中状态SQL后续增量版本会重新扫描评估，需要在三个版本周期内整改完成</span>
        </div>
        <div class="sub-des">
          <span class="color-area">白名单</span>
          <span>申请加入白名单需要业务部门领导同意，加入白名单的 SQL，文本不发生变化增量版本不会扫描</span>
        </div>
      </div>
    </div>
    <template slot="footer">
      <a-button class="highlight" @click="onAdd" v-if="isAdd">去添加标签和备注</a-button>
      <a-button class="highlight" @click="onCancel" v-if="!isAdd">取消提交</a-button>
      <a-button type="primary" @click="save">强制提交</a-button>
    </template>
  </a-modal>
</template>
<script>
import config from './config';
import Form from '@/components/Form';
import { getSubmitMessage, sendEmail } from '@/api/home';

export default {
  props: {
    isAdd: {
      type: Boolean,
      default: false
    }
  },
  components: { Form },
  data() {
    this.config = config(this);
    return {
      id: '',
      value: null,
      visible: false,
      spinning: false,
      message: '',
      formData: {},
      params: {
        multiCols: 1,
        clon: true,
        labelCol: { span: 0 },
        wrapperCol: { span: 24 },
        fields: this.config.fields
      }
    };
  },
  methods: {
    show(id) {
      this.spinning = true;
      this.id = id;
      getSubmitMessage({ record_id: id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.message = _.get(res, 'data.data.message');
            const flag = _.get(res, 'data.data.flag');
            this.visible = flag;
            this.spinning = false;
            if (!flag) this.save();
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onCancel() {
      this.visible = false;
    },
    onChange(e) {
      console.log('radio checked', e.target.value);
    },
    save() {
      this.$showLoading();
      sendEmail({
        record_id: this.id,
        confirm: 1
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.$emit('refresh');
            this.onCancel();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onAdd() {
      this.$emit('add');
      this.onCancel();
    }
  },
  watch: {}
};
</script>

<style lang="less">
.home-sqlreview-list-audit-modal {
  .ant-modal-header {
    padding: 32px 40px 12px 40px;
    background: #fff !important;
    .title {
      display: flex;
      font-size: 20px;
      color: #27272a;
      font-weight: 600;
      .anticon {
        color: #e71d36;
        font-size: 20px;
        margin-right: 4px;
        padding-top: 2px;
      }
    }
  }
  .ant-modal-close {
    .ant-modal-close-x {
      color: #27272a;
    }
  }
  .ant-modal-body {
    padding: 0 40px;
    .content {
      > div {
        padding: 16px 24px;
        border-bottom: 1px solid #e8e8e8;
        &:last-child {
          border: none;
          margin-bottom: 24px;
        }
        .des {
          font-size: 14px;
          color: #71717a;
          text-align: justify;
          font-weight: 600;
        }
        .sub-des {
          padding-left: 16px;
          margin-top: 8px;
          > span {
            font-size: 14px;
            color: #a1a1aa;
            text-align: justify;
            font-weight: 400;
          }
          .color-area {
            color: #f29339;
          }
        }
      }
    }
  }
  .ant-modal-footer {
    .ant-btn {
      // width: 104px;
      // height: 36px;
      // background: #ffffff;
      // border: 1px solid #008adc;
      // font-size: 14px;
      // color: #008adc;
      // font-weight: 600;
      // border-radius: 6px;
    }
    .ant-btn-primary {
      // background: #008adc;
      // color: #ffffff;
      // font-weight: 600;
    }
  }
}
</style>
