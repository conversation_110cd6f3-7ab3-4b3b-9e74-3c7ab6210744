<template>
  <div class="import-comp">
    <div class="header-info">
      <div>
        <span
          >导入完成，导入成功{{ successCount }}个{{ confItem.name }}，导入失败{{
            failCount
          }}个{{ confItem.name }}，已存在{{ existsCount }}个{{
            confItem.name
          }}。点击下载错误报告</span
        >
        <a @click="download">{{ erroReport }}</a>
      </div>
      <a-button type="primary" @click="toBack">完成</a-button>
    </div>

    <div class="err-info-table">
      <div class="err-info-table-header">
        <div>错误信息</div>
        <div>
          <InputSearch
            placeholder="请输入错误信息搜索"
            @search="search"
          ></InputSearch>
        </div>
      </div>
      <a-spin :spinning="spinning">
        <Table v-bind="tableParams" :dataSource="dataSource" bordered></Table>
      </a-spin>
    </div>
  </div>
</template>

<script>
import { downloadErrorEeport } from '@/api/config/dataSource';
import config from './config';
import InputSearch from '@/components/InputSearch';
import Table from '@/components/Table';
import common from '@/utils/common';
import { ConfMap } from '../upload/index';

export default {
  components: { Table, InputSearch },
  props: {
    compData: {
      type: Object,
      default: () => {}
    },
    type: String
  },
  data() {
    this.config = config(this);
    return {
      confItem: ConfMap[this.type],
      spinning: false,
      erroReport: '',
      dataSource: [],
      tableParams: {
        url: '',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id'
      },
      allCount: '',
      existsCount: '',
      failCount: '',
      filterData: []
    };
  },
  mounted() {
    this.$emit('submitProgress', 0);
  },
  created() {},
  methods: {
    // 搜索
    search(params) {
      this.spinning = true;
      if (!params) {
        this.dataSource = this.filterData;
        setTimeout(() => {
          this.spinning = false;
        }, 1000);
        return;
      }
      this.dataSource = this.filterData.filter(item => {
        if (item.error_message.includes(params)) {
          return item;
        }
      });
      setTimeout(() => {
        this.spinning = false;
      }, 1000);
    },
    // 返回数据源例表
    toBack() {
      this.$router.push({ name: this.confItem.route });
    },
    // 下载错误模板
    download() {
      this.$showLoading({
        tips: `下载中...`
      });
      downloadErrorEeport({ filename: this.erroReport })
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  },
  watch: {
    compData: {
      handler(newVal) {
        this.filterData = _.get(newVal, 'fail_message.result');
        this.dataSource = _.get(newVal, 'fail_message.result');
        this.erroReport = _.get(newVal, 'error_report');
        this.successCount = _.get(newVal, 'success_count');
        this.existsCount = _.get(newVal, 'exists_count');
        this.failCount = _.get(newVal, 'fail_count');
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.import-comp {
  .header-info {
    padding: 24px 24px 0 24px;
    margin: 24px 12px;
    background: rgb(229, 246, 254);
    > div {
      padding-bottom: 24px;
    }
    > .ant-btn {
      margin-bottom: 24px;
    }
  }
  .err-info-table {
    margin: 24px 12px;
    .err-info-table-header {
      display: flex;
      justify-content: space-between;
    }
    > div {
      padding-bottom: 12px;
      font-size: 16px;
    }
  }
}
</style>
