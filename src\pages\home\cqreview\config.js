export default function (ctx) {
  const statusColor = {
    待评审: '#FC925E',
    评审中: '#4F96FB',
    已通过: '#6EC84A',
    未通过: '#F9676B',
    未提交: '#AFADAD'
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sorter: true,
      width: 100
    },
    {
      title: '效率云卡片号/CQ单号',
      dataIndex: 'ref_id',
      key: 'ref_id',
      scopedSlots: { customRender: 'ref_id' },
      width: 150
    },
    {
      title: '子系统',
      dataIndex: 'project_name',
      key: 'project_name',
      scopedSlots: { customRender: 'project_name' },
      width: 150
    },
    {
      title: '审核单类型',
      dataIndex: 'form_type',
      key: 'form_type',
      scopedSlots: { customRender: 'form_type' },
      width: 150
    },
    {
      title: '文件数量',
      dataIndex: 'file_count',
      key: 'file_count',
      scopedSlots: { customRender: 'file_count' },
      width: 150
    },
    {
      title: 'AIReview',
      key: 'status',
      dataIndex: 'status',
      scopedSlots: { customRender: 'status' },
      width: 150
    },
    {
      title: 'DBA负责人',
      key: 'operator_dba',
      dataIndex: 'operator_dba',
      scopedSlots: { customRender: 'operator_dba' },
      width: 120
    },
    {
      title: 'DBA评审',
      key: 'dba_status',
      dataIndex: 'dba_status',
      scopedSlots: { customRender: 'dba_status' },
      width: 150
    },
    {
      title: 'SQL总数',
      key: 'sql_count',
      dataIndex: 'sql_count',
      sorter: true,
      width: 120
    },
    {
      title: 'AI通过率',
      key: 'passing_rate',
      sorter: true,
      dataIndex: 'passing_rate',
      width: 130
    },
    {
      title: '发起时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 180,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = [
    {
      type: 'Input',
      label: '效率云卡片号/CQ单号',
      key: 'ref_id',
      mainSearch: true,
      props: {
        placeholder: '效率云卡片号/CQ单号'
      }
    },
    {
      type: 'Input',
      label: 'ID',
      key: 'id',
      props: {
        placeholder: '请输入ID'
      }
    },
    {
      type: 'Select',
      label: '子系统',
      key: 'project_id',
      sourceKey: 'project_name',
      props: {
        url: '/sqlreview/project/list-all',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 50
      }
    },
    {
      type: 'Select',
      label: '审核单类型',
      key: 'form_type',
      props: {}
    },
    // {
    //   type: 'RangePicker',
    //   label: '发起时间',
    //   key: 'created_at',
    //   props: {
    //     showTime: {
    //       format: 'HH:mm:ss'
    //     }
    //   }
    // },
    {
      type: 'Select',
      label: 'AIReview状态',
      key: 'status',
      props: {
        options: [
          {
            label: '队列中',
            value: '0'
          },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '未通过',
            value: '-1,2'
          },
          {
            label: 'Review失败',
            value: '9'
          },
          {
            label: '拉取代码中',
            value: '3'
          },
          {
            label: '代码解析中',
            value: '4'
          },
          {
            label: '代码审核中',
            value: '5'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'DBA评审状态',
      key: 'dba_status',
      props: {
        options: [
          {
            label: '未提交',
            value: '0'
          },
          {
            label: '待评审',
            value: '1'
          },
          {
            label: '评审中',
            value: '2'
          },
          {
            label: '已通过',
            value: '3'
          },
          {
            label: '未通过',
            value: '4'
          }
        ]
      }
    }
    // {
    //   type: 'Select',
    //   label: 'DBA负责人',
    //   key: 'dba',
    //   sourceKey: 'operator_dba',
    //   props: {
    //     url: '/sqlreview/review/select-value/',
    //     reqParams: {
    //       which: 'dba'
    //     },
    //     allowSearch: true,
    //     backSearch: true,
    //     limit: 50
    //   }
    // },
  ];
  return {
    columns,
    statusColor,
    searchFields: fields
  };
}
