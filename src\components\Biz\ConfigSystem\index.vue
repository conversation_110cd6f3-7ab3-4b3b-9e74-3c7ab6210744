<template>
  <div class="config-system">
    <a-radio-group v-bind:value="value" @change="onChange">
      <a-radio
        :key="item.element_value"
        :value="item.element_value"
        class="line"
        v-for="item in (data &&
          data.sql_review_dynamic_form_element) ||
        []"
      >
        <div class="line-label">
          {{ item.element_name }}
        </div>
        <div class="line-text">
          {{ item.element_desc }}
        </div>
      </a-radio>
    </a-radio-group>
  </div>
</template>

<script>
export default {
  name: 'ConfigSystem',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: String,
    data: Object
  },
  data() {
    return {};
  },
  computed: {
    id() {
      return this.data.sql_review_dynamic_form_element.filter(x => x.element_value === this.value)[0].id
    }
  },
  created() {},
  mounted() {},
  methods: {
    onChange(e, c) {
      this.$emit('change', e.target.value);
    },
    hide() {}
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.config-system {
  .title {
    font-size: 16px;
    font-weight: 400;
    color: #333;
  }
  .line {
    display: flex;
    align-items: center;
    margin-top: 20px;
    /deep/ span:nth-of-type(2) {
      display: flex;
      align-items: center;
    }
    /deep/ span:nth-of-type(2) div:nth-of-type(1) {
      width: 80px;
      margin-right: 10px;
    }
    .line-text {
      margin-left: 20px;
      color: #7f7f7f;
      display: flex;
    }
  }
}
</style>
