<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="新建审核"
    okText="保存"
    width="580px"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="params" :formData="formData" class="add-form">
      </Form>
    </a-spin>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
const formParams = ctx => {
  return {
    layout: 'horizontal',
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
    fields: [
      {
        type: 'RadioGroup',
        label: '单号类型',
        key: 'ref_type',
        props: {
          options: [
            {
              label: 'ITSM单号',
              value: 1
            },
            {
              label: '任务号',
              value: 2
            }
          ]
        },
        listeners: {
          change: value => {
            ctx.$refs.form.saving({
              review_type: value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '单号ID',
          key: 'ref_id',
          props: {
            url: '/sqlreview/review/select_number',
            reqParams: {
              type: formData.ref_type
            },
            allowSearch: true,
            backSearch: true,
            limit: 10
          },
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ]
        };
      },

      {
        type: 'Select',
        label: '子系统',
        key: 'project_id',
        props: {
          url: '/sqlreview/project/list-all',
          reqParams: {
            review_type: 'none'
          },
          allowSearch: true,
          backSearch: true,
          limit: 50
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      }
    ]
  };
};

export default {
  components: { Form },
  props: {},
  data() {
    return {
      spinning: false,
      visible: false,
      formData: {
        ref_type: 1
      },
      params: formParams(this)
    };
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      this.formData = {
        ref_type: 1
      };
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData();
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .add-form {
  .ant-row .ant-form-item-control-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        width: 150px;
      }
    }
  }
}
</style>
