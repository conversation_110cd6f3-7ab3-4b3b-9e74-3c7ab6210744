<template>
  <!-- 版本信息 -->
  <div class="quater-block small-size version-block">
    <div class="version-info">
      <a-skeleton :loading="loading" active>
        <div class="title">
          版本信息<span class="version-num">{{ version }}</span>
        </div>
        <div class="updata" v-if="updataInfo.length > 0">
          <!-- <div class="title">
            <custom-icon type="info-circle" theme="filled"></custom-icon>更新内容
          </div> -->
          <div class="content" v-for="(item, index) in updataInfo" :key="index">
            <div class="point"></div>
            <LimitLabel
              :label="item || ''"
              :block="true"
              :popoverProps="{ placement: 'top' }"
              mode="ellipsis"
            ></LimitLabel>
          </div>
        </div>
        <Empty v-if="updataInfo.length <= 0" />
      </a-skeleton>
    </div>

    <div class="online">
      <a-skeleton :loading="feedLoading" active>
        <div class="title">在线反馈</div>
        <div
          class="content-box"
          v-if="role !== 'developer' && feedList.length > 0"
        >
          <div class="content" v-for="item in feedList" :key="item.id">
            <a-popover overlayClassName="data-view-version">
              <template slot="content">
                <p>问题描述：{{ item.feed_message }}</p>
                <p>提交人：{{ item.feed_person }}</p>
              </template>
              <div class="feed-message-box">
                <div class="point"></div>
                <div class="feed-message">{{ item.feed_message }}</div>
              </div>
            </a-popover>
            <!-- <div>
              <div class="point"></div>
              <LimitLabel
                :label="item.feed_message || ''"
                :popoverProps="{ placement: 'top' }"
                mode="ellipsis"
              ></LimitLabel>
            </div> -->
            <div class="feed-time">{{ item.feed_time }}</div>
          </div>
        </div>
        <Empty v-if="role !== 'developer' && feedList.length <= 0" />
        <div class="content-box" v-if="role == 'developer'">
          <div class="write">
            <a-textarea
              placeholder="请输入反馈建议"
              :rows="6"
              :value="feedMessage"
              @change="onChangeMessage"
            />
            <div>
              <a-button type="primary" @click="onSubmit" size="small">
                提交
              </a-button>
            </div>
          </div>
        </div>
      </a-skeleton>
    </div>
  </div>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
import Empty from './Empty';
import { addFeedBack } from '@/api/dataview';
export default {
  components: { LimitLabel, Empty },
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    feedLoading: {
      type: Boolean,
      default: true
    },
    updataInfo: {
      type: Array,
      default: () => []
    },
    feedList: {
      type: Array,
      default: () => []
    },
    version: String
  },
  data() {
    let role = this.$store.state.account.user.role;
    return {
      role,
      feedMessage: null
    };
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    onChangeMessage(event) {
      this.feedMessage = event.target.value;
    },
    onSubmit() {
      this.$showLoading();
      addFeedBack({ message: this.feedMessage })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.feedMessage = '';
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.quater-block {
  height: 420px;
  width: calc(25% - 16px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 0;
  margin-right: 0;
  padding: 0;
  background: transparent;
  box-shadow: none;
  box-shadow: none;
  .title {
    font-size: 16px;
    color: #1f1f1f;
    padding-bottom: 16px;
    font-weight: bold;
  }
  .version-info,
  .online {
    height: 48%;
    border-radius: 8px;
    padding: 16px 24px;
    background: #ffffff;
    box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.05);
    .title {
      font-size: 16px;
      color: #1f1f1f;
      padding: 0 0 16px 0;
      .version-num {
        margin-left: 8px;
        font-size: 16px;
        color: #fa541c;
        font-weight: 600;
      }
    }
  }

  .version-info {
    .updata {
      height: 136px;
      overflow-y: auto;
      padding: 0 12px 0 0;
      margin-right: 4px;
      .content {
        padding: 4px 0;
        display: flex;
        align-items: center;
        .point {
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #d9d9d9;
          margin-right: 8px;
          flex-shrink: 0;
        }
        .limit-label {
          pre {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            white-space: nowrap;
            font-family: 'PingFang SC', 'Microsoft YaHei';
          }
        }
      }
    }
  }
  .online {
    .content-box {
      padding: 0 12px 0 0;
      margin-right: 4px;
      height: 136px;
      overflow-y: auto;
      .content {
        padding: 4px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        // > div {
        //   display: flex;
        //   align-items: center;
        //   overflow: hidden;
        //   .point {
        //     width: 6px;
        //     height: 6px;
        //     border-radius: 50%;
        //     background: #d9d9d9;
        //     margin-right: 8px;
        //     flex-shrink: 0;
        //   }
        //   .limit-label {
        //     pre {
        //       font-size: 14px;
        //       color: rgba(0, 0, 0, 0.65);
        //       white-space: nowrap;
        //       font-family: 'PingFang SC', 'Microsoft YaHei';
        //     }
        //   }
        // }
        .feed-message-box {
          display: flex;
          align-items: center;
          overflow: hidden;
          .point {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #d9d9d9;
            margin-right: 8px;
            flex-shrink: 0;
          }
          .feed-message {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
            white-space: nowrap;
            font-family: 'PingFang SC', 'Microsoft YaHei';
          }
        }
        .feed-time {
          flex-shrink: 0;
          margin-left: 12px;
          color: #8c8c8c;
          font-size: 12px;
        }
      }
      .write {
        textarea.ant-input {
          height: 100px;
          margin-bottom: 12px;
        }
        div {
          width: 100%;
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }
}
</style>
<style lang="less">
// popover美化
.ant-popover {
  &.data-view-version {
    .ant-popover-inner {
      .ant-popover-inner-content {
        width: 240px;
        // min-height: 120px;
        > p:first-child {
          font-size: 14px;
          color: #1f1f1f;
        }
        > p:last-child {
          font-size: 14px;
          color: #8c8c8c;
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>