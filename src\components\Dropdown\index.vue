<template>
  <a-dropdown v-bind="params">
    <span style="cursor: pointer;">
      {{ showLabel }}
      <a-icon type="down" />
    </span>
    <a-menu slot="overlay" selectable :selectedKeys="[data]">
      <a-menu-item v-for="item in options" :key="item.value">
        <a @click="onChange(item.value)">{{ item.label }}</a>
      </a-menu-item>
    </a-menu>
  </a-dropdown>
</template>

<script>
export default {
  components: {},
  props: {
    options: Array,
    value: String | Number | Boolean
  },
  data() {
    return {
      data: this.value
    };
  },
  computed: {
    params() {
      return { ...this.$attrs };
    },
    showLabel() {
      return (this.options.find(item => item.value == this.data) || {}).label;
    }
  },
  methods: {
    onChange(val) {
      this.data = val;
      this.$emit('change', val);
    }
  },
  watch: {
    value(newVal) {
      this.data = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
.custom-dropdown {
}
</style>
