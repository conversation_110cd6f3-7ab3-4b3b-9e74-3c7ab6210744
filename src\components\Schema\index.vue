<template>
  <div class="custom-schema">
    <template v-if="!isArray(schema)">
      <SchemaComp :schema="schema" :value="value" :emitKey="emitKey"></SchemaComp>
    </template>
    <template v-else>
      <SchemaComp
        v-for="(item, index) in schema"
        :key="index"
        :schema="item"
        :value="value"
        :emitKey="emitKey"
      ></SchemaComp>
    </template>
  </div>
</template>

<script>
import SchemaComp from './Comp';

export default {
  name: 'Schema',
  // inheritAttrs: false,
  // model: {
  //   prop: 'value',
  //   event: 'change'
  // },
  components: { SchemaComp },
  props: {
    schema: Object | Array,
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      emitKey: _.uniqueId(`schemaUkey`),
      emitData: {},
      validateArr: []
    };
  },
  computed: {},
  created() {},
  mounted() {},
  destroyed() {
    this.$bus.$off(this.emitKey);
    this.$bus.$off(this.emitKey + '_validate');
  },
  methods: {
    isArray(data) {
      return Array.isArray(data);
    },
    validate(cbk) {
      this.validateArr = [];
      this.$bus.$emit(this.emitKey + '_validate', this.validateArr);
      return Promise.all(this.validateArr.filter(item => item)).then(() => {
        cbk && cbk();
      });
    },
    getData() {
      this.emitData = {};
      this.$bus.$emit(this.emitKey, this.emitData);
      return this.emitData;
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.custom-schema {
}
</style>