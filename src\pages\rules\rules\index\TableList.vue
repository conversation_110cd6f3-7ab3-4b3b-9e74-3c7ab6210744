<template>
  <div>
    <SwitchTable
      ref="switchTable"
      v-bind="tableParams"
      @reset="reset"
      :cardColumns="cardColumns"
      :listColumns="listColumns"
      :rowClassName="getRowClassName"
    >
      <template slot="tableTopRight">
        <a-input
          class="table-search"
          v-model="searchVal"
          placeholder="请输入规则名"
          @pressEnter="onSearch"
        >
          <template #suffix>
            <custom-icon
              class="search-icon"
              @click="onSearch"
              type="lu-icon-search"
            />
          </template>
        </a-input>
      </template>
      <!-- 卡片模式 -->
      <CardTable
        slot="cardTable"
        slot-scope="{ record }"
        v-bind="{ cardData: record }"
        @change="onChange"
      >
        <template slot="action">
          <a-popconfirm title="确定删除?" @confirm="() => deleteItem(record)">
            <a> <custom-icon type="delete" style="margin-right: 4px" />删除 </a>
          </a-popconfirm>
          <a @click="view(record)">
            <custom-icon type="profile" style="margin-right: 4px" />查看
          </a>
          <a @click="modify(record)" class="highlight">
            编辑
            <custom-icon type="lu-icon-right" />
          </a>
        </template>
      </CardTable>
      <!-- 列表模式 -->
      <DateFormat
        slot="updated_at"
        slot-scope="{ text }"
        :text="text"
        :limit="20"
      />
      <LimitLabel
        slot="name"
        slot-scope="{ text }"
        :label="text"
        :limit="18"
      ></LimitLabel>
      <LimitLabel
        slot="desc"
        slot-scope="{ text }"
        :label="text"
        :limit="18"
      ></LimitLabel>
      <LimitTags
        v-if="record.rule_set_name"
        slot="rule_set_name"
        slot-scope="{ record }"
        :tags="
          ((record.rule_set_name || '').split(',') || []).map((item) => ({
            label: item
          }))
        "
        :limit="1"
      ></LimitTags>
      <span slot="rule_result" slot-scope="{ text }">
        <span v-if="text != null">
          <custom-icon
            type="lu-icon-alarm"
            :style="{
              color: text == 1 ? '#f6b475' : 'rgb(231, 29, 54)',
              'font-size': '16px'
            }"
          />
          <span>{{ text == 0 ? '高风险' : '低风险' }}</span>
        </span>
        <span v-else>--</span>
      </span>
      <span slot="rule_category" slot-scope="{ text }">
        <span :class="`${text}-category`">{{ text }}</span>
      </span>
      <template slot="updated_by" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_update" placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{ record.ch_update }}</span>
          </div>
        </a-tooltip>
        <span v-else>{{ text || '--' }}</span>
      </template>
      <span slot="ruleStatus" slot-scope="{ record }">
        <a-switch
          size="small"
          v-if="record.rule_category === 'DML'"
          :checked="record.status === 1"
          @change="onChange(record)"
        />
        <span style="margin-left: 8px">{{
          record.status === 1 ? '启用' : '禁用'
        }}</span>
      </span>
      <span slot="type" slot-scope="{ record }">{{
        record.type | typeStatus
      }}</span>
      <custom-btns-wrapper slot="action" slot-scope="{ record }">
        <a @click="view(record)" actionBtn>查看</a>
        <a @click="modify(record)" actionBtn>编辑</a>
        <a-popconfirm
          title="确定删除?"
          @confirm="() => deleteItem(record)"
          actionBtn
        >
          <a class="remove">删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </SwitchTable>
  </div>
</template>
<script>
import { changeStatus, ruleDelete, newRuleDelete } from '@/api/config/rule';
import SwitchTable from '@/components/PageListNew/SwitchTable/table.vue';
import DateFormat from '@/components/DateFormat';
import LimitLabel from '@/components/LimitLabel';
import LimitTags from '@/components/LimitTags';
import CardTable from './CardTable';
import config from './config';
import _ from 'lodash';

export default {
  components: { SwitchTable, DateFormat, LimitLabel, LimitTags, CardTable },
  props: {
    // dbType: String
  },
  data() {
    this.config = config(this);
    this.searchData = {};
    return {
      tableParams: {
        url: '/sqlreview/project/all_rule_list',
        reqParams: {
          // db_type: this.dbType,
          // rule_category: this.ruleCategory
        },
        columns: this.config.cardColumns,
        searchFields: this.config.searchFields,
        searchInitData: { rule_category: 'ALL' },
        needTools: true,
        needSearchArea: true,
        isInitReq: false,
        rowKey: 'rule_id',
        scroll: { x: 'max-content' },
        cacheKey: 'rules-config-' + this.dbType,
        pagination: {
          size: ''
        }
      },
      cardColumns: this.config.cardColumns,
      listColumns: this.config.columns,
      searchVal: ''
    };
  },
  created() {},
  mounted() {},
  methods: {
    getRowClassName(record) {
      const { status } = record;
      if (status == 0) {
        return 'disabled';
      }
      return '';
    },
    deleteItem(data) {
      this.$showLoading();
      if (data.rule_category === 'DDL') {
        newRuleDelete({
          rule_category: data.rule_category,
          rule_id: data.rule_id,
          rule_set_id: data.rule_set_id
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.search(this.searchData, { keep: true, type: 'remove' });
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else {
        ruleDelete({
          id: data.rule_id
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.search(this.searchData, { keep: true, type: 'remove' });
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      }
    },
    modify(data) {
      this.$router.push({
        name: 'rules-config-edit',
        query: {
          id: data.rule_id || data.rule_uid,
          rule_category: data.rule_category
        }
      });
    },
    view(data) {
      this.$router.push({
        name: 'rules-config-detail',
        query: {
          id: data.rule_id || data.rule_uid,
          rule_category: data.rule_category,
          rule_set_id: data.rule_set_id
        }
      });
    },
    onChange(data) {
      this.$showLoading();
      changeStatus({
        id: data.rule_id,
        status: data.status === 0 ? 1 : 0
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.search(this.searchData, { keep: true });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 查询
    search(data, params = {}) {
      const { switchTable } = this.$refs;
      const _ref = switchTable.getInstance();
      const { keep, type } = params;
      this.searchData = data || {};
      if (keep) {
        _ref.refreshKeep(type, data);
      } else {
        _ref.refresh(null, data);
      }
    },
    // 重置
    reset() {
      this.searchData = {};
      this.searchVal = undefined;
    },
    onSearch() {
      const { switchTable } = this.$refs;
      const _ref = switchTable.getInstance();
      const { searchParams } = _ref;
      Object.assign(searchParams, {
        search: this.searchVal
      });
      this.search({ search: this.searchVal });
    },
    init(dbType, ruleCategory) {
      this.$set(this.tableParams.searchInitData, 'rule_category', ruleCategory);
      this.$set(this.tableParams, 'reqParams', {
        db_type: dbType,
        rule_category: ruleCategory,
        _t: +new Date()
      });
    }
  },
  filters: {
    typeStatus(value) {
      let obj = {
        1: 'SQL 语法规范',
        2: 'SQL 执行规范',
        9: '待确定'
      };
      return obj[value];
    }
  }
};
</script>

<style lang="less" scoped>
.table-search {
  margin-right: 4px;
}
.DDL-category {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #f29339;
  font-weight: 400;
}
.DML-category {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #4cbb3a;
  font-weight: 400;
}
// /deep/.custom-table .ant-table .ant-table-tbody > tr,
// /deep/ .custom-table .ant-table-fixed .ant-table-tbody > tr {
//   &.disabled {
//     > td {
//       background: rgba(243, 244, 246, 0.6);
//     }
//   }
// }
</style>
