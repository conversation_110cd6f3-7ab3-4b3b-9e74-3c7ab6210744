export default function (ctx) {
  const columns = [
    {
      title: '用户名',
      dataIndex: 'name',
      key: 'username',
      width: 150
    },
    {
      title: '姓名',
      dataIndex: 'ch_name',
      key: 'ch_name',
      customRender: (text) => {
        return text || '--';
      },
      width: 150
    },
    {
      title: '用户角色',
      dataIndex: 'role_name',
      key: 'userRole',
      width: 150
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200
    },
    {
      title: '项目组',
      dataIndex: 'groups_name',
      key: 'groups_name',
      scopedSlots: { customRender: 'groups_name' },
      width: 200
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 200,
      visible: $permissionBatch.some([
        { module: 'user', values: ['modifyAuth', 'delete', 'resetPwd'] }
      ])
    }
  ].map(item => { return { ...item, with: undefined } }).filter(item => item.visible !== false)
  const fields = [
    {
      type: 'Input',
      label: '用户名、姓名或邮箱',
      key: 'search',
      sourceKey: 'username' || 'email',
      mainSearch: true,
      props: {
        placeholder: '请输入用户名或邮箱'
      }
    },
    {
      type: 'Input',
      label: '项目组',
      key: 'groups_name',
      props: {
        placeholder: '请输入项目组'
      }
    },
    {
      type: 'Select',
      label: '用户角色',
      key: 'role',
      sourceKey: 'userRole',
      props: {
        options: [
          { label: '管理员', value: 'admin' },
          { label: 'DBA', value: 'dba' },
          { label: '开发', value: 'developer' },
          { label: 'LEADER', value: 'leader' }
        ]
      }
    }
  ];
  return {
    columns,
    searchFields: fields
  };
}
