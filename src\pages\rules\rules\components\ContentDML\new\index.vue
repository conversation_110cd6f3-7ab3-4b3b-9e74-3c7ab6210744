<template>
  <div class="config-rules-content">
    <!-- 基础信息 -->
    <div class="rules-content-base-info">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <a-icon type="profile" />基础信息 </span>
        <Form
          ref="baseInfo"
          class="base-info-form"
          v-bind="baseInfoParams"
          :formData="baseInfoData"
        >
        </Form>
      </a-card>
    </div>

    <!-- 条件 -->
    <div class="rules-content-condition">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <custom-icon type="lu-icon-rule" />规则内容 </span>
        <div class="rules-content">
          <Rules :ruleData="ruleData" ref="rulesComp" :dbType="dbType"></Rules>
          <EditModal ref="EditModal" @save="onConditionSave" :dbType="dbType" />
        </div>
      </a-card>
    </div>

    <!--输出结果  -->
    <div class="rules-outport-results">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <custom-icon type="lu-icon-do" />优化建议 </span>
        <Form
          ref="outportResults"
          class="rules-outport-results-form"
          v-bind="outportResultsParams"
          :formData="outportResultsData"
        />
      </a-card>
    </div>
  </div>
</template>
<script>
import Form from '@/components/Form';
import Select from '@/components/Select';
import EditModal from '../../EditModal';
import MarkdownViewer from '@/components/Markdown/viewer';
import Rules from '@/components/Biz/RuleComps/Rules/index';
import config from './config';
import { getDMLRuleSetList } from '@/api/config/rule';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Form,
    Select,
    EditModal,
    Rules,
    MarkdownViewer
  },
  props: {
    type: {
      type: String,
      default: 'add'
    },
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    },
    rule_type: {
      type: String,
      default: ''
    }
  },
  data() {
    this.config = config(this);
    let dbType = window.localStorage.getItem('db_type') || '';
    return {
      dbType,
      baseInfoData: { rule_type: 'DML', db_type: dbType, ob_mode: null },
      baseInfoParams: {
        fixedLabel: true,
        layout: 'horizontal',
        // labelCol: { span: 4 },
        // wrapperCol: { span: 20 },
        fields: this.config.baseInfo
      },
      value: false,
      rule_setList: [],
      outportResultsParams: {
        fixedLabel: true,
        gutter: 32,
        multiCols: 1,
        layout: 'horizontal',
        fields: this.config.outportResults
      },
      outportResultsData: {},
      rulesOptions: [],
      ruleType: {
        1: 'SQL 语法规范',
        2: 'SQL 执行规范',
        3: 'SQL 执行计划'
      },
      ruleData: {
        role_type: 'group',
        item_order: 1,
        relation: 'AND',
        children: [
          {
            role_type: 'item'
            // source_code: '',
            // index_code: { key: '', label: '' },
            // prev_operator: { key: '', label: '' },
            // target_operator: { key: '', label: '' },
            // target_value: ''
          }
        ]
      },
      valueList: []
    };
  },
  created() {},
  mounted() {
    const { EditModal } = this.$refs;
    this.$bus.$on('input-modal', data => {
      EditModal.show({ type: this.type, ...data });
    });
    this.getDMLRuleSetListFn();
  },
  destroyed() {
    this.$bus.$off('input-modal');
  },
  methods: {
    change(e) {
      this.$set(
        this.outportResultsData,
        'suggest',
        this.$el.querySelector('.source').value
      );
    },
    onConditionSave(data) {
      const { EditModal } = this.$refs;
      EditModal.hide();
    },
    deleteData(data = {}) {
      // 删除_parent
      const loop = item => {
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            delete child._parent;
            loop(child);
          });
        }
      };
      loop(data, 0);
      return data;
    },
    getData() {
      const ruleData = this.deleteData(this.ruleData);
      const { baseInfo, outportResults } = this.$refs;
      return new Promise((resolve, reject) => {
        Promise.all([baseInfo.validate(), outportResults.validate()])
          .then(values => {
            const outportResultsData = outportResults.getData();
            resolve({
              ...baseInfo.data,
              db_type: baseInfo.data.ob_mode
                ? baseInfo.data.ob_mode
                : baseInfo.data.db_type,
              ...outportResultsData,
              details: ruleData
            });
          })
          .catch(e => {
            reject(e);
          });
      });
    },
    // 数据库选择
    choose(value) {
      this.dbType = value;
      const baseInfo = this.$refs.baseInfo;
      baseInfo.saving({
        db_type: value,
        ob_mode: null,
        rule_set_uids: null
      });
    },
    getDMLRuleSetListFn(data = '') {
      const params = {
        db_type: data || this.dbType
      };
      getDMLRuleSetList(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            const resData = _.get(res, 'data.data');
            this.rulesOptions = resData;
            if (this.type !== 'detail') {
              const { baseInfo } = this.$refs;
              baseInfo.refresh();
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  },
  watch: {
    dataSource: {
      handler(newVal, oldVal) {
        if (newVal.db_type == 'OB_MYSQL' || newVal.db_type == 'OB_ORACLE') {
          this.baseInfoData.db_type = 'OCEANBASE';
        }
        if (newVal.db_type == 'TD_MYSQL' || newVal.db_type == 'TD_PGSQL') {
          this.baseInfoData.db_type = 'TDSQL';
        }
        this.baseInfoData = {
          ...this.baseInfoData,
          name: newVal.name,
          desc: newVal.desc,
          type: newVal.type === 9 ? '待确定' : newVal.type,
          level: newVal.level,
          rule_type: newVal.rule_set_type,
          rule_result: newVal.rule_result,
          ob_mode: newVal.db_type,
          rule_set_uids: newVal.rule_set_uids
        };
        this.outportResultsData = {
          suggest: newVal.suggest
        };
        if (newVal.rule_set && newVal.rule_set.length !== 0) {
          this.value = true;
          this.ruleSetData = {
            rule_set: newVal.rule_set,
            rule_set_name: newVal.rule_set_name
          };
          this.rule_setList = this.ruleSetData.rule_set_name || [];
        }
        this.ruleData = newVal.details;
      }
      // immediate: true
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.config-rules-content {
  .rules-content-base-info {
    /deep/ .common-pure-card {
      .ant-card-body {
        .base-info-form {
          display: flex;
          flex-wrap: wrap;
          .ant-form-item {
            display: flex !important;
            width: 45%;
            margin: 0 32px 12px 0;
            .ant-form-item-label {
              padding-right: 16px;
              align-items: start;
              width: 100px;
              flex-shrink: 0;
              top: 5px;
              > label {
                justify-content: flex-start;
                > span {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #27272a;
                  font-weight: 400;
                }
              }
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              .ant-form-item-children {
                .ant-row-flex-space-around {
                  .backgroundBlock {
                    .topBlock {
                      .text {
                        line-height: 20px;
                      }
                    }
                  }
                }
                .ant-radio-group {
                  white-space: nowrap;
                }
                .ant-input {
                  max-width: 488px !important;
                  height: 36px;
                  border: 1px solid #ebebec;
                  margin-bottom: 8px;
                }
                textarea.ant-input {
                  max-width: 488px !important;
                  min-height: 36px;
                  padding-top: 6px;
                }
                .ant-select {
                  max-width: 488px !important;
                  margin-bottom: 8px;
                  .ant-select-selection {
                    min-height: 36px;
                    border: 1px solid #ebebec;
                    .ant-select-selection__rendered {
                      line-height: 36px;
                      > ul > li {
                        margin-top: 5px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .rules-content-condition {
    .rules-content {
      padding: 0 10% 0 0;
    }
  }
  .rules-outport-results {
    /deep/ .common-pure-card {
      .ant-card-body {
        .ant-form {
          display: flex;
          padding: 0 16px;
          .ant-col {
            padding: 0 !important;
            .ant-form-item {
              display: flex;
              padding-right: 16px;
              align-items: start;
              margin: 0;
              .ant-form-item-label {
                display: none;
              }
              .ant-form-item-control-wrapper {
                width: 100%;
                .custom-markdown {
                  .toastui-editor-defaultUI {
                    max-width: 1250px;
                    border: none;
                    position: relative;
                    // .toastui-editor-toolbar {
                    //   border-radius: 8px 8px 0 0;
                    //   background: #e4e4e7;
                    // }
                    .toastui-editor-toolbar {
                      .toastui-editor-defaultUI-toolbar {
                        border: 1px solid #ebebec;
                        border-radius: 8px 8px 0 0;
                        border-bottom: none;
                      }
                    }
                    .toastui-editor-main {
                      border: 1px solid #ebebec;
                      border-radius: 0 0 8px 8px;
                      .toastui-editor {
                        .ProseMirror {
                          height: 100%;
                        }
                      }
                      .toastui-editor-md-preview {
                        .toastui-editor-contents {
                          padding-bottom: 24px;
                        }
                      }
                    }
                    .toastui-editor-mode-switch {
                      border-top: none;
                      position: absolute;
                      top: -32px;
                      right: 0;
                      // padding: 19px 0 0 0;
                      background: transparent;
                      .tab-item {
                        border: none;
                        border-radius: 8px;
                        margin-left: 8px;
                        &:hover {
                          background: #7fc4ed;
                          color: #fff;
                        }
                        &.active {
                          background: #7fc4ed;
                          color: #fff;
                        }
                      }
                    }
                  }
                }
              }
            }
            &:last-child {
              .ant-form-item {
                .ant-form-item-label {
                  .ant-form-item-no-colon {
                    margin-left: 12px;
                  }
                }
              }
            }
            &.suggest {
              .ant-form-item {
                flex-direction: column;
                .ant-col .ant-form-item-no-colon {
                  margin-left: 0;
                  > span {
                    margin-right: 4px;
                    &::after {
                      content: '(支持MarkDown语法)';
                      color: #a1a1aa;
                      font-size: 14px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
