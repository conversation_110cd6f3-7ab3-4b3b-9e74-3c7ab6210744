<template>
  <a-input-group compact class="biz-rule-range">
    <Select
      :style="{ width: visible ? '45%' : '100%' }"
      v-bind="targetParams"
      v-model="loacalVal.table_scan"
      @change="selectChange"
      :options="dataSource"
    />
    <Select
      style="width: 25%"
      v-bind="conditionParams"
      v-model="loacalVal.symbol"
      @change="onChange"
      v-if="visible"
    />
    <a-input-number
      style="width: 30%"
      placeholder="请输入"
      v-model="loacalVal.number_scan"
      :formatter="(value) => `${value}${unit}`"
      :parser="(value) => value.replace(unit, '')"
      @change="onChange"
      v-if="visible"
    />
  </a-input-group>
</template>

<script>
import Select from '@/components/Select';
const defaultProps = {};

export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {
    Select
  },
  props: {
    value: {
      type: String,
      default: '{}'
    },
    code: String,
    unit: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loacalVal: {},
      targetParams: {
        url: '/sqlreview/project/dml_adaptation_list',
        reqParams: {},
        loaded: this.onLoaded,
        allowClear: false
      },
      conditionParams: {
        url: '/sqlreview/project/dml_adaptation_symbol',
        reqParams: {},
        // options: [
        //   // { label: '等于', value: 'eq' },
        //   // { label: '大于', value: 'gt' },
        //   // { label: '大于等于', value: 'gte' },
        //   // { label: '小于', value: 'lt' },
        //   // { label: '小于等于', value: 'lte' }
        //   // { label: '=', value: 'eq' },
        //   // { label: '>', value: 'gt' },
        //   // { label: '>=', value: 'gte' },
        //   // { label: '<', value: 'lt' },
        //   // { label: '<=', value: 'lte' }
        // ],
        allowClear: false
      },
      dataSource: [],
      visible: false
    };
  },
  computed: {
    rangeVal() {
      const { loacalVal = [] } = this;
      return loacalVal;
    },
    rangeProps() {
      return { ...defaultProps, ...this.$attrs };
    }
  },
  created() {},
  mounted() {},
  methods: {
    getValObj(val = '{}') {
      let res = {};
      try {
        res = JSON.parse(val);
      } catch (e) {
        console.log(e, val);
      }
      return res;
    },
    onChange(val, type) {
      this.$emit('change', JSON.stringify(this.loacalVal));
    },
    selectChange(val, type) {
      this.loacalVal.symbol = null;
      this.loacalVal.number_scan = null;
      const itemData = this.dataSource.find(item => {
        return item.value == this.loacalVal.table_scan;
      });
      this.visible = itemData.is_later_visible;
      this.$set(this.conditionParams, 'reqParams', { code: itemData.value });
      this.$emit('change', JSON.stringify(this.loacalVal));
    },
    onLoaded(data) {
      this.dataSource = data;
      const itemData = data.find(item => {
        return item.value == this.loacalVal.table_scan;
      });
      this.visible = itemData && itemData.is_later_visible;
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.loacalVal = this.getValObj(newVal);
        this.$set(this.conditionParams, 'reqParams', {
          code: this.loacalVal.table_scan
        });
      },
      immediate: true
    },
    code: {
      handler(newVal) {
        this.$set(this.targetParams, 'reqParams', {
          code: newVal
        });
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.biz-rule-range {
  display: flex;
  align-items: center;

  > .ant-input-number {
    border-radius: 0 4px 4px 0;
  }
}
</style>
