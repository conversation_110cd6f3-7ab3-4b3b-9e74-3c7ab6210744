
export default function (ctx) {
  const columns = [
    {
      dataIndex: 'name',
      key: 'name',
      width: 300,
      slots: { title: 'customTitle' },
      scopedSlots: { customRender: 'name' },
      isEmptyOnEdit: false
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      width: 300,
      scopedSlots: { customRender: 'age' }
    },
    {
      title: 'Address',
      dataIndex: 'addressName',
      key: 'address',
      width: 300,
      scopedSlots: { customRender: 'address' }
    },
    {
      title: 'Tags',
      key: 'tags',
      dataIndex: 'tags',
      width: 300,
      scopedSlots: { customRender: 'tags' }
    },
    {
      title: 'Action',
      key: 'action',
      width: 300,
      scopedSlots: { customRender: 'action' }
    }
  ];
  const editConfig = {
    age: {
      type: 'Input',
      rules: [{ required: true, message: 'Age为必填项', trigger: 'blur' }],
      props: {
        style: { width: '90%' }
      },
      plainText: true
    },
    address: {
      type: 'Select_Obj',
      props: {
        url: '/api/home/<USER>/select',
        reqParams: {
          from: 'pageListAllConfig'
        },
        style: { width: '90%' }
      }
    },
    extra: {
      type: 'Input',
      label: '额外字段',
      rules: [{ required: true, message: 'extra为必填项', trigger: 'blur' }],
      props: {
        style: { width: '90%' }
      }
      // editProps: {
      //   disabled: true
      // }
    },
    extra1: {
      type: 'Select_Obj',
      label: '额外字段1',
      dataIndex: 'extra1Name',
      props: {
        url: '/api/home/<USER>/select',
        reqParams: {
          from: 'pageListAllConfig'
        },
        style: { width: '90%' }
      }
    }
  }
  return {
    columns, editConfig
  };
};
