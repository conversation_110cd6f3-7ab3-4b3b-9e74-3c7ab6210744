<template>
  <div class="label-container">
    <a-popover
      overlayClassName="biz-label-card"
      @visibleChange="onVisibleChange"
    >
      <template slot="content">
        <div class="label-type-content">
          <div>
            <span>标签属性：</span>
            <a class="text">
              <span
                ><a-icon
                  :type="iconType[labelStatus]"
                  :style="{ color: iconColor[labelStatus] }"
                />{{ labelCardData.label_attribute }}</span
              >
              <a-icon
                type="delete"
                @click="remove"
                v-if="[0, -1].includes(labelStatus)"
              />
            </a>
          </div>
          <div>
            <span>标签ID：</span>
            <LimitLabel
              :limit="16"
              :label="labelCardData.label_id"
              :needCopy="true"
            ></LimitLabel>
            <!-- <span>{{ labelCardData.label_id || '--' }}</span> -->
          </div>
          <div>
            <span>标签ID类型：</span>
            <span>{{ labelCardData.label_type || '--' }}</span>
          </div>
          <div>
            <span>标签来源：</span>
            <span>{{ labelCardData.label_source || '--' }}</span>
          </div>
          <div>
            <span>申请人：</span>
            <span>{{ labelCardData.created_by || '--' }}</span>
          </div>
          <!-- 已通过标签有 -->
          <div v-if="labelCardData.label_status == 1">
            <span>生效时间：</span>
            <span>{{ labelCardData.audit_time || '--' }}</span>
          </div>
          <div v-if="labelCardData.label_status == 1">
            <span>失效时间：</span>
            <span>{{ labelCardData.expired_time || '--' }}</span>
          </div>
          <!-- 待审核标签有 -->
          <div v-if="labelCardData.label_status == 0">
            <span>申请时间：</span>
            <span>{{ labelCardData.created_at || '--' }}</span>
          </div>
          <div v-if="labelCardData.label_status == 0">
            <span>有效期：</span>
            <span>{{
              labelCardData.permanent_day == 0
                ? '永久'
                : labelCardData.permanent_day + '天'
            }}</span>
          </div>
          <!-- 未通过标签有 -->
          <div v-if="labelCardData.label_status == '-1'">
            <span>审核人：</span>
            <span>{{ labelCardData.audit_user || '--' }}</span>
          </div>
          <div>
            <span>状态：</span>
            <span :class="`status${labelCardData.label_status}`">{{
              label[labelCardData.label_status]
            }}</span>
          </div>
        </div>
      </template>
      <span class="white-list" v-if="mode == 'text'">
        <a-icon
          :type="iconType[labelStatus]"
          :style="{ color: iconColor[labelStatus] }"
        />
        <span>{{ text == 0 ? '白名单' : '整改中' }}</span>
      </span>
      <custom-icon
        v-if="mode == 'icon'"
        type="tags"
        class="label-icon"
        style="color: #595959;"
      />
    </a-popover>
  </div>
</template>

<script>
import { getLabelCardData, delteteLabel } from '@/api/databaseaudit/topsql';
import LimitLabel from '@/components/LimitLabel';
export default {
  components: { LimitLabel },
  props: {
    mode: {
      type: String,
      default: 'text' // text、icon 文字、图标
    },
    requstMode: {
      type: String,
      default: 'everytime'
    }, // everytime/once 每次都请求/一次性全请求
    id: Number | String,
    text: Number | String,
    labelStatus: Number | String
  },
  data() {
    return {
      visible: false, // true/false 气泡框显示/隐藏
      labelCardData: {},
      label: {
        '-1': '审核未通过',
        0: '待审核',
        1: '审核通过',
        2: '已删除',
        3: '已失效'
      },
      labelColor: {
        '-1': '#EF6173',
        0: '#F29339',
        1: '#3A974C'
      },
      iconType: {
        '-1': 'stop',
        0: 'clock-circle',
        1: 'check',
        2: 'close'
      },
      iconColor: {
        '-1': '#FF4D4F',
        0: '#000000',
        1: '#52C41A',
        2: '#BFBFBF'
      }
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    // 气泡框显示隐藏的回调
    onVisibleChange() {
      this.visible = !this.visible;
      if (this.visible && this.requstMode == 'everytime') {
        setTimeout(() => {
          this.getData(this.id);
        }, 200);
      }
    },
    getData(id) {
      getLabelCardData({ id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.labelCardData = _.get(res, 'data.data');
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    remove() {
      this.$showLoading({ tips: '删除中' });
      delteteLabel({ id: this.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.$emit('refresh');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    }
  },
  watch: {
    id: {
      handler(newVal) {
        if (newVal && this.requstMode == 'once') {
          this.getData(newVal);
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.label-container {
  .white-list {
    // display: flex;
    // align-items: center;
    padding: 2px 12px;
    border-radius: 16px;
    width: auto;
    border: 1px solid #e4e4e7;
    .anticon {
      margin-right: 6px;
      margin-top: 2px;
      font-size: 12px;
    }
  }
}
</style>
<style lang="less">
.ant-popover {
  &.biz-label-card {
    .ant-popover-inner {
      .ant-popover-inner-content {
        min-width: 300px;
        max-height: 300px;

        .label-type-content {
          > div {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 4px 10px;
            > span {
              font-size: 14px;
              white-space: nowrap;
              &:first-child {
                color: #1f1f1f;
                width: 100px;
              }
              &:last-child {
                font-size: 14px;
                color: #595959;
              }
              &.status-1,
              &.status0,
              &.status1 {
                color: #1677ff;
              }
              &.status2,
              &.status3 {
                color: #ff4d4f;
              }
            }
            /deep/ .limit-label {
              font-size: 14px;
              color: #595959;
            }
            .text {
              display: flex;
              justify-content: space-between;
              align-items: center;
              > span {
                color: #27272a;
                padding: 2px 10px;
                border-radius: 6px;
                border: 1px solid #e4e4e7;
                white-space: nowrap;
                display: flex;
                align-items: center;
                margin-right: 8px;
                .anticon {
                  margin-right: 8px;
                  font-size: 12px;
                }
              }
              > .anticon {
                color: #1677ff;
                font-size: 14px;
                &:hover {
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
