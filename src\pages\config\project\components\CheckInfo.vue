<template>
  <div class="config-project-datasource-check-info">
    <div class="fail" v-if="dataSource.length > 0">
      <div class="title">！数据库检测失败，SQL审核将获取不到对应的执行计划</div>
      <Table ref="table" v-bind="tableParams || {}" :data-source="dataSource">
        <span slot="data_source_name" slot-scope="{record }">
          <LimitLabel
            :label="record.schema ?`${record.data_source_name}@${record.schema}` : record.data_source_name"
            :limit="18"
          ></LimitLabel>
        </span>
        <span slot="message" slot-scope="{record }">{{record.message || record.lack}}</span>
      </Table>
    </div>
    <a-alert v-else class="success" message="数据源检查成功!" type="success" show-icon />
  </div>
</template>
<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';

export default {
  components: { Table, LimitLabel },
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dataSource: [],
      tableParams: {
        url: '',
        reqParams: {},
        rowKey: 'id',
        pagination: false,
        columns: [
          {
            title: '数据库',
            dataIndex: 'data_source_name',
            key: 'data_source_name',
            width: 150,
            scopedSlots: { customRender: 'data_source_name' }
          },
          // {
          //   title: '缺少权限',
          //   dataIndex: 'lack',
          //   key: 'lack',
          //   width: 160
          // },
          {
            title: '错误信息',
            dataIndex: 'message',
            key: 'message',
            scopedSlots: { customRender: 'message' },
            width: 200
          }
        ]
      }
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.dataSource = newVal || [];
      },
      immediate: true
    }
  },
  methods: {}
};
</script>
<style lang="less" scoped>
.config-project-datasource-check-info {
  .fail {
    padding: 12px 24px;
    border: 1px solid red;
    background: rgb(254, 241, 239);
    .title {
      color: red;
      padding-bottom: 12px;
    }
  }
  .success {
    width: 500px;
  }
}
</style>