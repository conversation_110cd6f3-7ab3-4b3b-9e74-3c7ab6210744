
export default function (ctx) {
  const fields = {
    tag: [
      {
        type: 'Input',
        label: 'git地址',
        key: 'review_url',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'Input',
        label: 'tag白名单',
        tips:
          '<div>请使用正则表达式，如与review点匹配成功则该次发起直接通过</div>',
        key: 'review_tag',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'Input',
        label: '账号',
        key: 'username',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'InputPassword',
        label: '密码',
        key: 'password',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'TableEdit',
        label: 'review白名单',
        tips: `<div>配置文件或目录，仅操作该文件或目录下文件及子目录文件评审扫描；
            <div style='margin-top:8px;'>精确匹配：填写从项目根目录开始的完整路径（不包括项目根目录）: /module/script/sql_file.sql</div>
            <div style='margin-top:8px;'>模糊匹配：请输入'reg:'+正则表达式，例如，文件夹输入reg:.*dir/ 文件输入reg:.*sql_file.sql 若输入reg:.*name.* 则会将所有路径中含name的文件设置为白名单</div>
            </div>`,
        key: 'white_list',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        props: {
          columns: [
            {
              dataIndex: 'path',
              key: 'path',
              width: 300,
              scopedSlots: { customRender: 'path' }
            },
            {
              key: 'action',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ],
          editConfig: {
            path: (row, record = {}) => {
              return {
                type: 'Input',
                props: {
                  size: 'default',
                  placeholder: 'script/sql_file.sql 或者 script'
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          mode: 'list',
          initEditStatus: true,
          pagination: false,
          leastNum: 1,
          actionBtns: ['add', 'remove'],
          actionBtnsIcons: {
            add: 'plus-circle',
            remove: 'close-circle'
          }
        }
      },
      {
        type: 'TableEdit',
        label: 'review黑名单',
        tips: `<div>配置文件或目录，该文件或目录下文件及子目录文件不做评审扫描；
            <div style='margin-top:8px;'>精确匹配：填写从项目根目录开始的完整路径（不包括项目根目录）: /module/script/sql_file.sql</div>
            <div style='margin-top:8px;'>模糊匹配：请输入'reg:'+正则表达式，例如，文件夹输入reg:.*dir/ 文件输入reg:.*sql_file.sql 若输入reg:.*name.* 则会将所有路径中含name的文件设置为黑名单</div>
            </div>`,
        key: 'black_list',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        props: {
          columns: [
            {
              dataIndex: 'path',
              key: 'path',
              width: 300,
              scopedSlots: { customRender: 'path' }
            },
            {
              key: 'action',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ],
          editConfig: {
            path: (row, record = {}) => {
              return {
                type: 'Input',
                props: {
                  size: 'default',
                  placeholder: 'script/sql_file.sql 或者 script'
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          mode: 'list',
          initEditStatus: true,
          pagination: false,
          leastNum: 1,
          actionBtns: ['add', 'remove'],
          actionBtnsIcons: {
            add: 'plus-circle',
            remove: 'close-circle'
          }
        }
      }
    ],
    branch: [
      {
        type: 'Input',
        label: 'git地址',
        key: 'review_url',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'Input',
        label: '分支白名单',
        tips:
          '<div>请使用正则表达式，如与review点匹配成功则该次发起直接通过</div>',
        key: 'review_tag',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'Input',
        label: '账号',
        key: 'username',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'InputPassword',
        label: '密码',
        key: 'password',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'TableEdit',
        label: 'review白名单',
        tips: `<div>配置文件或目录，仅操作该文件或目录下文件及子目录文件评审扫描；
            <div style='margin-top:8px;'>精确匹配：填写从项目根目录开始的完整路径（不包括项目根目录）: /module/script/sql_file.sql</div>
            <div style='margin-top:8px;'>模糊匹配：请输入'reg:'+正则表达式，例如，文件夹输入reg:.*dir/ 文件输入reg:.*sql_file.sql 若输入reg:.*name.* 则会将所有路径中含name的文件设置为白名单</div>
            </div>`,
        key: 'white_list',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        props: {
          columns: [
            {
              dataIndex: 'path',
              key: 'path',
              width: 300,
              scopedSlots: { customRender: 'path' }
            },
            {
              key: 'action',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ],
          editConfig: {
            path: (row, record = {}) => {
              return {
                type: 'Input',
                props: {
                  size: 'default',
                  placeholder: 'script/sql_file.sql 或者 script'
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          mode: 'list',
          initEditStatus: true,
          pagination: false,
          leastNum: 1,
          actionBtns: ['add', 'remove'],
          actionBtnsIcons: {
            add: 'plus-circle',
            remove: 'close-circle'
          }
        }
      },
      {
        type: 'TableEdit',
        label: 'review黑名单',
        tips: `<div>配置文件或目录，该文件或目录下文件及子目录文件不做评审扫描；
            <div style='margin-top:8px;'>精确匹配：填写从项目根目录开始的完整路径（不包括项目根目录）: /module/script/sql_file.sql</div>
            <div style='margin-top:8px;'>模糊匹配：请输入'reg:'+正则表达式，例如，文件夹输入reg:.*dir/ 文件输入reg:.*sql_file.sql 若输入reg:.*name.* 则会将所有路径中含name的文件设置为黑名单</div>
            </div>`,
        key: 'black_list',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        props: {
          columns: [
            {
              dataIndex: 'path',
              key: 'path',
              width: 300,
              scopedSlots: { customRender: 'path' }
            },
            {
              key: 'action',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ],
          editConfig: {
            path: (row, record = {}) => {
              return {
                type: 'Input',
                props: {
                  size: 'default',
                  placeholder: 'script/sql_file.sql 或者 script'
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          mode: 'list',
          initEditStatus: true,
          pagination: false,
          leastNum: 1,
          actionBtns: ['add', 'remove'],
          actionBtnsIcons: {
            add: 'plus-circle',
            remove: 'close-circle'
          }
        }
      }
    ],
    svn: [
      {
        type: 'Input',
        label: 'svn地址',
        key: 'review_url',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'Input',
        label: '账号',
        key: 'username',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'InputPassword',
        label: '密码',
        key: 'password',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      {
        type: 'TableEdit',
        label: 'review白名单',
        tips: `<div>配置文件或目录，仅操作该文件或目录下文件及子目录文件评审扫描；
            <div style='margin-top:8px;'>精确匹配：填写从项目根目录开始的完整路径（不包括项目根目录）: /module/script/sql_file.sql</div>
            <div style='margin-top:8px;'>模糊匹配：请输入'reg:'+正则表达式，例如，文件夹输入reg:.*dir/ 文件输入reg:.*sql_file.sql 若输入reg:.*name.* 则会将所有路径中含name的文件设置为白名单</div>
            </div>`,
        key: 'white_list',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        props: {
          columns: [
            {
              dataIndex: 'path',
              key: 'path',
              width: 300,
              scopedSlots: { customRender: 'path' }
            },
            {
              key: 'action',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ],
          editConfig: {
            path: (row, record = {}) => {
              return {
                type: 'Input',
                props: {
                  size: 'default',
                  placeholder: 'script/sql_file.sql 或者 script'
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          mode: 'list',
          initEditStatus: true,
          pagination: false,
          leastNum: 1,
          actionBtns: ['add', 'remove'],
          actionBtnsIcons: {
            add: 'plus-circle',
            remove: 'close-circle'
          }
        }
      },
      {
        type: 'TableEdit',
        label: 'review黑名单',
        tips: `<div>配置文件或目录，该文件或目录下文件及子目录文件不做评审扫描；
            <div style='margin-top:8px;'>精确匹配：填写从项目根目录开始的完整路径（不包括项目根目录）: /module/script/sql_file.sql</div>
            <div style='margin-top:8px;'>模糊匹配：请输入'reg:'+正则表达式，例如，文件夹输入reg:.*dir/ 文件输入reg:.*sql_file.sql 若输入reg:.*name.* 则会将所有路径中含name的文件设置为黑名单</div>
            </div>`,
        key: 'black_list',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        props: {
          columns: [
            {
              dataIndex: 'path',
              key: 'path',
              width: 300,
              scopedSlots: { customRender: 'path' }
            },
            {
              key: 'action',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ],
          editConfig: {
            path: (row, record = {}) => {
              return {
                type: 'Input',
                props: {
                  size: 'default',
                  placeholder: 'script/sql_file.sql 或者 script'
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          mode: 'list',
          initEditStatus: true,
          pagination: false,
          leastNum: 1,
          actionBtns: ['add', 'remove'],
          actionBtnsIcons: {
            add: 'plus-circle',
            remove: 'close-circle'
          }
        }
      }
    ]
  }
  return {
    fields
  };
};
