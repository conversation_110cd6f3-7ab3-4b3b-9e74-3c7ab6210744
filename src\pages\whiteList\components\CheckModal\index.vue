<template>
  <a-modal
    v-model="visible"
    :title="type == 'table' ? '审核表白名单' : '审核SQL白名单'"
    :maskClosable="false"
    width="65%"
    :dialogStyle="{ 'minWidth': '1000px'}"
    @cancel="onCancel"
  >
    <template slot="footer">
      <a-button @click="onOk(-1)">不通过</a-button>
      <a-button @click="onOk(1)" type="primary">通过</a-button>
    </template>
    <a-spin :spinning="spinning">
      <div v-if="type == 'table'">
        <a-tabs :active-key="activeTab" @change="tabChange">
          <a-tab-pane key="tableMessage" tab="表信息">
            <TableList :activeTab="activeTab" ref="tableMessage" :dataSource="tableList"></TableList>
          </a-tab-pane>
          <a-tab-pane key="indexMessage" tab="索引信息">
            <TableList :activeTab="activeTab" ref="indexMessage" :dataSource="indexList"></TableList>
          </a-tab-pane>
          <a-tab-pane key="fieldMessage" tab="字段信息">
            <TableList :activeTab="activeTab" ref="fieldMessage" :dataSource="fieldList"></TableList>
          </a-tab-pane>
        </a-tabs>
      </div>
      <div v-else>
        <Form ref="sqlMapParams" v-bind="sqlMapParams" :formData="sqlMapParamsData"></Form>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import TableList from './TableList';
import Form from '@/components/Form';
import config from './config';
import {
  checkWhiteList,
  pass,
  sqlWhiteListInfo,
  sqlPass
} from '@/api/config/whiteList';
export default {
  components: { TableList, Form },
  data() {
    this.config = config(this);
    return {
      visible: false,
      data: {},
      activeTab: 'tableMessage',
      tableList: [],
      indexList: [],
      fieldList: [],
      record: {},
      sqlMapParams: {
        fields: this.config.fields,
        fixedLabel: true,
        layout: 'horizontal',
        colon: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 20 }
      },
      sqlMapParamsData: {},
      type: 'table',
      spinning: false
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(record, type) {
      this.type = type;
      this.record = record;
      this.visible = true;
      this.spinning = true;
      if (type == 'table') {
        checkWhiteList({ id: record.id })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.tableList = (res.data.data.table_list || []).map(
                (item, index) => ({
                  ...item,
                  id: index
                })
              );
              this.indexList = (res.data.data.index_list || []).map(
                (item, index) => ({
                  ...item,
                  id: index
                })
              );
              this.fieldList = (res.data.data.column_info || []).map(
                (item, index) => ({
                  ...item,
                  id: index + 1
                })
              );
              this.spinning = false;
            } else {
              this.spinning = false;
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.spinning = false;
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      } else {
        sqlWhiteListInfo({ id: record.id })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.spinning = false;
              this.$set(this, 'sqlMapParamsData', res.data.data.sql_map_config);
            } else {
              this.spinning = false;
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.spinning = false;
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      }
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk(num) {
      let obj = { id: this.record.id, status: num };
      if (this.type == 'table') {
        pass(obj)
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.visible = false;
              this.$emit('checkoutOver');
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      } else {
        sqlPass(obj)
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.visible = false;
              this.$emit('checkoutOver');
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      }
    },
    // 切换 tab 选项卡
    tabChange(activeKey) {
      this.activeTab = activeKey;
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ant-modal-footer {
  text-align: center;
  .ant-btn {
    width: 164px;
    height: 41px;
  }
}

/deep/ .ant-input[disabled] {
  color: rgba(0, 0, 0, 0.65);
}

/deep/ .ant-radio-disabled + span {
  color: rgba(0, 0, 0, 0.65);
}

/deep/ .ant-select-disabled {
  color: rgba(0, 0, 0, 0.65);
}
</style>
