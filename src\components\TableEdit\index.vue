<template>
  <div :class="className">
    <a-form-model ref="form" :model="formData">
      <Table
        ref="table"
        v-bind="tableProps"
        v-on="tableListeners"
        :rowClassName="getRowClassName"
        :data-source="data"
        @inited="watchTableInited('init')"
        @refresh="watchTableInited('refresh')"
      >
        <template v-for="sItem in tableScopedSlots" v-slot:[sItem]="{ text, record, index }">
          <!-- 拖动排序列 -->
          <span :key="sItem" v-if="sItem === 'draggable'">
            <custom-icon class="table-edit-drag-handler" type="lu-icon-sort" style="cursor:move;"></custom-icon>
          </span>
          <!-- 操作列（必须为action） -->
          <custom-btns-wrapper
            :key="sItem"
            v-else-if="sItem === 'action'"
            :id="instanceId + '_' + getEditKey(sItem, record)"
            v-bind="actionWrapperProps"
          >
            <!-- 行内新增、编辑的行 -->
            <template
              v-if="!modalEdit && type === 'lineEdit' && (isAdd || isEdit) && record._editable"
            >
              <span actionBtn v-if="actionBtnsSlots.saveManual">
                <slot :name="actionBtnsSlots.saveManual" v-bind="{record}"></slot>
              </span>
              <span @click="save(record)" actionBtn v-else>
                <custom-icon
                  style="color:#1890ff"
                  class="action-icon"
                  :type="actionBtnsIcons.save"
                  v-if="actionBtnsIcons.save"
                />
                <slot :name="actionBtnsSlots.save" v-else-if="actionBtnsSlots.save"></slot>
                <a v-else>保存</a>
              </span>
              <!-- <a-divider type="vertical" /> -->
              <span actionBtn>
                <a-popconfirm title="确定删除该行?" @confirm="() => remove(record)" v-if="isAdd">
                  <custom-icon
                    style="color:#e26148"
                    class="action-icon"
                    :type="actionBtnsIcons.remove"
                    v-if="actionBtnsIcons.remove"
                  />
                  <slot :name="actionBtnsSlots.remove" v-else-if="actionBtnsSlots.remove"></slot>
                  <a style="color:#e26148" v-else>取消</a>
                </a-popconfirm>
                <a-popconfirm title="确定取消编辑?" @confirm="() => cancel(record)" v-else>
                  <custom-icon
                    style="color:#e26148"
                    class="action-icon"
                    :type="actionBtnsIcons.cancel"
                    v-if="actionBtnsIcons.cancel"
                  />
                  <slot :name="actionBtnsSlots.cancel" v-else-if="actionBtnsSlots.cancel"></slot>
                  <a style="color:#e26148" v-else>取消</a>
                </a-popconfirm>
              </span>
            </template>
            <!-- 普通行 -->
            <template v-else>
              <template v-for="(btnInfo) in getActionBtns(record, index)">
                <span
                  actionBtn
                  :key="btnInfo.key"
                  :class="`${(!modalEdit && type === 'lineEdit' && (isAdd || isEdit) || btnInfo.disabled || (btnInfo.disabled !== false && record.editable === false)) && 'action-btn-disabled'}`"
                >
                  <!-- 新增 -->
                  <span @click="addLine(record)" v-if="btnInfo.key === 'add'">
                    <custom-icon
                      style="color:#4caf50"
                      class="action-icon"
                      :type="actionBtnsIcons.add"
                      v-if="actionBtnsIcons.add"
                    />
                    <slot :name="actionBtnsSlots.add" v-else-if="actionBtnsSlots.add"></slot>
                    <a v-else>新增</a>
                  </span>
                  <!-- 编辑 -->
                  <span v-else-if="btnInfo.key === 'edit'">
                    <a v-if="!record._editable" :disabled="isEdit" @click="editLine(record)">
                      <custom-icon
                        style="color:#4caf50"
                        class="action-icon"
                        :type="actionBtnsIcons.edit"
                        v-if="actionBtnsIcons.edit"
                      />
                      <slot :name="actionBtnsSlots.edit" v-else-if="actionBtnsSlots.edit"></slot>
                      <a v-else :disabled="isEdit">编辑</a>
                    </a>
                    <a v-else-if="modalVisible">正在编辑</a>
                    <span v-else>
                      <a @click="save(record)">保存</a>
                      <span>
                        <a-divider type="vertical" />
                        <a-popconfirm title="确定取消编辑?" @confirm="() => cancel(record)">
                          <a style="color:#e26148">取消</a>
                        </a-popconfirm>
                      </span>
                    </span>
                  </span>
                  <!-- 删除 -->
                  <span v-else-if="btnInfo.key === 'remove'">
                    <a-popconfirm title="确定删除该行?" @confirm="() => remove(record)">
                      <custom-icon
                        style="color:#e26148"
                        class="action-icon"
                        :type="actionBtnsIcons.remove"
                        v-if="actionBtnsIcons.remove"
                      />
                      <slot :name="actionBtnsSlots.remove" v-else-if="actionBtnsSlots.remove"></slot>
                      <a style="color:#e26148" v-else>删除</a>
                    </a-popconfirm>
                  </span>
                  <!-- extra -->
                  <span v-else-if="btnInfo.key && btnInfo.render">
                    <VNode :node="() => btnInfo.render($createElement, record, _self)" />
                  </span>
                  <span v-else-if="btnInfo.key">
                    <slot :name="btnInfo.key" v-bind="{record}"></slot>
                  </span>
                  <!-- 分割线 -->
                  <!-- <a-divider type="vertical" v-if="btnInfo.key" /> -->
                </span>
              </template>
            </template>
          </custom-btns-wrapper>
          <template v-else>
            <!-- 可编辑 -->
            <template v-if="record._editable && !modalVisible">
              <a-form-model-item
                :key="getEditKey(sItem, record)"
                :prop="getEditKey(sItem, record)"
                :rules="getEditInfo(sItem, index, record).rules"
                v-if="editConfig[sItem]"
              >
                <!-- 取组件 -->
                <a-tooltip>
                  <template slot="title" v-if="getEditInfo(sItem, index, record).tips">
                    <div v-html="getEditInfo(sItem, index, record).tips"></div>
                  </template>
                  <component
                    v-model="formData['key_' + record[rowKey] + '_' + sItem]"
                    v-bind="getEditInfo(sItem, index, record).props"
                    v-on="getEditInfo(sItem, index, record).listeners"
                    :editRecord="record"
                    :editRow="index"
                    :is="getEditInfo(sItem, index, record).compName"
                  >
                    <template
                      v-for="compSlot in (getEditInfo(sItem, index, record).compSlots || [])"
                      v-slot:[compSlot]="{ data }"
                    >
                      <slot :name="compSlot" v-bind="{ data, record }"></slot>
                    </template>
                  </component>
                  <VNode :node="() => getEditInfo(sItem, index, record).extra($createElement)" v-if="getEditInfo(sItem, index, record).extra"></VNode>
                </a-tooltip>
              </a-form-model-item>
              <template v-else>
                <span
                  :key="getEditKey(sItem, record)"
                  v-if="getColumInfo(sItem).isEmptyOnEdit !== false"
                >-</span>
                <slot :name="sItem" v-bind="{text, record, editRecord: getEditRecord(record)}" v-else></slot>
              </template>
            </template>
            <template v-else>
              <!-- 取插槽 -->
              <slot
                :name="sItem"
                v-bind="{text, record, editRecord: getEditRecord(record)}"
                v-if="!getEditInfo(sItem, index, record).plainText"
              ></slot>
              <!-- 仅展示文本 -->
              <template v-else>{{getPlainText(sItem, text, record, index)}}</template>
            </template>
          </template>
        </template>
        <template v-for="item in tableSlots" v-slot:[item]>
          <slot :name="item"></slot>
        </template>
      </Table>
    </a-form-model>
    <!-- 编辑弹窗 -->
    <a-modal
      v-model="modalVisible"
      v-bind="modalParams"
      @cancel="() => modalCancel()"
      @ok="modalOk"
      v-if="!modalCustom"
    >
      <template slot="footer">
        <slot name="modalFooter" v-bind="{ data: modalData }"></slot>
      </template>
      <custom-form ref="modalForm" v-bind="modalFormParams" :formData="modalData"></custom-form>
    </a-modal>
  </div>
</template>

<script>
import Table from '@/components/Table';
import Select from '@/components/Select';
import InputNumRange from '@/components/InputNumRange';
import InputSelect from '@/components/InputSelect';
import InputModal from '@/components/Biz/InputModal';
import YearPicker from '@/components/YearPicker';
import CheckboxGroup from '@/components/CheckboxGroup';
import RadioGroup from '@/components/RadioGroup';
import Label from '@/components/Label';
import Coder from '@/components/Coder';
import common from '@/utils/common';
import _ from 'lodash';
import Sortable from 'sortablejs';
import CustomTableEditComponents from './register';

const compMap = {
  Input: {
    compName: 'a-input',
    props: {
      // size: 'small',
      placeholder: '请输入',
      maxLength: 255
    }
  },
  Textarea: {
    compName: 'a-textarea',
    props: {
      // size: 'small',
      placeholder: '请输入'
      // maxLength: 255
    }
  },
  InputNumber: {
    compName: 'a-input-number',
    props: {
      // size: 'small',
      placeholder: '请输入',
      style: { width: '100%' }
    }
  },
  InputNumRange: {
    compName: 'InputNumRange',
    props: {
      // size: 'small'
    }
  },
  InputSelect: {
    compName: 'InputSelect',
    props: {}
  },
  Select: {
    compName: 'Select',
    props: {
      // size: 'small',
      placeholder: '请选择',
      showSearch: true,
      optionFilterProp: 'children'
    }
  },
  TreeSelect: {
    compName: 'a-tree-select',
    props: {
      // size: 'small',
      getPopupContainer: el => {
        // return document.getElementById('rootContent');
        return el.parentNode;
      }
    }
  },
  InputModal: {
    compName: 'InputModal',
    props: {}
  },
  Switch: {
    compName: 'a-switch',
    props: {
      // size: 'small'
    }
  },
  YearPicker: {
    compName: 'YearPicker',
    props: {
      // size: 'small'
    }
  },
  CheckboxGroup: {
    compName: 'CheckboxGroup',
    props: {
      // size: 'small'
    }
  },
  RadioGroup: {
    compName: 'RadioGroup',
    props: {
      // size: 'small'
    }
  },
  DatePicker: {
    compName: 'a-date-picker',
    props: {
      // size: 'small'
    }
  },
  UserRoleModal: {
    compName: 'UserRoleModal',
    props: {
      // size: 'small'
    }
  },
  Label: {
    compName: 'Label',
    props: {
      // size: 'small'
    }
  },
  Coder: {
    compName: 'Coder',
    props: {}
  },
  Select_Obj: {
    compName: 'Select',
    props: {
      labelInValue: true
      // size: 'small'
    },
    getValue(col, record = {}) {
      const column = this.getColumInfo(col);
      const editInfo = this.getEditInfo(col);
      const labelKey = editInfo.dataIndex || column.dataIndex;
      return record[col] != null
        ? {
            key: record[col],
            label: _.get(record, labelKey)
          }
        : undefined;
    },
    setValue(col, record = {}, val = {}) {
      const column = this.getColumInfo(col);
      const editInfo = this.getEditInfo(col);
      const labelKey = editInfo.dataIndex || column.dataIndex;
      record[col] = val.key;
      _.set(record, labelKey, val.label);
    }
  }
};

// 表格默认属性
const defaultProps = {
  // pagination: false,
};
// modal默认属性
const modalDefaultProps = {
  title: '编辑行',
  maskClosable: false
};
export default {
  inheritAttrs: false,
  model: {
    prop: 'dataSource'
  },
  components: {
    Table,
    Select,
    InputNumRange,
    InputSelect,
    InputModal,
    YearPicker,
    CheckboxGroup,
    RadioGroup,
    Label,
    Coder
  },
  props: {
    // 类型：allEdit: 全部编辑 | lineEdit: 行内编辑
    type: String,
    mode: String,
    leastNum: {
      type: Number,
      default: 0
    },
    dataSource: {
      type: Array,
      default: function() {
        return [];
      }
    },
    // 编辑配置
    editConfig: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // 操作列按钮
    actionBtns: {
      type: Array,
      default: function() {
        return [];
      }
    },
    // 操作列按钮icons
    actionBtnsIcons: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // 操作列按钮slots
    actionBtnsSlots: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // 事件回调
    actionCbks: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // 操作列属性
    actionWrapperProps: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // 初始化编辑状态
    initEditStatus: Boolean,
    // 是否采用弹窗编辑
    modalEdit: Boolean,
    // 是否弹窗自定义
    modalCustom: Boolean,
    // modal配置
    modalConfig: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // modal配置
    modalFormConfig: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // 添加行定位
    addIntoView: {
      type: Boolean,
      default: true
    },
    auth: {
      type: Object,
      default: function() {
        return {};
      }
    },
    draggable: {
      type: Boolean,
      default: false
    },
    scrollWrapper: {
      type: Function,
      default: () => {
        return document.querySelector('#layout-root .ant-layout.content');
      }
    },
    // 延迟保存,保证行数据更新到最新
    delaySave: {
      type: Boolean,
      default: false
    },
    // 统一函数钩子， 目前只用在remove icon上
    hooks: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    // const {} = this;
    const { columns, rowKey } = this.$attrs;
    const slotsInfo = common.getTableSlots(columns);
    this.sortable = null;
    this.inited = false;
    this.editAllCache = null;
    return {
      data: [],
      rowKey: rowKey || 'key',
      ...slotsInfo,
      isAdd: false,
      isEdit: this.initEditStatus || false,
      // 表单
      formData: {},
      // 单行编辑弹窗
      modalVisible: false,
      modalParams: {
        ...modalDefaultProps,
        ...this.modalConfig
      },
      modalFormParams: {},
      modalData: {},
      // classMode: this.mode === 'list' ? 'table-edit-list-like' : '',
      instanceId: _.uniqueId('_tableEdit')
    };
  },
  computed: {
    tableProps() {
      return { ...defaultProps, ...this.$attrs, ...{ auth: this.auth } };
    },
    tableListeners() {
      return { ...this.$listeners, ...{} };
    },
    className() {
      let res = ['table-edit'];
      if (this.mode === 'list') {
        res.push('table-edit-list-like');
      }
      if (this.mode === 'compact') {
        res.push('table-edit-compact');
      }
      if (!this.initEditStatus && (this.isAdd || this.isEdit)) {
        res.push('table-edit-ing');
      }
      return res;
    }
  },
  created() {},
  mounted() {},
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  methods: {
    /**
     * 表格加载完毕
     */
    watchTableInited(type) {
      const { isEdit } = this;
      if (isEdit) {
        // 默认开启编辑
        this.editAll();
      } else {
        this.$nextTick(() => {
          const { table } = this.$refs;
          // if (type === 'init' && table) {
          if (table) {
            this.data = table.data;
          }
        });
      }
      if (type === 'init') {
        this.inited = true;
      }

      // 处理拖动排序
      if (this.draggable) {
        if (this.sortable) {
          this.sortable.destroy();
        }
        this.$nextTick(() => {
          const { table } = this.$refs;
          let el = table.$el.querySelector('tbody');
          let sortable = new Sortable(el, {
            animation: 500,
            handle: '.table-edit-drag-handler',
            dataIdAttr: 'data-row-key',
            onEnd: e => {
              const dataSource = table.data;
              console.log('排序：', sortable.toArray());
              this.data = sortable.toArray().map((key, index) => {
                let matchItem =
                  dataSource.find(item => item[this.rowKey] == key) || {};
                // matchItem[this.dragSortKey] = index + 1;
                return matchItem;
              });
            }
          });
          this.sortable = sortable;
        });
      }
    },
    /**
     * 根据col获取column信息
     */
    getColumInfo(col) {
      return this.columnMap[col] || {};
    },
    /**
     * 获取编辑信息
     */
    getEditInfo(col, row, record) {
      let editInfo = this.editConfig[col];
      editInfo = _.isFunction(editInfo)
        ? editInfo(row, record, this, this.getColValue(col, record), this.getEditRecord(record))
        : editInfo || {};
      const matchItem =
        compMap[editInfo.type] ||
        CustomTableEditComponents.comp[editInfo.type] ||
        compMap['Input'];

      return {
        ...editInfo,
        ...matchItem,
        setValue: (col, record, value) => {
          const { setValue } = matchItem;
          return setValue
            ? setValue.call(this, col, record, value)
            : (record[col] = value);
        },
        getValue: (col, record) => {
          const { getValue } = matchItem;
          return getValue ? getValue.call(this, col, record) : record[col];
        },
        rules: editInfo.rules || [],
        props: {
          ...(matchItem.props || {}),
          ...(editInfo.props || {}),
          ...(this.isEdit ? editInfo.editProps : {}),
          ...(editInfo.cellProps ? editInfo.cellProps(row, record) : {})
        },
        listeners: {
          ...(editInfo.listeners || {})
        }
      };
    },
    /**
     * 获取操作按钮信息
     */
    getActionBtns(record, index) {
      const { actionBtns } = this;
      return $Auth(
        this.auth.actionBtns,
        actionBtns
          .map(item => {
            let res = {};
            if (_.isFunction(item)) {
              res = item(record);
            } else if (_.isPlainObject(item)) {
              res = item;
            } else {
              res = {
                key: item
              };
            }
            // leastNum，剔除remove
            if (
              res &&
              res.key === 'remove' &&
              this.leastNum > 0 &&
              this.data.length <= this.leastNum
            ) {
              res = null;
            }
            return res;
          })
          .filter(item => item != null)
      );
    },
    /**
     * 获取编辑组件对应key
     */
    getEditKey(col, record) {
      if (!record) {
        return;
      }
      return `key_${record[this.rowKey]}_${col}`;
    },
    getEditRecord(record = {}) {
      let temp = {};
      Object.keys(this.formData).map(key => {
        const prefix = `key_${record[this.rowKey]}_`;
        if (key.startsWith(prefix)) {
          let col = key.replace(prefix, '');
          temp[col] = this.formData[key];
        }
      });
      return {
        ...record,
        ...temp
      }
    },
    /**
     * 获取表单项值
     */
    getColValue(col, record) {
      if (this.modalEdit) {
        return this.getModalData()[col];
      }
      return this.formData[this.getEditKey(col, record)];
    },
    /**
     * 获取modalData
     */
    getModalData() {
      if (this.$refs.modalForm) {
        return this.$refs.modalForm.getData() || {};
      }
      return {};
    },
    // 获取非编辑文本
    getPlainText(col, text, record, index) {
      let plainText = this.getEditInfo(col, index, record).plainText;
      return _.isFunction(plainText) ? plainText(text, record, index) : text;
    },
    /**
     * 处理行信息
     */
    dealTableLine(record, info = {}) {
      const newData = [...this.$refs.table.data];
      const rowKey = this.rowKey;
      const target = newData.filter(item => record[rowKey] === item[rowKey])[0];
      if (target) {
        Object.assign(target, info);
        this.data = newData;
      }
    },
    /**
     * 编辑行
     */
    async editLine(record, dealAll) {
      const { edit } = this.actionCbks;
      let info = {};
      if (edit) {
        info = await edit(record);
        info = _.isPlainObject(info) ? info : {};
      }
      // 弹窗编辑
      if (this.modalEdit) {
        if (this.modalCustom) {
          this.isEdit = true;
          this.dealTableLine(record, { _editable: true });
          this.$emit('modalCustom', record);
          this.modalVisible = true;
          return;
        }
        this.modalInit('edit', { ...record, ...info });
      } else {
        // 行内编辑
        this.edit(record, dealAll);
      }
    },
    edit(record, dealAll) {
      this.isEdit = true;
      const cache = _.merge({}, record);
      // 编辑组件赋值
      Object.keys(this.editConfig).forEach(col => {
        const { getValue } = this.getEditInfo(col);
        this.$set(
          this.formData,
          this.getEditKey(col, cache),
          getValue(col, cache)
        );
        // this.formData[this.getEditKey(col, cache)] = getValue(col, cache);
      });
      !dealAll && this.dealTableLine(record, { _editable: true });
    },
    /**
     * 新增行
     */
    addLine(record, params = {}) {
      if (!this.inited) {
        this.$message.warning(`初始化请求中，请稍后`);
        return false;
      }
      if (this.$refs.table && this.$refs.table.loading) {
        this.$message.warning(`请求中，请稍后`);
        return false;
      }
      if (this.type === 'lineEdit' && (this.isAdd || this.isEdit)) {
        this.$message.warning(`有未保存的项，请先处理`);
        return false;
      }
      // 弹窗新增
      if (this.modalEdit) {
        this.modalInit('add', record);
      } else {
        // 行内新增
        const { url, pagination } = this.$attrs;
        const { insertBefore } = params;
        // 如果分页，且往后插入
        if (!record && pagination !== false && !insertBefore) {
          const { data = [], total, pageSize = 10, pageNum } = this.$refs.table;
          if (!url) {
            // 不请求：先添加项，再跳转分页
            const flag = this.add(record, null, params);
            flag && this.pageJump(Math.ceil((data.length + 1) / pageSize));
          } else {
            // 请求：先跳转分页，再添加项
            const pageTotal = Math.ceil(total / pageSize);
            const current = total % pageSize > 0 ? pageTotal : pageTotal + 1;
            if (current != pageNum) {
              this.pageJump(current).then(() => {
                this.add(record, null, params);
              });
            } else {
              this.add(record, null, params);
            }
          }
          return;
        }
        this.add(record, null, params);
      }
    },
    add(record = {}, item, params = {}) {
      if (this.type === 'lineEdit' && (this.isAdd || this.isEdit)) {
        this.$message.warning(`有未保存的项，请先处理`);
        return;
      }
      if (this.type === 'lineEdit') {
        this.isAdd = true;
      }
      const { getSpecialInitItem } = params;
      const newData = [...this.$refs.table.data];
      const rowKey = this.rowKey;
      const index = newData.findIndex(item => record[rowKey] === item[rowKey]);
      let newItem = Object.assign(
        item || {},
        getSpecialInitItem ? getSpecialInitItem() : {},
        {
          [rowKey]: _.uniqueId('add_'),
          _editable: true
        }
      );
      // 添加初始化值
      Object.keys(this.editConfig).forEach(col => {
        let { initialValue } = this.getEditInfo(
          col,
          index >= 0 ? index + 1 : newData.length
        );
        initialValue = newItem[col] != null ? newItem[col] : initialValue;
        if (initialValue != null) {
          this.$set(this.formData, this.getEditKey(col, newItem), initialValue);
          if (!item) {
            Object.assign(newItem, { [col]: initialValue });
          }
        }
      });
      if (window.$AuthGetDefaultValue) {
        // 设置权限默认值
        const defaultValueObj = $AuthGetDefaultValue(
          this.auth.columns || {},
          {}
        );
        Object.keys(defaultValueObj).forEach(fieldKey => {
          if (!fieldKey.startsWith('_') && fieldKey != rowKey) {
            const dataKey = this.getEditKey(fieldKey, newItem);
            if (
              this.formData[dataKey] == null &&
              defaultValueObj[fieldKey] != null
            ) {
              this.$set(this.formData, dataKey, defaultValueObj[fieldKey]);
            }
          }
        });
      }
      // index >= 0
      //   ? newData.splice(index + 1, 0, newItem)
      //   : newData.push(newItem);
      const { insertBefore } = params;
      if (index >= 0) {
        newData.splice(insertBefore ? index : index + 1, 0, newItem);
      } else {
        insertBefore ? newData.unshift(newItem) : newData.push(newItem);
      }
      this.data = newData;
      // 定位
      this.$nextTick(() => {
        if (this.addIntoView) {
          const span = document.getElementById(
            this.instanceId + '_' + this.getEditKey('action', newItem)
          );
          let tr = _.get(span, 'parentNode.parentNode');
          // console.log(span, this.instanceId + '_' + this.getEditKey('action', newItem), tr);
          let scrollWrapper = this.scrollWrapper();
          if (scrollWrapper && tr && !this.isInTarget(tr, scrollWrapper)) {
            tr && tr.scrollIntoView(false);
          }
        }
      });

      this.$emit('change', this.data);
      return true;
    },
    // 删除行
    async remove(record) {
      // 如果有回调，使用回调
      const { remove } = this.actionCbks;
      if (remove && !this.isAdd) {
        await remove(record);
      } else {
        const newData = [...this.$refs.table.data];
        const rowKey = this.rowKey;
        const index = newData.findIndex(
          item => record[rowKey] === item[rowKey]
        );
        if (newData.length <= this.leastNum) {
          this.$message.warning(`最少有${this.leastNum}条`);
          return;
        }
        if (index >= 0) {
          newData.splice(index, 1);
          this.data = newData;
        }
      }
      if (this.type === 'lineEdit') {
        this.isAdd = false;
        this.isEdit = false;
      }

      this.$emit('change', this.data);
      this.hooks.remove && this.hooks.remove(this.data)
    },
    /**
     * 取消
     */
    cancel(record) {
      this.isEdit = false;
      this.dealTableLine(record, { _editable: false });
      if (this.type === 'lineEdit') {
        this.formData = {};
      }
    },
    /**
     * 保存
     */
    async save(record, dealAll) {
      let saveItem = {};
      if (this.delaySave) {
        await new Promise((resolve, reject) => {
          setTimeout(() => {
            resolve();
          }, 300);
        });
      }
      this.validate(async (valid, error) => {
        if (!valid) {
          this.$message.warning('请检查输入项');
          return;
        }
        // 统计saveItem
        Object.keys(this.editConfig).forEach(col => {
          const { setValue } = this.getEditInfo(col);
          this.formData.hasOwnProperty(this.getEditKey(col, record)) &&
            setValue(
              col,
              saveItem,
              this.formData[this.getEditKey(col, record)]
            );
        });
        if (!dealAll) {
          const { save } = this.actionCbks;
          if (save) {
            saveItem = window.$AuthGetDefaultValue
              ? $AuthGetDefaultValue(this.auth.columns || {}, saveItem)
              : saveItem;
            // 如果有save回调，使用回调（save必须为promise）
            await save({ ...record, ...saveItem });
          }
          this.dealTableLine(record, {
            ...saveItem,
            _editable: false
          });
        }
        this.isEdit = false;
        this.isAdd = false;

        this.$emit('change', this.data);
      });
      return saveItem;
    },
    /**
     * 实时回写，不校验且保持编辑状态
     */
    saving(record, saveAll) {
      let saveItem = {};
      if (this.modalEdit) {
        if (_.isPlainObject(record)) {
          // _.forEach(record, (item, key) => {
          //   this.$set(this.modalData, key, item === null ? undefined : item);
          // });
          this.$refs.modalForm.saving(record);
        }
      } else {
        // 统计saveItem
        let saveKeys = [
          ...(saveAll ? Object.keys(record) : []),
          ...Object.keys(this.editConfig)
        ];
        saveKeys.forEach(col => {
          const { setValue } = this.getEditInfo(col);
          setValue(col, saveItem, this.formData[this.getEditKey(col, record)]);
          this.$set(
            this.formData,
            this.getEditKey(col, record),
            record[col] !== undefined
              ? record[col] === null
                ? undefined
                : record[col]
              : saveItem[col]
          );
        });
      }

      // 回写
      if (this.modalEdit) {
        // this.$refs.modalForm.refresh();
        // this.modalData = Object.assign({}, this.modalData);
      } else if (this.type !== 'lineEdit') {
        this.dealTableLine(record, {
          ...saveItem,
          ...record
        });
      } else {
        this.data = [...this.$refs.table.data];
      }
    },
    savingAll(dataSource) {
      const { rowKey } = this;
      this.data = (dataSource || [...this.$refs.table.data]).map(item => {
        if (item[rowKey] == null) {
          item[rowKey] = _.uniqueId('init_');
        };
        return item;
      });
      if (this.isEdit) {
        // 默认开启编辑
        this.editAll();
      }
      // 更新表单值
      if (dataSource) {
        let newFormData = {};
        dataSource.forEach(record => {
          let saveItem = {};
          Object.keys(this.editConfig).forEach(col => {
            const { setValue } = this.getEditInfo(col);
            setValue(
              col,
              saveItem,
              this.formData[this.getEditKey(col, record)]
            );
            newFormData[this.getEditKey(col, record)] =
              record[col] !== undefined
                ? record[col] === null
                  ? undefined
                  : record[col]
                : saveItem[col];
          });
        });
        console.log(newFormData);
        this.formData = Object.assign({}, this.formData, newFormData);
      }
    },
    /**
     * 统一操作（仅本地操作，未请求接口）
     */
    editAll() {
      // if (this.isEdit) return;
      this.$nextTick(() => {
        this.isEdit = true;
        const newData = [...this.$refs.table.data];
        this.editAllCache = _.merge([], newData);
        newData.forEach(item => {
          item._editable = true;
          this.edit(item, true);
        });
        this.data = newData;
      });
    },
    cancelAll() {
      if (!this.isEdit) return;
      this.isEdit = false;
      const newData = this.editAllCache || [...this.$refs.table.data];
      newData.forEach(item => {
        item._editable = false;
      });
      this.data = newData;
      this.formData = {};
      this.editAllCache = null;
    },
    saveAll() {
      if (!this.isEdit) return;
      this.validate((valid, error) => {
        if (!valid) {
          this.$message.warning('请检查输入项');
          return;
        }
        this.isEdit = false;
        const newData = [...this.$refs.table.data];
        newData.forEach(item => {
          item._editable = false;
          Object.assign(item, this.save(item, true));
        });
        this.data = newData;
      });
    },
    /**
     * modal操作
     */
    modalInit(type, record = {}) {
      const rowKey = this.rowKey;
      this.modalVisible = true;
      if (type === 'add') {
        this.modalParams.title = this.modalParams.title.replace('编辑', '新增');
        // 新增
        let modalData = {
          [rowKey]: record[rowKey],
          _newRowKey: _.uniqueId('add_'),
          _editable: true
        };
        // 初始化值
        Object.keys(this.editConfig).forEach(col => {
          const { initialValue } = this.getEditInfo(col, row, record);
          if (initialValue != null) {
            modalData[col] = initialValue;
          }
        });
        // 设置权限默认值
        modalData = window.$AuthGetDefaultValue
          ? $AuthGetDefaultValue(this.auth.modalFormFields || {}, modalData)
          : modalData;
        this.modalData = modalData;
      } else {
        // 编辑
        this.modalParams.title = this.modalParams.title.replace('新增', '编辑');
        this.isEdit = true;
        this.dealTableLine(record, { _editable: true });
        let modalData = _.merge({}, record);
        Object.keys(this.editConfig).forEach(col => {
          const { getValue } = this.getEditInfo(col);
          modalData[col] = getValue(col, modalData);
        });
        this.modalData = modalData;
      }
      const row = this.$refs.table.data.findIndex(
        item => item[rowKey] == record[rowKey]
      );
      this.modalFormParams = Object.assign(
        {
          fields: $Auth(
            this.auth.modalFormFields,
            Object.keys(this.editConfig).map(col => {
              const dateTime = +new Date();
              return () => {
                const column = this.getColumInfo(col);
                const editInfo = this.getEditInfo(col, row, this.modalData);
                const { type, label, props = {} } = editInfo;
                return _.merge(
                  {
                    ...editInfo,
                    type: type ? type.split('_')[0] : 'Input',
                    key: col,
                    label: label || column.title
                  },
                  {
                    props: {
                      reqParams: {
                        ...(props.reqParams || {}),
                        _t: editInfo.showRefresh ? dateTime : ''
                      }
                    }
                  }
                );
              };
            })
          ),
          fixedLabel: true,
          layout: 'horizontal',
          labelCol: { span: 4 },
          wrapperCol: { span: 20 },
          colon: true
        },
        this.modalFormConfig
      );
    },
    modalCancel(record) {
      if (this.isEdit) {
        this.cancel(record || this.modalData);
      }
      this.modalVisible = false;
      this.modalData = {};
      this.$refs.modalForm && this.$refs.modalForm.resetFields();
    },
    modalValidate(cbk) {
      return this.$refs.modalForm.validate(cbk);
    },
    modalOk() {
      this.$refs.modalForm.validate(async (valid, error) => {
        if (!valid) {
          return;
        }
        const rowKey = this.rowKey;
        const { save } = this.actionCbks;
        let type = 'add';
        if (this.isEdit) {
          type = 'edit';
        }
        // 统计saveItem
        let saveItem = {};
        // 获取modalForm值
        let modalData = _.merge(this.$refs.modalForm.getData());
        Object.keys(this.editConfig).forEach(col => {
          const { setValue } = this.getEditInfo(col);
          setValue(col, saveItem, modalData[col]);
        });
        Object.assign(
          saveItem,
          { _editable: false },
          type === 'add'
            ? { [rowKey]: this.modalData._newRowKey }
            : { [rowKey]: this.modalData[rowKey] }
        );

        if (save) {
          // 如果有save回调，使用回调（save必须为promise）且成功后自己刷新
          let _data = { ...this.modalData, ...saveItem };
          _data = window.$AuthGetDefaultValue
            ? $AuthGetDefaultValue(this.auth.modalFormFields || {}, _data)
            : _data;
          await save(_data);
          const record = [...this.$refs.table.data].find(
            item => item[rowKey] == _data[rowKey]
          );
          if (record) {
            this.dealTableLine(record, { _editable: false });
          }
        } else {
          // 回填
          if (type === 'add') {
            const newData = [...this.$refs.table.data];
            newData.push({
              ...this.modalData,
              ...saveItem
            });
            this.data = newData;
          } else {
            // if (type === 'add') {
            //   this.add(modalData, saveItem);
            // } else {
            // }
            this.dealTableLine(this.modalData, saveItem);
          }
        }
        this.modalData = {};
        this.$refs.modalForm.resetFields();
        this.modalVisible = false;
        this.isEdit = false;
      });
    },
    /**
     * 表单操作
     */
    validate(cbk) {
      return this.$refs.form.validate(cbk);
    },
    validateRow(record, cbk, keys) {
      keys = keys || Object.keys(this.editConfig);
      let errList = [];
      let valid = true;
      this.$refs.form.validateField(
        keys.map(col => {
          return this.getEditKey(col, record);
        }),
        errMsg => {
          errList.push(errMsg || '');
        }
      );
      if (errList.filter(err => err != '').length > 0) {
        valid = false;
      }
      cbk && cbk(valid, errList);
    },
    resetFields() {
      this.formData = {};
      if (this.type === 'lineEdit') {
        const newData = [...this.$refs.table.data];
        newData.forEach(item => {
          item._editable = false;
        });
        this.data = newData.filter(
          itm => itm[this.rowKey] && !(itm[this.rowKey] + '').startsWith('add_')
        );
        this.isEdit = false;
        this.isAdd = false;
      }
      return this.$refs.form.resetFields();
    },
    /**
     * 刷新表格
     */
    refresh(dataSource, params, config) {
      const { reset = false } = params || {};
      this.resetFields();
      if (reset) {
        this.data = [...this.$refs.table.data];
        return;
      }
      if (this.$attrs.url) {
        this.$refs.table.refresh(dataSource, params, config);
      } else {
        this.savingAll(dataSource);
      }
    },
    /**
     * 获取数据
     */
    getData(params = {}) {
      let list = [];
      this.data.forEach((record, index) => {
        if (this.modalEdit) {
          list.push({
            ...record
          });
          return;
        }
        let saveItem = {};
        // 统计saveItem
        Object.keys(this.editConfig).forEach(col => {
          const { setValue, type } = this.getEditInfo(col, index, record);
          let val = this.formData[this.getEditKey(col, record)];
          if (val && type === 'Input') {
            val = _.isString(val) && val ? val.trim() : val;
          }
          if (params.nullString) {
            val = val === undefined ? '' : val;
          }
          setValue(col, saveItem, val);
        });
        list.push({
          ...record,
          ...saveItem
        });
      });
      return list;
    },
    getSelectedInfo() {
      const { table } = this.$refs;
      return table.getSelectedInfo();
    },
    pageJump(current) {
      return this.$refs.table.pageJump(current);
    },
    isInTarget(source, target) {
      let flag = false;
      let sourceRect = source.getBoundingClientRect();
      let { top, bottom } = target.getBoundingClientRect();
      if (sourceRect.top > top && sourceRect.bottom < bottom) {
        flag = true;
      }
      return flag;
    },
    getRowClassName(record, index) {
      const { rowClassName } = this.$attrs;
      let res = [record._editable && 'is-editable'];
      if (rowClassName) {
        res.push(rowClassName(record, index));
      }
      return res.filter(item => item).join(' ');
    }
  },
  watch: {
    dataSource: {
      handler(newVal = [], oldVal) {
        // console.log('Table new dataSource: ', newVal, oldVal);
        const { rowKey } = this;
        const data =
          newVal.length <= 0
            ? Array(this.leastNum)
                .fill('')
                .map(item => {
                  return {};
                })
            : newVal;
        this.data = data.map(item => {
          if (item[rowKey] == null) {
            item[rowKey] = _.uniqueId('init_');
          }
          return item;
        });
        if (this.isEdit) {
          // 默认开启编辑
          this.editAll();
        }
      },
      immediate: true
      // deep: true
    },
    '$attrs.columns': {
      handler(newVal = [], oldVal) {
        const slotsInfo = common.getTableSlots(newVal);
        this.tableScopedSlots = slotsInfo.tableScopedSlots;
        this.tableSlots = slotsInfo.tableSlots;
        this.columnMap = slotsInfo.columnMap;
      }
      // immediate: true
      // deep: true
    }
    // formData: {
    //   handler(newVal, oldVal) {
    //     console.log(newVal, oldVal, 777777778888);
    //   },
    //   // immediate: true,
    //   deep: true
    // }
  }
};
</script>

<style lang="less" scoped>
.table-edit {
  /deep/ .ant-table-content .ant-form-item {
    margin-bottom: 0;

    .ant-form-item-control {
      line-height: inherit;
      &.has-error {
        white-space: nowrap;
      }
    }
    .ant-form-explain {
      position: absolute;
      font-size: 12px;
      margin-top: 1px;
    }
  }
  /deep/ .action-btn-disabled {
    a,
    .anticon {
      color: rgba(0, 0, 0, 0.25) !important;
      cursor: not-allowed !important;
      pointer-events: none;
    }
  }
  /deep/ .action-icon {
    cursor: pointer;
  }

  /deep/ .ant-table {
    .ant-table-tbody > tr > td {
      padding: 12px 16px 20px 16px;
      &:last-child {
        white-space: normal;

        > span > span:last-child {
          .ant-divider {
            display: none;
          }
        }
      }
    }
    .ant-table-tbody {
      tr.is-editable {
        > td {
          white-space: normal;
        }
      }
    }
  }

  // select
  /deep/ .ant-select-selection-selected-value {
    white-space: normal;
    width: 100%;
    height: 24px;
  }

  &.empty-no-border {
    /deep/ .ant-table-placeholder {
      border-bottom: 0;
    }
  }
}
// 可编辑表格列表样式
.table-edit-list-like {
  /deep/ .ant-table {
    thead {
      display: none;
    }
    .ant-table-tbody > tr {
      background: #ffffff !important;
      > td {
        border-bottom: none;
      }
    }
    .ant-table-tbody
      > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
      > td {
      background: transparent;
    }
    .ant-table-content td {
      padding: 10px 16px;
    }
    .ant-table-placeholder {
      display: none;
    }
  }
}

// 紧凑模式
.table-edit-compact {
  /deep/ .ant-table {
    .ant-table-tbody > tr {
      > td {
        background: #ffffff;
        padding: 8px;
        border-bottom: none;
        .ant-form-item {
          padding-bottom: 0;
        }

        &:first-child {
          padding-left: 0px;
        }
      }
    }
  }
}

.table-edit-ing {
  /deep/ .custom-table {
    .ant-table-pagination.ant-pagination {
      pointer-events: none;
      // cursor: not-allowed;
      opacity: 0.5;
    }
  }
}
</style>
<style lang="less">
.action-btn-disabled {
  a,
  .anticon {
    color: rgba(0, 0, 0, 0.25) !important;
    cursor: not-allowed !important;
    pointer-events: none;
  }
}
</style>