<!--
 * @Descripttion: 展示样式
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2021-01-25 15:04:35
-->

<template>
  <Fullscreen class="custom-prettier" @copy="onCopy" :needCopy="true">
    <pre v-html="formatVal"></pre>
  </Fullscreen>
</template>

<script>
import Fullscreen from '../FullScreen';
// import { format as sqlFormatter } from 'sql-formatter';
import xmlFormatter from 'xml-formatter';
import jsonFormatter from 'json-beautify';
import hljs from 'highlight.js';
// import 'highlight.js/styles/vs.css';

export default {
  components: { Fullscreen },
  props: {
    value: {
      type: String | Object
    },
    type: {
      type: String,
      default: 'sql'
    },
    dbType: String,
    format: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  computed: {
    formatVal() {
      const { value, type } = this;
      let res = value;
      if (this.format) {
        try {
          switch (type) {
            case 'sql':
              let defaultOptions = {};
              if (this.dbType) {
                defaultOptions = FORMAT_CONFIG.SqlFormat.getConfig(this.dbType);
              }
              res = sqlFormatter.format(value || '', defaultOptions);
              break;
            case 'xml':
              res = value ? xmlFormatter(value || '') : '';
              break;
            case 'json':
              let jsonObj = value;
              if (_.isString(value) && value) {
                jsonObj = JSON.parse(value);
              }
              res = value ? jsonFormatter(jsonObj, null, 2, 20) : '';
              break;
            default:
              break;
          }
        } catch (e) {
          // this.$message.warning('格式化错误，请检查');
          console.log(e);
        }
      }

      return hljs.highlight(type, res).value;
    }
  },
  mounted() {},
  methods: {
    onCopy() {
      CommonUtil.copy({
        value: this.value,
        callback: () => {
          this.$message.success('复制成功');
        }
      });
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.custom-prettier {
  background: rgba(15, 120, 251, 0.06);
  margin-bottom: 12px;

  > pre {
    margin-bottom: 0;
    padding: 16px;
  }
  // /deep/ .hljs-keyword {
  //   color: #0f78fb;
  // }
  // /deep/ .hljs-function {
  //   color: #0f78fb;
  // }
  /deep/ .hljs-number {
    color: blue;
  }
  /deep/ .hljs-string {
    color: green;
  }
  // /deep/.hljs-special {
  //   color: #ffb625;
  // }
  // /deep/ .hljs-bracket {
  //   color: #ffb625;
  // }

  &.fullscreen {
    > pre {
      height: 100% !important;
    }
  }
}
</style>