<template>
  <div
    :class="['fdc-canvas-wrapper', hasGrid && 'theme-grid']"
    @mousedown="onMousedown"
    @mouseup="onMouseup"
  >
    <div
      id="fdcCanvas"
      ref="canvas"
      class="fdc-canvas"
      :style="{visibility: data.nodeList && data.nodeList.length > 0 ? 'visible' : 'hidden'}"
    >
      <template v-for="node in data.nodeList">
        <div
          :key="node.id"
          :id="node.id"
          :class="['fdc-node', node.className || '', node.disabled && 'disabled']"
          :style="getNodeStyle(node)"
          @click.stop="(e) => clickNode(e, node)"
        >
          <template v-if="!node.useSlot">
            <!-- 节点类型的图标 -->
            <div class="flow-node-drag" v-if="!viewOnly">
              <a-icon type="share-alt" />
            </div>
            <!-- 节点名称 -->
            <div class="fdc-node-text" v-if="!node.slotNode">{{node.name}}</div>
            <slot name="node" :data="node" v-else></slot>
          </template>
          <slot name="node" :data="node" v-else></slot>
        </div>
      </template>
    </div>
    <!-- minimap -->
    <div
      class="fdc-canvas-minimap"
      :style="{visibility: data.nodeList && data.nodeList.length > 0 ? 'visible' : 'hidden'}"
      v-if="hasMinimap"
    >
      <div class="fdc-canvas-minimap-elements" :style="minimap.nodesAreaStyle">
        <template v-for="node in data.nodeList">
          <div
            :key="node.id"
            :style="{ left: node.left - minimap.nodesAreaPos.left + 'px', top: node.top - minimap.nodesAreaPos.top + 'px' }"
          ></div>
        </template>
      </div>
      <div class="fdc-canvas-minimap-viewport" :style="minimap.viewportStyle"></div>
    </div>
    <!-- empty展示 -->
    <div v-show="!data.nodeList || data.nodeList.length <= 0" class="fdc-empty-tips">
      <div v-if="!viewOnly">请拖动节点到此处</div>
      <div v-else>暂无节点信息</div>
    </div>
    <!-- 工具栏 -->
    <div class="fdc-tools">
      <a-tooltip>
        <template slot="title">回到中心</template>
        <a-icon type="home" @click.stop="toHome" />
      </a-tooltip>
      <a-tooltip>
        <template slot="title">删除元素</template>
        <a-icon type="delete" @click.stop="removeElement" v-if="!viewOnly" />
      </a-tooltip>
    </div>
    <!-- lineTips -->
    <div>
      <slot name="lineTips"></slot>
    </div>
  </div>
</template>

<script>
import { jsPlumb } from 'jsplumb';
import { flowMixin } from './mixins';
import panzoom from 'panzoom';
import Draggable from '@/utils/drag';
import ResizeObserver from 'resize-observer-polyfill';
console.log(jsPlumb, 'jsPlumb');

export default {
  components: {},
  props: {
    value: {
      type: Object,
      default: () => ({
        nodeList: [],
        lineList: []
      })
    },
    lineLabelMap: {
      type: Object,
      default: () => ({})
    },
    viewOnly: {
      type: [Boolean, String],
      default: false
    },
    baseOptions: {
      type: Object,
      default: () => {
        return {
          jsplumbSetting: {},
          jsplumbConnectOptions: {},
          jsplumbSourceOptions: {},
          jsplumbTargetOptions: {}
        };
      }
    },
    hasGrid: {
      type: Boolean,
      default: false
    },
    clearActiveObjArea: {
      type: Array,
      default: () => []
    },
    hasMinimap: {
      type: Boolean,
      default: false
    },
    asyncCbks: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      data: this.value,
      // 是否加载完毕标志位
      flowLoaded: false,
      // 缩放比例
      zoom: 1,
      // panzoom实例
      pan: null,
      // 拖动
      isDragging: false,
      // activeObj
      activeObj: null,
      // minimap
      minimap: {
        nodesAreaStyle: {},
        nodesAreaPos: { left: 0, top: 0 },
        viewportStyle: {}
      },
      minimapRate: 1
    };
  },
  // 一些基础配置移动该文件中
  mixins: [flowMixin],
  mounted() {
    // 画布添加拖动缩放
    this.pan = panzoom(this.getCanvas(), {
      // maxZoom: 1.5,
      // minZoom: 0.5
      smoothScroll: false,
      beforeMouseDown: e => {
        // allow mouse-down panning only if altKey is down. Otherwise - ignore
        let forbidden = false;
        if (
          this.hasMinimap &&
          _.isString(e.target.className) &&
          ['fdc-canvas-minimap'].find(item => e.target.className.includes(item))
        ) {
          forbidden = true;
        }
        return forbidden;
      }
    });
    this.pan.on('zoom', e => {
      this.setZoom(e.getTransform().scale);
      this.updateMinimapViewPort();
    });
    let startPos = [];
    let panFlag = false;
    this.pan.on('panstart', e => {
      panFlag = true;
      this.isDragging = false;
      startPos = [e.getTransform().x, e.getTransform().y];
    });
    this.pan.on('pan', e => {
      // console.log(e)
      if (
        panFlag &&
        (Math.abs(e.getTransform().x - startPos[0]) > 3 ||
          Math.abs(e.getTransform().y - startPos[1]) > 3)
      ) {
        this.isDragging = true;
        this.updateMinimapViewPort();
      }
    });
    this.pan.on('panend', e => {
      panFlag = false;
      setTimeout(() => {
        this.isDragging = false;
      });
    });
    console.log(this.pan, 'panzoom instance');
    this.setPanzoomStatus();

    // 初始化 jsPlumb
    this.initJsPlumb();

    // body事件
    document.addEventListener(
      'click',
      (this.bodyClick = e => {
        const { clearActiveObjArea } = this;
        if (clearActiveObjArea.length > 0) {
          if (
            _.isString(e.target.className) &&
            clearActiveObjArea.find(item => e.target.className.includes(item))
          ) {
            this.clearActiveObj();
          }
        } else {
          this.clearActiveObj();
        }
      })
    );
    // window.addEventListener(
    //   'resize',
    //   (this.bodyResize = _.debounce(e => {
    //     this.updateMinimap();
    //   }, 100))
    // );

    // minimap添加拖动
    const minimapViewport = this.$el.querySelector(
      '.fdc-canvas-minimap-viewport'
    );
    if (minimapViewport) {
      this.minimapDragInstance = new Draggable({
        el: minimapViewport,
        // handler: '.fpc-spc-item-handler',
        // clone: true,
        onStart: (e, moveItem) => {},
        onMove: (e, moveItem) => {
          this.minimapViewportResetPos(e, moveItem);
        },
        onEnd: (e, moveItem) => {}
      });

      this.ro = new ResizeObserver((entries, observer) => {
        this.updateMinimap();
      });
      this.ro.observe(this.$el);
    }
  },
  created() {},
  beforeDestroy() {
    if (this.pan) {
      this.pan.dispose();
      this.pan = null;
    }
    if (this.jsPlumb) {
      this.jsPlumb.clear();
      this.jsPlumb = null;
    }
    if (this.minimapDragInstance) {
      this.minimapDragInstance.destroy();
      this.minimapDragInstance = null;
    }
    document.removeEventListener('click', this.bodyClick);
    // window.removeEventListener('resize', this.bodyResize);
    if (this.ro) {
      this.ro.unobserve(this.$el);
      this.ro = null;
    }
  },
  methods: {
    /**
     * 初始化流程图
     */
    initJsPlumb() {
      this.flowLoaded = false;
      this.jsPlumb = jsPlumb.getInstance();
      console.log(this.jsPlumb, 'jsPlumb instance');
      this.jsPlumb.ready(() => {
        // 导入默认配置
        this.jsPlumb.importDefaults(
          Object.assign(
            {},
            this.jsplumbSetting,
            this.baseOptions.jsplumbSetting || {}
          )
        );
        // 会使整个jsPlumb立即重绘。
        this.jsPlumb.setSuspendDrawing(false, true);
        // 初始化节点
        this.loadFlow();
        // 单点击了连接线,
        this.jsPlumb.bind('click', (conn, originalEvent) => {
          // this.activeElement.type = 'line';
          // this.activeElement.sourceId = conn.sourceId;
          // this.activeElement.targetId = conn.targetId;
          // this.$refs.nodeForm.lineInit({
          //   from: conn.sourceId,
          //   to: conn.targetId,
          //   label: conn.getLabel()
          // });
          // console.log(conn, originalEvent, this.isDragging, 898);
          if (this.isDragging || (this.viewOnly && this.viewOnly !== 'onlyClickElement')) {
            return;
          }

          const { nodeList, lineList } = this.data;
          const line = lineList.find(
            item => item.from === conn.sourceId && item.to === conn.targetId
          );

          if (line.disabled) {
            console.log('line disabled');
            return;
          }
          conn.setPaintStyle({
            stroke: '#b0b2b5',
            strokeWidth: 2
          });
          this.setActiveObj('line', conn);
          this.$emit('clickElement', {
            type: 'line',
            data: {
              conn,
              line,
              fromNode: nodeList.find(item => item.id === line.from),
              toNode: nodeList.find(item => item.id === line.to)
            }
          });
          originalEvent.stopPropagation();
        });
        // 连线
        // this.jsPlumb.bind('connection', evt => {
        //   let from = evt.source.id;
        //   let to = evt.target.id;
        //   this.addLine(evt.connection, from, to);
        // });

        // 删除连线回调
        // this.jsPlumb.bind('connectionDetached', evt => {
        //   this.deleteLine(evt.sourceId, evt.targetId);
        // });

        // // 改变线的连接节点
        // this.jsPlumb.bind('connectionMoved', evt => {
        //   this.changeLine(evt.originalSourceId, evt.originalTargetId);
        // });

        // // 连线右击
        // this.jsPlumb.bind('contextmenu', evt => {
        //   console.log('contextmenu', evt);
        // });

        // 连线
        this.jsPlumb.bind('beforeDrop', evt => {
          let from = evt.sourceId;
          let to = evt.targetId;
          if (this.hasLine(from, to)) {
            this.$message.error('该关系已存在,不允许重复创建');
            return false;
          }
          // if (this.hashOppositeLine(from, to)) {
          //   this.$message.error('不支持两个节点之间连线回环');
          //   return false;
          // }
          // this.$message.success('连接成功');
          // 调用统一方法创建连线
          const line = this.addLine(from, to);
          this.$emit('addLine', line);
          return false;
        });

        // // beforeDetach
        // this.jsPlumb.bind('beforeDetach', evt => {
        //   console.log('beforeDetach', evt);
        // });

        // this.jsPlumb.setContainer(this.$el.querySelector('.fdc-canvas'));

        // console.log(this.getCanvasStyle());
      });
    },
    /**
     * 加载流程图
     */
    loadFlow() {
      // 初始化节点
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        this.initNode(node);
      }
      // 初始化连线
      for (let i = 0; i < this.data.lineList.length; i++) {
        let line = this.data.lineList[i];
        this.initLine(line);
      }
      this.$nextTick(() => {
        this.flowLoaded = true;
        this.$emit('loaded');
      });
    },
    /**
     * 重绘
     */
    reload(data) {
      if (this.jsPlumb) {
        this.jsPlumb.clear();
        this.jsPlumb.unbind('click');
        this.jsPlumb = null;
      }
      this.initJsPlumb();
    },
    /**
     * 初始化node
     */
    initNode(node) {
      // 设置源点，可以拖出线连接其他节点
      this.jsPlumb.makeSource(
        node.id,
        _.merge(
          {},
          this.jsplumbSourceOptions,
          this.baseOptions.jsplumbSourceOptions || {}
        )
      );
      // 设置目标点，其他源点拖出的线可以连接该节点
      this.jsPlumb.makeTarget(
        node.id,
        _.merge(
          {},
          this.jsplumbTargetOptions,
          this.baseOptions.jsplumbTargetOptions || {}
        )
      );
      if (!node.viewOnly && !this.viewOnly) {
        this.jsPlumb.draggable(node.id, {
          // containment: 'parent',
          start: (params = {}) => {
            const { e } = params;
            params.el._isDrag = false;
            params.el._startPos = [e.pageX, e.pageY];
            this.$emit('nodeDragStart', node);
          },
          drag: (params = {}) => {
            const { e } = params;
            const startPos = params.el._startPos;
            if (
              Math.abs(e.pageX - startPos[0]) > 3 ||
              Math.abs(e.pageY - startPos[1]) > 3
            ) {
              params.el._isDrag = true;
              this.$emit('nodeDragging', node);
            }
          },
          stop: params => {
            console.log(params);
            // 拖拽节点结束后的对调
            this.updateNodePos(node, params.el);
            this.$emit('nodeDragStop', node);
          }
        });
      }
    },
    /**
     * 添加节点
     */
    addNode(node = {}, e) {
      // 添加id
      node.id = _.uniqueId('add_');
      // 添加pos
      const canvas = this.getCanvas();
      const {
        left: canvasLeft,
        top: canvasTop
      } = canvas.getBoundingClientRect();
      node.left = (e.pageX - canvasLeft) / this.zoom;
      node.top = (e.pageY - canvasTop) / this.zoom;

      const { nodeList } = this.data;
      this.$set(this.data, 'nodeList', [...nodeList, node]);
      this.$nextTick(() => {
        this.initNode(node);
        this.setPanzoomStatus();
        this.updateMinimap();
      });
    },
    /**
     * 更新节点
     */
    updateNode() {},
    /**
     * 更新节点位置
     */
    updateNodePos(node, el) {
      const canvas = this.getCanvas();
      const {
        left: canvasLeft,
        top: canvasTop
      } = canvas.getBoundingClientRect();
      const { left, top } = el.getBoundingClientRect();
      this.$set(node, 'left', (left - canvasLeft) / this.zoom);
      this.$set(node, 'top', (top - canvasTop) / this.zoom);
      this.updateMinimap();
    },
    /**
     * 删除节点
     */
    removeNode(id) {
      this.jsPlumb.remove(id);
      let { nodeList, lineList } = this.data;
      this.$set(
        this.data,
        'nodeList',
        nodeList.filter(item => item.id != id)
      );
      this.$set(
        this.data,
        'lineList',
        lineList.filter(item => item.from != id && item.to != id)
      );
      this.$nextTick(() => {
        this.setPanzoomStatus();
        this.updateMinimap();
      });
      return {
        removeLines: lineList.filter(item => item.from == id || item.to == id)
      };
    },
    /**
     * 点击节点
     */
    clickNode(e, node) {
      let el = e.currentTarget;
      if (el._isDrag || (this.viewOnly && this.viewOnly !== 'onlyClickElement')) {
        el._isDrag = false;
        return;
      }
      let classes = el.className.split(' ');
      classes.push('active');
      el.className = [...new Set(classes)].join(' ');
      this.setActiveObj('node', el);
      this.$emit('clickElement', {
        type: 'node',
        data: {
          node
        }
      });
    },
    /**
     * 初始化line
     */
    initLine(line) {
      let connParam = {
        source: line.from,
        target: line.to,
        label: line.label ? line.label : '',
        connector: line.connector ? line.connector : '',
        anchors: line.anchors ? line.anchors : undefined,
        paintStyle: line.paintStyle ? line.paintStyle : undefined
      };
      return this.jsPlumb.connect(connParam, {
        ..._.merge(
          {},
          this.jsplumbConnectOptions,
          this.baseOptions.jsplumbConnectOptions || {}
        ),
        labelStyle: {
          cssClass: `flowLabel ${line.condition || ''}`
        },
        overlays: [
          [
            'Custom',
            {
              create(component) {
                const ele = document.createElement('div');
                const content = document.getElementById(
                  `flow_line_${line.from}_${line.to}`
                );
                content && ele.appendChild(content);
                // ele.innerHTML = '<a>dfdfd</a>';
                return ele;
              },
              location: 0.5,
              id: 'custom'
            }
          ]
        ]
      });
    },
    /**
     * 添加line
     */
    addLine(from, to) {
      const { lineLabelMap } = this;
      const matchItem = lineLabelMap['default'] || {};

      const conn = this.initLine({
        from,
        to,
        label: matchItem.label,
        condition: matchItem.value
      });

      const line = {
        from,
        to,
        condition: matchItem.value,
        conn
      };
      this.data.lineList.push(line);
      return line;
    },
    /**
     * 更新line
     */
    updateLine(data = {}) {
      const { from, to, condition } = data;
      const { lineList } = this.data;
      const conn = this.jsPlumb.getConnections({
        source: from,
        target: to
      })[0];
      const line = lineList.find(item => item.from === from && item.to === to);
      if (line) {
        this.$set(line, 'condition', condition);
      }
      if (conn) {
        const { lineLabelMap } = this;
        const matchItem = lineLabelMap[condition];
        if (matchItem) {
          conn.setLabel(matchItem.label);

          Object.keys(lineLabelMap).forEach(item => {
            conn.removeClass(item);
          });
          conn.addClass(matchItem.value);
        }
      }

      // overlays
    },
    /**
     * 删除line
     */
    removeLine(conn) {
      this.clearActiveObj();
      this.jsPlumb.deleteConnection(conn);
      let { lineList } = this.data;
      this.$set(
        this.data,
        'lineList',
        lineList.filter(
          item => item.from != conn.sourceId || item.to != conn.targetId
        )
      );
    },
    removeLineByFromTo(from, to) {
      const conn = this.jsPlumb.getConnections({
        source: from,
        target: to
      })[0];
      this.removeLine(conn);
    },
    /**
     * 获取线实例
     */
    getConnection(from, to) {
      const conn = this.jsPlumb.getConnections({
        source: from,
        target: to
      })[0];
      return conn;
    },
    /**
     * 获取node相关线
     */
    getLinesByNode(node) {
      let id = node.id;
      let { lineList } = this.data;
      return lineList.filter(item => item.from == id || item.to == id);
    },
    /**
     * 删除元素
     */
    removeElement() {
      if (!this.activeObj) {
        this.$message.warning('请选择需要删除的元素');
        return;
      }
      const { type, el } = this.activeObj;
      this.$confirm({
        title: '确认删除',
        onOk: () => {
          const asyncRemove = this.asyncCbks['remove'];
          if (asyncRemove) {
            asyncRemove(this.activeObj);
            return;
          }
          if (type === 'node') {
            this.removeNode(el.id);
          } else {
            this.removeLine(el);
          }
          this.$emit('removeElement');
        }
      });
    },
    /**
     * 清空
     */
    clear() {
      this.data.nodeList.forEach(node => {
        this.jsPlumb.remove(node.id);
      });
      this.$set(this.data, 'nodeList', []);
      this.$set(this.data, 'lineList', []);
      this.$nextTick(() => {
        this.setPanzoomStatus();
      });
    },
    /**
     * 设置activeObj
     */
    setActiveObj(type, el) {
      if (this.activeObj && this.activeObj.el === el) {
        // 重复点击
        console.log('重复点击');
        return;
      }
      this.clearActiveObj();
      this.activeObj = {
        type,
        el
      };
    },
    /**
     * 清空activeObj
     */
    clearActiveObj() {
      if (this.activeObj) {
        const { type, el } = this.activeObj;
        if (type === 'node') {
          let classes = el.className.split(' ');
          el.className = classes.filter(item => item !== 'active').join(' ');
        } else {
          el.setPaintStyle({
            stroke: '#E0E3E7',
            strokeWidth: 1
          });
        }
        this.activeObj = null;
      }
    },
    /**
     * 回到home点
     */
    toHome() {
      const { nodeList = [] } = this.data;
      if (nodeList.length <= 0) {
        return;
      }
      const { x, y, rate } = this.getHomeInfo();
      this.pan.zoomTo(0, 0, rate / this.zoom);
      this.pan.moveTo(x, y);
      this.isDragging = false;
      this.updateMinimapViewPort();
    },
    /**
     * 设置缩放
     */
    setZoom(zoom = 1) {
      this.jsPlumb.setZoom(zoom);
      this.zoom = zoom;
    },
    setPanzoomStatus() {
      const { nodeList = [] } = this.data;
      if (this.pan) {
        if (nodeList.length > 0 && !this.viewOnly) {
          this.pan.resume();
        } else {
          this.pan.pause();
        }
      }
    },
    /**
     * 监听mousedown
     */
    onMousedown(e) {
      if (e.target === this.$el) {
        e.target.style.cursor = 'grabbing';
      }
    },
    /**
     * 监听mouseup
     */
    onMouseup(e) {
      if (e.target === this.$el) {
        e.target.style.cursor = 'grab';
      }
    },
    /**
     * 获取中心点信息
     */
    getHomeInfo() {
      const canvasStyle = this.getCanvasStyle();
      const canvasWrapper = this.$el;
      const centerPos = {
        left: canvasWrapper.clientWidth / 2,
        top: canvasWrapper.clientHeight / 2
      };

      // rate
      // const widthRate = canvasWrapper.clientWidth / canvasStyle.width;
      // const heightRate = canvasWrapper.clientHeight / canvasStyle.height;
      // let rate = widthRate < heightRate ? widthRate : heightRate;
      // rate = isNaN(rate) ? 1 : rate > 0.9 ? 0.9 : rate * 0.9;
      // console.log(rate, 'rate');
      let rate = this.getFillContainerRate({
        container: this.$el,
        nodesAreaWidth: canvasStyle.width,
        nodesAreaHeight: canvasStyle.height
      });

      // spans
      const spans = {
        left: centerPos.left - canvasStyle.centerLeft * rate,
        top: centerPos.top - canvasStyle.centerTop * rate
      };

      return {
        x: spans.left,
        y: spans.top,
        rate
      };
    },
    /**
     * 获取区域填充container比例
     */
    getFillContainerRate({ container, nodesAreaWidth, nodesAreaHeight }) {
      const widthRate = container.clientWidth / nodesAreaWidth;
      const heightRate = container.clientHeight / nodesAreaHeight;
      let rate = widthRate < heightRate ? widthRate : heightRate;
      rate = isNaN(rate) ? 1 : rate > 0.9 ? 0.9 : rate * 0.9;

      return rate;
    },
    /**
     * 获取node样式
     */
    getNodeStyle(node = {}) {
      return {
        left: node.left + 'px',
        top: node.top + 'px'
      };
    },
    /**
     * 获取canvas样式
     */
    getCanvasStyle() {
      const { nodeList = [] } = this.data;
      // console.log(nodeList, 89898);
      let leftItem = _.minBy(nodeList, 'left');
      let topItem = _.minBy(nodeList, 'top');
      let rightItem = _.maxBy(nodeList, 'left');
      let bottomItem = _.maxBy(nodeList, 'top');
      // 最右的宽、最下的高
      let rightItemWidth = document.getElementById(rightItem.id).clientWidth;
      let bottomItemHeight = document.getElementById(bottomItem.id)
        .clientHeight;
      // 内容区域宽高
      let width = rightItem.left + rightItemWidth - leftItem.left;
      let height = bottomItem.top + bottomItemHeight - topItem.top;

      return {
        left: leftItem.left,
        right: rightItem.left + rightItemWidth,
        top: topItem.top,
        bottom: bottomItem.top + bottomItemHeight,
        centerLeft: leftItem.left + width / 2,
        centerTop: topItem.top + height / 2,
        width,
        height
      };
    },
    /**
     * 获取wrapper
     */
    getContainer() {
      return this.$el;
    },
    /**
     * 获取canvas
     */
    getCanvas() {
      return this.$el.querySelector('.fdc-canvas');
    },
    /**
     * 获取data
     */
    getData() {
      return this.data;
    },
    /**
     * 是否已经有线连接
     */
    hasLine(from, to) {
      const { lineList } = this.data;
      let flag = false;
      _.forEach(lineList, item => {
        if (item.from === from && item.to === to) {
          flag = true;
          return false;
        }
      });
      return flag;
    },
    /**
     * 是否已经有相反线连接
     */
    hashOppositeLine(from, to) {
      const { lineList } = this.data;
      let flag = false;
      _.forEach(lineList, item => {
        if (item.from === to && item.to === from) {
          flag = true;
          return false;
        }
      });
      return flag;
    },
    /**
     * 获取minimap信息
     */
    getMinimapInfo() {
      if (!this.$el || this.data.nodeList.length <= 0) return {};
      const canvasStyle = this.getCanvasStyle();
      const minimapWrapper = this.$el.querySelector('.fdc-canvas-minimap');
      let rate = this.getFillContainerRate({
        container: minimapWrapper,
        nodesAreaWidth: canvasStyle.width,
        nodesAreaHeight: canvasStyle.height
      });
      return {
        rate,
        nodesAreaPos: {
          left: canvasStyle.left,
          top: canvasStyle.top
        },
        nodesAreaStyle: {
          left:
            (minimapWrapper.clientWidth - canvasStyle.width * rate) / 2 + 'px',
          top:
            (minimapWrapper.clientHeight - canvasStyle.height * rate) / 2 +
            'px',
          transform: `scale(${rate})`,
          transformOrigin: '0px 0px 0px'
        }
      };
    },
    /**
     * 获取viewport信息
     */
    getViewpotInfo() {
      const transform = this.pan.getTransform();
      const canvasStyle = this.getCanvasStyle();
      const initRate = this.minimapRate;
      // const minimapElements = this.$el.querySelector(
      //   '.fdc-canvas-minimap-elements'
      // );
      const nodesAreaStyle = this.minimap.nodesAreaStyle || {};
      const elementsLeft = this.getPxVal(nodesAreaStyle.left);
      const elementsTop = this.getPxVal(nodesAreaStyle.top);

      // 计算viewport rate
      const widthRate =
        this.$el.clientWidth / (canvasStyle.width * transform.scale);
      const viewportWidth = canvasStyle.width * initRate * widthRate;
      const rate = viewportWidth / this.$el.clientWidth;

      // console.log(transform, canvasStyle, initRate, elementsLeft, elementsTop);

      return {
        rate,
        left:
          elementsLeft -
          (transform.x + canvasStyle.left * transform.scale) * rate +
          'px',
        top:
          elementsTop -
          (transform.y + canvasStyle.top * transform.scale) * rate +
          'px',
        transform: `scale(${rate})`
      };
    },
    /**
     * 更新minimap viewport
     */
    updateMinimapViewPort() {
      if (!this.hasMinimap) return;
      if (!this.$el || this.data.nodeList.length <= 0) return;
      // console.log('updateMinimapViewPort...');
      const viewportInfo = this.getViewpotInfo();

      this.$set(
        this.minimap,
        'viewportStyle',
        Object.assign({}, this.minimap.viewportStyle, viewportInfo)
      );
    },
    /**
     * 更新minimap
     */
    updateMinimap() {
      if (!this.hasMinimap) return;
      if (!this.$el || this.data.nodeList.length <= 0) return;
      // console.log('updateMinimap...');
      // 设置nodes
      const info = this.getMinimapInfo() || {};
      this.$set(this.minimap, 'nodesAreaStyle', info.nodesAreaStyle || {});
      this.$set(
        this.minimap,
        'nodesAreaPos',
        info.nodesAreaPos || { left: 0, top: 0 }
      );
      this.minimapRate = info.rate;

      // 设置viewport
      const { clientWidth, clientHeight } = this.$el;
      // rate = isNaN(rate) ? 1 : rate > 0.9 ? 0.9 : rate * 0.9;
      const viewportInfo = this.getViewpotInfo();
      this.$set(
        this.minimap,
        'viewportStyle',
        Object.assign(
          {},
          this.minimap.viewportStyle,
          {
            width: clientWidth + 'px',
            height: clientHeight + 'px',
            transform: `scale(${info.rate})`,
            transformOrigin: '0px 0px 0px'
          },
          viewportInfo
        )
      );
    },
    /**
     * 拖动minimap viewport 回设置
     */
    minimapViewportResetPos(e, moveItem) {
      if (!this.hasMinimap) return;
      if (!this.$el || this.data.nodeList.length <= 0) return;
      const { rate } = this.minimap.viewportStyle || {};
      const transform = this.pan.getTransform();
      const canvasStyle = this.getCanvasStyle();
      // const minimapElements = this.$el.querySelector(
      //   '.fdc-canvas-minimap-elements'
      // );
      const nodesAreaStyle = this.minimap.nodesAreaStyle || {};
      const elementsLeft = this.getPxVal(nodesAreaStyle.left);
      const elementsTop = this.getPxVal(nodesAreaStyle.top);

      let x =
        -(this.getPxVal(moveItem.style.left) - elementsLeft) / rate -
        canvasStyle.left * transform.scale;
      let y =
        -(this.getPxVal(moveItem.style.top) - elementsTop) / rate -
        canvasStyle.top * transform.scale;

      this.pan.moveTo(x, y);
    },
    getPxVal(px) {
      let res = parseFloat((px + '').replace('px', ''));
      return _.isNaN(res) ? 0 : res;
    }
  },
  watch: {
    value(newVal, oldVal) {
      // console.log(newVal, 33333);
      this.data = newVal;
      this.$nextTick(() => {
        this.reload();
        this.setPanzoomStatus();
        this.updateMinimap();
      });
    }
  }
};
</script>

<style lang="less" scoped>
.fdc-canvas-wrapper {
  flex-grow: 1;
  background: #f9f9f9;
  border: 1px solid #f2f2f2;
  position: relative;
  overflow: hidden;
  cursor: grab;
  outline: none;

  &.theme-grid {
    background-image: linear-gradient(90deg, #f5f5f5 1px, transparent 0),
      linear-gradient(#f5f5f5 1px, transparent 0) !important;
    background-size: 10px 10px !important;
  }

  .fdc-tools {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    background: #e8e8e8;
    padding: 4px 8px;

    .anticon {
      padding: 4px;
      &:hover {
        color: @primary-color;
      }
    }
  }

  .fdc-canvas {
    position: absolute;
    left: 0;
    right: 0;
    width: 50px;
    height: 50px;

    .fdc-node {
      padding: 8px 16px;
      text-align: center;
      cursor: move;
      min-width: 120px;
      background: #ffffff;
      border: 1px solid #d9d9d9;
      position: absolute;
      border-radius: 2px;
      display: flex;
      align-items: center;

      &:hover {
        border: 1px dashed @primary-color;
      }

      .flow-node-drag {
        width: 24px;
        height: 24px;
        cursor: crosshair;
        margin-right: 8px;
        background: @primary-5;
        color: white;
        border-radius: 4px;
        position: relative;
        flex-shrink: 0;

        .anticon {
        }

        &:after {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
        }
      }

      .fdc-node-text {
        white-space: nowrap;
      }

      &.active {
        border: 1px dashed @primary-color;
        background: @primary-1;
      }

      &.jtk-drag-hover {
        box-shadow: 2px 2px 2px @primary-color;
      }
      &.disabled {
        pointer-events: none;
        cursor: not-allowed;
      }
    }

    /deep/.flowLabel {
      background: #fbfbfb;
      padding: 8px;
      white-space: nowrap;
      border: 1px solid #e8e8e8;
      color: #5a5a5a !important;
      border-radius: 6px;
    }
  }

  .fdc-empty-tips {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    > div {
      color: #bfbfbf;
      padding: 24px;
      border: 1px dashed #d9d9d9;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      font-size: 24px;
    }
  }

  .fdc-canvas-minimap {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 150px;
    height: 150px;
    background: #ffffff;
    border: 1px solid #e8e8e8;
    overflow: hidden;

    .fdc-canvas-minimap-elements {
      position: absolute;
      left: 0;
      top: 0;
      width: 50px;
      height: 50px;

      > div {
        background: #d9d9d9;
        position: absolute;
        width: 126px;
        height: 32px;
      }
    }
    .fdc-canvas-minimap-viewport {
      position: absolute;
      left: 0;
      right: 0;
      width: 100%;
      height: 100%;
      background: @primary-3;
      opacity: 0.2;
    }
  }
}
</style>