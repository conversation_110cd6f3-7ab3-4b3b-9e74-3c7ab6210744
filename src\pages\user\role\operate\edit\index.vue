<template>
  <div class="user-role-add">
    <PageDetail title="角色编辑">
      <div class="frame-button-wrapper-relative-blank" slot="btns">
        <a-button @click="back">返回上一级</a-button>
        <a-button type="primary" @click="save">保存</a-button>
      </div>
      <a-spin :spinning="loading" slot="middle">
        <Content ref="content" type="edit" :data="data"></Content>
      </a-spin>
    </PageDetail>
  </div>
</template>

<script>
// import _ from 'lodash';
import { roleDetail, roleUpdate } from '@/api/user';
import Content from '../components/Content';
import PageDetail from '@/components/Biz/PageDetail';

export default {
  props: {},
  components: { Content, PageDetail },
  data() {
    this.id = this.$route.params.id;
    return {
      loading: false,
      data: {}
    };
  },
  computed: {},
  created() {
    this.loading = true;
    roleDetail({
      id: this.id
    })
      .then(res => {
        this.loading = false;
        if (_.get(res, 'data.code') == 0) {
          const data = _.get(res, 'data.data');
          this.data = data;
        } else {
          this.$hideLoading({
            method: 'error',
            tips: _.get(res, 'data.message')
          });
        }
      })
      .catch(e => {
        this.loading = false;
        console.error(e);
        this.$hideLoading({ method: 'error', tips: '请求失败' });
      });
  },
  mounted() {},
  methods: {
    back() {
      this.$router.push({ name: 'roleList' });
    },
    save() {
      this.$refs.content.validate(data => {
        // 请求
        this.$showLoading();
        roleUpdate({
          ...this.data,
          ...data.baseInfo,
          ...data.authInfo
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ useMessage: true, tips: '更新角色成功' });
              // this.$router.push({ name: 'roleList' });
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: '请求失败' });
          });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.user-role-add {
  .ant-divider {
    margin: 0 0 24px 0;
  }
  .btns {
    text-align: right;
  }
}
</style>
