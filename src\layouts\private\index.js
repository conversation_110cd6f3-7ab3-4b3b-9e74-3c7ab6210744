// import Vue from 'vue';
// import Announce from './Announce';

// // 注册全局组件，可以防止基础组件递归引用
// Vue.component(GLOBAL_COMPONENTS['Announce'], Announce);
import store from '@/store';
import router from '@/router/lazy.js';

// 同一浏览器打开多个窗口登录不同账户，蓝海银行强制退出到登录也，其他跳出弹框，刷新到最新用户
// document.addEventListener('visibilitychange', function () {
//   let userName = localStorage.getItem('changedUserName');
//   let vueXuserName = _.get(store.state.account, 'user.name');
//   if (vueXuserName != userName) {
//     if (process.channel === 'LanHaiBank') {
//       // router.push({ name: 'login' });
//       window.Login.go();
//     } else {
//       const ignorePages = ['login', 'help', 'exception'];
//       const targetPath = _.get(router, 'currentRoute.name');
//       if (ignorePages.includes(targetPath)) {
//         return;
//       }
//       location.reload();
//     }
//   }
// });
