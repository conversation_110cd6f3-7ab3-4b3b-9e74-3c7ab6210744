import Mock from '@/utils/mock'
import common from '@/utils/common'

Mock.mock(/\/api\/config\/test\/select(\?[\s\S]*)?$/, 'get', (options) => {
  // const query = common.getQueryParams(options.url);
  // const {} = query;
  const data = Array(10)
    .fill()
    .map((item, i) => ({
      label: 'New York No. 1 Lake Park' + i,
      value: i
    }));
  return {
    code: '0',
    message: 'ok',
    data: data
  };
})

Mock.mock(/\/api\/config\/table(\?[\s\S]*)?$/, 'get', (options) => {
  const query = common.getQueryParams(options.url);
  const { page = 1, pageSize = 10 } = query;
  const data = Array(18)
    .fill()
    .map((item, i) => ({
      key: i,
      time: 'John Brown' + i,
      creater: 32,
      x: i,
      y: 'New York No. 1 Lake Park' + i,
      z: 3333,
      status: '1'
    }));
  return {
    code: '0',
    message: 'ok',
    data: {
      results: _.merge([], data).slice(
        (page - 1) * pageSize,
        (page - 1) * pageSize + pageSize
      ),
      page,
      pageSize,
      count: data.length
    }
  };
})

// mock规则配置list数据
Mock.mock(/\/api\/config\/rule\/table(\?[\s\S]*)?$/, 'get', (options) => {
  const query = common.getQueryParams(options.url);
  const { page = 1, pageSize = 10 } = query;
  const data = Array(18)
    .fill()
    .map((item, i) => ({
      key: i,
      rule_name: 'Data Rule' + i,
      rule_description: 'Description' + i,
      rule_status: true,
      id: i
    }));
  return {
    code: '0',
    message: 'ok',
    data: {
      results: _.merge([], data).slice(
        (page - 1) * pageSize,
        (page - 1) * pageSize + pageSize
      ),
      page,
      pageSize,
      count: data.length
    }
  };
})

// Mock.mock(/\/sqlreview\/project_config\/get_project_config(\?[\s\S]*)?$/, 'get', (options) => {
//   return Mock.mock({
//     code: '0',
//     message: 'ok',
//     'data': {
//       'results|3': [{
//         'id|+1': 101,
//         'created_by': 'sys',
//         'updated_by': 'sys',
//         'created_at': '2022-03-14 19:39:47',
//         'updated_at': '2022-03-14 19:39:47',
//         'delete_mark': 0,
//         'item_key': 'sql_review_rule_status',
//         'item_value': '1',
//         'item_desc': '未命中规则设置',
//         'parent_item_key': '',
//         'enable': true,
//         'order': 0,
//         'config_desc': '未命中规则时，AI判断结果设置',
//         'user_editable': 1,
//         'form_element_id': 1004510,
//         'sql_review_dynamic_form': {
//           'id': 1004509,
//           'created_by': 'sys',
//           'updated_by': 'sys',
//           'created_at': '2022-04-08 16:39:24',
//           'updated_at': '2022-04-08 16:39:24',
//           'delete_mark': 0,
//           'form_name': 'no_rule_hit_setting',
//           'from_desc': '未命中规则时判定设置',
//           'form_type': 'radio',
//           'multiple_flag': false,
//           'sql_review_dynamic_form_element': {
//             'id': 1004509,
//             'created_by': 'sys',
//             'updated_by': 'sys',
//             'created_at': '2022-04-08 20:12:57',
//             'updated_at': '2022-04-08 20:12:57',
//             'delete_mark': 0,
//             'form_id': 1004509,
//             'element_value': '0',
//             'element_name': '未知',
//             'element_desc': '未命中规则时，AI判断结果为未知',
//             'order': 0
//           }
//         }
//       },]
//     }
//   })
// })
