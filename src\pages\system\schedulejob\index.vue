<template>
  <div class="data-base-audit-schedule-job">
    <a-tabs
      default-active-key="database"
      :animated="false"
      class="card"
      type="card"
      @change="tabChange"
    >
      <a-tab-pane key="database">
        <span slot="tab" class="tab-area">
          <custom-icon type="lu-icon-database" />
          <span class="tab-title">数据库审核任务</span>
        </span>
        <Content ref="database" :type="activeKey" :query="query" />
      </a-tab-pane>
      <a-tab-pane key="agentMaster">
        <span slot="tab" class="tab-area">
          <custom-icon type="lu-icon-folder" />
          <span class="tab-title">agent master任务</span>
        </span>
        <Content ref="agentMaster" :type="activeKey" :query="query" />
      </a-tab-pane>
      <a-tab-pane key="system">
        <span slot="tab" class="tab-area">
          <custom-icon type="lu-icon-system" />
          <span class="tab-title">系统任务</span>
        </span>
        <Content ref="system" :type="activeKey" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import Content from './Content';
import config from './config';

export default {
  name: 'schedule-job',
  components: { Content },
  props: {},
  data() {
    this.config = config(this);
    let query = this.$route.query;
    return {
      query,
      activeKey: 'database'
    };
  },
  computed: {},
  created() {},
  mounted() {},
  updated() {},
  methods: {
    tabChange(activeKey) {
      this.activeKey = activeKey;
    }
  }
};
</script>

<style lang="less" scoped>
.data-base-audit-schedule-job {
  background: #ffffff;
  padding: 24px 0;
  /deep/.ant-tabs-bar {
    .ant-tabs-nav-container {
      .ant-tabs-nav-scroll {
        .ant-tabs-nav {
          > div {
            .ant-tabs-tab {
              border-radius: 0;
              border-bottom: none;
              background: rgba(243, 244, 246, 0.5);
              transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
              margin-left: 16px;
              .tab-area {
                .anticon {
                  font-size: 16px;
                  color: #a1a1a1;
                }
                .tab-title {
                  font-size: 16px;
                }
              }
              &:hover {
                .tab-area {
                  .anticon {
                    font-size: 16px;
                    color: rgb(37, 167, 232);
                  }
                }
              }
              &.ant-tabs-tab-active {
                border-top: 2px solid #219be3;
                border-bottom: none;
                background: #fff;
                .tab-area {
                  .anticon {
                    font-size: 16px;
                    color: #008adc;
                  }

                  .tab-title {
                    color: #008adc;
                    font-weight: 600;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
