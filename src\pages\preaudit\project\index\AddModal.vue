<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="新建项目审核"
    okText="保存"
    width="640px"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="params" :formData="data" class="add-form">
        <div class="file-upload-area" slot="file-upload">
          <a-upload
            name="file"
            :multiple="true"
            :before-upload="beforeUpload"
            :fileList="fileList"
            :remove="handleRemove"
          >
            <a-button class="file-import-btn">点击上传</a-button>
            <span>若上传文件的类型为excel, 请使用模板</span>
            <a @click="download">excel模板下载</a>
          </a-upload>
        </div>
      </Form>
    </a-spin>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import { templateDownload } from '@/api/preaudit';
import moment from 'moment';
const formParams = (ctx, bool) => {
  return {
    layout: 'vertical',
    fields: [
      // 项目review
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'review对象',
          key: 'project_id',
          props: {
            url: '/sqlreview/review/all_project',
            reqParams: {},
            allowSearch: true,
            backSearch: true,
            limit: 50,
            loaded(data) {
              ctx.dataSourceOption = data;
            }
          },
          listeners: {
            change: value => {
              let target = {};
              ctx.dataSourceOption.forEach(item => {
                if (item.value == value) {
                  target = item;
                }
              });
              ctx.$refs.form.saving({
                project_id: value,
                review_point: null,
                version_control: target.version_control,
                is_agent: target.is_agent
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'agent worker',
          key: 'agent_workers',
          props: {
            mode: 'multiple',
            url: '/sqlreview/project/agent_review_master',
            reqParams: {
              id: formData.project_id
            },
            // beforeLoaded(data) {
            //   return data.map(item => {
            //     return {
            //       ...item,
            //       disabled: item.value == 0 ? formData.isAll == false : formData.isAll
            //     };
            //   });
            // },
            backSearchOnlyOnSearch: true,
            allowSearch: true,
            backSearch: true,
            limit: 30
          },
          listeners: {
            change: value => {
              if (value && value.includes(0)) {
                ctx.$refs.form.saving({
                  isAll: true,
                  agent_workers: [0]
                });
              } else {
                ctx.$refs.form.saving({
                  isAll: undefined,
                  agent_workers: value.filter(item => item !== 0)
                });
              }
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
          visible: formData.is_agent == 1
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '项目分支',
          key: 'review_point',
          props: {
            url: '/sqlreview/review/project-ls-git/',
            reqParams: {
              project_id: formData.project_id
            },
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
          visible: formData.version_control == 1
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '项目分支',
          key: 'review_point',
          // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
          visible: formData.version_control == 0 || formData.is_agent == 1
        };
      },
      // 时间范围选择
      (formData = {}) => {
        return {
          type: 'RangePicker',
          label: '时间范围选择',
          key: 'time_range',
          props: {
            showTime: {
              format: 'HH:mm:ss'
            },
            ranges: {
              昨天: [
                moment().subtract(48, 'hour'),
                moment().subtract(24, 'hour')
              ],
              近7天: [moment().subtract(7, 'day'), moment()],
              近10天: [moment().subtract(10, 'day'), moment()],
              近15天: [moment().subtract(15, 'day'), moment()],
              近30天: [moment().subtract(30, 'day'), moment()]
            }
          },
          listeners: {
            change: value => {
              const timeArr = [];
              value.forEach(item => {
                timeArr.push(item.format('YYYY-MM-DD HH:mm:ss'));
              });
              ctx.$refs.form.saving({
                time_range: timeArr
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项' }],
          visible: formData.is_agent === 1
        };
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: 'review方式',
          key: 'mode',
          props: {
            options: [
              {
                label: '增量',
                value: 1
              },
              {
                label: '全量',
                value: 0
              }
            ]
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                mode: value
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '上传文件',
          key: 'files_list',
          hideComponent: true,
          slots: [{ key: 'file-upload' }],
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ],
          visible: formData.is_agent !== 1 && formData.version_control == 0
        };
      }
    ]
  };
};

export default {
  components: { Form },
  props: {},
  data() {
    return {
      spinning: false,
      visible: false,
      data: {},
      params: formParams(this),
      fileList: [],
      fileSize: 0
    };
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      this.data = {};
      form.resetFields();
      this.visible = false;
      this.fileList = [];
      this.fileSize = 0;
    },
    // 上传之前
    beforeUpload(file) {
      const maxSize = 200 * 1024 * 1024; // byte
      this.fileSize = this.fileSize + file.size;
      // const test = /\.(sql|xml|txt|xlsx)$/.test(file.name);
      // 防抖 最后抛报错
      if (this.timer !== null) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        if (this.fileSize > maxSize) {
          this.$message.error('文件大小错误，文件总大小不超过200MB');
          return;
        }
        // if (!test) {
        //   this.$message.error('文件格式错误，文件类型支持.sql/.xml/.txt/.xlsx');
        //   return;
        // }
        if (this.fileList.length > 4) {
          this.$message.warning('最多可上传五个文件');
          return;
        }
        if (fileNames.includes(file.name)) {
          this.$message.warning('该文件已上传，无需重复上传');
        }
      }, 200);
      const fileNames = this.fileList.map(item => item.name);
      if (
        this.fileSize < maxSize &&
        !fileNames.includes(file.name) &&
        this.fileList.length < 5
      ) {
        this.fileList.push(file);
      }
      const { form } = this.$refs;
      form.saving({ files_list: this.fileList });
      return false;
    },
    handleRemove(file) {
      this.fileList = this.fileList.filter(item => item.name !== file.name);
    },
    onCancel() {
      this.hide();
    },
    download() {
      templateDownload({ file_type: 'code_review_sql' })
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData();
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .add-form {
  .ant-row .ant-form-item-control-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        width: 150px;
      }
    }
    .file-upload-area {
      .file-import-btn {
        margin-right: 12px;
      }
    }
  }
}
</style>
