import Bracket from '@/components/Biz/Bracket';
export default function (ctx) {
  const { type } = ctx;
  const isAdd = false;
  const baseInfo = [
    {
      type: 'Select',
      label: '规则集类型',
      key: 'rule_type',
      props: {
        options: [
          {
            label: 'DDL规则',
            value: 'DDL'
          },
          {
            label: 'DML规则',
            value: 'DML'
          }
        ],
        disabled: type === 'add' ? isAdd : !isAdd
      },
      listeners: {
        change: (value) => {
          const baseInfo = ctx.$refs.baseInfo;
          baseInfo.saving({
            rule_type: value,
            db_type: null
          });
          if (value === 'DDL') {
            ctx.$emit('change', true);
          } else {
            ctx.$emit('change', false);
          }
        }
      },
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Select',
      label: '数据库类型',
      key: 'db_type',
      props: {
        url: 'sqlreview/project/rule_support_db',
        reqParams: {
          rule_set_type: 'DDL'
        },
        placeholder: '请选择数据库',
        disabled: type === 'add' ? isAdd : !isAdd
      },
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
      listeners: {
        change: (value) => {
          ctx.onSelectChange(value);
        }
      }
    },
    {
      type: 'Input',
      label: '规则集名称',
      key: 'name',
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
      props: {
        disabled: type == 'edit' ? !isAdd : isAdd,
        placeholder: '建议在名称前加数据库类型，例如mysql_规则集1'
      }
    }
  ];
  const ruleBaseInfo = [
    {
      type: 'Input',
      label: '规则名称',
      key: 'rule_name',
      // width: '95%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Input',
      label: '规则描述',
      key: 'rule_desc',
      // width: '95%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    }
    // {
    //   type: 'Select',
    //   label: '规则分类',
    //   key: 'type',
    //   props: {
    //     url: '/sqlreview/project/get_rule_type'
    //   },
    //   rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    // },
    // {
    //   type: 'Select',
    //   label: '风险等级',
    //   key: 'level',
    //   props: {
    //     url: '/sqlreview/project/get_rule_level',
    //     reqParams: {
    //       edit: 1
    //     }
    //   },
    //   rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    // }
  ];
  const conditionInfo = [
    {
      type: 'Select',
      label: '规则分类',
      key: 'category',
      props: {
        url: '/sqlreview/project/rule_category_list'
      },
      listeners: {
        change: (value) => {
          const condition = ctx.$refs.conditionForm;
          condition.saving({
            category: value,
            target_property: null
          });
          ctx.tableData = [{}];
        }
      },
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    (formData) => {
      return {
        type: 'Select',
        label: '目标属性',
        key: 'target_property',
        props: {
          url: '/sqlreview/project/rule_target_property',
          reqParams: {
            category: formData && formData.category
          }
        },
        width: '100%',
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      };
    }
  ];
  const columns = [
    {
      title: '规则名称',
      dataIndex: 'rule_name',
      key: 'rule_name',
      scopedSlots: { customRender: 'rule_name' }
    },
    {
      title: '规则描述',
      dataIndex: 'rule_desc',
      key: 'rule_desc',
      scopedSlots: { customRender: 'rule_desc' }
    },
    {
      title: '规则类型',
      dataIndex: 'rule_category',
      key: 'rule_category',
      scopedSlots: { customRender: 'rule_category' }
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      fixed: 'right'
    }
  ];
  const condition = {
    columns: [
      {
        dataIndex: 'relation',
        key: 'relation',
        width: 120,
        scopedSlots: { customRender: 'relation' }
      },
      {
        dataIndex: 'method_comment',
        key: 'method_comment',
        width: 300,
        scopedSlots: { customRender: 'method_comment' }
      },
      {
        dataIndex: 'rule_value',
        key: 'rule_value',
        width: 300,
        scopedSlots: { customRender: 'rule_value' }
      },
      {
        key: 'action',
        width: 100,
        scopedSlots: { customRender: 'action' }
      }
    ],
    editConfig: (params = {}) => {
      return {
        relation: (row, record = {}, tableEdit, colVal) => {
          let options = [
            {
              label: '空置',
              value: ''
            },
            {
              label: 'AND',
              value: 'and'
            },
            {
              label: 'OR',
              value: 'or'
            }
          ];
          if (row > 0) {
            options = options.slice(1);
          }
          return {
            type: 'Select',
            props: {
              options,
              allowClear: false,
              disabled: row == 0,
              class: `relation-${colVal}`
            },
            initialValue: row == 0 ? '' : 'and',
            extra: (h) => {
              return (
                <Bracket color={colVal === 'and' ? '#d0eecc' : '#fce3cb'} />
              );
            }
            // rules: [
            //   { required: true, message: '该项为必填项' }
            // ]
          };
        },
        method_comment: (row, record = {}) => {
          return {
            type: 'Select',
            props: {
              url: '/sqlreview/project/rule_method_list',
              size: 'default',
              reqParams: { rule_type: 'DML' }
            },
            listeners: {
              change: (value) => {
                ctx.$refs.tableEdit.saving({
                  id: record.id,
                  method_id: value,
                  rule_value: null
                });
              }
            },
            rules: [{ required: true, message: '该项为必填项' }]
          };
        },
        rule_value: (row, record = {}) => {
          let type = '';
          let props = {};
          let rules = [{ required: true, message: '该项为必填项' }];
          const methodId = record.method_id;
          if (methodId == 16 || methodId == 15) {
            type = 'Select';
            props = {
              options: [
                {
                  label: '是',
                  value: 'true'
                },
                {
                  label: '否',
                  value: 'false'
                }
              ]
            };
          } else if (methodId == 4) {
            type = 'InputNumRange';
            rules = [
              {
                validator: function (rule, value, callback) {
                  if (value) {
                    if (value[0] == null || value[1] == null) {
                      callback(new Error('有输入项为空'));
                    }
                    if (value[0] >= value[1]) {
                      callback(new Error('最小值不能大于等于最大值'));
                    }
                    callback();
                  } else {
                    callback(new Error('有输入项为空'));
                  }
                }
              }
            ];
          } else {
            type = 'Input';
          }
          return {
            type,
            props: {
              ...props
            },
            rules: rules
          };
        }
      };
    }
  };
  const outputConfig = {
    key: {
      options: [
        {
          label: '审批意见',
          value: 0
        }
      ]
    },
    value: {
      options: [
        {
          label: '拒绝',
          value: 0
        },
        {
          label: '通过',
          value: 1
        }
      ]
    }
  };
  const searchFields = [
    {
      type: 'Select',
      label: '',
      key: 'rule_type',
      props: {
        url: '/sqlreview/project/ddl_add_type',
        reqParams: {},
        loading: true,
        placeholder: '请选择规则分类'
      }
      // rules: [{ required: true, message: '该项为必填项' }]
    },
    {
      type: 'Input',
      label: '',
      props: {
        placeholder: '请输入关键字搜索'
      },
      key: 'rule_name'
    }
  ];

  const DDLSearchFields = [
    {
      type: 'Input',
      label: '规则名称',
      key: 'rule_name',
      props: {
        placeholder: '请输入规则名称'
      }
    },
    {
      type: 'Input',
      label: '规则描述',
      key: 'rule_desc',
      props: {
        placeholder: '请输入规则描述'
      }
    },
    {
      type: 'Select',
      label: '风险等级',
      key: 'rule_level',
      props: {
        options: [
          {
            label: '未知',
            value: '0'
          },
          {
            label: '高风险',
            value: '1'
          },
          {
            label: '中风险',
            value: '2'
          },
          {
            label: '低风险',
            value: '3'
          },
          {
            label: '无风险',
            value: '9'
          }
        ],
        placeholder: '请选择风险等级'
      }
    }
  ];
  return {
    baseInfo,
    ruleBaseInfo,
    columns,
    condition,
    outputConfig,
    searchFields,
    conditionInfo,
    DDLSearchFields
  };
}
