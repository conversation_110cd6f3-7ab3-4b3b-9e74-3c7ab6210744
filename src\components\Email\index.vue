<template>
  <!-- 邮箱组件 -->
  <div class="custom-email">
    <a-input class="email" v-bind="emailProps" v-on="emailListeners" :value="email">
      <template #addonAfter>
        <InputSelect
          class="email-suffix"
          v-bind="suffixProp"
          :options="suffixOptions"
          :value="suffix"
          @change="(val) => onChange(val, 'suffix')"
        ></InputSelect>
      </template>
    </a-input>
  </div>
</template>

<script>
import InputSelect from '@/components/InputSelect';

const defaultProps = {
  placeholder: '请输入'
};

const suffixDefaultProps = {
  style: { width: '152px' }
};
export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  components: { InputSelect },
  props: {
    value: [String],
    suffixOptions: {
      type: Array,
      default: () => {
        return [];
      }
    },
    suffixProps: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      localVal: this.value
    };
  },
  computed: {
    email() {
      if (this.localVal) {
        return this.hasSuffix()
          ? this.localVal.substring(0, this.localVal.indexOf('@'))
          : this.localVal;
      }
      return '';
    },
    suffix() {
      if (this.hasSuffix()) {
        return this.localVal.substring(
          this.localVal.indexOf('@'),
          this.localVal.length
        );
      }
      return '';
    },
    emailProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    emailListeners() {
      return { ...this.$listeners, change: e => this.onChange(e, 'value') };
    },
    suffixProp() {
      return { ...suffixDefaultProps, ...this.suffixProps };
    }
  },
  created() {},
  mounted() {},
  methods: {
    onChange(val, type) {
      const iptVal = type == 'value' ? _.get(val, 'target.value') : val || '';
      const { localVal = '' } = this;
      let email = '';
      let emailSuffix = '';
      if (localVal) {
        // 包含@取前面的值,不包含取当前值
        email = this.hasSuffix()
          ? localVal.substring(0, localVal.indexOf('@'))
          : localVal;
        // 包含@取 @及后面的值,不包含则无值
        emailSuffix = this.hasSuffix()
          ? localVal.substring(localVal.indexOf('@'), localVal.length)
          : '';
      }
      const emitVal =
        type == 'value' ? `${iptVal}${emailSuffix}` : `${email}${iptVal}`;

      // console.log(localVal, val);
      this.localVal = emitVal;
      this.$emit('change', emitVal);
    },
    hasSuffix() {
      return this.localVal && this.localVal.includes('@');
    }
  },
  watch: {
    value(newVal) {
      this.localVal = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
.custom-email {
  /deep/ .ant-input-group-addon {
    border: none;
    padding: 0;
  }
  /deep/ .custom-input-select {
    margin: 0;
    .ant-select-selection {
      border: none;
      margin: 0;
      .ant-select-search {
        height: 32px;
        line-height: 32px;

        input {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          background: #fafafa;
          border-left: 0;
          // border-left: none;
        }
      }
    }
  }
}
</style>
