<!--
 * @Descripttion: d
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2020-12-16 18:26:49
-->
<template>
  <div class="report-table-code">
    <code-format v-if="code" :code="getCode()"/>
  </div>
</template>

<script>
import CodeFormat from '@/components/log/SqlEdit'
export default {
  name: 'report-code',
  components: { CodeFormat },
  props: {
    dataSource: {
      type: Function,
      requied: true,
      default: () => []
    },
    data: {
      type: Object,
      required: false,
      default: function () {
        return {}
      }
    },
    search: {
      type: Object,
      required: false,
      default: function () {
        return {}
      }
    }
  },
  data () {
    return {
      code: ''
    }
  },
  mounted () {
    this.getData()
  },
  methods: {
    getCode () {
      return this.code
    },
    getData () {
      this.dataSource({ ...this.data, ...this.search }).then(code => {
        if (typeof code !== 'string') {
          this.code = JSON.stringify(code, 2, 2)
        } else {
          this.code = code
        }
      })
    }
  },
  watch: {
    search: {
      handler: function (newValue) {
        this.getData()
      }
    }
  }
}
</script>
<style>
.report-table-code {
  margin-left: -24px;
  margin-right: -24px;
  margin-top: -24px;
  min-height: 400px;
}
</style>
