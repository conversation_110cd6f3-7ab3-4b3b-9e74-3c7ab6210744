export default function (ctx) {
  return {
    fields: [
      () => {
        return {
          type: 'Select',
          key: 'code',
          props: {
            options: [
              { label: '111', value: '111' },
              { label: '222', value: '222' }
            ],
            size: 'default'
          },
          listeners: {
            change(val) {
              // ctx.$refs.tableEdit.saving({
              //   id: record.id,
              //   rule_detail_code: val,
              //   rule_detail_type: null,
              //   rule_detail_value: null
              // });
            }
          },
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      () => {
        return {
          type: 'Input',
          key: 'type',
          props: {},
          rules: [
            { required: true, message: '该项为必填项' }
          ]
        }
      }]
  }
}