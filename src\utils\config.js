const { getChannelInfo } = require('../../config/channel.js');
const img = require('@/assets/img/private/table-empty.svg');
let config = {
  'ProjectCode': 'sqlreview',
  'ProjectApiPrefix': '/sqlreview',
  // 项目名称
  'ProjectName': 'SQL审核平台',
  // 请求过期时间（10s）
  'Timeout': 3600 * 24 * 10,
  // 全局loadingKey
  'GlobalLoadingKey': 'globalLoading',
  'messageConfig': {
    top: '24px',
    duration: 2
  },
  'loadingErrWay': 'notify',
  // token key
  'TokenKey': 'sqlreview_token',
  'LuTokenKey': 'lu_token',
  'ThirdTokenKey': 'third_token',
  'LuLoginUrl': 'http://sso.op.lufax.com/passport/account/login',
  // table组件
  'tableComp': {
    pageNumberKey: 'page_number',
    pageSizeKey: 'page_size'
  },
  // 是否启用刷新token
  'useRefreshToken': false,
  // 路由缓存
  'routerCache': true,
  'routerCacheFixed': ['sqlresolver'],
  // 使用统一empty图
  'useUnifiedEmpty': true
}

const globalComponents = {
  // 通用
  'custom-form': 'custom-form',
  'custom-auth': 'custom-auth',
  'custom-icon': 'custom-icon',
  'custom-btns-wrapper': 'custom-btns-wrapper',
  'custom-empty': 'custom-empty',
  // 自定义
  'UserRoleModal': 'UserRoleModal',
  'CheckTemplate': 'CheckTemplate',
  'SplitPath': 'SplitPath',
  'Frequency': 'Frequency',
  'DbImg': 'DbImg',
  'UserChoose': 'UserChoose',
  'DataBaseChoose': 'DataBaseChoose',
  'Status': 'Status',
  'RuleCompsRange': 'RuleCompsRange',
  'biz-rule-item': 'biz-rule-item',
  'biz-rule-group': 'biz-rule-group'
}

// 标识自定义window属性名称
const windowAttr = {
  // 全局配置
  'GLOBAL_CONFIG': 'GLOBAL_CONFIG',
  // 全局组件
  'GLOBAL_COMPONENTS': 'GLOBAL_COMPONENTS',
  // 通用组件配置
  'COMP_CONFIG': 'COMP_CONFIG',
  // 渠道信息
  'CHANNEL_INFO': 'CHANNEL_INFO',
  // 通用方法控制
  'CommonUtil': 'CommonUtil',
  // 请求控制
  'ReqUtil': 'ReqUtil',
  // 路由集合
  'routesMap': 'routesMap',
  // 权限函数
  '$Auth': '$Auth',
  // 受控权限元素集合
  'AuthMap': 'AuthMap',
  // 登录相关
  'Login': 'Login'
}
console.log('标识自定义window属性名称', windowAttr);

window.GLOBAL_CONFIG = config;
window.GLOBAL_COMPONENTS = globalComponents;
// 通用组件配置
window.COMP_CONFIG = {
  'TABLE': {
    filterPopContainerBindToBody: true, // 筛选组件浮层的渲染节点是否在body上
    locale: (h) => {
      return {
        emptyText: <a-empty image={img}></a-empty>
      }
    }
  }
}
// 渠道信息
window.CHANNEL_INFO = getChannelInfo(process.channel);
export default config;