
// import Status from '@/components/Biz/Status';
export default function (ctx) {
  const btns = [
    {
      label: '测试按钮',
      color: 'green',
      callback: (pctx) => {
        ctx.btnTest();
        // console.log(pctx)
        // 模拟请求后刷新table
        ctx.$showLoading();
        setTimeout(() => {
          ctx.$hideLoading();
          pctx.$refs.table.refresh();
        }, 3000);
      }
    }
  ];
  const searches = [
    {
      type: 'Input',
      placeholder: '测试input',
      key: 'testSearch'
    },
    {
      type: 'Select',
      placeholder: '测试select',
      key: 'testSelect',
      width: 200,
      props: {
        url: '/api/home/<USER>/select',
        reqParams: {
          from: 'pageListAllConfig'
        }
      }
    },
    {
      type: 'Input',
      placeholder: '项目编号|项目名|app|创建人UM',
      key: 'langInput',
      width: 250
    }
  ];
  const columns = [
    {
      dataIndex: 'name',
      key: 'name',
      slots: { title: 'customTitle' },
      scopedSlots: { customRender: 'name' }
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      scopedSlots: { customRender: 'age' }
    },
    {
      title: 'Address',
      dataIndex: 'addressName',
      key: 'address'
    },
    {
      title: 'Tags',
      key: 'tags',
      dataIndex: 'tags',
      scopedSlots: { customRender: 'tags' }
    },
    {
      title: 'Action',
      key: 'action',
      scopedSlots: { customRender: 'action' }
    }
  ];
  return {
    columns,
    btns,
    searches
  };
};

// 虽然jsx语法片段无法放在data()函数外部使用
// 但可以使用函数式组件！！！
// 需要注意：key值为组件名称，如果会跟全局组件重名，请在对应vue文件中自定义组件名称
export const columnSlots = {
  name: {
    functional: true,
    render(createElement, context) {
      const { props = {}, parent } = context;
      const { text } = props;
      // console.log(context);
      return <a onClick={() => { console.log('测试函数式组件配置！！！'); parent.btnTest() }}>{text}</a>
    }
  },
  customTitle: {
    functional: true,
    render(createElement, context) {
      // const { props = {} } = context;
      // const { text } = props;
      return <span><a-icon type="team" />Name</span>
    }
  },
  tags: {
    functional: true,
    render(createElement, context) {
      const { props = {} } = context;
      const { text } = props;
      return (
        <span>
          {
            text.map((tag, i) => <a-tag key={i} color={tag === 'loser' ? 'volcano' : tag.length > 5 ? 'geekblue' : 'green'}>{tag + '!'}</a-tag>)
          }
        </span>
      )
    }
  },
  age: {
    functional: true,
    render(createElement, context) {
      const { props = {} } = context;
      const { text } = props;
      return <span>{text}</span>;
    }
  }
};