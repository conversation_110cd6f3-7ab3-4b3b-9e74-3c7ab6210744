<template>
  <div
    :class="['biz-instance-item', size == 'small' ? 'small-size' : '']"
    :style="{ width : width ? (width + '').endsWith('%') ? width : width + 'px' : 'auto'}"
    ref="bizInstanceItem"
  >
    <a-tag
      v-if="view == 'default'"
      :closable="handleClosable"
      @close="onClose"
      :class="['instance-item-tag', disabled ? 'disabled': '']"
    >
      <Tag type="Env" :text="handleTagText" class="instance-item-tag-tag" />
      <DbImg
        :value="value"
        :schemaName="handleSchemaName"
        :mode="mode"
        :limit="limit"
        :isNeedTips="isNeedTips"
        :class="[ closable ? 'closable' : 'db-img']"
        :style="{ width : percent}"
        :LimitLabelProps="LimitLabelProps"
      />
    </a-tag>
    <a-tag
      v-if="view == 'new'"
      :closable="handleClosable"
      @close="onClose"
      :class="['instance-item-tag', disabled ? 'disabled': '', view]"
    >
      <DbImg
        :value="value"
        :schemaName="handleSchemaName"
        :mode="mode"
        :limit="limit"
        :isNeedTips="isNeedTips"
        :class="[ closable ? 'closable' : 'db-img', view]"
        :style="{ width : percent}"
        :LimitLabelProps="LimitLabelProps"
      />
      <span :style="{color: color[tagText]}">{{localText[tagText]}}</span>
    </a-tag>
  </div>
</template>

<script>
import DbImg from '@/components/CustomImg/DbImg';
import Tag from '@/components/Biz/Tag';

export default {
  inheritAttrs: false,
  components: { DbImg, Tag },
  props: {
    src: String,
    text: String,
    tagText: String,
    limit: Number,
    closable: {
      type: Boolean,
      default: false
    },
    width: String | Number,
    disabled: Boolean,
    sourceInstance: {
      type: Object,
      default: () => {
        return {};
      }
    },
    mode: {
      type: String,
      default: 'ellipsis'
    },
    percent: {
      type: String,
      default: '85%'
    },
    isNeedTips: {
      type: Boolean,
      default: true
    },
    view: {
      type: String,
      default: 'default'
    }, // default ,new
    size: String, // small
    LimitLabelProps: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      color: {
        TEST: '#4CBB3A',
        PROD: '#FA8C16'
      },
      localText: {
        TEST: '测试',
        PROD: '生产'
      }
    };
  },
  computed: {
    value() {
      const { src } = this;
      return src;
    },
    handleTagText() {
      const { tagText = '' } = this;
      return tagText;
    },
    handleSchemaName() {
      const { text = '' } = this;
      return text;
    },
    handleClosable() {
      const { closable = false } = this;
      return closable;
    }
  },
  created() {},
  mounted() {},
  methods: {
    onClose() {
      this.$emit('close', this.sourceInstance);
    }
  },
  watch: {}
};
</script>
<style lang="less" scoped>
.biz-instance-item {
  width: 100%;
  .instance-item-tag {
    width: 100%;
    position: relative;
    padding: 9px 16px;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border: 1px solid #e4e4e7;
    &.new {
      padding: 9px 4px 9px 12px;
    }
    .instance-item-tag-tag {
      position: absolute;
      left: 10px;
      border-radius: 6px;
      // font-family: PingFangSC-Medium;
      font-size: 12px;
      color: #ffffff !important;
      font-weight: 500;
      // border-color: #f0f0f0;
      margin-right: 0;
      padding: 0 4px;
      &.common-tags {
        line-height: 20px;
      }
      .anticon-close {
        position: absolute;
        right: 10px;
      }
    }
    .db-img {
      margin-left: 36px;
      padding-right: 14px;
      // width: 85%;
      /deep/ .iconClass {
        display: flex;
        align-items: center;
        .anticon {
          font-size: 16px;
          padding-top: 0;
        }
        .ellipsis {
          width: 100%;
          // min-width: 150px;
          > pre {
            // font-family: PingFangSC-Medium;
            font-size: 13px;
            color: #27272a;
            font-weight: 400;
          }
        }
      }
      &.new {
        margin-left: 0;
        padding-right: 0;
      }
    }
    .closable {
      margin-left: 50px;
      padding-right: 30px;
      // width: 85%;
      /deep/ .ellipsis {
        width: 100%;
      }
    }
  }
  .disabled {
    cursor: not-allowed;
    // background:rgba(225,225,225, 0.25);
    opacity: 0.5;
  }
  &.small-size {
    .instance-item-tag {
      padding: 4px 16px;
      .instance-item-tag-tag {
        font-size: 12px;
      }
      .db-img {
        /deep/ .iconClass {
          .anticon {
            font-size: 14px;
          }
          .ellipsis {
            width: 100%;
            > pre {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
</style>
