<template>
  <a-auto-complete
    v-bind="selectProps"
    v-on="selectListeners"
    :class="className"
    :value="selectValue"
    :open="dropdownVisible"
    @search="handleSearch"
    @dropdownVisibleChange="onDropdownVisibleChange"
  >
    <a-input>
      <a-icon slot="suffix" type="loading" v-show="loading" />
    </a-input>
    <template slot="dataSource">
      <a-select-option v-for="item in data" :key="item.value" v-bind="item">{{ item.value }}</a-select-option>
    </template>
  </a-auto-complete>
</template>

<script>
import Request from './request';

const defaultProps = {
  placeholder: '请选择',
  // dropdownMatchSelectWidth: false,
  allowClear: true,
  showSearch: true,
  getPopupContainer: el => {
    // return document.getElementById('rootContent');
    return el.parentNode;
  }
};
export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {},
  props: {
    value: String,
    url: String,
    reqParams: Object,
    valueKey: {
      type: String,
      default: 'value'
    },
    noCache: {
      type: Boolean,
      default: true
    },
    beforeLoaded: Function,
    loaded: Function,
    backSearch: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: 30
    }
  },
  data() {
    this.sourceData = [];
    this.searchValue = '';
    this.search = _.debounce(() => {
      this.refresh();
    }, 800);
    return {
      loading: false,
      data: [],
      dropdownVisible: false
    };
  },
  computed: {
    selectProps() {
      const filterOption = this.backSearch ? false : this.$attrs.filterOption;
      const allowClear = this.loading
        ? false
        : this.$attrs.allowClear != null
        ? this.$attrs.allowClear
        : true;
      return {
        ...defaultProps,
        ...this.$attrs,
        filterOption: filterOption,
        allowClear
      };
    },
    selectListeners() {
      return { ...this.$listeners, ...{ change: this.handleChange } };
    },
    className() {
      let arr = [];
      return ['custom-input-select'].concat(arr);
    },
    selectValue() {
      return this.value;
    }
  },
  created() {
    const { options } = this.selectProps;
    // 有options，优先使用传入的options
    if (options && options.length > 0) {
      this.data = options;
      return;
    }
    this.getList();
  },
  mounted() {},
  methods: {
    // 模拟请求
    getList(refresh = false) {
      const { url, reqParams = {}, noCache, value } = this;
      if (!url) return;
      // console.log(this.searchValue, value, 888);
      this.loading = true;
      let selectedKeys = value;

      let searchParams = this.backSearch
        ? {
            search_value: this.searchValue,
            search_key: selectedKeys,
            limit: this.limit
          }
        : {};
      Request({
        url,
        reqParams: {
          ...reqParams,
          ...searchParams
        },
        noCache,
        refresh
      })
        .then(res => {
          this.loading = false;
          if (this.beforeLoaded) {
            res = this.beforeLoaded(res);
          }
          let _sourceData = res;
          let _data = res
            // .filter(item => item.visible !== false)
            .map(item => {
              return {
                ...item,
                value: _.get(item, this.valueKey)
              };
            });
          // 后端搜索特殊处理选项
          if (this.backSearch) {
            let selectedOptions =
              this.data.filter(item =>
                (selectedKeys || '').split(',').find(itm => item.value == itm)
              ) || [];
            let selectedSourceOptions =
              this.sourceData.filter(item =>
                (selectedKeys || '')
                  .split(',')
                  .find(itm => item[this.valueKey] == itm)
              ) || [];
            _data = _.uniqBy([...selectedOptions, ..._data], 'value');
            _data.push({
              label: '在光标闪烁处输入关键字搜索更多选项',
              value: 'search_tips',
              disabled: true,
              class: 'custom-input-select-search-tips'
            });
            _sourceData = _.uniqBy(
              [...selectedSourceOptions, ..._sourceData],
              this.valueKey
            );
          }
          this.data = _data;
          this.sourceData = _sourceData;
          if (_.isFunction(this.loaded)) {
            this.loaded(this.sourceData);
          }
        })
        .catch(e => {
          console.log(e);
          this.loading = false;
          this.sourceData = [];
          this.data = [];
          // this.value = undefined;
          this.$emit('change', undefined);
          // this.$message.error(e || '请求失败');
          this.$hideLoading({ method: 'error', tips: e || '请求失败' });
        });
    },
    refresh() {
      this.getList(true);
    },
    // 监听变化
    handleChange(value) {
      // console.log(value, 'change');
      let emitValue = value;
      this.$emit('change', emitValue);
      // 搜索模式，处理options
      if (this.backSearch) {
        this.handleSearch();
      }
    },
    handleSearch(value) {
      // console.log(value, 'search');
      if (this.backSearch) {
        // 防抖刷新
        this.searchValue = value;
        this.search();
      }
    },
    // 获取元数据item
    getSourceItem(key) {
      return this.sourceData.find(item => item[this.valueKey] == key);
    },
    // 下拉菜单显示隐藏
    onDropdownVisibleChange(open) {
      this.dropdownVisible = open;
    }
  },
  watch: {
    url: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.refresh();
        } else {
          this.data = this.$attrs.options || [];
        }
      }
      // immediate: true,
      // deep: true
    },
    reqParams: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.refresh();
        }
      }
      // immediate: true,
      // deep: true
    },
    '$attrs.options': {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.data = newVal;
        }
      }
      // immediate: true,
      // deep: true
    },
    value(newVal) {
      // console.log('get value', newVal);
      if (this.backSearch) {
        this.handleSearch();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.custom-input-select {
}
</style>
<style lang="less">
.custom-input-select-search-tips {
  color: @primary-color !important;
}
</style>
