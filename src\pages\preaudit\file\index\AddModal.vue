<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="新建文件审核"
    okText="保存"
    width="640px"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="params" :formData="data" class="add-form">
        <div class="file-upload-area" slot="file-upload">
          <a-upload
            name="file"
            :multiple="true"
            :before-upload="beforeUpload"
            :fileList="fileList"
            :remove="handleRemove"
          >
            <a-button class="file-import-btn">点击上传 </a-button>
             <span>若上传文件的类型为excel, 请使用模板</span>
            <a @click.stop="download">excel模板下载</a>
          </a-upload>
        </div>
      </Form>
    </a-spin>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import { templateDownload } from '@/api/preaudit';
const formParams = (ctx, _t, bool, type) => {
  return {
    layout: 'vertical',
    fields: [
      // 文件扫描
      {
        type: 'Input',
        label: '任务名称',
        key: 'review_name',
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '审核文件',
          key: 'files_list',
          hideComponent: true,
          slots: [{ key: 'file-upload' }],
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ]
        };
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: '审核方式',
          key: 'audit_type',
          props: {
            mode: 'tips',
            class: 'inline',
            options: [
              {
                label: '离线审核',
                value: 'offline'
              },
              {
                label: '在线审核',
                value: 'online'
              }
            ]
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                audit_type: value,
                rule_set: undefined,
                db_type: null,
                schema_list: null
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '数据库类型',
          key: 'db_type',
          props: {
            placeholder: '数据库类型',
            url: '/sqlreview/review/select_all_db_type',
            getPopupContainer: el => {
              return document.body;
            },
            loaded(data) {
              ctx.dbTypeOption = data;
            }
          },
          listeners: {
            change: value => {
              let ruleSet;
              ctx.dbTypeOption.forEach(item => {
                if (item.value == value) {
                  ruleSet = item.default_rule_set;
                }
              });
              ctx.$refs.form.saving({
                db_type: value,
                rule_set: ruleSet ? [ruleSet] : undefined
              });
            }
          },
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ],
          visible: formData.audit_type == 'offline'
        };
      },
      (formData = {}) => {
        return {
          type: 'TableEdit',
          label: '指定数据源/schema',
          key: 'schema_list',
          width: '100%',
          getDataMethod: 'getData',
          resetFieldsMethod: 'resetFields',
          initialValue: [],
          rules: [{ required: false, message: '该项为必填项' }],
          props: {
            columns: [
              {
                title: 'id',
                dataIndex: 'id',
                key: 'id',
                width: 30,
                scopedSlots: { customRender: 'draggable' }
              },
              {
                title: '数据源',
                dataIndex: 'datasource_id',
                key: 'datasource_id',
                width: 200,
                scopedSlots: { customRender: 'datasource_id' }
              },
              {
                title: 'schema',
                dataIndex: 'schema',
                key: 'schema',
                width: 220,
                scopedSlots: { customRender: 'schema' }
              },
              {
                title: '操作',
                key: 'action',
                width: 80,
                scopedSlots: { customRender: 'action' }
              }
            ],
            editConfig: {
              datasource_id: (row, record = {}) => {
                return {
                  type: 'DataBaseChoose',
                  props: {
                    url: '/sqlreview/project/data_source_choices',
                    reqParams: {
                      restrict: 'restrict'
                    },
                    loaded(data) {
                      ctx.dataSourceOption = data;
                    },
                    beforeLoaded(data) {
                      return data.map(item => {
                        return {
                          ...item,
                          instance_usage: item.env,
                          showText: item.label + '(' + item.db_url + ')'
                        };
                      });
                    },
                    mode: 'default',
                    optionLabelProp: 'children',
                    allowSearch: true,
                    backSearch: true,
                    limit: 30
                  },
                  rules: [{ required: true, message: '该项为必填项' }],
                  listeners: {
                    change: value => {
                      const { form } = ctx.$refs;
                      const table = form.$refs.schema_list[0];
                      if (value) {
                        const env = ctx.dataSourceOption.filter(
                          item => item.value === value
                        )[0].env;
                        table.saving({
                          id: record.id,
                          datasource_id: value,
                          env: env,
                          schema: null
                        });
                      } else {
                        table.saving({
                          id: record.id,
                          datasource_id: null,
                          env: '--',
                          schema: null,
                          rule_set: undefined
                        });
                      }
                      ctx.dataSourceOption.forEach(item => {
                        if (item.value == value) {
                          record.default_rule_set = item.default_rule_set;
                        }
                      });
                      ctx.datasourceId = table.data.map(item => {
                        return item.datasource_id;
                      });
                      ctx.ruleSet = table.data.map(item => {
                        return item.default_rule_set;
                      });
                      form.saving({
                        rule_set: !_.isEmpty(ctx.ruleSet)
                          ? [...new Set(ctx.ruleSet)]
                          : undefined,
                        datasource_id: value,
                        data_source_id: ctx.datasourceId.join()
                      });
                    }
                  }
                };
              },
              schema: (row, record = {}) => {
                return {
                  type: 'Select',
                  props: {
                    mode: 'multiple',
                    url: '/sqlreview/project/get_schema_list',
                    reqParams: { datasource_id: record.datasource_id || '' },
                    backSearchOnlyOnSearch: true,
                    allowSearch: true,
                    backSearch: true,
                    limit: 50
                  },
                  rules: [{ required: true, message: '该项为必填项' }]
                };
              }
            },
            hooks: {
              remove: data => {
                const { form } = ctx.$refs;
                data.forEach(item => {
                  ctx.datasourceId = ctx.datasourceId.filter(
                    elem => elem !== item.datasource_id
                  );
                  ctx.ruleSet = ctx.ruleSet.filter(
                    elem => elem !== item.default_rule_set
                  );
                });
                form.saving({
                  rule_set: !_.isEmpty(ctx.ruleSet) && [
                    ...new Set(ctx.ruleSet)
                  ],
                  data_source_id: ctx.datasourceId && ctx.datasourceId.join()
                });
              }
            },
            rowKey: 'id',
            initEditStatus: true,
            pagination: false,
            draggable: true,
            leastNum: 1,
            actionBtns: ['add', 'remove'],
            actionBtnsIcons: {
              add: 'plus-circle',
              remove: 'close-circle'
            }
          },
          visible: formData.audit_type == 'online'
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '规则集',
          key: 'rule_set',
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ],
          props: {
            url: '/sqlreview/project/rule_set_all',
            mode: 'multiple',
            // separator: ',',
            reqParams: {
              db_type: formData.db_type,
              // data_source_id: formData.datasource_id
              data_source_id: formData.data_source_id
            },
            backSearchOnlyOnSearch: true,
            allowSearch: true,
            backSearch: true,
            limit: 30
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                rule_set: value
              });
            }
          }
        };
      }
    ]
  };
};

export default {
  components: { Form },
  props: {},
  data() {
    return {
      spinning: false,
      visible: false,
      data: {},
      params: formParams(this),
      fileList: [],
      fileSize: 0,
      timer: null,
      datasourceId: [],
      ruleSet: []
    };
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      this.data = {};
      form.resetFields();
      this.visible = false;
      this.fileList = [];
      this.fileSize = 0;
      this.timer = null;
      this.ruleSet = [];
      this.datasourceId = [];
    },
    // 上传之前
    beforeUpload(file) {
      const maxSize = 200 * 1024 * 1024; // byte
      this.fileSize = this.fileSize + file.size;
      // const test = /\.(sql|xml|txt|xlsx)$/.test(file.name);
      // 防抖 最后抛报错
      if (this.timer !== null) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        if (this.fileSize > maxSize) {
          this.$message.error('文件大小错误，文件总大小不超过200MB');
          return;
        }
        // if (!test) {
        //   this.$message.error('文件格式错误，文件类型支持.sql/.xml/.txt/.xlsx');
        //   return;
        // }
        if (this.fileList.length > 4) {
          this.$message.warning('最多可上传五个文件');
          return;
        }
        if (fileNames.includes(file.name)) {
          this.$message.warning('该文件已上传，无需重复上传');
        }
      }, 200);
      const fileNames = this.fileList.map(item => item.name);
      if (
        this.fileSize < maxSize &&
        !fileNames.includes(file.name) &&
        this.fileList.length < 5
      ) {
        this.fileList.push(file);
      }
      const { form } = this.$refs;
      form.saving({ files_list: this.fileList });
      return false;
    },
    handleRemove(file) {
      this.fileList = this.fileList.filter(item => item.name !== file.name);
    },
    onCancel() {
      this.hide();
    },
    download() {
      templateDownload({ file_type: 'code_review_sql' })
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData();
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .add-form {
  .ant-row .ant-form-item-control-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        width: 150px;
      }
    }
    .file-upload-area {
      .file-import-btn {
        margin-right: 12px;
      }
    }
  }
}
</style>
