<template>
  <!-- 数据审核 -->
  <a-skeleton :loading="loading" active class="quater-block small-size">
    <div class="title">数据库审核</div>
    <div class="database-review">
      <div class="content" v-for="(value, key) in databaseAuditInfo" :key="key">
        <div class="label">
          <span :class="riskClass[key]" v-if="isRiskTagFn(key)"
            >{{ riskTag[key] }}
          </span>
          {{ riskMap[key] }} {{ isRiskTagFn(key) ? 'SQL' : '' }}
        </div>
        <div :class="['value', riskClass[key]]">{{ value.total || 0 }}</div>
      </div>
    </div>
    <div class="pie-chart">
      <Chart :option="pieOption" ref="pie" />
    </div>
  </a-skeleton>
</template>

<script>
import Chart from '@/components/Chart';
export default {
  components: { Chart },
  props: {
    databaseAuditInfo: {
      type: Object,
      default: () => {}
    },
    pieOption: {
      type: Object,
      default: () => {}
    },
    riskClass: {
      type: Object,
      default: () => {}
    },
    riskMap: {
      type: Object,
      default: () => {}
    },
    riskTag: {
      type: Object,
      default: () => {}
    },
    loading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    isRiskTagFn(key) {
      const bool = [
        'high_risk',
        'lower_risk',
        'no_risk',
        'modifying_sql',
        'whitelist_sql'
      ].includes(key);
      return bool;
    }
  }
};
</script>

<style lang="less" scoped>
.quater-block {
  height: 420px;
  width: calc(25% - 16px);
  border-radius: 8px;
  margin-right: 16px;
  padding: 16px 24px;
  background: #ffffff;
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.05);
  .title {
    font-size: 16px;
    color: #1f1f1f;
    padding-bottom: 16px;
    font-weight: bold;
  }
  .database-review {
    .content {
      padding: 7px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      .label {
        span {
          display: inline-block;
          margin-right: 8px;
          width: 22px;
          height: 22px;
          border-radius: 4px;
          font-size: 12px;
          text-align: center;
          line-height: 20px;
        }
        .high-risk {
          color: #f5222d;
          border: 1px solid #ffa39e;
          background: #fff1f0;
        }
        .lower-risk {
          color: #faad14;
          border: 1px solid #ffe58f;
          background: #fffbe6;
        }
        .no-risk {
          color: #52c41a;
          border: 1px solid #b7eb8f;
          background: #f6ffed;
        }
      }
      .value {
        font-size: 20px;
        font-weight: 500;
        color: #1f1f1f;
        &.high-risk,
        &.lower-risk,
        &.no-risk {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }
  .pie-chart {
    margin-top: 16px;
    height: 120px;
  }
}
</style>