<template>
  <component
    :is="struct.compName"
    v-if="!isArray(struct)"
    v-bind="getCompProps(struct)"
    ref="current"
  >
    <template v-for="(item, index) in (struct.children || [])">
      <SchemaComp :key="index" :schema="item" :value="value" :slot="item.slot" :emitKey="emitKey"></SchemaComp>
    </template>
  </component>
</template>

<script>
import Blocks from '@/components/Blocks';
import Form from '@/components/Form';

export default {
  name: 'SchemaComp',
  // inheritAttrs: false,
  // model: {
  //   prop: 'value',
  //   event: 'change'
  // },
  components: { Blocks, Form },
  props: {
    schema: Object | Array,
    value: {
      type: Object,
      default: () => ({})
    },
    emitKey: String
  },
  data() {
    this.bindListener = false;
    return {
      emitData: {}
    };
  },
  computed: {
    struct() {
      const { schema } = this;
      return schema;
    }
  },
  created() {},
  mounted() {
    this.addListener();
  },
  methods: {
    isArray(data) {
      return Array.isArray(data);
    },
    addListener() {
      const { struct } = this;
      if (struct.emit && !this.bindListener) {
        this.$bus.$on(this.emitKey, emitData => {
          emitData[struct.emit] = this.getData();
        });
        this.$bus.$on(this.emitKey + '_validate', validateArr => {
          validateArr.push(this.validate());
        });
        this.bindListener = true;
      }
    },
    getCompProps(item = {}) {
      let compData = _.get(this.value, [item.dataKey]);
      if (compData === undefined && item.initialValue) {
        compData = item.initialValue;
      }
      return {
        ...(item.props || {}),
        ...(item.dataProp ? { [item.dataProp]: compData } : {})
      };
    },
    validate() {
      const { current } = this.$refs;
      return current && current.validate && current.validate();
    },
    getData() {
      const { struct } = this;
      const { current } = this.$refs;
      return (
        current &&
        current[struct.getDataMethod || 'getData'] &&
        current[struct.getDataMethod || 'getData']()
      );
    }
  },
  watch: {
    schema(newVal, oldVal) {
      this.addListener();
    }
  }
};
</script>

<style lang="less" scoped>
</style>