<template>
  <div class="order-content">
    <!-- 页面概述 -->
    <div class="header">
      <div :class="['summary', isCollapse && 'collapse']" @click="onCollapse">
        <div class="title">
          <span>查询</span>
          <div>
            <!-- <a-input-search
              placeholder="搜索ID"
              @search="onSearch"
              v-if="isCollapse"
              v-model="localSearchValue"
            ></a-input-search>
            <span class="search" v-if="isCollapse"
              >高级查询</span
            > -->
            <custom-icon :type="isCollapse ? 'down' : 'up'"></custom-icon>
          </div>
        </div>
      </div>
      <!-- 搜索内容 -->
      <div
        :class="['search-content', isCollapse && 'collapse']"
        v-if="!isCollapse"
      >
        <SearchArea
          v-bind="searchParams || {}"
          @reset="reset"
          @search="search"
          ref="search"
          searchMode="switch"
          :iconCombine="false"
          :isShowExpandIcon="false"
          :searchData="searchData || {}"
        ></SearchArea>
      </div>
    </div>
    <PageList :mode="'tabs'">
      <div class="order-data-info">
        <a-tabs
          :activeKey="activeKey"
          :animated="false"
          class="card"
          type="card"
          @change="tabChange"
        >
          <a-tab-pane key="sqlreview">
            <SqlReviewList
              @getOrderTotal="getOrderTotalFn"
              orderType="sqlreview"
              ref="sqlreview"
            ></SqlReviewList>
            <span slot="tab">
              <a-badge :count="sqlreviewCount" :overflow-count="99" show-zero>
                <span class="head-example">代码审核</span>
              </a-badge>
            </span>
          </a-tab-pane>
          <a-tab-pane key="datasource">
            <DataSourceList
              @getOrderTotal="getOrderTotalFn"
              orderType="datasource"
              ref="datasource"
            ></DataSourceList>
            <span slot="tab">
              <a-badge :count="databasCount" :overflow-count="99" show-zero>
                <span class="head-example">数据库审核</span>
              </a-badge>
            </span>
          </a-tab-pane>
        </a-tabs>
      </div>
    </PageList>
  </div>
</template>

<script>
import PageList from '@/components/PageListNew/SwitchTable/index.vue';
import DataSourceList from './DataSourceList';
import SqlReviewList from './SqlReviewList';
import SearchArea from '@/components/Biz/SearchArea/new';
import config from './config';
import { getOrderTotal } from '@/api/order';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  name: 'orderList',
  components: {
    SqlReviewList,
    DataSourceList,
    PageList,
    SearchArea
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    this.config = config(this);
    return {
      localSearchValue: '',
      isCollapse: false,
      searchData: {},
      searchParams: {
        fields: this.config.searchFields
      },
      loading: false,
      activeKey: 'sqlreview',
      sqlreviewCount: 0,
      tableCount: 0,
      sqlCount: 0,
      databasCount: 0,
      tabList: []
    };
  },
  mounted() {
    this.getOrderTotalFn();
  },
  created() {},
  methods: {
    // 新建项目
    addProject() {
      const { addModal } = this.$refs;
      addModal.show('order');
    },
    getOrderTotalFn() {
      getOrderTotal()
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data');
            this.sqlreviewCount = resData.record_count;
            this.tableCount = resData.white_list_table_count;
            this.sqlCount = resData.review_white_sql_count;
            this.databasCount = resData.database_review_count;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 切换 tab 选项卡
    tabChange(activeKey) {
      this.activeKey = activeKey;
      this.$set(
        this.searchParams,
        'fields',
        activeKey == 'sqlreview'
          ? this.config.searchFields
          : this.config.dateSourceFields
      );
    },
    // 查询
    search(data) {
      const _ref = this.$refs[this.activeKey];
      const { switchTable } = _ref.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const _ref = this.$refs[this.activeKey];
      const { switchTable } = _ref.$refs;
      if (_ref.checked) _ref.checked = false;
      const { table } = switchTable.$refs;
      table.onReset();
    },
    // onSearch() {
    //   const data = { data_source_id: this.localSearchValue };
    //   const { switchTable } = _ref.$refs;
    //   const { table } = switchTable.$refs;
    //   const { searchParams } = table;
    //   Object.assign(searchParams, { ...data });
    //   table.refresh(null, data);
    // },
    onCollapse() {
      this.isCollapse = !this.isCollapse;
    }
  },
  watch: {
    '$route.query.activeKey': {
      handler(newVal) {
        if (newVal) {
          this.activeKey = newVal;
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.order-content {
  .header {
    border-radius: 16px;
    margin-bottom: 16px;
    background-color: #fff;
    .summary {
      padding: 16px 24px;
      border-radius: 16px;
      cursor: pointer;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);

        > div {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          .ant-input-search {
            width: 200px;
          }
          .search {
            font-size: 14px;
            color: #1f1f1f;
            font-weight: 400;
            padding: 0 16px;
            cursor: pointer;
          }
          > .anticon {
            font-size: 14px;
            color: #8c8c8c;
            margin-right: 0;
          }
        }
      }
    }
    .search-content {
      border-top: 1px solid #f0f0f0;
      &.collapse {
        .title {
          border-bottom: none;
        }
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        > .anticon {
          font-size: 14px;
          color: #8c8c8c;
          margin-right: 0;
        }
      }
      /deep/.search-area {
        box-shadow: none;
        border: none;
      }
    }
  }
  .order-data-info {
    /deep/.ant-tabs-bar {
      border: none;
      position: relative;
      top: -12px;
      .ant-tabs-nav-container {
        .ant-tabs-nav-scroll {
          .ant-tabs-nav {
            > div {
              background: rgb(239, 239, 239);
              margin-left: 24px;
              padding: 2px;
              border-radius: 8px;
              .ant-tabs-tab {
                border-radius: 0;
                border: none;
                margin-right: 0 !important;

                &:first-child {
                  margin-left: 0 !important;
                }
                background: transparent;
                &.ant-tabs-tab-active {
                  border-bottom: none;
                  background: #fff;
                  border-radius: 8px;
                  .ant-badge {
                    .head-example {
                      color: @primary-6;
                    }
                  }
                }
                .ant-badge {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  height: 32px;
                  line-height: 32px;
                  .head-example {
                    margin-right: 24px;
                  }
                  .ant-badge-count {
                    right: 8px;
                    top: 50%;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
