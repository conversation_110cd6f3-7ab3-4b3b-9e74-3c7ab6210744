<template>
  <div class="custom-flow-detail">
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </div>
</template>

<script>
import Form from '@/components/Form';
import flowUtil from '@/utils/flow';
// import config from './config';

export default {
  props: {
    type: String,
    flowType: String,
    formParams: {
      type: Object,
      default: () => {
        return {};
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    extraData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  components: { Form },
  data() {
    // this.config = config(this, isEdit);
    return {
      params: {
        layout: 'vertical',
        ...this.formParams,
        fields: []
      },
      data: {},
      extra: this.extraData
    };
  },
  computed: {
    isEdit() {
      return this.type === 'edit' || false;
    }
  },
  created() {
    // 流程节点设置扩展属性
    this.setFields();
  },
  mounted() {
    this.$bus.$on(
      'FlowFormRefresh',
      (this.refresh = extra => {
        this.extra = extra || {};
        this.setFields();
      })
    );
  },
  destroyed() {
    if (this.refresh) {
      this.$bus.$off('FlowFormRefresh', this.refresh);
      this.refresh = null;
    }
  },
  methods: {
    setFields() {
      const taskType = _.get(this.extra, 'taskType') || '';
      flowUtil.setFlowNodeProperty({
        taskType: taskType || this.flowType,
        configType: 'form',
        ctx: this,
        cbk: fields => {
          this.$set(this.params, 'fields', fields);
        }
      });
    },
    getData() {
      const { form } = this.$refs;
      let res = form.getData();
      _.forEach(res, (itm, key) => {
        if (typeof itm === 'object') {
          res[key] = JSON.stringify(itm);
        }
      });
      return res;
    },
    validate(cbk) {
      const { form } = this.$refs;
      return form.validate();
    }
  },
  watch: {
    formData: {
      handler(newVal = {}) {
        const dataExtends = newVal.data_extends || [];
        let res = {};
        dataExtends.forEach(item => {
          res[item.property_name] = flowUtil.getPropertyValue(
            item.property_name,
            newVal
          );
        });
        this.data = res;
        this.setFields();
      },
      immediate: true
    },
    extraData(newVal) {
      this.extra = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
.custom-flow-detail {
}
</style>