<template>
  <div class="config-rules-edit">
    <div class="frame-button-wrapper">
      <a-button @click="onCancel" class="highlight">返回</a-button>
    </div>

    <a-spin tip="加载中" :spinning="loading">
      <DetailDML
        :type="type"
        ref="detailDML"
        v-if="rule_category === 'DML'"
        :dataSource="dataSource"
      />
      <DetailDDL :type="type" ref="detailDDL" v-else :dataSource="dataSource" />
    </a-spin>
  </div>
</template>

<script>
import { ruleDetail, newRuleDetail } from '@/api/config/rule';

import DetailDDL from '../components/DetailDDL';
import DetailDML from '../components/DetailDML/old';

export default {
  components: { DetailDDL, DetailDML },
  props: {},
  data() {
    return {
      rule_category: this.$route.query.rule_category,
      showDDL: '',
      loading: false,
      dbType: '',
      type: 'detail',
      dataSource: {},
      oldRuleSetUids: []
    };
  },
  mounted() {},
  created() {
    // 请求
    this.loading = true;
    if (this.rule_category === 'DML') {
      ruleDetail({
        // dml
        id: this.$route.query.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.loading = false;
            let dataSource = _.get(res, 'data.data') || {};
            const { details = [] } = dataSource;
            dataSource.details = details.map(item => {
              return {
                ...item,
                ...(item.target_info || {})
              };
            });
            this.dataSource = dataSource;
            this.dbType =
              dataSource.db_type ||
              window.localStorage.getItem('db_type') ||
              '';
          } else {
            this.$message.error(_.get(res, 'data.message'));
            this.loading = false;
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$message.error('请求失败');
        });
    } else {
      newRuleDetail({
        // ddl
        rule_id: this.$route.query.id,
        rule_category: this.rule_category,
        rule_set_id: this.$route.query.rule_set_id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.loading = false;
            let dataSource = _.get(res, 'data.data') || {};
            this.dataSource = dataSource;
            this.oldRuleSetUids = dataSource.rule_set_uids || [];
          } else {
            this.$message.error(_.get(res, 'data.message'));
            this.loading = false;
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$message.error('请求失败');
        });
    }
  },
  methods: {
    onCancel() {
      this.$router.push({ name: 'rules-config' });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
