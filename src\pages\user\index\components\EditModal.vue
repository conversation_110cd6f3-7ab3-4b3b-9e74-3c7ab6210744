<template>
  <!-- 修改用户权限弹窗 -->
  <a-modal
    v-model="visible"
    title="修改用户权限"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
// import common from '@/utils/common';

export default {
  components: { Form },
  props: {},
  data() {
    return {
      visible: false,
      data: {},
      params: {
        layout: 'vertical',
        fields: [
          {
            type: 'Input',
            label: '用户名',
            key: 'name',
            props: {
              disabled: true
            }
          },
          {
            type: 'Input',
            label: '姓名',
            key: 'ch_name'
          },
          // {
          //   type: 'TreeSelect',
          //   label: '选择组',
          //   key: 'groups',
          //   props: {
          //     url: '/sqlreview/get-all-user-group/',
          //     multiple: true,
          //     showSearch: true,
          //     treeNodeFilterProp: 'label'
          //     // mode: 'multiple'
          //   }
          // },
          {
            type: 'Select',
            label: '项目组',
            key: 'groups',
            props: {
              url: '/sqlreview/project_config/select_project_group',
              reqParams: {},
              mode: 'multiple',
              allowSearch: true,
              backSearch: true,
              backSearchOnlyOnSearch: true,
              limit: 20
            }
          },
          {
            type: 'Input',
            label: '邮箱',
            key: 'email',
            rules: [
              // { required: true, message: '该项为必填项', trigger: 'blur' },
              {
                pattern: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
                message: '必须符合邮箱格式'
              }
            ]
          },
          {
            type: 'Select',
            key: 'userRole',
            label: '用户角色',
            props: {
              url: '/sqlreview/project_config/role_info_by_select'
            }
          }
        ]
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data) {
      // console.log(data);
      this.data = {
        name: data.name,
        id: data.id,
        groups: data.groups,
        userRole: data.role,
        email: data.email,
        ch_name: data.ch_name
      };
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData();
      console.log(data, 'data');
      form.validate(valid => {
        if (valid) {
          this.$emit('save', { ...this.data, ...data });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
