<template>
  <div class="bar-label">
    <div>
      <div class="color-block" :style="{ width: current/max == 1 ? '95%' :  `${current/max*100}%`}"></div>
      <LimitLabel :label="label" format="sql" mode="ellipsis" class="sql-text"></LimitLabel>
    </div>
  </div>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
export default {
  components: { LimitLabel },
  props: {
    label: String,
    max: String | Number,
    current: String | Number
  },
  data() {
    return {};
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.bar-label {
  width: 100%;
  border-radius: 5px;
  background: #f4f5f7;
  height: 36px;
  > div {
    display: flex;
    flex-direction: column;
    .color-block {
      width: 100%;
      height: 32px;
      border-radius: 5px;
      background: #d0eeff;
    }
    .sql-text {
      max-width: 320px;
      display: inline-block;
      padding: 6px 8px;
      color: #3e60c1;
      font-size: 12px;
      position: relative;
      top: -32px;
      left: 0px;
    }
  }
}
</style>