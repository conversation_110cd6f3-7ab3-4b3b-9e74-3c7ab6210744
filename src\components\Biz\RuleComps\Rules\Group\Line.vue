<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    version="1.1"
    :width="width"
    :height="height"
  >
    <path
      v-for="(item, index) in path"
      :key="index"
      :d="item"
      stroke="#d9d9d9"
      stroke-width="1"
      fill="none"
    />
  </svg>
</template>

<script>
export default {
  components: {},
  props: {
    list: Array
  },
  data() {
    return {
      path: [],
      width: 0,
      height: 0
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {
    this.draw();
  },
  methods: {
    draw() {
      const parentDom = this.$parent.$el;
      const relationNode = parentDom.querySelector('.rule-tree-relation');
      const children = (
        parentDom.querySelector('.rule-tree-relation-children') || {}
      ).children;
      let nodes = [...(children || [])].slice(0, -1);
      if (this.list.length <= 1) nodes = nodes.slice(1)
      // console.log(relationNode, nodes, 'jhhhhhhh');
      const path = [];
      const relationNodeRect = relationNode.getBoundingClientRect();
      let lastY = 0;
      nodes.forEach((item, index) => {
        const rect = item.getBoundingClientRect();
        const x = rect.x - relationNodeRect.x;
        const y = rect.y - relationNodeRect.y + 20;
        if (index === 0) {
          path.push(`M 80 20 ${x} 20`);
        } else if (index === nodes.length - 1) {
          path.push(
            `M 80 20 80 ${y - 10} Q 80 ${y} ${80 + 10} ${y} M ${80 +
              10} ${y} ${x} ${y}`
          );
          lastY = y;
        } else {
          path.push(
            `M 80 ${y - 10} Q 80 ${y} ${80 + 10} ${y} M ${80 +
              10} ${y} ${x} ${y}`
          );
        }
      });
      this.width = 120;
      this.height = lastY + 20;
      this.path = path;
      // console.log(this.width, this.height, this.path);
    }
  }
};
</script>

<style scoped lang="less">
svg {
  position: absolute;
  left: 0;
  top: 0;
}
</style>
