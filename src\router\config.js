import Cookie from 'js-cookie';
export const ignorePages = ['login', 'help', 'dbacat', 'dbacat-detail'];
export const ignoreAuthCheckPages = ['exception', 'optimizeDirective'];

// 第三方登录（虚拟登录）页面权限
const virtualLoginAuthCheck = function({ store, to }) {
  let nameArr = ['project-review-detail', 'project-review-review'];
  let isVirtualLogin = _.get(store.state.account, 'user.virtual_login');
  return (
    !isVirtualLogin ||
    ignorePages.includes(to.name) ||
    nameArr.includes(to.name)
  );
};

// 页面权限验证函数 老逻辑
// const pageAuthCheck = function ({ store, toName, routesMap }) {
//   let flag = true;
//   const role = store.state.account.user && store.state.account.user.role;
//   const auth = routesMap[toName].auth;
//   if ((auth && auth.includes(role)) || auth == null) {
//     // do nothing
//   } else {
//     flag = false;
//   }
//   return flag;
// };

// 页面权限验证函数 新逻辑
// 新逻辑菜单数据结构不是前端写死的
// 而是后端根据登录角色判断返回
// 所以不需要再判断角色，而是判断页面是否在返回的数据结构中
const pageAuthCheck = function({ store, to, pages }) {
  let flag = true;
  let isVirtualLogin = _.get(store.state.account, 'user.virtual_login');
  if (
    !isVirtualLogin &&
    to.name &&
    ![...ignorePages, ...ignoreAuthCheckPages].includes(to.name) &&
    !pages.find(page => page.id.split('_')[1] == to.name) &&
    !to.query._f
  ) {
    // console.log('页面权限不足');
    // tips('页面权限不足');
    flag = false;
  }
  return flag;
};

export const authCheck = function(params = {}) {
  if (!pageAuthCheck(params)) {
    return false;
  } else if (!virtualLoginAuthCheck(params)) {
    return false;
  }
};

export const runAfterAuthCheck = function(params = {}) {
  const { router } = params;
  if (!pageAuthCheck(params)) {
    router.push({ path: '/exception/403?desc=抱歉，你没有权限操作' });
  } else if (!virtualLoginAuthCheck(params)) {
    // 清空cookie
    Cookie.remove(GLOBAL_CONFIG.TokenKey);
    Cookie.remove(GLOBAL_CONFIG.TokenKey + '_tag');
    // window.location.href = '/#/login';
    // search暂时不要， 后续如果需要一定的部分search 再说
    window.location.href = location.origin + location.pathname + '#/login';
  }
};

export default {};
