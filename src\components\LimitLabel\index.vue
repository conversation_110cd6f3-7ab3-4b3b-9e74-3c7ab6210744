<template>
  <span
    :class="['limit-label', mode, block && 'is-block', needCopy && 'need-copy']"
  >
    <!-- popover -->
    <template v-if="type === 'popover'">
      <a-popover
        :overlayClassName="
          `limit-label-popover ${popoverMergeProps.overlayClassName || ''}`
        "
        v-bind="popoverMergeProps"
        :visible="popoverVisible"
        @visibleChange="onVisibleChange"
        v-if="needTips"
      >
        <!-- <a-popover overlayClassName="limit-label-popover" v-if="limit < label.length || forcePop"> -->
        <template slot="content">
          <!-- <pre :style="{width: popoverWidth}">{{label}}</pre> -->
          <Prettier
            :style="contentStyle"
            :value="sourceLabel || label"
            :type="format"
            :dbType="dbType"
            v-if="format"
          ></Prettier>
          <pre
            class="tips"
            :style="contentStyle"
            v-html="srcLabel"
            v-else-if="renderHtml"
          ></pre>
          <pre class="tips" :style="contentStyle" v-else>{{ srcLabel }}</pre>
          <!-- <span>{{label}}</span> -->
          <slot name="popoverBottom"></slot>
        </template>
        <pre v-html="showLabel" v-if="renderHtml"></pre>
        <pre v-else>{{ showLabel }}</pre>
        <!-- <a-icon style="marginLeft:4px" type="question-circle" v-if="hasTips && !forcePop" /> -->
        <a-icon
          style="margin-left: 4px"
          type="question-circle"
          v-if="hasTips && mode === 'limit' && !needCopy"
        />
        <a-icon
          style="margin-left: 4px"
          type="copy"
          v-if="needCopy"
          @click.stop="onCopy"
        />
      </a-popover>
      <pre v-html="showLabel" v-else-if="renderHtml"></pre>
      <pre v-else>{{ showLabel }}</pre>
      <a-icon
        style="margin-left: 4px"
        type="copy"
        v-if="!needTips && needCopy"
        @click.stop="onCopy"
      />
    </template>
    <!-- modal -->
    <template v-if="type === 'modal'">
      <span class="link" v-if="needTips" @click="openModal">{{
        showLabel
      }}</span>
      <pre v-else>{{ showLabel }}</pre>
      <a-modal
        v-model="visible"
        title="详情"
        okText="关闭"
        v-bind="popoverMergeProps"
        :maskClosable="false"
        :width="'50%'"
        :dialogStyle="{ minWidth: '600px', maxWidth: '800px' }"
        @ok="closeModal"
        @cancel="closeModal"
      >
        <Prettier
          :style="contentStyle"
          :value="sourceLabel || label"
          :type="format"
          :dbType="dbType"
          v-if="format"
        ></Prettier>
        <pre :style="contentStyle" v-else>{{ srcLabel }}</pre>
      </a-modal>
    </template>
  </span>
</template>

<script>
// import _ from 'lodash';
// import Http from '@/utils/request';
// import _ from 'lodash';
// import SqlHighlight from '@/components/SqlHighlight';
import Prettier from '@/components/Prettier';
import hljs from 'highlight.js';
// import ResizeObserver from 'resize-observer-polyfill';

export default {
  inheritAttrs: false,
  components: { Prettier },
  props: {
    dbType: String,
    // limit, ellipsis
    mode: {
      type: String,
      default: 'limit'
    },
    block: {
      type: Boolean,
      default: false
    },
    limit: Number,
    limitLine: Number,
    label: {
      type: String,
      default: ''
    },
    sourceLabel: {
      type: String,
      default: ''
    },
    hasTips: {
      type: Boolean,
      default: true
    },
    isNeedTips: {
      type: Boolean,
      default: true
    },
    needCopy: {
      type: Boolean,
      default: false
    },
    contentStyle: {
      type: Object,
      default: () => ({})
    },
    // forcePop: {
    //   type: Boolean,
    //   default: false
    // },
    type: {
      type: String,
      default: 'popover'
    },
    format: {
      type: String,
      default: ''
    },
    popoverProps: {
      type: Object,
      default: () => ({})
    },
    nowrap: {
      type: Boolean,
      default: false
    },
    ignoreFirstLineComment: {
      type: Boolean,
      default: false
    },
    highlight: String,
    useHtml: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      popoverVisible: false,
      visible: false
      // isOverWidth: false
    };
  },
  computed: {
    renderHtml() {
      return this.highlight || this.useHtml;
    },
    showLabel() {
      let { label, limit, limitLine, nowrap, ignoreFirstLineComment } = this;
      label = label || ''; // 防止后端传值为null
      let lineArr = label.split('\n');
      let lineString = ignoreFirstLineComment
        ? label
            .replace(/^\/\/.*(\r|\n)/g, '')
            .replace(/\s+/g, ' ')
            .replace(/\/\*\*([\s\S]+)\*\//g, '')
            .replace(/[\r\n]/g, ' ')
        : label.replace(/\s+/g, ' ').replace(/[\r\n]/g, ' ');
      let res = '';

      if (nowrap) label = lineString;
      if (this.mode === 'ellipsis') {
        res = label;
      } else {
        res =
          limitLine > 0 && lineArr.length > limitLine
            ? lineArr.slice(0, limitLine).join('\n') +
              (this.hasTips ? '...' : '')
            : limit < label.length
            ? label.substring(0, limit) + (this.hasTips ? '...' : '')
            : label;
      }
      if (this.highlight) {
        res = hljs.highlight(this.highlight, res).value;
      }
      return res;
    },
    srcLabel() {
      const { sourceLabel, label } = this;
      let res = sourceLabel || label;
      if (this.highlight) {
        res = hljs.highlight(this.highlight, res).value;
      }
      return res;
    },
    needTips() {
      let { label, limit, limitLine, isNeedTips } = this;
      label = label || '';
      const lineArr = label.split('\n');
      if (!isNeedTips) return false;
      return (
        this.mode === 'ellipsis' ||
        (limitLine > 0 && lineArr.length > limitLine) ||
        limit < label.length
      );
    },
    popoverMergeProps() {
      const { popoverProps, mode } = this;
      const defaultProps = {
        placement: mode === 'ellipsis' ? 'topLeft' : undefined
      };
      return Object.assign({}, defaultProps, popoverProps);
    }
  },
  created() {},
  beforeDestroy() {
    // if (this.ro) {
    //   this.ro.unobserve(this.$el);
    //   this.ro = null;
    // }
  },
  mounted() {
    // this.setOverWidth();
    // // 监听
    // this.ro = new ResizeObserver((entries, observer) => {
    //   this.setOverWidth();
    // });
    // this.ro.observe(this.$el);
  },
  methods: {
    openModal() {
      this.visible = true;
    },
    closeModal() {
      this.visible = false;
    },
    onVisibleChange(e) {
      this.popoverVisible =
        this.mode === 'ellipsis' && !this.isOverWidth() ? false : e;
    },
    isOverWidth() {
      if (!this.$el) {
        return;
      }
      // console.log(this.$el.querySelector('pre').offsetWidth, this.$el.offsetWidth);
      return this.$el.querySelector('pre').offsetWidth > this.$el.offsetWidth;
    },
    onCopy() {
      CommonUtil.copy({
        value: this.label
      });
    }
    // setOverWidth() {
    //   const el = this.$el;
    //   const pre = el.querySelector('pre');
    //   const realWidth = _.max([
    //     pre.getBoundingClientRect().width,
    //     pre.scrollWidth
    //   ]);
    //   console.log(
    //     el.getBoundingClientRect().width,
    //     el.scrollWidth,
    //     el.offsetWidth,
    //     realWidth
    //   );
    //   this.isOverWidth = realWidth > el.offsetWidth;
    // }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.limit-label {
  pre {
    overflow: visible;
    white-space: pre-wrap;
    font-size: 12px;
    display: inline;
    font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system,
      BlinkMacSystemFont, 'Segoe UI', 'Hiragino Sans GB', 'Helvetica Neue',
      Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
      'Segoe UI Symbol';
  }
  .link {
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }

  &.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: normal;
    white-space: normal;
    &.need-copy {
      position: relative;
      padding-right: 16px;
      > span {
        .anticon-copy {
          position: absolute;
          top: 4px;
          right: 0;
        }
      }
    }
    pre {
      white-space: nowrap;
    }
  }
  &.is-block {
    display: inline-block !important;
    width: 100%;
  }
  .anticon-copy {
    cursor: pointer;
    &:hover {
      color: @primary-color;
    }
  }
}
</style>
<style lang="less">
.ant-popover.limit-label-popover {
  z-index: 1051;
}
</style>
