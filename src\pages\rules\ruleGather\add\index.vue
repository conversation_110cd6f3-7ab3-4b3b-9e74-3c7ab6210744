<template>
  <div class="config-rules-add">
    <ContentNew type="add" ref="ruleGather" />
    <div class="frame-button-wrapper">
      <a-button @click="onCancel" class="highlight">取消</a-button>
      <a-button @click="onSave" type="primary">保存并启用</a-button>
    </div>
  </div>
</template>

<script>
import ContentNew from '../components/ContentNew/index';
import { ruleSetAdd } from '@/api/config/rule';

export default {
  components: { ContentNew },
  props: {},
  data() {
    const dbType = window.localStorage.getItem('db_type') || '';
    return {
      dbType
    };
  },
  mounted() {},
  created() {},
  methods: {
    onCancel() {
      this.$router.push({ name: 'ruleGather-config' });
    },
    onSave() {
      let _ref = this.$refs.ruleGather;
      let { baseInfo, tableEdit } = _ref.$refs;
      baseInfo.validate((valid, error) => {
        if (valid) {
          let selectedRowKeys = tableEdit.selectedRowKeys || [];
          const formData = baseInfo.getData();
          let parmas = {
            ...formData,
            rule: selectedRowKeys.join(',') || ''
          };

          if (selectedRowKeys.length <= 0) {
            this.$hideLoading({
              method: 'warn',
              tips: '请选择规则'
            });
            return;
          }
          this.$showLoading();
          ruleSetAdd(parmas)
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.$hideLoading({ tips: _.get(res, 'data.message') });
                this.$router.push({ name: 'ruleGather-config' });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
