<template>
  <div class="database-audit-top-sql-chart">
    <a-row>
      <a-col :span="10">
        <div class="search-area">
          <div class="title">SORT BY</div>
          <div class="search-content">
            <Select
              v-bind="sortParams"
              v-model="sortBy"
              @change="handleChange"
              :allowClear="false"
            />
            <!-- <div class="show-time" v-if="isShow">
              <custom-icon type="calendar"></custom-icon>
              <span>{{exeTime}}</span>
              <custom-icon type="close-circle" @click="close"></custom-icon>
            </div>-->
          </div>
        </div>
        <a-spin :spinning="loading">
          <Table
            ref="table"
            v-bind="tableParams"
            :dataSource="dataSource || []"
            class="top-sql-chart-table"
            :rowClassName="getRowClassName"
          >
            <a
              slot="sql_text"
              slot-scope="{ record, text }"
              @click="onSelect(record)"
            >
              <BarLabel
                :label="text"
                :max="record.max_value"
                :current="record.value"
              ></BarLabel>
            </a>
            <span slot="value" slot-scope="{ record, text }">{{
              text + record.unit
            }}</span>
          </Table>
        </a-spin>
      </a-col>
      <a-col style="padding: 16px" :span="14">
        <div class="chart-area">
          <div class="title" v-if="!isSelected">Overview</div>
          <div v-else class="back-content">
            <a class="back" @click="back"> <custom-icon type="left" />返回 </a>
            <span>
              <custom-icon type="lu-icon-ID" />
              {{ sqlId || '--' }}
            </span>
            <span v-if="dbType !== 'ORACLE'">
              <custom-icon type="lu-icon-database" />
              {{ executiveSchema || '--' }}
            </span>
            <span>
              <custom-icon type="lu-icon-user1" />
              {{ executiveUser || '--' }}
            </span>
          </div>
        </div>
        <div>
          <a-spin :spinning="lineLoading">
            <div style="height: 300px; margin-bottom: 20px">
              <Chart :option="lineOption" ref="line" />
            </div>
          </a-spin>

          <a-spin :spinning="columnLoading">
            <div style="height: 300px; margin-bottom: 20px">
              <Chart :option="columnOption" ref="column" />
            </div>
          </a-spin>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import Chart from '@/components/Chart';
import Table from '@/components/Table';
import BarLabel from './BarLabel';
import Select from '@/components/Select';
import moment from 'moment';
import config from './config';
import {
  getTopFiveChart,
  getAllTargetChart,
  getExeTimeChart
} from '@/api/databaseaudit/topsql';
export default {
  components: { Chart, Table, BarLabel, Select },
  props: {
    id: Number | String,
    searchData: {
      type: Object,
      default: () => {}
    },
    unit: {
      type: String,
      default: 'ms'
    },
    dbType: {
      type: String,
      default: 'MYSQL'
    }
  },
  data() {
    this.config = config(this);
    const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
    const startTime = moment()
      .subtract(48, 'hour')
      .format('YYYY-MM-DD HH:mm:ss');
    const sortByObj = {
      ORACLE: 'execution_average_time',
      MYSQL: 'execution_average_time',
      DB2: 'total_cpu_time',
      GAUSSDB: 'cpu_time',
      GOLDENDB: 'execution_average_time'
    };
    const sortBy = sortByObj[this.dbType];
    return {
      loading: false,
      lineLoading: false,
      columnLoading: false,
      lineOption: null,
      columnOption: null,
      dataSource: [],
      tableParams: {
        url: '/sqlreview/after_audit/get_sort_chart',
        isInitReq: false,
        reqParams: {},
        columns: this.config.columns,
        showHeader: false,
        rowKey: 'id'
      },
      sortParams: {
        url: '/sqlreview/after_audit/get_sort_by_data',
        reqParams: { data_source_id: this.id },
        placeholder: '请选择'
      },
      executionTime: [startTime, endTime].toString(),
      sortBy: sortBy,
      sqlId: null,
      tableId: null,
      executiveSchema: null,
      executiveUser: null,
      isSelected: false,
      isShow: false,
      exeTime: null,
      clickNum: 1,
      params: {},
      columnData: {}
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.$set(this.tableParams, 'reqParams', {
      data_source_id: this.id,
      sort_by: this.sortBy,
      execution_time: this.executionTime,
      unit_type: this.unit,
      ...this.searchData
    });
  },
  methods: {
    init(params) {
      this.getTopFiveData(params);
      this.getExeTimeChartData(params);
    },
    getTopFiveData(params = {}) {
      this.lineLoading = true;
      getTopFiveChart(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.lineLoading = false;
            const resData = _.get(res, 'data.data');
            this.lineOption = this.config.topOption(resData);
            this.$hideLoading({ duration: 0 });
          } else {
            this.lineLoading = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.lineLoading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getExeTimeChartData(params = {}) {
      this.columnLoading = true;
      getExeTimeChart(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.columnLoading = false;
            const resData = _.get(res, 'data.data');
            this.columnData = resData;
            this.columnOption = this.config.exeTimeOption(resData);
            // 绑定事件
            this.bindEvent();
            this.$hideLoading({ duration: 0 });
          } else {
            this.columnLoading = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.columnLoading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 绑定事件
    bindEvent() {
      setTimeout(() => {
        const { column } = this.$refs;
        column &&
          column.on('click', e => {
            this.exeTimeClick(e, e.dataIndex);
          });
      }, 300);
    },
    getRowClassName(record) {
      const id = record.id;
      if (this.tableId == id) {
        return 'selected';
      }
      return '';
    },
    // 表格选中
    onSelect(record) {
      this.sqlId = record.sql_id;
      this.tableId = record.id;
      this.executiveSchema = record.executive_schema;
      this.executiveUser = record.executive_user;
      this.isShow = false;
      const params = {
        sql_id: record.sql_id,
        data_source_id: this.id,
        sort_by: this.sortBy,
        execution_time: this.executionTime,
        executive_schema: record.executive_schema,
        executive_user: record.executive_user,
        unit_type: this.unit
      };
      this.lineLoading = true;
      this.columnLoading = true;
      getAllTargetChart(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.lineLoading = false;
            this.columnLoading = false;
            const resData = _.get(res, 'data.data');
            this.lineOption = this.config.exeNumberOption(resData, this.dbType);
            this.columnOption = this.config.exeMaxTimeOption(
              resData,
              this.dbType
            );
            this.isSelected = true;
            this.$hideLoading({ duration: 0 });
          } else {
            this.lineLoading = false;
            this.columnLoading = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.lineLoading = false;
          this.columnLoading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 耗时报表选中
    exeTimeClick(data = {}) {
      if (data.componentType === 'xAxis') {
        if (this.exeTime == data.value) {
          this.clickNum++;
        }
        if (this.clickNum == 2) {
          this.close();
          this.getTopFiveData({
            ...this.params,
            date: undefined
          });
          this.columnOption = this.config.exeTimeOption(this.columnData);
          return;
        }
        this.isShow = true;
        this.exeTime = data.value;

        const startTime = data.value + ' ' + '00:00:00';
        const endTime = data.value + ' ' + '23:59:59';
        const { table } = this.$refs;
        const { searchParams } = table;
        Object.assign(searchParams, { date: [startTime, endTime].toString() });
        table.refresh();
        this.getTopFiveData({
          ...this.params,
          date: [startTime, endTime].toString()
        });
        this.columnOption = this.config.exeTimeOption(
          this.columnData,
          data.value
        );
      }
    },
    close() {
      this.isShow = false;
      this.exeTime = null;
      this.clickNum = 1;
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { date: undefined });
      table.refresh();
    },
    // overview 返回
    back() {
      this.isSelected = false;
      this.getTopFiveData(this.params);
      this.getExeTimeChartData(this.params);
      this.sqlId = null;
      this.tableId = null;
      this.exeTime = null;
      // this.getRowClassName({});
    },
    // sort by选项
    handleChange(e) {
      this.sortBy = e;
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { sort_by: e });
      table.refresh();
      this.$set(this.params, 'sort_by', e);
      // 表格选中状态，清除
      this.back();
    },
    refresh(data = {}) {
      this.isShow = false;
      this.isSelected = false;
      this.sqlId = null;
      this.tableId = null;

      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        ...data,
        execution_time: data.execution_time
          ? data.execution_time
          : this.executionTime
      });
      table.refresh();
    }
  },
  watch: {
    searchData: {
      handler(newVal) {
        const executionTime = newVal.execution_time || this.executionTime;
        this.executionTime = executionTime;
        this.params = {
          data_source_id: this.id,
          sort_by: this.sortBy,
          ...newVal,
          execution_time: this.executionTime,
          unit_type: this.unit
        };
        this.init(this.params);
      },
      immediate: true
    },
    unit: {
      handler(newVal) {
        this.params = {
          ...this.params,
          unit_type: newVal
        };

        const { table } = this.$refs;
        const { searchParams } = table;
        Object.assign(searchParams, { unit_type: newVal });

        this.back();
        table.refresh();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.database-audit-top-sql-chart {
  // height: 800px;
  .ant-row {
    // height: 800px;
    .ant-col {
      // height: 800px;
      /deep/.search-area {
        background: #ffffff;
        padding: 16px 24px;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        border-radius: 12px 12px 0 0;
        .title {
          font-size: 14px;
          color: #27272a;
          font-weight: 600;
          margin-right: 12px;
        }
        .search-content {
          display: flex;
          align-items: center;
          .ant-select {
            width: 160px;
            margin-right: 16px;
            .ant-select-selection--single {
              height: 28px;
              .ant-select-selection__rendered {
                line-height: 28px;
              }
            }
          }
          // .show-time {
          //   padding: 5px 6px 2px 6px;
          //   border-radius: 4px;
          //   border: 1px solid #1890ff;
          //   display: flex;
          //   align-items: center;
          //   span,
          //   .anticon {
          //     font-size: 12px;
          //     color: #1890ff;
          //     position: relative;
          //     top: -1px;
          //   }
          //   span {
          //     margin: 0 4px;
          //   }
          //   .anticon-close-circle {
          //     &:hover {
          //       cursor: pointer;
          //     }
          //   }
          // }
        }
      }
      /deep/.custom-table {
        &.top-sql-chart-table {
          .ant-table-wrapper {
            .ant-table {
              .ant-table-content {
                .ant-table-tbody {
                  tr {
                    td {
                      border-bottom: none !important;
                      padding: 10px 0px 10px 16px;
                      .bar-label {
                        border: 2px solid rgba(77, 181, 242, 0);
                      }
                    }
                    &.selected {
                      td {
                        background: rgb(234, 234, 235) !important;
                        .bar-label {
                          border: 2px solid #4db5f2;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      .chart-area {
        .title {
          font-size: 12px;
          color: #27272a;
          position: relative;
          margin-left: 12px;
          margin-bottom: 20px;
          &::before {
            content: '';
            position: absolute;
            top: 4px;
            left: -10px;
            width: 4px;
            height: 10px;
            background: #008adc;
          }
        }
        .back-content {
          margin-bottom: 20px;
          .back {
            font-size: 14px;
            color: #008adc;
            margin-right: 6px;
            .anticon {
              font-size: 14px;
              margin-right: 2px;
              color: #008adc;
            }
          }
          > span {
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 16px;
            font-size: 13px;
            margin-right: 12px;
            color: #27272a;
          }
        }
      }
    }
  }
}
</style>