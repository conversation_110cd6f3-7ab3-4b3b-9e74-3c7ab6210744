<template>
  <a-cascader
    v-bind="cascaderProps"
    v-on="cascaderListeners"
    :class="className"
    :value="cascaderValue"
    :options="data"
    :loadData="loadData"
  ></a-cascader>
</template>

<script>
import Request from './request';

const defaultProps = {
  placeholder: '请选择',
  allowClear: true,
  showSearch: true,
  getPopupContainer: el => {
    // return document.getElementById('rootContent');
    return el.parentNode;
  }
};
export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {},
  props: {
    value: Array,
    url: String,
    reqParams: Function,
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    noCache: {
      type: Boolean,
      default: true
    },
    loaded: Function
  },
  data() {
    return {
      data: []
    };
  },
  computed: {
    cascaderProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    cascaderListeners() {
      return { ...this.$listeners, ...{ change: this.handleChange } };
    },
    className() {
      let arr = [];
      return ['custom-cascader'].concat(arr);
    },
    cascaderValue() {
      return this.value;
    }
  },
  created() {
    const { options } = this.cascaderProps;
    // 有options，优先使用传入的options
    if (options && options.length > 0) {
      this.data = options;
      return;
    }
    this.getList();
  },
  mounted() {},
  methods: {
    // 模拟请求
    getList(refresh = false, pnode) {
      const { url, reqParams = () => {}, noCache } = this;
      if (!url) return;

      Request({
        url,
        reqParams: {
          ...(reqParams(pnode) || {})
        },
        noCache,
        refresh
      })
        .then(res => {
          let _data = res
            // .filter(item => item.visible !== false)
            .map(item => {
              return {
                ...item,
                label: _.get(item, this.labelKey),
                value: _.get(item, this.valueKey)
              };
            });

          if (!pnode) {
            this.data = _data;
          } else {
            pnode.children = _data;
          }

          if (_.isFunction(this.loaded)) {
            this.loaded(_data, this.data);
          }
        })
        .catch(e => {
          console.log(e);
          this.data = [];
          // this.value = undefined;
          this.$emit('change', undefined);
          // this.$message.error(e || '请求失败');
          this.$hideLoading({ method: 'error', tips: e || '请求失败' });
        })
        .finally(() => {
          if (pnode) {
            pnode.loading = false;
          }
          this.data = [...this.data];
        });
    },
    refresh() {
      this.data = [];
      this.getList(true);
    },
    loadData(selectedOptions) {
      const targetOption = selectedOptions[selectedOptions.length - 1];
      targetOption.loading = true;
      // console.log(selectedOptions, 11111);

      this.getList(true, targetOption);
    },
    // 监听变化
    handleChange(value) {
      console.log(value, 'change');
      this.$emit('change', value);
    },
    // 获取元数据item
    getSourceItem(key) {
      // return this.sourceData.find(item => item[this.valueKey] == key);
    }
  },
  watch: {
    url: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.refresh();
        } else {
          this.data = this.$attrs.options || [];
        }
      }
      // immediate: true,
      // deep: true
    },
    reqParams: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.refresh();
        }
      }
      // immediate: true,
      // deep: true
    },
    '$attrs.options': {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.data = newVal;
        }
      }
      // immediate: true,
      // deep: true
    },
    value(newVal) {
      // console.log('get value', newVal);
      // if (this.backSearch) {
      //   this.handleSearch();
      // }
    }
  }
};
</script>

<style lang="less" scoped>
.custom-cascader {
}
</style>
<style lang="less">
</style>
