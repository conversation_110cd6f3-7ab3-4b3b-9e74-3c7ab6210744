<template>
  <div class="project-group-import" style="margin-top: -32px;">
    <a-progress
      :percent="percent"
      status="active"
      size="small"
      v-if="percent !== 0"
      :show-info="false"
    ></a-progress>
    <div v-if="percent== 0" style="margin-bottom: 32px;"></div>
    <a-spin :spinning="loading">
      <div class="steps-box">
        <!-- <a-steps :current="current" size="small" :style="{marginBottom: '30px'}" class="arrow"> -->
        <a-steps :current="current" :style="{marginBottom: '30px'}">
          <a-step v-for="item in steps" :key="item.key" :title="item.title" />
        </a-steps>
      </div>
      <!-- 内容区域 -->
      <!-- <keep-alive> -->
      <template v-for="(item, index) in steps">
        <component
          :ref="item.ref"
          :is="item.key"
          :props="item.props"
          :key="item.key"
          v-if="current === index"
          @onChange="onChange"
          @submitProgress="submitProgress"
          :compData="compData"
          :type="type"
        ></component>
      </template>
      <!-- </keep-alive> -->
    </a-spin>
  </div>
</template>
<script>
import Upload from '../../components/upload/index';
import Import from '../../components/import/index';

export default {
  components: { Upload, Import },
  data() {
    return {
      percent: 0,
      loading: false,
      current: 0,
      type: 'projectGroup',
      steps: [
        {
          key: 'Upload',
          ref: 'upload',
          title: '上传项目组信息',
          props: {}
        },
        {
          key: 'Import',
          ref: 'import',
          title: '导入',
          props: {}
        }
      ],
      compData: {}
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    onChange(curr, data = {}) {
      this.current = curr;
      this.compData = data;
    },
    // 提交进度
    submitProgress(data) {
      this.percent = data;
    }
  }
};
</script>

<style lang="less" scoped>
.project-group-import {
  width: 100%;

  .steps-box {
    display: flex;
    justify-content: center;
    .ant-steps {
      width: 60%;
    }
  }
}
</style>
