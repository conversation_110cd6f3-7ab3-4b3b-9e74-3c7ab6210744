export default function (ctx) {
  const columns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id',
    //   width: 100
    // },
    {
      title: '项目组英文名',
      dataIndex: 'gr_en',
      key: 'gr_en',
      width: 160
    },
    {
      title: '项目组中文名',
      dataIndex: 'gr_cn',
      key: 'gr_cn',
      width: 160
    },
    {
      title: '项目组负责人',
      dataIndex: 'gr_mgr',
      key: 'gr_mgr',
      scopedSlots: { customRender: 'gr_mgr' },
      width: 280
    },
    {
      title: 'DBA负责人',
      dataIndex: 'dba_mgr',
      key: 'dba_mgr',
      scopedSlots: { customRender: 'dba_mgr' },
      width: 280
    },
    {
      title: '项目组邮箱',
      dataIndex: 'gr_email',
      key: 'gr_email',
      scopedSlots: { customRender: 'gr_email' },
      width: 360
    },
    {
      title: '关联项目',
      dataIndex: 'projects_name',
      key: 'projects_name',
      scopedSlots: { customRender: 'projects_name' },
      width: 160
    },
    {
      title: '项目组成员',
      dataIndex: 'user_set',
      key: 'user_set',
      scopedSlots: { customRender: 'user_set' },
      width: 160
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 110,
      fixed: 'right',
      visible: $permissionBatch.some([
        { module: 'projectGroup', values: ['edit', 'delete'] }
      ])
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = () => {
    return [
      {
        type: 'Input',
        label: '项目组英文名',
        key: 'gr_en',
        width: '360',
        rules: [{ required: true, message: '该项为必填项' }]
      },
      {
        type: 'Input',
        label: '项目组中文名',
        key: 'gr_cn',
        width: '360',
        rules: [{ required: true, message: '该项为必填项' }]
      },
      {
        type: 'Select',
        label: '项目组负责人',
        key: 'gr_mgr',
        width: '360',
        props: {
          mode: 'multiple',
          url: '/sqlreview/project_config/select_user',
          reqParams: { type: 'leader' },
          allowSearch: true,
          backSearch: true,
          limit: 20
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Select',
        label: '项目组成员',
        key: 'gr_bind_user',
        width: '360',
        props: {
          mode: 'multiple',
          url: '/sqlreview/project_config/select_user',
          reqParams: { type: 'user' },
          allowSearch: true,
          backSearch: true,
          limit: 20
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Select',
        label: 'DBA负责人',
        key: 'dba_mgr',
        width: '360',
        props: {
          mode: 'multiple',
          url: '/sqlreview/project_config/select_user',
          reqParams: { type: 'dba' },
          allowSearch: true,
          backSearch: true,
          limit: 20
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Input',
        label: '项目组邮箱',
        key: 'gr_email',
        width: '360',
        props: {
          placeholder: '请输入，多个邮箱用“,”分割'
        }
      },
      {
        type: 'Select',
        label: '关联项目',
        key: 'projects',
        width: '360',
        props: {
          mode: 'multiple',
          url: '/sqlreview/project/list-all',
          reqParams: {},
          allowSearch: true,
          backSearch: true,
          limit: 20
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
    ];
  };
  const searchFields = [
    {
      type: 'Input',
      label: '项目组名',
      key: 'name',
      mainSearch: true,
      props: {
        placeholder: '请输入项目组名'
      }
    },
    {
      type: 'Select',
      label: '项目组负责人',
      key: 'gr_mgr',
      props: {
        url: '/sqlreview/project_config/select_user',
        reqParams: { type: 'leader' },
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'Select',
      label: 'DBA负责人',
      key: 'dba_mgr',
      props: {
        url: '/sqlreview/project_config/select_user',
        reqParams: { type: 'dba' },
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'Input',
      label: '邮箱',
      key: 'gr_email',
      props: {
        placeholder: '请输入'
      }
    }
  ];
  return {
    columns,
    searchFields,
    fields
  };
}
