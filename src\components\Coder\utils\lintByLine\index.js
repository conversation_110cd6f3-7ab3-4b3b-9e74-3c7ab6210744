import './style.less';

const lint = function (list = []) {
  console.log('lint by line');
  const cm = this.coder;
  const doc = cm.getDoc();
  // const errTipsPanel = this.$el.querySelector('.CodeMirror-errTips-panel');
  // 先清空
  doc.clearGutter('CodeMirror-lintbyline');
  this.errLines.forEach(item => {
    doc.removeLineClass(item.line, 'CodeMirror-lintbyline-err-line');
  });

  list.forEach(item => {
    const line = item.line;
    if (line != null) {
      doc.addLineClass(line, '', 'CodeMirror-lintbyline-err-line');
      const err = document.createElement('div');
      err.className = 'CodeMirror-lintbyline-item';
      let errTipsPanel = null;
      err.addEventListener('mouseenter', (e) => {
        // console.log('mouseenter');
        err._timeout = setTimeout(() => {
          errTipsPanel = document.createElement('div');
          errTipsPanel.className = 'CodeMirror-lintbyline-item-tips';
          errTipsPanel.style.left = e.pageX + 4 + 'px';
          errTipsPanel.style.top = e.pageY + 4 + 'px';
          errTipsPanel.innerHTML = `${item.tips || ''}`;
          document.body.appendChild(errTipsPanel);
        }, 50);
      });
      err.addEventListener('mousemove', (e) => {
        if (errTipsPanel) {
          errTipsPanel.style.left = e.pageX + 4 + 'px';
          errTipsPanel.style.top = e.pageY + 4 + 'px';
        }
      });
      err.addEventListener('mouseleave', (e) => {
        // console.log('mouseleave');
        if (err._timeout) {
          clearTimeout(err._timeout);
          err._timeout = null;
        }
        errTipsPanel && document.body.removeChild(errTipsPanel);
        errTipsPanel = null;
      });
      doc.setGutterMarker(line, 'CodeMirror-lintbyline', err);
    }
  });
  this.errLines = list;
}

export default lint;