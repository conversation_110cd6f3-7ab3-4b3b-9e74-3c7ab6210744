import Vue from 'vue';
import axios from 'axios';
import Config from './config';
import <PERSON><PERSON> from 'js-cookie';
// import Router from '@/router/lazy';
import _ from 'lodash';
// import { setLoginTarget } from '@/utils/login';
// import store from '../store';
require('promise.prototype.finally').shim();
function getBus() {
  return Vue.prototype.$bus;
}

// 全局默认值
axios.defaults.baseURL = process.env.API_HOST;

// create an axios instance
export const Http = axios.create({
  timeout: Config.Timeout * 1000 // request timeout
});

// 提示
const tips = (message, method, params = {}) => {
  getBus().$emit('showScreenLoading', false, {
    tips: message || '请求失败',
    method: method || 'error',
    ...params
  });
};
// 统一提示，屏蔽具体业务接口提示
const unifiedTipsCodeMap = {};
const unifiedTips = (code, message, method, params) => {
  if (unifiedTipsCodeMap[code]) {
    clearTimeout(unifiedTipsCodeMap[code]);
  }
  // window.LAYOUT_APP.loadingPaused = true;
  unifiedTipsCodeMap[code] = setTimeout(() => {
    // window.LAYOUT_APP.loadingPaused = false;
    delete unifiedTipsCodeMap[code];
    Vue.prototype.$message.destroy();
    Vue.prototype.$notification.destroy();
    message && tips(message, method, params);
  });
};

// 关闭全屏遮罩
const closeFullscreenMask = () => {
  window.LAYOUT_APP && (window.LAYOUT_APP.showLoading = false);
};

// request interceptor
Http.interceptors.request.use(
  (config) => {
    // 添加Authorization
    config.headers['Authorization'] = Cookie.get(Config.TokenKey) || Cookie.get(Config.LuTokenKey);
    // 添加lu-token
    // config.headers['lu-token'] = Cookie.get(Config.LuTokenKey);
    // 添加cancelToken
    config.cancelToken = new axios.CancelToken(cancel => {
      window._axiosPromiseArr.push({ cancel });
      if (config.getCancel) {
        config.getCancel(cancel);
      }
    });
    // 三方登录添加API-SRC-TOKEN
    config.headers['API-SRC-TOKEN'] = Cookie.get(Config.LuTokenKey);

    // console.groupCollapsed('request: ' + config.url);
    // console.log(config);
    // console.groupEnd();
    return config;
  },
  (error) => {
    tips(error.message);
    console.log('---------- request error ----------');
    Promise.reject(error);
  }
);

// response interceptor
Http.interceptors.response.use(
  (response) => {
    let data = _.get(response, 'data.data') || {};
    if (_.get(response, 'data.code') == '401') {
      // 过期跳转登录页面
      // Router.push({ name: 'login' });
      window.Login.go(data);
    } else if (_.get(response, 'data.code') == '4000') {
      // 警告提示
      unifiedTips('4000', _.get(response, 'data.message'), 'warn');
    } else if (_.get(response, 'data.code') == '10001') {
      // 激活码过期跳转登录页面
      // Router.push({ name: 'login' });
      let bool = data && data.is_third_login;
      if (bool) {
        getBus().$emit('openActivationCodeModal', data);
        unifiedTips('10001', '', 'warn');
        return;
      }
      window.Login.go(data);
      sessionStorage.setItem('isActivation', true);
    } else if (_.get(response, 'data.code') == '503') {
      unifiedTips('503', _.get(response, 'data.message'), 'error');
      window.Login.go(data);
    }
    // console.groupCollapsed('response: ' + response.config.url);
    // console.log(response);
    // console.groupEnd();
    return response;
  },
  (error) => {
    // tips(error.message);
    const { status, data = {} } = _.get(error, 'response') || {};
    if (status == '401') {
      // 三方登录
      // if (data.url) {
      //   // 清空token，设置login_target等
      //   setLoginTarget();
      //   sessionStorage.setItem(
      //     'lastUserName',
      //     _.get(store.state.account, 'user.name')
      //   );
      //   Cookie.remove(GLOBAL_CONFIG.TokenKey);
      //   // lufax跳转登录
      //   Cookie.remove(GLOBAL_CONFIG.LuTokenKey);
      //   // 第三方登录
      //   Cookie.remove(GLOBAL_CONFIG.ThirdTokenKey);
      //   Cookie.remove('but');
      //   data.domain && Cookie.remove('but', { domain: data.domain });
      //   // 拼接redirectUrl，去除_token参数
      //   let searchStr = location.search
      //     .slice(1)
      //     .split('&')
      //     .filter((item) => item && !item.startsWith('_token='))
      //     .join('&');
      //   searchStr = searchStr ? `?${searchStr}` : searchStr;
      //   let redirectUrl =
      //     location.origin + location.pathname + searchStr + location.hash;
      //   let LuLoginUrl = data.url || GLOBAL_CONFIG.LuLoginUrl;
      //   location.href = LuLoginUrl + '?redirect=' + redirectUrl;
      //   return;
      // }
      _.set(error, 'response.data.message', '[401]');
      // 过期跳转登录页面
      window.Login.go(data);
    } else if (status == '403') {
      // Router.push({ name: 'exception', params: { status } });
      // setTimeout(() => {
      //   Vue.prototype.$message.destroy();
      //   Vue.prototype.$notification.destroy();
      //   tips('你没有权限操作');
      // });
      _.set(error, 'response.data.message', '[403]');
      tips('你没有权限操作');
    } else if (error.toString() === 'Cancel') {
      // do nothing
      // setTimeout(() => {
      //   Vue.prototype.$message.destroy();
      //   Vue.prototype.$notification.destroy();
      // });
      _.set(error, 'response.data.message', '[cancel]');
    } else {
      // 关闭全局loading，防止外部请求没有按规范添加catch
      // setTimeout(() => {
      //   Vue.prototype.$message.destroy();
      //   Vue.prototype.$notification.destroy();
      //   tips();
      // });
    }
    closeFullscreenMask();
    console.log('---------- response error ----------');
    return Promise.reject(error);
  }
);

window.ReqUtil = {
  req({
    ctx,
    reqInstance,
    params = {},
    tips = '请求成功',
    cbk,
    err,
    needLoading = true
  }) {
    // 请求
    needLoading && ctx.$showLoading();
    return reqInstance(params)
      .then((res) => {
        if (CommonUtil.isSuccessCode(res)) {
          needLoading && ctx.$hideLoading({ tips });
          cbk && cbk(_.get(res, 'data.data'));
        } else {
          ctx.$hideLoading({
            method: 'error',
            tips: _.get(res, 'data.message')
          });
          err && err(res);
        }
        return _.get(res, 'data.data');
      })
      .catch((e) => {
        console.error(e);
        ctx.$hideLoading({
          method: 'error',
          tips: _.get(e, 'response.data.message') || '请求失败'
        });
      });
  }
};

export default function (config = {}) {
  return Http(config);
}
