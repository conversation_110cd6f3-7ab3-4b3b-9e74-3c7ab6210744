<template>
  <a-card :bordered="false" class="small-card">
    <div
      class="title-content"
      v-if="isProjectRview || isFileReview || isOrderRview"
    >
      <div class="title-flex" v-if="isProjectRview || isOrderRview">
        <div class="title-label">Review对象</div>
        <a-popover>
          <template slot="content">
            <span>{{ headerInfo.project_name }}</span>
          </template>
          <span class="title-value">{{ headerInfo.project_name || '--' }}</span>
        </a-popover>
      </div>
      <div class="title-flex" v-if="isFileReview">
        <div class="title-label">任务名称</div>
        <a-popover>
          <template slot="content">
            <span>{{ headerInfo.project_name }}</span>
          </template>
          <span class="title-value">{{ headerInfo.project_name || '--' }}</span>
        </a-popover>
      </div>
      <div class="title-flex">
        <div class="title-label">SQL来源</div>
        <a-popover>
          <template slot="content">
            <span>{{ headerInfo.file_path }}</span>
          </template>
          <span class="title-value file-path">{{
            headerInfo.file_name || '--'
          }}</span>
        </a-popover>
      </div>
      <div class="title-flex">
        <div class="title-label">数据库类型</div>
        <span class="title-value db-type">
          <template v-if="headerInfo.db_type && headerInfo.db_type.length >= 1">
            <DbImg
              v-for="item in headerInfo.db_type"
              :key="item"
              :value="item"
              :UseIconOnly="true"
            />
          </template>
          <span v-else>--</span>
        </span>
      </div>
      <div class="title-flex" v-if="isProjectRview || isOrderRview">
        <div class="title-label">代码框架</div>
        <span class="title-value file-path">{{
          headerInfo.sql_frame || '--'
        }}</span>
      </div>
      <div class="title-flex" v-if="isProjectRview || isOrderRview">
        <div class="title-label">SQL标签</div>
        <div class="sql-label">
          <LabelCard
            v-if="[0, 1].includes(headerInfo.label_attribute)"
            ref="labelCard"
            :labelStatus="headerInfo.label_status"
            :id="headerInfo.label_obj_id"
            :text="headerInfo.label_attribute"
            requstMode="everytime"
            @refresh="refresh"
          />
          <div
            v-else-if="
              (sqlLabelStatus == 1 || sqlLabelStatus == 2) &&
              jksuser !== 'jksuser'
            "
          >
            <div class="plus disabled" v-if="sqlLabelStatus == 1">
              <custom-icon type="plus" />
            </div>
            <div v-else class="plus" @click="addSqlTag">
              <custom-icon type="plus" />
            </div>
            <span class="color-title-value">特殊需求，可为SQL添加标签</span>
          </div>
          <div v-else>{{ '--' }}</div>
        </div>
      </div>
      <div class="title-flex" v-if="isFileReview">
        <div class="title-label">规则集</div>
        <span class="title-value file-path">{{
          headerInfo.rule_set && headerInfo.rule_set.length > 0
            ? headerInfo.rule_set.join()
            : '--'
        }}</span>
      </div>
    </div>
    <div class="title-content" v-else>
      <div class="title-flex">
        <div class="title-label">任务名称</div>
        <a-popover>
          <template slot="content">
            <span>{{ headerInfo.project_name }}</span>
          </template>
          <span class="title-value">{{ headerInfo.project_name || '--' }}</span>
        </a-popover>
      </div>
      <div class="title-flex">
        <div class="title-label">数据源</div>
        <span class="title-value db-type">
          <template v-if="dataSourceObj">
            <DbImg
              :value="dataSourceObj.db_type"
              :schemaName="
                dataSourceObj.name + '(' + dataSourceObj.db_url + ')'
              "
              mode="ellipsis"
            />
          </template>
          <span v-else>--</span>
        </span>
      </div>
      <div class="title-flex">
        <div class="title-label">Schema</div>
        <span class="title-value file-path">{{
          dataSourceObj && dataSourceObj.schema_name.length > 0
            ? dataSourceObj.schema_name.join()
            : '--'
        }}</span>
      </div>
      <div class="title-flex">
        <div class="title-label">SQL来源</div>
        <a-popover>
          <template slot="content">
            <span>{{ headerInfo.file_path }}</span>
          </template>
          <span class="title-value file-path">{{
            headerInfo.file_name || '--'
          }}</span>
        </a-popover>
      </div>
    </div>
  </a-card>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';
import Status from '@/components/Biz/Status';
import DbImg from '@/components/CustomImg/DbImg';
import LabelCard from '@/components/Biz/LabelCard';
export default {
  components: { LimitLabel, Status, DbImg, LabelCard },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    isWhite: {
      type: Boolean,
      default: true
    },
    sqlLabelStatus: Number | String,
    dataSourceList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    isProjectRview() {
      const name = this.$route.name;
      return name == 'project-review-review';
    },
    isOrderRview() {
      const name = this.$route.name;
      return name == 'orderReview';
    },
    isFileReview() {
      const name = this.$route.name;
      return name == 'file-review-review';
    },
    dataSourceObj() {
      return this.dataSourceList.length > 0 ? this.dataSourceList[0] : null;
    }
  },
  data() {
    const jksuser = this.$store.state.account.user.name;
    return {
      jksuser
    };
  },
  mounted() {},
  methods: {
    // 打标
    addSqlTag() {
      this.$emit('addSqlTag');
    },
    refresh() {
      this.$emit('refresh', { id: this.headerInfo.label_obj_id }, true);
    }
  },
  watch: {}
};
</script>
<style lang="less" scoped>
.small-card {
  border-radius: 8px;
  width: 32%;
  margin-right: 16px;
  .title-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .title-flex {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      border-bottom: 1px solid #e8e8e8;
      &:last-child {
        border-bottom: none;
      }
      .title-label {
        font-family: PingFangSC-Regular;
        color: #1f1f1f;
        white-space: nowrap;
        width: 100px;
      }
      .title-value {
        display: inline-block;
        margin: 4px 0 0 0;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
        color: #71717a;
        background: #ffffff;
        &.db-type {
          display: inline-block;
          max-width: 180px;
          .database-image {
            .limit-label {
              width: 120px;
            }
          }
          &::before {
            display: none;
          }
          .anticon {
            margin-right: 4px;
          }
        }
      }
      .sql-label {
        margin-top: 4px;
        .title-value {
          padding: 2px 12px;
          border-radius: 16px;
          border: 1px solid #e4e4e7;
          display: flex;
          align-items: center;
          &.audit-status-red {
            background: #e71d36;
            border: 1px solid #e71d36;
            > span {
              color: #fff;
            }
          }
          &::before {
            content: '';
            display: none;
          }
          > span {
            font-size: 12px;
            color: #a1a1aa;
          }
          .anticon {
            display: none;
            color: #fff;
            width: 16px;
            height: 16px;
            line-height: 18px;
            font-size: 12px;
            border-radius: 50%;
            margin-left: 4px;
            border: none;
          }
          &:hover {
            cursor: pointer;
            .anticon {
              background: #008adc;
              display: inline-flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
        a[disabled] {
          cursor: not-allowed;
          pointer-events: none;
          background: #f4f5f7;
          border: 1px solid #e4e4e7;
          > span {
            color: #a1a1aa;
          }
        }
        div {
          display: flex;
          align-items: center;
          .plus {
            width: 24px;
            height: 24px;
            min-width: 24px;
            min-height: 24px;
            border-radius: 12px;
            border: 1px solid #e4e4e7;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 6px;
            color: #71717a;
            &.disabled {
              &:hover {
                cursor: not-allowed;
                border: 1px solid #e4e4e7;
                color: #71717a;
              }
            }
            > .anticon {
              font-size: 12px;
              font-weight: 600;
            }
            &:hover {
              cursor: pointer;
              border: 1px solid #008adc;
              color: #008adc;
            }
          }
          .color-title-value {
            color: #f29339;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>