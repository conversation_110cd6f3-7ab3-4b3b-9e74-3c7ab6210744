<template>
  <a-modal
    v-model="visible"
    width="55%"
    title="表结构信息"
    :maskClosable="false"
    :mask="false"
    :footer="null"
    wrapClassName="sqldetail-total-info-modal"
    :maskStyle="{ pointerEvents: 'none' }"
    :bodyStyle="{ maxHeight: '600px', overflow: 'auto' }"
    @cancel="onCancel"
  >
    <a-spin :spinning="loading">
      <Form
        ref="baseInfo"
        class="baseInfoStyle fixed-label-left"
        v-bind="baseInfoParams"
        :formData="baseInfoData"
      />
      <a-tabs default-active-key="table">
        <a-tab-pane key="table" tab="表信息">
          <div v-for="item in tableData" :key="item.id">
            <div v-if="item.table_name" class="table-name-wrapper">
              <div class="table-name">
                <span>{{ item.table_name }}</span>
              </div>
            </div>
            <Table
              v-bind="tableParams"
              :dataSource="item.tableData"
              :rowKey="
                () => {
                  return Math.random();
                }
              "
              bordered
            >
              <span slot="table_name" slot-scope="{ record }">
                {{ record.table_name }}
              </span>
              <template slot="table_name" slot-scope="{ text }">
                <LimitLabel :label="text || ''" :limit="10"></LimitLabel>
              </template>
            </Table>
          </div>
          <custom-empty v-if="tableData.length === 0" />
        </a-tab-pane>
        <a-tab-pane key="index" tab="索引信息">
          <div
            class="table-name-wrapper"
            v-for="item in indexData"
            :key="item.table_name"
          >
            <div v-if="item.indexData" class="table-name">
              <span>{{ item.table_name }}</span>
            </div>
            <Table
              v-bind="indexParams"
              :columns="getColumns(item.table_name)"
              :dataSource="item.indexData"
              :rowKey="
                () => {
                  return Math.random();
                }
              "
              bordered
            >
              <span slot="titleFactor">
                {{ '聚簇因子' }}
                <a-popover>
                  <template slot="content">
                    <p>
                      索引块上的数据存储顺序与表数据存储顺序的差异性。
                      <br />
                      良好的CF值，会趋向于数据表的块数。
                      <br />
                      较差的CF值，会趋向于数据表的行数。
                    </p>
                  </template>
                  <a-icon type="question-circle" />
                </a-popover>
              </span>
            </Table>
          </div>
          <custom-empty v-if="indexData.length === 0" />
        </a-tab-pane>
        <a-tab-pane key="field" tab="字段信息">
          <div v-for="item in fieldData" :key="item.id">
            <div v-if="item.table_name" class="table-name-wrapper">
              <div class="table-name">
                <span>{{ item.table_name }}</span>
              </div>
            </div>
            <Table
              v-bind="fieldParams"
              :dataSource="item.fieldData"
              :rowKey="
                () => {
                  return Math.random();
                }
              "
              bordered
            >
              <span slot="titleNdv">
                {{ 'NDV值' }}
                <a-popover>
                  <template slot="content">
                    <p>对表字段唯一值个数的统计</p>
                  </template>
                  <a-icon type="question-circle" />
                </a-popover>
              </span>
            </Table>
          </div>
          <custom-empty v-if="fieldData.length === 0" />
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
import Form from '@/components/Form';
import LimitLabel from '@/components/LimitLabel';
import { getTableInfo } from '@/api/review';
import config from './config';

export default {
  components: { Table, Form, LimitLabel },
  props: {},
  data() {
    this.config = config(this);
    return {
      baseInfoData: {},
      baseInfoParams: {
        fixedLabel: true,
        layout: 'horizontal',
        fields: this.config.baseInfo,
        labelCol: { span: 2 },
        wrapperCol: { span: 4 }
      },
      visible: false,
      loading: false,
      tableParams: {
        columns: this.config.tableColumns,
        // pagination: false,
        rowKey: 'id'
      },
      tableData: [], // 表数据
      indexParams: {
        columns: this.config.indexColumns(),
        // pagination: false,
        rowKey: 'id'
      },
      indexData: [], // 索引数据
      table_name: null,
      fieldParams: {
        columns: this.config.fieldColumns,
        // pagination: false,
        rowKey: 'id'
      },
      fieldData: [] // 字段数据
    };
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.visible = true;
    },
    // 获取表格数据
    getTableInfoData(schema) {
      this.loading = true;
      ReqUtil.req({
        ctx: this,
        reqInstance: getTableInfo,
        params: {
          id: this.$route.query.id,
          schema: schema
        },
        needLoading: false,
        cbk: (data) => {
          this.tableData = []
          this.indexData = []
          this.fieldData = []
          data = data || [];
          data.forEach((item, index) => {
            this.tableData.push({
              id: index,
              table_name: item.table_name,
              tableData: item.info.table_info || []
            });
            this.fieldData.push({
              id: index,
              table_name: item.table_name,
              fieldData: item.info.column_info || []
            });
            this.indexData.push({
              id: index,
              table_name: item.table_name,
              indexData: item.info.index_info || []
            });
          });
        },
        err: (res) => {
          this.tableData = [];
          this.indexData = [];
          this.fieldData = [];
        }
      })
        .then(() => {
          this.$hideLoading({ duration: 0 });
          this.loading = false;
        })
        .catch((e) => {
          this.loading = false;
        });
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    getColumns(tableName) {
      const data = this.indexData.filter((itm) => itm.table_name === tableName);
      return this.config.indexColumns({
        combineInfo: this.combineIndexTable(data)
      });
    },
    combineIndexTable(data = []) {
      const combineColumns = ['table_name', 'index_name'];
      let map = { table_name: {}, index_name: {} };
      data.forEach((item, index) => {
        combineColumns.forEach((key) => {
          let colVal = item[key];
          let colMap = map[key];
          if (colVal) {
            let uid = colVal + '_' + index;
            colMap[uid] = {
              index,
              rowSpan: 1
            };
            // 和前面值相同
            if (index > 0 && data[index - 1][key] == colVal) {
              colMap[uid].rowSpan = 0;
              let pid = colVal + '_' + (index - 1);
              if (!colMap[pid].parent) {
                colMap[pid].rowSpan += 1;
                colMap[uid].parent = colMap[pid];
              } else {
                colMap[pid].parent.rowSpan += 1;
                colMap[uid].parent = colMap[pid].parent;
              }
            }
          }
        });
      });
      // console.log(map, 8989);
      return map;
      // this.$set(
      //   this.indexParams,
      //   'columns',
      //   this.config.indexColumns({ combineInfo: map })
      // );
    }
  }
};
</script>

<style lang="less">
.sqldetail-total-info-modal {
  pointer-events: none;
  .ant-modal-header {
    cursor: move;
  }
  .ant-modal-content {
    position: absolute;
    width: 100%;
    .custom-table {
      padding-bottom: 10px;
    }
    .table-name-wrapper {
      padding: 8px 0;
      &:first-child {
        padding-top: 0;
      }
      .table-name {
        color: #1890ff;
        font-size: 14px;
        font-weight: 700;
        margin: 8px 0 16px 0;
        span {
          padding: 4px 8px;
          border: 1px solid #1890ff;
          border-radius: 15px;
        }
      }
    }
  }
}
</style>
