<template>
  <!-- 上面基础信息区域 -->
  <a-card style="width: 100%" class="common-page-card" :bordered="false">
    <div class="review-detail-info">
      <div class="review-detail-main-info">
        <div class="left-block">
          <span
            class="left-avatar-box"
            :style="{ background: avatarBkg[topFormData.analy_status] }"
          >
            <custom-icon :type="avatarIcon[topFormData.analy_status]" />
          </span>
          <div class="left-info-side-box">
            <div class="info-box">
              <span class="id-tag">{{ id || '--' }}</span>
              <span class="project-name">{{ appName || '--' }}</span>
              <span class="review-point" v-if="isProjectReviewDetail"
                >Tag: {{ topFormData.review_point || '--' }}</span
              >
              <span
                class="project-group"
                v-if="
                  projectGroup &&
                  projectGroup.length > 0 &&
                  isProjectReviewDetail
                "
              >
                <span>项目组：</span>
                <span v-if="projectGroup.length == 1">{{
                  projectGroup[0]
                }}</span>
                <a-tooltip v-else>
                  <template slot="title">
                    <span>{{ projectGroup.toString() }}</span>
                  </template>
                  <span>{{ projectGroup[0] + '; ' + '...' }}</span>
                </a-tooltip>
              </span>
              <span class="no-wrap" v-if="isProjectReviewDetail">
                <span class="status">
                  <span class="review-name">项目审核</span>
                  <StatusTag
                    type="review"
                    :status="topFormData.analy_status"
                  ></StatusTag>
                </span>
                <span class="status">
                  <span class="review-name">DBA审核</span>
                  <StatusTag type="dba" :status="topFormData.dba_status" />
                </span>
              </span>
              <span class="data-source-content" v-if="dataSourceObj">
                <span :class="dataSourceObj.env == 'TEST' ? 'test' : 'prod'">{{
                  dataSourceObj.env == 'TEST' ? '测试' : '生产'
                }}</span>
                <DbImg
                  :value="dataSourceObj.db_type"
                  :schemaName="
                    dataSourceObj.name + '(' + dataSourceObj.db_url + ')'
                  "
                  mode="ellipsis"
                />
                <span class="schema">
                  <custom-icon type="database"></custom-icon>
                  {{
                    dataSourceObj.schema_name &&
                    dataSourceObj.schema_name.join(', ')
                  }}</span
                >
                <a-popover
                  overlayClassName="preaudit-base-info-popover"
                  v-if="dataSourceList.length > 1"
                >
                  <template slot="content">
                    <div
                      v-for="item in dataSourceList.slice(1)"
                      :key="item.name"
                      class="data-source-content"
                    >
                      <span :class="item.env == 'TEST' ? 'test' : 'prod'">{{
                        item.env == 'TEST' ? '测试' : '生产'
                      }}</span>
                      <DbImg
                        :value="item.db_type"
                        :schemaName="item.name + '(' + item.db_url + ')'"
                      />
                      <span class="schema">
                        <custom-icon type="database"></custom-icon>
                        {{
                          item.schema_name && item.schema_name.join(', ')
                        }}</span
                      >
                    </div>
                  </template>
                  <a-tag class="extra-data-source">{{
                    `+${dataSourceList.length - 1}`
                  }}</a-tag>
                </a-popover>
                <span class="ai-review">
                  <span>AI审核</span>
                  <!-- <a-progress
                    v-if="[0, 9, 3, 4, 5].includes(topFormData.status)"
                    :strokeWidth="8"
                    :percent="Number(topFormData.progress)"
                    size="small"
                  /> -->
                  <a-tooltip
                    v-if="
                      topFormData.status == '9' && topFormData.error_message
                    "
                  >
                    <template slot="title">
                      <span>{{ topFormData.error_message }}</span>
                    </template>
                    <StatusTag
                      type="review"
                      :status="topFormData.status"
                      fromPath="sqlreview"
                    >
                      <a-icon style="marginleft: 4px" type="question-circle" />
                    </StatusTag>
                  </a-tooltip>
                  <StatusTag
                    type="review"
                    :status="topFormData.status"
                    fromPath="sqlreview"
                    v-else
                  />
                </span>
              </span>
            </div>

            <div class="side-info-box">
              <span class="created-by">
                <a-tooltip>
                  <template slot="title">
                    <span>{{ topFormData.create_by }}</span>
                  </template>
                  <span style="color: #71717a">{{ topFormData.ch_name }}</span>
                </a-tooltip>
                <span style="color: #a1a1a1"
                  >于{{ topFormData.creat_at }} 发起审核</span
                >
              </span>
              <span
                style="color: #71717a"
                class="no-wrap"
                v-if="topFormData.ai_review_total_time"
                >审核耗时 {{ topFormData.ai_review_total_time + '分钟' }}</span
              >
            </div>
          </div>
        </div>

        <div class="right-block no-wrap">
          <div class="right-block-content">
            <div>
              <span>SQL语句</span>
              <span>{{ reviewInfo.all_count || '--' }}</span>
            </div>
            <a-divider type="vertical" />
            <div class="hover-operation-area">
              <div class="hover-switch">
                <span>AI审核通过</span>
                <span class="pass" v-if="reviewInfo.all_count"
                  >{{
                    (
                      (reviewInfo.ai_pass_count / reviewInfo.all_count) *
                      100
                    ).toFixed(2)
                  }}%</span
                >
                <span class="pass" v-else>{{ '--' }}</span>
              </div>
              <div class="hover-detail">
                <span>通过条数</span>
                <span>
                  <span class="pass">{{ reviewInfo.ai_pass_count }}</span>
                </span>
              </div>
            </div>
            <a-divider type="vertical" />
            <div class="hover-operation-area">
              <div class="hover-switch">
                <span>审核异常</span>
                <span class="error" v-if="reviewInfo.all_count"
                  >{{
                    (
                      (reviewInfo.error_count / reviewInfo.all_count) *
                      100
                    ).toFixed(2)
                  }}%</span
                >
                <span class="error" v-else>{{ '--' }}</span>
              </div>
              <div class="hover-detail">
                <span>异常条数</span>
                <span class="error">{{ reviewInfo.error_count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
import StatusTag from '@/components/Biz/Status/Tag';
export default {
  components: { StatusTag },
  props: {
    topFormData: {
      type: Object,
      default: () => {}
    },
    reviewInfo: {
      type: Object,
      default: () => {}
    },
    appName: String,
    projectGroup: {
      type: Array,
      default: () => []
    },
    dataSourceList: {
      type: Array,
      default: () => []
    },
    id: String | Number
  },
  data() {
    return {
      avatarIcon: {
        队列中: 'lu-icon-unknown',
        待审核: 'lu-icon-unknown',
        通过: 'lu-icon-pass1',
        未通过: 'lu-icon-nopass',
        拉取代码中: 'lu-icon-unknown',
        代码解析中: 'lu-icon-unknown',
        代码审核中: 'lu-icon-unknown',
        Review失败: 'lu-icon-nopass'
      },
      avatarBkg: {
        队列中: '#F8C99C',
        待审核: '#F8C99C',
        通过: '#9CCBA5',
        未通过: '#F38E9B',
        拉取代码中: '#F8C99C',
        代码解析中: '#F8C99C',
        代码审核中: '#F8C99C',
        Review失败: '#F38E9B'
      },
      statusColor: {
        '0': '#ff9f28',
        '1': '#14c55f',
        '-1': '#f73232',
        '9': '#f73232',
        '2': '#f73232',
        '3': '#ff9f28',
        '4': '#ff9f28',
        '5': '#ff9f28'
      },
      iconMap: {
        xml: 'lu-icon-xml',
        sql: 'lu-icon-sql',
        java: 'lu-icon-java'
      },
      labelColor: {
        0: '#F29339',
        1: '#3A974C',
        '-1': '#EF6173'
      }
    };
  },
  computed: {
    isProjectReviewDetail() {
      const name = this.$route.name;
      return name == 'project-review-detail';
    },
    dataSourceObj() {
      return this.dataSourceList.length > 0 ? this.dataSourceList[0] : null;
    }
  },
  mounted() {},
  created() {},

  methods: {}
};
</script>

<style lang="less" scoped>
.common-page-card {
  margin-bottom: 12px;
  border-radius: 8px;
  padding: 24px;
}
.review-detail-info {
  .review-detail-main-info {
    display: flex;
    justify-content: space-between;
    .left-block {
      width: 68%;
      display: flex;
      align-items: center;
      .left-avatar-box {
        margin-right: 12px;
        width: 68px;
        height: 68px;
        border-radius: 50%;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        > .anticon {
          font-size: 48px;
          color: #fff;
        }
      }
      .left-info-side-box {
        .info-box {
          > span {
            display: inline-block;
            margin-bottom: 12px;
            margin-right: 12px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #a1a1aa;
            font-weight: 400;
          }
          .id-tag {
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 2px 4px;
            font-size: 12px;
          }
          .project-name {
            color: #27272a;
            font-weight: 600;
            margin-right: 4px;
          }
          .review-point,
          .project-group {
            background-color: rgba(228, 228, 231, 0.5);
            border-radius: 12px;
            padding: 2px 12px;
            color: #a1a1aa;
            font-size: 14px;
          }
          .ant-tag {
            border-radius: 12px;
          }
          .status {
            margin-right: 30px;
          }
        }
        .no-wrap {
          .review-name {
            margin-right: 4px;
          }
        }
        /deep/.data-source-content {
          display: flex !important;
          align-items: center;
          flex-wrap: wrap;
          margin-bottom: 0 !important;
          .test {
            background: #f6ffed;
            border: 1px solid rgba(183, 235, 143, 1);
            color: #52c41a;
            font-size: 12px;
            padding: 0 6px;
            border-radius: 4px;
            white-space: nowrap;
            margin-bottom: 12px;
          }
          .prod {
            background: #fff7e6;
            border: 1px solid rgba(255, 213, 145, 1);
            color: #fa8c16;
            font-size: 12px;
            padding: 0 4px;
            border-radius: 4px;
            white-space: nowrap;
            margin-bottom: 12px;
            margin-right: 12px;
          }
          .schema {
            white-space: nowrap;
            margin-right: 8px;
            margin-bottom: 12px;
          }
          .database-image {
            margin: 0 12px;
            margin-bottom: 12px;
            .limit-label {
              width: 120px;
            }
          }
          .extra-data-source {
            margin-bottom: 12px;
          }
          .ai-review {
            margin-bottom: 12px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            white-space: nowrap;
            > span {
              margin-right: 4px;
            }
            .ant-progress {
              margin-left: 8px;
              margin-right: 12px;
              .ant-progress-inner {
                width: 64px;
                border-radius: 0;
                .ant-progress-bg {
                  border-radius: 0 !important;
                }
              }
            }
          }
        }
      }
      .side-info-box {
        > span {
          height: 22px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          line-height: 22px;
          font-weight: 400;
        }
        .created-by {
          margin-right: 12px;
        }
      }
    }
    .right-block {
      width: 32%;
      display: flex;
      justify-content: flex-end;
      margin-left: 32px;
      .right-block-content {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        > div {
          display: flex;
          flex-direction: column;
          text-align: center;
          height: 56px;
          & span {
            line-height: 22px;
            font-weight: 400;
            margin-bottom: 8px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
          }
          & span:first-child {
            color: #71717a;
          }
          & span:last-child {
            font-size: 24px;
            color: #27272a;
          }
        }
        .hover-operation-area {
          display: block;
          text-align: center;
          height: 56px;
          overflow: hidden;
          cursor: default;
          &:hover {
            > div {
              transform: translateY(-56px);
            }
          }
          > div {
            display: flex;
            flex-direction: column;
            text-align: center;
            height: 56px;
            transition: transform 0.2s;
            .error {
              color: #e71d36 !important;
              font-size: 24px;
              font-weight: 400;
            }
            .pass {
              font-size: 24px;
              font-weight: 400;
              color: #3a974c !important;
            }
          }
        }
        .ant-divider-vertical {
          margin: 0 30px;
          width: 1px;
          height: 32px;
        }
      }
    }
  }
}
</style>

<style lang="less">
.preaudit-base-info-popover {
  .ant-popover-content {
    max-width: 800px;
    max-height: 400px;
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 800px;
        max-height: 400px;
        .data-source-content {
          display: flex !important;
          align-items: center;
          padding-right: 8px;
          margin-bottom: 8px;
          &:last-child {
            margin-bottom: 0;
          }
          .test {
            background: #f6ffed;
            border: 1px solid rgba(183, 235, 143, 1);
            color: #52c41a;
            font-size: 12px;
            padding: 0 6px;
            border-radius: 4px;
            white-space: nowrap;
          }
          .prod {
            background: #fff7e6;
            border: 1px solid rgba(255, 213, 145, 1);
            color: #fa8c16;
            font-size: 12px;
            padding: 0 4px;
            border-radius: 4px;
            white-space: nowrap;
          }
          .schema {
            white-space: nowrap;
          }
          .database-image {
            margin: 0 12px;
            .limit-label {
              pre {
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }
}
</style>
