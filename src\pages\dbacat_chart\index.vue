<template>
  <div class="dbacat_chart">
    <!-- <div class="item_container">
      <span style="margin-right: 12px">
        {{ alarm_start_time }} - 
        {{ alarm_end_time }}
      </span>
      <a-radio-group
        v-model="time"
        size="default"
        @change="time_change"
        default-value="now"
        button-style="solid"
      >
        <a-radio-button
          :name="item.label"
          v-for="(item, index) in topOptions"
          :key="index"
          :value="item.value"
        >
          {{ item.label }}
        </a-radio-button>
      </a-radio-group>
    </div> -->

    <div class="item_container">
      <a-switch
        style="float: right; margin-top: 6px"
        @change="dotime_change"
        v-model="dotime"
        checked-children="开"
        un-checked-children="关"
        default-checked
      />
      <span style="margin-right: 12px; font-weight: 600; font-size: 16px">
        {{ alarm_end_time }}
      </span>
      <a-button-group>
        <a-button
          v-for="(item, index) in topOptions"
          :key="index"
          :value="item.value"
          @click="time_btn(item)"
        >
          {{ item.label }}</a-button
        >
        <a-button
          @click="now_time_btn({ label: 'now', value: 0 })"
          :type="is_now ? 'primary' : ''"
        >
          now
        </a-button>
      </a-button-group>
    </div>

    <div class="item_container">
      <a-radio-group
        v-model="type"
        size="default"
        @change="type_change"
        default-value="a"
        button-style="solid"
      >
        <a-radio-button
          v-for="(item, index) in centerOptions"
          :key="index"
          :value="item.value"
        >
          {{ item.label }}
        </a-radio-button>
      </a-radio-group>
      <br />

      <a-radio-group
        v-model="minute"
        style="width: 900px"
        size="default"
        @change="minute_change"
        default-value="a"
        button-style="solid"
      >
        <a-radio-button
          v-for="(item, index) in bottomOptions"
          :key="index"
          :value="item.value"
          style="width: 30px; text-align: center"
        >
          {{ item.label }}
        </a-radio-button>
      </a-radio-group>
    </div>

    <div class="item_container">
      <span>
        <span>数据库面板服务</span>
        <a-tag :color="list_data.server_status != '异常' ? 'green' : 'red'">
          {{ list_data.server_status }}
        </a-tag>
      </span>
      <span style="margin-left: 24px">
        <label>实例名称：</label>
        <el-select
          multiple
          collapse-tags
          filterable
          remote
          @change="example_change"
          :remote-method="method_example"
          style="width: 300px"
          size="small"
          v-model="instance_id_list"
          placeholder="请选择"
        >
          <el-option
            v-for="item in instance_list_options"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
            <a-tag :class="item.env.toLowerCase() == 'test' ? 'test' : 'pro'">{{
              item.env.toLowerCase() == 'test' ? '测试' : '生产'
            }}</a-tag>
            <custom-icon :type="iconType[item.db_type]" />
            <span>{{ item.label }}</span>
            <span style="color: #a1a1aa">{{ item.db_type }}</span>
          </el-option>
        </el-select>
      </span>
    </div>

    <div style="min-height: 300px" v-loading="loading" class="item_container">
      <!-- <span class="item_">
                <p class="item_title">12:27:30 (2)</p>
                <el-table :header-cell-style="{
                    background: '#F2F3F7',
                    color: '#000000',
                }" border :data="tableData" style="width: 100%" :row-class-name="getRowClassName">
                    <el-table-column prop="date" label="实例名" width="">
                        <template slot-scope="scope">
                            <el-tooltip class="item" effect="dark" content="Top Left 提示文字" placement="top-start">
                                <span style="cursor: pointer; color: royalblue;" @click="name_click(scope.row)">
                                    {{ scope.row.date }}
                                </span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="告警" width="">
                    </el-table-column>
                    <el-table-column prop="address" label="健康值">
                    </el-table-column>
                </el-table>
            </span> -->
      <span
        v-for="(item, index) in list_data.alarm_rule_list"
        :key="index"
        class="item_"
      >
        <p class="item_title">{{ item.group_end_time }}</p>
        <el-table
          :header-cell-style="{
            background: '#F2F3F7',
            color: '#606266'
          }"
          border
          :data="item.alarm_data"
          style="width: 100%"
          :row-class-name="getRowClassName"
        >
          <el-table-column prop="db_type" label="实例名" width="120px">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" content="" placement="top-start">
                <div slot="content">
                  <p>实例名 : {{ scope.row.instance_name }}</p>
                  <p>cpu : {{ scope.row.cpu }}</p>
                  <p>健康度 : {{ scope.row.health_level }}</p>
                  <p>url : {{ scope.row.db_url }}</p>
                  <p>内存 : {{ scope.row.memory }} （GB）</p>
                  <p>最大连接数 : {{ scope.row.maximum_connection }}</p>
                </div>
                <span
                  style="cursor: pointer; color: royalblue"
                  @click="name_click(scope.row)"
                >
                  <span
                    style="
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      max-width: 300px;
                    "
                  >
                    {{ scope.row.instance_name }}
                  </span>
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="告警" width="70px">
            <!-- <template slot-scope="scope">
              <span>
                {{ scope.row.alarm_total }}
              </span>
            </template> -->
            <template slot-scope="scope">
              <el-tooltip
                v-if="scope.row.alarm_list.length > 0"
                class="item"
                effect="dark"
                content=""
                placement="top"
              >
                <div slot="content">
                  <p v-for="tooltip in scope.row.alarm_list">
                    {{ tooltip.rule_name }}
                  </p>
                </div>
                <span
                  style="cursor: pointer; color: royalblue"
                  @click="name_click(scope.row)"
                >
                  {{ scope.row.alarm_total }}
                </span>
              </el-tooltip>

              <span
                v-else
                style="cursor: pointer; color: royalblue"
                @click="name_click(scope.row)"
              >
                {{ scope.row.alarm_total }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="健康值" width="70px">
            <template slot-scope="scope">
              <span>
                {{ scope.row.health_level }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </span>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import DataBaseChoose from '@/components/Biz/DataBaseChoose';
import * as API from '@/api/dbacat';

export default {
  comments: { DataBaseChoose },
  data() {
    return {
      timer: null,
      is_now: true,
      time_label: 'now',
      loading: false,
      instance_id_list: [],
      list_data: {},
      instance_list_options: [],
      time: -570000,
      type: 'ALL',
      minute: '',
      alarm_start_time: '',
      alarm_end_time: '',
      tableData: [
        {
          date: 'phyxdecgc',
          name: '3',
          address: '2'
        },
        {
          date: 'phyxdecgc',
          name: '3',
          address: '2'
        },
        {
          date: 'phyxdecgc',
          name: '3',
          address: '2'
        },
        {
          date: 'phyxdecg1c',
          name: '3',
          address: '2'
        }
      ],

      topOptions: [
        { label: '-7d', value: -604800000 },
        { label: '-1d', value: -86400000 },
        { label: '-1h', value: -3600000 },
        { label: '+1h', value: 3600000 },
        { label: '+1d', value: 86400000 },
        { label: '+7d', value: 604800000 }
      ],
      topVal: 'now',
      centerOptions: [
        { label: 'ALL', value: 'ALL' },
        { label: 'Postgresql', value: 'Postgresql' },
        { label: 'Mysql', value: 'Mysql' },
        { label: 'Oracle', value: 'Oracle' }
      ],
      centerVal: 'ALL',
      bottomOptions: Array(60)
        .fill()
        .map((item, index) => {
          return { label: index + 1, value: index + 1 };
        }),
      iconType: {
        ORACLE: 'lu-icon-oracle',
        MYSQL: 'lu-icon-mysql',
        PGSQL: 'lu-icon-pgsql',
        HBASE: 'lu-icon-hbase',
        CLICKHOUSE: 'lu-icon-clickhouse',
        KAFKA: 'lu-icon-kafka',
        ELASTICSEARCH: 'lu-icon-elasticsearch',
        POSTGRES: 'lu-icon-pgsql',
        POSTGRE: 'lu-icon-pgsql',
        TIDB: 'lu-icon-tidb',
        STARROCKS: 'lu-icon-starrocks',
        UBISQL: 'lu-icon-ubisql',
        RASESQL: 'lu-icon-rosesql',
        OB_MYSQL: 'lu-icon-oceanbase',
        DB2: 'lu-icon-db2',
        OCEANBASE: 'lu-icon-oceanbase',
        SQLSERVER: 'lu-icon-sql-server',
        TD_MYSQL: 'lu-icon-tdsql-1',
        FILESYSTEM: 'folder-open',
        TDSQL: 'lu-icon-tdsql-1',
        OB_ORA: 'lu-icon-oceanbase',
        OB_ORACLE: 'lu-icon-oceanbase',
        // IMPALA: 'lu-icon-impala',
        GBASE: 'lu-icon-gbase',
        GAUSSDB: 'lu-icon-gaussdb',
        OPENGAUSS: 'lu-icon-opengauss',
        HUDI: 'lu-icon-hudi',
        KINGBASE: 'lu-icon-kingbase',
        HIVE: 'lu-icon-hive',
        IMPALA: 'lu-icon-impala1',
        DM: 'lu-icon-dameng',
        ROCKETMQ: 'lu-icon-apacherocketmq',
        PRESTO: 'lu-icon-presto',
        RDS_MYSQL: 'lu-icon-rds',
        GOLDENDB: 'lu-icon-goldendb',
        MOGDB: 'lu-icon-mogdb',
        DORIS: 'lu-icon-Doris',
        TD_PGSQL: 'lu-icon-tdsql-1'
      },
      dotime: true
    };
  },
  mounted() {
    this.alarm_end_time = moment().format('YYYY-MM-DD HH:mm:ss');
    this.minute = parseInt(this.alarm_end_time.slice(14, 16));
    console.log(this.minute, 'moment +++++++++++++++');
    this.method_example();
    this.alarm_record_list();
    this.interval_time();
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    dotime_change() {
      // this.dotime = !this.dotime
      console.log(this.dotime);
      if (this.dotime) {
        this.is_now = true;
        this.interval_time();
      } else {
        clearInterval(this.timer);
      }
    },
    interval_time() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        this.alarm_end_time = moment().format('YYYY-MM-DD HH:mm:ss');
        this.minute = parseInt(this.alarm_end_time.slice(14, 16));
        this.alarm_record_list();
      }, 30000);
    },
    time_btn(e) {
      this.dotime = false;
      clearInterval(this.timer);
      this.alarm_end_time = moment(
        moment(this.alarm_end_time).valueOf() + e.value
      ).format('YYYY-MM-DD HH:mm:ss');
      let end = moment(this.alarm_end_time).valueOf();
      let now = moment().valueOf() + 6000;
      // let a = moment(end).format('YYYY-MM-DD HH:mm:ss');
      // let b = moment(now).format('YYYY-MM-DD HH:mm:ss');
      // console.log(end, now);
      // console.log(end < now);
      // console.log(a, b);
      if (end < now) {
        this.is_now = false;
        this.time_label = e.label;
        this.alarm_record_list();
      } else {
        this.$message.info('时间超出。');
      }
    },
    now_time_btn(e) {
      console.log(e.label);
      this.is_now = true;
      this.dotime = true;
      this.alarm_end_time = moment().format('YYYY-MM-DD HH:mm:ss');
      this.minute = parseInt(this.alarm_end_time.slice(14, 16));
      this.time_label = e.label;
      this.alarm_record_list();
      this.interval_time();
    },
    example_change(e) {
      this.alarm_record_list();
    },
    alarm_record_list() {
      this.loading = true;
      // 是否选择了now

      let params = {
        db_type: this.type, // 数据库类型
        // alarm_start_time: this.alarm_start_time,
        alarm_end_time: this.alarm_end_time,
        instance_id_list: this.instance_id_list, // id list
        is_now: this.is_now, //是否选择当前时间now按钮
        query_time: this.time_label,
        query_minute: this.minute //数据库类型下面的分钟数字,
        // "alarm_start_time": "2025-04-10 09:39:35",
        // "alarm_end_time": "2025-04-10 09:49:50",

        // "is_now": false,
        // "query_time": "",
        // "query_minute": 0
      };
      if (params.db_type == 'ALL') {
        params.db_type = '';
      }

      if (params.query_time == 'now') {
        params.query_time = '';
      }

      API.alarm_record_list(params)
        .then((res) => {
          console.log(res);
          this.list_data = res.data.data;
          this.alarm_start_time = res.data.data.alarm_start_time;
        })
        .catch((e) => {})
        .finally(() => {
          this.loading = false;
        });
    },
    method_example(e) {
      let params = {
        instance_name: e || ''
      };
      API.get_instance_list(params)
        .then((res) => {
          this.instance_list_options = res.data.data;
        })
        .catch((e) => {})
        .finally(() => {});
    },
    time_change(e) {
      this.minute = '';
      this.gettime(e.target.value);
      this.alarm_record_list();
    },
    gettime(e) {
      if (e) {
        if (e > 0) {
          this.alarm_end_time = moment(moment().valueOf() + e).format(
            'YYYY-MM-DD HH:mm:ss'
          );
          this.alarm_start_time = moment().format('YYYY-MM-DD HH:mm:ss');
        } else {
          this.alarm_start_time = moment(moment().valueOf() + e).format(
            'YYYY-MM-DD HH:mm:ss'
          );
          this.alarm_end_time = moment().format('YYYY-MM-DD HH:mm:ss');
        }
      } else {
        this.alarm_start_time = moment(moment().valueOf() - 60 * 10000).format(
          'YYYY-MM-DD HH:mm:ss'
        );
        this.alarm_end_time = moment().format('YYYY-MM-DD HH:mm:ss');
      }
    },
    getRowClassName({ row, rowIndex }) {
      if (row.alarm_total > 0) {
        return 'row_table';
      }
      return '';
    },
    type_change(e) {
      this.alarm_record_list();
    },
    minute_change(e) {
      const currentTimestamp = moment(this.alarm_end_time).unix();
      const timestamp = Math.floor(currentTimestamp / 3600) * 3600;
      this.alarm_end_time = moment(
        timestamp * 1000 + e.target.value * 60 * 1000
      ).format('YYYY-MM-DD HH:mm:ss');
      let end = moment(this.alarm_end_time).valueOf();
      let now = moment().valueOf() + 6000;
      if (end < now) {
        this.alarm_record_list();
      } else {
        this.$message.info('时间超出。');
      }
    },
    change(e) {},
    name_click(e) {
      console.log(e);
      // e.alarm_start_time = e.group_start_time;
      // e.alarm_end_time = e.alarm_end_time;
      this.$router.push({
        name: 'dbacat-chart-detail',
        params: e
      });
    }
  }
};
</script>


<style lang="less" scoped>
.dbacat_chart {
  padding: 16px;
  background: #fff;

  .item_ {
    width: 264px;
    display: inline-block;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin: 0 2px 16px 0;
  }

  .item_title {
    text-align: center;
    margin: 0;
    padding: 6px 0;
    background: #f2f3f7;
    color: #606266;
    border-bottom: 1px solid #cecece;
  }
}

.item_container {
  margin-bottom: 24px;

  .ant-radio-button-wrapper {
    padding: 0 6px;
  }
}
</style>

<style>
.item_ .el-table .el-table__cell {
  padding: 4px 0 !important;
}

.row_table {
  color: #fff;
  background: #ff0000 !important;
  /* F4A460 */
}
.row_table .item {
  color: #fff !important;
}
.test {
  color: #fff;
  background: rgb(76, 187, 58);
}
.pro {
  color: #fff;
  background: rgb(250, 140, 22);
}
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background: none !important;
}
</style>