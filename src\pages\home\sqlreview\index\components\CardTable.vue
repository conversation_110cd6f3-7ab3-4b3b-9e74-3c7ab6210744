<template>
  <div class="card-table">
    <div class="card-table-main-info">
      <div class="left-block">
        <div class="info-box">
          <span class="project-name">
            <!-- <span v-if="cardData.project_name.length <= 14">{{ cardData.project_name }}</span> -->
            <LimitLabel
              :label="cardData.project_name || ''"
              mode="ellipsis"
              :noWrap="true"
            ></LimitLabel>
          </span>
          <div class="status-box">
            <span class="review-type">
              <span>Review方式</span>
              <a-tag
                color="rgba(91,147,255,0.15)"
                style="color: #214ac0; border-radius: 12px"
                >{{ cardData.mode == 0 ? '全量' : '增量' }}</a-tag
              >
            </span>
            <span>
              <a-tooltip>
                <template slot="title" v-if="cardData.operator_dba">
                  <span>{{ cardData.operator_dba }}</span>
                </template>
                <span>DBA审核({{ cardData.ch_dba || '--' }})</span>
              </a-tooltip>
              <!-- <StatusTag type="dba" :status="cardData.dba_status" /> -->
              <a-tooltip
                v-if="
                  cardData.dba_status == '未通过' && cardData.comment_content
                "
              >
                <template slot="title">
                  <span>驳回原因：{{ cardData.comment_content }}</span>
                </template>
                <StatusTag type="dba" :status="cardData.dba_status">
                  <a-icon style="marginleft: 4px" type="question-circle" />
                </StatusTag>
              </a-tooltip>
              <StatusTag type="dba" :status="cardData.dba_status" v-else />
            </span>
            <span class="ai-review">
              <span>AI审核</span>
              <a-tooltip
                v-if="cardData.status == '9' && cardData.error_message"
              >
                <template slot="title">
                  <span>{{ cardData.error_message }}</span>
                </template>
                <StatusTag type="review" :status="cardData.status" fromPath='sqlreview'>
                  <a-icon style="marginleft: 4px" type="question-circle" />
                </StatusTag>
              </a-tooltip>
              <StatusTag type="review" :status="cardData.status" fromPath='sqlreview' v-else />
            </span>
          </div>
        </div>

        <div class="tag-box">
          <span>ID：{{ cardData.id }}</span>
          <span>Tag：{{ cardData.review_point }}</span>
          <span
            class="project-group"
            v-if="cardData.project_group && cardData.project_group.length > 0"
          >
            <span>项目组：</span>
            <span v-if="cardData.project_group.length == 1">{{
              cardData.project_group[0]
            }}</span>
            <a-tooltip v-else>
              <template slot="title">
                <span>{{ cardData.project_group.toString() }}</span>
              </template>
              <span>{{ cardData.project_group[0] + '; ' + '...' }}</span>
            </a-tooltip>
          </span>
          <span
            v-if="
              cardData.passing_rate && !['--'].includes(cardData.passing_rate)
            "
            >AI通过率： {{ cardData.passing_rate }}</span
          >
          <span v-if="cardData.history_baseline == 1">历史标准基线</span>
        </div>

        <div class="side-info-box">
          <span class="avatar-part">
            <a-avatar icon="user" />
          </span>
          <a-tooltip v-if="cardData.ch_creater">
            <template slot="title">
              <span>{{ cardData.created_by }}</span>
            </template>
            <span class="created-by">{{ cardData.ch_creater || '--' }}</span>
          </a-tooltip>
          <span class="created-by" v-else>{{ cardData.created_by }}</span>
          <span>于{{ cardData.created_at + ' ' }}</span>
          <span class="event">发起审核</span>
        </div>
      </div>
      <div class="right-block">
        <!-- <div class="right-block-content"> -->
        <div class="right-block-rules">
          <div>
            <span>触发规则</span>
            <span class="loading" v-if="[0, 3, 4, 5].includes(cardData.status)">
              <custom-icon type="lu-icon-loading1"></custom-icon>
            </span>
            <span class="rules" v-else>{{ cardData.rule_count }}</span>
          </div>
        </div>
        <a-divider type="vertical" />
        <div class="right-block-sql-text">
          <div>
            <span>SQL语句</span>
            <span class="loading" v-if="[0, 3, 4, 5].includes(cardData.status)">
              <custom-icon type="lu-icon-loading1"></custom-icon>
            </span>
            <span v-else>{{ cardData.sql_count }}</span>
          </div>
        </div>
        <a-divider class="right-block-divider" type="vertical" />
        <div class="right-block-botton">
          <div class="btns-wrapper-nomal">
            <a
              v-if="[1, 2].includes(cardData.review_status)"
              :disabled="cardData.review_status == 1"
              actionBtn
              @click="authSubmit(cardData.id)"
            >
              <a-icon type="file-done" />提交评审</a
            >
            <!-- <a
              v-if="
                cardData.status === 1 ||
                cardData.status === 9 ||
                cardData.sql_count === 0 ||
                cardData.sql_count === null ||
                [1, 2].includes(cardData.type)
              "
              actionBtn
              disabled
            >
              <a-icon type="file-done" />提交评审
            </a> -->
            <a
              v-if="
                cardData.dba_status === '评审中' ||
                cardData.dba_status === '待评审'
              "
              actionBtn
              @click="urge"
              >催办</a
            >
            <!-- <a
              v-else-if="
                cardData.dba_status === '已通过' ||
                cardData.dba_status === '未通过'
              "
              actionBtn
              disabled
              >催办</a
            > -->
            <!-- <a
              v-if="
                ![1, 2].includes(cardData.type) &&
                ['未提交', '未通过'].includes(cardData.dba_status)
              "
              @click="authSubmit(cardData.id)"
              actionBtn
            >
              <a-icon type="file-done" />提交评审</a
            > -->
            <a @click="showReport" actionBtn> <a-icon type="profile" />报表 </a>
            <a-popconfirm
              title="重新发起审核任务?"
              @confirm="() => reReview(cardData)"
            >
              <a
                v-if="
                  ![1, 2].includes(cardData.type) &&
                  [-1, 9].includes(cardData.status) &&
                  ['未提交', '未通过'].includes(cardData.dba_status)
                "
                actionBtn
              >
                <custom-icon type="lu-icon-re-review" /> 重新review</a
              >
            </a-popconfirm>
            <a-popconfirm
              title="确定移除该数据?"
              @confirm="() => remove(cardData)"
            >
              <a
                actionBtn
                v-if="canDo && ![0, 3, 4, 5].includes(cardData.status)"
              >
                <custom-icon type="delete" style="margin-right: 4px" />删除
              </a>
            </a-popconfirm>

            <a
              v-if="[0, 3, 4, 5].includes(cardData.status)"
              @click="terminate(cardData)"
              actionBtn
            >
              <a-icon type="pause-circle" style="margin-right: 4px" />终止
            </a>
            <!-- <a
              @click="showHistoryBaselineModel(cardData)"
              v-if="canDo && cardData.mode == 0 && cardData.history_baseline == 1"
              style="margin-top: 8px;"
              actionBtn
            >
              <custom-icon type="lu-icon-config" style="transform: rotate(90deg);" />历史标准基线
            </a>-->
          </div>
          <div class="btns-wrapper-highlight">
            <a @click="toDetail" class="highlight" actionBtn>
              详情
              <a-icon type="right" />
            </a>
          </div>
        </div>
      </div>
      <HistoryBaseline ref="historyBaseline" @onSave="onSave"></HistoryBaseline>
    </div>
  </div>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';
import StatusTag from '@/components/Biz/Status/Tag';
import HistoryBaseline from './HistoryBaseline';
import { editHistoryBaseline } from '@/api/home';
export default {
  components: { LimitLabel, StatusTag, HistoryBaseline },
  props: {
    cardData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  computed: {
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  created() {},
  methods: {
    // 历史标准基线
    showHistoryBaselineModel(record) {
      this.$refs.historyBaseline.show(record);
    },
    // 详情
    toDetail() {
      this.$emit('toDetail');
    },
    // 报表
    showReport() {
      this.$emit('showReport');
    },
    // 催办
    urge() {
      this.$emit('urge');
    },
    // 提交评审
    authSubmit() {
      this.$emit('authSubmit');
    },
    // 删除
    remove() {
      this.$emit('remove');
    },
    // 终止
    terminate() {
      this.$emit('terminate');
    },
    reReview() {
      this.$emit('reReview');
    },
    onSave(id) {
      const params = {
        id: id,
        history_baseline: 1
      };
      this.$showLoading();
      editHistoryBaseline(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            this.$emit('refresh');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.card-table {
  .card-table-main-info {
    display: flex;
    justify-content: space-between;
    .left-block {
      display: flex;
      flex-direction: column;
      margin-right: 32px;
      .info-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .status-box > span,
        > span {
          margin-right: 28px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          font-weight: 400;
        }
        .status-box {
          .review-type {
            white-space: nowrap;
            .ant-tag {
              font-size: 14px;
              margin-right: 0;
              padding: 0 8px;
            }
          }
        }
        .project-name {
          position: relative;
          white-space: nowrap;
          top: -2px;
          > span {
            font-family: PingFangSC-Semibold;
            font-size: 20px;
            color: #27272a;
            font-weight: 600;
            max-width: 150px;
            display: block;
          }
          /deep/.limit-label {
            pre {
              font-family: PingFangSC-Semibold;
              font-size: 20px !important;
              color: #27272a;
              font-weight: 600 !important;
            }
          }
        }
      }
      .tag-box {
        margin: 12px 0 16px 0;
        display: flex;
        flex-wrap: wrap;
        > span {
          margin: 2px 16px 2px 0;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #27272a;
          letter-spacing: 0;
          font-weight: 400;
          text-align: center;
          border: 1px solid #e4e4e7;
          border-radius: 4px;
          padding: 4px 7px;
          white-space: nowrap;
        }
      }
      .side-info-box {
        display: flex;
        align-items: center;
        > span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          letter-spacing: 0;
          font-weight: 400;
          &.avatar-part {
            width: 20px;
            .ant-avatar {
              width: 20px;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              margin-bottom: 2px;
              color: #fff;
              background: #4ec3f5;
            }
          }
        }
        .created-by {
          margin: 0 4px 0 8px;
          color: rgb(113, 113, 122);
        }
        .event {
          color: rgb(113, 113, 122);
        }
      }
    }
    .right-block {
      display: flex;
      align-items: center;
      .ant-divider-vertical {
        width: 1px;
        height: 32px;
        margin: 0 12px;
      }
      .right-block-rules,
      .right-block-sql-text {
        padding: 0 18px;
        display: flex;
        align-items: center;
        > div {
          display: flex;
          flex-direction: column;
          text-align: center;
          & span:first-child {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #71717a;
            letter-spacing: 0;
            font-weight: 400;
            margin-bottom: 8px;
            white-space: nowrap;
          }
          & span:last-child {
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: #27272a;
            letter-spacing: 0;
            font-weight: 400;
          }
          @keyframes rotate {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
          .loading {
            animation: rotate 2s linear infinite;
          }
        }
      }
      .right-block-rules {
        .rules {
          color: #e71d36 !important;
        }
      }
      .right-block-botton {
        padding-left: 18px;
        width: 240px;
        display: flex;
        align-items: flex-start;
        justify-content: space-between !important;
        // flex-wrap: wrap;
        .btns-wrapper-nomal {
          display: flex;
          flex-wrap: wrap;
          a {
            margin: 6px 12px 8px 0;
          }
        }
        a {
          height: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #4db5f2;
          letter-spacing: 0;
          line-height: 16px;
          font-weight: 400;
          display: flex;
          align-items: center;
          // margin-right: 12px;
          > .anticon {
            margin-right: 4px;
          }
          &.highlight {
            padding: 14px 6px 14px 8px;
            background: #4db5f2;
            border-radius: 4px;
            color: #fff;
            margin-right: 0;
            > .anticon {
              font-size: 12px;
              margin-right: 0;
              margin-left: 4px;
            }
          }
          &:hover {
            color: @primary-color;
            &.highlight {
              color: #fff;
              background: @primary-color;
            }
          }
        }
        a[disabled] {
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .card-table {
    .card-table-main-info {
      .right-block {
        display: flex;
        justify-content: space-between;
        .right-block-divider {
          display: none;
        }
        .right-block-botton {
          width: 240px;
          display: none;
        }
      }
      .left-block {
        .info-box {
          .project-name {
            width: 100%;
            > span {
              max-width: 300px;
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1560px) {
  .card-table {
    .card-table-main-info {
      .left-block {
        .info-box {
          .project-name {
            > span {
              max-width: 270px;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less" scoped></style>
