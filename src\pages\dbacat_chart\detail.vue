<template>
  <div class="dbacat_chart_detail">
    <div class="item_container">
      <!-- <a-switch
        style="float: right; margin-top: 6px"
        @change="dotime_change"
        v-model="dotime"
        checked-children="开"
        un-checked-children="关"
        default-checked
      /> -->
      <span>
        <label>实例名称：</label>
        <el-select
          filterable
          remote
          :remote-method="get_third_data_source_list"
          style="width: 300px"
          size="small"
          v-model="search_form.third_data_source_id"
          placeholder="请选择"
        >
          <el-option
            @click.native="db_type_btn(item)"
            v-for="item in instance_list_options"
            :key="item.id"
            :label="item.name"
            :value="item.third_data_source_id"
          >
            <a-tag :class="item.env.toLowerCase() == 'test' ? 'test' : 'pro'">{{
              item.env.toLowerCase() == 'test' ? '测试' : '生产'
            }}</a-tag>
            <custom-icon :type="iconType[item.db_type]" />
            <span>{{ item.name }}</span>
            <span style="color: #a1a1aa">{{ item.db_type }}</span>
          </el-option>
        </el-select>
      </span>
      <span style="margin-left: 24px">
        <label>执行时间：</label>
        <a-range-picker
          style="width: 380px"
          :showTime="{
            hideDisabledOptions: true,
            defaultValue: [
              moment('00:00:00', 'HH:mm:ss'),
              moment('23:59:59', 'HH:mm:ss')
            ]
          }"
          v-model:value="search_form.time"
          :format="['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']"
          @change="handleDateChange"
        />
      </span>

      <span style="margin-left: 24px">
        <!-- <a-button type="">
          重置
        </a-button> -->
        <a-button @click="search_btn" type="primary"> 搜索 </a-button>
      </span>
    </div>

    <div class="item_container_">
      <el-descriptions
        :labelStyle="{
          width: '74px'
        }"
        title=""
      >
        <el-descriptions-item label="cpu">{{
          cpu_data.cpu
        }}</el-descriptions-item>
        <el-descriptions-item label="内存"
          >{{ cpu_data.memory }}（GB）</el-descriptions-item
        >
        <el-descriptions-item label="磁盘容量"
          >{{ cpu_data.total_disk }}（GB）</el-descriptions-item
        >
        <el-descriptions-item label="最大连接数">{{
          cpu_data.maximum_connection
        }}</el-descriptions-item>
        <el-descriptions-item label="健康度">{{
          cpu_data.health_level
        }}</el-descriptions-item>
        <el-descriptions-item label="资源配置">{{
          cpu_data.resource_config_display
        }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-if="alarm_list.length > 0" class="item_container_">
      <p style="font-weight: 600">严重告警明细</p>
      <el-table
        ref="table_"
        :header-cell-style="{
          background: '#fafafa',
          height: '40px',
          padding: '5px 0',
          color: '#606266'
        }"
        :data="alarm_list"
        style="width: 100%"
        :cell-style="{ padding: '5px 0', width: '200px', height: '40px' }"
      >
        <el-table-column prop="alarm_start_time" label="告警时间" width="">
        </el-table-column>

        <el-table-column prop="target" label="指标" width=""> </el-table-column>

        <el-table-column prop="current_threshold" label="当前值" width="">
        </el-table-column>

        <el-table-column prop="rule_name" label="告警规则" width="">
        </el-table-column>
      </el-table>
    </div>
    <!-- <span>时间：{{ now_time }} </span> -->
    <a-radio-group
      style="margin: 16px 0"
      size="default"
      @change="radio_change"
      default-value="a"
      button-style="solid"
      v-model="time_group"
    >
      <a-radio-button
        v-for="(item, index) in topOptions"
        :key="index"
        :value="item.value"
      >
        {{ item.label }}
      </a-radio-button>
    </a-radio-group>
    <!-- <div class="chart_box">
      <div v-for="(item, index) in chart_list" :key="index" class="chart_item">
        <p class="title">{{ item.name }}</p>
        <div class="chart_item_" :id="item.name"></div>
      </div>
    </div> -->

    <div v-show="charts_show" class="chart_box">
      <div
        v-loading="chart_loading"
        style="height: 350px; width: 400px"
        v-for="(item, index) in chart_params_list"
        :key="item.id"
        class="chart_item"
        :id="item.metric_type + item.id"
      >
        <p class="title">{{ item.metric_name }}</p>
        <div
          style="height: 300px; width: 400px"
          class="chart_item_"
          :id="item.metric_type + item.id"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import moment from 'moment';
import DataBaseChoose from '@/components/Biz/DataBaseChoose';
import * as echarts from 'echarts';
import * as API from '@/api/dbacat';
export default {
  name: 'dbacat-chart-detail',
  comments: { DataBaseChoose },
  data() {
    return {
      time_group: '',
      step: '30',
      chart_loading: false,
      chart_list: [],

      search_form: {
        time: ['2025-04-09 16:20:16', '2025-04-09 16:20:16'],
        third_data_source_id: ''
      },
      topOptions: [
        { label: '10分钟', value: 600000 },
        { label: '15分钟', value: 900000 },
        { label: '30分钟', value: 1800000 },
        { label: '1小时', value: 3600000 },
        { label: '6小时', value: 21600000 },
        { label: '12小时', value: 43200000 },
        { label: '24小时', value: 86400000 }
      ],

      instance_list_options: [],
      vm_query_url: '',
      query_metric: '',
      cpu_data: {}, //cpu数据
      alarm_list: [],
      now_time: '',
      chart_params_list: [],
      iconType: {
        ORACLE: 'lu-icon-oracle',
        MYSQL: 'lu-icon-mysql',
        PGSQL: 'lu-icon-pgsql',
        HBASE: 'lu-icon-hbase',
        CLICKHOUSE: 'lu-icon-clickhouse',
        KAFKA: 'lu-icon-kafka',
        ELASTICSEARCH: 'lu-icon-elasticsearch',
        POSTGRES: 'lu-icon-pgsql',
        POSTGRE: 'lu-icon-pgsql',
        TIDB: 'lu-icon-tidb',
        STARROCKS: 'lu-icon-starrocks',
        UBISQL: 'lu-icon-ubisql',
        RASESQL: 'lu-icon-rosesql',
        OB_MYSQL: 'lu-icon-oceanbase',
        DB2: 'lu-icon-db2',
        OCEANBASE: 'lu-icon-oceanbase',
        SQLSERVER: 'lu-icon-sql-server',
        TD_MYSQL: 'lu-icon-tdsql-1',
        FILESYSTEM: 'folder-open',
        TDSQL: 'lu-icon-tdsql-1',
        OB_ORA: 'lu-icon-oceanbase',
        OB_ORACLE: 'lu-icon-oceanbase',
        // IMPALA: 'lu-icon-impala',
        GBASE: 'lu-icon-gbase',
        GAUSSDB: 'lu-icon-gaussdb',
        OPENGAUSS: 'lu-icon-opengauss',
        HUDI: 'lu-icon-hudi',
        KINGBASE: 'lu-icon-kingbase',
        HIVE: 'lu-icon-hive',
        IMPALA: 'lu-icon-impala1',
        DM: 'lu-icon-dameng',
        ROCKETMQ: 'lu-icon-apacherocketmq',
        PRESTO: 'lu-icon-presto',
        RDS_MYSQL: 'lu-icon-rds',
        GOLDENDB: 'lu-icon-goldendb',
        MOGDB: 'lu-icon-mogdb',
        DORIS: 'lu-icon-Doris',
        TD_PGSQL: 'lu-icon-tdsql-1'
      },
      timer: null,
      dotime: true,
      charts_show: false
    };
  },

  created() {
    let base_data = { ...this.$route.params };
    this.now_time = moment().format('YYYY-MM-DD HH:mm:ss');
    if (Object.keys(this.$route.params).length === 0) {
      base_data = JSON.parse(localStorage.getItem('base_data'));
    }
    let now_time_up = moment(
      moment(base_data.group_end_time).unix() * 1000 - 600000
    ).format('YYYY-MM-DD HH:mm:ss');
    this.search_form.time = [now_time_up, base_data.group_end_time];
    this.search_form.third_data_source_id = base_data.third_data_source_id;
    this.search_form.db_type = base_data.db_type;
    localStorage.setItem('base_data', JSON.stringify(base_data));
  },
  async mounted() {
    this.instance_details(); //获取cpu
    this.get_third_data_source_list(); // 获取实例名称
    await this.get_vm_url(); // 获取chart的请求地址
    await this.get_query_metric(); // 获取chart的请求参数
    // this.get_chart(); // 获取chart
    this.get_params_chart();
    // this.interval_time();
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    handleDateChange() {},
    dotime_change() {
      // this.dotime = !this.dotime
      if (this.dotime) {
        this.interval_time();
      } else {
        clearInterval(this.timer);
      }
    },
    interval_time() {
      if (this.timer) {
        clearInterval(this.timer);
      }

      let tt = this.search_form.time;
      let nt = [];
      console.log(this.search_form.time[0]);

      let t0 = '';
      let t1 = '';

      if (typeof this.search_form.time[0] === 'object') {
        t0 = moment(this.search_form.time[0]._i).unix();
        t1 = moment(this.search_form.time[1]._i).unix();
      } else {
        t0 = moment(this.search_form.time[0]).unix();
        t1 = moment(this.search_form.time[1]).unix();
      }

      console.log(t0, t1);

      this.timer = setInterval(() => {
        t0 += 3000;
        t1 += 3000;
        console.log(moment(t0).format('YYYY-MM-DD HH:mm:ss'), '++++');
        this.get_params_chart(t0, t1);
      }, 3000);
    },
    get_third_data_source_list(e) {
      let params = {
        instance_name: e || ''
      };
      API.get_third_data_source_list(params)
        .then((res) => {
          this.instance_list_options = res.data.data;
        })
        .catch((e) => {})
        .finally(() => {});
    },
    async radio_change(e) {
      this.now_time = moment(moment().valueOf() - e.target.value).format(
        'YYYY-MM-DD HH:mm:ss'
      );
      this.search_form.time = [
        moment(moment().valueOf() - e.target.value).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        moment().format('YYYY-MM-DD HH:mm:ss')
      ];
      this.instance_details(); //获取cpu
      await this.get_vm_url(); // 获取chart的请求地址
      await this.get_query_metric(); // 获取chart的请求参数
      // this.get_chart(); // 获取chart
      this.get_params_chart();
    },
    async search_btn() {
      this.time_group = '';
      this.chart_loading = true;
      let params = {
        time: this.search_form.time.map((t) =>
          moment(t).format('YYYY-MM-DD HH:mm:ss')
        )
      };
      if (this.search_form.time[0]) {
        params.time[0] = moment(this.search_form.time[0]._d).format(
          'YYYY-MM-DD HH:mm:ss'
        );
        params.time[1] = moment(this.search_form.time[1]._d).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }

      this.instance_details();
      await this.get_vm_url(); // 获取chart的请求地址
      await this.get_query_metric(); // 获取chart的请求参数
      // this.get_chart(); // 获取chart
      this.get_params_chart();
    },
    instance_details() {
      // let params = {
      //   "alarm_start_time": "2025-03-04 16:00:00",
      //   "alarm_end_time": "2025-03-04 16:10:00",
      //   "third_data_source_id": 2
      // }
      let params = {
        alarm_start_time: moment(this.search_form.time[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        alarm_end_time: moment(this.search_form.time[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        third_data_source_id: this.search_form.third_data_source_id
      };
      API.instance_details(params)
        .then((res) => {
          this.cpu_data = res.data.data;
          this.alarm_list =
            res.data.data.alarm_list.length >= 10
              ? res.data.data.alarm_list.slice(0, 10)
              : res.data.data.alarm_list;
        })
        .catch((e) => {})
        .finally(() => {});
    },
    db_type_btn(e) {
      this.search_form.third_data_source_id = e.third_data_source_id;
      this.search_form.db_type = e.db_type;
    },
    get_chart(url) {
      let params = {
        // query: '{__name__=~"pg_mem_total_used|pg_disk_used_total"}',
        // start: 1744258082.336,
        // end: 1744279682.336,
        // step: '45s'
        query: this.query_metric,
        start: moment(this.search_form.time[0]).unix(),
        end: moment(this.search_form.time[1]).unix(),
        step: '40s'
      };

      axios({
        method: 'get',
        url: this.vm_query_url,
        params: params
      }).then((res) => {
        let arr = [];
        res.data.data.result.forEach((item) => {
          item.x_data = [];
          item.series_data = [];
          item.name = item.metric.__name__;
          item.values.forEach((v) => {
            item.x_data.push(
              moment(v[0] * 1000)
                .format('YYYY-MM-DD HH:mm:ss')
                .slice(11)
            );
            item.series_data.push(v[1]);
          });
          arr.push(item);
        });

        this.chart_list = arr;
        this.$nextTick(() => {
          this.getEahcrt();
        });
      });
    },

    getEahcrt() {
      this.chart_list.forEach((elem) => {
        let chartDom = document.getElementById(elem.name);
        let myChart = this.$echarts.init(chartDom);

        let option = {
          grid: {
            left: '10%',
            right: '10%',
            bottom: '10%',
            top: '15%'
          },
          title: {
            text: elem.metric.current_role,
            textStyle: {
              fontWeight: 500,
              fontSize: 12
            }
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              axisLine: {
                lineStyle: {
                  color: '#57617B'
                }
              },
              data: elem.x_data
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              name: '',
              type: 'line',
              smooth: true,
              symbol: 'circle',
              data: elem.series_data
            }
          ]
        };
        myChart.setOption(option);
        window.addEventListener('resize', () => {
          myChart.resize();
        });
      });
    },

    method_example(e) {
      let params = {
        instance_name: e || ''
      };
      API.get_instance_list(params)
        .then((res) => {
          this.instance_list_options = res.data.data;
        })
        .catch((e) => {})
        .finally(() => {});
    },
    // 获取图标请求接口
    get_vm_url() {
      return new Promise((resolve, reject) => {
        API.get_vm_url().then((res) => {
          if (res.status == 200) {
            this.vm_query_url = '';
            console.log(window.location);
            if (window.location.origin.includes('localhost')) {
              this.vm_query_url = res.data.data.vm_query_url;
            } else {
              this.vm_query_url =
                window.location.origin + '/prometheus/api/v1/query_range'; // 请求地址
            }

            this.step = res.data.data.vm_query_step; // 请求地址
            resolve(res.data.data.vm_query_url);
          } else {
            reject();
          }
        });
      });
    },
    // 获取参数
    get_query_metric() {
      return new Promise((resolve, reject) => {
        let params = {
          // db_type: 'MYSQL',
          // third_data_source_id: 21
          db_type: this.search_form.db_type,
          third_data_source_id: this.search_form.third_data_source_id,
          start: moment(this.search_form.time[0]).unix(),
          end: moment(this.search_form.time[1]).unix()
        };
        API.get_query_metric(params).then((res) => {
          if (res.status == 200) {
            if (res.data.data.length == 0) {
              this.charts_show = false;
            } else {
              this.query_metric = res.data.data[0].query_metric;
              this.chart_params_list = res.data.data;
              this.step = res.data.data[0].step;
              resolve(res.data.data[0].query_metric);
            }
          } else {
            reject;
          }
        });
      });
    },

    get_params_chart(t0, t1) {
      this.charts_show = true;
      this.chart_loading = true;
      this.chart_params_list.forEach((element) => {
        let params = {
          query: element.query_metric,
          start: moment(this.search_form.time[0]).unix(),
          end: moment(this.search_form.time[1]).unix(),
          step: this.step
        };

        axios({
          method: 'get',
          url: this.vm_query_url,
          params: params
        }).then((res) => {
          let arr = [];
          res.data.data.result.forEach((item) => {
            item.x_data = [];
            item.series_data = [];
            item.name = item.metric.__name__;
            item.values.forEach((v) => {
              item.x_data.push(
                moment(v[0] * 1000).format('YYYY-MM-DD HH:mm:ss')
              );
              item.series_data.push(Math.floor(v[1] * 100) / 100); // 保留两位小数
            });
            arr.push(item);
          });
          let chartDom = document.getElementById(
            element.metric_type + element.id
          );
          let myChart = this.$echarts.init(chartDom);
          //  无数据 隐藏div
          // if (res.data.data.result.length == 0) {
          //   document.getElementById(
          //     element.metric_type + element.id
          //   ).style.display = 'none';
          // } else {
          //   document.getElementById(
          //     element.metric_type + element.id
          //   ).style.display = 'block';
          // }
          if (res.data.data.result.length > 0) {
            let option = {
              grid: {
                left: '95',
                right: '10%',
                bottom: '10%',
                top: '15%'
              },
              title: {
                text: res.data.data.result[0].metric.__name__,
                textStyle: {
                  fontWeight: 500,
                  fontSize: 12
                }
              },
              tooltip: {
                trigger: 'axis'
              },
              xAxis: [
                {
                  type: 'category',
                  boundaryGap: false,
                  axisLine: {
                    lineStyle: {
                      color: '#57617B'
                    }
                  },
                  data: res.data.data.result[0].x_data
                }
              ],
              yAxis: [
                {
                  type: 'value',
                  axisLabel: {
                    formatter: '{value} ' + element.metric_unit // 在每个标签后添加单位
                  }
                }
              ],
              series: [
                {
                  name: '',
                  color:'#008adc',
                  type: 'line',
                  smooth: true,
                  symbol: 'circle',
                  data: res.data.data.result[0].series_data
                }
              ]
            };
            myChart.setOption(option);
            window.addEventListener('resize', () => {
              myChart.resize();
            });
          }
        });
      });
      this.chart_loading = false;
    },

    moment
  }
};
</script>

<style lang="less" scoped>
.dbacat_chart_detail {
  padding: 16px;
  background: #fff;

  .item_container_ {
    border: 1px dashed #cecece;
    padding: 16px;
    border-radius: 2px;
    margin-top: 16px;
  }

  .chart_box {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .chart_item {
      width: calc((100% - 32px) / 3);
      border: 1px solid #dfdfdf;
      border-radius: 4px;

      .title {
        // text-align: center;
        background: #f5f5f5;
        padding: 4px 8px;
      }
    }

    .chart_item_ {
      height: 300px !important;
    }
  }
}
.test {
  color: #fff;
  background: rgb(76, 187, 58);
}
.pro {
  color: #fff;
  background: rgb(250, 140, 22);
}
</style>