export default function (ctx) {
  const columns = [
    {
      title: '用户',
      dataIndex: 'user_name',
      key: 'user_name',
      shower: {
        useSlot: 'user_and_type'
      },
      scopedSlots: { customRender: 'Shower' }
      // width: 200
    },
    // {
    //   title: '用户类型',
    //   dataIndex: 'user_type',
    //   key: 'user_type',
    //   shower: (record) => {
    //     return {
    //       title: '用户类型',
    //       type: 'LimitLabel',
    //       showerProps: {
    //         label: record.user_type == 1 ? '用户组' : '用户名'
    //       },
    //       useSlot: 'user_type'
    //     };
    //   },
    //   scopedSlots: { customRender: 'Shower' },
    //   width: 120
    // },
    {
      title: '权限类型',
      key: 'permission_type',
      dataIndex: 'permission_type',
      // width: 200,
      shower: (record) => {
        return {
          // title: '权限类型',
          // type: 'LimitLabel',
          // showerProps: {
          //   label: record.permission_type == 1 ? '数据库登录' : '实例登录'
          // },
          useSlot: 'permission_type'
        };
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: '生效时间',
      dataIndex: 'created_at',
      key: 'created_at',
      shower: (record) => {
        return {
          // title: '生效时间',
          // type: 'DateFormat',
          // showerProps: {
            // text: record.created_at
          // },
          useSlot: 'created_and_updated'
        };
      },
      scopedSlots: { customRender: 'Shower' }
      // width: 250
    },
    // {
    //   title: '过期时间',
    //   dataIndex: 'updated_at',
    //   key: 'updated_at',
    //   shower: (record) => {
    //     return {
    //       title: '过期时间',
    //       type: 'DateFormat',
    //       showerProps: {
    //         text: record.updated_at
    //       }
    //     };
    //   },
    //   scopedSlots: { customRender: 'Shower' },
    //   width: 200
    // },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 150,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const searchFields = [
    {
      type: 'Input',
      label: 'user',
      mainSearch: true,
      key: 'user'
    },
    {
      type: 'Select',
      label: '用户类型',
      key: 'user_type',
      props: {
        options: [
          { label: '用户', value: 0 },
          { label: '用户组', value: 1 }
        ]
      }
    },
    {
      type: 'Select',
      label: '权限类型',
      key: 'permission_type',
      props: {
        options: [
          { label: '实例登录', value: 0 },
          { label: '数据库登录', value: 1 }
        ]
      }
    }
  ];
  return {
    columns,
    searchFields
  };
}
