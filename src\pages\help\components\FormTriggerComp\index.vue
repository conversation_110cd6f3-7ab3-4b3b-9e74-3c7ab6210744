<template>
  <div>
    <!-- <a-divider orientation="left">单个独立使用</a-divider>
    <div :style="{padding: '0 0 0 40px'}">
      <div :key="item.key" v-for="item in fields">
        <FormTriggerComp
          ref="FormTriggerComp"
          :field="item"
          v-model="baseInfoFormData[item.key]"
          @edit="onEdit"
          @save="onSave"
        ></FormTriggerComp>
      </div>
    </div> -->
    <a-divider orientation="left">
      Form（使用自定义组件）
      <a-button @click="getFormData">获取表单数据</a-button>
    </a-divider>
    <Form ref="useCompForm" v-bind="baseInfoFormParams" :compact="true" :useTriggerComp="true" :formData="baseInfoFormData"/>
    <a-divider orientation="left">Form</a-divider>
    <Form ref="baseInfoForm" v-bind="baseInfoFormParams" :formData="baseInfoFormData"/>
  </div>
</template>

<script>
import config from './config';
import FormTriggerComp from '@/components/Form/FormTriggerComp';
import Form from '@/components/Form';

export default {
  components: { FormTriggerComp, Form },
  props: {},
  data() {
    this.config = config(this);
    return {
      // 单个独立使用
      fields: this.config.baseFields.map(item => {
        let formItem = _.isFunction(item) ? item({}, this) : item;
        return formItem;
      }),
      // 表单使用
      baseInfoFormData: {
        app_id: 1
      },
      baseInfoFormParams: {
        labelCol: { span: 4 },
        wrapperCol: { span: 8 },
        fields: this.config.baseFields
      }
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    onEdit(key) {
      console.log('edit', key)
    },
    onSave(data, dataTxt, key) {
      console.log(data, dataTxt, key, 'save----')
    },
    getFormData() {
      const {useCompForm, baseInfoForm} = this.$refs
      console.log(useCompForm.getData(), baseInfoForm.getData())
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>
