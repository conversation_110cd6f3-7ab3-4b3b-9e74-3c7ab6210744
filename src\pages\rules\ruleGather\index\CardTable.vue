<template>
  <div class="card-table">
    <div class="card-table-main-info">
      <div class="left-block">
        <div class="info-box">
          <span class="project-name">
            <LimitLabel :label="cardData.name || ''" mode="ellipsis" :noWrap="true"></LimitLabel>
          </span>
        </div>

        <div class="tag-box">
          <span v-if="cardData.is_default == 1">默认</span>
          <DbImg
            :type="cardData.db_type"
            v-if="cardData.db_type"
            :schemaName="cardData.db_type"
            class="db-type-span"
          />
          <!-- <span>{{ cardData.rule_set_type }}</span> -->
        </div>

        <!-- <div class="side-info-box">
          <span class="avatar-part">
            <a-avatar icon="user" />
          </span>
          <span class="created-by">{{ cardData.updated_by }}</span>
          <span>于{{ cardData.updated_at + ' ' }}</span>
          <span class="event">修改</span>
        </div>-->
      </div>
      <div class="right-block">
        <div class="right-block-rules">
          <div>
            <span>规则数</span>
            <span>{{cardData.count || '--'}}</span>
          </div>
        </div>
        <a-divider type="vertical" />
        <div class="right-block-sql-text">
          <div>
            <span>规则状态</span>
            <span>
              <custom-icon
                :type="cardData.status == 0 ? 'lu-icon-disable' : 'lu-icon-enable'"
                :style="{color: cardData.status == 0 ? '#F38E9B' : '#4CBB3A', 'font-size': '24px'}"
              />
            </span>
          </div>
        </div>
        <a-divider class="right-block-divider" type="vertical" />
        <div class="right-block-botton">
          <slot name="action"></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';
import StatusTag from '@/components/Biz/Status/Tag';

export default {
  components: { LimitLabel, StatusTag },
  props: {
    cardData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  created() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.card-table {
  .card-table-main-info {
    display: flex;
    justify-content: space-between;
    .left-block {
      display: flex;
      flex-direction: column;
      margin-right: 32px;
      .info-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .status-box > span,
        > span {
          margin-right: 28px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          font-weight: 400;
        }
        .status-box {
          .review-type {
            white-space: nowrap;
            .ant-tag {
              font-size: 14px;
              margin-right: 0;
              padding: 0 8px;
            }
          }
        }
        .project-name {
          white-space: nowrap;
          > span {
            font-family: PingFangSC-Semibold;
            font-size: 20px;
            color: #27272a;
            font-weight: 600;
            display: block;
          }
          /deep/.limit-label {
            pre {
              font-family: PingFangSC-Semibold;
              font-size: 20px !important;
              color: #27272a;
              font-weight: 600 !important;
            }
          }
        }
      }
      .tag-box {
        margin: 8px 0 0 0;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        > .db-type-span,
        > span {
          margin-right: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #27272a;
          letter-spacing: 0;
          font-weight: 400;
          text-align: center;
          border: 1px solid #e4e4e7;
          border-radius: 4px;
          padding: 2px 7px;
        }
      }
      .side-info-box {
        display: flex;
        align-items: center;
        > span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          letter-spacing: 0;

          font-weight: 400;
          &.avatar-part {
            width: 20px;
            .ant-avatar {
              width: 20px;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              margin-bottom: 2px;
            }
          }
        }
        .created-by {
          margin: 0 4px 0 8px;
          color: rgb(113, 113, 122);
        }
        .event {
          color: rgb(113, 113, 122);
        }
      }
    }
    .right-block {
      display: flex;
      align-items: center;
      .ant-divider-vertical {
        width: 1px;
        height: 32px;
        margin: 0 12px;
      }
      .right-block-rules,
      .right-block-sql-text {
        padding: 0 18px 0 18px;
        display: flex;
        align-items: center;
        > div {
          display: flex;
          flex-direction: column;
          text-align: center;
          > span {
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: #27272a;
            font-weight: 400;
            &:first-child {
              font-size: 14px;
              color: #71717a;
              white-space: nowrap;
              margin-bottom: 8px;
            }
            &:last-child {
              margin-top: -6px;
            }
          }
        }
      }
      .right-block-rules {
        .rules {
          color: #e71d36 !important;
        }
      }
      .right-block-botton {
        padding-left: 18px;
        width: 160px;
        display: flex;
        align-items: center;
        justify-content: space-between !important;
        flex-wrap: wrap;
        > a {
          height: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #4db5f2;
          letter-spacing: 0;
          line-height: 16px;
          font-weight: 400;
          display: flex;
          align-items: center;
          margin-right: 12px;
          > .anticon {
            margin-right: 4px;
          }
          &.highlight {
            padding: 14px 6px 14px 8px;
            background: #4db5f2;
            border-radius: 4px;
            color: #fff;
            margin-right: 0;
            > .anticon {
              font-size: 12px;
              margin-right: 0;
              margin-left: 4px;
            }
          }
          &:hover {
            color: @primary-color;
            &.highlight {
              color: #fff;
              background: @primary-color;
            }
          }
        }
        > a[disabled] {
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .card-table {
    .card-table-main-info {
      .right-block {
        display: flex;
        justify-content: space-between;
        .right-block-divider {
          display: none;
        }
        .right-block-botton {
          width: 200px;
          display: none;
        }
      }
      .left-block {
        .info-box {
          .project-name {
            > span {
              display: block;
              max-width: 350px;
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1560px) {
  .card-table {
    .card-table-main-info {
      .left-block {
        .info-box {
          .project-name {
            > span {
              display: block;
              max-width: 540px;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less" scoped></style>
