<template>
  <div>
    <div class="flow-drag-comp">
      <div class="fdct-items">
        <div class="fdct-item">新建变更单</div>
        <div class="fdct-item">自动审核</div>
      </div>
      <Flow ref="Flow"></Flow>
    </div>
  </div>
</template>

<script>
import Draggable from '@/utils/drag';
import Flow from './Flow';

export default {
  components: { Flow },
  props: {},
  data() {
    this.sItemsDragInstance = null;
    return {};
  },
  mounted() {
    // 源节点添加拖动
    this.bindSourceItemsDraggable();
  },
  created() {},
  beforeDestroy() {
    if (this.sItemsDragInstance) {
      this.sItemsDragInstance.destroy();
      this.sItemsDragInstance = null;
    }
  },
  methods: {
    /**
     * 源节点添加拖动
     */
    bindSourceItemsDraggable() {
      const { Flow } = this.$refs;
      this.sItemsDragInstance = new Draggable({
        el: document.querySelectorAll('.fdct-item'),
        clone: true,
        onStart: (e, moveItem) => {},
        onMove: (e, moveItem) => {
          const canvasWrapper = Flow.getContainer();
          if (this.isInTarget(e, canvasWrapper)) {
            moveItem.style.cursor = 'copy';
          } else {
            moveItem.style.cursor = 'not-allowed';
          }
        },
        onEnd: (e, moveItem) => {
          if (moveItem.style.cursor === 'copy') {
            // mock
            let node = {
              code: 'xxxx',
              name: 'xxxxxxxxxx大幅度发的',
              type: 'task',
              ico: 'el-icon-present',
              state: 'running'
            };
            Flow.addNode(node, e);
          }
        }
      });
    },
    getPxVal(val) {
      let res = val + '';
      return parseFloat(res.split('px')[0] || 0);
    },
    isInTarget(e, target) {
      let flag = false;
      let { top, bottom, left, right } = target.getBoundingClientRect();
      if (
        e.pageX > left &&
        e.pageX < right &&
        e.pageY > top &&
        e.pageY < bottom
      ) {
        flag = true;
      }
      return flag;
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.flow-drag-comp {
  min-height: 400px;
  display: flex;

  .fdct-items {
    width: 200px;
    padding: 24px;
    border: 1px solid red;
  }
}
</style>

<style lang="less">
.fdct-item {
  // position: absolute;
  margin-bottom: 12px;
  border: 1px solid red;
  padding: 8px;
  text-align: center;
  cursor: move;
  background: #ffffff;
  position: relative;
}
</style>