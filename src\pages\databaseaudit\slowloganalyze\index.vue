<template>
  <div class="database-audit-slow-log-analyze up-20">
    <div v-if="panes.length > 0" class="slow-log-analyze-radio">
      <a-spin :spinning="spinning" :class="['left-spinning', isFold && 'fold']">
        <div class="radio-search">
          <a-input-search
            placeholder="请输入文件名"
            @search="searchInfo"
            v-if="!isFold"
          />
          <custom-icon
            type="upload"
            @click="upload"
            v-if="!isFold && $permission.slowLogAnalyze('upload')"
          />
          <custom-icon
            :type="!isFold ? 'menu-fold' : 'menu-unfold'"
            @click="fold"
          />
        </div>
        <a-radio-group
          class="custom-radio-group"
          v-model="checked"
          @change="onClickChange"
        >
          <template v-for="pane in panes || []">
            <a-radio-button :key="pane.id" :value="pane.id">
              <div class="slow-log-info">
                <div class="file-info">
                  <a-tag color="#108ee9">{{ '#' + pane.id }}</a-tag>
                  <LimitLabel
                    :label="pane.file_simpleness_name"
                    mode="ellipsis"
                    v-if="!isFold"
                  ></LimitLabel>
                </div>
                <a-popover
                  placement="top"
                  v-if="!isFold && pane.data_source_id"
                >
                  <template slot="content">
                    <div>{{ pane.db_name + '(' + pane.db_url + ')' }}</div>
                  </template>
                  <InstanceItem
                    view="new"
                    mode="limit"
                    :tagText="pane.instance_usage"
                    :src="pane.db_type"
                    :text="pane.db_name"
                    :isNeedTips="false"
                  />
                </a-popover>
                <div class="time-info" v-if="!isFold">
                  <div v-if="[2, -1].includes(pane.record_status)">
                    <a-popover placement="top" v-if="pane.record_status == -1">
                      <template slot="content">
                        <div style="color: #ef6173">
                          解析失败：{{ pane.error_message }}
                        </div>
                      </template>
                      <a-icon type="close-circle" theme="filled" />
                    </a-popover>
                    <span
                      >{{ pane.created_by }}上传 于{{ pane.created_at }}</span
                    >
                  </div>
                  <div v-if="[0, 1].includes(pane.record_status)">
                    <custom-icon type="loading" />
                    <span v-if="pane.record_status == 0">文件上传中...</span>
                    <span v-if="pane.record_status == 1">文件解析中...</span>
                  </div>
                </div>
              </div>
              <div class="btn-area" v-if="!isFold">
                <custom-icon
                  type="edit"
                  @click="upload(pane, true)"
                  v-if="$permission.slowLogAnalyze('edit')"
                />
                <a-popconfirm
                  title="确定删除?"
                  @confirm="remove(pane.id)"
                  v-if="$permission.slowLogAnalyze('delete')"
                >
                  <custom-icon type="delete" class="delete-btn" />
                </a-popconfirm>
              </div>
            </a-radio-button>
          </template>
        </a-radio-group>
      </a-spin>
      <Content ref="pageList" :pane="singlePane" :class="[isFold && 'fold']" />
    </div>
    <div class="ps-empty" v-else>
      <div>
        <div class="left-block">
          <div class="img">
            <img src="~@/assets/img/private/info.svg" alt />
          </div>
          <div class="title">操作手册</div>
        </div>
        <div class="middle-block"></div>
        <div class="right-block">
          <a-steps direction="vertical" size="small">
            <a-step title="从服务器下载慢日志文件" />
            <a-step title="上传慢日志文件">
              <a-button
                slot="description"
                type="primary"
                @click="upload"
                v-if="$permission.slowLogAnalyze('upload')"
                >上传</a-button
              >
            </a-step>
            <a-step title="查看解析结果，进行AI审核" />
          </a-steps>
        </div>
      </div>
    </div>
    <UploadDrawer ref="upload" @save="init" />
  </div>
</template>

<script>
import InstanceItem from '@/components/Biz/InstanceItem';
import UploadDrawer from './components/UploadDrawer.vue';
import Content from './Content';
import LimitLabel from '@/components/LimitLabel';
import { getSlowRecord, deleteSlowLog } from '@/api/databaseaudit/slowlog';

export default {
  components: { InstanceItem, Content, UploadDrawer, LimitLabel },
  data() {
    return {
      panes: [],
      newTabIndex: 0,
      authInfo: null,
      checked: null,
      spinning: false,
      singlePane: {},
      searchValue: undefined,
      isFold: false
    };
  },
  computed: {
    // canDo() {
    //   const user = this.$store.state.account.user || {};
    //   return ['admin', 'dba'].includes(user.role);
    // }
  },
  mounted() {
    this.init();
  },
  methods: {
    init(params = { page_number: 1, page_size: 10 }) {
      getSlowRecord(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data.results');
            if (resData && !_.isEmpty(resData)) {
              this.checked = resData[0].id;
              this.singlePane = resData[0];
              this.panes = resData.map(item => {
                return {
                  ...item,
                  instance_usage: item.db_env
                };
              });
            }
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    upload(pane, bool) {
      this.$refs.upload.show(pane, bool);
    },
    onClickChange(e) {
      const id = e.target.value;
      this.panes.forEach(item => {
        if (item.id == id) {
          this.singlePane = item;
        }
      });
    },
    remove(id) {
      deleteSlowLog({ record_id: id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.init({
              flie_name: this.searchValue,
              page_number: 1,
              page_size: 10
            });
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    searchInfo(value) {
      this.searchValue = value;
      this.init({
        file_simpleness_name: value,
        page_number: 1,
        page_size: 10
      });
    },
    // 折叠
    fold() {
      this.isFold = !this.isFold;
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.database-audit-slow-log-analyze {
  /deep/ .slow-log-analyze-radio {
    display: flex;
    .left-spinning {
      background: #fff;
      width: 240px;
      .radio-search {
        // width: 100%;
        height: 56px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px;
        border-bottom: 1px solid #ededec;
        .ant-input-search {
          width: 136px;
        }
        .anticon {
          font-size: 16px;
          color: #000000;
          &:hover {
            color: #4ec3f5;
            cursor: pointer;
          }
        }
      }
      .custom-radio-group {
        background: #fff;
        min-height: 736px;
        overflow-y: auto;
        width: 100%;
        &::-webkit-scrollbar {
          width: 4px;
          height: 4px;
        }
        .ant-radio-button-wrapper {
          position: relative;
          height: auto;
          border: none;
          width: 100%;
          line-height: 0;
          border-radius: 0;
          border-top: 1px solid #ededec;
          box-shadow: 0 0 0 0 #fff;
          padding: 12px 12px 16px 12px;
          &:first-child {
            border-top: none;
          }
          &:not(:first-child)::before {
            display: none;
          }
          .ant-radio-button {
            display: none;
          }
          .slow-log-info {
            width: 100%;
            .biz-instance-item {
              width: 100% !important;
              padding-bottom: 12px;
              display: inline-block;
              .instance-item-tag {
                background: transparent;
                border: 0;
                padding: 0 !important;

                &::after {
                  display: none;
                }

                .database-image {
                  margin-left: 0;
                  width: auto !important;
                  max-width: 85% !important;
                  margin-right: 8px;

                  > span > .custom-icon {
                    margin-right: 0;
                  }
                  > span > .iconText {
                    overflow: hidden;
                    max-width: 160px;
                    text-overflow: ellipsis;
                    > pre {
                      font-size: 13px;
                      color: #71717a;
                      font-weight: 400;
                      white-space: nowrap;
                    }
                  }
                }
              }

              &:hover {
                .iconText {
                  color: @primary-color;
                  font-weight: 500;
                }
              }
            }
            .file-info {
              display: flex;
              align-items: center;
              padding-bottom: 12px;
              > span {
                color: #008adc;
                &.ant-tag {
                  background: #008adc;
                  font-size: 12px;
                  color: #ffffff;
                  font-weight: 600;
                }
              }
              .limit-label {
                line-height: 1;
                > pre {
                  font-size: 14px;
                  color: #008adc;
                  font-weight: 400;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  margin: 0;
                }
              }
            }
            .time-info {
              display: flex;
              text-align: left;
              padding-bottom: 4px;
              > div {
                display: flex;
                align-items: center;
                .anticon-close-circle {
                  font-size: 13px;
                  color: rgba(231, 29, 54, 0.75);
                }
                > span {
                  line-height: 13px;
                  display: inline-block;
                  font-size: 12px;
                  color: #a1a1a1;
                  font-weight: 500;
                  margin-left: 4px;
                }
              }
            }
          }
          .btn-area {
            position: absolute;
            bottom: 0;
            right: 0;
            visibility: hidden;

            .anticon {
              font-size: 13px;
              padding: 2px;
              color: #fff;
              background: #71717a;
              &:hover {
                background: #4ec3f5;
                cursor: pointer;
              }
            }
            .delete-btn {
              margin-left: 2px;
            }
          }
          &:hover {
            .btn-area {
              visibility: visible;
            }
          }
          &.ant-radio-button-wrapper-checked {
            background: #eff5ff;
            .file-info {
              > span {
                > pre {
                  font-weight: 600;
                }
              }
            }
          }
        }
      }
      &.fold {
        width: 60px;
        .radio-search {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .custom-radio-group {
          .ant-radio-button-wrapper {
            padding: 0;
            padding-left: 8px;
            .slow-log-info {
              .file-info {
                height: 100px;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
          }
        }
      }
    }
    .database-audit-slow-log-analyze-content {
      width: calc(100% - 240px);
      &.fold {
        width: calc(100% - 60px);
      }
    }
  }
  .ps-empty {
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 800px;
    > div {
      display: flex;
      justify-content: center;
      .left-block {
        display: flex;
        flex-direction: column;
        align-items: center;
        .img {
          margin-bottom: 24px;
        }
        .title {
          text-align: center;
          font-size: 24px;
          color: #27272a;
        }
      }
      .middle-block {
        width: 64px;
      }
      .right-block {
        /deep/.ant-steps {
          .ant-steps-item {
            .ant-steps-item-container {
              .ant-steps-item-icon {
                background: #4db5f2;
                .ant-steps-icon {
                  color: #fff;
                }
              }
              .ant-steps-item-content {
                min-height: 72px;
                display: flex;
                overflow: visible;
                .ant-steps-item-title {
                  font-size: 16px;
                  color: #27272a;
                }
                .ant-steps-item-description {
                  .ant-btn {
                    // height: 24px;
                    // line-height: 24px;
                    position: relative;
                    top: -4px;
                    > span {
                      padding: 0;
                      font-size: 14px;
                      color: #ffffff;
                      font-weight: 600;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>