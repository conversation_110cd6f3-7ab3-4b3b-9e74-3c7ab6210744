<template>
  <a-modal v-model="visible" title="最佳性能实践" :maskClosable="false" width="700px">
    <div class="content">
      <!-- SQL文本结果 -->
      <div class="top-content">
        <div class="title">
          <span class="des">我们推荐使用以下query样式：</span>
        </div>
        <div class="coder-wrapper">
          <!-- <Coder
                class="default-full-white"
                v-model="sqlText"
                :needFormat="false"
                v-bind="coderParams"
                height="500"
          ></Coder>-->
          <!-- <pre>{{ 'asdffffffffff465465132132132' }}</pre> -->
          <sql-highlight :sql="data.OPT_SQL_AFTER"></sql-highlight>
        </div>
      </div>
      <a-divider />
      <div class="bottom-content">
        <div class="title">
          <span class="des">替代以下query样式：</span>
        </div>
        <div class="coder-wrapper">
          <!-- <Coder
                class="default-full-white"
                v-model="sqlText"
                :needFormat="false"
                v-bind="coderParams"
                height="500"
          ></Coder>-->
          <!-- <pre>{{ 'dafdsfasdfasasasasasas' }}</pre> -->
          <sql-highlight :sql="data.OPT_SQL_BEFORE"></sql-highlight>
        </div>
      </div>
    </div>
    <div slot="footer">
      <a-button @click="onCancel">关闭</a-button>
    </div>
  </a-modal>
</template>

<script>
import Coder from '@/components/Coder';
import SqlHighlight from '@/components/SqlHighlight';
import config from './config';
export default {
  components: { Coder, SqlHighlight },
  props: {},
  computed: {},
  data() {
    this.config = config(this);
    return {
      loading: false,
      visible: false,
      data: {},
      coderParams: {
        height: '50%',
        options: {
          theme: 'default',
          readOnly: true
        }
        // needFormat: true
      }
    };
  },
  watch: {},
  methods: {
    show(data) {
      this.visible = true;
      this.data = data;
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.ant-modal-body {
  font-size: 15px;
}
/deep/ div.sql-format {
  // background: #fafafa;
  background: #fff;
}
.des {
  font-weight: 600;
}
</style>