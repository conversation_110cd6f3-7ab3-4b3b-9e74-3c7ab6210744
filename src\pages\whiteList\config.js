export default function (ctx) {
  const statusColor = {
    待评审: '#FC925E',
    评审中: '#4F96FB',
    已通过: '#6EC84A',
    未通过: '#F9676B',
    未提交: '#AFADAD'
  };

  const columns = [
    {
      title: '标签ID',
      dataIndex: 'label_id',
      key: 'label_id',
      scopedSlots: { customRender: 'label_id' },
      width: 100
    },
    {
      title: '类型',
      dataIndex: 'label_type',
      key: 'label_type',
      scopedSlots: { customRender: 'label_type' },
      width: 150
    },
    {
      title: 'SQL/XML',
      dataIndex: 'sql_source',
      key: 'sql_source',
      scopedSlots: { customRender: 'sql_source' },
      width: 150
    },
    {
      title: '标签属性',
      key: 'label_attribute',
      dataIndex: 'label_attribute',
      customRender: (text) => {
        return text == 0 ? '白名单' : '整改中'
      },
      width: 150
    },
    {
      title: '标签来源',
      key: 'label_source',
      dataIndex: 'label_source',
      customRender: (text) => {
        return text == 0 ? '事前审核' : '数据库审核'
      },
      width: 150
    },
    {
      title: '项目',
      dataIndex: 'projects',
      key: 'projects',
      scopedSlots: { customRender: 'projects' },
      width: 100
    },
    {
      title: '数据源',
      dataIndex: 'data_source',
      key: 'data_source',
      scopedSlots: { customRender: 'data_source' },
      width: 150
    },
    // {
    //   title: 'Scheam',
    //   dataIndex: 'scheam',
    //   key: 'scheam',
    //   scopedSlots: { customRender: 'scheam' },
    //   width: 100
    // },
    {
      title: '状态',
      key: 'label_status',
      dataIndex: 'label_status',
      scopedSlots: { customRender: 'label_status' },
      width: 120
    },
    {
      title: '生效时间',
      key: 'audit_time',
      sorter: true,
      dataIndex: 'audit_time',
      width: 130
    },
    {
      title: '失效时间',
      key: 'expired_time',
      dataIndex: 'expired_time',
      width: 120
    },
    {
      title: '申请人',
      key: 'created_by',
      dataIndex: 'created_by',
      scopedSlots: { customRender: 'created_by' },
      width: 120
    },
    {
      title: '审核人',
      dataIndex: 'audit_user',
      key: 'audit_user',
      scopedSlots: { customRender: 'audit_user' },
      width: 180
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 180,
      visible: $permissionBatch.some([
        // { module: 'whiteList', values: ['delete'] }
        { module: 'whiteList', values: ['del'] }
      ]),
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = [
    {
      type: 'Input',
      label: '标签ID',
      key: 'label_id'
    },
    {
      type: 'Input',
      label: 'SQL/XML',
      key: 'sql_source'
    },
    {
      type: 'Select',
      label: '类型',
      key: 'label_type',
      props: {
        options: [
          { label: 'SQL_ID', value: 0 },
          { label: 'XML_TEXT', value: 1 },
          { label: 'XML_ID', value: 2 },
          { label: 'TABLE', value: 3 },
          { label: 'JAVA', value: 4 },
          { label: 'PROCEDURE', value: 5 }
        ]
      }
    },
    {
      type: 'Select',
      label: '标签属性',
      key: 'label_attribute',
      props: {
        options: [
          {
            label: '白名单',
            value: 0
          },
          {
            label: '整改中',
            value: 1
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '项目',
      key: 'projects',
      props: {
        url: '/sqlreview/review/all_project',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'DataBaseChoose',
      label: '数据源',
      key: 'data_source',
      props: {
        url: '/sqlreview/project/data_source_choices',
        beforeLoaded(data) {
          return data.map((item) => {
            return {
              ...item,
              instance_usage: item.env,
              showText: item.label + '(' + item.db_url + ')',
              view: 'new',
              limit: 'limit'
            };
          });
        },
        mode: 'default',
        optionLabelProp: 'children',
        allowSearch: true,
        backSearch: true,
        limit: 30
      }
    },
    {
      type: 'Select',
      label: '申请人',
      key: 'created_by',
      props: {
        url: '/sqlreview/project_config/select_user',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'Select',
      label: '审核人',
      key: 'audit_user',
      props: {
        url: '/sqlreview/project_config/select_user',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'RangePicker',
      label: '生效时间',
      key: 'audit_time',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    },
    {
      type: 'RangePicker',
      label: '失效时间',
      key: 'expired_time',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    },
    {
      type: 'Select',
      label: '状态',
      key: 'label_status',
      props: {
        options: [
          {
            label: '审核通过',
            value: 'pass'
          },
          {
            label: '已失效',
            value: 'expired'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '标签来源',
      key: 'label_source',
      props: {
        options: [
          {
            label: '事前审核',
            value: 0
          },
          {
            label: '数据库审核',
            value: 1
          }
        ]
      }
    },
    {
      type: 'Input',
      label: '',
      key: 'btns',
      hideComponent: true,
      slots: [{ key: 'btns' }],
      rules: []
    }
  ];
  return {
    columns,
    statusColor,
    searchFields: fields
  };
}
