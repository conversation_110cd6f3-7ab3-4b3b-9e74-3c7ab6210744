<template>
  <div class="order page-list-single">
    <SwitchTable
      ref="switchTable"
      v-bind="tableParams"
      :cardColumns="cardColumns"
      :listColumns="listColumns"
    >
      <template slot="tableTopRight">
        <div class="checkbox-block">
          <a-checkbox @change="onChange" :checked="checked"> 仅看待我审核 </a-checkbox>
        </div>
        <a-button
          slot="extra"
          type="primary"
          class="new-button"
          icon="plus"
          @click="addProject"
          >新建</a-button
        >
        <div class="switch-view">
          <a-tooltip>
            <template slot="title"> 切换视图 </template>
            <custom-icon
              type="lu-icon-viewlist"
              @click="switchView"
            ></custom-icon>
          </a-tooltip>
        </div>
      </template>
      <!-- 卡片模式 -->
      <SqlReviewCard
        slot="cardTable"
        slot-scope="{ record }"
        v-bind="{ cardData: record }"
        @toDetail="toDetail"
      >
        <template slot="action">
          <a
            @click="aKeyPass(record.id)"
            :disabled="!['待评审', '评审中'].includes(record.dba_status)"
          >
            <custom-icon type="file-done" />一键通过
          </a>
          <a @click="showReport(record)">
            <custom-icon type="file-done" />报表
          </a>
          <!-- <a @click="toDetail(record, $event)" class="highlight">
            详情
            <custom-icon type="lu-icon-right" />
          </a> -->
          <a
            @click="reject(record)"
            :disabled="!['待评审', '评审中'].includes(record.dba_status)"
          >
            <custom-icon type="close" />一键驳回
          </a>
        </template>
      </SqlReviewCard>
      <!-- 列表模式 -->
      <template slot="id" slot-scope="{ text, record }">
        <a @click="toDetail(record, $event)">{{ text }}</a>
      </template>
      <template slot="project_name" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </template>
      <template slot="project_group" slot-scope="{ text }">
        <span class="project-group" v-if="text && text.length > 0">
          <span>{{ text[0] }}</span>
          <span v-if="text.length > 1">
            <a-tooltip>
              <template slot="title">
                <span>{{ text.toString() }}</span>
              </template>
              <span>...</span>
            </a-tooltip>
          </span>
        </span>
      </template>
      <template slot="review_point" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </template>
      <div slot="dba_status" slot-scope="{ text, record }" class="dba-status">
        <StatusTag type="dba" :status="record.dba_status" />
        <a-popover>
          <template slot="content">
            <span>催办</span>
          </template>
          <a-icon
            type="thunderbolt"
            v-if="record.is_urge == 1"
            style="font-size: 16px"
          ></a-icon>
        </a-popover>
      </div>
      <template slot="review_process" slot-scope="{ record }">
        <span v-if="record.review_process" style="color: #e87d00"
          >待处理条数：{{ record.review_process }}</span
        >
        <span v-else style="color: #70b603">已完成</span>
      </template>
      <template slot="created_by" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_creater" placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{ record.ch_creater }}</span>
          </div>
        </a-tooltip>
        <span v-else>{{ text || '--' }}</span>
      </template>
      <template slot="operator_dba" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_dba" placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{ record.ch_dba }}</span>
          </div>
        </a-tooltip>
        <span v-else>{{ text || '--' }}</span>
      </template>
      <!-- DBA负责人 -->
      <template slot="dba_leader" slot-scope="{ record, text }">
        <span class="dba-leader" v-if="text && text.length > 0">
          <span>{{ text[0] }}</span>
          <span v-if="text.length > 1">
            <a-tooltip>
              <template slot="title">
                <span>{{ text.toString() }}</span>
              </template>
              <span>...</span>
            </a-tooltip>
          </span>
        </span>
      </template>
      <template slot="project_group_leader" slot-scope="{ record, text }">
        <span class="project-group-leader" v-if="text && text.length > 0">
          <span>{{ text[0] }}</span>
          <span v-if="text.length > 1">
            <a-tooltip>
              <template slot="title">
                <span>{{ text.toString() }}</span>
              </template>
              <span>...</span>
            </a-tooltip>
          </span>
        </span>
      </template>

      <custom-btns-wrapper slot="action" slot-scope="{ record }" :limit="3">
        <a
          @click="aKeyPass(record.id)"
          actionBtn
          v-if="['待评审', '评审中'].includes(record.dba_status)"
          >一键通过</a
        >
        <a @click="showReport(record)" actionBtn>报表</a>
        <!-- <a @click="toDetail(record, $event)" actionBtn>详情</a> -->
        <a
          actionBtn
          @click="reject(record)"
          class="highlight"
          v-if="['待评审', '评审中'].includes(record.dba_status)"
        >
          一键驳回
        </a>
      </custom-btns-wrapper>
    </SwitchTable>
    <HistoryBaseline ref="historyBaseline" @onSave="onSave"></HistoryBaseline>
    <!-- 新建项目弹窗 -->
    <AddModal ref="addModal" @save="saveProject"></AddModal>
    <!-- 一键通过弹窗 -->
    <GetPassModal ref="getPass" @refresh="onRefresh"></GetPassModal>
    <!-- 报表抽屉 -->
    <Report ref="Report"></Report>
    <!-- 一键驳回弹窗 -->
    <RejectModal ref="reject" @refresh="reset"></RejectModal>
  </div>
</template>

<script>
import { createReview, sendEmail, editHistoryBaseline } from '@/api/home';

import LimitTags from '@/components/LimitTags';
import Status from '@/components/Biz/Status';
import SearchArea from '@/components/SearchArea';
import AddModal from './components/AddModal';
import GetPassModal from './components/GetPassModal';
import Report from './components/Report';
import RejectModal from './components/RejectModal';
import HistoryBaseline from '@/pages/home/<USER>/index/components/HistoryBaseline';
import moment from 'moment';
import config from './config';
import SwitchTable from '@/components/PageListNew/SwitchTable/table.vue';
import SqlReviewCard from './components/SqlReviewCard';
import StatusTag from '@/components/Biz/Status/Tag';

export default {
  name: 'order-list-sql-review',
  components: {
    Status,
    SearchArea,
    AddModal,
    Report,
    GetPassModal,
    LimitTags,
    SwitchTable,
    SqlReviewCard,
    StatusTag,
    HistoryBaseline,
    RejectModal
  },
  props: {
    orderType: String
  },
  data() {
    this.config = config(this);
    const searchCacheData = _.get(
      this.$store.state,
      'common.searchCache.orderList'
    );
    let tableQuery = { ...this.$route.query };
    let createdAt = this.$route.query.created_at;
    if (createdAt) {
      let createdAtArr = createdAt.split(',');
      tableQuery.created_at = createdAtArr.map(item => moment(item));
    }
    if (_.isEmpty(searchCacheData)) {
      this.$store.commit('common/setSearchCache', {
        orderListsqlreview: { ...tableQuery }
      });
    }
    // keep-alive是否激活
    this.activated = null;
    return {
      statusColor: this.config.statusColor,
      tableParams: {
        url: '/sqlreview/review/dba_list',
        reqParams: {},
        columns: this.config.cardColumns,
        rowKey: 'id',
        showHeader: false,
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' },
        cacheKey: 'orderList' + this.orderType
      },
      cardColumns: this.config.cardColumns,
      listColumns: this.config.columns,
      checked: false
    };
  },
  mounted() {},
  activated() {
    if (this.activated === false) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refreshKeep();
      this.activated = true;
    }
  },
  deactivated() {
    this.activated = false;
  },
  computed: {
    showAKeyPass() {
      const role = this.$store.state.account.user.role || '';
      return role == 'admin' || role.toLowerCase() == 'dba';
    },
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  created() {},
  methods: {
    // 切换视图
    switchView() {
      const { switchTable } = this.$refs;
      switchTable.switchView();
    },
    // 带我审核checkbox
    onChange(e) {
      const bool = _.get(e, 'target.checked')
      this.checked = bool;
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { is_wait_audit: bool ? 1 : 0 });
      table.refresh(null, { is_wait_audit: bool ? 1 : 0 });
    },
    // 新建项目
    addProject() {
      const { addModal } = this.$refs;
      addModal.show();
    },
    // 新建项目保存
    saveProject(data) {
      const { addModal, switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      // console.log(JSON.stringify(data), '898989');

      // 请求
      this.$showLoading();
      createReview({
        ...data,
        source_flag: 1
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            addModal.hide();
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    toDetail(record, e) {
      this.$router.push({
        name: 'orderDetail',
        params: { id: record.id },
        query: { status: record.dba_status, activeKey: 'sqlreview' }
      });
    },
    reject(record) {
      this.$refs.reject.show(record);
    },
    showReport(record) {
      this.$refs.Report.show(record);
    },
    // 查询
    // search(data) {
    //   const { switchTable } = this.$refs;
    //   const { table } = switchTable.$refs;
    //   table.refresh(null, data);
    // },
    // 重置
    reset() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refresh();
    },
    onRefresh() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refresh();
      this.$emit('getOrderTotal');
    },
    // 一键通过
    aKeyPass(id) {
      this.$refs.getPass.show(id);
    },
    // 提交评审
    authSubmit(id) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      this.$confirm({
        title: '确认提交审批?',
        onOk: () => {
          this.$showLoading();
          sendEmail({
            record_id: id
          })
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.$hideLoading({ tips: _.get(res, 'data.message') });
                table.refresh();
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        },
        onCancel() {}
      });
    },
    // 历史标准基线
    showHistoryBaselineModel(record) {
      this.$refs.historyBaseline.show(record);
    },
    onSave(id) {
      const params = {
        id: id,
        history_baseline: 1
      };
      this.$showLoading();
      editHistoryBaseline(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.order {
  &.page-list-single {
    /deep/.custom-table {
      .search-area-wrapper {
        padding: 12px 24px;
        .custom-table-top-right {
          display: flex;
          align-items: center;
          .checkbox-block {
            margin-right: 12px;
          }
          .new-button {
            margin-right: 12px;
          }
          .switch-view {
            font-size: 16px;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            &:hover {
              cursor: pointer;
              background: #7adcff;
              color: #ffffff;
            }
          }
        }
      }
    }
    .ant-table-wrapper {
      .ant-table {
        .ant-table-content {
          .ant-table-thead {
            > tr {
              > th {
                background: #fafafa;
                border-top: 1px solid #e8e8e8;
                border-right: 1px solid #e8e8e8;
                &:last-child {
                  border-right: none;
                }
              }
            }
          }
        }
      }
    }
  }
}
.ant-dropdown-link {
  margin-top: 5px;
}

.des {
  display: flex;
  justify-content: center;
  align-items: center;
  > span {
    margin-left: 4px;
  }
}

.project-group,
.dba-leader,
.project-group-leader {
  display: flex;
  > span {
    margin-right: 16px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #27272a;
    text-align: center;
    border: 1px solid #e4e4e7;
    border-radius: 4px;
    padding: 4px 7px;
    white-space: nowrap;
  }
}

.dba-status {
  display: flex;
  align-items: center;
  .anticon-thunderbolt {
    margin-left: 8px;
    color: rgb(232, 125, 0);
  }
}
</style>