import Http from '@/utils/request'

export function addComments(data = {}) {
  return Http({
    url: `/sqlreview/comment-config/`,
    method: 'post',
    data: data
  });
}

export function deleteComments(data = {}) {
  return Http({
    url: `/sqlreview/comment-config/${data.id}/`,
    method: 'delete',
    params: {}
  });
}
export function editComments(data = {}) {
  return Http({
    url: `/sqlreview/comment-config/${data.id}/`,
    method: 'put',
    data: data
  });
}