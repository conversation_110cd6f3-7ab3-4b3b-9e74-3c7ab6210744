export default function (ctx) {
  const getFields = (node = {}) => {
    let fields = [
      {
        type: 'Input',
        label: '用户组名称',
        key: 'name',
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Select',
        label: '绑定项目',
        key: 'projects',
        props: {
          mode: 'multiple',
          url: '/sqlreview/project/list-all',
          reqParams: {},
          allowSearch: true,
          backSearch: true,
          limit: 50
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Textarea',
        label: '用户组描述',
        key: 'group_desc'
      }
    ];

    return fields;
  };
  return {
    getFields
  };
}
