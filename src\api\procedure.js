import Http from '@/utils/request'
export function isSuccessCode(e) {
  return e.code == 0 || e.resCode == 0;
}
export function reviewProcedure(params = {}) {
  return Http({
    url: `/sqlreview/review/review-procedure/`,
    method: 'post',
    data: params
  });
}

export function reviewProcedureDetail(params = {}) {
  return Http({
    url: `/sqlreview/review/review-procedure/${params.id}/`,
    method: 'get',
    params: {}
  });
}

export function exportProcedure(params = {}) {
  return Http({
    url: `/sqlreview/review/${params.id}/export_procedure/`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}
// 详情页面开始
export function reveiewPass(data) {
  return Http({
    url: `/sqlreview/review/procedure-detail-pass/`,
    method: 'post',
    data: data
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      return true;
    } else {
      return e;
    }
  })
}
export function reveiewFail(data) {
  return Http({
    url: `/sqlreview/review/procedure-detail-fail/`,
    method: 'post',
    data: data
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      return true;
    } else {
      return e;
    }
  })
}
export function removeReview(data) {
  return Http({
    url: `/sqlreview/review/procedure-delete-comment/`,
    method: 'post',
    data: data
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      return true;
    } else {
      return e;
    }
  })
}
export function getDetail(params, isInfo) {
  return Http({
    url: isInfo ? `/sqlreview/review/procedure-detail-info/` : `/sqlreview/review/procedure-detail/`,
    method: 'get',
    params: params
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      return e.data;
    } else {
      return e;
    }
  })
}

export function getDisOrAgree() {
  return Http({
    url: `/sqlreview/project/comment-config/get-all`,
    method: 'get',
    params: {
      review_type: 'PROCEDURE'
    }
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      let items = {
        pass: [],
        fail: [],
        filter: []
      }
      const { filter = [], not_pass: notPass = [], pass = [] } = e.data || {}
      items.pass = pass.map(it => {
        return {
          key: it.value,
          name: it.label
        }
      })
      items.fail = notPass.map(it => {
        return {
          key: it.value,
          name: it.label
        }
      })
      items.filter = filter.map(it => {
        return {
          key: it.value,
          name: it.label
        }
      })
      return items;
    }
    return e;
  });
}

export function getSaveAdvice(data) {
  return Http({
    url: 'sqlreview/review/procedure-detail-update/',
    method: 'post',
    data: data
  })
}
export function reviewWhiteListAction(data = {}) {
  return Http({
    url: `/sqlreview/review/procedure-white-list/`,
    method: 'post',
    data: data
  });
}
export default {};
