<template>
  <a-modal v-model="visible" title="差异对比" :maskClosable="false" width="1200px">
    <a-spin :spinning="loading" class="rewrite-info-wraper">
      <!-- <code-mirror
        class="rewrite-info-sql"
        ref="sql"
        :theme="'white'"
        :orig="sql.sql || ''"
        origTitle="原SQL"
        :value="sqlRewrite.sql || ''"
        valueTitle="改写后的SQL"
        :format="'sql'"
        :options="mirrorOptions"
        :origPos="'Left'"
      />
      <code-mirror
        class="rewrite-info-plan"
        ref="plan"
        :theme="'white'"
        :orig="sql.sql_plan"
        origTitle="原执行计划"
        :value="sqlRewrite.sql_plan || ''"
        valueTitle="改写后的执行计划"
        :format="'txt'"
        :options="mirrorOptions"
        :origPos="'Left'"
      />-->
      <Diff :list="diffList" :format="true" v-if="diffList.length > 0" />
      <custom-empty v-else />
    </a-spin>
    <div slot="footer">
      <a-button @click="onCancel">关闭</a-button>
    </div>
  </a-modal>
</template>

<script>
import { sqlDifference } from '@/api/review';
// import CodeMirror from '@/components/CodeMirror';
import Diff from '@/components/Diff';
import config from './config';
export default {
  components: { Diff },
  props: {},
  computed: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      loading: false,
      mirrorOptions: {
        lineNumbers: true
      },
      diffList: []
    };
  },
  watch: {},
  methods: {
    show(data = {}) {
      this.visible = true;
      // 获取详情数据
      this.loading = true;
      sqlDifference({
        detail_id: this.$route.params.id,
        sql_rewrite_detail_id: data.sql_rewrite_detail_id
      })
        .then(res => {
          this.loading = false;
          if (_.get(res, 'data.code') == 0) {
            const sql = _.get(res, 'data.data.sql') || {};
            const sqlRewrite = _.get(res, 'data.data.sql_rewrite') || {};
            this.diffList = [
              {
                filename: '原SQL → 改写后的SQL',
                oldStr: sql.sql || '',
                newStr: sqlRewrite.sql || '',
                type: 'sql'
              },
              {
                filename: '原执行计划 → 改写后的执行计划',
                oldStr: sql.sql_plan || '',
                newStr: sqlRewrite.sql_plan || '',
                type: 'txt'
              }
            ];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.loading = false;
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });

      // 刷新ui
      // setTimeout(() => {
      //   const { sql, plan } = this.$refs;
      //   sql.refresh();
      //   plan.refresh();
      // }, 400);
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    }
  }
};
</script>

<style lang="less" scoped>
.rewrite-info-wraper {
  // /deep/ .custom-code-mirror-diff {
  //   background: #ffffff;
  //   box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
  //   border-radius: 8px;
  //   margin-bottom: 24px;
  //   border: 1px solid #f5f5f5;

  //   .ccm-diff-container .CodeMirror-merge {
  //     min-height: 100px;
  //   }

  //   &.rewrite-info-plan {
  //     .CodeMirror {
  //       height: auto;
  //     }
  //   }
  // }
}
</style>