import Http from '@/utils/request';
// real-sql 获取数据源基本信息
export function getDatasourceBaseInfo(params) {
  return Http({
    url: `/sqlreview/after_audit/datasource_base_info`,
    method: 'get',
    params: params
  });
}

// real-sql Mysql 查询实时SQL任务的运行情况
export function getRealTimeInfo(params, cancel) {
  let url = `/sqlreview/api/v1/realtime/mysql/${params.id}`;
  const paramsArr = [
    { key: 'user', value: params.user },
    { key: 'host', value: params.host },
    { key: 'info', value: params.info },
    { key: 'db', value: params.db },
    { key: 'no_sleep', value: params.no_sleep }
  ].filter(item => item.value !== undefined && item.value !== '');
  if (paramsArr.length > 0) {
    let arr = []
    paramsArr.forEach(item => {
      const res = `${item.key}=${item.value}`
      arr.push(res)
    })
    url = url + '?' + arr.join('&')
  }
  return Http({
    url,
    method: 'post',
    data: params,
    getCancel: (c) => {
      cancel(c);
    }
  });
}

// 查询Mysql全部实例
export function getDatabases(params) {
  return Http({
    url: `/sqlreview/after_audit/select_data`,
    method: 'get',
    params
  });
}

// 查询Mysql全部用户
export function getUsers(params) {
  return Http({
    url: `/sqlreview/after_audit/select_data`,
    method: 'get',
    params
  });
}

// real-sql 强制停止采集实时SQL任务
// export function deleteRealTimeInfo(params) {
//   return Http({
//     url: `/sqlreview/api/v1/realtime/mysql/${params.id}`,
//     method: 'delete'
//   });
// }

// 执行计划
export function afterSqlTextExplain(params) {
  return Http({
    url: `/sqlreview/after_audit/after_sql_text_explain`,
    method: 'post',
    data: params
  });
}

// kill
export function realtimeKill(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/mysql/${params.data_source_id}/${params.id}`,
    method: 'delete'
  });
}
// kill pgsql
export function realtimePgsqlKill(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/postgresql/${params.data_source_id}/${params.pid}`,
    method: 'delete'
  });
}

// ob数据库接口
//  检测ob的实时是不是在
export function checkObConfig(params) {
  return Http({
    url: `/sqlreview/after_audit/check_ob_config`,
    method: 'get',
    params
  });
}

// ob real-sql 查询实时SQL任务的运行情况
export function getObRealTimeInfo(params, cancel) {
  return Http({
    url: `/sqlreview/api/v1/realtime/oboracle/${params.id}`,
    method: 'get',
    params,
    getCancel: (c) => {
      cancel(c);
    }
  });
}

// 查询OBOracle全部Server
export function getOboracleServer(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/oboracle/${params.id}/server`,
    method: 'get'
  });
}

// 查询OBOracle全部租户
export function getOboracleTenant(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/oboracle/${params.id}/tenant`,
    method: 'get'
  });
}

// 查询OBOracle的执行计划
export function getOboracleExplain(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/oboracle/${params.id}/explain`,
    method: 'post',
    data: params
  });
}

// 强制停止OB Oracle Process进程
export function oboracleRealtimeKill(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/oboracle/${params.data_source_id}/${params.id}`,
    method: 'delete'
  });
}

// OB Oracle 数据库审核配置 测试链接
export function realTimeCheckAuth(params) {
  return Http({
    url: `/sqlreview/after_audit/real_time_check_auth`,
    method: 'post',
    data: params
  });
}

// OB Oracle 数据库审核配置 保存
export function realTimeConfigSave(params) {
  return Http({
    url: `/sqlreview/after_audit/ob_config_save`,
    method: 'post',
    data: params
  });
}

// ORACLE
// 获取OracleRealTime SQL数据
export function getOracleRealTimeInfo(id, sqlId = null, insId, params) {
  let url = `/sqlreview/api/v1/realtime/oracle/${id}`;
  if (sqlId && !insId) {
    url = url + `?sql_id=${sqlId}`;
  }
  if (insId && !sqlId) {
    url = url + `?ins_id=${insId}`;
  }
  if (insId && sqlId) {
    url = url + `?sql_id=${sqlId}&ins_id=${insId}`;
  }
  return Http({
    url: url,
    method: 'post',
    data: params
  });
}

// 获取oracle数据库ins节点信息
export function getInstanceInfo(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/oracle/instance/${params.id}`,
    method: 'get',
    params: params
  });
}

// 查询Oracle实时审核SQL模板
export function getOracleProcess(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/oracle/process`,
    method: 'get',
    params: params
  });
}

// DB2
// 获取DB2RealTime SQL数据
export function getDB2RealTimeInfo(id, sqlId = null, insId, params) {
  let url = `/sqlreview/api/v1/realtime/db2/${id}`;
  if (sqlId && !insId) {
    url = url + `?sql_id=${sqlId}`;
  }
  if (insId && !sqlId) {
    url = url + `?ins_id=${insId}`;
  }
  if (insId && sqlId) {
    url = url + `?sql_id=${sqlId}&ins_id=${insId}`;
  }
  return Http({
    url: url,
    method: 'post',
    data: params
  });
}

// 查询DB2实时审核SQL模板
export function getDB2Process(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/db2/process`,
    method: 'get',
    params: params
  });
}

// GaussDB
// 获取GaussDB RealTime SQL数据
export function getGaussdbRealTimeInfo(id, sqlId = null, insId, params) {
  let url = `/sqlreview/api/v1/realtime/gaussdb/${id}`;
  if (sqlId && !insId) {
    url = url + `?sql_id=${sqlId}`;
  }
  if (insId && !sqlId) {
    url = url + `?ins_id=${insId}`;
  }
  if (insId && sqlId) {
    url = url + `?sql_id=${sqlId}&ins_id=${insId}`;
  }
  return Http({
    url: url,
    method: 'post',
    data: params
  });
}

// 查询GaussDB实时审核SQL模板
export function getGaussdbProcess(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/gaussdb/process`,
    method: 'get',
    params: params
  });
}

// PGSQL
// 获取postgresqlRealTime SQL数据
export function getPostgresqlRealTimInfo(id, sqlId = null, params) {
  let url = `/sqlreview/api/v1/realtime/postgresql/${id}`;
  if (sqlId) {
    url = url + `?sql_id=${sqlId}`;
  }
  return Http({
    url: url,
    method: 'post',
    data: params
  });
}

// 查询Postgresql实时审核SQL模板
export function getPostgresqlProcess(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/postgresql/process`,
    method: 'get',
    params: params
  });
}

// Postgresql模板维护保存
export function savePostgresqlProcess(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/postgresql/process`,
    method: 'post',
    data: params
  });
}

// Goldendb 查询实时SQL任务的运行情况
export function getGoldendbRealTimeInfo(params, cancel) {
  let url = `/sqlreview/api/v1/realtime/goldendb/${params.id}`;
  const paramsArr = [
    { key: 'user', value: params.user },
    { key: 'host', value: params.host },
    { key: 'info', value: params.info },
    { key: 'db', value: params.db },
    { key: 'no_sleep', value: params.no_sleep }
  ].filter(item => item.value !== undefined && item.value !== '');
  if (paramsArr.length > 0) {
    let arr = []
    paramsArr.forEach(item => {
      const res = `${item.key}=${item.value}`
      arr.push(res)
    })
    url = url + '?' + arr.join('&')
  }
  return Http({
    url,
    method: 'post',
    data: params,
    getCancel: (c) => {
      cancel(c);
    }
  });
}

// kill Goldendb
export function goldendbRealtimeKill(params) {
  return Http({
    url: `/sqlreview/api/v1/realtime/goldendb/${params.data_source_id}/${params.id}`,
    method: 'delete'
  });
}

// 获取kill模板
export function getRealtimeKillTabs(params) {
  return Http({
    url: `/sqlreview/real_time/kill/tabs`,
    method: 'get',
    params: params
  });
}

// RealTime 新增kill模板
export function addRealtimeKillTab(params) {
  return Http({
    url: `/sqlreview/real_time/kill/tabs`,
    method: 'post',
    data: params
  });
}

// RealTime 修改kill模板
export function editRealtimeKillTab(id, params) {
  return Http({
    url: `/sqlreview/real_time/kill/tabs/${id}`,
    method: 'put',
    data: params
  });
}

// RealTime 删除kill模板
export function deleteRealtimeKillTab(id) {
  return Http({
    url: `/sqlreview/real_time/kill/tabs/${id}`,
    method: 'delete'
  });
}

// RealTime批量Kill会话
export function realtimeKillExecute(id, dbType, params) {
  return Http({
    url: `/sqlreview/real_time/${dbType}/${id}/batch`,
    method: 'delete',
    data: params
  });
}

// RealTime 获取批量Kill process的语句
export function realtimeKillGenerate(id, dbType, params) {
  let url = ''
  url = dbType == 'postgre' ? `/sqlreview/real_time/${dbType}/get/batch/kill/sql` : `/sqlreview/real_time/${dbType}/${id}/get/batch/kill/sql`;
  return Http({
    url: url,
    method: 'post',
    data: params
  });
}

export default {};
