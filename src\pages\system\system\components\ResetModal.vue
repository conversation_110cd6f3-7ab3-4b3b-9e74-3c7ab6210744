<template>
  <!-- 系统配置弹窗 -->
  <a-modal
    v-model="visible"
    title="系统配置"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <div class="title">{{ data.item_desc }}</div>
      <!-- <a-alert :message="data.config_desc" type="info"> </a-alert> -->
      <div class="info">
        <pre
          v-if="data.item_key == 'kill_realtime_sql'"
          style="margin-bottom: 0"
          >{{ data.config_desc }}</pre
        >
        <span v-else>{{ data.config_desc }}</span>
        <span v-if="data.special_desc" class="special-desc">{{
          data.special_desc
        }}</span>
      </div>
      <component
        v-model="value"
        :is="realType"
        :data="showData"
        :ref="'system-config-' + data.item_key"
      ></component>
    </a-spin>
    <div slot="footer">
      <a-button @click="onCancel">关闭</a-button>
      <a-button @click="testEmail" v-if="realType == 'emailSystem'"
        >邮件测试</a-button
      >
      <a-button type="primary" @click="onOk">确定</a-button>
    </div>
  </a-modal>
</template>

<script>
import { sendEmail } from '@/api/config/system';
import ConfigSystem from '@/components/Biz/ConfigSystem';
import FileSuffix from './FileSuffix';
import InputSystem from './InputSystem';
import SelectSystem from './SelectSystem';
import InputSystemCmdb from './InputSystemCmdb';
import CheckboxSystem from './CheckboxSystem';
import TableEditSystem from './TableEditSystem';
import EmailSystem from './EmailSystem';
export default {
  components: {
    ConfigSystem,
    InputSystem,
    InputSystemCmdb,
    TableEditSystem,
    SelectSystem,
    CheckboxSystem,
    EmailSystem,
    FileSuffix
  },
  props: {},
  data() {
    return {
      realType: '',
      showData: {},
      visible: false,
      data: { sql_review_dynamic_form: {} },
      value: '',
      spinning: false
    };
  },
  computed: {},
  created() {},
  methods: {
    show(data) {
      this.data = _.cloneDeep(data);
      this.value = this.data.item_value;
      if (!this.data.union_form) {
        this.showData = this.data;
        this.realType = 'InputSystemCmdb';
      } else {
        this.showData = this.data.sql_review_dynamic_form;
        let type = this.data.sql_review_dynamic_form.form_type;
        if (type === 'radio') {
          this.realType = 'ConfigSystem';
        } else if (type === 'checkbox') {
          this.realType = 'CheckboxSystem';
        } else if (type === 'input' || type === 'inputNumber') {
          this.realType = 'InputSystem';
        } else if (type === 'select') {
          this.realType = 'SelectSystem';
        } else if (type === 'tableEdit') {
          this.realType = 'TableEditSystem';
        } else if (type === 'email') {
          this.realType = 'emailSystem';
        } else if (type === 'fileSuffix') {
          this.realType = 'FileSuffix';
        }
      }
      this.visible = true;
    },
    onCancel() {
      const dynamicForm = this.data.sql_review_dynamic_form;
      const _ref = this.$refs['system-config-' + this.data.item_key];
      if (dynamicForm.form_type === 'input') {
        _ref.inputValue = dynamicForm.sql_review_dynamic_form_element;
      }
      this.visible = false;
      _ref.reset && _ref.reset();
    },
    onOk() {
      // dynamicForm.form_type === 'select'  form_element_id传数组，其他都传字符串，多个用 ','隔开
      let value = [];
      let formElementIdList =
        this.$refs['system-config-' + this.data.item_key].id || [];
      const dynamicForm = this.data.sql_review_dynamic_form;
      if (
        dynamicForm.form_type === 'input' ||
        dynamicForm.form_type === 'inputNumber'
      ) {
        const _ref = this.$refs['system-config-' + this.data.item_key];
        value = _ref.inputValue.map(item => item.element_name);
      }
      if (dynamicForm.form_type === 'tableEdit') {
        const data =
          this.$refs['system-config-' + this.data.item_key].getData() || {};
        value = data.sql_review_dynamic_form_element.map(
          item => item.element_name
        );
        formElementIdList = data.sql_review_dynamic_form_element
          .map(item => {
            if (
              typeof item.id === 'string' &&
              (item.id.startsWith('add') || item.id.startsWith('init'))
            ) {
              item.id = ' ';
            }
            return item.id;
          })
          .join();
      }
      if (dynamicForm.form_type === 'select') {
        const targetRef = this.$refs['system-config-' + this.data.item_key];
        const data = targetRef.getData();
        value = [
          { name: 'MYSQL', rule_set_id: data.MYSQL },
          { name: 'POSTGRE', rule_set_id: data.POSTGRE },
          { name: 'TIDB', rule_set_id: data.TIDB },
          { name: 'ORACLE', rule_set_id: data.ORACLE }
        ];
      }
      if (dynamicForm.form_type === 'checkbox') {
        const targetRef = this.$refs['system-config-' + this.data.item_key];
        const data = targetRef.getData();
        value = data;
        if (formElementIdList.length <= 0) {
          this.$message.warning('请选择至少一种审核的数据库语句类型');
          return;
        }
      }
      if (dynamicForm.form_type === 'email') {
        const targetRef = this.$refs['system-config-' + this.data.item_key];
        const data = targetRef.getData();
        if (!data.validate) return;
        value = data;
      }
      if (dynamicForm.form_type === 'fileSuffix') {
        const targetRef = this.$refs['system-config-' + this.data.item_key];
        const data = targetRef.getData();
        value = data.file_suffix.join();
      }
      if (!this.data.union_form) {
        value = this.$refs['system-config-' + this.data.item_key].inputValue;
      }
      this.$emit('save', {
        item_value: dynamicForm.form_type === 'radio' ? this.value : value,
        form_element_id: formElementIdList,
        id: this.data.id
      });
    },
    testEmail() {
      const dynamicForm = this.data.sql_review_dynamic_form;
      if (dynamicForm.form_type === 'email') {
        const targetRef = this.$refs['system-config-' + this.data.item_key];
        const data = targetRef.getData();
        if (!data.validate) return;
        this.spinning = true;
        sendEmail(data)
          .then(res => {
            if (CommonUtil.isSuccessCode(res)) {
              this.spinning = false;
              this.$hideLoading({ tips: _.get(res, 'data.message') });
            } else {
              this.spinning = false;
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
  // padding: 0 16px;
}
.info {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  padding: 4px 16px;
  color: rgba(0, 0, 0, 0.85);
  border-radius: 4px;
  .special-desc {
    color: #f81d22;
  }
}
/deep/ .config-system {
  padding: 0 15px;
}
/deep/ .ant-alert.ant-alert-no-icon {
  padding: 4px 15px;
}
</style>
