export default {
  namespaced: true,
  state: {
    source: null,
    map: {},
    menus: [],
    pages: [],
    elements: [],
    // 元素
    permissions: null,
    eleMap: {}
  },
  mutations: {
    setAuth(state, params) {
      const { resource, permissions } = params || {};
      // 处理resource
      state.source = resource;
      let map = {};
      let menus = [];
      let pages = [];
      let elements = [];
      (resource || []).forEach(item => {
        const key = item.id;
        // 设置map
        map[key] = item;
        if (key.startsWith('$menu')) {
          menus.push(item);
        } else if (key.startsWith('$page')) {
          pages.push(item);
        } else {
          elements.push(item);
        }
      });
      state.map = map;
      state.menus = menus;
      state.pages = pages;
      state.elements = elements;

      // 处理元素permission
      state.permissions = permissions;
      state.eleMap = {};
      function loop(list = []) {
        list.forEach(item => {
          if (item.resourceType == 3) {
            state.eleMap[item.resourceCode] = item;
          }
          if (item.children) {
            loop(item.children);
          }
        });
      }
      loop(permissions);
    }
  }
};
