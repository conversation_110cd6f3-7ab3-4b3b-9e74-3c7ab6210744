<template>
  <a-result :status="status" :title="title" :sub-title="subTitle"></a-result>
</template>

<script>
const config = {
  403: {
    title: '403',
    desc: '抱歉，你无权访问该页面'
  },
  404: {
    title: '404',
    desc: '抱歉，你访问的页面不存'
  },
  500: {
    title: '500',
    desc: '抱歉，服务器出错了'
  }
};
export default {
  // components: { ExceptionPage },
  data() {
    // console.log(this, 99);
    const status = this.$route.params.status || '404';
    const item = config[status] || {};
    return {
      status: status + '',
      title: item.title || '',
      subTitle: item.desc || ''
    };
  },
  created() {
    // console.log(this.changeStatus)
  },
  methods: {
    changeStatus(status) {
      if (status) {
        const item = config[status] || {};
        this.status = status + '';
        this.title = item.title || '';
        this.subTitle = item.desc || '';
      }
    }
  },
  watch: {
    $route(to, from) {
      // console.log(to, from, 8888);
      this.changeStatus(to.params.status);
    }
  }
};
</script>

<style scoped>
</style>
