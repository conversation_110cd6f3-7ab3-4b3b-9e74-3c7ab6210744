<template>
  <div class="psc-left-source-content">
    <a-tabs type="card" v-model="activeKey" @change="onChange">
      <a-tab-pane key="database" tab="数据库">
        <Database ref="database" />
      </a-tab-pane>
      <a-tab-pane key="query" tab="查询">
        <Query />
      </a-tab-pane>
      <a-tab-pane key="history" tab="历史">
        <History />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import Database from './Database';
import Query from './Query';
import History from './History';

export default {
  components: { Database, Query, History },
  data() {
    return {
      activeKey: 'database'
    };
  },
  computed: {},
  mounted() {},
  methods: {
    onChange(key) {
      if (key === 'database') {
        const ref = _.get(this.$refs, 'database');
        if (ref) {
          ref.updateTree();
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.psc-left-source-content {
  padding: 12px;
  height: 100%;

  // 左侧滚动条hover才显示
  ::-webkit-scrollbar-thumb {
    background: #ffffff;
  }
  ::-webkit-scrollbar-track {
    background: #ffffff;
  }

  :hover {
    ::-webkit-scrollbar-thumb {
      background: #d2d2d2;
    }
    ::-webkit-scrollbar-track {
      // background: #f2f2f2;
    }
  }

  /deep/ .ant-tabs {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: 100%;

    > .ant-tabs-bar {
      > .ant-tabs-nav-container {
        height: 32px;

        .ant-tabs-tab {
          height: 32px;
          line-height: 30px;
          border-radius: 2px 2px 0 0;
          font-size: 13px;
        }
      }
    }

    > .ant-tabs-content {
      display: flex;
      flex-grow: 1;
      > .ant-tabs-tabpane {
        &.ant-tabs-tabpane-active {
          flex-grow: 1;
        }
        &.ant-tabs-tabpane-inactive {
          // width: 0;
          display: none;
        }
      }
    }
  }
}
</style>
