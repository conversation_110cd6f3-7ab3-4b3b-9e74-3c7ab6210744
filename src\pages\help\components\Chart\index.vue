<template>
  <div :style="{ height: '300px', position: 'relative' }">
    <Chart :option="option" />
  </div>
</template>

<script>
import Chart from '@/components/Chart';

export default {
  components: { Chart },
  props: {},
  data() {
    return {
      option: {
        title: {
          text: 'SQL总数',
          textStyle: {
            color: '#b1b1b1',
            fontSize: 14,
            fontWeight: 400
          },
          subtext: `${200}条`,
          subtextStyle: {
            color: '#000000',
            fontSize: 24,
            fontWeight: 600
          },
          left: 'center',
          top: '40%'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          show: false
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: ['40%', '70%'],
            data: [
              { value: 1048, name: '搜索引擎' },
              { value: 735, name: '直接访问' },
              { value: 580, name: '邮件营销' },
              { value: 484, name: '联盟广告' },
              { value: 300, name: '视频广告' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    };
  },
  mounted() {},
  created() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
</style>