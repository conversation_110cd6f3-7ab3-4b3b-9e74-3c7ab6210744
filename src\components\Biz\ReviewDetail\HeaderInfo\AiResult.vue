<template>
  <a-card type="small" :bordered="false" class="small-card">
    <div class="ai-comment-part">
      <div class="ai-result">
        <span class="title">AI判定结果</span>
        <div
          class="result"
          :style="{ background: backgroundColor[dataInfo.risk] }"
        >
          <div class="result-icon">
            <a-icon
              :style="{ color: statusColor[dataInfo.risk] }"
              :type="statusType[dataInfo.risk] || 'file-done'"
            />
          </div>
          <div class="result-text">
            <div class="no-pass" :style="{ color: statusColor[dataInfo.risk] }">
              {{ dataInfo.risk | riskStatus }}
            </div>
          </div>
        </div>
      </div>
      <div class="part">
        <div class="part-level">
          <div
            v-for="(item, index) in dataInfo.ai_comment"
            :key="index"
            class="level"
          >
            <span>
              <!-- rule_result 0 高风险 1 低风险 -->
              <custom-icon
                class="ccg-icon high"
                type="lu-icon-alarm"
                v-if="item.rule_result == 0"
              />
              <custom-icon
                class="ccg-icon low"
                type="lu-icon-alarm"
                v-if="item.rule_result == 1"
              />
              <custom-icon
                v-if="item.all_wrong == 1"
                class="ccg-icon error"
                type="lu-icon-unusual"
              />
            </span>
            <LimitLabel
              :label="item.ai_comment"
              mode="ellipsis"
              :block="true"
              class="text"
            ></LimitLabel>
          </div>
          <div v-if="dataInfo.ai_comment.length <= 0" class="part-no">
            <custom-empty />
          </div>
        </div>
        <!-- <div v-if="sqlErrorMessage && sqlErrorMessage.length > 0">
          <Table v-bind="tableParams" :dataSource="sqlErrorMessage">
            <LimitLabel
              slot="schema"
              slot-scope="{ text }"
              :label="text || ''"
              mode="ellipsis"
              :block="true"
            ></LimitLabel>
            <div slot="error_message" slot-scope="{ text }">
              <LimitLabel
                :label="text || ''"
                mode="ellipsis"
                :block="true"
              ></LimitLabel>
            </div>
          </Table>
        </div> -->
      </div>
    </div>
  </a-card>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
import RichEditorViewer from '@/components/RichEditor/viewer';
import Table from '@/components/Table';
import config from './config';
export default {
  components: { Table, LimitLabel, RichEditorViewer },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
    // sqlErrorMessage: {
    //   type: Array,
    //   default: () => []
    // }
  },
  computed: {
    // statusType() {
    //   if (this.aiStatus === 1 || this.aiStatus === 2) {
    //     return 'file-done';
    //   } else if (this.aiStatus === -1 || this.aiStatus === 9) {
    //     return 'exception';
    //   } else {
    //     return 'file-unknown';
    //   }
    // }
  },
  data() {
    this.config = config(this);
    return {
      statusColor: this.config.statusColor,
      tableParams: {
        url: '',
        reqParams: {},
        columns: this.config.columns,
        showHeader: false,
        size: 'small',
        rowKey: 'id',
        pagination: false
      },
      backgroundColor: {
        null: 'rgba(58, 151, 76, 0.2)',
        low: 'rgba(242,147,57, 0.2)',
        high: 'rgba(231, 29, 54, 0.05)',
        error: 'rgba(0, 59, 114, 0.06)'
      },
      statusType: {
        null: 'file-done',
        low: 'file-done',
        high: 'file-unknown',
        error: 'exception'
      }
    };
  },
  methods: {},
  watch: {},
  filters: {
    riskStatus(value) {
      let obj = {
        null: '无风险',
        low: '低风险',
        high: '高风险',
        error: '异常'
      };
      return obj[value];
    }
  }
};
</script>

<style lang="less" scoped>
.small-card {
  border-radius: 8px;
  width: 34%;
  /deep/.ant-card-body {
    padding: 24px 16px 16px 16px;
    .ai-comment-part {
      display: flex;
      .ai-result {
        display: flex;
        flex-direction: column;
        align-items: center;
        .title {
          text-align: center;
          font-family: PingFangSC-Regular;
          font-size: 16px;
          color: #27272a;
          margin: 0 0 16px 0;
        }
        .result {
          width: 180px;
          height: 140px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border-radius: 8px;
          .result-icon {
            width: 100%;
            text-align: center;
          }
          .anticon-file-unknown {
            font-size: 36px;
          }
          .anticon-file-done {
            font-size: 36px;
          }
          .anticon-exception {
            font-size: 36px;
          }
          .result-text {
            width: 100%;
            text-align: center;
          }
          .no-pass {
            font-size: 20px;
            color: #e71d36;
            font-weight: 500;
            margin: 6px 0 2px 0;
          }
        }
      }

      .part {
        position: relative;
        width: 90%;
        height: 100%;
        text-align: left;
        padding: 0 0 0 16px;
        overflow-y: auto;
        // .error-message {
        //   max-width: 400px;
        //   &.has-splan-info {
        //     max-width: 450px;
        //   }
        // }
        .part-level {
          .level {
            width: 100%;
            padding: 8px 2px;
            margin: 2px 0;
            background-color: transparent;
            color: #0f78fb;
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            .level0 {
              color: #b0aeae;
            }
            .level1 {
              color: #ff4d4f;
            }
            .level2 {
              color: #ff9358;
            }
            .level3 {
              color: #1edfa9;
            }
            .level9 {
              color: #52c41a;
            }
            .ccg-icon {
              &.high {
                color: #e71d36;
                margin-right: 4px;
              }
              &.low {
                color: #f29339;
                margin-right: 4px;
              }
              &.error {
                color: #71717a;
                margin-right: 4px;
              }
            }
            .ccg-num {
              text-align: right;
              font-size: 14px;
              white-space: nowrap;
            }
            .text {
              color: #595959;
              > pre {
                font-family: PingFangSC-Regular;
                color: #595959;
                font-size: 14px;
              }
            }
          }
        }
        .part-no {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
}
</style>