
export default function (ctx) {
  const columns = [
    {
      dataIndex: 'name',
      key: 'name',
      shower: {
        title: 'Label组件',
        type: 'Label',
        showerProps: {
          defaultValue: '默认值'
        }
      },
      scopedSlots: { customRender: 'Shower' },
      sorter: true
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      shower: {
        title: '年龄'
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: 'Address',
      dataIndex: 'addressName',
      key: 'address',
      shower: (record) => {
        return {
          title: 'LimitLabel组件',
          type: 'LimitLabel',
          showerProps: {
            limit: 10,
            label: record.addressName
          }
        }
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: 'Tags',
      key: 'tags',
      dataIndex: 'tags',
      shower: (record) => {
        const color = ['green', 'blue']
        const tagsList = (record.tags || []).map((item, index) => ({label: item, color: color[index]}));
        return {
          title: 'LimitTags组件',
          type: 'LimitTags',
          showerProps: {
            limit: 4,
            tags: tagsList
          }
        }
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: 'CreatedAt',
      dataIndex: 'created_at',
      key: 'created_at',
      shower: (record) => {
        return {
          title: 'DateFormat组件',
          type: 'DateFormat',
          showerProps: {
            text: record.created_at
          }
        }
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      shower: (record) => {
        return {
          title: 'Status组件',
          type: 'Status',
          showerProps: {
            text: record.status,
            status: record.status == 1 ? 'success' : 'error',
            type: 'badge'
          }
        }
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: 'Action',
      key: 'action',
      width: 130,
      scopedSlots: { customRender: 'action' }
    }
  ];

  /**
   * 固定列
   */
  const fixedColumns = [
    {
      dataIndex: 'name',
      key: 'name',
      shower: {
        title: 'Label组件',
        type: 'Label',
        showerProps: {
          defaultValue: '默认值'
        }
      },
      scopedSlots: { customRender: 'Shower' },
      sorter: true,
      width: 100
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      width: 100
    },
    {
      title: 'Address',
      dataIndex: 'addressName',
      key: 'address',
      shower: (record) => {
        return {
          title: 'LimitLabel组件',
          type: 'LimitLabel',
          showerProps: {
            limit: 10,
            label: record.addressName
          }
        }
      },
      scopedSlots: { customRender: 'Shower' },
      width: 200
    },
    {
      title: 'Tags',
      key: 'tags',
      dataIndex: 'tags',
      shower: (record) => {
        const color = ['green', 'blue']
        const tagsList = (record.tags || []).map((item, index) => ({label: item, color: color[index]}));
        return {
          title: 'LimitTags组件',
          type: 'LimitTags',
          showerProps: {
            limit: 4,
            tags: tagsList
          }
        }
      },
      scopedSlots: { customRender: 'Shower' },
      width: 200
    },
    {
      title: 'CreatedAt',
      dataIndex: 'created_at',
      key: 'created_at',
      shower: (record) => {
        return {
          title: 'DateFormat组件',
          type: 'DateFormat',
          showerProps: {
            text: record.created_at
          }
        }
      },
      scopedSlots: { customRender: 'Shower' },
      width: 200
    },
    {
      title: 'Action',
      key: 'action',
      width: 150,
      scopedSlots: { customRender: 'action' },
      fixed: 'right'
    }
  ];
  /**
   * 固定某几列宽度，其余宽度自适应
   */
   const fixedWidthColumns = [
    {
      title: 'Label组件',
      dataIndex: 'name',
      key: 'name',
      shower: {
        type: 'Label',
        showerProps: {
          defaultValue: '默认值'
        }
      },
      scopedSlots: { customRender: 'Shower' },
      width: 110
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      width: 60
    },
    {
      title: 'LimitLabel组件',
      dataIndex: 'addressName',
      key: 'address',
      shower: (record) => {
        return {
          type: 'LimitLabel',
          showerProps: {
            // limit: 10,
            label: record.addressName
          }
        }
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: 'LimitTags组件',
      key: 'tags',
      dataIndex: 'tags',
      shower: (record) => {
        const color = ['green', 'blue']
        const tagsList = (record.tags || []).map((item, index) => ({label: item, color: color[index]}));
        return {
          type: 'LimitTags',
          showerProps: {
            limit: 4,
            tags: tagsList
          }
        }
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: 'DateFormat组件',
      dataIndex: 'created_at',
      key: 'created_at',
      shower: (record) => {
        return {
          type: 'DateFormat',
          showerProps: {
            text: record.created_at
          }
        }
      },
      scopedSlots: { customRender: 'Shower' },
      width: 140
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      scopedSlots: { customRender: 'action' },
      fixed: 'right'
    }
  ];
  const fixedWidthColumns1 = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      shower: {
        type: 'Label',
        showerProps: {
          defaultValue: '张三'
        }
      },
      scopedSlots: { customRender: 'Shower' },
      width: 80
    },
    // {
    //   title: 'Age',
    //   dataIndex: 'age',
    //   key: 'age',
    //   width: 60
    // },
    {
      title: '地址',
      dataIndex: 'addressName',
      key: 'address',
      shower: (record) => {
        return {
          type: 'LimitLabel',
          showerProps: {
            // limit: 10,
            label: record.addressName
          }
        }
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      scopedSlots: { customRender: 'action' },
      fixed: 'right'
    }
  ];
  return {
    columns,
    fixedColumns,
    fixedWidthColumns,
    fixedWidthColumns1
  };
};
