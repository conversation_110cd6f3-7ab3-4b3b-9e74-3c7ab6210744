<template>
  <div class="review-header">
    <ReviewHeader
      @addSqlTag="addSqlTag"
      @refresh="refresh"
      :headerInfo="headerInfo"
      :isWhite="isWhite"
      :sqlLabelStatus="sqlLabelStatus"
      :dataSourceList="dataSourceList"
    ></ReviewHeader>

    <!-- AI判定结果 -->
    <AiResult
      class="ai-result"
      :dataInfo="dataInfo"
      :pieOption="pieOption"
      :sqlPlanInfo="sqlPlanInfo"
      :sqlErrorMessage="sqlErrorMessage"
    ></AiResult>

    <!-- DBA、索引、优化建议 -->
    <SqlSuggest
      v-if="
        sqlPlanInfo.length > 0 ||
        (dbaComment.length > 0 && isProject) ||
        sqlSuggest.length > 0 ||
        (isOrder && isDba)
      "
      class="splan-info"
      @saveAdvice="saveAdvice"
      :sqlSuggest="sqlSuggest"
      :dbaComment="dbaComment"
      :sqlPlanInfo="sqlPlanInfo"
      :id="id"
      :isOrder="isOrder"
    ></SqlSuggest>
  </div>
</template>

<script>
import AiResult from './AiResult';
import ReviewHeader from './ReviewHeader';
import SqlSuggest from './SqlSuggest';
import LimitLabel from '@/components/LimitLabel';
import Status from '@/components/Biz/Status';
import DbImg from '@/components/CustomImg/DbImg';
import LabelCard from '@/components/Biz/LabelCard';
export default {
  components: {
    LimitLabel,
    Status,
    DbImg,
    LabelCard,
    ReviewHeader,
    AiResult,
    SqlSuggest
  },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    isWhite: {
      type: Boolean,
      default: true
    },
    sqlLabelStatus: Number | String,
    dataSourceList: {
      type: Array,
      default: () => []
    },
    dataInfo: {
      type: Object,
      default: () => {}
    },
    pieOption: {
      type: Object,
      default: () => {}
    },
    sqlErrorMessage: {
      type: Array,
      default: () => []
    },
    sqlPlanInfo: {
      type: Array,
      default: () => []
    },
    sqlSuggest: {
      type: Array,
      default: () => []
    },
    dbaComment: {
      type: Array,
      default: () => []
    },
    commentStatus: String | Number,
    id: String | Number,
    isOrder: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isDba() {
      const user = this.$store.state.account.user || {};
      return user.role === 'dba';
    },
    isProjectRview() {
      const name = this.$route.name;
      return name == 'project-review-review';
    },
    isOrderRview() {
      const name = this.$route.name;
      return name == 'orderReview';
    },
    isFileReview() {
      const name = this.$route.name;
      return name == 'file-review-review';
    },
    dataSourceObj() {
      return this.dataSourceList.length > 0 ? this.dataSourceList[0] : null;
    },
    isProject() {
      const name = this.$route.name;
      return name == 'project-review-review';
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    // 打标
    addSqlTag() {
      this.$emit('addSqlTag');
    },
    saveAdvice(data) {
      this.$emit('saveAdvice', data);
    },
    refresh() {
      this.$emit('refresh', { id: this.headerInfo.label_obj_id });
    }
  },
  watch: {}
};
</script>
<style lang="less">
.ant-popover {
  &.small-size {
    .ant-popover-content {
      .ant-popover-inner {
        .ant-popover-inner-content {
          max-width: 400px;
          max-height: 300px;
          > pre {
            font-size: 14px;
            color: #71717a;
            font-weight: 400;
          }
        }
      }
    }
  }
}
</style>
<style lang="less" scoped>
.review-header {
  margin-bottom: 24px;
  display: flex;
  min-height: 260px;
}
/deep/.ant-btn {
  > .anticon + span,
  > span + .anticon {
    margin-left: 2px !important;
  }
}
</style>