<template>
  <div class="biz-page-detail">
    <a-card style="width:100%" :title="title" :bordered="false">
      <!-- 右上角按钮 -->
      <slot name="btns"></slot>
      <!-- title extra -->
      <div class="bpd-title-extra" slot="extra">
        <slot name="titleExtra"></slot>
      </div>
      <a-divider style="margin:0"></a-divider>
      <!-- 中间区域 -->
      <Blocks :value="blocks" v-if="blocks.length > 0">
        <template v-for="item in blocks" v-slot:[item.key]>
          <slot :name="item.key"></slot>
        </template>
      </Blocks>
      <slot name="middle" v-else></slot>
      <!-- 底部区域 -->
      <slot name="bottom"></slot>
    </a-card>
  </div>
</template>

<script>
// import _ from 'lodash';
import Blocks from '@/components/Blocks';

export default {
  props: {
    title: String,
    blocks: {
      type: Array,
      default: () => []
    }
  },
  components: { Blocks },
  data() {
    return {};
  },
  computed: {},
  mounted() {},
  methods: {}
};
</script>

<style lang="less" scoped>
@hpadding: 40px;
.biz-page-detail {
  /deep/ .ant-card {
    > .ant-card-head {
      font-size: 18px;
      border-bottom: none;
      padding: 8px @hpadding;

      &::before {
        display: block;
        content: '';
        width: 8px;
        height: 16px;
        position: absolute;
        top: 0;
        left: 16px;
        border-radius: 0 0 4px 4px;
        background: @primary-color;
      }

      .ant-card-head-title {
        flex: none;
      }
    }
    > .ant-card-body {
      padding: 0;
    }
  }
}
</style>
