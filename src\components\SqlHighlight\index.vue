<!--
 * @Descripttion: 展示样式
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2021-01-25 15:04:35
-->

<template>
  <div :class="['sql-format', fullscreen && 'fullscreen']">
    <p v-if="title">{{ title }}</p>
    <pre v-html="sqlHtml"></pre>
    <div class="fullscreen-tools">
      <a-icon
        type="copy"
        @click="onCopy"
        v-if="needCopy"
        style="margin-right: 4px"
      ></a-icon>
      <a-icon
        :type="fullscreen ? 'fullscreen-exit' : 'fullscreen'"
        @click="toggleFullscreen"
      ></a-icon>
    </div>
  </div>
</template>

<script>
// import { format } from 'sql-formatter';
import hljs from 'highlight.js';
// import 'highlight.js/styles/vs.css';

export default {
  props: {
    dbType: String,
    title: String,
    sql: {
      type: String
    },
    needCopy: {
      type: Boolean,
      default: false
    },
    format: {
      type: <PERSON>olean,
      default: true
    }
  },
  data(vm) {
    let formatSql = this.getSqlText();
    return {
      sqlHtml: hljs.highlight('sql', formatSql).value,
      fullscreen: false
    };
  },
  mounted() {
    this.parentNode = this.$el.parentNode;
    this.elIndex = _.findIndex(
      this.parentNode.childNodes,
      item => item === this.$el
    );
  },
  methods: {
    toggleFullscreen() {
      this.fullscreen = !this.fullscreen;
      this.$nextTick(() => {
        if (this.fullscreen) {
          document.body.appendChild(this.$el);
        } else {
          const childNodes = this.parentNode.childNodes;
          const index = this.elIndex;
          if (index === -1 || index == childNodes.length) {
            this.parentNode.appendChild(this.$el);
          } else {
            this.parentNode.insertBefore(this.$el, childNodes[index]);
          }
        }
      });
    },
    getSqlFormatOptions() {
      let defaultOptions = {};
      if (this.dbType) {
        defaultOptions = FORMAT_CONFIG.SqlFormat.getConfig(this.dbType);
      }
      return defaultOptions;
    },
    getSqlText(sql) {
      let source = sql || this.sql || '';
      let res = source;
      if (this.format) {
        try {
          res = sqlFormatter.format(source, this.getSqlFormatOptions());
        } catch (e) {
          res = source;
          console.log(e);
        }
      }
      return res;
    },
    onCopy() {
      CommonUtil.copy({
        value: this.sql,
        callback: () => {
          this.$message.success('复制成功');
        }
      });
    }
  },
  watch: {
    sql: function(value) {
      const formatSql = this.getSqlText(value);
      this.sqlHtml = hljs.highlight('sql', formatSql).value;
    }
  }
};
</script>

<style lang="less" scoped>
.sql-format {
  color: rgba(78, 80, 84, 1);
  background: rgba(15, 120, 251, 0.06);
  border-radius: 2px;
  margin-bottom: 12px;
  position: relative;
  // padding: 16px;
  p {
    color: #4e5054;
    margin-bottom: 0;
    opacity: 0.6;
    padding: 16px 16px 0 16px;
  }
  pre {
    margin-bottom: 0;
    padding: 16px;
  }
  /deep/ .sql-hl-keyword {
    color: #0f78fb;
  }
  /deep/ .sql-hl-function {
    color: #0f78fb;
  }
  /deep/ .sql-hl-number {
    color: #23be6c;
  }
  /deep/ .sql-hl-string {
    color: #23be6c;
  }
  /deep/.sql-hl-special {
    color: #ffb625;
  }
  /deep/ .sql-hl-bracket {
    color: #ffb625;
  }

  .fullscreen-tools {
    position: absolute;
    top: 0;
    right: 0;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.5);
    z-index: 10;
    display: none;
    .anticon {
      cursor: pointer;
      // line-height: 24px;
    }
  }

  &.fullscreen {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100% !important;
    height: 100% !important;
    z-index: 1051;
    margin: 0 !important;
    padding: 0 !important;
    background: #f4faff;

    pre {
      height: 100%;
    }
    .fullscreen-tools {
      display: inline-block;
    }
  }

  &:hover {
    .fullscreen-tools {
      display: inline-block;
    }
  }
}
</style>