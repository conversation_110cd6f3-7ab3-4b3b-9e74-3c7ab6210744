<template>
  <div class="page-dbacat-detail">
    <SearchArea
      :fields="searchFields"
      :colon="true"
      mode="inline"
      @search="search"
    />
    <div class="detail-content">
      <div class="total">
        <div><label>cpu: </label><span>32c</span></div>
        <div><label>内存: </label><span>64GB</span></div>
        <div><label>最大连接数: </label><span>1000</span></div>
        <div><label>健康度: </label><span>80</span></div>
        <div><label>资源配置: </label><span>均衡</span></div>
      </div>
      <div class="items">
        <div>严重告警明细</div>
        <Table v-bind="tableParams" />
      </div>
    </div>
    <Charts />
  </div>
</template>

<script>
// import {
//   getTask,
//   getReportIndex,
//   getReportVersion,
//   getFeedBack,
//   getReportCodeReview,
//   getReportTopSql,
//   getReportDatabaseAudit,
//   getDatabaseMonitorData
// } from '@/api/dataview';
// import config from './config';
import SearchArea from '@/components/SearchArea';
import Table from '@/components/Table';
import Charts from '../components/Charts';
import config from './config';

export default {
  components: { SearchArea, Table, Charts },
  data() {
    this.config = config(this);
    return {
      searchFields: this.config.searchFields,
      tableParams: {
        columns: [
          {
            title: '告警时间',
            dataIndex: 'instance_name',
            width: 80
          },
          {
            title: '指标',
            dataIndex: 'warn_num',
            width: 50
          },
          {
            title: '当前值',
            dataIndex: 'health',
            width: 60
          },
          {
            title: '告警规则',
            dataIndex: 'config',
            width: 50
          }
        ],
        ellipsis: true
      }
    };
  },
  created() {},
  mounted() {
    // this.init();
    console.log(this.config);
  },
  beforeDestroy() {},
  methods: {
    init() {},
    search(e){
      console.log(e);
    }
  }
};
</script>

<style lang="less" scoped>
.page-dbacat-detail {
  background: #ffffff;
  padding: 12px;

  /deep/ .search-area {
    border: 0;
    box-shadow: none;
    padding: 8px 0 0 0 !important;

    .ant-form-item-control-wrapper {
      overflow: hidden;
    }
    .ant-form {
      > .ant-row:not(:last-child) {
        .ant-form-item-control {
          width: 300px !important;
        }
      }
    }
  }

  .detail-content {
    > div {
      border: 1px dashed #dfdfdf;
      padding: 16px;
      position: relative;
      margin-bottom: 24px;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        display: block;
        width: 4px;
        height: 10px;
        background:@primary-color;
      }

      &.total {
        display: flex;
        flex-wrap: wrap;
        > div {
          width: 33.333333%;
          margin-bottom: 8px;
          font-size: 16px;
          font-weight: 500;
          > label {
            color: #a0a0a0;
            margin-right: 8px;
          }
        }
      }

      &.items {
        > div:first-child {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>
