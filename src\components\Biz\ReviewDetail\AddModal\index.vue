<template>
  <a-modal
    v-model="visible"
    :title="title"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
    okText="保存"
    width="600px"
    wrapClassName="home-white-list-add-modal"
  >
    <Form ref="form" v-bind="params" :formData="data"></Form>
    <Form
      class="permanent-day-form"
      ref="permanentDayForm"
      v-bind="formParams"
      :formData="formData"
    ></Form>
    <a-alert
      message="提示：加入表白名单后，如果SQL中只含白名单表的查询，且在时效期内，将自动判断为评审通过。否则将按触发规则判断。"
      type="info"
    />
  </a-modal>
</template>

<script>
// import format from 'date-fns/format';
// import dateFns from 'date-fns';
// import moment from 'moment';
import Form from '@/components/Form';
import config from './config';
export default {
  components: { Form },
  data() {
    this.config = config(this);
    return {
      form: this.$form.createForm(this, {}),
      visible: false,
      data: {},
      params: {
        layout: 'horizontal',
        gutter: 32,
        colon: true,
        fields: this.config.baseInfo
      },
      formData: {
        permanent_day: 0
      },
      formParams: {
        gutter: 32,
        colon: true,
        multiCols: 2,
        fields: [
          (formData = {}) => {
            return {
              type: 'RadioGroup',
              label: '有效期(天)',
              key: 'permanent_day',
              props: {
                mode: 'tips',
                class: 'inline',
                options: [
                  {
                    label: '永久',
                    value: 0
                  },
                  {
                    label: '自定义',
                    value: 1
                  }
                ]
              },
              listeners: {
                change: value => {
                  const { permanentDayForm } = this.$refs;
                  permanentDayForm.saving({
                    permanent_day: value,
                    custom_day: null
                  });
                }
              },
              rules: [
                { required: true, message: '该项为必填项', trigger: 'change' }
              ]
            };
          },
          (formData = {}) => {
            return {
              type: 'InputNumber',
              label: '',
              key: 'custom_day',
              props: {
                placeholder: '请输入',
                min: 1
              },
              visible: formData.permanent_day == 1,
              listeners: {
                change: value => {
                  const { permanentDayForm } = this.$refs;
                  permanentDayForm.saving({
                    custom_day: value
                  });
                }
              },
              rules: [
                { required: true, message: '该项为必填项', trigger: 'change' }
              ]
            };
          }
        ]
      },
      title: ''
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(record) {
      this.title = '添加白名单';
      this.visible = true;
      // let nowTime = new Date(Date.now());
      // let expire = format(dateFns.addDays(nowTime, 31), 'YYYY-MM-DD HH:mm:ss');
      // let takeEffect = format(nowTime, 'YYYY-MM-DD HH:mm:ss');
      this.data = Object.assign({}, this.data, {
        // white_list_classify: '表级别',
        // take_effect: moment(takeEffect),
        // expire: moment(expire)
      });
      if (record) {
        this.$set(this.data, 'data_source_id', record.data_source_id);
        this.$set(this.data, 'schema_id', record.schema_id);
        this.$set(
          this.data,
          'table_name',
          Array.isArray(record.table_name)
            ? [...record.table_name]
            : [record.table_name]
        );
      }
    },
    hide() {
      this.visible = false;
      this.$refs.form.resetFields();
      this.data = Object.assign({}, this.data, {
        table_name: []
      });
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form, permanentDayForm } = this.$refs;
      Promise.all([form.validate(), permanentDayForm.validate()]).then(
        valid => {
          if (valid) {
            let getData = form.getData() || {};
            let permanentDayData = permanentDayForm.getData();
            let data = {
              ...getData,
              permanent_day: permanentDayData.custom_day || 0
            };
            // getData.white_list_classify = '表级别';
            // getData.take_effect = getData.take_effect.format(
            //   'YYYY-MM-DD HH:mm:ss'
            // );
            // getData.expire = getData.expire.format('YYYY-MM-DD HH:mm:ss');
            this.$emit('save', data);
            this.hide();
          }
        }
      );
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ant-alert-info {
  border: none;
}
/deep/.ant-form {
  .ant-row {
    display: flex !important;
    align-items: center;
    .ant-col {
      width: auto;
      padding-right: 0 !important;
      padding-left: 0 !important;
      &.ant-form-item-label {
        width: 120px;
      }
      &.ant-form-item-control-wrapper {
        width: 360px;
      }
      .ant-form-item-control {
        .ant-form-item-children {
          .ant-select-selection {
            .ant-select-selection__rendered {
              .biz-instance-item {
                display: inline-block;
                width: 80%;
                .instance-item-tag {
                  background: transparent;
                  border: 0;
                  height: 30px;

                  &::after {
                    display: none;
                  }
                  .instance-item-tag-tag {
                    left: 0px;
                  }
                  .database-image {
                    margin-left: 26px;
                    > span > .custom-icon {
                      margin-right: 0;
                    }
                    > span > .iconText {
                      overflow: hidden;
                      max-width: 180px;
                      > pre {
                        font-size: 13px;
                        color: #71717a;
                        font-weight: 400;
                        white-space: nowrap;
                        font-family: 'PingFang SC', 'Microsoft YaHei';
                      }
                    }
                  }
                }

                &:hover {
                  .iconText {
                    color: @primary-color;
                    font-weight: 500;
                  }
                }
              }
              .biz-data-base-choose {
                > .ant-select-selection {
                  height: 30px;
                  border: none;
                  background: #f5f5f5;
                  .ant-select-selection__rendered {
                    margin: 0;
                    .ant-select-selection__placeholder {
                      margin-left: 11px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

/deep/ .permanent-day-form {
  margin-left: 16px;
  margin-bottom: 8px;
  .ant-row {
    display: flex;
    align-items: center;
    .ant-col {
      width: auto;
      padding-right: 0 !important;
      padding-left: 0 !important;
      &.ant-form-item-control-wrapper {
        width: auto;
      }
      .ant-form-item {
        display: flex;
        .ant-form-item-control-wrapper {
          .ant-radio-group {
            display: flex;
            width: 210px;
          }
          .ant-input-number {
            width: 150px !important;
            height: 35px;
            .ant-input-number-input-wrap {
              input {
                height: 34px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.home-white-list-add-modal {
  z-index: 1005;
}
</style>
