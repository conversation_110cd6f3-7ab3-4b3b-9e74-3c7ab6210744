/*
 * @Author: your name
 * @Date: 2021-01-28 10:16:11
 * @LastEditTime: 2021-02-05 17:43:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/api/review.js
 */
import Http from '@/utils/request'

export function isSuccessCode(e) {
  return e.code == 0 || e.resCode == 0;
}
export function reveiewPass(data) {
  return Http({
    url: `/sqlreview/review/review-detail-pass/`,
    method: 'post',
    data: data
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      return true;
    } else {
      return e;
    }
  })
}
export function reveiewFail(data) {
  return Http({
    url: `/sqlreview/review/review-detail-fail/`,
    method: 'post',
    data: data
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      return true;
    } else {
      return e;
    }
  })
}
export function removeReview(data) {
  return Http({
    url: `/sqlreview/review/delete-comment/`,
    method: 'post',
    data: data
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      return true;
    } else {
      return e;
    }
  })
}

export function getDetail(params, isInfo) {
  return Http({
    url: isInfo ? `/sqlreview/review/review-detail-info/` : `/sqlreview/review/review-detail/`,
    method: 'get',
    params: params
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      return e.data;
    } else {
      return e;
    }
  })
}

// 索引优化建议
export function sqlAdviceInfo(params) {
  return Http({
    url: `sqlreview/review/sql_advice`,
    method: 'get',
    params
  });
}

export function getDisOrAgree() {
  return Http({
    url: `/sqlreview/project/comment-config/get-all`,
    method: 'get',
    params: {
      review_type: 'SQL'
    }
  }).then(res => {
    let e = res.data;
    if (isSuccessCode(e)) {
      let items = {
        pass: [],
        fail: [],
        filter: []
      }
      const { filter = [], not_pass: notPass = [], pass = [] } = e.data || {}
      items.pass = pass.map(it => {
        return {
          key: it.value,
          name: it.label
        }
      })
      items.fail = notPass.map(it => {
        return {
          key: it.value,
          name: it.label
        }
      })
      items.filter = filter.map(it => {
        return {
          key: it.value,
          name: it.label
        }
      })
      return items;
    }
    return e;
  });
}

export function getSaveAdvice(data) {
  return Http({
    url: 'sqlreview/review/review-detail-update/',
    method: 'post',
    data: data
  })
}
export function reviewWhiteListAction(data = {}) {
  return Http({
    url: `/sqlreview/review/review-white-list/`,
    method: 'post',
    data: data
  });
}

export function getTableInfo(params = {}) {
  return Http({
    url: `/sqlreview/review/table-info`,
    method: 'get',
    params
  });
}

export function saveSqlmapConfig (params = {}) {
  return Http({
    url: `/sqlreview/review/review-detail-sqlmap/`,
    method: 'post',
    data: params
  });
}

export function getCatData(params = {}) {
  return Http({
    url: `/sqlreview/review/cat/`,
    method: 'get',
    params
  });
}

// 获取每个详情错误
export function getEveryDetailError(params = {}) {
  return Http({
    url: `sqlreview/review/every-detail-error/`,
    method: 'get',
    params
  });
}

export function sqlRewrite(params = {}) {
  return Http({
    url: `/sqlreview/review/sql_rewrite`,
    method: 'post',
    data: params
  });
}

export function sqlDifference(params = {}) {
  return Http({
    url: `/sqlreview/review/sql_difference`,
    method: 'post',
    data: params
  });
}

export function sqlRewriteJudge (params = {}) {
  return Http({
    url: `/sqlreview/review/sql_rewrite_judge`,
    method: 'post',
    data: params
  });
}

export function getReviewTableList (params = {}) {
  return Http({
    url: `/sqlreview/review/table_list`,
    method: 'get',
    params
  })
}

export function getNewTableInfo (params = {}) {
  return Http({
    url: `/sqlreview/review/new_table_info`,
    method: 'post',
    data: params
  })
}

export default {};
