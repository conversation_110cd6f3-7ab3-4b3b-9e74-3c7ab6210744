<template>
  <sqlModifyDetail></sqlModifyDetail>
</template>

<script>
import sqlModifyDetail from '@/components/Biz/ReviewDetail/sqlModify/detail';
// import common from '@/utils/common';
// import config from './config';
// import {
//   getDetail,
//   sqlAdviceInfo,
//   saveSqlmapConfig,
//   getDisOrAgree,
//   reveiewPass,
//   reveiewFail,
//   reviewWhiteListAction,
//   getEveryDetailError,
//   getCatData
// } from '@/api/review';
export default {
  components: {
    sqlModifyDetail
  },
  data() {
    // this.config = config(this);
    return {};
  },
  mounted() {},
  methods: {}
};
</script>

<style lang="less" scoped>
</style>