export default function (ctx) {
  const columns = [
    {
      title: '审核结果',
      dataIndex: 'ai_status',
      key: 'ai_status',
      scopedSlots: { customRender: 'ai_status' },
      width: 200
    },
    {
      title: 'SQL文本',
      dataIndex: 'sql_statement',
      key: 'sql_statement',
      scopedSlots: { customRender: 'sql_statement' },
      width: 150
    },
    {
      title: 'Schema',
      key: 'username',
      dataIndex: 'username',
      scopedSlots: { customRender: 'username' },
      width: 180
    },
    {
      title: '执行总次数',
      key: 'in_execution_count',
      dataIndex: 'in_execution_count',
      width: 200
    },
    {
      title: '总耗时(ms)',
      key: 'in_total_time',
      dataIndex: 'in_total_time',
      width: 180
    },
    {
      title: '总影响行数',
      key: 'total_rows',
      dataIndex: 'total_rows',
      width: 180
    },
    {
      title: 'CPU总消耗(ms)',
      dataIndex: 'in_total_worker_time',
      key: 'in_total_worker_time',
      width: 300
    },
    {
      title: '总物理读',
      dataIndex: 'in_total_physical_reads',
      key: 'in_total_physical_reads',
      width: 300
    },
    {
      title: '总逻辑读',
      dataIndex: 'in_total_logical_reads',
      key: 'in_total_logical_reads',
      width: 300
    },
    {
      title: '最近一次执行时间',
      dataIndex: 'last_execution_time',
      key: 'last_execution_time',
      scopedSlots: { customRender: 'last_execution_time' },
      width: 300
    },
    // {
    //   title: 'SQLID',
    //   dataIndex: 'sql_biz_id',
    //   key: 'sql_biz_id',
    //   scopedSlots: { customRender: 'sql_biz_id' },
    //   width: 150
    // },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 80,
      fixed: 'right'
    }
  ].map(item => {
    return {
      ...item,
      width: undefined
    }
  });
  const fields = [
    {
      type: 'Select',
      label: '审核结果',
      key: 'ai_status',
      props: {
        options: [
          {
            label: '未知',
            value: 0
          },
          {
            label: '通过',
            value: 1
          },
          {
            label: '未通过',
            value: -1
          },
          {
            label: '白名单通过',
            value: 2
          },
          {
            label: '错误',
            value: 9
          }

        ]
      }
    },
    {
      type: 'Input',
      label: 'SQL文本',
      key: 'sql_statement'
    },
    {
      type: 'Input',
      label: 'Schema',
      key: 'username'
    }
  ];
  return {
    columns,
    searchFields: fields
  };
}
