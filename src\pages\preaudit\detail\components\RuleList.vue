<template>
  <!-- 触发规则列表 -->
  <div
    class="risk"
    v-if="
      noRiskOptions.length > 0 ||
      errorOptions.length > 0 ||
      lowRiskOptions.length > 0 ||
      highRiskOptions.length > 0 ||
      sqlOptions.length > 0 ||
      packageOptions.length > 0 ||
      functionOptions.length > 0 ||
      procedureOptions.length > 0
    "
  >
    <div class="main-title">风险提示</div>
    <!-- 高风险 -->
    <div class="scroll-box">
      <div v-if="highRiskOptions.length > 0">
        <div class="risk-header">
          <div class="title">
            <custom-icon type="lu-icon-alarm" class="high" />高风险
          </div>
          <div>
            <a-checkbox
              :indeterminate="highIndeterminate"
              :checked="highCheckAll"
              @change="onCheckAllHigh"
            ></a-checkbox>
          </div>
        </div>
        <a-spin :spinning="spinning">
          <custom-empty v-if="highRiskOptions.length <= 0" />
          <a-checkbox-group
            :class="[
              'custom-checkbox-group',
              highRiskOptions.length > 0 ? 'has-exception' : 'rule'
            ]"
            v-model="highRiskCheckedList"
            v-else
          >
            <template v-for="item in highRiskOptions || []">
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickHighRisk"
              >
                <div class="ccg-icon-label">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                </div>
                <span class="ccg-num">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
            </template>
          </a-checkbox-group>
        </a-spin>
      </div>
      <!-- 低风险 -->
      <div
        class="divider-line"
        v-if="highRiskOptions.length > 0 && lowRiskOptions.length > 0"
      ></div>
      <div v-if="lowRiskOptions.length > 0">
        <div class="risk-header">
          <div class="title">
            <custom-icon type="lu-icon-alarm" class="low" />低风险
          </div>
          <div>
            <a-checkbox
              :indeterminate="lowIndeterminate"
              :checked="lowCheckAll"
              @change="onCheckAllLow"
            ></a-checkbox>
          </div>
        </div>

        <a-spin :spinning="spinning">
          <custom-empty v-if="lowRiskOptions.length <= 0" />
          <a-checkbox-group
            :class="[
              'custom-checkbox-group',
              lowRiskOptions.length > 0 ? 'has-exception' : 'rule'
            ]"
            v-model="lowRiskCheckedList"
            v-else
          >
            <template v-for="item in lowRiskOptions || []">
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickLowRisk"
              >
                <div class="ccg-icon-label">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                </div>
                <span class="ccg-num">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
            </template>
          </a-checkbox-group>
        </a-spin>
      </div>
      <!-- 无风险 -->
      <div
        class="divider-line"
        v-if="
          noRiskOptions.length > 0 &&
          (lowRiskOptions.length > 0 || highRiskOptions.length > 0)
        "
      ></div>
      <div v-if="noRiskOptions.length > 0">
        <div class="risk-header">
          <div class="title">
            <custom-icon type="lu-icon-success" class="low" />无风险
          </div>
          <div>
            <a-checkbox
              :indeterminate="indeterminate"
              :checked="checkAll"
              @change="onCheckAll"
            ></a-checkbox>
          </div>
        </div>

        <a-spin :spinning="spinning">
          <custom-empty v-if="noRiskOptions.length <= 0" />
          <a-checkbox-group
            :class="[
              'custom-checkbox-group',
              noRiskOptions.length > 0 ? 'has-exception' : 'rule'
            ]"
            v-model="noRiskCheckedList"
            v-else
          >
            <template v-for="item in noRiskOptions || []">
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickNoRisk"
              >
                <div class="ccg-icon-label">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                </div>
                <span class="ccg-num">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
            </template>
          </a-checkbox-group>
        </a-spin>
      </div>
      <div
        class="divider-line"
        v-if="
          errorOptions.length > 0 &&
          (lowRiskOptions.length > 0 ||
            highRiskOptions.length > 0 ||
            noRiskOptions.length > 0)
        "
      ></div>
      <div v-if="errorOptions.length > 0">
        <div class="risk-header">
          <div class="title">
            <custom-icon type="lu-icon-unusual" class="error" />审核异常
          </div>
          <div>
            <a-checkbox
              :indeterminate="errorIndeterminate"
              :checked="errorCheckAll"
              @change="onCheckAllError"
            ></a-checkbox>
          </div>
        </div>
        <a-spin :spinning="spinning">
          <custom-empty v-if="errorOptions.length <= 0" />
          <a-checkbox-group
            :class="[
              'custom-checkbox-group',
              errorOptions.length > 0 ? 'has-rule' : 'exception'
            ]"
            v-model="errorCheckedList"
            v-else
          >
            <template v-for="item in errorOptions || []">
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickError"
              >
                <div class="ccg-icon-label">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                </div>
                <span class="ccg-num">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
            </template>
          </a-checkbox-group>
        </a-spin>
      </div>
    </div>
    <div class="main-title" style="margin-top: 24px">SQL来源</div>
    <!-- SQL来源 -->
    <div class="scroll-box">
      <div v-if="sqlOptions.length > 0">
        <div class="risk-header">
          <div class="title">
            <custom-icon type="lu-icon-sql" class="sql" />文件名
          </div>
          <div>
            <a-checkbox
              :indeterminate="sqlIndeterminate"
              :checked="sqlCheckAll"
              @change="onCheckAllSql"
            ></a-checkbox>
          </div>
        </div>

        <a-spin :spinning="spinning">
          <custom-empty v-if="sqlOptions.length <= 0" />
          <a-checkbox-group
            :class="[
              'custom-checkbox-group',
              sqlOptions.length > 0 ? 'has-rule' : 'exception'
            ]"
            v-model="sqlCheckedList"
            v-else
          >
            <template v-for="item in sqlOptions || []">
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickSql"
                v-if="!item.error"
              >
                <div class="ccg-icon-label">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                </div>
                <span class="ccg-num">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickSql"
                 v-if="item.error"
              >
                <div class="ccg-icon-label" style="color: #e71d36;">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                  <custom-icon style="color: #e71d36; margin-left: 8px" type="exclamation-circle" />
                </div>
                <span class="ccg-num" style="color: #e71d36;">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
            </template>
          </a-checkbox-group>
        </a-spin>
      </div>
      <!-- 包 -->
      <div v-if="packageOptions.length > 0">
        <div class="risk-header">
          <div class="title">
            <custom-icon type="lu-icon-applyfor" class="error" />包
          </div>
          <div>
            <a-checkbox
              :indeterminate="packageIndeterminate"
              :checked="packageCheckAll"
              @change="onCheckAllPackage"
            ></a-checkbox>
          </div>
        </div>
        <a-spin :spinning="spinning">
          <custom-empty v-if="packageOptions.length <= 0" />
          <a-checkbox-group
            :class="[
              'custom-checkbox-group',
              packageOptions.length > 0 ? 'has-rule' : 'exception'
            ]"
            v-model="packageCheckedList"
            v-else
          >
            <template v-for="item in packageOptions || []">
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickPackage"
              >
                <div class="ccg-icon-label">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                </div>
                <span class="ccg-num">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
            </template>
          </a-checkbox-group>
        </a-spin>
      </div>
      <!-- 函数 -->
      <div v-if="functionOptions.length > 0">
        <div class="risk-header">
          <div class="title">
            <custom-icon type="lu-icon-function1" class="error" />函数
          </div>
          <div>
            <a-checkbox
              :indeterminate="functionIndeterminate"
              :checked="functionCheckAll"
              @change="onCheckAllFunction"
            ></a-checkbox>
          </div>
        </div>
        <a-spin :spinning="spinning">
          <custom-empty v-if="functionOptions.length <= 0" />
          <a-checkbox-group
            :class="[
              'custom-checkbox-group',
              functionOptions.length > 0 ? 'has-rule' : 'exception'
            ]"
            v-model="functionCheckedList"
            v-else
          >
            <template v-for="item in functionOptions || []">
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickFunction"
              >
                <div class="ccg-icon-label">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                </div>
                <span class="ccg-num">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
            </template>
          </a-checkbox-group>
        </a-spin>
      </div>
      <!-- 存储过程 -->
      <div v-if="procedureOptions.length > 0">
        <div class="risk-header">
          <div class="title">
            <custom-icon type="lu-icon-storage" class="error" />存储过程
          </div>
          <div>
            <a-checkbox
              :indeterminate="procedureIndeterminate"
              :checked="procedureCheckAll"
              @change="onCheckAllProcedure"
            ></a-checkbox>
          </div>
        </div>
        <a-spin :spinning="spinning">
          <custom-empty v-if="procedureOptions.length <= 0" />
          <a-checkbox-group
            :class="[
              'custom-checkbox-group',
              procedureOptions.length > 0 ? 'has-rule' : 'exception'
            ]"
            v-model="procedureCheckedList"
            v-else
          >
            <template v-for="item in procedureOptions || []">
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickProcedure"
              >
                <div class="ccg-icon-label">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                </div>
                <span class="ccg-num">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
            </template>
          </a-checkbox-group>
        </a-spin>
      </div>
      <!-- agent -->
      <div v-if="agentOptions.length > 0">
        <div class="risk-header">
          <div class="title">agent worker</div>
          <div>
            <a-checkbox
              :indeterminate="agentIndeterminate"
              :checked="agentCheckAll"
              @change="onCheckAllAgent"
            ></a-checkbox>
          </div>
        </div>
        <a-spin :spinning="spinning">
          <custom-empty v-if="agentOptions.length <= 0" />
          <a-checkbox-group
            :class="[
              'custom-checkbox-group',
              agentOptions.length > 0 ? 'has-rule' : 'exception'
            ]"
            v-model="agentCheckedList"
            v-else
          >
            <template v-for="item in agentOptions || []">
              <a-checkbox
                :key="item.value"
                v-bind="item || {}"
                @change="onClickAgent"
              >
                <div class="ccg-icon-label">
                  <custom-icon class="ccg-icon" type="lu-icon-right1" />
                  <span>{{ item.label }}</span>
                </div>
                <span class="ccg-num">
                  <span>{{ item.num }}</span>
                </span>
              </a-checkbox>
            </template>
          </a-checkbox-group>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    noRiskOptions: {
      type: Array,
      default: () => []
    },
    errorOptions: {
      type: Array,
      default: () => []
    },
    lowRiskOptions: {
      type: Array,
      default: () => []
    },
    highRiskOptions: {
      type: Array,
      default: () => []
    },
    sqlOptions: {
      type: Array,
      default: () => []
    },
    packageOptions: {
      type: Array,
      default: () => []
    },
    functionOptions: {
      type: Array,
      default: () => []
    },
    procedureOptions: {
      type: Array,
      default: () => []
    },
    agentOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      spinning: false,
      // 风险提示选项
      riskSelects: [],
      // sql来源选项
      sqlSelects: [],
      // 存储过程选项
      storedProcedureSelects: [],
      // 无风险
      noRiskCheckedList: [], // 绑定的已经选
      noRiskSelects: [], // 选择值集合
      indeterminate: false, // 半选
      checkAll: false, // 全选
      // 低风险
      lowRiskCheckedList: [],
      lowRiskSelects: [],
      lowIndeterminate: false,
      lowCheckAll: false,
      // 高风险
      highRiskCheckedList: [],
      highRiskSelects: [],
      highIndeterminate: false,
      highCheckAll: false,
      // 异常
      errorCheckedList: [],
      errorSelects: [],
      errorIndeterminate: false,
      errorCheckAll: false,
      // sql来源
      sqlCheckedList: [],
      sqlCheckAll: false,
      sqlIndeterminate: false,
      // 包
      packageIndeterminate: false,
      packageCheckAll: false,
      packageCheckedList: [],
      packageSelects: [],
      // 函数
      functionIndeterminate: false,
      functionCheckAll: false,
      functionCheckedList: [],
      functionSelects: [],
      // 存储过程
      procedureIndeterminate: false,
      procedureCheckAll: false,
      procedureCheckedList: [],
      procedureSelects: [],
      // agent
      agentIndeterminate: false,
      agentCheckAll: false,
      agentCheckedList: [],
      agentSelects: []
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    onClickHighRisk(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.highRiskSelects.includes(value)) {
        this.highRiskSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.highRiskSelects = this.highRiskSelects.filter(item => {
          return item !== value;
        });
      }
      this.highRiskCheckedList = [...this.highRiskSelects];
      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      this.highIndeterminate =
        !!this.highRiskCheckedList.length &&
        this.highRiskCheckedList.length < this.highRiskOptions.length;
      this.highCheckAll =
        this.highRiskCheckedList.length === this.highRiskOptions.length;

      this.$emit('setParams', null, this.riskSelects);
    },
    onClickLowRisk(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.lowRiskSelects.includes(value)) {
        this.lowRiskSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.lowRiskSelects = this.lowRiskSelects.filter(item => {
          return item !== value;
        });
      }
      this.lowRiskCheckedList = [...this.lowRiskSelects];
      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      // 判定全选与否
      this.lowIndeterminate =
        !!this.lowRiskCheckedList.length &&
        this.lowRiskCheckedList.length < this.lowRiskOptions.length;
      this.lowCheckAll =
        this.lowRiskCheckedList.length === this.lowRiskOptions.length;
      console.log(
        this.lowRiskSelects,
        this.lowIndeterminate,
        this.lowCheckAll,
        'this.lowRiskSelects, this.lowIndeterminate, this.lowCheckAll'
      );
      this.$emit('setParams', null, this.riskSelects);
    },
    onClickNoRisk(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.noRiskSelects.includes(value)) {
        this.noRiskSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.noRiskSelects = this.noRiskSelects.filter(item => {
          return item !== value;
        });
      }
      this.noRiskCheckedList = [...this.noRiskSelects];
      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      // 判定全选与否
      this.indeterminate =
        !!this.noRiskCheckedList.length &&
        this.noRiskCheckedList.length < this.noRiskOptions.length;
      this.checkAll =
        this.noRiskCheckedList.length === this.noRiskOptions.length;

      // 刷新表格
      this.$emit('setParams', null, this.riskSelects);
    },
    onClickError(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.errorSelects.includes(value)) {
        this.errorSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.errorSelects = this.errorSelects.filter(item => {
          return item !== value;
        });
      }
      this.errorCheckedList = [...this.errorSelects];
      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      // 判定全选与否
      this.errorIndeterminate =
        !!this.errorCheckedList.length &&
        this.errorCheckedList.length < this.errorOptions.length;
      this.errorCheckAll =
        this.errorCheckedList.length === this.errorOptions.length;
      // 刷新表格
      this.$emit('setParams', null, this.riskSelects);
    },
    onClickSql(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.sqlSelects.includes(value)) {
        this.sqlSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.sqlSelects = this.sqlSelects.filter(item => {
          return item !== value;
        });
      }
      this.sqlCheckedList = [...this.sqlSelects];
      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      // 判定全选与否
      this.sqlIndeterminate =
        !!this.sqlCheckedList.length &&
        this.sqlCheckedList.length < this.sqlOptions.length;
      this.sqlCheckAll = this.sqlCheckedList.length === this.sqlOptions.length;
      // 刷新表格
      this.$emit('setParams', null, this.riskSelects, this.sqlSelects);
    },
    onClickPackage(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.packageSelects.includes(value)) {
        this.packageSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.packageSelects = this.packageSelects.filter(item => {
          return item !== value;
        });
      }
      this.packageCheckedList = [...this.packageSelects];
      this.storedProcedureSelects = [
        ...this.functionSelects,
        ...this.procedureSelects,
        ...this.packageSelects,
        ...this.agentSelects
      ];
      // 判定全选与否
      this.packageIndeterminate =
        !!this.packageCheckedList.length &&
        this.packageCheckedList.length < this.packageOptions.length;
      this.packageCheckAll =
        this.packageCheckedList.length === this.packageOptions.length;
      // 刷新表格
      this.$emit(
        'setParams',
        null,
        this.riskSelects,
        this.storedProcedureSelects
      );
    },
    onClickFunction(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.functionSelects.includes(value)) {
        this.functionSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.functionSelects = this.functionSelects.filter(item => {
          return item !== value;
        });
      }
      this.functionCheckedList = [...this.functionSelects];
      this.storedProcedureSelects = [
        ...this.functionSelects,
        ...this.procedureSelects,
        ...this.packageSelects,
        ...this.agentSelects
      ];
      // 判定全选与否
      this.functionIndeterminate =
        !!this.functionCheckedList.length &&
        this.functionCheckedList.length < this.functionOptions.length;
      this.functionCheckAll =
        this.functionCheckedList.length === this.functionOptions.length;

      // 刷新表格
      this.$emit(
        'setParams',
        null,
        this.riskSelects,
        this.storedProcedureSelects
      );
    },
    onClickProcedure(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.procedureSelects.includes(value)) {
        this.procedureSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.procedureSelects = this.procedureSelects.filter(item => {
          return item !== value;
        });
      }
      this.procedureCheckedList = [...this.procedureSelects];
      this.storedProcedureSelects = [
        ...this.functionSelects,
        ...this.procedureSelects,
        ...this.packageSelects,
        ...this.agentSelects
      ];
      // 判定全选与否
      this.procedureIndeterminate =
        !!this.procedureCheckedList.length &&
        this.procedureCheckedList.length < this.procedureOptions.length;
      this.procedureCheckAll =
        this.procedureCheckedList.length === this.procedureOptions.length;
      // 刷新表格
      this.$emit(
        'setParams',
        null,
        this.riskSelects,
        this.storedProcedureSelects
      );
    },
    onClickAgent(e) {
      let value = _.get(e, 'target.value');
      let checked = _.get(e, 'target.checked');

      // 选中，判断数组里面是否已经存在
      if (checked && !this.agentSelects.includes(value)) {
        this.agentSelects.push(value);
      } else {
        // 取消选中，从数据中剔除该值
        this.procedureSelects = this.agentSelects.filter(item => {
          return item !== value;
        });
      }
      this.agentCheckedList = [...this.agentSelects];
      this.storedProcedureSelects = [
        ...this.functionSelects,
        ...this.procedureSelects,
        ...this.packageSelects,
        ...this.agentSelects
      ];
      // 判定全选与否
      this.agentIndeterminate =
        !!this.agentCheckedList.length &&
        this.agentCheckedList.length < this.agentOptions.length;
      this.agentCheckAll =
        this.agentCheckedList.length === this.agentOptions.length;
      // 刷新表格
      this.$emit(
        'setParams',
        null,
        this.riskSelects,
        this.storedProcedureSelects
      );
    },
    // 高风险 全选 反选
    onCheckAllHigh(e) {
      let checked = _.get(e, 'target.checked');

      const _options = this.highRiskOptions.map(item => item.value);
      this.highRiskSelects = checked ? [..._options] : [];

      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      Object.assign(this, {
        highRiskCheckedList: checked ? _options : [],
        highIndeterminate: false,
        highCheckAll: checked
      });

      this.$emit('setParams', checked, this.riskSelects);
    },
    // 低风险 全选 反选
    onCheckAllLow(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.lowRiskOptions.map(item => item.value);
      this.lowRiskSelects = checked ? [..._options] : [];

      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      Object.assign(this, {
        lowRiskCheckedList: checked ? _options : [],
        lowIndeterminate: false,
        lowCheckAll: checked
      });

      this.$emit('setParams', checked, this.riskSelects);
    },
    // 无风险 全选 反选
    onCheckAll(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.noRiskOptions.map(item => item.value);
      this.noRiskSelects = checked ? [..._options] : [];

      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      Object.assign(this, {
        noRiskCheckedList: checked ? _options : [],
        indeterminate: false,
        checkAll: checked
      });

      this.$emit('setParams', checked, this.riskSelects);
    },
    // 异常 全选 反选
    onCheckAllError(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.errorOptions.map(item => item.value);
      this.errorSelects = checked ? [..._options] : [];

      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      Object.assign(this, {
        errorCheckedList: checked ? _options : [],
        errorIndeterminate: false,
        errorCheckAll: checked
      });

      this.$emit('setParams', checked, this.riskSelects);
    },
    // sql来源 全选 反选
    onCheckAllSql(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.sqlOptions.map(item => item.value);
      this.sqlSelects = checked ? [..._options] : [];

      this.riskSelects = [
        ...this.highRiskSelects,
        ...this.lowRiskSelects,
        ...this.errorSelects,
        ...this.noRiskSelects
      ];
      Object.assign(this, {
        sqlCheckedList: checked ? _options : [],
        sqlIndeterminate: false,
        sqlCheckAll: checked
      });

      this.$emit('setParams', checked, this.riskSelects, this.sqlSelects);
    },
    // 包 全选 反选
    onCheckAllPackage(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.packageOptions.map(item => item.value);
      this.packageSelects = checked ? _options : [];

      this.storedProcedureSelects = [
        ...this.functionSelects,
        ...this.procedureSelects,
        ...this.packageSelects,
        ...this.agentSelects
      ];
      Object.assign(this, {
        packageCheckedList: checked ? _options : [],
        packageIndeterminate: false,
        packageCheckAll: checked
      });

      this.$emit(
        'setParams',
        checked,
        this.riskSelects,
        this.storedProcedureSelects
      );
    },
    // 函数 全选 反选
    onCheckAllFunction(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.functionOptions.map(item => item.value);
      this.functionSelects = checked ? _options : [];

      this.storedProcedureSelects = [
        ...this.functionSelects,
        ...this.procedureSelects,
        ...this.packageSelects,
        ...this.agentSelects
      ];
      Object.assign(this, {
        functionCheckedList: checked ? _options : [],
        functionIndeterminate: false,
        functionCheckAll: checked
      });

      this.$emit(
        'setParams',
        checked,
        this.riskSelects,
        this.storedProcedureSelects
      );
    },
    // 存储过程 全选 反选
    onCheckAllProcedure(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.procedureOptions.map(item => item.value);
      this.procedureSelects = checked ? _options : [];

      this.storedProcedureSelects = [
        ...this.functionSelects,
        ...this.procedureSelects,
        ...this.packageSelects,
        ...this.agentSelects
      ];
      Object.assign(this, {
        procedureCheckedList: checked ? _options : [],
        procedureIndeterminate: false,
        procedureCheckAll: checked
      });

      this.$emit(
        'setParams',
        checked,
        this.riskSelects,
        this.storedProcedureSelects
      );
    },
    // 存储过程 全选 反选
    onCheckAllAgent(e) {
      let checked = _.get(e, 'target.checked');
      const _options = this.agentOptions.map(item => item.value);
      this.agentSelects = checked ? _options : [];

      this.storedAgentSelects = [
        ...this.functionSelects,
        ...this.procedureSelects,
        ...this.packageSelects,
        ...this.agentSelects
      ];
      Object.assign(this, {
        agentCheckedList: checked ? _options : [],
        agentIndeterminate: false,
        agentCheckAll: checked
      });

      this.$emit(
        'setParams',
        checked,
        this.riskSelects,
        this.storedProcedureSelects
      );
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.risk {
  width: 25%;
  height: 660px;
  overflow-y: auto;
  .scroll-box {
    padding: 0 24px 0 0;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-track {
      background: #fff;
    }
    &::-webkit-scrollbar-thumb {
      background: #fff;
    }
    &:hover::-webkit-scrollbar-track {
      background: #e8e8e8;
    }
    &:hover::-webkit-scrollbar-thumb {
      background: rgb(209, 209, 209);
    }
  }
  .divider-line {
    height: 1px;
    width: auto;
    background: #f4f5f7;
    margin: 10px 0 0 0;
  }
  .main-title {
    font-size: 16px;
    color: #27272a;
    font-weight: 600;
    margin-bottom: 6px;
  }
  .risk-header {
    display: flex;
    justify-content: space-between;
    padding: 16px 8px 16px 0;
    .title {
      font-family: PingFangSC-Semibold;
      font-size: 14px;
      color: #27272a;
      font-weight: 600;
      margin-left: 12px;
      .anticon {
        font-size: 16px;
        margin-right: 9px;
      }
      .high {
        color: #e71d36;
      }
      .low {
        color: #f29339;
      }
      .error {
        color: #71717a;
      }
    }
    .ant-checkbox-wrapper {
      width: 16px;
      height: 16px;
    }
  }

  .ant-table {
    .ant-table-thead {
      border: none;
      background: transparent;
    }
  }
  .custom-checkbox-group {
    display: block;
    &.rule {
      max-height: 600px;
    }
    /deep/ .ant-checkbox-wrapper {
      display: block;
      margin: 0 0 5px 0;
      padding: 4px 8px 4px 0;
      font-size: 12px;
      border: none;
      .ant-checkbox {
        display: none;
      }
      .ant-checkbox + span {
        padding-right: 0;
        padding-left: 0;
      }
      > span:last-child {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ccg-icon-label {
          display: flex;
          align-items: center;
          margin-left: 12px;
          .ccg-icon {
            margin-right: 8px;
            color: #e4e4e7;
            font-size: 12px;
            &.circle {
              color: #e71d36;
              margin-right: 0px;
            }
            &.bell {
              color: #f29339;
              margin-right: 0px;
            }
            &.robot {
              color: #4db5f2;
              margin-right: 10px;
            }
          }
          .limit-label {
            > pre {
              color: #71717a;
              font-size: 12px;
            }
          }
          > span {
            word-break: break-word;
          }
        }
        .ccg-num {
          text-align: right;
          color: #71717a;
          font-size: 12px;
          margin-left: 16px;
          white-space: nowrap;
          > span {
            display: inline-block;
            min-width: 16px;
            text-align: center;
          }
        }
      }
      &:hover {
        color: @primary-color;
        .ccg-icon-label {
          .ccg-icon {
            &.circle {
              color: #e71d36;
            }
            &.bell {
              color: #f29339;
            }
            &.robot {
              color: #4db5f2;
            }
          }
        }
      }
      &.ant-checkbox-wrapper-checked {
        background: #ebf2ff;
        color: #008adc;
        border-radius: 8px;
        > span:last-child {
          .ccg-icon-label {
            .ccg-icon {
              color: #008adc;
              &.circle {
                color: #e71d36;
              }
              &.bell {
                color: #f29339;
              }
              &.robot {
                color: #4db5f2;
              }
            }
            .limit-label {
              > pre {
                color: #008adc;
              }
            }
          }
          .ccg-num {
            color: #008adc;
          }
        }
      }
    }
  }
}
</style>
