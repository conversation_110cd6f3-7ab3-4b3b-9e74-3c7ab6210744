<template>
  <div>
    <Schema :schema="schema" :value="data" ref="schema"></Schema>
    <a-button @click="test">测试</a-button>
  </div>
</template>

<script>
import Schema from '@/components/Schema';

export default {
  components: { Schema },
  props: {},
  data() {
    return {
      schema: {
        compName: 'Blocks',
        props: {
          value: [
            { key: 'base_info', title: '基础信息', icon: 'edit' },
            { key: 'extend_info', title: '扩展信息', icon: 'block' }
          ]
        },
        children: [
          {
            compName: 'Form',
            slot: 'base_info',
            dataKey: 'base_info',
            dataProp: 'formData',
            props: {
              fields: [
                {
                  type: 'Label',
                  label: '开始',
                  key: 'fromName'
                },
                {
                  type: 'Label',
                  label: '结束',
                  key: 'toName'
                }
              ],
              fixedLabel: true,
              layout: 'horizontal',
              labelCol: { span: 8 },
              wrapperCol: { span: 16 },
              gutter: 24,
              multiCols: 2,
              colon: true
            }
          },
          {
            compName: 'Form',
            slot: 'extend_info',
            emit: 'extend_info',
            dataKey: 'extend_info',
            dataProp: 'formData',
            props: {
              fields: [
                {
                  type: 'Select',
                  label: '条件',
                  key: 'condition',
                  props: {
                    options: [
                      { label: '成功', value: 'success' },
                      { label: '失败', value: 'failure' }
                    ]
                  }
                }
              ],
              fixedLabel: true,
              layout: 'horizontal',
              labelCol: { span: 4 },
              wrapperCol: { span: 20 },
              gutter: 24,
              multiCols: 1,
              colon: true
            }
          }
        ]
      },
      data: {
        base_info: {
          fromName: 'aaa',
          toName: 'bbb'
        },
        extend_info: {}
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    test() {
      console.log(this.$refs.schema.getData());
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>