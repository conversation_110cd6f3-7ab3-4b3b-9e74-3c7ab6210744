<template>
  <a-spin :spinning="spinning" class="quick-audit-content">
    <SplitPanel
      split-to="rows"
      primary="second"
      units="percents"
      :min-size="0"
      :size="32"
    >
      <div class="psc-right-query-content-wrapper" slot="first">
        <div class="header-info">
          <Form
            ref="form"
            class="base-info-form"
            v-bind="formParams"
            :formData="formData"
          ></Form>
          <div class="button-area">
            <!-- <a-button class="highlight" @click="download" v-if="isFinished"
              >导出</a-button
            > -->
            <a-dropdown
              v-if="isFinished"
              overlayClassName="pre-audit-detail-overlay"
              :getPopupContainer="getPopupContainer"
              class="download"
            >
              <a-menu
                class="download-menu"
                slot="overlay"
                @click="(event) => handleMenuClick(event)"
              >
                <a-menu-item key="1"> 导出excel </a-menu-item>
                <a-menu-item key="2"> 导出html </a-menu-item>
              </a-menu>
              <a-button icon="download" class="highlight"> 导出 </a-button>
            </a-dropdown>
            <a-button type="primary" @click="edit" v-if="isFinished"
              >打开编辑</a-button
            >
            <a-button type="primary" @click="audit" :disabled="isFinished"
              >开始审核</a-button
            >
          </div>
        </div>
        <Coder
          ref="coder"
          :coderData="coderData"
          :auditResults="auditResults"
          :isFinished="isFinished"
          :progress="progress"
          :mode="mode"
          @updateData="updateData"
        ></Coder>
      </div>
      <div class="psc-right-query-detail-wrapper" slot="second">
        <div
          class="result-failed"
          v-if="progress == 100 && auditResults.length <= 0"
        >
          <custom-icon type="lu-icon-error"></custom-icon>
          <div>Review失败</div>
          <span>{{ errorMessage || '系统处理异常' }}</span>
        </div>
        <div :class="['result', resultData.risk && auditType]" v-else>
          <div class="header">
            <div class="title">审核结果</div>
          </div>
          <div class="content">
            <div v-if="resultData.risk">
              <div class="risk">
                <div class="title">
                  风险
                  <span :class="resultData.risk">{{
                    riskText[resultData.risk]
                  }}</span>
                </div>
                <div class="err-message" v-if="resultData.error_message">
                  错误信息：{{ resultData.error_message }}
                </div>
                <div
                  v-for="(item, index) in resultData.rule"
                  :key="index"
                  class="rule-text"
                >
                  <custom-icon
                    type="lu-icon-alarm"
                    :style="{
                      color: item.rule_result == 1 ? '#f29339' : '#e71d36'
                    }"
                  />
                  <span>{{ item.name }}</span>
                  <MarkdownViewer
                    class="rich-editor-preview"
                    :value="item.suggest"
                    v-if="item.suggest"
                  ></MarkdownViewer>
                </div>
              </div>
            </div>
            <custom-empty v-else></custom-empty>
          </div>
        </div>
        <div
          class="sql-plan-failed"
          v-if="progress == 100 && auditResults.length <= 0"
        >
          <custom-empty></custom-empty>
        </div>
        <div :class="['sql-plan', resultData.risk && auditType]" v-else>
          <SqlPlan
            ref="sqlPlan"
            :resultData="resultData"
            v-if="auditType == 'online'"
          ></SqlPlan>
        </div>
      </div>
    </SplitPanel>
  </a-spin>
</template>
<script>
import Form from '@/components/Form';
import Coder from './Coder';
import SqlPlan from './SqlPlan';
import SplitPanel from '@/components/SplitPanel';
import MarkdownViewer from '@/components/Markdown/viewer';
import config from './config';
import {
  getHistoryDetail,
  quickAudit,
  quickAuditBackList,
  historyDownload
} from '@/api/quick';
export default {
  components: { Form, Coder, SplitPanel, SqlPlan, MarkdownViewer },
  props: {
    pane: {
      type: Object,
      default: () => {}
    },
    paneKey: Number | String
  },
  data() {
    this.config = config(this);
    this.reqCancelHandler = null; // 取消请求句柄
    return {
      isFinished: false,
      inited: false,
      count: 0,
      timeout: 1000,
      params: {},
      isPause: false,
      wrongNum: 0,
      formData: { audit_type: 'offline' },
      formParams: {
        fixedLabel: true,
        layout: 'horizontal',
        iconCombine: true,
        fields: this.config.fields()
      },
      current: -1,
      riskText: {
        high: '高',
        low: '低',
        no_risk: '无',
        error: '异常'
      },
      resultData: {},
      coderData: {},
      recordId: null,
      auditResults: [],
      progress: 0,
      contentType: null,
      spinning: false,
      mode: '',
      auditType: 'online',
      errorMessage: ''
    };
  },
  created() {},
  mounted() {},
  destroyed() {
    this.pageDestroyed = true;
    this.cancel();
  },
  methods: {
    getPopupContainer() {
      return document.getElementById('rootContent');
    },
    handleMenuClick(e) {
      this.download(e.key);
    },
    download(key) {
      this.$showLoading({
        tips: `下载中...`
      });
      const params = {
        export_type: 2,
        tab_id: this.paneKey,
        export_file_type: key,
        // detail_id: this.resultData.detail_id,
        record_id: this.recordId
      };
      historyDownload(params)
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    },
    // 审核完选择sql, 或者选择历史记录调用
    updateData(data, mode) {
      this.mode = mode;
      getHistoryDetail({
        detail_id: data.detail_id,
        quick_audit_id: data.quick_audit_id
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.resultData = _.get(res, 'data.data');
            this.resultData.detail_id = data.detail_id;
            this.resultData.quick_audit_id = data.quick_audit_id;
            this.recordId = this.resultData.record_id;
            // this.resultData.db_type = data.db_type;
            this.resultData.tab_id = this.paneKey;
            this.formData = { ...this.resultData };
            if (mode == 'history') {
              this.isFinished = true;
              this.coderData = this.resultData;
              this.$set(this.formParams, 'fields', this.config.fields(true));
            }
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 审核
    audit() {
      const { coder, form } = this.$refs;
      const formData = form.getData();
      const coderData = coder.getData();
      const storeData = {
        [this.paneKey]: coderData
      };
      window.localStorage.setItem(
        'quick-audit-coder-data',
        JSON.stringify(storeData)
      );

      const params = {
        ...formData,
        ...coderData
      };
      Promise.all([form.validate()]).then(valid => {
        if (valid) {
          if (!coderData.content) {
            this.$message.warning(`请填写${coderData.content_type}`);
            return;
          }
          quickAudit(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({ duration: 0 });
                const recordId = _.get(res, 'data.data.record_id');
                this.recordId = recordId;
                this.params = { record_id: recordId };
                this.onSetInterval();
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    // 手动用setTimeout 实现setInterval
    onSetInterval() {
      const loop = (params = {}) => {
        if (this.pageDestroyed) return;
        quickAuditBackList(params, c => {
          this.reqCancelHandler = c;
        })
          .then(res => {
            this.count++;
            this.spinning = true;
            if (CommonUtil.isSuccessCode(res)) {
              let resData = _.get(res, 'data.data') || {};
              this.auditResults = resData.results;
              this.errorMessage = resData.error_message;
              this.progress = resData.progress;
            } else {
              if (this.count > 1) return;
              this.$message.error(_.get(res, 'data.message'));
            }
          })
          .catch(e => {
            this.wrongNum++;
            if (this.wrongNum > 1) return;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          })
          .finally(e => {
            if (!this.inited) {
              this.inited = true;
            }
            if (this.progress == 100) {
              this.spinning = false;
              this.isFinished = true;
              const { coder, form } = this.$refs;
              const formData = form.getData();
              const coderData = coder.getData();
              const ruleSet = this.pane.rule_set;
              const data = { ...formData, ...coderData };
              data.tab_id = this.paneKey;
              data.name = this.pane.name;
              this.$set(this.formParams, 'fields', this.config.fields(true));
              if (ruleSet) {
                this.$emit('saveTabs', data, 'edit');
              } else {
                this.$emit('saveTabs', data, 'add');
              }
              this.cancel();
              return;
            }
            if (this.wrongNum >= 5) {
              this.spinning = false;
              this.cancel();
              return;
            }
            clearTimeout(this.timer);
            // 取消接口请求
            if (this.reqCancelHandler) {
              this.reqCancelHandler();
            }
            // 暂停之后 搜索会再调一次接口 这时不轮巡
            if (!this.isPause) {
              this.timer = setTimeout(() => {
                loop(this.params);
              }, this.timeout);
            }
          });
      };
      loop(this.params);
    },
    // 停止计时器
    cancel() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      // 取消接口请求
      if (this.reqCancelHandler) {
        this.reqCancelHandler();
      }
      this.count = 0;
      this.wrongNum = 0;
      this.params = {};
    },
    // 暂停
    pause() {
      this.isPause = true;
      this.cancel();
    },
    // 恢复
    resume() {
      if (!this.inited) return;
      this.isPause = false;
      this.cancel();
      this.onSetInterval();
    },
    edit() {
      this.isFinished = false;
      this.progress = 0;
      this.mode = 'edit';
      this.$set(this.formParams, 'fields', this.config.fields(false));
      const res = window.localStorage.getItem('quick-audit-coder-data');
      const storeData = JSON.parse(res);
      this.coderData = storeData[this.paneKey];
      this.resultData = {};
    },
    // 关闭
    close() {
      this.errorMessage = null;
    },
    // 清空
    hide() {
      this.sql_text = '';
      this.xml_text = '';
      this.errorMessage = null;
      this.fileList = [];
      this.$refs.coder && this.$refs.coder.signError([]);
    },
    // 切换SQL文本粘贴 和 文件上传时清空 XML/SQL语句或者上传文件
    clear() {
      this.sql_text = '';
      this.xml_text = '';
      this.fileList = [];
      this.errorMessage = null;
    },
    // sqlText fileUpload切换
    tabChange(activeKey) {
      this.tabKey = activeKey;
    }
  },
  watch: {
    pane: {
      handler(newVal) {
        if (!_.isEmpty(newVal)) {
          this.formData = { ...newVal };
          this.auditType = newVal.audit_type || 'offline';
          this.formData.audit_type = this.auditType;
          this.coderData = {
            ...newVal,
            content: newVal.content
          };
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.quick-audit-content {
  height: 100%;
  position: static;
  /deep/.ant-spin-container {
    position: static;
    .pane-rs {
      top: 50px;
      height: calc(100% - 50px);
      min-height: calc(100% - 50px);
      .psc-right-query-content-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
        .header-info {
          display: flex;
          justify-content: space-between;
          margin-left: 16px;
          .base-info-form {
            display: flex;
            .ant-row {
              margin-bottom: 0;
              padding-bottom: 12px;
              .ant-form-item-label {
                display: none;
              }
              .ant-col {
                padding-right: 16px;
                .ant-form-item-no-colon {
                  margin-left: 16px;
                  justify-content: flex-start;
                  > span {
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: #27272a;
                    font-weight: 400;
                  }
                }
                .ant-form-item-control {
                  .ant-form-item-children {
                    .ant-radio-group {
                      white-space: nowrap;
                      .ant-radio-button-wrapper-checked {
                        background: #fff;
                        color: #008adc;
                      }
                    }
                    .ant-select {
                      width: 270px !important;
                      .ant-select-selection {
                        min-height: 32px;
                        .ant-select-selection__rendered {
                          line-height: 32px;
                          ul {
                            li {
                              margin-top: 6px;
                            }
                          }
                          .ant-select-selection-selected-value {
                            height: 32px;
                            .biz-instance-item .instance-item-tag {
                              height: 30px;
                              padding: 0px 16px !important;
                              border: none !important;
                              margin-left: -10px;
                              .ant-tag,
                              .database-image {
                                transition: none;
                              }
                            }
                          }
                          .ant-select-arrow {
                            color: #27272a;
                          }
                        }
                      }
                      .ant-select-selection--multiple {
                        padding-bottom: 0;
                      }
                    }
                  }
                }
              }
            }
          }
          .button-area {
            margin: 4px 16px 0 0;
            padding-bottom: 16px;
            white-space: nowrap;
          }
        }
      }
      .psc-right-query-detail-wrapper {
        height: 100%;
        display: flex;
        .result,
        .sql-plan {
          .header {
            padding: 12px 16px;
            height: 48px;
            min-height: 48px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            .title {
              font-family: PingFangSC-Semibold;
              font-size: 14px;
              color: #1f1f1f;
              font-weight: 600;
            }
          }
        }
        .result {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          &.online {
            width: 30%;
          }
          .header {
            justify-content: space-between;
          }
          .content {
            overflow: auto;
            flex-grow: 1;
            .risk {
              padding: 8px 24px;
              .title {
                font-family: PingFangSC-Regular;
                color: #27272a;
                > span {
                  // color: #f5222d;
                  // border: 1px solid #ffa39e;
                  // background: #fff1f0;
                  display: inline-block;
                  margin-left: 8px;
                  width: 22px;
                  height: 22px;
                  border-radius: 4px;
                  font-size: 12px;
                  text-align: center;
                  line-height: 20px;
                }
                .high {
                  color: #f5222d;
                  border: 1px solid #ffa39e;
                  background: #fff1f0;
                }
                .low {
                  color: #faad14;
                  border: 1px solid #ffe58f;
                  background: #fffbe6;
                }
                .no_risk {
                  color: #52c41a;
                  border: 1px solid #b7eb8f;
                  background: #f6ffed;
                }
                .error {
                  display: inline-block;
                  white-space: nowrap;
                  padding: 0 2px;
                  width: auto;
                  color: rgba(0, 0, 0, 0.85);
                  background: #f5f5f5;
                  border: 1px solid #d9d9d9;
                }
              }

              .rule-text {
                padding: 8px 0;
                > span {
                  word-break: break-all;
                }
                .rich-editor-preview {
                  margin: 12px 0;
                  padding: 8px;
                  background-color: #f5f5f5;
                  border-radius: 8px;
                  .hljs {
                    border: none;
                    margin: 0;
                  }
                  h6 {
                    margin-bottom: 0;
                  }
                }
              }
              .err-message {
                padding: 8px 0;
                word-break: break-all;
              }
            }
            // .suggest {
            //   padding: 8px 24px;
            //   .title {
            //     font-family: PingFangSC-Regular;
            //     color: #27272a;
            //   }
            //   .suggest-text {
            //     display: flex;
            //     margin: 16px 0;
            //     .anticon {
            //       font-size: 14px;
            //       margin-top: 4px;
            //       margin-right: 8px;
            //     }
            //     > div {
            //       display: flex;
            //       flex-direction: column;
            //       .rich-editor-preview {
            //         margin-bottom: 12px;
            //         padding: 8px;
            //         background-color: #f5f5f5;
            //         border-radius: 8px;
            //         .hljs {
            //           border: none;
            //           margin: 0;
            //         }
            //         h6 {
            //           margin-bottom: 0;
            //         }
            //       }
            //     }
            //   }
            // }
            .custom-empty {
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
            }
          }
        }
        .sql-plan {
          width: 0%;
          border-left: none;
          display: none;
          &.online {
            width: 70%;
            border-left: 1px solid #e8e8e8;
            display: block;
          }
          // &.offline {
          //   width: 70%;
          //   border-left: 1px solid #d9d9d9;
          // }
          .content {
            // height: 60%;
            // width: 100%;
            // display: flex;
            // align-items: center;
            // justify-content: center;
            // padding: 54px 36px;
          }
        }
        .result-failed {
          width: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #e8e8e8;
          .anticon {
            font-size: 36px;
          }
          > div {
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            padding: 8px 0 0 0;
          }
          > span {
            font-family: PingFangSC-Regular;
            color: rgba(0, 0, 0, 0.45);
          }
        }
        .sql-plan-failed {
          width: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
@media screen and(max-width: 1560px) {
  .quick-audit-content {
    /deep/.ant-spin-container {
      .pane-rs {
        .psc-right-query-content-wrapper {
          .header-info {
            flex-wrap: wrap;
            .base-info-form {
              .ant-row {
                .ant-col {
                  .ant-form-item-control {
                    .ant-form-item-children {
                      .ant-select {
                        width: 220px !important;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
