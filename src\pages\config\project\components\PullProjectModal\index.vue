<template>
  <a-modal
    v-model="visible"
    title="拉取项目"
    wrapClassName="config-project-pull-project-modal"
    width="50%"
    :dialogStyle="{ 'minWidth': '600px', 'maxWidth': '800px' }"
  >
    <a-spin :spinning="spinning">
      <div class="seach-area-content">
        <Form ref="form" v-bind="formParams" :formData="formData" class="fixed-label-left"></Form>
        <div class="seach-area-btns">
          <a-button @click="reset">重置</a-button>
          <a-button type="primary" @click="search">查询</a-button>
        </div>
      </div>
      <Table ref="table" v-bind="tableParams" @selectChange="selectChange"></Table>
    </a-spin>
    <template slot="footer">
      <a-button type="default" @click="hide">关闭</a-button>
      <a-button type="primary" @click="save">拉取</a-button>
    </template>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import Table from '@/components/Table';
import config from './config';
import { pushAppMessage } from '@/api/config/project';
export default {
  components: {
    Form,
    Table
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      spinning: false,
      formData: {},
      formParams: {
        fields: [
          {
            type: 'Select',
            label: '',
            key: 'project_code',
            props: {
              options: [
                { label: '系统编号', value: 0 },
                { label: '英文简称', value: 1 },
                { label: '中文简称', value: 2 }
              ]
            }
          },
          {
            type: 'Input',
            label: '',
            key: 'project_name'
          }
        ],
        multiCols: 2
      },
      tableParams: {
        url: '/sqlreview/faced/get_app_list',
        reqParams: {},
        method: 'post',
        rowKey: 'code',
        rowSelection: {
          onSelectAll: this.onSelectAll
        },
        columns: this.config.columns
      }
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    show() {
      this.visible = true;
      this.$set(this.tableParams, 'reqParams', {
        _t: +new Date()
      });
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    reset() {
      const { form, table } = this.$refs;
      form.resetFields();
      table.refresh();
    },
    search() {
      const params = this.getData();
      this.$set(this.tableParams, 'reqParams', params);
    },
    getData() {
      const { form } = this.$refs;
      const data = form.getData();
      const params = {};
      switch (data.project_code) {
        case 0:
          params.code = data.project_name;
          break;
        case 1:
          params.e_name = data.project_name;
          break;
        case 2:
          params.c_name = data.project_name;
          break;
        default:
          break;
      }
      return params;
    },
    // 表格全选
    onSelectAll(e) {
      this.isSelectAll = e;
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    save() {
      const codes = this.selectedRowKeys || [];
      if (_.isEmpty(codes)) {
        return this.$message.warn('请选择需要拉取的项目');
      }
      this.$showLoading();
      pushAppMessage({ code: codes.join() })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ useMessage: true, tips: '拉取成功' });
            this.hide();
            this.$emit('refresh');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(err => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(err, 'data.message')
          });
        });
    }
  }
};
</script>

<style scoped lang="less">
.seach-area-content {
  display: flex;
  justify-content: space-between;
  .seach-area-btns {
    text-align: right;
    .ant-btn {
      margin-left: 8px;
    }
  }
}
/deep/.ant-row {
  width: 100%;
  display: flex;
  .ant-col-12 {
    min-width: 200px;
  }
}
</style>
