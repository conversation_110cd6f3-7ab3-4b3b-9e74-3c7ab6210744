import Http from '@/utils/request'

export function getProject (params = {}) {
  return Http({
    url: `/sqlreview/project/list`,
    method: 'get',
    data: params
  });
}
export function getNewProject (params = {}) {
  return Http({
    url: `/sqlreview/project/list`,
    method: 'post',
    data: params
  });
}
export function getDataSouce (params = {}) {
  return Http({
    url: `/sqlreview/project/data_source_choices`,
    method: 'get',
    data: params
  });
}
export function getSchema (params = {}) {
  return Http({
    url: `/sqlreview/project/get_schema_list`,
    method: 'get',
    params
  });
}
export function getSchemaEdit (params = {}) {
  return Http({
    url: `/sqlreview/project/get_schema`,
    method: 'get',
    params
  });
}
export function getEditProject (params = {}) {
  return Http({
    url: `/sqlreview/project/edit`,
    method: 'post',
    data: params
  });
}
export function getDleProject (params = {}) {
  return Http({
    url: `/sqlreview/project/edit`,
    method: 'DELETE',
    data: params
  });
}
export function getCheckData (params = []) {
  return Http({
    url: `/sqlreview/project/check_schema_permission/`,
    method: 'post',
    data: params
  });
}

export function getProjectDetail (params = {}) {
  return Http({
    url: `/sqlreview/project/project_detail`,
    method: 'get',
    params: params
  });
}

export function checkProjectUrl (params = {}) {
  return Http({
    url: `/sqlreview/project/check_project_url`,
    method: 'post',
    data: params
  });
}

export function pushAppMessage (params = {}) {
  return Http({
    url: `/sqlreview/faced/get_app_message`,
    method: 'get',
    params: params
  });
}

// 项目导入 项目信息模板下载
export function downloadProjectTemplate(params = {}) {
  return Http({
    url: `/sqlreview/project/download_pj_template`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 项目导入 项目信息模板 提交并获取信息
export function uploadProject(params = {}) {
  return Http({
    url: `/sqlreview/project/upload_project`,
    method: 'post',
    data: params
  });
}

// 项目导入 点击下载错误模板
export function downloadErrorEeport(params = {}) {
  return Http({
    url: `/sqlreview/project/download_error_report`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 项目导出
export function projectDownload(params = {}) {
  return Http({
    url: `/sqlreview/project/download`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}
export default {};