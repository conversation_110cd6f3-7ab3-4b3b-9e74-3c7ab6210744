<template>
  <a-spin class="spin" :spinning="false">
    <a-card type="small" :bordered="false" class="small-card">
      <a-tabs :active-key="activeKey" @change="activeChange" :animated="false">
        <a-tab-pane key="sqlInfo" tab="SQL信息与执行计划">
          <sqlInfo :detaliData="detaliData" :sqlPlan="sql_list.sql_plan  || {}"></sqlInfo>
        </a-tab-pane>
        <a-tab-pane
          key="sqlMap"
          :tab="formatTitle[detaliData.sql_format] || 'SQLMap'"
          v-if="isSqlMap"
        >
          <sqlMap
            :sqlMap="sql_list.sql_map || {}"
            :detaliData="detaliData"
            :formatTitle="formatTitle"
          ></sqlMap>
        </a-tab-pane>
        <a-tab-pane key="sqlException" tab="动态拼接分析" v-if="isDynamicSplicing">
          <sqlException :dynamicSplicing="sql_list.dynamic_splicing || []"></sqlException>
        </a-tab-pane>
        <a-tab-pane key="sqlmapParams" tab="SQL备注">
          <sqlmapParams :sqlMapParamsData="sqlMapParamsData" @saveNote="onSaveNote"></sqlmapParams>
        </a-tab-pane>
        <!-- <a-tab-pane key="aboutSql" tab="表结构信息" v-if="tableData.length > 0">
          <AboutSqlTable
            @showInfoModal="showInfoModal"
            @showAddModal="clickApply"
            :tableData="tableData"
            ref="aboutSql"
          ></AboutSqlTable>
        </a-tab-pane>-->

        <div slot="tabBarExtraContent">
          <!-- 标题 -->
          <span>审核明细</span>
          <div class="about-sql-table-content" @click="showInfoModal">
            <custom-icon type="lu-icon-list" />
            <span>表结构信息</span>
          </div>
        </div>
      </a-tabs>
    </a-card>
    <!-- 统计信息 -->
    <InfoModal @clickApply="clickApply" ref="InfoModal"></InfoModal>
    <!-- 申请表 白名单 -->
    <AddModal ref="addModal" @save="save"></AddModal>
  </a-spin>
</template>

<script>
import AboutSqlTable from '../AboutSqlTable';
import sqlInfo from './components/sqlInfo';
import sqlMap from './components/sqlMap';
import sqlException from './components/sqlException';
import InfoModal from './components/InfoModal';
import AddModal from '@/pages/whiteList/components/AddModal';
import sqlmapParams from '@/components/Biz/ReviewDetail/sqlmapParams';
import Draggable from '@/utils/drag';
import { addWhiteListTable } from '@/api/config/whiteList';
// import { getTableInfo } from '@/api/review';
export default {
  components: {
    sqlInfo,
    sqlMap,
    sqlException,
    InfoModal,
    AddModal,
    AboutSqlTable,
    sqlmapParams
  },
  props: {
    sqlMapParamsData: {
      type: Object,
      default: () => {}
    },
    activeKey: {
      type: String,
      default: 'sqlInfo'
    },
    detaliData: {
      type: Object,
      default: () => {}
    },
    sql_list: {
      type: Object,
      default: () => {}
    },
    id: {
      type: [Number, String]
    },
    tableExistFlag: Boolean
  },
  computed: {
    isSqlMap() {
      const bool = this.sql_list.sql_map && !_.isEmpty(this.sql_list.sql_map);
      return bool;
    },
    isDynamicSplicing() {
      const bool =
        this.sql_list.dynamic_splicing &&
        !_.isEmpty(this.sql_list.dynamic_splicing);
      return bool;
    },
    isSqlMapParams() {
      const bool = this.sqlMapParamsData && !_.isEmpty(this.sqlMapParamsData);
      return bool;
    }
  },
  data() {
    return {
      formatTitle: {
        xml: 'SQLMap',
        sql: 'SQLMap',
        java: 'JAVA代码'
      },
      tableData: []
    };
  },
  mounted() {
    const params = { detail_id: this.$route.params.id };
    this.tableExistFlag && this.getInitData(params);
  },
  methods: {
    getInitData(params = {}) {
      // 发起请求
      this.loading = true;
      // ReqUtil.req({
      //   ctx: this,
      //   reqInstance: getTableInfo,
      //   params: params,
      //   needLoading: false,
      //   cbk: data => {
      //     data = data || {};
      //     this.tableData = (data.table_list || []).map((item, index) => ({
      //       ...item,
      //       id: index
      //     }));
      //   },
      //   err: res => {
      //     this.tableData = [];
      //   }
      // })
      //   .then(() => {
      //     this.$hideLoading({ duration: 0 });
      //     this.loading = false;
      //   })
      //   .catch(e => {
      //     this.loading = false;
      //   });
    },
    activeChange(data) {
      this.$emit('activeChange', data);
    },
    showInfoModal() {
      this.$refs.InfoModal.show(this.id);
      this.setInfoModalDraggable();
    },
    // 弹窗拖动
    setInfoModalDraggable() {
      !this.infoModalDragInstance &&
        setTimeout(() => {
          const modalElement = this.$refs.InfoModal.$el;
          if (modalElement && modalElement.querySelector) {
            this.infoModalDragInstance = new Draggable({
              el: modalElement.querySelector('.ant-modal-content'),
              handler: modalElement.querySelector('.ant-modal-header')
            });
          } else {
            this.setInfoModalDraggable();
          }
        }, 800);
    },
    clickApply(data) {
      this.$refs.addModal.show(data);
    },
    save(payload) {
      addWhiteListTable(payload)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onSaveNote(data) {
      this.$emit('saveNote', data);
    }
  },
  watch: {
    id(newVal) {
      this.getInitData({ detail_id: newVal });
    }
  }
};
</script>

<style lang="less" scoped>
.small-card {
  margin-bottom: 24px;
  border-radius: 8px;
  /deep/.ant-card-body {
    padding: 24px 32px 32px 32px;
  }
  // .title {
  //   font-family: PingFangSC-Semibold;
  //   font-size: 16px;
  //   color: #27272a;
  //   font-weight: 600;
  //   margin: 6px 0 6px 0;
  // }
  /deep/.ant-card-head-title {
    display: flex;
    justify-content: space-between;
    .infoIndex {
      color: #1890ff;
      cursor: pointer;
      font-size: 14px;
    }
  }
  // /deep/ .ant-tabs-extra-content {
  //   font-size: 16px;
  //   color: #333333;
  //   &:hover {
  //     cursor: pointer;
  //     color: #1890ff;
  //   }
  // }
  // /deep/ .ant-tabs {
  //   .ant-tabs-bar {
  //     position: sticky;
  //     top: -70px;
  //     background: #fff;
  //     z-index: 10;
  //   }
  // }
  /deep/ .ant-tabs {
    overflow: visible;
    position: relative;
    .ant-tabs-bar {
      position: sticky;
      top: 0;
      background: #fff;
      z-index: 10;
      display: flex;
      align-items: center;
      border: none;
      padding: 8px 0;
      margin: 0 0 8px 0;
      .ant-tabs-nav-container {
        .ant-tabs-nav-wrap {
          margin-bottom: 0;
          .ant-tabs-tab {
            padding: 7px 16px;
            text-align: center;
            margin-right: 16px;
            border-radius: 16px;
            background: #f4f5f7;
            border: none;
            transition: none;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #71717a;
            font-weight: 400;
            &.ant-tabs-tab-active {
              background: #4db5f2;
              color: #fff;
            }
            &:last-child {
              &::after {
                display: none;
              }
            }
            &:hover {
              background: #4db5f2;
              color: #fff;
            }
          }
          .ant-tabs-ink-bar {
            width: 0 !important;
          }
        }
      }
      .ant-tabs-extra-content {
        margin-right: 54px;
        margin-top: -2px;
        .about-sql-table-content {
          position: absolute;
          top: 8px;
          right: 0;
          font-size: 14px;
          color: #008adc;
          > span {
            font-size: 14px;
            color: #008adc;
          }
          &:hover {
            cursor: pointer;
          }
        }
        > div span {
          font-family: PingFangSC-Semibold;
          font-size: 16px;
          color: #27272a;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
<style lang="less">
// .ant-popover {
//   &.about-sql-table {
//     .ant-popover-content {
//       .ant-popover-inner {
//         .ant-popover-inner-content {
//           max-width: 420px;
//           max-height: 240px;
//           padding: 0;
//         }
//       }
//     }
//   }
// }
</style>