'use strict'
const utils = require('./utils')
const config = require('../config')
// const px2rem = require('postcss-plugin-px2rem');
const isProduction = process.env.NODE_ENV === 'production'
const sourceMapEnabled = isProduction
  ? config.build.productionSourceMap
  : config.dev.cssSourceMap

module.exports = {
  loaders: utils.cssLoaders({
    sourceMap: sourceMapEnabled,
    extract: isProduction
  }),
  cssSourceMap: sourceMapEnabled,
  cacheBusting: config.dev.cacheBusting,
  transformToRequire: {
    video: ['src', 'poster'],
    source: 'src',
    img: 'src',
    image: 'xlink:href'
  },
  /* 配置remUnit */
  // postcss: function () {
  //   return [
  //     px2rem({
  //       rootValue: 100, // 换算基数，默认100，把根标签的font-size规定为1rem为50px,在设计稿上量出多少px直接在代码中写多少px
  //       // unitPrecision: 5, // 保留rem小数点多少位
  //       // propList: ['*'], //  存储将被转换的属性列表，'!font-size' 即不对字体进行rem转换
  //       // selectorBlackList: ['.radius'], // 要忽略并保留为px的选择器，例如fs-xl类名，里面有关px的样式将不被转换，支持正则写法。
  //       // replace: true,
  //       // exclude: function (file) { console.log(file); return false; }, // 可以（reg）利用正则表达式排除某些文件夹的方法，例如/(node_module)/ 。如果想把前端UI框架内的px也转换成rem，请把此属性设为默认值
  //       // mediaQuery: false, // （布尔值）媒体查询( @media screen 之类的)中不生效
  //       // minPixelValue: 12 // 设置要替换的最小像素值，px小于12的不会被转换
  //       // propWhiteList: [],  //默认值是一个空数组，这意味着禁用白名单并启用所有属性
  //       // propBlackList: ['font-size'], //黑名单
  //     })
  //   ];
  // }
  // plugins: ['lodash']
}
