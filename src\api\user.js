import Http from '@/utils/request'

export function userListReqTest(params = {}) {
  return Http({
    url: `/sqlreview/user/`,
    method: 'get',
    params: params
  });
}

export function userDelete(id) {
  return Http({
    url: `/sqlreview/user/${id}/`,
    method: 'delete'
  });
}

export function userAdd(data = {}) {
  return Http({
    url: `/sqlreview/user/`,
    method: 'post',
    data: data
  });
}

export function updateUserRole(data = {}, id) {
  return Http({
    url: `/sqlreview/user/${id}/`,
    method: 'put',
    data: data
  });
}

export function resetPassword(data = {}) {
  return Http({
    url: `/sqlreview/reset-psw/`,
    method: 'post',
    data: data
  });
}

export function newGroup(data = {}) {
  return Http({
    url: `/sqlreview/user-group/`,
    method: 'post',
    data: data
  });
}

export function deleteGroup(id) {
  return Http({
    url: `/sqlreview/user-group/${id}/`,
    method: 'delete'
  });
}

export function editGroup(data = {}, id) {
  return Http({
    url: `/sqlreview/user-group/${id}/`,
    method: 'PUT',
    data: data
  });
}
// 添加组员
export function addMember(data = {}) {
  return Http({
    url: `/sqlreview/user-group-member/`,
    method: 'post',
    data: data
  });
}
// 删除组员
export function deleteMember(data) {
  return Http({
    url: `/sqlreview/user-group-member/ `,
    method: 'DELETE',
    data: data
  });
}

// 角色相关
export function roleCreate (data) {
  return Http({
    url: '/sqlreview/project_config/role/create',
    method: 'post',
    data
  })
}

export function roleDetail (data) {
  return Http({
    url: '/sqlreview/project_config/role/info',
    method: 'get',
    params: data
  })
}

export function roleUpdate (data) {
  return Http({
    url: '/sqlreview/project_config/role/update',
    method: 'post',
    data
  })
}

export function roleRemove (data) {
  return Http({
    url: '/sqlreview/project_config/role/delete',
    method: 'post',
    data
  })
}
export default {};
