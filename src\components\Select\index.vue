<template>
  <a-select
    v-bind="selectProps"
    v-on="selectListeners"
    ref="select"
    :class="className"
    :value="selectValue"
    :loading="loading"
    :filter-option="filterOption"
    :open="$attrs.open != null ? $attrs.open : dropdownVisible"
    @search="handleSearch"
    @blur="handleBlur"
    @dropdownVisibleChange="onDropdownVisibleChange"
  >
    <a-select-option
      v-for="item in data"
      :key="item.value"
      v-bind="item"
      :title="item.label"
    >
      <slot
        v-if="
          optionItemSlot && !['search_tips', 'limit_tips'].includes(item.value)
        "
        :name="optionItemSlot"
        v-bind="{ data: getSourceItem(item.value) }"
      ></slot>
      <template v-else>{{ item.label }}</template>
    </a-select-option>
    <!-- 自定义 -->
    <div slot="dropdownRender" slot-scope="menu">
      <v-nodes :vnodes="menu" v-if="useManualDropdown !== true" />
      <slot
        :name="extraSlot"
        :value="selectValue"
        :data="{ searchValue, dataSource: data }"
      ></slot>
    </div>
  </a-select>
</template>

<script>
// import _ from 'lodash';
// import Http from '@/utils/request';
import Request from './request';
import _ from 'lodash';

const defaultProps = {
  placeholder: '请选择',
  dropdownMatchSelectWidth: false,
  allowClear: true,
  showSearch: true,
  getPopupContainer: el => {
    // return document.getElementById('rootContent');
    return el.parentNode;
  }
};
export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  props: {
    value: [String, Number, Boolean, Object, Array],
    url: String,
    method: String,
    reqParams: Object,
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    disabledKeys: {
      type: Array,
      default: () => []
    },
    separator: {
      type: String,
      default: ''
    },
    noCache: {
      type: Boolean,
      default: true
    },
    maxTags: Number,
    beforeLoaded: Function,
    loaded: Function,
    backSearch: {
      type: Boolean,
      default: false
    },
    // 后端搜索只在搜索时触发
    backSearchOnlyOnSearch: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: 30
    },
    frontLimit: {
      type: Boolean,
      default: false
    },
    options: Array,
    optionItemSlot: String,
    extraSlot: {
      type: String,
      default: 'extra'
    },
    // 初始请求并仅有一项数据时默认选中第一项
    chooseFirstItem: {
      type: Boolean,
      default: false
    },
    useManualDropdown: Boolean
  },
  data() {
    this.sourceData = [];
    this.search = _.debounce(() => {
      this.refresh();
    }, 800);
    // 正在选取item
    this.choosingItem = false;
    return {
      loading: false,
      filterOption: !this.backSearch && !this.frontLimit,
      data: [],
      dropdownVisible: false,
      searchValue: ''
    };
  },
  computed: {
    selectProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    selectListeners() {
      return { ...this.$listeners, ...{ change: this.handleChange } };
    },
    className() {
      const { mode, labelInValue } = this.selectProps;
      let arr = [];
      if (mode === 'tags' && labelInValue !== true && this.maxTags === 1) {
        arr = ['no-animate'];
      }
      return ['custom-select'].concat(arr);
    },
    selectValue() {
      const { mode, labelInValue } = this.selectProps;
      if (
        (mode === 'tags' || mode === 'multiple') &&
        this.separator &&
        labelInValue !== true &&
        _.isString(this.value)
      ) {
        if (mode === 'tags' && this.maxTags === 1) {
          return this.value === '' ? undefined : this.value;
        }
        const arr = this.value.split(this.separator);
        return this.data
          .filter(item => arr.find(key => key == item.value))
          .map(item => item.value);
      }
      return this.value;
    }
  },
  created() {
    const { options } = this;
    // 有options，优先使用传入的options
    if (options && options.length > 0) {
      this.sourceData = options;
      if (this.frontLimit) {
        // 前端限制
        this.frontFilter();
      } else {
        this.data = options;
      }
      return;
    }
    this.getList();
  },
  mounted() {},
  methods: {
    // 模拟请求
    getList(refresh = false) {
      const { url, method, reqParams = {}, noCache } = this;
      // const { mode, labelInValue } = this.selectProps;
      if (!url) return;
      // console.log(this.searchValue, value, 888);
      this.loading = true;
      let selectedKeys = this.getSeletedKeys();
      let searchParams = this.backSearch
        ? {
            search_value: this.searchValue,
            search_key: selectedKeys,
            limit: this.limit
          }
        : {};
      Request({
        url,
        method,
        reqParams: {
          ...reqParams,
          ...searchParams
        },
        noCache,
        refresh
      })
        .then(res => {
          this.loading = false;
          if (this.beforeLoaded) {
            res = this.beforeLoaded(res);
          }
          let _sourceData = res;
          let _data = res
            // .filter(item => item.visible !== false)
            .map(item => {
              const isDisabled =
                item.disabled ||
                this.disabledKeys.includes(_.get(item, this.valueKey));
              return {
                ...item, // 融合元数据，方便扩展 (2023.5.25)
                label: _.get(item, this.labelKey),
                value: _.get(item, this.valueKey),
                disabled: isDisabled,
                class: item.visible === false ? 'hide' : ''
              };
            });
          // 后端搜索特殊处理选项
          if (this.backSearch) {
            let selectedOptions =
              this.data.filter(item =>
                (selectedKeys || '').split(',').find(itm => item.value == itm)
              ) || [];
            let selectedSourceOptions =
              this.sourceData.filter(item =>
                (selectedKeys || '')
                  .split(',')
                  .find(itm => item[this.valueKey] == itm)
              ) || [];
            _data = _.uniqBy([...selectedOptions, ..._data], 'value');
            _data.push({
              label: '在光标闪烁处输入关键字搜索更多选项',
              value: 'search_tips',
              disabled: true,
              class: 'custom-select-search-tips'
            });
            _sourceData = _.uniqBy(
              [...selectedSourceOptions, ..._sourceData],
              this.valueKey
            );
          }
          this.data = _data;
          this.sourceData = _sourceData;
          if (!this.backSearch && this.frontLimit) {
            // 前端限制
            this.frontFilter();
          }
          if (_.isFunction(this.loaded)) {
            this.loaded(this.sourceData, this);
          }
          if (this.chooseFirstItem && this.selectValue == null) {
            this.chooseFirst();
          }
        })
        .catch(e => {
          console.log(e);
          this.loading = false;
          this.sourceData = [];
          this.data = [];
          // this.value = undefined;
          this.$emit('change', undefined);
          // this.$message.error(e || '请求失败');
          this.$hideLoading({ method: 'error', tips: e || '请求失败' });
        });
    },
    refresh() {
      this.getList(true);
    },
    chooseFirst() {
      if (this.sourceData.length > 1) {
        return;
      }
      const value = _.get(this.data, '0.value') || undefined;
      this.handleChange(value);
    },
    handleBlur() {
      const { mode } = this.selectProps;
      if (mode === 'multiple' && this.backSearch && this.data.length <= 1) {
        this.handleSearch();
      }
    },
    // 监听变化
    handleChange(value) {
      // console.log(value, 'change');
      let emitValue = value;
      const { mode, labelInValue } = this.selectProps;
      if (mode === 'tags' && this.maxTags > 0) {
        emitValue = emitValue.slice(-this.maxTags);
      }
      if (
        (mode === 'tags' || mode === 'multiple') &&
        this.separator &&
        labelInValue !== true
      ) {
        emitValue = emitValue.join(this.separator);
      }
      // 特殊处理visible
      const matchItem = this.getSourceItem(value);
      if (matchItem && matchItem.visible === false) {
        emitValue = undefined;
      }
      this.$emit('change', emitValue);
      // 搜索模式，处理options
      if (this.backSearch) {
        if (this.backSearchOnlyOnSearch) {
          this.choosingItem = true;
          this.$nextTick(() => {
            this.choosingItem = false;
          });
        } else {
          this.handleSearch();
        }
      }
      // 隐藏
      if (mode === 'tags' && this.maxTags == 1) {
        this.dropdownVisible = false;
      }
    },
    handleSearch(value) {
      // console.log(value, 'search');
      if (this.backSearch) {
        // 防抖刷新
        this.searchValue = value;
        this.search();
      } else if (this.frontLimit) {
        // 前端限制
        this.frontFilter(value);
      }
      this.$emit('search', value);
    },
    // 获取元数据item
    getSourceItem(key) {
      return this.sourceData.find(item => item[this.valueKey] == key);
    },
    // 根据value，获取selectedKeys
    getSeletedKeys() {
      const { value } = this;
      const { mode, labelInValue } = this.selectProps;
      let selectedKeys =
        mode === 'tags' || mode === 'multiple'
          ? this.separator && _.isString(value)
            ? value
            : labelInValue
            ? (value || []).map(itm => itm.key).join(',')
            : (value || []).join(',')
          : labelInValue
          ? (value || {}).key
          : value;
      selectedKeys = _.isNumber(selectedKeys)
        ? selectedKeys + ''
        : selectedKeys;

      return selectedKeys;
    },
    // 下拉菜单显示隐藏
    onDropdownVisibleChange(open) {
      this.dropdownVisible = open;
      if (open && this.frontLimit) {
        // 前端限制
        this.frontFilter();
      }
    },
    exec(method, ...args) {
      const { select } = this.$refs;
      if (select && _.isFunction(select[method])) {
        select[method](...args);
      }
    },
    frontFilter(searchValue) {
      const sourceData = this.sourceData || [];
      const selectedKeys = (this.getSeletedKeys() || '').split(',');
      const selectedItems = [];
      // 前端限制
      const res = sourceData.filter(item => {
        if (selectedKeys.find(itm => itm == item.value)) {
          selectedItems.push(item);
        }
        return (
          item.visible !== false &&
          (searchValue == null ||
            item.label.match(new RegExp(searchValue + '', 'i')))
        );
      });
      // console.log(selectedKeys, selectedItems);
      this.data =
        res.length > this.limit
          ? [
              ..._.uniqBy([...selectedItems, ...res], 'value').slice(
                0,
                this.limit
              ),
              {
                label: `*最多显示${this.limit}项，可搜索`,
                value: 'limit_tips',
                disabled: true,
                class: 'custom-limit-tips'
              }
            ]
          : res;
    },
    isValueEmpty(val) {
      return val !== 0 && _.isEmpty(val);
    }
  },
  watch: {
    url: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.refresh();
        } else {
          this.data = this.options || [];
          this.sourceData = this.options || [];
        }
      }
      // immediate: true,
      // deep: true
    },
    reqParams: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.refresh();
        }
      }
      // immediate: true,
      // deep: true
    },
    options: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.sourceData = newVal;
          if (this.frontLimit) {
            // 前端限制
            this.frontFilter();
          } else {
            this.data = newVal;
          }
        }
      }
      // immediate: true,
      // deep: true
    },
    value(newVal) {
      // console.log('get value', newVal);
      if (this.backSearch) {
        if (
          this.isValueEmpty(newVal) ||
          !this.choosingItem ||
          !this.backSearchOnlyOnSearch
        ) {
          this.handleSearch();
        }
      }
    },
    disabledKeys(newVal = []) {
      this.data = this.data.map(item => {
        item.disabled = newVal.includes(item.value);
        return item;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.custom-select {
  &.no-animate {
    transition: none !important;
    animation: none !important;
    /deep/ * {
      transition: none !important;
      animation: none !important;
    }
  }
}
</style>
<style lang="less">
.custom-select-search-tips {
  color: @primary-color !important;
}
.custom-limit-tips {
  color: rgba(0, 0, 0, 0.45) !important;
}
</style>
