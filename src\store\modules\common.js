export default {
  namespaced: true,
  state: {
    navi: [], // 导航信息
    theme: '',
    mode: '',
    layout: '',
    searchCache: {},
    tableCache: {},
    pageCache: {},
    quickEntrance: []
  },
  mutations: {
    setNavi(state, navi = []) {
      state.navi = navi;
    },
    setTheme(state, theme = '') {
      state.theme = theme;
    },
    setMode(state, mode = '') {
      state.mode = mode;
    },
    setLayout(state, layout = '') {
      state.layout = layout;
    },
    setSearchCache(state, val = {}) {
      _.forEach(val, (value, key) => {
        if (_.isEmpty(value)) {
          val[key] = null;
        }
      });
      state.searchCache = { ...state.searchCache, ...val };
    },
    setTableCache(state, val = {}) {
      _.forEach(val, (value, key) => {
        if (_.isEmpty(value)) {
          val[key] = null;
        }
      });
      state.tableCache = { ...state.tableCache, ...val };
      // console.log(state.tableCache)
    },
    setPageCache(state, val = {}) {
      _.forEach(val, (value, key) => {
        if (_.isEmpty(value)) {
          val[key] = null;
        }
      });
      state.pageCache = { ...state.pageCache, ...val };
      // console.log(state.pageCache)
    },
    setQuickEntrance(state, quickEntrance = []) {
      state.quickEntrance = quickEntrance;
    },
    clearCache(state, val = {}) {
      state.searchCache = {};
      state.tableCache = {};
      state.pageCache = {};
      console.log('清空缓存');
    },
    clearCacheByKeys(state, keys = []) {
      keys.forEach((key) => {
        state.searchCache[key] = null;
        state.tableCache[key] = null;
        state.pageCache[key] = null;
      });
      console.log('清空缓存', keys);
    }
  }
};
