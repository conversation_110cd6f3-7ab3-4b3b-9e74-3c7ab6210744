<template>
  <div class="config-system-form">
    <Form ref="form" class="form-box" v-bind="formParams" :formData="formData"></Form>
  </div>
</template>

<script>
import Form from '@/components/Form';
export default {
  components: { Form },
  props: {
    data: Object
  },
  data() {
    return {
      formParams: {
        fixedLabel: true,
        gutter: 32,
        layout: 'horizontal',
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        colon: true,
        fields: [
          {
            type: 'TableEdit',
            label: 'ip',
            key: 'sql_review_dynamic_form_element',
            width: '100%',
            getDataMethod: 'getData',
            validateMethod: 'validate',
            resetFieldsMethod: 'resetFields',
            initialValue: [],
            props: {
              columns: [
                {
                  title: '',
                  dataIndex: 'element_name',
                  key: 'element_name',
                  width: 200,
                  scopedSlots: { customRender: 'element_name' }
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 100,
                  scopedSlots: { customRender: 'action' }
                }
              ],
              editConfig: {
                element_name: (row, record = {}) => {
                  return {
                    type: 'Input',
                    props: {},
                    rules: [{ required: true, message: '该项为必填项' }]
                  };
                }
              },
              rowKey: 'id',
              initEditStatus: true,
              pagination: false,
              leastNum: 1,
              showHeader: false,
              actionBtns: ['add', 'remove'],
              actionBtnsIcons: {
                add: 'plus-circle',
                remove: 'close-circle'
              }
            },
            rules: [{ required: false, message: '该项为必填项' }]
          }
        ]
      },
      formData: {}
    };
  },
  watch: {
    data: {
      handler(newVal) {
        this.formData = newVal;
      },
      immediate: true
    }
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    getData() {
      const { form } = this.$refs;
      const data = form.getData();
      return data;
    }
  }
};
</script>

<style lang="less" scoped>
.config-syste-form {
  margin-top: 20px;
  // margin-left: 20px;
}
/deep/ .ant-form-item-label {
  padding-top: 12px;
}
</style>
