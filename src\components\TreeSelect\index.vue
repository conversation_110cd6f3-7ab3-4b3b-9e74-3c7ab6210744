<template>
  <a-tree-select
    v-bind="selectProps"
    v-on="selectListeners"
    :class="className"
    :value="selectValue"
    :treeData="data"
    dropdownClassName="custom-tree-select-drapdown"
  ></a-tree-select>
</template>

<script>
// import _ from 'lodash';
// import Http from '@/utils/request';
import Request from './request';
import _ from 'lodash';

const defaultProps = {
  placeholder: '请选择',
  // dropdownMatchSelectWidth: false,
  allowClear: true
  // getPopupContainer: el => {
  //   // return document.getElementById('rootContent');
  //   return el.parentNode;
  // }
};
export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {},
  props: {
    value: [String, Number, Boolean, Object, Array],
    url: String,
    reqParams: Object,
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    // separator: {
    //   type: String,
    //   default: ''
    // },
    noCache: {
      type: Boolean,
      default: true
    },
    // maxTags: Number,
    loaded: Function,
    beforeLoaded: Function,
    // request报错时的回调，报错后如需其他操作可使用
    loadedError: Function
  },
  data() {
    this.sourceData = [];
    return {
      loading: false,
      data: []
    };
  },
  computed: {
    selectProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    selectListeners() {
      return { ...this.$listeners, ...{ change: this.handleChange } };
    },
    className() {
      // const { mode, labelInValue } = this.selectProps;
      let arr = [];
      // if (mode === 'tags' && labelInValue !== true && this.maxTags === 1) {
      //   arr = ['no-animate'];
      // }
      return ['custom-tree-select'].concat(arr);
    },
    selectValue() {
      // const { mode, labelInValue } = this.selectProps;
      // if (
      //   (mode === 'tags' || mode === 'multiple') &&
      //   this.separator &&
      //   labelInValue !== true &&
      //   _.isString(this.value)
      // ) {
      //   if (mode === 'tags' && this.maxTags === 1) {
      //     return this.value === '' ? undefined : this.value;
      //   }
      //   const arr = this.value.split(this.separator);
      //   return this.data
      //     .filter(item => arr.find(key => key == item.value))
      //     .map(item => item.value);
      // }
      return this.value;
    }
  },
  created() {
    const { treeData } = this.selectProps;
    // 有treeData，优先使用传入的treeData
    if (treeData && treeData.length > 0) {
      this.data = treeData;
      return;
    }
    this.getList();
  },
  mounted() {},
  methods: {
    // 模拟请求
    getList(refresh = false) {
      const { url, reqParams, noCache } = this;
      if (!url) return;
      this.loading = true;
      Request({
        url,
        reqParams,
        noCache,
        refresh
      })
        .then(res => {
          this.loading = false;
          let dataSource = res;
          if (this.beforeLoaded) {
            dataSource = this.beforeLoaded(res);
          }
          this.sourceData = dataSource;
          this.data = this.preDeal(dataSource);
          if (_.isFunction(this.loaded)) {
            this.loaded(this.sourceData);
          }
        })
        .catch(e => {
          this.loading = false;
          this.sourceData = [];
          this.data = [];
          // this.value = undefined;
          this.$emit('change', undefined);
          // this.$message.error(_.get(e, 'response.data.message') || '请求失败');
          this.loadedError
            ? this.loadedError(e.source)
            : this.$hideLoading({
                method: 'error',
                tips: e.msg || '请求失败'
              });
        });
    },
    preDeal(list = []) {
      return (
        list
          // .filter(item => item.visible !== false)
          .map(item => {
            const children = this.preDeal(item.children);
            let newItem = {
              ...item,
              label: _.get(item, this.labelKey),
              value: _.get(item, this.valueKey),
              class: item.visible === false ? 'hide' : '',
              children: children
            };
            return newItem;
          })
      );
    },
    refresh() {
      this.getList(true);
    },
    // 监听变化
    handleChange(value) {
      let emitValue = value;
      // const { mode, labelInValue } = this.selectProps;
      // if (mode === 'tags' && this.maxTags > 0) {
      //   emitValue = emitValue.slice(-this.maxTags);
      // }
      // if (
      //   (mode === 'tags' || mode === 'multiple') &&
      //   this.separator &&
      //   labelInValue !== true
      // ) {
      //   emitValue = emitValue.join(this.separator);
      // }
      // 特殊处理visible
      const matchItem = this.getSourceItem(value);
      if (matchItem && matchItem.visible === false) {
        emitValue = undefined;
      }
      this.$emit('change', emitValue);
    },
    // 获取元数据item
    getSourceItem(key) {
      return this.sourceData.find(item => item[this.valueKey] == key);
    }
  },
  watch: {
    url: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.refresh();
        } else {
          this.data = this.$attrs.treeData || [];
        }
      }
      // immediate: true,
      // deep: true
    },
    reqParams: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.refresh();
        }
      }
      // immediate: true,
      // deep: true
    },
    '$attrs.treeData': {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.data = newVal;
        }
      }
      // immediate: true,
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.custom-tree-select {
  &.no-animate {
    transition: none !important;
    animation: none !important;
    /deep/ * {
      transition: none !important;
      animation: none !important;
    }
  }
}
</style>
<style lang="less">
.custom-tree-select-drapdown {
  &.ant-select-tree-dropdown {
    .ant-select-tree {
      max-height: 250px;
      overflow: auto;
    }
  }
}
</style>
