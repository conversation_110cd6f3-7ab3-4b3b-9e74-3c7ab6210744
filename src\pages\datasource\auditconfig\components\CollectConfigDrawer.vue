<template>
  <!-- 新建项目抽屉 -->
  <a-drawer
    title="数据库审核配置"
    :visible="visible"
    @close="hide"
    width="730px"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="config-collect-config-drawer"
  >
    <!-- 内容区域 -->
    <a-spin :spinning="spinning">
      <!-- 采集配置 -->
      <div class="item">
        <div class="title">采集配置</div>
        <!-- 数据源选择OB时 新增选项 -->
        <div
          v-if="['OB_ORACLE', 'OB_MYSQL'].includes(dbType)"
          class="ob-extra-form"
        >
          <div class="des">
            <custom-icon type="info-circle" theme="filled" />
            <span
              >OceanBase数据库审核需要连接到sys系统租户进行数据库执行SQL采集，如需使用数据库审核功能，请配置sys系统租户账号密码</span
            >
          </div>
          <Form
            class="ob-form"
            ref="obForm"
            v-bind="obParams"
            :formData="obData"
          ></Form>
        </div>
        <!-- 权限校验失败后 显示 -->
        <div class="tips-content" v-if="!isPass">
          <div class="message"><a-icon type="close-circle" />{{ message }}</div>
          <div class="sql-text">
            <pre>{{ sqlText }}</pre>
          </div>
        </div>
        <div class="extra-form-box">
          <Form
            class="extra-form"
            ref="form"
            v-bind="formParams"
            :formData="formData"
          >
            <JCronModal
              :data="collectFrequency"
              ref="JCronModal"
              slot="frequency"
              @cronExpression="cronExpression"
            ></JCronModal>
          </Form>
          <div
            class="extra-columns-box"
            v-if="
              collectType &&
              collectType == 'jdbc' &&
              !['GOLDENDB', 'POSTGRE'].includes(dbType)
            "
          >
            <div class="extra-columns-radio">
              <span>附加字段采集配置</span>
              <a-radio-group
                :options="options"
                v-model="extraStatus"
                @change="onChange"
              />
            </div>
            <div class="extra-columns-desc" v-if="extraStatus == 1">
              <custom-icon type="exclamation-circle" />
              <span
                >检测到该版本MySQL数据库information
                schema.processlisk存在额外附加字段，是否要对这些字段进行采集?</span
              >
            </div>
            <div class="extra-columns-table" v-if="extraStatus == 1">
              <TableEdit
                ref="tableEdit"
                v-bind="tableParams || {}"
                :dataSource="dataSource || []"
              ></TableEdit>
            </div>
          </div>
        </div>
      </div>
      <!-- 自动审核配置 -->
      <div
        class="item"
        v-if="
          ['MYSQL', 'GOLDENDB', 'OB_ORACLE', 'ORACLE', 'POSTGRE'].includes(
            dbType
          )
        "
      >
        <div class="title">自动审核配置</div>
        <div class="auto-review-form-box">
          <Form
            class="auto-review-form"
            ref="autoReviewForm"
            v-bind="autoReviewParams" 
            :formData="autoReviewData"
          ></Form>
        </div>
      </div>
      <div
        class="item"
        v-if="
          ['MYSQL', 'GOLDENDB', 'OB_ORACLE', 'ORACLE', 'POSTGRE'].includes(
            dbType
          )
        "
      >
        <div class="title">argus和realtime采集开关</div>
        <div class="auto-review-form-box">
          <Form
            class="auto-review-form"
            ref="argus_realtime_form"
            v-bind="argus_realtime_params"
            :formData="argus_realtime_data"
          ></Form>
        </div>
      </div>
    </a-spin>
    <!-- 按钮区域 -->
    <div
      class="btns-area"
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 10
      }"
    >
      <a-button @click="checkOpenApi" v-if="collectType == 'openapi'"
        >连接测试</a-button
      >
      <a-button
        @click="checkOb"
        v-if="['OB_ORACLE', 'OB_MYSQL'].includes(dbType)"
        >连接测试</a-button
      >
      <a-divider
        type="vertical"
        v-if="
          collectType == 'openapi' || ['OB_ORACLE', 'OB_MYSQL'].includes(dbType)
        "
      />
      <a-button @click="hide" class="hide-btn">取消</a-button>
      <a-button
        @click="onCheckPermisson"
        type="primary"
        :disabled="
          (collectType == 'openapi' && !flag) ||
          (['OB_ORACLE', 'OB_MYSQL'].includes(dbType) && !flag)
        "
        >保存</a-button
      >
    </div>
  </a-drawer>
</template>

<script>
import DataSourceBlocks from '@/components/Biz/DataSourceBlocks';
import TableEdit from '@/components/TableEdit';
import JCronModal from '@/components/Biz/JCronModal';
import config from './config';
import Form from '@/components/Form';
import { checkOpenApi, getExtraColumns } from '@/api/config/dataSource';
import {
  getCollectConfig,
  testObCollectConfig,
  checkPermission
} from '@/api/databaseaudit/topsql';
import { saveCollectConfig } from '@/api/databaseaudit/auditconfig';
export default {
  components: {
    DataSourceBlocks,
    Form,
    TableEdit,
    JCronModal
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      spinning: false,
      formData: {
        collect_type: 0
      },
      collectType: 0,
      formParams: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 12 },
        fields: []
      },
      dataSource: [],
      tableParams: {
        initEditStatus: true,
        actionBtns: [],
        editConfig: this.config.editConfig(),
        columns: this.config.columns,
        pagination: false,
        rowKey: 'id'
      },
      obData: {},
      obParams: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 16 },
        fields: this.config.collectObInfo()
      },
      autoReviewData: {
        // review_frequency: 12,
        // top_strategy: 'execution_total_time',
        // top_sort: 5,
        // filter_day: 12,
        // auto_review_status: 1
      },
      autoReviewParams: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 12 },
        fields: []
      },
      argus_realtime_data:{},
      argus_realtime_params: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 12 },
        fields: []
      },
      id: null,
      title: null,
      openApiType: null,
      flag: false, // 阿里云校验成功 才能保存
      extraStatus: 0,
      options: [
        { label: '不采集', value: 0 },
        { label: '采集', value: 1 }
      ],
      captureEnumOptions: [],
      afteAuditStatus: null,
      dbType: '',
      collectFrequency: '',
      message: '',
      sqlText: '',
      isPass: true
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(record) {
      this.visible = true;
      this.id = record.id;
      this.dbType = record.db_type;

      this.spinning = true;
      getCollectConfig({
        data_source_id: this.id
      })
        .then((res) => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            const collectType = _.get(resData, 'collect_type');
            this.formData = { ...resData };
            this.obData = { ...resData };
            this.argus_realtime_data = { ...resData };
            this.autoReviewData = { ...resData };
            this.openApiType = _.get(resData, 'open_api_type');
            this.extraStatus = _.get(resData, 'extra_status');
            const dataSource = _.get(resData, 'extra_column');
            this.collectFrequency = _.get(resData, 'collect_frequency');
            if (!_.isEmpty(dataSource) && this.dbType == 'MYSQL') {
              getExtraColumns({ data_source_id: this.id })
                .then((res) => {
                  if (CommonUtil.isSuccessCode(res)) {
                    const resData = _.get(res, 'data.data');

                    const captureEnumOptions = resData[0].capture_enum;
                    this.$set(
                      this.tableParams,
                      'editConfig',
                      this.config.editConfig(captureEnumOptions)
                    );
                    this.dataSource = dataSource;
                    this.$hideLoading({ duration: 0 });
                  } else {
                    this.$hideLoading({
                      method: 'error',
                      tips: _.get(res, 'data.message')
                    });
                  }
                })
                .catch((e) => {
                  this.$hideLoading({
                    method: 'error',
                    tips: _.get(e || {}, 'response.data.message') || '请求失败'
                  });
                });
            }
            switch (this.dbType) {
              case 'ORACLE':
                this.$set(
                  this.formParams,
                  'fields',
                  this.config.oracleFields()
                );
                this.$set(
                  this.autoReviewParams,
                  'fields',
                  this.config.autoReviewFields('ORACLE')
                );
                this.$set(
                  this.argus_realtime_params,
                  'fields',
                  this.config.argus_realtime('ORACLE')
                );
                break;
              case 'MYSQL':
              case 'GOLDENDB':
                this.$set(
                  this.formParams,
                  'fields',
                  this.config.fields(this.dbType)
                );
                this.$set(
                  this.autoReviewParams,
                  'fields',
                  this.config.autoReviewFields()
                );
                this.$set(
                  this.argus_realtime_params,
                  'fields',
                  this.config.argus_realtime()
                );
                break;
              case 'POSTGRE':
                this.$set(
                  this.formParams,
                  'fields',
                  this.config.pgFields(this.id)
                );
                this.$set(
                  this.autoReviewParams,
                  'fields',
                  this.config.autoReviewFields('POSTGRE')
                );
                this.$set(
                  this.argus_realtime_params,
                  'fields',
                  this.config.argus_realtime('ORACLE')
                );
                break;
              case 'OB_ORACLE':
                this.$set(
                  this.obParams,
                  'fields',
                  this.config.collectObInfo(
                    resData.examine_user ? 'edit' : 'add'
                  )
                );
                this.$set(this.formParams, 'fields', this.config.obFields());
                this.$set(
                  this.autoReviewParams,
                  'fields',
                  this.config.autoReviewFields()
                );
                this.$set(
                  this.argus_realtime_params,
                  'fields',
                  this.config.argus_realtime()
                );
               
                break;
              case 'OB_MYSQL':
                this.$set(
                  this.obParams,
                  'fields',
                  this.config.collectObInfo(
                    resData.examine_user ? 'edit' : 'add'
                  )
                );
                this.$set(this.formParams, 'fields', this.config.obFields());
                break;
              case 'DB2':
                this.$set(this.formParams, 'fields', this.config.db2Fields());
                break;
              case 'GAUSSDB':
                this.$set(
                  this.formParams,
                  'fields',
                  this.config.gaussdbFields()
                );
                break;
              default:
                break;
            }
            this.$set(this, 'collectType', collectType);
            this.spinning = false;
            this.$hideLoading({ duration: 0 });
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch((e) => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    hide() {
      this.visible = false;
      this.isPass = true;
      this.message = '';
      this.sqlText = '';
      this.formData = {
        collect_type: 0
      };
      this.collectType = 0;
      this.collectFrequency = '';
      this.flag = false;
      const { form, obForm } = this.$refs;
      form.resetFields();
      obForm && obForm.resetFields();
    },
    getData() {},

    // 保存
    save() {
      const { form, tableEdit, JCronModal, obForm, autoReviewForm,argus_realtime_form } =
        this.$refs;
      const JCronForm = JCronModal && JCronModal.$refs.form;
      Promise.all([
        form.validate(),
        JCronModal && JCronForm.validate(),
        obForm && obForm.validate(),
        autoReviewForm && autoReviewForm.validate(),
        argus_realtime_form && argus_realtime_form.validate()
      ]).then((valid) => {
        if (valid) {
          // this.spinning = true;
          let formData = {};
          let obData = {};
          let autoReviewData = {};
          let argus_realtime_data = {};
          if (JCronModal) {
            JCronModal.handleSubmit();
          }
          if (form) {
            formData = form.getData();
            formData.ssh_pwd = Base64.encode(formData.ssh_pwd || '');
          }
          if (obForm) {
            obData = obForm.getData();
            obData.examine_pwd = Base64.encode(obData.examine_pwd || '');
          }
          if (autoReviewForm) {
            autoReviewData = autoReviewForm.getData();
          }
          if (argus_realtime_form) {
            argus_realtime_data = argus_realtime_form.getData();
          }
          const tableData = (tableEdit && tableEdit.getData()) || [];
          const params = {
            ...this.formData,
            ...formData,
            ...obData,
            ...autoReviewData,
            ...argus_realtime_data,
            extra_column: [...tableData],
            data_source_id: this.id,
            collect_type: this.collectType,
            extra_status: this.extraStatus
          };

       

          // console.log(
          //   this.autoReviewData.review_frequency,
          //   this.autoReviewData.auto_review_status,
          //   params.review_frequency,
          //   params.auto_review_status
          // );

          // console.log(params);

          saveCollectConfig(params)
            .then((res) => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({
                  useMessage: true,
                  tips: '操作成功'
                });
                this.spinning = false;
                this.$hideLoading({ duration: 0 });
                this.hide();
                this.$emit('save');
              } else {
                this.spinning = false;
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch((e) => {
              this.spinning = false;
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    onCheckPermisson() {
      let formData = {};
      if (this.dbType == 'MYSQL') {
        formData = this.$refs.form.getData();
      }
      checkPermission({
        data_source_id: this.id,
        after_audit_status: formData.after_audit_status
      })
        .then((res) => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ duration: 0 });
            this.isPass = true;
            this.save();
            // 权限验证不通过用4001
          } else if (_.get(res, 'data.code') == 4001) {
            this.isPass = false;
            this.sqlText = _.get(res, 'data.data.sql_text');
            this.message = _.get(res, 'data.message');
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch((e) => {
          this.isPass = false;
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    checkOpenApi() {
      const { form } = this.$refs;
      Promise.all([form.validate()]).then((valid) => {
        if (valid) {
          this.$showLoading();
          const data = form.getData();
          const pararms = { ...data };
          checkOpenApi(pararms)
            .then((res) => {
              if (CommonUtil.isSuccessCode(res)) {
                this.flag = res.data.data.flag || false;
                this.$hideLoading({
                  useMessage: true,
                  tips: '校验成功'
                });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch((e) => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    checkOb() {
      const { form, obForm } = this.$refs;
      Promise.all([form.validate(), obForm.validate()]).then((valid) => {
        if (valid) {
          this.$showLoading();
          const data = form.getData();
          const obData = obForm.getData();
          obData.examine_pwd = Base64.encode(obData.examine_pwd || '');
          const params = { ...data, ...obData, data_source_id: this.id };
          testObCollectConfig(params)
            .then((res) => {
              if (CommonUtil.isSuccessCode(res)) {
                this.flag = res.data.data.flag || false;
                this.$hideLoading({
                  useMessage: true,
                  tips: '校验成功'
                });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch((e) => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    onChange(e) {
      this.extraStatus = 0;
      if (e.target.value == 1) {
        this.spinning = true;
        getExtraColumns({ data_source_id: this.id })
          .then((res) => {
            if (CommonUtil.isSuccessCode(res)) {
              const resData = _.get(res, 'data.data');
              this.dataSource = resData;
              const captureEnumOptions = resData[0].capture_enum;
              this.$set(
                this.tableParams,
                'editConfig',
                this.config.editConfig(captureEnumOptions)
              );
              this.spinning = false;
              this.$hideLoading({ duration: 0 });
              this.extraStatus = e.target.value;
            } else {
              this.spinning = false;
              this.extraStatus = 0;
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch((e) => {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else {
        this.dataSource = [];
      }
    },
    cronExpression(data, type) {
      if (data && data.split(' ').length > 0) {
        if (data.split(' ').includes('undefined')) {
          this.formData = Object.assign({}, this.formData, {
            collect_frequency: '',
            collect_time: type
          });
        } else {
          this.formData = Object.assign({}, this.formData, {
            collect_frequency: data,
            collect_time: type
          });
          this.$refs.form.saving({
            collect_frequency: data,
            collect_time: type
          });
        }
      }
      this.$nextTick(() => {
        this.$refs.form.validate();
      });
    }
  }
};
</script>

<style lang="less" scoped>
.config-collect-config-drawer {
  /deep/ .ant-drawer-content-wrapper {
    width: 720px;
    .ant-spin-container {
      .item {
        margin-bottom: 24px;
        .title {
          &::before {
            content: '';
            content: '';
            width: 3px;
            background: @primary-color;
            height: 16px;
            border-radius: 0px;
            margin-right: 8px;
          }
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 8px;
        }
      }
      .tips-content {
        padding: 12px 24px 12px 24px;
        background: rgb(254, 241, 239);
        .message {
          .anticon {
            color: red;
            margin-right: 8px;
          }
        }
        .sql-text {
          margin-top: 12px;
          margin-left: 24px;
          > pre {
            margin: 0;
          }
        }
      }
      .hidden-label > .ant-form-item-label {
        // display: none;
        opacity: 0;
      }
      .password {
        padding-bottom: 8px;
      }
      .collect-type {
        // padding-top: 16px;
        // border-top: 1px solid #e8e8e8;
      }
      .extra-form-box,
      .auto-review-form-box {
        padding: 12px 32px;
        background: #fff;
        .ant-form {
          .ant-row {
            .ant-col-4 {
              padding-right: 16px;
              width: 120px;
              min-height: 36px;
              // white-space: pre-line;
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              .ant-form-item-children {
                .ant-input,
                .ant-input-number,
                .ant-select-selection {
                  height: 36px;
                  border: 1px solid rgba(217, 217, 217, 1);
                }
                .ant-time-picker-input {
                  height: 36px;
                  position: relative;
                  top: 1px;
                }
              }
            }
            .ant-radio-group {
              display: flex;
              .ant-radio-wrapper {
                padding: 6px 8px;
              }
            }
          }
          &.ant-form {
            .ant-row {
              .ant-col-4 {
                padding-right: 16px;
                width: 200px;
                // white-space: pre-line;
                > label {
                  justify-content: flex-start;
                }
              }
              .ant-form-item-control-wrapper {
                flex-grow: 1;
                .ant-form-item-children {
                  .ant-input,
                  .ant-input-number,
                  .ant-select-selection {
                    height: 36px;
                    border: 1px solid rgba(217, 217, 217, 1);
                  }
                }
              }
              .ant-radio-group {
                display: flex;
                .ant-radio-wrapper {
                  padding: 6px 10px;
                  // background: #ffffff;
                  border: 1px solid rgba(228, 228, 231, 1);
                  &.ant-radio-wrapper-checked {
                    border: 1px solid rgba(77, 181, 242, 1);
                  }
                }
              }
            }
          }
        }
        .extra-columns-box {
          background: #fff;
          .extra-columns-radio {
            display: flex;
            // justify-content: space-between;
            padding: 8px 10px 8px 0;
            > span {
              width: 200px;
              font-size: 13px;
              color: #27272a;
              font-weight: 400;
              padding-right: 16px;
            }
          }
          .extra-columns-desc {
            padding: 8px 8px;
            background: #003b72;
            display: flex;
            justify-content: center;
            // align-items: center;
            span {
              font-size: 12px;
              color: #ffffff;
              text-align: justify;
              font-weight: 400;
            }
            .anticon {
              color: #fff;
              margin-right: 4px;
              margin-top: 2px;
            }
          }
          .extra-columns-table {
            // padding: 12px 0;
          }
        }
      }
      .ob-extra-form {
        padding: 12px 32px;
        background: #eff5ff;
        margin-bottom: 16px;
        .des {
          display: flex;
          margin-bottom: 16px;
          .anticon {
            color: #008adc;
            font-size: 16px;
            margin-right: 8px;
            position: relative;
            top: 3px;
          }
          > span {
            font-size: 14px;
            color: #000000;
          }
        }
        .ant-form {
          .ant-row {
            .ant-col-4 {
              padding-right: 16px;
              width: 160px;
              min-height: 36px;
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              .ant-form-item-children {
                .ant-input,
                .ant-input-number,
                .ant-select-selection {
                  height: 36px;
                  border: 1px solid rgba(217, 217, 217, 1);
                }
              }
            }
          }
        }
      }
    }
  }
}
.btns-area {
  display: flex;
  justify-content: flex-end;
  .ant-btn {
    margin-left: 10px;
    &.hide-btn {
      margin-left: 0px;
    }
  }
  .ant-divider {
    height: 32px;
    margin: 0 12px;
  }
}
.ant-steps {
  padding: 24px;
}
.ant-divider-horizontal {
  margin: 0 0 24px 0;
}
.ant-tag-red {
  font-size: 14px;
  padding: 8px;
}

/deep/.ant-row-flex-space-around {
  justify-content: flex-start;
  .backgroundBlock {
    margin-right: 12px;
  }
}
</style>
