<template>
  <!-- 风险sql -->
  <a-skeleton
    :loading="loading"
    active
    class="quater-block small-size topsql-block"
  >
    <div class="title"><span class="risk">高风险</span>SQL分布</div>
    <div class="data-overview">
      <div>
        <span>SQL解析率</span>
        <p>
          {{
            sqlParsingRate
              ? `${(Number(sqlParsingRate) * 100).toFixed(2)}` + '%'
              : '--'
          }}
        </p>
      </div>
      <div>
        <span>SQL通过率</span>
        <p>
          {{ passRate ? `${(Number(passRate) * 100).toFixed(2)}` + '%' : '--' }}
        </p>
      </div>
    </div>
    <div class="data-content" v-if="ruleList.length > 0">
      <div v-for="item in ruleList" :key="item.rule_id">
        <LimitLabel
          :label="item.rule_name || ''"
          :block="true"
          mode="ellipsis"
        ></LimitLabel>
        <span class="num">{{ item.trigger_total }}</span>
      </div>
    </div>
    <Empty v-else />
  </a-skeleton>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
import Empty from './Empty';
export default {
  components: { LimitLabel, Empty },
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    ruleList: {
      type: Array,
      default: () => []
    },
    sqlParsingRate: Number | String,
    passRate: Number | String
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.topsql-block {
  height: 420px;
  width: 25%;
  margin-right: 16px;
  padding: 16px 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.05);
  .title {
    font-size: 16px;
    color: #1f1f1f;
    padding-bottom: 16px;
    font-weight: bold;
    .risk {
      color: #ff4d4f;
      margin-right: 4px;
    }
  }
  .data-overview {
    display: flex;
    justify-content: space-around;
    margin-bottom: 24px;
    div {
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        color: #595959;
      }
      p {
        font-size: 30px;
        color: #1f1f1f;
        margin: 0;
        font-weight: 500;
      }
    }
  }
  .data-content {
    height: 260px;
    overflow-y: auto;
    > div {
      padding: 8px 6px;
      display: flex;
      justify-content: space-between;
      color: #1f1f1f;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      .num {
        margin-left: 12px;
      }
      &:last-child {
        border: none;
      }
      .limit-label {
        pre {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          white-space: nowrap;
          font-family: 'PingFang SC', 'Microsoft YaHei';
        }
      }
    }
  }
}
</style>