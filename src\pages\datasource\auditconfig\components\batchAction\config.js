export default function (ctx) {
  const mysqlFields = () => {
    return [
      {
        type: 'Switch',
        label: '覆盖已有策略',
        key: 'is_covered',
        props: {
          size: 'default',
          'checked-children': '是',
          'un-checked-children': '否'
        },
        width: 50,
        rules: []
      },
      (formData = {}) => {
        return {
          type: 'Label',
          label: '采集方式',
          key: 'collect_type',
          tips: '批量配置只支持JBDC采集模式，不支持OpenAPI',
          tipsPlacement: 'end',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'jdbc_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'jdbc_frequency'
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '统计间隔',
          key: 'statistical_interval',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: { enum_name: 'jdbc_statistical_interval' }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      }
    ];
  }
  const oracleFields = () => {
    return [
      {
        type: 'Switch',
        label: '覆盖已有策略',
        key: 'is_covered',
        props: {
          size: 'default',
          'checked-children': '是',
          'un-checked-children': '否'
        },
        width: 50,
        rules: []
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '首次采集天数',
          key: 'collect_day',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'oracle_collect_day'
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'collect_frequency',
          props: {},
          hideComponent: true,
          slots: [{ key: 'frequency' }],
          rules: [{ required: true, message: '', trigger: 'change' }]
        };
      }
    ];
  };

  const db2Fields = () => {
    return [
      {
        type: 'Switch',
        label: '覆盖已有策略',
        key: 'is_covered',
        props: {
          size: 'default',
          'checked-children': '是',
          'un-checked-children': '否'
        },
        width: 50,
        rules: []
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'collect_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'db2_collect_frequency'
            }
          },
          rules: [{ required: true, message: '', trigger: 'change' }]
        };
      }
    ];
  };

  const obFields = () => {
    return [
      {
        type: 'Switch',
        label: '覆盖已有策略',
        key: 'is_covered',
        props: {
          size: 'default',
          'checked-children': '是',
          'un-checked-children': '否'
        },
        width: 50,
        rules: []
      },
      {
        type: 'Select',
        label: '采集天数',
        key: 'collect_day',
        props: {
          url: '/sqlreview/after_audit/get_sql_collect_enum',
          reqParams: {
            enum_name: 'ob_collect_day'
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Select',
        label: '采集频率',
        key: 'collect_frequency',
        props: {
          url: '/sqlreview/after_audit/get_sql_collect_enum',
          reqParams: {
            enum_name: 'ob_collect_frequency'
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Select',
        label: '统计间隔',
        key: 'statistical_interval',
        props: {
          url: '/sqlreview/after_audit/get_sql_collect_enum',
          reqParams: {
            enum_name: 'ob_statistical_interval'
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
    ];
  };

  const pgFields = () => {
    return [
      {
        type: 'Switch',
        label: '覆盖已有策略',
        key: 'is_covered',
        props: {
          size: 'default',
          'checked-children': '是',
          'un-checked-children': '否'
        },
        width: 50,
        rules: []
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集模式',
          key: 'collect_type',
          className: 'collect-type',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'pg_collect_type'
            }
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                collect_type: value,
                jdbc_frequency: value == 'jdbc' ? 200 : null,
                statistical_interval: value == 'jdbc' ? 60000 : null
              });
              ctx.$set(ctx, 'collectType', value);
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'jdbc_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'jdbc_frequency'
            }
          },
          visible: formData.collect_type == 'jdbc',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'pg_stat_statements_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'pg_stat_statements_frequency'
            }
          },
          visible: formData.collect_type == 'pg_stat_statements',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '统计间隔',
          key: 'statistical_interval',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: { enum_name: 'pg_jdbc_statistical_interval' }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      }
    ];
  }
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '数据源',
      dataIndex: 'name',
      key: 'name',
      // width: 200,
      scopedSlots: { customRender: 'name' }
    }
  ];

  const autoReviewFields = () => {
    return [
      {
        type: 'Select',
        label: '自动审核开关',
        key: 'auto_review_status',
        props: {
          options: [
            { label: '开', value: 1 },
            { label: '关', value: 0 }
          ]
        }
        // rules: [{ required: true, message: '该项为必填项' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '审核频率',
          key: 'review_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'auto_review_frequency'
            }
          }
          // rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'TOP策略',
          key: 'top_strategy',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'pg_top_strategy'
            }
          }
          // rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'InputNumber',
          label: 'TOP排名',
          key: 'top_sort',
          props: {
            max: 100,
            min: 0
          }
          // rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '重复SQL过滤',
          key: 'filter_day',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'pg_filter_time'
            }
          }
          // rules: [{ required: true, message: '该项为必填项' }]
        };
      }
    ];
  };
  return {
    columns,
    obFields,
    pgFields,
    db2Fields,
    mysqlFields,
    oracleFields,
    autoReviewFields
  };
}
