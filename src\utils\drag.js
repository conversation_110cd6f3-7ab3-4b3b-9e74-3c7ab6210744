class Draggable {
  constructor(options = {}) {
    /**
     * 参数
     * el: 拖动对象 -- dom对象或数组
     * handler: 拖动控制 -- dom对象 或 [当el为数组为css选择器]
     * clone: 克隆
     */
    this.options = Object.assign({
      el: null,
      handler: null,
      clone: false,
      zIndex: 1,
      offset: 0,
      onStart: () => { },
      onMove: () => { },
      onEnd: () => { }
    }, options);
    this.init();
  }
  init() {
    const { el, handler } = this.options;
    if (!el) {
      console.error('el 不存在');
      return;
    }

    document.addEventListener('mousedown', this.start = (e) => this.onStart(e));
    document.addEventListener('mousemove', this.move = (e) => this.onMove(e));
    document.addEventListener('mouseup', this.end = (e) => this.onEnd(e));
    this.el = el;
    this.handler = handler;
    this.moveItem = null;
  }

  onStart(e) {
    if (_.isArrayLike(this.el)) {
      const matchIndex = _.map(this.el, item => _.isString(this.handler) ? item.querySelector(this.handler) : item).findIndex(itm => this.isInTarget(e, itm));
      if (matchIndex >= 0) {
        if (this.options.clone) {
          this.moveItem = this.cloneNode(this.el[matchIndex]);
        } else {
          this.moveItem = this.el[matchIndex];
        }
        this.flag = true;
      }
    } else {
      const handler = _.isString(this.handler) ? this.el.querySelector(this.handler) : (this.handler || this.el);
      if (this.isInTarget(e, handler)) {
        this.moveItem = this.el;
        this.flag = true;
      }
    }

    if (this.flag) {
      const { offset } = this.options;
      this.elStartPos = {
        'left': offset ? e.pageX + offset : this.moveItem.style.left,
        'top': offset ? e.pageY + offset : this.moveItem.style.top
      };
      this.startPos = {
        x: e.pageX,
        y: e.pageY
      };

      const { onStart } = this.options;
      onStart && onStart(e, this.moveItem);
      // e.preventDefault();
    }
  }

  onMove(e) {
    if (this.flag) {
      if (Math.abs(e.pageX - this.startPos.x) < 5 && Math.abs(e.pageY - this.startPos.y) < 5) {
        // 移动距离过小
        return;
      }
      this.moveItem.style.left = this.getPxVal(this.elStartPos.left) + (e.pageX - this.startPos.x) + 'px';
      this.moveItem.style.top = this.getPxVal(this.elStartPos.top) + (e.pageY - this.startPos.y) + 'px';

      const { onMove } = this.options;
      onMove && onMove(e, this.moveItem);
      e.preventDefault();
    }
  }

  onEnd(e) {
    if (this.flag) {
      this.flag = false;

      const { onEnd } = this.options;
      onEnd && onEnd(e, this.moveItem);

      if (this.options.clone) {
        document.body.removeChild(this.moveItem);
        this.moveItem = null;
      }
    }
  }

  getPxVal(val) {
    let res = val + '';
    return parseFloat(res.split('px')[0] || 0);
  }
  isInTarget(e, target) {
    let flag = false;
    let { top, bottom, left, right } = target.getBoundingClientRect();
    if (
      e.pageX > left &&
      e.pageX < right &&
      e.pageY > top &&
      e.pageY < bottom
    ) {
      flag = true;
    }
    return flag;
  }

  cloneNode(node) {
    const { zIndex = 10 } = this.options;
    let clone = node.cloneNode(true);
    // let borderTop = this.getPxVal(getComputedStyle(node).borderTopWidth);
    // let borderLeft = this.getPxVal(getComputedStyle(node).borderLeftWidth);
    const { left, top, width, height } = node.getBoundingClientRect();
    clone.style.position = 'absolute';
    clone.style.left = left + 'px';
    clone.style.top = top + 'px';
    clone.style.width = width + 'px';
    clone.style.height = height + 'px';
    clone.style.zIndex = zIndex;
    clone.className += ' drag-ele-clone';
    document.body.appendChild(clone);
    return clone;
  }

  destroy() {
    document.removeEventListener('mousedown', this.start);
    document.removeEventListener('mousemove', this.move);
    document.removeEventListener('mouseup', this.end);
    this.el = null;
    this.handler = null;
    this.moveItem = null;
  }
}
export default Draggable;