<template>
  <div class="schedule-anget-detail">
    <div class="frame-button-wrapper">
      <a-button class="highlight" @click="toBack">返回</a-button>
    </div>
    <div class="header">
      <div class="host-container">
        <custom-icon type="desktop"></custom-icon>
        <span>主机：</span>
        <span>{{ headerInfo.name }}</span>
        <a-tag :class="`status-${headerInfo.status}`">{{
          headerInfo.status == 1 ? '在线' : '离线'
        }}</a-tag>
      </div>
      <div class="ip-container">
        <span>ip：</span>
        <span>{{ headerInfo.ip }}</span>
      </div>
    </div>
    <div class="table-content">
      <Table
        ref="table"
        v-bind="tableParams"
        :dataSource="dataSource || []"
        class="new-view-table"
      >
        <template slot="tableTopLeft">
          <div>agent worker列表</div>
        </template>
        <template slot="tableTopRight">
          <div class="search-form-container">
            <a-input-search
              placeholder="请输入java进程名称"
              @search="onSearch"
            />
            <!-- <div class="seach-area-btns">
              <a-button @click="search">查询</a-button>
              <a-tooltip>
                <template slot="title">重置</template>
                <custom-icon type="lu-icon-clean" @click="reset" />
              </a-tooltip>
            </div> -->
          </div>
        </template>

        <template slot="status" slot-scope="{ text }">
          <span :class="`status-${text}`">{{
            text == 1 ? '在线' : '离线'
          }}</span>
        </template>
      </Table>
    </div>
    <div class="table-content">
      <Table
        ref="javaTable"
        v-bind="javaTableParams"
        :dataSource="javaDataSource || []"
        class="new-view-table"
      >
        <template slot="tableTopLeft">
          <div>java进程列表</div>
        </template>
        <template slot="tableTopRight">
          <div class="search-form-container">
            <!-- <span>
              <custom-icon type="refresh"></custom-icon>
              <i> 刷新时间： {{}}</i>
            </span> -->
            <a-input-search
              placeholder="请输入java进程名称"
              @search="onJavaSearch"
            />
            <!-- <div class="seach-area-btns">
              <a-button @click="javaSearch">查询</a-button>
              <a-tooltip>
                <template slot="title">重置</template>
                <custom-icon type="lu-icon-clean" @click="javaReset" />
              </a-tooltip>
            </div> -->
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>

<script>
import Form from '@/components/Form';
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import config from './config';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Form,
    Table,
    LimitLabel
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      headerInfo: {},
      dataSource: [],
      tableParams: {
        url: `/sqlreview/agent/agent_worker`,
        reqParams: {
          // id: this.$route.query.type
        },
        columns: this.config.agentColumns,
        rowKey: 'id',
        needSearchArea: true,
        searchFields: {},
        scroll: { x: 'max-content' },
        pagination: false,
        loaded: this.onTableLoaded
      },
      javaDataSource: [],
      javaTableParams: {
        url: `/sqlreview/agent/agent_worker`,
        reqParams: {
          // id: 24
        },
        columns: this.config.javaColumns,
        rowKey: 'id',
        needSearchArea: true,
        searchFields: {},
        scroll: { x: 'max-content' },
        pagination: false,
        loaded: this.javaLoaded
      },
      type: null
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    onTableLoaded(req, res) {
      this.headerInfo = res.data.data.info;
      this.dataSource = res.data.data.worker_list;
    },
    javaLoaded(req, res) {
      this.javaDataSource = res.data.data.worker_list;
    },
    toBack() {
      this.$router.push({ name: 'schedule-job' });
    },
    // 查询
    // search() {
    //   const { table, form } = this.$refs;
    //   const data = form.getData();
    //   const { searchParams } = table;
    //   Object.assign(searchParams, { ...data });
    //   table.refresh(null, data);
    // },
    // 重置
    // reset() {
    //   this.formData = {};
    //   const { table } = this.$refs;
    //   table.searchParams = {};
    //   table.refresh();
    // },
    // javaSearch() {
    //   const { javaTable, form } = this.$refs;
    //   const data = form.getData();
    //   const { searchParams } = javaTable;
    //   Object.assign(searchParams, { ...data });
    //   javaTable.refresh(null, data);
    // },
    // javaReset() {
    //   this.javaFormData = {};
    //   const { javaTable } = this.$refs;
    //   javaTable.searchParams = {};
    //   javaTable.refresh();
    // },
    onSearch(val) {
      const { table } = this.$refs;
      table.refresh(null, { name: val });
    },
    onJavaSearch(val) {
      const { javaTable } = this.$refs;
      javaTable.refresh(null, { name: val });
    }
  },
  watch: {
    '$route.query': {
      handler(newVal) {
        if (newVal) {
          this.$set(this.tableParams, 'reqParams', {
            monitored: 1,
            id: newVal.id
            // name: newVal.name
          });
          this.$set(this.javaTableParams, 'reqParams', {
            id: newVal.id
          });
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.schedule-anget-detail {
  .header {
    background: #fff;
    padding: 12px 16px;
    border-radius: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .host-container {
      display: flex;
      align-items: center;
      .anticon {
        width: 64px;
        height: 64px;
        font-size: 36px;
        background: #69b1ff;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 24px;
      }
      span {
        font-family: PingFangSC-Regular;
        font-size: 24px;
        color: #1f1f1f;
      }
      .ant-tag {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        padding: 1px 6px;
        border-radius: 4px;
        margin-left: 24px;
        &.status-1 {
          background: #e6f7ff;
          border: 1px solid rgba(145, 213, 255, 1);
          color: #1890ff;
        }
        &.status-0 {
          background: #fafafa;
          border: 1px solid rgba(217, 217, 217, 1);
          color: #1f1f1f;
        }
      }
    }
    .ip-container {
      span {
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #1f1f1f;
        text-align: right;
      }
    }
  }
  /deep/.table-content {
    border-radius: 16px;
    background: #fff;
    margin-bottom: 16px;
    .custom-table {
      .search-area-wrapper {
        display: flex;
        justify-content: space-between !important;
        align-items: center;
        padding: 12px 24px;
        border-bottom: solid 1px #fafafa;
        .custom-table-top-left {
          > div {
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #1f1f1f;
            font-weight: 600;
          }
        }
        .custom-table-top-right {
          .search-form-container {
            display: flex;
            align-items: center;
            .ant-input {
              margin-right: 16px;
            }
            .seach-area-btns {
              white-space: nowrap;
              .ant-btn {
                padding: 0 12px;
                border-radius: 4px !important;
              }
              .anticon {
                font-size: 16px;
                margin: 0 12px;
                border-radius: 50%;
                &:hover {
                  color: #000;
                  cursor: pointer;
                }
              }
              &:hover {
                .ant-btn {
                  span {
                    color: #25a7e8;
                  }
                }
              }
            }
          }
        }
      }
      .ant-table-wrapper {
        padding: 0 16px 16px 16px;
      }

      .status-0,
      .status-1 {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        padding: 1px 6px;
        border-radius: 4px;
      }
      .status-1 {
        background: #e6f7ff;
        border: 1px solid rgba(145, 213, 255, 1);
        color: #1890ff;
      }
      .status-0 {
        background: #fafafa;
        border: 1px solid rgba(217, 217, 217, 1);
        color: #1f1f1f;
      }
    }
  }
}
</style>