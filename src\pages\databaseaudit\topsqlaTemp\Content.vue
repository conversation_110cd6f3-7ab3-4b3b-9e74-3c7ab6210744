<template>
  <div class="topsqlaTemp">
    <div class="search_box">
      <div class="search_box_title">
        <span>查询条件</span>
        <span @click="chart_show_btn" style="color: #1677ff; cursor: pointer">
          <span>
            {{ chart_show ? '隐藏图表' : '显示图表' }}
          </span>
        </span>
      </div>
      <el-form
        size="small"
        :model="search_form"
        :rules="search_rules"
        ref="search_form"
        label-width="70px"
        class="demo-ruleForm search_container"
      >
        <div class="flex-container">
          <el-form-item label-width="108px" label="实例名称" prop="id">
            <el-select
              @focus="focus_db_type"
              v-model="search_form.id"
              placeholder="请选择"
              clearable
              filterable
              remote
              :remote-method="db_type_method"
            >
              <template v-if="search_form.id" slot="prefix">
                <!-- <a-tag
                  :class="search_form.env.toLowerCase() == 'test' ? 'test' : 'pro'"
                  >{{
                    search_form.env.toLowerCase() == 'test' ? '测试' : '生产'
                  }}</a-tag
                > -->
                <custom-icon :type="iconType[search_form.db_type]" />
              </template>
              <el-option
                v-for="(item, index) in datasource_list"
                :key="index"
                :label="item.label"
                :value="item.id"
                @click.native="datasource_list_btn(item)"
              >
                <a-tag
                  :class="item.env.toLowerCase() == 'test' ? 'test' : 'pro'"
                  >{{
                    item.env.toLowerCase() == 'test' ? '测试' : '生产'
                  }}</a-tag
                >
                <custom-icon :type="iconType[item.db_type]" />
                <span>{{ item.label }}</span>
                <span style="color: #a1a1aa">{{ item.db_type }}</span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label-width="108px" label="数据来源" prop="source_type">
            <!-- source_type_list -->
            <el-select
              @change="source_type_change"
              v-model="search_form.source_type"
              placeholder="请选择"
              clearable
              filterable
              remote
            >
              <el-option
                v-for="(item, index) in source_type_list"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label-width="108px" label="执行时间" prop="time">
            <a-range-picker
              @change="onChange"
              :ranges="dataRange"
              style="width: 100%"
              :showTime="{
                hideDisabledOptions: true,
                defaultValue: [
                  moment('00:00:00', 'HH:mm:ss'),
                  moment('23:59:59', 'HH:mm:ss')
                ]
              }"
              v-model:value="search_form.time"
              :format="['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']"
            />
          </el-form-item>

          <el-form-item
            v-if="search_form.db_type != 'ORACLE'"
            v-show="retract_icon_status"
            label-width="108px"
            label="执行数据库"
            prop=""
          >
            <el-select
              v-model="search_form.executive_schema"
              placeholder="请选择"
              clearable
              filterable
              remote
            >
              <el-option
                v-for="(item, index) in executive_data"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            v-show="retract_icon_status"
            label-width="108px"
            label="执行用户"
            prop=""
          >
            <el-select
              v-model="search_form.executive_user"
              placeholder="请选择"
              clearable
              filterable
              remote
            >
              <el-option
                v-for="(item, index) in executive_data_user"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            v-show="retract_icon_status"
            label-width="108px"
            label="风险等级"
            prop=""
            collapse-tags
          >
            <el-select
              v-model="search_form.risk_level"
              placeholder="请选择"
              clearable
              multiple
            >
              <el-option label="待审核" value="0"></el-option>
              <el-option label="高风险" value="-1"></el-option>
              <el-option label="无风险" value="1"></el-option>
              <el-option label="低风险" value="2"></el-option>
              <el-option label="异常" value="9"></el-option>
              <el-option label="审核中" value="3"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            v-show="retract_icon_status"
            label-width="108px"
            label="SQL_ID"
            prop=""
          >
            <el-input placeholder="请输入" v-model="search_form.sql_id">
            </el-input>
          </el-form-item>

          <el-form-item
            v-show="retract_icon_status"
            label-width="108px"
            label="SQL语句"
            prop=""
          >
            <el-input placeholder="请输入" v-model="search_form.sql_text">
            </el-input>
          </el-form-item>
        </div>
        <div class="search_btn_box" style="display: flex; width: 260px">
          <el-form-item label-width="" prop="">
            <el-button
              style="padding-left: 12px"
              v-show="retract_icon_status"
              class="el-icon-arrow-up"
              type="text"
              @click="retract"
              >收起</el-button
            >
            <el-button
              style="padding-left: 12px"
              v-show="!retract_icon_status"
              class="el-icon-arrow-down"
              type="text"
              @click="retract"
              >展开</el-button
            >

            <el-button @click="search__" type="primary">查 询</el-button>
            <el-button @click="reset">清 空</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div v-if="chart_show" class="chart_box">
      <div v-loading="chart_loading" class="chart_box_header">
        <span>性能趋势</span>
        <el-select
          @change="func_change"
          style="width: 130px"
          size="small"
          v-model="search_form.func"
          placeholder="p99"
        >
          <el-option label="平均值" value="mean"></el-option>
          <el-option label="最大值" value="max"></el-option>
          <el-option label="最小值" value="min"></el-option>
          <el-option label="p99   " value="p99"></el-option>
          <el-option label="p95" value="p95"></el-option>
        </el-select>
      </div>
      <div class="chart_container" id="chart_container"></div>
    </div>
    <div class="tabs_box">
      <div style="z-index: 100000" class="tabs_box_header">
        <!-- <span style="
            background-color: rgb(230, 244, 255);
            color: rgb(9, 88, 217);
            margin-left: 4px;
            font-size: 12px;
            padding: 0 8px;
          ">
          请在上图中圈出要分析的时间段
        </span> -->
        <span></span>
        <span>
          <!-- <span>2025-03-17 11:50 ~ 2025-03-17 15:22</span> -->
          <span
            @click="downloadFile"
            style="
              cursor: pointer;
              font-size: 16px;
              font-weight: 600;
              margin-left: 12px;
            "
            class="el-icon-download"
          ></span>
          <span
            @click="table_selection_btn"
            style="
              cursor: pointer;
              font-size: 16px;
              font-weight: 600;
              margin-left: 12px;
            "
            class="el-icon-edit"
          ></span>

          <span
            @click="edit_title"
            style="
              cursor: pointer;
              position: relative;
              top: 3px;
              font-size: 20px;
              font-weight: 600;
              margin-left: 12px;
            "
            class="el-icon-c-scale-to-original"
          ></span>
        </span>
      </div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="TOPSQL" name="first">
          <el-table
            @sort-change="handleSortChange"
            ref="table_"
            v-loading="loading_list"
            :header-cell-style="{
              background: '#fafafa',
              height: '40px',
              padding: '5px 0',
              color: '#000000'
            }"
            :data="table_data.results"
            style="width: 100%"
            :cell-style="{ padding: '5px 0', width: '200px', height: '40px' }"
            @selection-change="handleSelectionChange"
          >
            <el-table-column fixed type="selection" width="55">
            </el-table-column>

            <!-- for header -->
            <span v-for="(item, index) in table_headers_list" :key="index">
              <el-table-column
                :sortable="item.sorter ? 'custom' : false"
                v-if="
                  headers_keys.includes('sql_text') && item.key == 'sql_text'
                "
                show-overflow-tooltip
                :prop="item.key"
                :label="item.value"
                width="240px"
              >
                <template slot-scope="scope">
                  <!-- -->

                  <el-popover
                    popper-class="device-popover"
                    placement="top"
                    width="400"
                    trigger="hover"
                  >
                    <div>
                      <pre
                        style="
                          white-space: pre-line;
                          max-width: 600px;
                          max-height: 300px;
                        "
                        v-html="
                          $options.filters.sql_text_cpt(scope.row.sql_text)
                        "
                      ></pre>
                      <div style="text-align: right">
                        <span
                          style="cursor: pointer"
                          @click="copySql(scope.row.sql_text)"
                          class="el-icon-document-copy"
                        >
                        </span>
                      </div>
                    </div>
                    <span
                      v-if="scope.row.sql_text.length > 20"
                      slot="reference"
                    >
                      {{ scope.row.sql_text.substring(0, 20) + '...' }}
                    </span>
                    <span v-else slot="reference">
                      {{ scope.row.sql_text }}</span
                    >
                  </el-popover>
                  <span
                    style="cursor: pointer"
                    @click="copySql(scope.row.sql_text)"
                    class="el-icon-document-copy"
                  ></span>
                </template>
              </el-table-column>
              <el-table-column
                :sortable="item.sorter ? 'custom' : false"
                v-else-if="headers_keys.includes('risk') && item.key == 'risk'"
                show-overflow-tooltip
                :prop="item.key"
                :label="item.value"
                width="220px"
              >
                <template slot-scope="scope">
                  <a-tag v-if="scope.row.risk == -1" color="red">
                    {{ scope.row.risk | risk_filters }}
                  </a-tag>
                  <a-tag v-else-if="scope.row.risk == 9" color="orange">
                    {{ scope.row.risk | risk_filters }}
                  </a-tag>
                  <a-tag v-else-if="scope.row.risk == 1" color="green">
                    {{ scope.row.risk | risk_filters }}
                  </a-tag>
                  <a-tag v-else> {{ scope.row.risk | risk_filters }} </a-tag>
                </template>
              </el-table-column>
              <el-table-column
                :sortable="item.sorter ? 'custom' : false"
                v-else-if="
                  headers_keys.includes('source_type') &&
                  item.key == 'source_type'
                "
                show-overflow-tooltip
                :prop="item.key"
                :label="item.value"
                width="220px"
              >
                <template slot-scope="scope">
                  <span>{{
                    scope.row.source_type | source_type_filter(source_type_list)
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                :sortable="item.sorter ? 'custom' : false"
                v-else-if="
                  headers_keys.includes('sql_id') && item.key == 'sql_id'
                "
                show-overflow-tooltip
                :prop="item.key"
                :label="item.value"
                width="320px"
              >
              </el-table-column>
              <el-table-column
                :sortable="item.sorter ? 'custom' : false"
                v-else
                show-overflow-tooltip
                :prop="item.key"
                :label="item.value"
                width="220px"
              >
              </el-table-column>
            </span>

            <el-table-column fixed="right" label="操作" width="120">
              <template slot-scope="scope">
                <el-button @click="trend(scope.row)" type="text">
                  趋势
                </el-button>
                <el-button @click="diagnose(scope.row)" type="text">
                  诊断
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="block">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="search_form.page_number"
              :page-sizes="[10, 20, table_count]"
              :page-size="search_form.page_size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="table_count"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
        <!-- <el-tab-pane label="智能分析（测试）" name="second">
          <div style="    border-bottom: 1px solid #EEEEEE;">
            <p class="analyse_title">分析结果</p>
            <div style="padding: 0 20px 0 24px;">
              <h3 class="analyse_title_item">以下为所选区域Top3可疑SQL:</h3>
              <div class="analyse_box">
                <img class="analyse_img" src="../../../assets//img//sql_top_.png" alt="">
                <div style="flex: 1 1;">
                  <ul style="padding-top: 20px;min-height: 168px;">
                    <li @mouseenter="onMouseEnter(1)" @mouseleave="onMouseLeave(1)" class="analyse_li_item" style="">
                      <span class="analyse_span_icon">1</span>
                      <span>
                        ed8f9ec8a9937a2a8284c01155e99e0e
                      </span>

                      <span>分析</span>
                    </li>

                    <li class="analyse_li_item" style="">
                      <span class="analyse_span_icon">1</span>
                      <span>
                        ed8f9ec8a9937a2a8284c01155e99e0e
                      </span>
                    </li>

                    <li class="analyse_li_item" style="">
                      <span class="analyse_span_icon">1</span>
                      <span>
                        ed8f9ec8a9937a2a8284c01155e99e0e
                      </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div style="    border-bottom: 1px solid #EEEEEE;">
            <p class="analyse_title">分析结果</p>
            <div style="padding: 0 20px 0 24px;">
              <h3 class="analyse_title_item">以下为所选区域Top3可疑SQL:</h3>
              <div class="analyse_box">
                <div>
                  <ul style="padding-top: 20px;min-height: 168px;">
                    <li @mouseenter="onMouseEnter(1)" @mouseleave="onMouseLeave(1)" class="analyse_li_item" style="">
                      <span class="analyse_span_icon">1</span>
                      <span>
                        ed8f9ec8a9937a2a8284c01155e99e0e
                      </span>

                      <span>分析</span>
                    </li>

                    <li class="analyse_li_item" style="">
                      <span class="analyse_span_icon">1</span>
                      <span>
                        ed8f9ec8a9937a2a8284c01155e99e0e
                      </span>
                    </li>

                    <li class="analyse_li_item" style="">
                      <span class="analyse_span_icon">1</span>
                      <span>
                        ed8f9ec8a9937a2a8284c01155e99e0e
                      </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div style="padding: 0 20px 0 24px;">
              <h3 class="analyse_title_item">以下为所选区域Top3可疑SQL:</h3>
              <div class="analyse_box">
                <div>
                  <ul style="padding-top: 20px;min-height: 168px;">
                    <li @mouseenter="onMouseEnter(1)" @mouseleave="onMouseLeave(1)" class="analyse_li_item" style="">
                      <span class="analyse_span_icon">1</span>
                      <span>
                        ed8f9ec8a9937a2a8284c01155e99e0e
                      </span>

                      <span>分析</span>
                    </li>

                    <li class="analyse_li_item" style="">
                      <span class="analyse_span_icon">1</span>
                      <span>
                        ed8f9ec8a9937a2a8284c01155e99e0e
                      </span>
                    </li>

                    <li class="analyse_li_item" style="">
                      <span class="analyse_span_icon">1</span>
                      <span>
                        ed8f9ec8a9937a2a8284c01155e99e0e
                      </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane> -->
      </el-tabs>
    </div>

    <!-- 申请添加标签 -->
    <a-modal
      width="650px"
      v-model:visible="add_tag_dialogVisible"
      title="申请添加标签"
      @ok="add_tag_ok"
    >
      <div
        style="
          font-size: 12px;
          color: #a1a1aa;
          font-weight: 400;
          margin-bottom: 16px;
        "
      >
        为SQL添加的标签需要评审人员进行审核。审核通过则SQL标签添加成功。
        批量添加标签时，已审核通过的标签不受影响，待审核和未通过的SQL标签会被覆盖。
      </div>

      <div class="radio_item">
        <el-radio @change="add_tag_radio" v-model="add_tag.radio" label="1"
          >白名单</el-radio
        >
        <span
          >SQL
          执行效率低，但由于调用频率，系统核心程度等原因不做优化调整时，可申请加入白名单。</span
        >
      </div>
      <div class="radio_item">
        <el-radio @change="add_tag_radio" v-model="add_tag.radio" label="2"
          >整改中</el-radio
        >
        <span
          >整改周期较长的SQL，可先做标记，暂不影响发版流程。整改中的SQL需要尽快修改，后续版本会继续扫描。</span
        >
      </div>

      <el-form
        :model="add_tag_form"
        :rules="add_tag_rules"
        ref="add_tag_form"
        label-width="100px"
      >
        <el-form-item label="有效期(天):" prop="time">
          <div style="display: flex; align-items: center; height: 40px">
            <el-radio
              @change="add_tag_radio"
              v-if="add_tag.radio == '1'"
              v-model="add_tag_form.time"
              label="2"
              >永久</el-radio
            >
            <el-radio
              @change="add_tag_radio"
              v-if="add_tag.radio == '1' || add_tag.radio == '2'"
              v-model="add_tag_form.time"
              label="1"
              >自定义</el-radio
            >
            <el-input
              v-if="add_tag.radio == '2' || add_tag_form.time == '1'"
              style="width: 100px"
              type=""
              placeholder="请输入"
              v-model="add_tag_form.name"
            >
            </el-input>
          </div>
        </el-form-item>
      </el-form>
    </a-modal>
    <!-- sql 全屏 -->
    <a-modal
      :footer="null"
      class="sql_modal"
      width="100%"
      height="100vh"
      :mask="true"
      :maskClosable="false"
      v-model:visible="sql_dialogVisible"
      title=""
    >
      <div>
        <!-- <pre style="white-space: pre-line;"
          v-html="$options.filters.sql_text_cpt(sql_text_content)"></pre> -->
      </div>
    </a-modal>

    <!-- 自定义表头 -->
    <a-modal
      width="780px"
      v-model:visible="title_dialogVisible"
      title="表头设置"
      @ok="handleOk"
    >
      <el-transfer
        target-order="push"
        ref="transfer"
        class="transfe_box"
        :titles="['全部', '已选']"
        style="display: flex; justify-content: center; align-items: center"
        v-model="transfer_value"
        :data="table_headers_all_list"
      ></el-transfer>
    </a-modal>
    <!-- 抽屉 -->
    <a-drawer
      width="1150px"
      title="趋势"
      placement="right"
      :closable="false"
      :visible="trend_visible"
      :after-visible-change="afterVisibleChangeTrend"
      @close="onClose"
    >
      <Trend
        :source_type_list="source_type_list"
        :key="key_status_trend"
        :base_data="base_data"
        :search_time="search_form.time"
      />
    </a-drawer>

    <a-drawer
      width="1150px"
      title="诊断"
      placement="right"
      :closable="false"
      :visible="diagnose_visible"
      :after-visible-change="afterVisibleChangeDiagnose"
      @close="onClose_diagnose"
    >
      <Diagnose
        :key="key_status_diagnose"
        :search_form_to_child="search_form"
        :base_data="base_data"
        :search_time="search_form.execution_time"
      />
    </a-drawer>
  </div>
</template>

<script>
import moment from 'moment';
import hljs from 'highlight.js';
import Trend from './Trend.vue';
import Diagnose from './Diagnose.vue';
import common from '@/utils/common';
import Cookie from 'js-cookie';
import {
  get_summary_data_list,
  get_header_columns,
  get_datasource_list,
  select_source_type,
  get_executive_data,
  get_overview_chart,
  save_top_sql_label,
  export_excel,
  save_label,
  add_header_columns
} from '@/api/databaseaudit/topsqltemp';
export default {
  components: { Trend, Diagnose },
  data() {
    const detail_valid = (rule, value, callback) => {
      if (!this.add_tag_form.time) {
        callback(new Error());
      } else if (
        this.add_tag_form.time == '1' &&
        this.add_tag_form.name == ''
      ) {
        callback(new Error());
      } else {
        callback();
      }
    };
    return {
      color_arr: ['#00ff40', '#ff8000', '#0000ff', '#ff0080'],
      chart_loading: false,
      key_status_trend: true,
      key_status_diagnose: true,
      chart_show: true,
      diagnose_visible: false,
      sql_dialogVisible: false,
      add_tag: {
        radio: '1'
      },
      add_tag_form: { time: '', name: '' },
      add_tag_rules: {
        time: [
          {
            required: true,
            validator: detail_valid,
            message: '请确认',
            trigger: 'blur'
          }
        ]
      },
      add_tag_dialogVisible: false,
      trend_visible: false,
      transfer_value: [], //穿梭框value
      title_dialogVisible: false, // 穿梭框
      multipleSelection: [], //多选
      retract_icon_status: false, // 搜索框显示范围
      search_form: {
        func: 'p99',
        id: '',
        data_source_id: '',
        db_type: '',
        page_size: 10,
        page_number: 1,
        time: [],
        business_code: 11,
        _sorter: {},
        env: 'test',
        step: 120,
        risk_level: ['-1']
      },
      search_rules: {
        id: [{ required: true, message: '请输入', trigger: 'change' }],
        source_type: [{ required: true, message: '请输入', trigger: 'change' }],
        time: [{ required: true, message: '请输入', trigger: 'change' }]
      },
      chart: null,
      activeName: 'first', //tabs 标识
      table_count: 0,
      sql_text_content: null,
      table_headers_list: [],
      table_headers_all_list: [],
      table_data: {},
      loading_list: false,
      datasource_list: [],
      source_type_list: [],
      executive_data: [],
      executive_data_user: [],
      headers_keys: [],
      base_data: {},
      chart_interval: 5,
      dataRange: {
        近6小时: [moment().subtract(6, 'hour'), moment().subtract(0, 'hour')],
        近1天: [moment().subtract(48, 'hour'), moment().subtract(24, 'hour')],
        近7天: [moment().subtract(7, 'day'), moment()],
        近10天: [moment().subtract(10, 'day'), moment()],
        近15天: [moment().subtract(15, 'day'), moment()],
        近30天: [moment().subtract(30, 'day'), moment()]
      },
      iconType: {
        ORACLE: 'lu-icon-oracle',
        MYSQL: 'lu-icon-mysql',
        PGSQL: 'lu-icon-pgsql',
        HBASE: 'lu-icon-hbase',
        CLICKHOUSE: 'lu-icon-clickhouse',
        KAFKA: 'lu-icon-kafka',
        ELASTICSEARCH: 'lu-icon-elasticsearch',
        POSTGRES: 'lu-icon-pgsql',
        POSTGRE: 'lu-icon-pgsql',
        TIDB: 'lu-icon-tidb',
        STARROCKS: 'lu-icon-starrocks',
        UBISQL: 'lu-icon-ubisql',
        RASESQL: 'lu-icon-rosesql',
        OB_MYSQL: 'lu-icon-oceanbase',
        DB2: 'lu-icon-db2',
        OCEANBASE: 'lu-icon-oceanbase',
        SQLSERVER: 'lu-icon-sql-server',
        TD_MYSQL: 'lu-icon-tdsql-1',
        FILESYSTEM: 'folder-open',
        TDSQL: 'lu-icon-tdsql-1',
        OB_ORA: 'lu-icon-oceanbase',
        OB_ORACLE: 'lu-icon-oceanbase',
        // IMPALA: 'lu-icon-impala',
        GBASE: 'lu-icon-gbase',
        GAUSSDB: 'lu-icon-gaussdb',
        OPENGAUSS: 'lu-icon-opengauss',
        HUDI: 'lu-icon-hudi',
        KINGBASE: 'lu-icon-kingbase',
        HIVE: 'lu-icon-hive',
        IMPALA: 'lu-icon-impala1',
        DM: 'lu-icon-dameng',
        ROCKETMQ: 'lu-icon-apacherocketmq',
        PRESTO: 'lu-icon-presto',
        RDS_MYSQL: 'lu-icon-rds',
        GOLDENDB: 'lu-icon-goldendb',
        MOGDB: 'lu-icon-mogdb',
        DORIS: 'lu-icon-Doris',
        TD_PGSQL: 'lu-icon-tdsql-1'
      }
    };
  },
  created() {},
  mounted() {

    this.settime();
    if (Cookie.get('topsqlaTempSearchData')) {
      this.search_form = JSON.parse(Cookie.get('topsqlaTempSearchData'));
      this.$set(this.search_form, 'risk_level', ['-1']);
      this.search_form.time = [
        moment(this.search_form.time[0]).format('YYYY-MM-DD HH:mm:ss'),
        moment(this.search_form.time[1]).format('YYYY-MM-DD HH:mm:ss')
      ];
      this.search_form.execution_time = [
        moment(this.search_form.time[0]).format('YYYY-MM-DD HH:mm:ss'),
        moment(this.search_form.time[1]).format('YYYY-MM-DD HH:mm:ss')
      ];
      let step = this.getStep({
        startTimestamp: moment(this.search_form.time[0]).unix(),
        endTimestamp: moment(this.search_form.time[1]).unix()
      });

      console.log(step);
      this.search_form.step = step;

      console.log(this.search_form);
    }
    this.search_form.page_size = 10;
    if (this.search_form.db_type) {
      // this.get_list();
      // this.initChart();
      this.select_source_type(this.search_form.db_type);
    }

    this.get_datasource_list();
    this.get_executive_data();
    this.get_executive_data_user();
    this.get_header_columns();
    // this.search_form.source_type = this.source_type_list[0].value
    console.log( this.search_form,'1111111111111111111');
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
  filters: {
    sql_text_cpt(e) {
      return hljs.highlight('sql', e).value;
    },
    risk_filters(e) {
      if (e == 0) {
        return '待审核';
      }

      if (e == -1) {
        return '高风险';
      }

      if (e == 1) {
        return '无风险';
      }

      if (e == 2) {
        return '低风险';
      }

      if (e == 9) {
        return '异常';
      }

      if (e == 3) {
        return '审核中';
      }

      return '';
    },
    source_type_filter(e, arr) {
      if (e !== '' && arr) {
        let str = arr.filter((item) => {
          if (item.value == e) {
            return item;
          }
        });
        return str[0].label;
      }
      return '';
    }
  },
  methods: {
    handleSortChange(e) {
      this.$set(this.search_form, '_sorter', '');
      if (e.order) {
        if (e.order == 'ascending') {
          this.search_form._sorter = JSON.stringify({
            [e.prop]: 'ascend'
          });
        }

        if (e.order == 'descending') {
          this.search_form._sorter = JSON.stringify({
            [e.prop]: 'descend'
          });
        }
      }
      this.get_list();
    },
    downloadFile() {
      if (this.multipleSelection.length > 0) {
        let params = { ...this.search_form };
        params.is_select_all = 0;
        let sql_ids = [];
        this.multipleSelection.forEach((item) => {
          sql_ids.push(item.sql_id);
        });
        params.sql_ids = sql_ids;
        export_excel(params)
          .then((response) => {
            common.downLoad(this, response);
          })
          .catch((e) => {})
          .finally(() => {
            this.loading_list = false;
          });
      } else {
        this.$message.info('请重新选择。');
      }
    },
    moment,
    reset() {
      this.search_form = {
        func: 'p99',
        data_source_id: '',
        db_type: '',
        page_size: 10,
        page_number: 1,
        time: [],
        business_code: 11,
        risk_level: ['-1']
      };
      this.chart.clear();
      this.get_list();
      this.initChart();
    },
    add_tag_radio(e) {
      this.add_tag_form.name = '';
    },
    settime() {
      let now = new Date();
      let date = new Date(now.getTime());
      let time = moment(date).format('YYYY-MM-DD HH:mm:ss');
      let fitsttime = moment(date.setDate(date.getDate() - 1)).format(
        'YYYY-MM-DD HH:mm:ss'
      );
      this.search_form.time = [fitsttime, time];
      this.search_form.execution_time = [fitsttime, time];
    },
    // 获取searchid
    datasource_list_btn(e) {
      if (this.chart) {
        this.chart.clear();
      }
      this.search_form.env = e.env;
      this.search_form.data_source_id = e.id;
      this.search_form.db_type = e.db_type;
      this.search_form.id = e.id;
      this.search_form.title = e.label;
      this.select_source_type(e);
      this.get_executive_data();
      this.get_executive_data_user();
    },
    // 汇总数据获取
    get_list(brushSelected) {
      if (!brushSelected) {
        if (
          this.search_form.time[0] != undefined &&
          this.search_form.time[0]._d
        ) {
          this.search_form.execution_time = [
            moment(this.search_form.time[0]._d).format('YYYY-MM-DD HH:mm:ss'),
            moment(this.search_form.time[1]._d).format('YYYY-MM-DD HH:mm:ss')
          ];
        }
      }

      this.loading_list = true;
      let params = {
        source_type: 1,
        db_type: 'POSTGRE',
        data_source_id: 10,
        execution_time: ['2025-03-17 16:16:59', '2025-03-17 17:16:59'],
        executive_user: '',
        executive_schema: '',
        risk_level: '',
        sql_id: '',
        sql_text: '',
        page_number: 1,
        page_size: 10,
        _sorter: {}
      };

      console.log(this.search_form);

      // this.search_form.source_type =  this.search_form.source_type ||  this.source_type_list[0].value
      get_summary_data_list(this.search_form)
        .then((res) => {
          Cookie.set(
            'topsqlaTempSearchData',
            JSON.stringify({
              id: this.search_form.id,
              data_source_id: this.search_form.data_source_id,
              db_type: this.search_form.db_type,
              env: this.search_form.env,
              execution_time: this.search_form.execution_time,
              time: this.search_form.time,
              source_type: this.search_form.source_type,
              business_code: 11,
              func: 'p99'
            })
          );
          this.table_headers_list_search = [];
          if (res.data.data.results.length > 0) {
            this.headers_keys = Object.keys(res.data.data.results[0]);
          }

          this.table_data = res.data.data;
          this.table_count = res.data.data.count;
          this.search_form.count = res.data.data.count;
          // 多选 反选
          // this.$nextTick(() => {
          //   this.table_data.results.forEach(row => {
          //     console.log(row);
          //     this.$refs.table_.toggleRowSelection(row, true);
          //   });
          // })

          this.$refs.table_.doLayout();
        })
        .catch((e) => {})
        .finally(() => {
          this.loading_list = false;
          this.$refs.table_.doLayout();
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    search__() {
      this.search_form.page_number = 1;
      this.search_form.page_size = 10;
      this.chart.clear();
      this.$refs['search_form'].validate((valid) => {
        if (valid) {
          this.get_header_columns();
          this.initChart();
          this.get_list();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 获取执行数据库
    get_executive_data() {
      // let params = {
      //   "source_type": "1",
      //   "select_type": "scheam",
      //   "db_type": "POSTGRE",
      //   "data_source_id": 10,
      //   "execution_time": ["2025-03-17 15:07:51", "2025-03-17 16:07:51"]
      // }
      let params = { ...this.search_form };
      params.select_type = 'scheam';
      get_executive_data(params)
        .then((res) => {
          this.executive_data = res.data.data;
        })
        .catch((e) => {})
        .finally(() => {});
    },
    // 获取执行用户
    get_executive_data_user() {
      let params = { ...this.search_form };
      params.select_type = 'user';
      get_executive_data(params)
        .then((res) => {
          this.executive_data_user = res.data.data;
        })
        .catch((e) => {})
        .finally(() => {});
    },
    source_type_change(e) {
      this.chart.clear();
      this.get_executive_data();
      this.get_executive_data_user();
      this.get_list();
      this.initChart();
      this.get_header_columns();
      this.source_type_list.forEach((item) => {
        if (e == item.value) {
          this.search_form.source_type = e;
          this.$forceUpdate();
        }
      });
    },
    // 获取采集方式
    select_source_type(e) {
      select_source_type({
        db_type: this.search_form.db_type,
        data_source_id: this.search_form.data_source_id
      })
        .then((res) => {
          this.source_type_list = res.data.data;
          this.$set(
            this.search_form,
            'source_type',
            this.source_type_list[0].value
          );

          this.settime();
          this.get_list();
          this.initChart();
          this.get_header_columns();
          // 默认必须要有采集方式
        })
        .catch((e) => {})
        .finally(() => {});
    },
    // 获取示例名称
    db_type_method(e) {
      this.get_datasource_list(e);
    },
    focus_db_type() {
      this.get_datasource_list();
    },
    get_datasource_list(e) {
      get_datasource_list({
        search_value: e || ''
      })
        .then((res) => {
          this.datasource_list = res.data.data;
        })
        .catch((e) => {})
        .finally(() => {
          this.loading_list = false;
        });
    },
    handleOk() {
      // 自定义表头 确定
      let sort_arr = [];
      this.table_headers_all_list.forEach((item) => {
        this.transfer_value.forEach((v) => {
          if (v == item.key) {
            sort_arr.push(v);
          }
        });
      });
      console.log(this.transfer_value);
      // 后端记录表头
      let params = {
        business_code: this.search_form.business_code,
        db_type: this.search_form.db_type,
        source_type: this.search_form.source_type || 0,
        keys: this.transfer_value
      };
      add_header_columns(params)
        .then((res) => {})
        .catch((e) => {})
        .finally(() => {});

      //  前端生成表头table
      this.table_headers_list = [];
      let arr = [];

      this.transfer_value.forEach((item_) => {
        this.table_headers_all_list.forEach((item) => {
          if (item.key == item_) {
            arr.push(item);
          }
        });
      });

      this.table_headers_list = Array.from(new Set(arr));
      setTimeout(() => {
        this.$refs.table_.doLayout();
        this.title_dialogVisible = false;
      }, 200);
    },
    // 获取表头 get_header_columns
    get_header_columns() {
      let params = this.search_form;
      get_header_columns({
        business_code: this.search_form.business_code,
        source_type: this.search_form.source_type || 0,
        db_type: this.search_form.db_type,
        data_source_id: this.search_form.data_source_id
      })
        .then((res) => {
          let arr = [];
          res.data.data.display_list.forEach((item) => {
            arr.push(item.key);
          });
          this.transfer_value = arr;
          this.table_headers_list = res.data.data.display_list;
          res.data.data.all_list.map((item) => {
            item.label = item.value;
          });
          this.table_headers_all_list = res.data.data.all_list;
        })
        .catch((e) => {});
    },

    toggleFullScreen(e) {
      this.sql_text_content = e;
      this.sql_dialogVisible = true;
    },
    copySql(e) {
      // 创建一个临时的 textarea 元素
      const textarea = document.createElement('textarea');
      textarea.value = e.trim();
      document.body.appendChild(textarea);

      // 执行复制操作
      textarea.select();
      document.execCommand('copy');

      // 移除临时元素
      document.body.removeChild(textarea);
      this.$message.success('复制成功');
    },
    // 鼠标事件
    onMouseEnter(e) {},
    onMouseLeave(e) {},

    // 弹框确认
    add_tag_ok() {
      let params = {
        sql_ids: [],
        ...this.search_form
      };
      if (this.add_tag.radio == '1') {
        params.label_attribute = 0;
      }
      if (this.add_tag.radio == '2') {
        params.label_attribute = 1;
      }
      if (this.add_tag_form.time == '2') {
        params.permanent_day = 0;
      }
      if (this.add_tag_form.time == '1') {
        params.permanent_day = parseInt(this.add_tag_form.name);
      }

      if (this.multipleSelection.length > 0) {
        this.multipleSelection.forEach((item) => {
          params.sql_ids.push(item.sql_id);
        });
      }
      if (this.multipleSelection.length == this.table_data.results.length) {
        params.is_select_all = 1;
      } else {
        params.is_select_all = 0;
      }
      this.$refs['add_tag_form'].validate((valid) => {
        if (valid) {
          save_label(params)
            .then((res) => {
              if (res.data.code == 0) {
                this.$message.success(res.data.message);
                this.add_tag_dialogVisible = false;
              }
            })
            .catch((e) => {});
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 诊断
    diagnose(e) {
      e.title = this.search_form.title;
      e.iconType = this.iconType;
      this.base_data = e;
      this.base_data.db_type = this.search_form.db_type;
      this.diagnose_visible = true;
    },
    trend(e) {
      console.log(this.search_form);
      e.title = this.search_form.title;
      e.iconType = this.iconType;
      this.base_data = e;
      this.base_data.execution_time = [
        moment(this.search_form.time[0]).format('YYYY-MM-DD HH:mm:ss'),
        moment(this.search_form.time[1]).format('YYYY-MM-DD HH:mm:ss')
      ];
      this.base_data.source_type = this.search_form.source_type;
      this.base_data.db_type = this.search_form.db_type;
      this.base_data.data_source_id = this.search_form.data_source_id;
      this.trend_visible = true;
    },
    afterVisibleChangeTrend(val) {
      this.key_status_trend = val;
    },
    afterVisibleChangeDiagnose(val) {
      this.key_status_diagnose = val;
    },
    showDrawer() {
      this.trend_visible = true;
    },
    onClose_diagnose() {
      this.diagnose_visible = false;
    },
    onClose() {
      this.trend_visible = false;
    },

    edit_title() {
      this.title_dialogVisible = true;
    },
    handleBrush(params) {
      // 处理brush事件
      console.log('被圈选的数据范围:', params);
    },
    table_selection_btn() {
      if (this.multipleSelection.length > 0) {
        this.add_tag_dialogVisible = true;
      } else {
        this.$message.info('请重新选择。');
      }
    },

    // tabs 切换
    handleClick(tab, event) {
      console.log(tab, event);
    },
    func_change(e) {
      this.search_form.func = e;
      this.initChart();
    },
    //  获取chart
    chart_show_btn() {
      this.chart_show = !this.chart_show;
      this.initChart();
    },
    getStep(timeStamp) {
      // 趋势图步长策略（性能趋势）：
      // (0~12小时]：数据点间隔1分钟（数据点个数：0 ~720）
      // (12~24小时]：数据点间隔2分钟（数据点个数：360 ~720）
      // (1~2天]：数据点间隔5分钟（数据点个数：288 ~576）
      // (2~7天]：数据点间隔10分钟（数据点个数：192 ~672）
      // (7~30天]：数据点间隔1小时（数据点个数：168 ~720）

      const { startTimestamp, endTimestamp } = timeStamp;

      const diff = (endTimestamp - startTimestamp) / (60 * 60);
      if (diff < 12) {
        return 60;
      }
      if (diff <= 24) {
        return 2 * 60;
      }
      if (diff <= 48) {
        return 5 * 60;
      }
      if (diff <= 7 * 24) {
        return 10 * 60;
      }
      return 60 * 60;
    },
    onChange(e) {
      let step = this.getStep({
        startTimestamp: moment(e[0]._d).unix(),
        endTimestamp: moment(e[1]._d).unix()
      });

      console.log(step);
      this.search_form.step = step;
    },
    initChart() {
      this.chart_loading = true;
      // this.search_form.step = 900;
      console.log(this.search_form);
      get_overview_chart(this.search_form)
        .then((res) => {
          console.log(this.search_form.execution_time);
          const dom = document.getElementById('chart_container');
          this.chart = this.$echarts.init(dom);
          let this_ = this;
          // "连接数使用率"
          let pg_connections_pct = res.data.data[0];
          let times = [];
          pg_connections_pct.values.forEach((item_) => {
            times.push(moment(item_.timestamp * 1000).format('MM-DD HH:mm:ss'));
          });

          let series_arr = [];

          if (res.data.data.length > 0) {
            res.data.data.forEach((item, c_index) => {
              console.log(item);
              series_arr.push({
                name: item.name,
                type: 'line',
                data: item.values,
                smooth: true,
                showSymbol: false,
                yAxisIndex: 0
              });
            });
          }

          const option = {
            title: {
              text: ''
            },
            grid: {
              left: 50,
              top: 10,
              right: 160,
              bottom: 90
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross'
              }
            },
            legend: {
              icon: 'rect', // 设置图例图标为长方形
              itemWidth: 20, // 设置图例图标的宽度
              itemHeight: 10, // 设置图例图标的高度
              align: 'left',
              orient: 'vertical', // 设置图例为垂直排列
              x: 'right', // 设置图例位置在左侧
              y: 'center', // 设置图例垂直居中
              data: ['CPU使用率', '内存使用率', '连接数使用率', '风险SQL个数']
            },
            xAxis: {
              type: 'category',
              data: times,

              axisLabel: {
                rotate: 45, // 设置刻度标签旋转角度
                align: 'center', // 设置对齐方式
                margin: 40
                // interval: this.chart_interval,
              }
            },
            yAxis: [
              {
                type: 'value'
              },
              {
                type: 'value',
                name: '收入'
              }
            ],
            series: series_arr,
            brush: {
              brushType: 'lineX',
              transformable: true,
              geoIndex: 'all',
              show: false,
              xAxisIndex: 'all',
              throttleType: 'debounce', //开启选中延迟后调用回调延迟
              throttleDelay: 600 //选中延迟后调用回调延迟时
            }
          };

          option.series.forEach((series, index) => {
            series.color = ['#00bfff', '#ff8000', '#0000ff', '#ff0080'][index];
          });

          this.chart.on('brushSelected', function (params) {
            if (
              params.batch[0].areas[0] &&
              pg_connections_pct.values.length > 0
            ) {
              let index_ = params.batch[0].areas[0].coordRange[0];
              let index_end = params.batch[0].areas[0].coordRange[1];

              if (pg_connections_pct.values[index_]) {
                this_.search_form.execution_time = [
                  moment(
                    pg_connections_pct.values[index_].timestamp * 1000
                  ).format('YYYY-MM-DD HH:mm:ss'),
                  moment(
                    pg_connections_pct.values[index_end].timestamp * 1000
                  ).format('YYYY-MM-DD HH:mm:ss')
                ];
                console.log(
                  this_.search_form.execution_time,
                  'this_.search_form.execution_time'
                );
                // this_.search_form.time = this_.search_form.execution_time;
              }
              this_.get_list('brushSelected');
            }
          });

          this.chart.setOption(option);

          this.chart.dispatchAction({
            type: 'takeGlobalCursor',
            key: 'brush',
            brushOption: {
              brushType: 'lineX'
            }
          });

          // 自适应窗口大小
          window.addEventListener('resize', () => {
            this.chart.resize();
          });

          console.log(this.search_form.execution_time);
        })
        .catch((e) => {})
        .finally(() => {
          this.chart_loading = false;
        });
    },
    retract() {
      this.retract_icon_status = !this.retract_icon_status;
    },
    handleSizeChange(size) {
      this.search_form.page_size = size;
      this.get_list();
    },
    handleCurrentChange(pageNum) {
      this.search_form.page_number = pageNum;
      this.get_list();
    }
  }
};
</script>

<style lang="less" scoped>
.topsqlaTemp {
  // background: #f9f9f9;
  flex: 1;
  overflow: auto;
  position: relative;

  .search_box {
    margin-bottom: 16px;
    background: #fff;
    border-radius: 2px;
  }

  .search_box_title {
    border-bottom: 1px solid #bcbeca;
    align-items: center;
    color: #333;
    display: flex;
    height: 56px;
    justify-content: space-between;
    padding: 0 10px;
  }

  .search_container {
    padding: 14px 0 4px 0;
    display: flex;
  }

  .flex-container {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
      padding: 0;
      display: inline-block;
      width: 470px;
    }

    .el-select,
    .el-range-editor.el-input__inner {
      width: 100%;
    }
  }

  .chart_box {
    background: #fff;
    margin-top: 16px;
    padding: 0 16px;

    .chart_box_header {
      height: 56px;
      display: flex;
      justify-content: space-around;
      justify-content: space-between;
      align-items: center;
    }

    .chart_container {
      width: 100%;
      height: 246px;
    }
  }

  .tabs_box {
    position: relative;
    background: #fff;
    margin-top: 16px;
    padding: 0 16px;

    .tabs_box_header {
      position: absolute;
      display: flex;
      width: calc(100% - 264px);
      margin-left: 230px;
      top: 10px;
      justify-content: space-between;
    }

    .block {
      margin: 26px 0;
      text-align: right;
    }
  }

  .radio_item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    border: none;
    border-radius: 6px;
    padding: 12px 16px;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 12px;
    background: #f4f5f7;
  }

  .analyse_title {
    font-size: 16px;
    color: #000000e0;
    font-weight: 500;
    margin: 24px 0 20px 10px;
    height: 22px;

    .analyse_title_item {
      height: 22px;
      margin-bottom: 8px;
      font-size: 14px;
      color: #000000e0;
      line-height: 22px;
      font-weight: 500;
    }
  }

  .analyse_box {
    min-height: 168px;
    margin-bottom: 32px;
    background: #ffffff;
    box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014,
      0 9px 28px 8px #0000000d;
    border-radius: 8px;
    display: flex;
  }

  .analyse_img {
    margin: 31px 9px 0 32px;
    width: 120px;
    height: 104px;
  }

  .analyse_li_item {
    height: 32px;
    margin-bottom: 12px;
    list-style: none;
    display: flex;
    align-items: center;
  }

  .analyse_span_icon {
    margin-right: 12px;
    padding: 0 4px;
    min-width: 18px;
    height: 18px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    line-height: 18px;
    border-radius: 50%;
    background: #ff4d4f;
  }

  .sql_box {
    position: absolute;
    top: 0;
    height: 500px;
    background: red;
    z-index: 100000;
    width: 100%;
    height: 100%;
  }
}
</style>

<style>
.radio_item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  border: none;
  border-radius: 6px;
  padding: 12px 16px;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 12px;
  background: #f4f5f7;
}

.search_btn_box .el-form-item--small {
  width: 260px !important;
}

.search_btn_box .el-form-item__content {
  margin-left: 0 !important;
}

.sql_modal .ant-modal-content {
  height: 100vh;
  border-radius: 0;
}

.sql_modal .ant-modal {
  top: 0;
}

.transfe_box .el-transfer-panel {
  width: 260px;
}

/* .device-popover{
   background-color: rgba(0, 0, 0, 0.85) !important;
   border-color: rgba(0, 0, 0, 0.85) !important;
}
.device-popover .popper__arrow::after{
   border-right-color:rgba(0, 0, 0, 0.85) !important;
} */
.test {
  color: #fff;
  background: rgb(76, 187, 58);
}
.pro {
  color: #fff;
  background: rgb(250, 140, 22);
}
.el-input__prefix {
  left: 10px;
  top: 1px;
}
</style>