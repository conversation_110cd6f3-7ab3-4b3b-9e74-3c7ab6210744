export default function(minWidth = 1024) {
  return {
    mounted() {
      this.$nextTick(() => {
        document.body.style.minWidth = minWidth + 'px';
      })
    },
    beforeDestroy() {
      document.body.style.minWidth = '1024px';
    },
    activated() {
      document.body.style.minWidth = minWidth + 'px';
    },
    deactivated() {
      document.body.style.minWidth = '1024px';
    }
  };
}
