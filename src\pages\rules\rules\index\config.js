export default function (ctx) {
  // const dbType = window.localStorage.getItem('db_type') || '';
  const cardColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      scopedSlots: { customRender: 'cardTable' }
    }
  ];
  const columns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      scopedSlots: { customRender: 'name' },
      width: 300
    },
    {
      title: '规则类型',
      dataIndex: 'rule_category',
      key: 'rule_category',
      scopedSlots: { customRender: 'rule_category' },
      width: 100
    },
    {
      title: '数据库类型',
      dataIndex: 'db_type',
      key: 'db_type',
      width: 150,
      scopedSlots: { customRender: 'db_type' }
    },
    {
      title: '规则状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      scopedSlots: { customRender: 'ruleStatus' }
    },
    {
      title: '所属规则集',
      dataIndex: 'rule_set_name',
      key: 'rule_set_name',
      scopedSlots: { customRender: 'rule_set_name' },
      width: 200
    },
    // {
    //   title: '规则描述',
    //   dataIndex: 'desc',
    //   key: 'desc',
    //   scopedSlots: { customRender: 'desc' },
    //   width: 300
    // },
    {
      title: '风险类型',
      dataIndex: 'rule_result',
      key: 'rule_result',
      scopedSlots: { customRender: 'rule_result' },
      width: 300
    },
    {
      title: '修改人',
      dataIndex: 'updated_by',
      key: 'updated_by',
      scopedSlots: { customRender: 'updated_by' },
      width: 130
    },
    {
      title: '修改时间',
      dataIndex: 'updated_time',
      key: 'updated_time',
      width: 200,
      scopedSlots: { customRender: 'updated_time' }
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
      visible: $permissionBatch.some([
        { module: 'rule', values: ['edit', 'delete', 'view'] }
      ])
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const searchFields = (dbType) => {
    return [
      {
        type: 'Input',
        // label: '规则名称',
        key: 'name',
        compIcon: 'highlight',
        props: {
          placeholder: '规则名称'
        }
      },
      {
        type: 'Select',
        // label: '数据库类型',
        key: 'db_type',
        compIcon: 'lu-icon-database',
        props: {
          placeholder: '数据库类型',
          url: '/sqlreview/review/select_all_db_type',
          reqParams: {}
        }
      },
      {
        type: 'Select',
        // label: '规则类型',
        key: 'rule_category',
        compIcon: 'lu-icon-field',
        props: {
          placeholder: '规则类型',
          options: ['IMPALA', 'HIVE'].includes(dbType)
            ? [
              {
                label: 'DML',
                value: 'DML'
              }
            ]
            : [
              {
                label: '全部',
                value: 'ALL'
              },
              {
                label: 'DML',
                value: 'DML'
              },
              {
                label: 'DDL',
                value: 'DDL'
              }
            ]
        }
      },
      {
        type: 'Select',
        // label: '规则状态',
        compIcon: 'play-circle',
        key: 'status',
        props: {
          placeholder: '规则状态',
          options: [
            { label: '启用', value: '1' },
            { label: '禁用', value: '0' }
          ]
        }
      },
      {
        type: 'Select',
        // label: '风险类型',
        compIcon: 'lu-icon-alarm',
        key: 'rule_result',
        props: {
          placeholder: '风险类型',
          options: [
            { label: '高风险', value: '0' },
            { label: '低风险', value: '1' }
          ]

        }
      },
      {
        type: 'Select',
        // label: '所属规则集',
        compIcon: 'lu-icon-rule',
        key: 'rule_set_name',
        props: {
          placeholder: '所属规则集',
          url: `/sqlreview/project/rule_set_all`,
          reqParams: {}
        }
      }
    ];
  };
  // const searchFields = (formData = {}, table) => {
  //   return [
  //     {
  //       type: 'RadioGroup',
  //       label: '规则类型',
  //       key: 'rule_category',
  //       mainSearch: true,
  //       props: {
  //         mode: 'buttonAlone',
  //         options: ['IMPALA', 'HIVE'].includes(dbType)
  //           ? [
  //             {
  //               label: 'DML',
  //               value: 'DML'
  //             }
  //           ]
  //           : [
  //             {
  //               label: '全部',
  //               value: 'ALL'
  //             },
  //             {
  //               label: 'DML',
  //               value: 'DML'
  //             },
  //             {
  //               label: 'DDL',
  //               value: 'DDL'
  //             }
  //           ]
  //       },
  //       listeners: {
  //         change(value) {
  //           const ruleCategory = formData.rule_category;
  //           window.localStorage.setItem('rules-rule-category', ruleCategory)
  //         }
  //       }
  //     },
  //     {
  //       type: 'Select',
  //       label: '风险类型',
  //       key: 'rule_result',
  //       props: {
  //         options: [
  //           { label: '高风险', value: '0' },
  //           { label: '低风险', value: '1' }
  //         ]
  //       }
  //     },
  //     {
  //       type: 'Select',
  //       label: '规则状态',
  //       key: 'status',
  //       props: {
  //         options: [
  //           { label: '启用', value: '1' },
  //           { label: '禁用', value: '0' }
  //         ]
  //       }
  //     },
  //     {
  //       type: 'Input',
  //       label: '修改人',
  //       key: 'updated_by'
  //     },
  //     {
  //       type: 'RangePicker',
  //       label: '修改时间',
  //       key: 'updated_time',
  //       props: {
  //         showTime: {
  //           format: 'YYYY-MM-DD HH:mm:ss'
  //         }
  //         // disabledDate: (current) => {
  //         //   return current > moment().endOf('day');
  //         // }
  //       }
  //     }
  //   ]
  // };
  return {
    columns,
    cardColumns,
    searchFields
  };
}
