const buildEnvChannel = process.env.npm_config_channel;
const defaultConfig = {
  // 皮肤
  theme: 'darkBlue',
  // 布局
  layout: 'left-right-new',
  // 模式
  mode: 'default',
  // 首页相关
  home: '/data/view',
  homeExtend: {
    default: '/data/view',
    dba: '/data/view'
  },
  getHome(role) {
    const path = this.homeExtend[role];
    return path || this.homeExtend['default'];
  }
};
const Map = {
  DEFAULT: Object.assign({}, defaultConfig),
  LUFAX: Object.assign({}, defaultConfig, {
    homeExtend: {
      default: '/data/view',
      dba: '/report'
    }
    // init(params = {}) {
    //   const { Cookie } = params;
    //   const luToken = CommonUtil.getQueryParams(null, '_token');
    //   if (luToken) {
    //     Cookie.set(config.LuTokenKey, luToken);
    //   }
    // }
  }),
  LanHaiBank: Object.assign({}, defaultConfig, {
    SSO: 'toLogin'
  }),
  TaiLongBank: Object.assign({}, defaultConfig),
  BiGuiYuan: Object.assign({}, defaultConfig)
};
module.exports = {
  channel: buildEnvChannel,
  getChannelInfo(channel) {
    const _c = channel || buildEnvChannel;
    return Map[_c] || Map['DEFAULT'];
  }
};
