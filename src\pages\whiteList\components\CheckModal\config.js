export default function (ctx) {
  const tableColumns = [
    {
      title: 'Schema',
      dataIndex: 'schema_name',
      key: 'schema_name',
      width: 200
    },
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name',
      width: 200
    },
    {
      title: '数据量',
      dataIndex: 'table_rows',
      key: 'table_rows',
      width: 200
    },
    {
      title: '分区类型',
      dataIndex: 'partition_type',
      key: 'partition_type',
      width: 200
    },
    {
      title: '分区键',
      dataIndex: 'part_column',
      key: 'part_column',
      width: 200
    },
    {
      title: '表状态',
      dataIndex: 'is_onlie',
      key: 'is_onlie',
      width: 200
    },
    {
      title: '统计信息收集时间',
      dataIndex: 'last_collect_time',
      key: 'last_collect_time',
      width: 270
    },
    {
      title: '表注释',
      dataIndex: 'comment',
      key: 'comment',
      width: 200
    }
  ];
  const indexColumns = [
    {
      title: '索引名称',
      dataIndex: 'index_name',
      key: 'index_name',
      width: 200
    },
    {
      title: '索引类型',
      dataIndex: 'unique_name',
      key: 'unique_name',
      width: 200
    },
    {
      title: '索引字段',
      dataIndex: 'column_name',
      key: 'column_name',
      width: 200
    },
    {
      title: '字段类型',
      dataIndex: 'data_type',
      key: 'data_type',
      width: 250
    },
    {
      title: '字段位置',
      dataIndex: 'column_position',
      key: 'column_position',
      width: 150
    },
    {
      title: '区分度',
      dataIndex: 'cardinality',
      key: 'cardinality',
      width: 150
    }
  ];
  const fieldColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '字段名',
      dataIndex: 'column_name',
      key: 'column_name',
      width: 200
    },
    {
      title: '数据类型',
      dataIndex: 'column_type',
      key: 'column_type',
      width: 200
    },
    {
      title: '可空',
      dataIndex: 'is_nullable',
      key: 'is_nullable',
      customRender: (text, record, index) => {
        return text == 'YES' ? '是' : '否'
      },
      width: 100
    },
    {
      title: '自增',
      key: 'auto_increment',
      dataIndex: 'auto_increment',
      customRender: (text, record, index) => {
        return text == 1 ? '是' : '否'
      },
      width: 100
    },
    {
      title: '缺省值',
      key: 'column_default',
      dataIndex: 'column_default',
      // scopedSlots: { customRender: 'is_onlie' },
      width: 200
    },
    {
      title: '备注',
      key: 'column_comment',
      dataIndex: 'column_comment',
      width: 200
    }
  ];
  const options = [
    { label: '高档（100次以上/分钟）', value: 'high' },
    { label: '中档（10-100次/分钟）', value: 'middle' },
    { label: '低档（10次以下/分钟）', value: 'low' }
  ];
  const sqlOptions = [
    { label: '低档（低于1000条/天）', value: 'low' },
    { label: '中档（1000-10000条/天）', value: 'middle' },
    { label: '高档（大于10000条/天）', value: 'high' }
  ];
  const fields = [
    {
      type: 'RadioGroup',
      label: '峰值调用频率',
      key: 'sqlmap_max_frequency',
      props: {
        options: options,
        disabled: true
      },
      width: 'auto'
    },
    {
      type: 'TableEdit',
      label: '每月数据量增加',
      key: 'sqlmap_monthly_increase',
      getDataMethod: 'getData',
      resetFieldsMethod: 'resetFields',
      initialValue: [],
      props: {
        columns: [
          {
            title: '表名',
            dataIndex: 'table_name',
            key: 'table_name',
            width: 300
          },
          {
            title: 'SCHEMA名',
            dataIndex: 'schema',
            key: 'schema',
            width: 300
          },
          {
            title: '数据增加档数',
            dataIndex: 'frequency',
            key: 'frequency',
            width: 300,
            scopedSlots: { customRender: 'frequency' }
          }
        ],
        editConfig: {
          frequency: (row, record = {}) => {
            return {
              type: 'Select',
              props: {
                options: sqlOptions,
                placeholder: '请选择',
                disabled: true
              },
              rules: [
                // { required: true, message: '该项为必填项' }
              ]
            };
          }
        },
        initEditStatus: true,
        pagination: false,
        size: 'small'
      },
      width: '80%'
    },
    {
      type: 'Textarea',
      label: '备注',
      key: 'sqlmap_note',
      width: '80%',
      props: {
        disabled: true
      }
    }
  ];
  return {
    tableColumns,
    indexColumns,
    fieldColumns,
    fields
  };
}
