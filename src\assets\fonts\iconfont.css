@font-face {
  font-family: "luicon"; /* Project id 2809617 */
  src: url('iconfont.eot?t=1710999200839'); /* IE9 */
  src: url('iconfont.eot?t=1710999200839#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('iconfont.woff2?t=1710999200839') format('woff2'),
       url('iconfont.woff?t=1710999200839') format('woff'),
       url('iconfont.ttf?t=1710999200839') format('truetype'),
       url('iconfont.svg?t=1710999200839#luicon') format('svg');
}

.luicon {
  font-family: "luicon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.lu-icon-realtimesql:before {
  content: "\e6d2";
}

.lu-icon-topsql:before {
  content: "\e6d3";
}

.lu-icon-awspgsql:before {
  content: "\e6d0";
}

.lu-icon-awsmysql:before {
  content: "\e6d1";
}

.lu-icon-mogdb:before {
  content: "\e776";
}

.lu-icon-re-review:before {
  content: "\e6cf";
}

.lu-icon-orderdown:before {
  content: "\e6cd";
}

.lu-icon-orderup:before {
  content: "\e6ce";
}

.lu-icon-goldendb:before {
  content: "\e6cb";
}

.lu-icon-node:before {
  content: "\e6ca";
}

.lu-icon-loading1:before {
  content: "\e962";
}

.lu-icon-folder:before {
  content: "\e6cc";
}

.lu-icon-cpu:before {
  content: "\e6c9";
}

.lu-icon-release-copy:before {
  content: "\ed50";
}

.lu-icon-memo:before {
  content: "\e6c8";
}

.lu-icon-Doris:before {
  content: "\e78a";
}

.lu-icon-rds:before {
  content: "\e6c7";
}

.lu-icon-polardb:before {
  content: "\e6c6";
}

.lu-icon-presto:before {
  content: "\e6c5";
}

.lu-icon-index:before {
  content: "\e6ba";
}

.lu-icon-primarykey:before {
  content: "\e6bb";
}

.lu-icon-indexgroup:before {
  content: "\e6bc";
}

.lu-icon-fieldgroup:before {
  content: "\e6bd";
}

.lu-icon-table1:before {
  content: "\e6be";
}

.lu-icon-viewgroup:before {
  content: "\e6bf";
}

.lu-icon-function2:before {
  content: "\e6c0";
}

.lu-icon-functiongroup:before {
  content: "\e6c1";
}

.lu-icon-view1:before {
  content: "\e6c2";
}

.lu-icon-schema1:before {
  content: "\e6c3";
}

.lu-icon-triggergroup:before {
  content: "\e6c4";
}

.lu-icon-ID:before {
  content: "\e6b9";
}

.lu-icon-apacherocketmq:before {
  content: "\eb40";
}

.lu-icon-amazon:before {
  content: "\e6b8";
}

.lu-icon-dameng:before {
  content: "\e6b7";
}

.lu-icon-filter1:before {
  content: "\e6b6";
}

.lu-icon-unusual:before {
  content: "\e699";
}

.lu-icon-notice:before {
  content: "\e6b5";
}

.lu-icon-nopass:before {
  content: "\e693";
}

.lu-icon-unknown:before {
  content: "\e694";
}

.lu-icon-pass1:before {
  content: "\e695";
}

.lu-icon-download:before {
  content: "\e6ae";
}

.lu-icon-confirm:before {
  content: "\e6af";
}

.lu-icon-code:before {
  content: "\e6b0";
}

.lu-icon-upload:before {
  content: "\e6b2";
}

.lu-icon-warning1:before {
  content: "\e6b4";
}

.lu-icon-day:before {
  content: "\e6b1";
}

.lu-icon-database:before {
  content: "\e6b3";
}

.lu-icon-night:before {
  content: "\e6ad";
}

.lu-icon-alarm:before {
  content: "\e6ab";
}

.lu-icon-ring:before {
  content: "\e6ac";
}

.lu-icon-impala1:before {
  content: "\e6a9";
}

.lu-icon-hive:before {
  content: "\e6aa";
}

.lu-icon-DBA:before {
  content: "\e6a3";
}

.lu-icon-developer:before {
  content: "\e6a4";
}

.lu-icon-if:before {
  content: "\e6a5";
}

.lu-icon-do:before {
  content: "\e6a6";
}

.lu-icon-admin:before {
  content: "\e6a7";
}

.lu-icon-enable:before {
  content: "\e6a8";
}

.lu-icon-kingbase:before {
  content: "\e6a2";
}

.lu-icon-hudi:before {
  content: "\e6a1";
}

.lu-icon-viewlist:before {
  content: "\e696";
}

.lu-icon-viewgrid:before {
  content: "\e697";
}

.lu-icon-java:before {
  content: "\e6a0";
}

.lu-icon-sql:before {
  content: "\e69d";
}

.lu-icon-xml:before {
  content: "\e69e";
}

.lu-icon-annotation:before {
  content: "\e69b";
}

.lu-icon-mybatis:before {
  content: "\e69f";
}

.lu-icon-clean:before {
  content: "\e640";
}

.lu-icon-storage:before {
  content: "\e629";
}

.lu-icon-dbreview:before {
  content: "\e69a";
}

.lu-icon-codereview:before {
  content: "\e69c";
}

.lu-icon-log:before {
  content: "\e692";
}

.lu-icon-whitelist:before {
  content: "\e698";
}

.lu-icon-config1:before {
  content: "\e634";
}

.lu-icon-PC:before {
  content: "\e63c";
}

.lu-icon-order:before {
  content: "\e644";
}

.lu-icon-rule:before {
  content: "\e64e";
}

.lu-icon-user1:before {
  content: "\e68e";
}

.lu-icon-apps:before {
  content: "\e68f";
}

.lu-icon-qreview:before {
  content: "\e690";
}

.lu-icon-sqledit:before {
  content: "\e691";
}

.lu-icon-opengauss:before {
  content: "\e628";
}

.lu-icon-data-sensitive:before {
  content: "\e616";
}

.lu-icon-gaussdb:before {
  content: "\e618";
}

.lu-icon-mapping:before {
  content: "\e601";
}

.lu-icon-function1:before {
  content: "\e602";
}

.lu-icon-router:before {
  content: "\e603";
}

.lu-icon-store:before {
  content: "\e604";
}

.lu-icon-gbase:before {
  content: "\e68d";
}

.lu-icon-gbase1:before {
  content: "\e627";
}

.lu-icon-impala:before {
  content: "\e70b";
}

.lu-icon-capacity:before {
  content: "\e68c";
}

.lu-icon-batch:before {
  content: "\e68b";
}

.lu-icon-operation:before {
  content: "\e689";
}

.lu-icon-safe:before {
  content: "\e68a";
}

.lu-icon-applyfor1:before {
  content: "\e687";
}

.lu-icon-seal:before {
  content: "\e688";
}

.lu-icon-tdsql-1:before {
  content: "\e685";
}

.lu-icon-sql-server:before {
  content: "\e686";
}

.lu-icon-scan:before {
  content: "\e684";
}

.lu-icon-db2:before {
  content: "\e682";
}

.lu-icon-tdsql:before {
  content: "\e683";
}

.lu-icon-oceanbase:before {
  content: "\e681";
}

.lu-icon-loading:before {
  content: "\e680";
}

.lu-icon-app:before {
  content: "\e67f";
}

.lu-icon-disable:before {
  content: "\e679";
}

.lu-icon-sensitive:before {
  content: "\e67c";
}

.lu-icon-impower:before {
  content: "\e67d";
}

.lu-icon-applyfor:before {
  content: "\e67e";
}

.lu-icon-person2:before {
  content: "\e672";
}

.lu-icon-joint:before {
  content: "\e66e";
}

.lu-icon-idea:before {
  content: "\e668";
}

.lu-icon-star:before {
  content: "\e67b";
}

.lu-icon-filter:before {
  content: "\e659";
}

.lu-icon-bell:before {
  content: "\e673";
}

.lu-icon-cost:before {
  content: "\e674";
}

.lu-icon-rollback:before {
  content: "\e675";
}

.lu-icon-pass:before {
  content: "\e676";
}

.lu-icon-exempt:before {
  content: "\e677";
}

.lu-icon-details:before {
  content: "\e678";
}

.lu-icon-tool:before {
  content: "\e67a";
}

.lu-icon-roam:before {
  content: "\e66f";
}

.lu-icon-import:before {
  content: "\e670";
}

.lu-icon-pull:before {
  content: "\e671";
}

.lu-icon-robot:before {
  content: "\e66a";
}

.lu-icon-person1:before {
  content: "\e66b";
}

.lu-icon-staring:before {
  content: "\e66c";
}

.lu-icon-wrong:before {
  content: "\e66d";
}

.lu-icon-right1:before {
  content: "\e669";
}

.lu-icon-format:before {
  content: "\e660";
}

.lu-icon-user-platform:before {
  content: "\e661";
}

.lu-icon-check:before {
  content: "\e662";
}

.lu-icon-vs:before {
  content: "\e663";
}

.lu-icon-user-role:before {
  content: "\e664";
}

.lu-icon-full:before {
  content: "\e665";
}

.lu-icon-quit:before {
  content: "\e667";
}

.lu-icon-stop:before {
  content: "\e65e";
}

.lu-icon-run:before {
  content: "\e65f";
}

.lu-icon-search:before {
  content: "\e65b";
}

.lu-icon-person:before {
  content: "\e65d";
}

.lu-icon-trigger:before {
  content: "\e65c";
}

.lu-icon-minus:before {
  content: "\e641";
}

.lu-icon-edit:before {
  content: "\e643";
}

.lu-icon-end:before {
  content: "\e652";
}

.lu-icon-error:before {
  content: "\e657";
}

.lu-icon-success:before {
  content: "\e658";
}

.lu-icon-pending:before {
  content: "\e659";
}

.lu-icon-warning:before {
  content: "\e65a";
}

.lu-icon-locknew:before {
  content: "\e647";
}

.lu-icon-unlocknew:before {
  content: "\e64b";
}

.lu-icon-sort:before {
  content: "\e656";
}

.lu-icon-business:before {
  content: "\e633";
}

.lu-icon-copy:before {
  content: "\e635";
}

.lu-icon-ddl:before {
  content: "\e636";
}

.lu-icon-dml:before {
  content: "\e637";
}

.lu-icon-delete:before {
  content: "\e638";
}

.lu-icon-editor:before {
  content: "\e639";
}

.lu-icon-function:before {
  content: "\e63a";
}

.lu-icon-field:before {
  content: "\e63b";
}

.lu-icon-exit:before {
  content: "\e63d";
}

.lu-icon-entity:before {
  content: "\e63e";
}

.lu-icon-homenew:before {
  content: "\e63f";
}

.lu-icon-reset:before {
  content: "\e642";
}

.lu-icon-right:before {
  content: "\e645";
}

.lu-icon-list:before {
  content: "\e646";
}

.lu-icon-plusnew:before {
  content: "\e648";
}

.lu-icon-key:before {
  content: "\e649";
}

.lu-icon-memory:before {
  content: "\e64a";
}

.lu-icon-sequence:before {
  content: "\e64c";
}

.lu-icon-sqlresolver:before {
  content: "\e64d";
}

.lu-icon-usernew:before {
  content: "\e64f";
}

.lu-icon-up:before {
  content: "\e650";
}

.lu-icon-view:before {
  content: "\e651";
}

.lu-icon-system:before {
  content: "\e653";
}

.lu-icon-task:before {
  content: "\e654";
}

.lu-icon-schema:before {
  content: "\e655";
}

.lu-icon-rosesql:before {
  content: "\e631";
}

.lu-icon-ubisql:before {
  content: "\e632";
}

.lu-icon-starrocks:before {
  content: "\e630";
}

.lu-icon-flink:before {
  content: "\e62f";
}

.lu-icon-tidb:before {
  content: "\e62e";
}

.lu-icon-free:before {
  content: "\e666";
}

.lu-icon-clickhouse:before {
  content: "\e62a";
}

.lu-icon-kafka:before {
  content: "\e62b";
}

.lu-icon-hbase:before {
  content: "\e62c";
}

.lu-icon-elasticsearch:before {
  content: "\e62d";
}

.lu-icon-oracle:before {
  content: "\e622";
}

.lu-icon-lock:before {
  content: "\e623";
}

.lu-icon-partition:before {
  content: "\e61b";
}

.lu-icon-table:before {
  content: "\e61d";
}

.lu-icon-mysql:before {
  content: "\e624";
}

.lu-icon-pgsql:before {
  content: "\e625";
}

.lu-icon-unlock:before {
  content: "\e626";
}

.lu-icon-extend-info:before {
  content: "\e617";
}

.lu-icon-data-change:before {
  content: "\e613";
}

.lu-icon-user:before {
  content: "\e614";
}

.lu-icon-refresh:before {
  content: "\e610";
}

.lu-icon-config:before {
  content: "\e619";
}

.lu-icon-publish:before {
  content: "\e61a";
}

.lu-icon-expand:before {
  content: "\e61c";
}

.lu-icon-metadata:before {
  content: "\e61e";
}

.lu-icon-change-order:before {
  content: "\e61f";
}

.lu-icon-base-info:before {
  content: "\e620";
}

.lu-icon-databus:before {
  content: "\e621";
}

