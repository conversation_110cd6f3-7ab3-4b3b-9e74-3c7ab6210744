<template>
  <div>
    <!-- 1 -->
    <a-card class="common-pure-card" :bordered="false">
      <div class="common-display">
        <div class="common-pure-card-title">SQL Review 报表统计</div>
        <div class="common-pure-card-dba">
          <a-tabs :active-key="activeKey" type="card" @change="clickTime">
            <a-tab-pane key="week" tab="week"></a-tab-pane>
            <a-tab-pane key="month" tab="month"></a-tab-pane>
            <a-tab-pane key="last3" tab="last 3 months"></a-tab-pane>
          </a-tabs>
          <Form
            style="display: inline-block;"
            ref="baseInfo"
            v-bind="baseInfoParams"
            :formData="baseInfoData"
          />
        </div>
      </div>
    </a-card>
    <!-- 2 -->
    <div class="common-display">
      <headerInfo :headerInfoData="headerInfoData" :headerInfoLoading="headerInfoLoading"></headerInfo>
      <commonView
        :commonViewOption="reviewTagOption"
        :commonViewLoading="reviewResultLoading"
        :commonViewTitle="'项目review结果一览'"
      ></commonView>
    </div>
    <!-- 3 -->
    <div class="common-display">
      <commonView
        :commonViewOption="passReadOption"
        :commonViewLoading="passReadLoading"
        :commonViewTitle="'应用通过率排行(识别率、通过率、未通过率)'"
      ></commonView>
      <commonView
        :commonViewOption="failReadOption"
        :commonViewLoading="failReadLoading"
        :commonViewTitle="'未知/错误占比图'"
      ></commonView>
    </div>
    <!-- 4 -->
    <div class="common-display">
      <dbaReview
        :dbaReviewOption="dbaReviewOption"
        :dbaDataSource="dbaDataSource"
        :dbaReviewLoading="dbaReviewLoading"
      ></dbaReview>
    </div>
    <!-- 5   -->
    <div class="common-display">
      <commonView
        :commonViewOption="projectReadOption"
        :commonViewLoading="projectReadLoading"
        :commonViewTitle="'应用排行'"
      ></commonView>
    </div>
  </div>
</template>

<script>
import Form from '@/components/Form';
import config from './config';
import format from 'date-fns/format';
import dateFns from 'date-fns';
// import moment from 'moment';
import headerInfo from './components/headerInfo/index';
import dbaReview from './components/dbaReview/index';
import commonView from './components/commonView/index';

import {
  getHeaderInfo,
  getReviewResult,
  getPassRead,
  getFailRead,
  getDbaRead,
  getProjectRead
} from '@/api/report';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Form,
    headerInfo,
    dbaReview,
    commonView
  },
  data() {
    this.config = config(this);
    return {
      activeKey: 'week', // 默认选中 tab
      baseInfoParams: {
        fixedLabel: false,
        multiCols: 1,
        layout: 'horizontal',
        fields: this.config.baseInfo
      },
      baseInfoData: {},
      // BU标识：陆金所
      headerInfoLoading: false,
      headerInfoData: {},
      // 项目review结果一览
      reviewResultLoading: false,
      reviewTagOption: null,
      // 应用通过率排行
      passReadLoading: false,
      passReadOption: null,
      // 未知/错误占比图
      failReadLoading: false,
      failReadOption: null,
      // DBA review统计
      dbaReviewLoading: false,
      dbaReviewOption: null,
      dbaDataSource: [],
      // 应用排行
      projectReadLoading: false,
      projectReadOption: null
    };
  },
  created() {},
  mounted() {
    let nowTime = new Date(Date.now());
    let beginTime = format(dateFns.subDays(nowTime, 7), 'YYYY-MM-DD HH:mm:ss');
    let endTime = format(nowTime, 'YYYY-MM-DD HH:mm:ss');
    let timeRange = beginTime + ',' + endTime;
    this.getData(timeRange);
  },
  beforeDestroy() {},
  methods: {
    getData(time) {
      this.getHeaderInfoData(time);
      this.getpassRateOption(time);
      this.getPassReadOption(time);
      this.getFailReadOption(time);
      this.getdbaReviewOption(time);
      this.getProjectReadOption(time);
    },
    // 时间按钮
    clickTime(type) {
      this.baseInfoData = Object.assign({}, this.baseInfoData, {
        time_range: [null, null]
      });
      this.activeKey = type;
      let nowTime = new Date(Date.now());
      let beginTime = '';
      let endTime = format(nowTime, 'YYYY-MM-DD HH:mm:ss');
      if (type === 'week') {
        beginTime = format(dateFns.subDays(nowTime, 7), 'YYYY-MM-DD HH:mm:ss');
      } else if (type === 'month') {
        beginTime = format(
          dateFns.subMonths(nowTime, 1),
          'YYYY-MM-DD HH:mm:ss'
        );
      } else {
        beginTime = format(
          dateFns.subMonths(nowTime, 3),
          'YYYY-MM-DD HH:mm:ss'
        );
      }
      let timeRange = beginTime + ',' + endTime;
      this.getData(timeRange);
    },
    // BU标识：陆金所
    getHeaderInfoData(time) {
      this.headerInfoLoading = true;
      getHeaderInfo({ time_range: time })
        .then(res => {
          this.headerInfoLoading = false;
          if (_.get(res, 'data.code') == 0) {
            this.headerInfoData = res.data.data || {};
          }
        })
        .catch(e => {
          this.headerInfoLoading = false;
        });
    },
    // 项目review结果一览
    getpassRateOption(time) {
      this.reviewResultLoading = true;
      getReviewResult({ time_range: time })
        .then(res => {
          this.reviewResultLoading = false;
          if (_.get(res, 'data.code') == 0) {
            this.reviewTagOption = this.config.reviewTagOption({
              data: res.data.data || []
            });
          }
        })
        .catch(e => {
          this.reviewResultLoading = false;
        });
    },
    // 应用通过率排行
    getPassReadOption(time) {
      this.passReadLoading = true;
      getPassRead({ time_range: time })
        .then(res => {
          this.passReadLoading = false;
          if (_.get(res, 'data.code') == 0) {
            this.passReadOption = this.config.passReadOption({
              data: res.data.data || []
            });
          }
        })
        .catch(e => {
          this.passReadLoading = false;
        });
    },
    // 未知/错误占比图
    getFailReadOption(time) {
      this.failReadLoading = true;
      getFailRead({ time_range: time })
        .then(res => {
          this.failReadLoading = false;
          if (_.get(res, 'data.code') == 0) {
            this.failReadOption = this.config.failReadOption({
              data: res.data.data || []
            });
          }
        })
        .catch(e => {
          this.failReadLoading = false;
        });
    },
    // DBA review统计
    getdbaReviewOption(time) {
      this.dbaReviewLoading = true;
      getDbaRead({ time_range: time })
        .then(res => {
          this.dbaReviewLoading = false;
          if (_.get(res, 'data.code') == 0) {
            this.dbaDataSource = res.data.data || [];
            this.dbaReviewOption = this.config.dbaReviewOption({
              data: res.data.data || []
            });
          }
        })
        .catch(e => {
          this.dbaReviewLoading = false;
        });
    },
    // 应用排行
    getProjectReadOption(time) {
      this.projectReadLoading = true;
      getProjectRead({ time_range: time })
        .then(res => {
          this.projectReadLoading = false;
          if (_.get(res, 'data.code') == 0) {
            this.projectReadOption = this.config.projectReadOption({
              data: res.data.data || []
            });
          }
        })
        .catch(e => {
          this.projectReadLoading = false;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.common-display {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .common-pure-card-title {
    font-size: 20px;
    color: #262629;
    font-weight: 500;
  }
  .common-pure-card-dba {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .ant-tabs {
      margin-right: 10px;
      /deep/.ant-tabs-nav-container {
        height: 32px;
        /deep/.ant-tabs-tab {
          height: 32px;
          line-height: 32px;
        }
      }
    }
  }
  .spin {
    flex: 1;
  }
}
</style>
