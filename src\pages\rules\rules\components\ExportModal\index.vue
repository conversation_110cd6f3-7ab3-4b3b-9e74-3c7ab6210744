<template>
  <a-modal
    v-model="visible"
    :title="'文件类型选择'"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <div>请选择导出文件类型，如需进行规则导入，请下载json文件进行导入</div>
    <Form ref="form" v-bind="formParams" />
  </a-modal>
</template>

<script>
import Form from '@/components/Form';

export default {
  components: { Form },
  props: {},
  data() {
    return {
      visible: false,
      formParams: {
        fields: [
          {
            type: 'RadioGroup',
            label: '',
            props: {
              options: [
                {
                  label: 'excel文件',
                  value: 'excel'
                },
                {
                  label: 'json文件',
                  value: 'json'
                }
              ]
            },
            key: 'file_format',
            rules: [{ required: true, message: '该项为必填项' }]
          }
        ],
        layout: 'horizontal',
        labelCol: { span: 24 },
        wrapperCol: { span: 24 },
        multiCols: 1
      }
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show() {
      this.visible = true;
    },
    hide() {
      this.visible = false;
      const { form } = this.$refs;
      form && form.resetFields();
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      form && form.validate((valid, error) => {
        if (!valid) {
          return
        }
        this.$emit('save', form.getData().file_format);
      })
    }
  }
};
</script>

<style lang="less" scoped>
</style>
