<template>
  <a-tabs
    v-model="activeKey"
    type="card"
    @change="tabChange"
    class="tracking-list"
  >
    <a-tab-pane :key="item.key" :tab="item.tab" v-for="item in tabs">
      <TableList
        type="sql"
        :ref="item.key"
        :tabKey="item.key"
        :rangeTime="rangeTime"
      ></TableList>
    </a-tab-pane>
    <div slot="tabBarExtraContent">
      <a-range-picker
        v-model="dateModel"
        :show-time="{
          hideDisabledOptions: true,
          defaultValue: [
            moment('00:00:00', 'HH:mm:ss'),
            moment('11:59:59', 'HH:mm:ss')
          ]
        }"
        :format="dateFormat"
        @ok="rangePickerChange"
      />
    </div>
  </a-tabs>
</template>
<script>
import TableList from './TableList';
import moment from 'moment';
export default {
  components: { TableList },
  data() {
    return {
      activeKey: 'oneWeeky',
      tabs: [
        {
          key: 'oneWeeky',
          tab: '近1周'
        },
        {
          key: 'oneMonth',
          tab: '近1月'
        },
        {
          key: 'threeMonth',
          tab: '近3月'
        },
        {
          key: 'rangePicker',
          tab: '自选时间'
        }
      ],
      dateModel: [null, null],
      dateFormat: ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss'],
      rangeTime: {
        activeKey: 'oneWeeky',
        value: [
          moment()
            .subtract(7, 'day')
            .format('YYYY-MM-DD HH:mm:ss'),
          moment().format('YYYY-MM-DD HH:mm:ss')
        ]
      }
    };
  },
  methods: {
    moment,
    rangePickerChange(values) {
      this.tabChange('rangePicker', values);
    },
    tabChange(activeKey, values) {
      this.activeKey = activeKey;
      !values && (this.dateModel = [null, null]);

      let num = null;
      switch (activeKey) {
        case 'oneWeeky':
          num = 7;
          break;
        case 'oneMonth':
          num = 30;
          break;
        case 'threeMonth':
          num = 90;
          break;
        default:
          break;
      }
      let endTime = null;
      let startTime = null;
      if (values) {
        startTime = moment(values[0]).format('YYYY-MM-DD HH:mm:ss');
        endTime = moment(values[1]).format('YYYY-MM-DD HH:mm:ss');
      } else {
        startTime = moment()
          .subtract(num, 'day')
          .format('YYYY-MM-DD HH:mm:ss');
        endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      }

      this.rangeTime = {
        activeKey,
        value: [startTime, endTime]
      };
    },
    getSearchParamsFn() {
      const ref = _.get(this.$refs, `${this.activeKey}.0`);
      const data = ref.getSearchParamsFn() || {};
      const res = {
        ...data,
        time_range: this.rangeTime.value
      };
      return res;
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .ant-tabs-nav-scroll {
  > .ant-tabs-nav {
    > div {
      .ant-tabs-tab:last-child {
        display: none;
      }
      .ant-tabs-tab {
        margin-right: 12px;
        height: 32px;
        line-height: 32px;
        margin-top: 4px;
      }
    }
  }
}
.tracking-list {
  /deep/ .ant-tabs-bar {
    display: flex;
    justify-content: flex-end;
    flex-direction: row-reverse;
    margin: 0;
  }
  // /deep/ .ant-tabs-extra-content {
  //   > div {
  //     > .ant-calendar-picker {
  //       > .ant-calendar-picker-input {
  //         height: 32px !important;
  //       }
  //     }
  //   }
  // }
}
</style>