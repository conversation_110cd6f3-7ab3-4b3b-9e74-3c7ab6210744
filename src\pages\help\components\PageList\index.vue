<template>
  <PageListContent v-bind="params">
    <!-- table插槽 -->
    <a slot="name" slot-scope="{ text }">{{ text }}</a>
    <span slot="customTitle">
      <a-icon type="smile-o" />Name
    </span>
    <span slot="tags" slot-scope="{ text }">
      <a-tag
        v-for="tag in text"
        :key="tag"
        :color="tag === 'loser' ? 'volcano' : tag.length > 5 ? 'geekblue' : 'green'"
      >{{ tag }}</a-tag>
    </span>
    <span slot="action" slot-scope="{ record }">
      <a @click="lineClick(record, $event)">Invite 一 {{record.name }}</a>
      <a-divider type="vertical" />
      <a>Delete</a>
      <a-divider type="vertical" />
      <a class="ant-dropdown-link">
        More actions
        <a-icon type="down" />
      </a>
    </span>
  </PageListContent>
</template>

<script>
import PageListContent from '@/components/PageList/content';
import common from '@/utils/common';
import config from './config';

export default {
  components: { PageListContent },
  props: {},
  data() {
    this.config = config(this);

    // 方法一：
    // table columns主体配置到config.js
    // jsx语法必须写在这里，才能解析!!!
    // 做一个合并，会消耗一些性能，可尽量保持主页简单（也可以把配置都拿进来）
    const columnsBindinfo = {
      // name: {
      //   customRender: (text, row, index) => {
      //     return <a>{text}</a>;
      //   }
      // }
      // tags: {
      //   customRender: (text, row, index) => {
      //     return text.map(item => <a-tag>{item}</a-tag>);
      //   }
      // }
    };
    common.combineColumns(this.config.columns, columnsBindinfo);

    // 方法二（封装组件如果被嵌套封装，必须层层将slot传递进去）:
    // 参照antd-vue-table例子
    // 传入所需替换的slot，由table组件处理
    // config.js里还是配置slots和scopedSlots
    // 注意取值有些不一样，参考例子

    return {
      params: {
        tableParams: {
          url: '/api/home/<USER>/table',
          reqParams: {
            from: 'pageList'
          },
          columns: this.config.columns,
          rowSelection: {
            // type: 'radio'
          }
          // needAllSelectedRows: true
        },
        btns: this.config.btns,
        searches: this.config.searches
      }
    };
  },
  mounted() {
    // 全局loading测试
    // this.$showLoading();
    // setTimeout(() => {
    //   this.$hideLoading();
    // }, 2000);
  },
  created() {},
  methods: {
    btnTest() {
      console.log('hahaha btnTest!!!!');
    },
    lineClick(record, event) {
      console.log('lineTest!!!!', record, event);
    }
  }
};
</script>

<style lang="less" scoped>
</style>
