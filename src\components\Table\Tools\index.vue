<template>
  <div class="custom-table-tools">
    <a-tooltip v-for="item in extraTools" :key="item.key">
      <template slot="title">{{item.tips}}</template>
      <a-icon :type="item.icon" @click="() => { item.callback && item.callback() }" />
    </a-tooltip>
    <a-tooltip>
      <template slot="title">高级筛选</template>
      <custom-icon :type="filterIcon" @click="filter" />
    </a-tooltip>
    <a-tooltip>
      <template slot="title">重置</template>
      <custom-icon :type="resetIcon" @click="reset" />
    </a-tooltip>
    <a-tooltip v-if="layout !== 'left-right-new'">
      <template slot="title">刷新</template>
      <custom-icon type="sync" @click="search" />
    </a-tooltip>
    <a-tooltip v-if="uniqKey">
      <template slot="title">自定义列</template>
      <a-icon type="menu" @click="CustomSetColumn" />
    </a-tooltip>
    <span v-for="item in extraToolsAfter" :key="item.key" class="extra-tools-after">
      <a-divider type="vertical" v-if="item.isDivider" />
      <a-tooltip>
        <template slot="title">{{item.tips}}</template>
        <custom-icon :type="item.icon" @click="() => { item.callback && item.callback() }" />
      </a-tooltip>
    </span>
    <!-- 筛选抽屉 -->
    <FilterDrawer ref="FilterDrawer" :fields="fields" @search="search" @reset="reset"></FilterDrawer>
    <!-- 自定义列弹窗 -->
    <ColumnModal ref="ColumnModal" @save="onFilterColumns"></ColumnModal>
  </div>
</template>

<script>
import FilterDrawer from './Filter';
import ColumnModal from './Column';
export default {
  components: { FilterDrawer, ColumnModal },
  props: {
    fields: {
      type: Array,
      default: function() {
        return [];
      }
    },
    searchData: {
      type: Object,
      default: () => ({})
    },
    extraTools: {
      type: Array,
      default: () => []
    },
    extraToolsAfter: {
      type: Array,
      default: () => []
    }
  },
  inject: ['uniqKey'],
  data() {
    const layout = window.CHANNEL_INFO.layout;
    return {
      layout
    };
  },
  computed: {
    filterIcon() {
      return this.layout == 'left-right-new' ? 'lu-icon-filter1' : 'filter';
    },
    resetIcon() {
      return this.layout == 'left-right-new' ? 'lu-icon-clean' : 'lu-icon-free';
    },
    refreshIcon() {
      return this.layout == 'left-right-new' ? '' : 'sync';
    }
  },
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    reset() {
      this.$emit('reset');
    },
    search(data = {}) {
      this.$emit('search', data);
    },
    filter() {
      // this.$emit('filter');
      this.$refs.FilterDrawer.show(this.searchData);
    },
    CustomSetColumn() {
      this.$refs.ColumnModal.show();
    },
    onFilterColumns(checkedKeys) {
      this.$emit('filterColumns', checkedKeys);
    }
  },
  watch: {
    searchData(newVal, oldVal) {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        this.$refs.FilterDrawer.setData(newVal);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.custom-table-tools {
  padding-bottom: 4px;
  .anticon {
    margin-left: 8px;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.45);
    border-radius: 50%;
    width: 26px;
    height: 26px;
    line-height: 30px;
    &:hover {
      background: @primary-3;
      color: #ffffff;
    }
  }
  .ant-divider {
    width: 0.86px;
    height: 20px;
    background: #e0e0e0;
  }
  .extra-tools-after {
    .ant-divider {
      margin: 0 0 0 8px;
    }
  }
}
</style>
