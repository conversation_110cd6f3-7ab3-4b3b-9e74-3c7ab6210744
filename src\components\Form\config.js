import Select from '@/components/Select';
import TableEdit from '@/components/TableEdit';
import Label from '@/components/Label';
import InputNumRange from '@/components/InputNumRange';
import YearPicker from '@/components/YearPicker';
import TreeSelect from '@/components/TreeSelect';
import Coder from '@/components/Coder';
import RadioGroup from '@/components/RadioGroup';
import CheckboxGroup from '@/components/CheckboxGroup';
import RichEditor from '@/components/RichEditor';
import Markdown from '@/components/Markdown';
import Cascader from '@/components/Cascader';
import InputSelect from '@/components/InputSelect';
import Email from '@/components/Email';
import InputPassword from '@/components/InputPassword';

export default function (ctx) {
  const defineComp = {
      Select,
      TableEdit,
      Label,
      InputNumRange,
      YearPicker,
      TreeSelect,
      Coder,
      RadioGroup,
      CheckboxGroup,
      RichEditor,
      Markdown,
      Cascader,
      InputSelect,
      Email,
      InputPassword
  }
  const compMap = {
    Input: {
      props: {
        placeholder: '请输入',
        maxLength: 255
      }
    },
    InputNumber: {
      props: {
        placeholder: '请输入'
      }
    },
    InputPassword: {
      compName: 'InputPassword',
      props: {
        placeholder: '请输入'
      }
    },
    Textarea: {
      props: {
        placeholder: '请输入'
        // maxLength: 255
      }
    },
    Select: {
      compName: 'Select',
      props: {
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'children'
      }
    },
    TableEdit: {
      compName: 'TableEdit',
      props: {}
    },
    Label: {
      compName: 'Label'
    },
    InputNumRange: {
      compName: 'InputNumRange'
    },
    YearPicker: {
      compName: 'YearPicker'
    },
    TreeSelect: {
      compName: 'TreeSelect'
    },
    UserRoleModal: {
      compName: 'UserRoleModal'
    },
    Coder: {
      compName: 'Coder'
    },
    RadioGroup: {
      compName: 'RadioGroup'
    },
    CheckboxGroup: {
      compName: 'CheckboxGroup'
    },
    RichEditor: {
      compName: 'RichEditor'
    },
    Markdown: {
      compName: 'Markdown'
    },
    Cascader: {
      compName: 'Cascader'
    },
    InputSelect: {
      compName: 'InputSelect'
    },
    Email: {
      compName: 'Email'
    }
  };
  // 处理文字回显
  const setDataText = function (value) {
    const type = ctx.fieldOption.type;
    // 下拉框
    if (type == 'Select') {
      ctx.dataTxt = '';
      if (!value) return;
      const {props = {}} = ctx.fieldOption
      if (props.mode == 'tags' || props.mode == 'multiple') {
        const valueData = props.mode == 'tags' ? value.split(props.separator || ',') : value;
        // 多选
        const choseData = ctx.sourceData.filter(item => valueData.indexOf(item.value) > -1);
        let textArr = [];
        choseData.map((item) => {
          textArr.push(item.label);
        })
        ctx.dataTxt = textArr.join();
      } else {
        // 单选
        ctx.sourceData.map((item) => {
          if (item.value == value) {
            ctx.dataTxt = item.label;
          }
        });
      }
    }
  }
  return {
    defineComp,
    compMap,
    setDataText
  }
}