import Http from '@/utils/request'

export function getOrderTotal(params = {}) {
  return Http({
    url: `/sqlreview/review/order_total/`,
    method: 'get',
    params: params
  });
}

export function getPassInfo(params = {}) {
  return Http({
    url: `/sqlreview/review/review_information`,
    method: 'get',
    params: params
  });
}

export function sqlWhitePassOrReject(params = {}) {
  return Http({
    url: `/sqlreview/review/white_change_state`,
    method: 'post',
    data: params
  });
}

export function allPass(params = {}) {
  return Http({
    url: `/sqlreview/review/white_all_pass`,
    method: 'put',
    data: params
  });
}

// 一键驳回
export function allReject(params = {}) {
  return Http({
    url: `/sqlreview/review/review_detail_all_rejected`,
    method: 'post',
    data: params
  });
}

// 一键驳回初始信息
export function getReviewInformation(params = {}) {
  return Http({
    url: `/sqlreview/review/review_information`,
    method: 'get',
    params: params
  });
}

// 数据审核接口区域

// 数据库审核列表, 一键通过、一键驳回
export function databaseReviewPassOrReject(params = {}) {
  return Http({
    url: `/sqlreview/review/database_review/list_audit`,
    method: 'post',
    data: params
  });
}

// 数据库审核详情列表 单条通过
export function databaseReviewDetailPass(params = {}) {
  return Http({
    url: `/sqlreview/review/label_card/${params.id}/`,
    method: 'put',
    data: { audit_status: params.audit_status }
  });
}

// 数据库审核详情列表 单条驳回
export function databaseReviewDetailReject(params = {}) {
  return Http({
    url: `/sqlreview/review/label_card/${params.id}/`,
    method: 'patch',
    data: { audit_status: params.audit_status }
  });
}

// 数据库审核详情列表 批量驳回
export function databaseReviewDetailBatch(params = {}) {
  return Http({
    url: `/sqlreview/review/database_review/detail_audit`,
    method: 'post',
    data: params
  });
}

export default {};