// import './style.less';

const markText = function (from = {}, to = {}, options = {}) {
  console.log('mark text');
  const cm = this.coder;
  const doc = cm.getDoc();

  doc.markText(from, to, options);
}
const clearMarks = function () {
  console.log('clear text');
  const cm = this.coder;
  const doc = cm.getDoc();

  doc.getAllMarks().forEach(item => item.clear && item.clear());
}

export default {
  markText,
  clearMarks
};