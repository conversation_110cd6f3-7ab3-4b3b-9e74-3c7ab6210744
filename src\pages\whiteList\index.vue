<template>
  <div class="white-list-content">
    <!-- 页面概述 -->
    <div class="header">
      <div :class="['summary', isCollapse && 'collapse']" @click="onCollapse">
        <div class="title">
          <span>查询</span>
          <custom-icon :type="isCollapse ? 'down' : 'up'"></custom-icon>
        </div>
      </div>
      <!-- 搜索内容 -->
      <div
        :class="['search-content', isCollapse && 'collapse']"
        v-show="!isCollapse"
      >
        <SearchArea
          v-bind="searchParams || {}"
          @reset="reset"
          @search="search"
          ref="search"
          searchMode="switch"
          :iconCombine="false"
          :isShowExpandIcon="false"
          :searchData="searchData || {}"
        ></SearchArea>
      </div>
    </div>
    <div class="table-content">
      <Table
        ref="table"
        v-bind="tableParams"
        class="new-view-table"
        :dataSource="dataSource"
        :rowSelection="rowSelection"
        @selectChange="selectChange"
      >
        <template slot="tableTopLeft">
          <div>白名单列表</div>
        </template>
        <template slot="tableTopRight">
          <!-- <a-button slot="extra" class="new-button" @click="batchExport"
            >批量导出</a-button
          > -->
          <!-- <a-button
            slot="extra"
            class="new-button"
            @click="batchDelete"
            v-if="$permission.whiteList('batchDelete')"
            >批量移除</a-button
          > -->
          <a-popconfirm
            slot="extra"
            title="确定删除数据?"
            @confirm="batchDelete"
            v-if="$permission.whiteList('batchDel')"
          >
            <a-button type="primary">批量移除</a-button>
          </a-popconfirm>
        </template>
        <!-- 列表插槽 -->
        <a
          slot="label_id"
          slot-scope="{ text, record }"
          @click="toDetail(record)"
          class="label-id"
          ><LimitLabel :limit="24" :label="text" :needCopy="true"></LimitLabel
        ></a>

        <template slot="label_type" slot-scope="{ text }">{{
          labelTypeText[text]
        }}</template>
        <span class="projects-box" slot="projects" slot-scope="{ text }">
          <LimitTags
            :tags="text ? text.map((item) => ({ label: item })) : []"
            :limit="1"
            mode="numTag"
          ></LimitTags>
        </span>
        <span slot="label_status" slot-scope="{ text }" :class="text">{{
          text == 'pass' ? '审核通过' : '已失效'
        }}</span>
        <template slot="data_source" slot-scope="{ text }">
          <span
            class="datasource-content"
            :key="index"
            v-for="(item, index) in text"
          >
            <span :class="item.env == 'TEST' ? 'test' : 'prod'">{{
              item.env == 'TEST' ? '测试' : '生产'
            }}</span>
            <DbImg
              :value="item.db_type"
              :schemaName="item.name + '(' + item.db_url + ')'"
              mode="ellipsis"
            />
          </span>
        </template>
        <LimitLabel
          slot="sql_source"
          slot-scope="{ text }"
          :limit="24"
          :label="text"
          :needCopy="true"
          :nowrap="true"
          format="sql"
        ></LimitLabel>
        <template slot="audit_user" slot-scope="{ record, text }">
          <a-tooltip v-if="record.ch_audit_user" placement="topLeft">
            <template slot="title">
              <span>{{ text }}</span>
            </template>
            <div class="des">
              <a-icon type="user" />
              <span>{{ record.ch_audit_user }}</span>
            </div>
          </a-tooltip>
          <span v-else>{{ text || '--' }}</span>
        </template>
        <template slot="created_by" slot-scope="{ record, text }">
          <a-tooltip v-if="record.ch_created_by" placement="topLeft">
            <template slot="title">
              <span>{{ text }}</span>
            </template>
            <div class="des">
              <a-icon type="user" />
              <span>{{ record.ch_created_by }}</span>
            </div>
          </a-tooltip>
          <span v-else>{{ text || '--' }}</span>
        </template>
        <custom-btns-wrapper
          slot="action"
          slot-scope="{ text, record }"
          :limit="3"
        >
          <!-- <a-popconfirm
            title="确定移除该数据?"
            @confirm="() => remove(record)"
            v-if="$permission.whiteList('delete')"
          > -->
          <a-popconfirm
            title="确定移除该数据?"
            @confirm="() => remove(record)"
            v-if="$permission.whiteList('del')"
          >
            <a actionBtn class="remove">移除</a>
          </a-popconfirm>
        </custom-btns-wrapper>
      </Table>
    </div>
  </div>
</template>
<script>
import { removeRecord, batchDelete } from '@/api/whitelist';
import LimitLabel from '@/components/LimitLabel';
import LimitTags from '@/components/LimitTags';
import StatusTag from '@/components/Biz/Status/Tag';
import Table from '@/components/Table';

import Status from '@/components/Biz/Status';
import SearchArea from '@/components/Biz/SearchArea/new';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import tableSelectAll from '@/mixins/tableSelectAll';
export default {
  name: 'white-list',
  components: {
    Table,
    Status,
    LimitTags,
    StatusTag,
    SearchArea,
    LimitLabel
  },
  mixins: [bodyMinWidth(1280), tableSelectAll({})],
  props: {},
  data() {
    this.config = config(this);
    return {
      isCollapse: true,
      statusColor: this.config.statusColor,
      searchData: {},
      searchParams: {
        fields: this.config.searchFields
      },
      dataSource: [],
      tableParams: {
        url: '/sqlreview/review/white_review/list',
        reqParams: {},
        method: 'get',
        columns: this.config.columns.filter(item => item.visible !== false),
        rowKey: 'id',
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' },
        loaded: this.onTableLoaded
      },
      labelTypeText: {
        0: 'SQL_ID',
        1: 'XML_TEXT',
        2: 'XML_ID',
        3: 'TABLE',
        4: 'JAVA',
        5: 'PROCEDURE'
      }
    };
  },
  created() {},
  mounted() {},
  destroyed() {},
  computed: {},
  methods: {
    toDetail(record, e) {
      this.$router.push({
        name: 'white-list-detail',
        params: { id: record.id }
      });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
      this.$store.commit('common/setSearchCache', {
        whiteList: data
      });
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.onReset();
    },
    refresh() {
      const { table } = this.$refs;
      table.refreshKeep();
    },
    remove(record) {
      this.$showLoading();
      removeRecord({ id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // onSearch() {
    //   const data = { id: this.localSearchValue };
    //   const { table } = this.$refs;
    //   const { searchParams } = table;
    //   Object.assign(searchParams, { ...data });
    //   table.refresh(null, data);
    // },
    onCollapse() {
      this.isCollapse = !this.isCollapse;
    },
    batchExport() {},
    batchDelete() {
      if (_.isEmpty(this.selectedRowKeys)) {
        this.$message.warning('未选择数据');
        return;
      }
      const { table } = this.$refs;
      const { searchParams } = table;
      const params = {
        ...searchParams,
        examine_list: this.isSelectAll ? this.excludes : this.includes,
        is_select_all: this.isSelectAll ? 1 : 0
      };
      this.$showLoading();
      batchDelete(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.resetAllCheckData();
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
@assetsUrl: '~@/assets';
.white-list-content {
  .header {
    border-radius: 16px;
    margin-bottom: 16px;
    background-color: #fff;
    .summary {
      padding: 16px 24px;
      border-radius: 16px;
      cursor: pointer;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        > span {
          font-weight: 600;
        }
        .anticon {
          font-size: 14px;
          color: #8c8c8c;
        }
      }
    }
    .search-content {
      border-top: 1px solid #f0f0f0;
      &.collapse {
        .title {
          border-bottom: none;
        }
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        > .anticon {
          font-size: 14px;
          color: #8c8c8c;
          margin-right: 0;
        }
      }
      /deep/.search-area {
        box-shadow: none;
        border: none;
      }
    }
  }
  .table-content {
    border-radius: 16px;
    background: #fff;
    /deep/.custom-table {
      // .ant-table {
      //   line-height: none !important;
      // }
      .search-area-wrapper {
        display: flex;
        justify-content: space-between !important;
        align-items: center;
        padding: 12px 24px;
        border-bottom: solid 1px #fafafa;
        .custom-table-top-left {
          > div {
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #1f1f1f;
            font-weight: 600;
          }
        }
        .custom-table-top-right {
          .new-button {
            margin-left: 12px;
          }
        }
      }
      .datasource-content {
        display: flex;
        align-items: center;
        > span {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          border-radius: 4px;
          margin: 0 4px 0 0;
          padding: 0 4px;
        }
        .test {
          background: #f6ffed;
          border: 1px solid rgba(183, 235, 143, 1);
          color: #52c41a;
        }
        .prod {
          background: #fff7e6;
          border: 1px solid rgba(255, 213, 145, 1);
          color: #fa8c16;
        }
        .limit-label {
          width: 120px;
        }
      }
      .label-id {
        .limit-label {
          pre {
            color: @primary-6 !important;
          }
        }
      }
      .pass {
        background: #f6ffed;
        border: 1px solid rgba(183, 235, 143, 1);
        color: #52c41a;
        font-size: 12px;
        padding: 2px 4px;
        border-radius: 4px;
      }
      .expired {
        background: #fafafa;
        border: 1px solid rgba(217, 217, 217, 1);
        font-size: 12px;
        color: #1f1f1f;
        padding: 2px 4px;
        border-radius: 4px;
      }
    }
  }
}
</style>
