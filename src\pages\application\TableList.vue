<template>
  <div class="page-list-single">
    <Table ref="table" v-bind="tableParams || {}" class="new-view-table">
      <template slot="subscribe_status" slot-scope="{ record }">
        <a-tag :color="record.subscribe_status ? 'green' : 'grey'">{{
          record.subscribe_status ? '已关注' : '未关注'
        }}</a-tag>
      </template>
      <template slot="group" slot-scope="{ text }">
        <LimitTags
          :tags="text.map((item) => ({ label: item }))"
          :limit="1"
          mode="numTag"
        ></LimitTags>
      </template>
      <template slot="data_source_list" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
          mode="numTag"
        ></LimitTags>
      </template>
      <span
        slot="action"
        slot-scope="{ record }"
        style="display: flex; justify-content: space-between"
      >
        <a-popconfirm
          v-if="!record.subscribe_status"
          title="确定关注此应用?"
          @confirm="() => removeItem(record, true)"
        >
          <a>关注</a>
        </a-popconfirm>
        <a-popconfirm
          v-else
          title="确定取消关注此应用?"
          @confirm="() => removeItem(record, false)"
        >
          <a>取消关注</a>
        </a-popconfirm>
      </span>
    </Table>
  </div>
</template>

<script>
import Table from '@/components/Table';
import LimitTags from '@/components/LimitTags';
import { attention, attentionCancel } from '@/api/attention';
import LimitLabel from '@/components/LimitLabel';
import config from './config';
export default {
  components: {
    Table,
    LimitLabel,
    LimitTags
  },
  props: {
    isFollow: Boolean
  },
  data() {
    this.config = config(this);
    const { isFollow } = this;
    return {
      tableParams: {
        url: isFollow
          ? '/sqlreview/subscribe/sub-op/'
          : '/sqlreview/subscribe/sub-op-user/',
        method: 'post',
        reqParams: {},
        columns: this.config.columns(isFollow),
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        rowSelection: isFollow ? {} : false,
        searchFields: this.config.searchFields(isFollow),
        needTools: true,
        needSearchArea: true,
        scroll: { x: 'max-content' }
      },
      searchParams: {
        fields: this.config.searchFields(isFollow),
        multiCols: 4
      },
      title: ''
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {
    // this.$set(
    //   this.tableParams,
    //   'columns',
    //   this.config.columns().filter(item => item.visible != true)
    // );
  },
  methods: {
    removeItem(record, bool) {
      const { table } = this.$refs;
      this.$showLoading();
      if (bool) {
        attention({
          projects: record.id
        })
          .then(res => {
            this.$hideLoading();
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              table.refreshKeep();
              table.setSelectedInfo([]);
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else {
        attentionCancel({
          ids: record.id
        })
          .then(res => {
            this.$hideLoading();
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              table.refreshKeep();
              table.setSelectedInfo([]);
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      }
    },
    // 查询
    search(data, params = {}) {
      const { table } = this.$refs;
      const { keep, type } = params;
      this.searchData = data || {};
      if (keep) {
        table.refreshKeep(type, data);
      } else {
        table.refresh(null, data);
      }
      table.setSelectedInfo([]);
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      this.searchData = {};
      table.refresh();
      table.setSelectedInfo([]);
    },
    // 批量删除
    attention(bool) {
      const { table } = this.$refs;
      if (table.selectedRows.length == 0) {
        this.$message.warning(`请选择所要${bool ? '' : '取消'}关注的应用`);
        return;
      }
      const ids = table.selectedRows.length
        ? table.selectedRows.map(item => {
            return item.id;
          })
        : [];
      const params = {
        id: ids.join(',')
      };
      this.$showLoading();
      this.removeItem(params, bool);
    }
  }
};
</script>

<style scoped lang="scss">
</style>
