<!--
 * @Descripttion: 基于配置的表单
 * @Version: 1.0.0
 * @Author: wangjian366
 * @Date: 2020-12-03 18:42:22
-->
<template>
  <div>
    <a-modal
      v-if="mode !== 'drawer'"
      :title="title"
      :width="mwidth"
      :visible="modalVisible"
      :okText="okText"
      :cancelText ="cancelText"
      @ok="onSave"
      @cancel="onClose"
      :cancelButtonProps="{click:()=> {alert(3)}}"
      wrapClassName="former-modal"
    >
    <Former
          ref="modal"
          :fields="fields"
          :column="column"
          :layout="layout"
          :defaultValue="value"
        ></Former>
    </a-modal>

    <a-drawer
      v-if="mode == 'drawer'"
      :title="title"
      :width="mwidth"
      :visible="drawerVisible"
      placement="right"
      wrapClassName="former-drawer"
      @close="onClose"
    >
      <div class="former-drawer-content">
        <Former
          ref="drawer"
          :fields="fields"
          :column="column"
          :layout="layout"
          :defaultValue="value"
        ></Former>
      </div>
      <div class="former-drawer-footer">
        <a-button :style="{ marginRight: '8px' }" @click="onClose">
          取消
        </a-button>
        <a-button type="primary" @click="onSave">
          {{ okText }}
        </a-button>
      </div>
    </a-drawer>
  </div>
</template>

<script>
import Former from './former.vue'

export default {
  name: 'Formers',
  components: {Former},
  props: {
    mode: {
      type: String,
      required: false,
      default: () => 'drawer'
    },
    title: {
      type: String,
      required: true,
      default: () => '编辑数据'
    },
    fields: {
      type: Array,
      required: true,
      default: () => []
    },
    visible: {
      type: Boolean,
      required: true,
      default: () => false
    },
    defaultValue: {
      type: Object,
      required: false
    },
    width: {
      type: Number,
      required: false
    },
    okText: {
      type: String,
      required: false,
      default: () => '保存'
    },
    cancelText: {
      type: String,
      required: false,
      default: () => '取消'
    },
    cancelValidation: {
      type: Boolean,
      required: false,
      default: () => false
    },
    saveCallback: {
      type: Function,
      required: false
    },
    cancelCallback: {
      type: Function,
      required: false
    },
    layout: {
      type: String,
      required: false,
      default: () => 'horizontal'
    },
    column: {// 表单列数
      type: Number,
      required: false,
      default: () => 1
    }
  },
  data (vm) {
    return {
      // okText: vm.defaultValue ? '保存' : '新增',
      drawerVisible: false,
      modalVisible: false,
      value: vm.defaultValue,
      mwidth: (() => {
        if (vm.width) {
          return vm.width
        }
        switch (vm.column) {
          case 1:
            return 400
          case 2:
            return 560
          case 3:
            return 680
          default:
            return 750
        }
      })()
    }
  },
  watch: {
    defaultValue: function (value) {
      this.value = Object.assign({}, value || {})
    },
    visible: function (value) {
      this.resetVisible(value)
      let formerRef = this.getFormerRef()
      formerRef && formerRef.resetFields()
    }
  },
  methods: {
    resetVisible (visible) {
      if (this.mode === 'drawer') {
        this.drawerVisible = visible
        this.modalVisible = false
      } else {
        this.drawerVisible = false
        this.modalVisible = visible
      }
    },
    onClose (e) {
      if (e.target.nodeName === 'BUTTON' && this.cancelCallback) {
        let formerRef = this.getFormerRef()
        if (formerRef) {
          formerRef.validation((value) => {
            this.cancelCallback(value, this)
          })
        }
      } else {
        this.resetVisible(false)
        this.$emit('close')
      }
    },
    getFormerRef () {
      return this.mode === 'drawer' ? this.$refs.drawer : this.$refs.modal
    },
    onSave () {
      let formerRef = this.getFormerRef()
      if (formerRef) {
        formerRef.validation((value) => {
          if (this.saveCallback) {
            this.saveCallback(value, () => {
              this.$emit('close')
              this.resetVisible(false)
            })
          } else {
            this.$emit('submit', value)
            this.$emit('close')
            this.resetVisible(false)
          }
        })
      }
    }
  }
}
</script>

<style>
.former-drawer .ant-drawer-wrapper-body {
    box-sizing: border-box;
    padding-top: 55px;
    padding-bottom: 40px;
}
.former-drawer .former-drawer-content {
  overflow: auto;
  height: 100%;
}
.former-drawer .ant-drawer-header {
  position: absolute;
  top: 0;
  height: 55px;
  left: 0;
  width: 100%;
  z-index: 1;
}
.former-drawer .former-drawer-footer {
  bottom: 0;
  left: 0;
  position: absolute;
  z-index: 1;
  width: 100%;
  text-align: right;
  padding: 16px;
  background: #fff;
}
.former-modal .ant-modal-body {
  max-height: 400px;
  overflow: auto;
}
</style>
