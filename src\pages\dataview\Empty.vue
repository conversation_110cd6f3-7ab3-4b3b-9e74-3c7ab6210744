<template>
  <a-empty :description="description" :image="emptyImage"></a-empty>
</template>

<script>
import Chart from '@/components/Chart';
export default {
  components: { Chart },
  props: {
    description: {
      type: String | Object,
      default: '暂无数据'
    },
    image: {
      type: String | Object,
      default: 'default'
    }
  },
   data() {
    return {
      useUnifiedEmpty: GLOBAL_CONFIG.useUnifiedEmpty
    };
  },
  computed: {
    emptyImage() {
      return this.useUnifiedEmpty
        ? require('@/assets/img/private/table-empty.svg')
        : _.isString(this.image)
        ? Map[this.image]
        : this.image;
    }
  }
};
</script>

<style lang="less" scoped>
</style>