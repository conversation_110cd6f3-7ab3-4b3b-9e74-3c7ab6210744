<template>
  <div class="home-review-wraper">
    <a-spin tip="加载中" :spinning="false">
      <!-- SQLID -->
      <div class="review-header">
        <h4>SQL详情</h4>
        <div>
          <a-button :disabled="dataInfo.index === 1" @click="onPrev">
            <a-icon type="left" />上一条
          </a-button>
          <a-button :disabled="dataInfo.count === dataInfo.index" @click="onNext">
            下一条
            <a-icon type="right" />
          </a-button>
          <span class="pageInfo">{{ dataInfo.index }}/{{ dataInfo.count }}</span>
        </div>
      </div>
      <!-- AI判定结果 -->
      <a-card type="small" :bordered="false">
        <div class="title">
          <custom-icon type="profile" />
          <span style="margin-left: 4px;">AI判定结果</span>
          <a-tag
            :color="
              dataInfo.status === 1 || dataInfo.status === 2
                ? '#52c41a'
                : dataInfo.status === -1 || dataInfo.status === 9
                ? '#ff4d4f'
                : '#b0aeae'
            "
          >{{ dataInfo.status | aiStatus }}</a-tag>
        </div>
        <div v-if="dataInfo.ai_comment" class="ai-comment-part">
          <div v-for="(item, index) in dataInfo.ai_comment" :key="index" class="ai-comment-div">
            <!-- <span
              :class="{
                level0: item.level === 0,
                level1: item.level === 1,
                level2: item.level === 2,
                level3: item.level === 3,
                level9: item.level === 9
              }"
            >【{{ item.level | levelStatus }}】</span>
            <span>{{ item.ai_comment }}</span>-->
            <span>
              <custom-icon
                class="rule-icon circle"
                type="warning"
                v-if="item.rule_result == 1"
              />
              <custom-icon class="rule-icon bell" type="lu-icon-ring" v-if="item.rule_result == 0" />
            </span>
            <span>{{ item.ai_comment }}</span>
            <span class="rule-num">
              <a-popover
                placement="top"
                v-if="item.suggest"
                overlayClassName="sql-detail-suggest-part"
              >
                <template slot="content">
                  <div class="suggest-title">优化建议</div>
                  <MarkdownViewer class="rich-editor-preview" v-model="item.suggest"></MarkdownViewer>
                </template>
                <custom-icon type="bulb" theme="filled" style="margin-left: 4px" />
              </a-popover>
            </span>
          </div>
        </div>
        <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>
      </a-card>
      <!-- 优化建议 -->
      <a-card v-if="sqlPlanInfo.length > 0" type="small" :bordered="false">
        <div class="title">
          <a-icon type="like" />
          <span style="margin-left: 4px;">优化建议</span>
        </div>
        <div class="ai-comment-part" v-for="(item, index) in sqlPlanInfo" :key="index">
          <div style="color: #0f78fb;">{{ item.message }}</div>
          <span style="color: #0f78fb;">{{ item.sql }}</span>
        </div>
      </a-card>
      <!-- SQL执行指标 -->
      <a-card v-if="false" type="small" :bordered="false">
        <div class="title">
          <a-icon type="exception" />
          <span style="margin-left: 4px;">SQL执行指标</span>
          <span style="margin-left: 10px; font-size: 14px; font-weight: normal;">
            ( 执行时间范围：{{
            dataInfo.system_info.time_range || '--'
            }}
            )
          </span>
        </div>
        <div v-if="dataInfo.system_info">
          <div class="common-content system-info">
            <div>
              <span>执行次数：</span>
              {{ dataInfo.system_info.executions_total || '--' }}/次
            </div>
            <div>
              <span>执行用户：</span>
              {{ dataInfo.system_info.user || '--' }}
            </div>
            <div>
              <span>平均执行时间：</span>
              {{ dataInfo.system_info.avg_time_ms || '--' }}/ms
            </div>
            <div>
              <span>平均逻辑读：</span>
              {{ dataInfo.system_info.avg_buffer_gets || '--' }}/次
            </div>
            <div>
              <span>平均物理读：</span>
              {{ dataInfo.system_info.avg_physical_read_requests || '--' }}/次
            </div>
            <div>
              <span>平均消耗排序：</span>
              {{ dataInfo.system_info.avg_sort_cost || '--' }}/次
            </div>
            <div>
              <span>平均IO等待时间：</span>
              {{ dataInfo.system_info.avg_io_wait || '--' }}/ms
            </div>
            <div>
              <span>平均集群等待时间：</span>
              {{ dataInfo.system_info.avg_cluster_wait || '--' }}/ms
            </div>
          </div>
        </div>
        <div v-else>
          <custom-empty />
        </div>
      </a-card>
      <!-- SQL文本 -->
      <a-card type="small" :bordered="false">
        <div class="title">
          <a-icon type="edit" />
          <span style="margin-left: 4px;">SQL文本</span>
        </div>
        <sql-highlight v-if="dataInfo.sql_text" :sql="dataInfo.sql_text"></sql-highlight>
        <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>
      </a-card>
      <!-- 执行计划 -->
      <a-card type="small" :bordered="false">
        <div class="title">
          <a-icon type="flag" />
          <span style="margin-left: 4px;">执行计划</span>
        </div>
        <div v-if="dataInfo.sql_plan">
          <pre v-for="(item, index) in dataInfo.sql_plan" :key="index">{{ item }}</pre>
        </div>
        <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>
      </a-card>
      <!-- SQLMap历史代码 -->
      <a-card type="small" :bordered="false" v-if="dataInfo.sql_map">
        <div class="title">
          <a-icon type="exception" />
          <span style="margin-left: 4px;">SQLMap</span>
        </div>
        <Prettier
          v-if="dataInfo.sql_map"
          class="code-content"
          :value="dataInfo.sql_map"
          :type="dataInfo.sql_map.slice(0,1) === '<' ? 'xml': 'sql'"
        />
        <!-- <sql-highlight v-if="dataInfo.sql_map" :sql="dataInfo.sql_map"></sql-highlight> -->
        <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>
      </a-card>
      <!-- 动态拼接信息 -->

      <SqlException ref="SqlException" :sql_list="dataInfo.dynamic_list"></SqlException>
    </a-spin>
  </div>
</template>
<script>
import SqlHighlight from '@/components/SqlHighlight';
import SqlException from './sqlException';
import Prettier from '@/components/Prettier';
import MarkdownViewer from '@/components/Markdown/viewer';
export default {
  components: {
    SqlHighlight,
    SqlException,
    Prettier,
    MarkdownViewer
  },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    },
    sqlPlanInfo: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  watch: {},
  destroyed() {},
  computed: {},
  methods: {
    onPrev() {
      this.$emit('onPrev');
    },
    onNext() {
      this.$emit('onNext');
    }
  },
  filters: {
    levelStatus(value) {
      let obj = {
        0: '未知',
        1: '高风险',
        2: '中风险',
        3: '低风险',
        9: '无风险'
      };
      return obj[value];
    },
    aiStatus(value) {
      let obj = {
        0: '未知',
        1: '通过',
        2: '白名单通过',
        9: '错误',
        '-1': '不通过'
      };
      return obj[value];
    }
  }
};
</script>
<style lang="less" scoped>
.home-review-wraper {
  color: rgba(86, 87, 89, 1);
  /deep/ .anticon-robot,
  .anticon-edit,
  .anticon-flag,
  .anticon-like,
  .anticon-exception {
    color: #1890ff;
  }
  .ant-spin-container {
    background: #fff;
    padding: 0 24px;
    .review-header {
      display: flex;
      align-items: center;
      padding: 0;
      > h4 {
        font-family: PingFangSC-Semibold;
        font-size: 20px;
        color: #27272a;
        font-weight: 600;
        margin-right: 32px;
        margin-bottom: 0;
      }
      > div {
        display: flex;
        align-items: center;
        /deep/.ant-btn {
          padding: 0;
          width: 84px;
          height: 30px;
          background: #ffffff;
          border: 1px solid rgba(228, 228, 231, 1);
          border-radius: 6px 0px 0px 6px;
          .anticon {
            // color: #71717a;
          }
          span {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            // color: #71717a;
            font-weight: 400;
          }
          .anticon + span,
          span + .anticon {
            margin-left: 0;
          }
        }
        .ant-btn:nth-child(2) {
          border-radius: 0px 6px 6px 0px;
          border-left: none;
        }
        .ant-btn[disabled] {
          border: 1px solid rgba(228, 228, 231, 0.6) !important;
        }
      }
      .pageInfo {
        margin-left: 16px;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #a1a1aa;
        text-align: right;
        font-weight: 400;
      }
    }
    .ant-card {
      .ant-card-body {
        .code-content {
          border-radius: 12px;
        }
        .title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 8px;
          .anticon {
            font-size: 16px;
            color: #333333;
          }
          > span {
            font-family: PingFangSC-Semibold;
            font-size: 14px;
            font-weight: 600;
          }
          .ant-tag-has-color {
            font-weight: 400;
          }
        }
      }
    }
  }
  /deep/ .ant-tag {
    margin-left: 8px;
  }
  /deep/ .ant-card-head-title {
    padding-top: 0;
    padding-bottom: 8px;
  }
  /deep/ .ant-card-head {
    padding: 0;
    min-height: 32px;
  }
  /deep/ .ant-card-body {
    padding: 32px 0 24px 0;
    padding-bottom: 0;
    border-radius: 12px;
    .sql-format {
      margin-bottom: 0;
    }
  }
  pre {
    border-radius: 2px;
    padding: 16px;
    // background-color: rgba(125, 125, 125, 0.1);
    background-color: rgb(240, 246, 254);
    color: #0f78fb;
    margin-bottom: 0;
  }
  .common-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    div {
      width: 25%;
      &:nth-child(n + 5) {
        margin-top: 10px;
      }
    }

    &.system-info {
      > div {
        color: rgba(0, 0, 0, 0.6);
        > span {
          color: #000000;
        }
      }
    }
  }
  @media screen and (max-width: 1500px) {
    .common-content > div {
      width: 25%;
    }
  }
  .sql-plan {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.ai-comment-part {
  padding: 16px;
  // background-color: #edf5ff;
  background-color: rgb(237, 243, 254);
  border-radius: 5px;
  > span {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #71717a;
    font-weight: 400;
  }
  .ai-comment-div {
    padding: 0;
    margin: 2px 0;
    background-color: transparent;
    color: #0f78fb;
    line-height: 25px;
    .level0 {
      color: #b0aeae;
    }
    .level1 {
      color: #ff4d4f;
    }
    .level2 {
      color: #ff9358;
    }
    .level3 {
      color: #1edfa9;
    }
    .level9 {
      color: #52c41a;
    }
    .rule-icon {
      &.circle {
        color: #e71d36;
      }
      &.bell {
        color: #f29339;
      }
    }
    .rule-num {
      text-align: right;
      font-size: 14px;
      white-space: nowrap;
    }
  }
}
</style>
<style lang="less">
.ant-popover {
  &.sql-detail-suggest-part {
    .ant-popover-content {
      .ant-popover-inner {
        .ant-popover-inner-content {
          max-width: 300px;
          max-height: 200px;
          font-size: 12px;
          font-weight: 400;
          .suggest-title {
            color: #27272a;
            height: 24px;
            line-height: 24px;
          }
          .suggest-des {
            color: #71717a;
            height: 24px;
            line-height: 24px;
          }
        }
      }
    }
  }
}
</style>