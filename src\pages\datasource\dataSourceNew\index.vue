<template>
  <CustomIframe :url="url" />
</template>

<script>
import CustomIframe from '@/components/Iframe';
export default {
  components: { CustomIframe },
  props: {},
  data() {
    return {};
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  computed: {
    project() {
      return this.$store.state.project;
    },
    account() {
      return this.$store.state.account;
    },
    url() {
      const { dsmUrl } = this.project;
      const { user } = this.account;
      return `${dsmUrl}&umNo=${user.name}&platform=SQLReview`;
    }
  },
  methods: {}
};
</script>

<style lang="less" scoped></style>
