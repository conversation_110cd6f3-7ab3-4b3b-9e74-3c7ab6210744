<template>
  <div class="page-dbacat-detail-item">
    <Table v-bind="params" />
  </div>
</template>

<script>
import Table from '@/components/Table';

export default {
  components: { Table },
  data() {
    return {
      params: {
        columns: [
          {
            title: '12:27:30 (2)',
            children: [
              {
                title: '实例名',
                dataIndex: 'instance_name',
                width: 80
              },
              {
                title: '告警',
                dataIndex: 'warn_num',
                width: 50
              },
              {
                title: '健康值',
                dataIndex: 'health',
                width: 60
              },
              {
                title: '配置',
                dataIndex: 'config',
                width: 50
              }
            ]
          }
        ],
        ellipsis: true,
        bordered: true,
        // size: "small"
      }
    };
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.page-dbacat-detail-item {
  /deep/ .custom-table {
    .ant-empty-image {
      height: 50px;
    }
  }
}
@media (max-width: 1980px) {
  .page-dbacat-detail-item {
    width: 16.66666%;
  }
}
@media (min-width: 1980px) and (max-width: 2400px) {
  .page-dbacat-detail-item {
    width: 12.5%;
  }
}
@media (min-width: 2400px) {
  .page-dbacat-detail-item {
    width: 10%;
  }
}
</style>
