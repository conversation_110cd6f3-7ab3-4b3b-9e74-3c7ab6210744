export default function (ctx) {
  const columns = (value) => {
    return [
      {
        title: 'UUID',
        dataIndex: 'job_uuid',
        key: 'job_uuid',
        scopedSlots: { customRender: 'job_uuid' }
      },
      {
        title: '任务状态',
        dataIndex: 'status',
        key: 'status',
        scopedSlots: { customRender: 'status' }
      },
      {
        title: '期望时间',
        dataIndex: 'expect_time',
        key: 'expect_time',
        visible: value !== 'OpenAPI',
        scopedSlots: { customRender: 'expect_time' }
      },
      {
        title: '开始时间',
        dataIndex: 'start_time',
        key: 'start_time',
        scopedSlots: { customRender: 'start_time' }
      },
      {
        title: '结束时间',
        dataIndex: 'end_time',
        key: 'end_time',
        scopedSlots: { customRender: 'end_time' }
      },
      {
        title: '采集时间范围',
        dataIndex: 'time_range',
        key: 'time_range',
        visible: value == 'OpenAPI',
        scopedSlots: { customRender: 'time_range' }
      },
      {
        title: '执行耗时',
        dataIndex: 'execution_time',
        key: 'execution_time'
      },
      {
        title: '延迟',
        key: 'delay',
        dataIndex: 'delay',
        visible: value !== 'OpenAPI'
      },
      {
        title: '信息',
        dataIndex: 'msg',
        key: 'msg',
        scopedSlots: { customRender: 'msg' }
      }
    ];
  };

  const searchFields = (type) => {
    return [
      {
        type: 'Select',
        key: 'status',
        label: '任务状态',
        mainSearch: true,
        props: {
          placeholder: '任务状态',
          url:
            type == 'job_jdbc_detail_choose'
              ? '/sqlreview/api/v1/schedules/job/status'
              : '/sqlreview/after_audit/get_sql_collect_enum',
          reqParams:
            type == 'job_jdbc_detail_choose'
              ? { is_sub: 1 }
              : {
                enum_name: type
              },
          getPopupContainer: (el) => {
            return document.body;
          }
        }
      }
    ];
  };

  const agentColumns = [
    {
      title: '监控的java进程',
      dataIndex: 'app_name',
      key: 'app_name',
      scopedSlots: { customRender: 'app_name' }
    },
    {
      title: '关联项目',
      dataIndex: 'project_name',
      key: 'project_name',
      scopedSlots: { customRender: 'project_name' }
    },
    {
      title: 'agent worker 状态',
      key: 'status',
      dataIndex: 'status',
      scopedSlots: { customRender: 'status' }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true
    },
    {
      title: '最后采集时间',
      dataIndex: 'last_collect_time',
      key: 'last_collect_time',
      sorter: true
    }
  ];
  const agentSearchFields = [
    {
      type: 'Input',
      key: 'app_name',
      compIcon: 'highlight',
      props: {
        placeholder: 'java进程名称'
      }
    }
  ];

  const javaColumns = [
    {
      title: 'java进程',
      dataIndex: 'app_name',
      key: 'app_name'
    },
    {
      title: 'PID',
      dataIndex: 'jvm_pid',
      key: 'jvm_pid'
    },
    {
      title: '进程启动时间',
      key: 'app_start_time',
      dataIndex: 'app_start_time',
      sorter: true
    },
    {
      title: '是否被agent监控',
      dataIndex: 'monitored',
      key: 'monitored',
      customRender: (text) => {
        return text == 1 ? '是' : '否'
      }
    }
  ];

  const javaSearchFields = [
    {
      type: 'Input',
      key: 'jvm_name',
      compIcon: 'highlight',
      props: {
        placeholder: 'java进程名称'
      }
    }
  ];

  return {
    columns,
    searchFields,
    agentColumns,
    agentSearchFields,
    javaColumns,
    javaSearchFields
  };
}
