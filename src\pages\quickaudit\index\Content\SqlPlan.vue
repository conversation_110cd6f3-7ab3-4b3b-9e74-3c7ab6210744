<template>
  <div class="quick-audit-sql-plan-comp">
    <a-tabs :active-key="activeKey" @change="change" class="sql-plan-tabs">
      <a-tab-pane key="sqlplan" tab="SQL执行计划">
        <pre class="sql-plan-pre" v-if="sqlPlan">{{ sqlPlan }}</pre>
        <custom-empty v-else></custom-empty>
      </a-tab-pane>
      <a-tab-pane key="table" tab="表信息">
        <Table
          v-bind="tableParams"
          :dataSource="tableData"
          bordered
          class="new-view-table small-size"
        >
          <template slot="is_onlie" slot-scope="{ record }">
            <span>{{ record.is_onlie }}</span>
            <a-popover v-if="record.is_onlie === '未知'">
              <template slot="content">
                <span>没有可用的生产数据库链接，无法获取到表上线状态。</span>
              </template>
              <a-icon type="question-circle" />
            </a-popover>
            <!-- :color="statusColor[record.dba_status]" -->
            <a-tooltip placement="bottom">
              <template slot="title"
                >生效时间:{{ record.take_effect }} 失效时间:{{
                  record.expire
                }}</template
              >
              <a-tag v-if="record.is_white === 1" color="rgba(35, 190, 108, 1)"
                >已加入白名单</a-tag
              >
            </a-tooltip>
          </template>
          <template slot="comment" slot-scope="{ text }">
            <LimitLabel :label="text || ''" :limit="20"></LimitLabel>
          </template>
        </Table>
      </a-tab-pane>
      <a-tab-pane key="index" tab="索引信息">
        <!-- <a-radio-group
          v-model="value"
          @change="onChange"
          class="table-name-group"
        >
          <div v-for="item in tableList" :key="item" class="table-name">
            <a-radio-button :value="item">
              <custom-icon type="table" /> {{ item }}
            </a-radio-button>
          </div>
        </a-radio-group> -->
        <a-select
          v-model="value"
          style="width: 360px; padding: 12px"
          @change="onChange"
        >
          <template v-for="item in tableList">
            <a-select-option :value="item" :key="item">
              <custom-icon type="table" /> {{ item }}
            </a-select-option>
          </template>
        </a-select>

        <Table
          v-bind="indexParams"
          :dataSource="indexData"
          bordered
          class="new-view-table small-size"
        ></Table>
      </a-tab-pane>
      <a-tab-pane key="field" tab="字段信息">
        <!-- <a-radio-group
          v-model="value"
          @change="onChange"
          class="table-name-group"
        >
          <a-radio-button
            class="table-name"
            v-for="item in tableList"
            :key="item"
            :value="item"
          >
            <custom-icon type="table" /> {{ item }}
          </a-radio-button>
        </a-radio-group> -->
        <a-select
          v-model="value"
          style="width: 360px; padding: 12px"
          @change="onChange"
        >
          <template v-for="item in tableList">
            <a-select-option :value="item" :key="item">
              <custom-icon type="table" /> {{ item }}
            </a-select-option>
          </template>
        </a-select>
        <Table
          v-bind="fieldParams"
          :dataSource="fieldData"
          bordered
          class="new-view-table small-size"
        ></Table>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import config from './config';
import { getHistoryColumns } from '@/api/quick';
export default {
  components: { Table, LimitLabel, Tag, DbImg },
  props: {
    isFinished: Boolean,
    resultData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    this.config = config(this);
    return {
      activeKey: 'sqlplan',
      tableData: [],
      tableParams: {
        url: '/sqlreview/quick_audit/quick_audit_table_info',
        reqParams: {},
        columns: this.config.tableColumns,
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      indexData: [],
      indexParams: {
        columns: this.config.indexColumns(),
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      fieldData: [],
      fieldParams: {
        columns: this.config.fieldColumns,
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      sqlPlan: '',
      tableList: [],
      value: '',
      detailId: null,
      quickAuditId: null
    };
  },
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    // sql xml 切换
    change(activeKey) {
      this.activeKey = activeKey;
    },
    // 表名切换
    onChange(value) {
      this.value = value;
      const params = {
        detail_id: this.detailId,
        quick_audit_id: this.quickAuditId,
        table_name: this.value
      };
      this.getColumnsInfo(params);
    },
    getColumnsInfo(params) {
      getHistoryColumns(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            this.indexData = resData.index_list;
            this.fieldData = resData.column_info;
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  },
  watch: {
    resultData: {
      handler(newVal) {
        if (!_.isEmpty(newVal)) {
          this.tableList = newVal.table_list;
          this.sqlPlan = newVal.sql_plan;
          this.value = !_.isEmpty(this.tableList) > 0 && this.tableList[0];
          this.detailId = newVal.detail_id;
          this.quickAuditId = newVal.quick_audit_id;
          this.getColumnsInfo({
            detail_id: newVal.detail_id,
            quick_audit_id: newVal.quick_audit_id,
            table_name: this.value
          });
          this.$set(this.tableParams, 'reqParams', {
            detail_id: newVal.detail_id,
            quick_audit_id: newVal.quick_audit_id
          });
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.quick-audit-sql-plan-comp {
  height: 100%;
  /deep/.sql-plan-tabs {
    height: 100%;
    &.ant-tabs {
      .ant-tabs-bar {
        margin: 0;
        .ant-tabs-nav-container {
          .ant-tabs-nav {
            > div {
              .ant-tabs-tab:not(.ant-tabs-tab-active) {
                height: 49px;
                line-height: 49px;
                font-family: PingFangSC-Regular;
                color: #1f1f1f;
                font-weight: 600;
                background: transparent;
                // &.ant-tabs-tab-active {
                //   color: #165dff;
                //   font-weight: 600;
                // }
              }
            }
          }
        }
      }
      .ant-tabs-content {
        height: 85%;
        .ant-tabs-tabpane-active {
          overflow-y: auto;
          .sql-plan-pre {
            margin: 0;
            padding: 16px 32px;
          }
          // .table-name-group {
          //   padding: 12px 16px;
          //   display: flex;
          //   .table-name {
          //     margin-right: 16px;
          //   }
          // }
          .new-view-table {
            .ant-table {
              .ant-table-content {
                .ant-table-body {
                  .ant-table-fixed {
                    border: none;
                    .ant-table-thead {
                      > tr {
                        > th {
                          padding: 0 !important;
                          > span {
                            padding: 16px 24px;
                            background: #fafafa;
                            font-family: PingFangSC-Regular;
                            font-size: 14px;
                            color: #1f1f1f;
                            font-weight: 400;
                            width: 100%;
                          }
                        }
                      }
                    }
                    .ant-table-tbody {
                      > tr {
                        > td {
                          padding: 16px 24px !important;
                          border: none;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        .custom-empty {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
</style>
