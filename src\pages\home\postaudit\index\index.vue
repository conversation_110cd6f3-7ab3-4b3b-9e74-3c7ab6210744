<template>
  <div>
    <!-- 顶部按钮 -->
    <!-- <div class="frame-button-wrapper" v-if="activeKey !== 'DB2'"> -->
    <div class="frame-button-wrapper">
      <a-button
        slot="topBtns"
        class="highlight"
        icon="plus"
        v-if="activeKey!== 'DB2' && !isDev"
        @click="addProject"
      >新建审核对象</a-button>
    </div>
    <Tabs
      v-model="activeKey"
      :tabsList="tabsList"
      :mode="'tag'"
      :needDatabaseIcon="true"
      @change="onChange"
      :animated="false"
    >
      <div slot="ORACLE" slot-scope="{ item }">
        <PageList :ref="item.key" :dbType="item.dbType" />
      </div>
      <div slot="MYSQL" slot-scope="{ item }">
        <PageList :ref="item.key" :dbType="item.dbType" />
      </div>
      <div slot="POSTGRE" slot-scope="{ item }">
        <PageList :ref="item.key" :dbType="item.dbType" />
      </div>
      <div slot="DB2" slot-scope="{ item }">
        <!-- 原来的实时审核挪到了事后审核里面来，成了DB2的页面 -->
        <Immediate :ref="item.key" :dbType="item.dbType" />
      </div>
      <div slot="SQLSERVER" slot-scope="{ item }">
        <SqlServer :ref="item.key" :dbType="item.dbType" />
      </div>
    </Tabs>
  </div>
</template>

<script>
import Tabs from '@/components/Tabs';
import PageList from './PageList';
import SqlServer from '../sqlServer/index/index';
import Immediate from '../immediate/index';
export default {
  components: { Tabs, PageList, Immediate, SqlServer },
  props: {},
  data() {
    const dbType = _.get(
      this.$store.state,
      'common.pageCache.homePostaudit.dbType'
    );
    return {
      activeKey: dbType || 'ORACLE',
      tabsList: [
        {
          tab: 'ORACLE',
          key: 'ORACLE',
          dbType: 'ORACLE'
        },
        {
          tab: 'MYSQL',
          key: 'MYSQL',
          dbType: 'MYSQL'
        },
        {
          tab: 'POSTGRE',
          key: 'POSTGRE',
          dbType: 'POSTGRE'
        },
        {
          tab: 'DB2',
          key: 'DB2',
          dbType: 'DB2'
        },
        {
          tab: 'SQLSERVER',
          key: 'SQLSERVER',
          dbType: 'SQLSERVER'
        }
      ]
    };
  },
  computed: {
    isDev() {
      const user = this.$store.state.account.user || {};
      return user.role === 'developer';
    }
  },
  created() {},
  mounted() {},
  methods: {
    onChange(e) {
      console.log(e, this.activeKey);
    },
    addProject() {
      // const addModal = this.$refs[this.activeKey === 'DB2' ? 'ORACLE' : this.activeKey];
      const addModal = this.$refs[this.activeKey];
      addModal.addProject();
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
/deep/
  .custom-tabs.tag
  > .ant-tabs-bar
  > .ant-tabs-nav-container
  > .ant-tabs-nav-wrap
  > .ant-tabs-nav-scroll
  > .ant-tabs-nav
  > div
  > .ant-tabs-tab {
  font-size: 24px;

  .database-image .iconClass {
    display: flex;
    align-items: center;
    .iconStyle {
      padding-top: 0px;
    }
    .limit-label pre {
      font-size: 18px;
      display: flex;
      align-items: center;
      margin-bottom: 0rem;
    }
  }
}
</style>
