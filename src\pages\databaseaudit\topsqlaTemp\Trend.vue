<template>
  <!-- 趋势 -->
  <div class="trend_container">
    <div class="header" style="text-align: right; margin-bottom: 16px">
      <a-range-picker
        @ok="time_ok"
        style="width: 360px"
        :showTime="{
          hideDisabledOptions: true,
          defaultValue: [
            moment('00:00:00', 'HH:mm:ss'),
            moment('23:59:59', 'HH:mm:ss')
          ]
        }"
        v-model="do_time"
        :format="['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']"
      />
    </div>
    <el-descriptions title="基础信息">
      <el-descriptions-item label="ID">{{
        base_data.sql_id
      }}</el-descriptions-item>
      <el-descriptions-item label="实例名称">
        <div style="display: flex; align-items: center">
          <custom-icon :type="base_data.iconType[base_data.db_type]" />
          <span> {{ base_data.title }}</span>
        </div>
      </el-descriptions-item>
      <el-descriptions-item :label="base_data.db_type | db_type_filter">{{
        base_data.executive_schema
      }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="1">
      <el-descriptions-item :span="24" label="SQL文本"
        ><span
          style="
            max-width: 800px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: blue;
            cursor: pointer;
          "
          @click="sql_text_btn"
        >
          {{ base_data.sql_text }}</span
        ></el-descriptions-item
      >
    </el-descriptions>

    <div v-show="sql_text_visable">
      <pre
        ref="container"
        style="
          width: 100%;
          white-space: pre-line;
          padding: 16px 24px;
          margin-bottom: 16px;
          background-color: antiquewhite;
        "
        v-html="sql_text_cpt"
      ></pre>
    </div>
    <el-descriptions title="趋势指标"> </el-descriptions>
    <div
      v-loading="loading_chart"
      style="width: 100%; height: 300px"
      id="child_1"
      class="child_1"
    ></div>
    <div
      v-loading="loading_chart"
      style="width: 100%; height: 300px; margin: 46px 0"
      id="child_2"
      class="child_2"
    ></div>
    <el-table
      @sort-change="handleSortChange"
      ref="table_"
      v-loading="loading_list"
      :header-cell-style="{
        background: '#fafafa',
        height: '40px',
        padding: '5px 0',
        color: '#000000'
      }"
      :data="table_data"
      style="width: 100%"
      :cell-style="{ padding: '5px 0', width: '200px', height: '40px' }"
    >
      <!-- for header -->
      <span v-for="(item, index) in table_headers_list" :key="index">
        <el-table-column
          sortable="custom"
          v-if="headers_keys.includes('sql_text') && item.key == 'sql_text'"
          show-overflow-tooltip
          :prop="item.key"
          :label="item.value"
          width="220px"
        >
          <template slot-scope="scope">
            <!-- -->

            <el-popover
              popper-class="device-popover"
              placement="top"
              width="400"
              trigger="hover"
            >
              <div>
                <pre
                  style="
                    white-space: pre-line;
                    max-width: 600px;
                    max-height: 300px;
                  "
                  v-html="$options.filters.sql_text_cpt(scope.row.sql_text)"
                ></pre>
                <div style="text-align: right">
                  <span
                    style="cursor: pointer"
                    @click="copySql(scope.row.sql_text)"
                    class="el-icon-document-copy"
                  >
                  </span>
                </div>
              </div>
              <span v-if="scope.row.sql_text.length > 20" slot="reference">
                {{ scope.row.sql_text.substring(0, 20) + '...' }}
              </span>
              <span v-else slot="reference"> {{ scope.row.sql_text }}</span>
            </el-popover>
            <span
              style="cursor: pointer"
              @click="copySql(scope.row.sql_text)"
              class="el-icon-document-copy"
            ></span>
          </template>
        </el-table-column>
        <el-table-column
          sortable="custom"
          v-else-if="
            headers_keys.includes('ai_status') && item.key == 'ai_status'
          "
          show-overflow-tooltip
          :prop="item.key"
          :label="item.value"
          width="220px"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ai_status | risk_filters }}</span>
          </template>
        </el-table-column>
        <el-table-column
          sortable="custom"
          v-else-if="
            headers_keys.includes('source_type') && item.key == 'source_type'
          "
          show-overflow-tooltip
          :prop="item.key"
          :label="item.value"
          width="220px"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.source_type | source_type_filter(source_type_list)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          sortable="custom"
          v-else-if="headers_keys.includes('sql_id') && item.key == 'sql_id'"
          show-overflow-tooltip
          :prop="item.key"
          :label="item.value"
          width="320px"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          v-else
          show-overflow-tooltip
          :prop="item.key"
          :label="item.value"
          width="220px"
        >
        </el-table-column>
      </span>
      <el-table-column width="1px"> </el-table-column>
    </el-table>
    <div class="block">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="search_form.page_number"
        :page-sizes="[10, 20, 30]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        :total="table_count"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import hljs from 'highlight.js';
import * as API from '@/api/databaseaudit/topsqltemp';
import {
  get_header_columns,
  get_datasource_list,
  select_source_type,
  get_executive_data,
  get_overview_chart,
  save_top_sql_label
} from '@/api/databaseaudit/topsqltemp';
export default {
  data() {
    return {
      do_time: '',
      loading_chart: false,
      table_data: [],
      table_count: 0,
      loading_list: false,
      sql_text_visable: false,
      search_form: {
        time: [],
        page_number: 1,
        page_size: 10
      },
      table_headers_list: [],
      table_headers_all_list: [],
      transfer_value: [],
      chart1: null,
      headers_keys: [],
      sql_text:
        'SELECT J.jobid FROM pgagent.pga_job J WHERE jobenabled AND jobagentid IS NULL AND jobnextrun <= now() AND(jobhostagent = ? OR jobhostagent = ?) ORDER BY jobnextrun'
    };
  },
  filters: {
    sql_text_cpt(e) {
      return hljs.highlight('sql', e).value;
    },
    risk_filters(e) {
      if (e == 0) {
        return '待审核';
      }

      if (e == -1) {
        return '高风险';
      }

      if (e == 1) {
        return '无风险';
      }

      if (e == 2) {
        return '低风险';
      }

      if (e == 9) {
        return '异常';
      }

      if (e == 3) {
        return '审核中';
      }

      return '';
    },
    db_type_filter(e) {
      if (e == 'MYSQL' || e == 'POSTGRE' || e == 'postgre' || e == 'TD_MYSQL') {
        return 'Database';
      }

      return 'Schema';
    },
    source_type_filter(e, arr) {
      let str = arr.filter((item) => {
        if (item.value == e) {
          return item;
        }
      });
      return str[0].label;
    }
  },
  props: ['search_time', 'base_data', 'source_type_list'],
  computed: {
    sql_text_cpt() {
      if (this.base_data.sql_text) {
        return hljs.highlight('sql', this.base_data.sql_text).value;
      }
    }
  },

  created() {
    this.search_form = { ...this.base_data };
    this.do_time = this.search_time;
  },
  mounted() {
    console.log(this.base_data, '++++++++++++');
    this.search_form = { ...this.base_data };
    let step = this.getStep({
      startTimestamp: moment(this.search_form.execution_time[0]).unix(),
      endTimestamp: moment(this.search_form.execution_time[1]).unix()
    });

    console.log(step);
    this.search_form.step = step;
    this.get_header_columns();
    this.get_tendency_metrics_chart(); // 执行概览 执行耗时
    this.get_detail_data_list(); // 数据明细
    const span = document.createElement('span');
    span.textContent = '';
    span.className = 'el-icon-document-copy my-span-class';
    span.style.cursor = 'pointer';
    span.style.marginLeft = '10px';
    span.addEventListener('click', this.copySql_);
    this.$refs.container.appendChild(span);
  },
  activated() {
    this.search_form = { ...this.base_data };
    this.get_header_columns();
    this.get_tendency_metrics_chart(); // 执行概览 执行耗时
    this.get_detail_data_list(); // 数据明细
  },
  beforeDestroy() {
    if (this.chart1) {
      this.chart1.dispose();
    }
  },
  methods: {
    getStep(timeStamp) {
      // 趋势图步长策略（性能趋势）：
      // (0~12小时]：数据点间隔1分钟（数据点个数：0 ~720）
      // (12~24小时]：数据点间隔2分钟（数据点个数：360 ~720）
      // (1~2天]：数据点间隔5分钟（数据点个数：288 ~576）
      // (2~7天]：数据点间隔10分钟（数据点个数：192 ~672）
      // (7~30天]：数据点间隔1小时（数据点个数：168 ~720）

      const { startTimestamp, endTimestamp } = timeStamp;

      const diff = (endTimestamp - startTimestamp) / (60 * 60);
      if (diff < 12) {
        return 60;
      }
      if (diff <= 24) {
        return 2 * 60;
      }
      if (diff <= 48) {
        return 5 * 60;
      }
      if (diff <= 7 * 24) {
        return 10 * 60;
      }
      return 60 * 60;
    },
    handleSortChange(e) {
      this.$set(this.search_form, '_sorter', '');
      if (e.order) {
        if (e.order == 'ascending') {
          this.search_form._sorter = JSON.stringify({
            [e.prop]: 'ascend'
          });
        }

        if (e.order == 'descending') {
          this.search_form._sorter = JSON.stringify({
            [e.prop]: 'descend'
          });
        }
      }
      this.get_detail_data_list();
    },
    copySql_(e) {
      e = this.base_data.sql_text;
      // 创建一个临时的 textarea 元素
      const textarea = document.createElement('textarea');
      textarea.value = e.trim();
      document.body.appendChild(textarea);

      // 执行复制操作
      textarea.select();
      document.execCommand('copy');

      // 移除临时元素
      document.body.removeChild(textarea);
      this.$message.success('复制成功');
    },
    copySql(e) {
      // 创建一个临时的 textarea 元素
      const textarea = document.createElement('textarea');
      textarea.value = e.trim();
      document.body.appendChild(textarea);

      // 执行复制操作
      textarea.select();
      document.execCommand('copy');

      // 移除临时元素
      document.body.removeChild(textarea);
      this.$message.success('复制成功');
    },
    // 获取表头 get_header_columns
    get_header_columns() {
      let params = {
        business_code: 10,
        source_type: this.search_form.source_type,
        db_type: this.search_form.db_type,
        data_source_id: this.search_form.data_source_id
      };
      get_header_columns(params)
        .then((res) => {
          res.data.data.display_list.forEach((item) => {
            this.transfer_value.push(item.key);
          });
          this.table_headers_list = res.data.data.display_list;

          res.data.data.all_list.map((item) => {
            item.label = item.value;
          });
          this.table_headers_all_list = res.data.data.all_list;
          this.$refs.table_.doLayout();
        })
        .catch((e) => {});
    },
    handleSizeChange(size) {
      this.search_form.page_size = size;
      this.get_detail_data_list();
    },
    handleCurrentChange(pageNum) {
      this.search_form.page_number = pageNum;
      this.get_detail_data_list();
    },
    get_detail_data_list() {
      this.loading_list = true;
      let params = {
        source_type: 1,
        db_type: 'POSTGRE',
        data_source_id: 10,
        execution_time: ['2025-03-20 08:17:51', '2025-03-20 10:17:51'],
        executive_user: '',
        executive_schema: '',
        risk_level: '',
        sql_id: '7ed8f44f52d9817e72fd7634216e7396',
        sql_text: '',
        page_number: 1,
        page_size: 10,
        _sorter: '{"execution_average_time":"ascend"}'
      };
      let req = {
        source_type: this.search_form.source_type,
        db_type: this.search_form.db_type,
        data_source_id: this.search_form.data_source_id,
        execution_time: this.search_form.execution_time,
        executive_user: this.search_form.executive_user,
        executive_schema: this.search_form.executive_schema,
        risk_level: this.search_form.risk_level,
        sql_id: this.search_form.sql_id,
        sql_text: this.search_form.sql_text,
        page_number: this.search_form.page_number,
        page_size: this.search_form.page_size,
        _sorter: this.search_form._sorter,
        page_number: this.search_form.page_number,
        page_size: this.search_form.page_size
      };
      let sendd = {
        ...this.search_form,
        risk_level: this.search_form.risk,
        page_number: 1,
        page_size: 10,
        _sorter: ''
      };
      API.get_detail_data_list(req)
        .then((res) => {
          if (res.data.data.results.length > 0) {
            this.headers_keys = Object.keys(res.data.data.results[0]);
          }

          this.table_count = res.data.data.count;
          this.table_data = res.data.data.results;
          this.$refs.table_.doLayout();
        })
        .catch((e) => {})
        .finally(() => {
          this.loading_list = false;
        });
    },
    get_tendency_metrics_chart() {
      this.loading_chart = true;
      let params = {
        source_type: 1,
        db_type: 'POSTGRE',
        data_source_id: 10,
        execution_time: ['2025-03-20 08:17:51', '2025-03-20 10:17:51'],
        executive_user: '',
        executive_schema: '',
        risk_level: '',
        sql_id: '',
        sql_text: '',
        step: 900,
        _sorter: ''
      };
      let req = {
        source_type: this.search_form.source_type,
        db_type: this.search_form.db_type,
        data_source_id: this.search_form.data_source_id,
        execution_time: this.search_form.execution_time,
        executive_user: this.search_form.executive_user,
        executive_schema: this.search_form.executive_schema,
        risk_level: this.search_form.risk_level,
        sql_id: this.search_form.sql_id,
        sql_text: this.search_form.sql_text,
        page_number: this.search_form.page_number,
        page_size: this.search_form.page_size,
        _sorter: this.search_form._sorter,
        step: this.search_form.step ||120
      };
      API.get_tendency_metrics_chart(req)
        .then((res) => {
          let chart_data = res.data.data;
          const dom = document.getElementById('child_1');
          this.chart1 = this.$echarts.init(dom);
          let chart_arr = [];
          if (this.search_form.source_type == 10) {
            chart_arr = chart_data.filter((item) => {
              return (
                item.code == 'execution_number' ||
                item.code == 'sql_effected_rows'
              );
            });
          } else {
            chart_arr = chart_data.filter((item) => {
              return (
                item.code == 'execution_number' ||
                item.code == 'examined_total_rows' ||
                item.code == 'plans'
              );
            });
          }

          let pg_connections_pct = res.data.data[0];
          let times = [];
          pg_connections_pct.values.forEach((item_) => {
            times.push(moment(item_.timestamp * 1000).format('MM-DD HH:mm:ss'));
          });
          let series_arr = [];
          let legend_data = [];

          chart_arr.forEach((item) => {
            series_arr.push({
              name: item.name,
              type: 'line',
              data: item.values,
              smooth: true,
              showSymbol: false,
              yAxisIndex: 0
            });
            legend_data.push(item.name);
          });

          const option = {
            title: {
              text: 'SQL执行耗时',
              textStyle: {
                fontSize: 12,
                fontWeight: 500
              }
            },
            grid: {
              left: 50,
              top: 50,
              right: 40,
              bottom: 70
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross'
              }
            },
            legend: {
              icon: 'pin', // 设置图例图标为长方形
              itemWidth: 16, // 设置图例图标的宽度
              itemHeight: 8, // 设置图例图标的高度
              align: 'left',
              // orient: 'vertical', // 设置图例为垂直排列
              x: 'right', // 设置图例位置在左侧
              y: 'top', // 设置图例垂直居中
              data: legend_data
            },
            xAxis: {
              type: 'category',
              data: times,
              axisLabel: {
                rotate: 45, // 设置刻度标签旋转角度
                align: 'center', // 设置对齐方式
                margin: 40
              }
            },
            yAxis: [
              {
                type: 'value'
              },
              {
                type: 'value'
              }
            ],
            series: series_arr
          };

          this.chart1.setOption(option);
          // 自适应窗口大小
          window.addEventListener('resize', () => {
            this.chart1.resize();
          });

          let chart_arr2 = [];

          if (this.search_form.source_type == 10) {
            chart_arr2 = chart_data.filter((item) => {
              return (
                item.code == 'execution_total_time' ||
                item.code == 'execution_average_time' ||
                item.code == 'execution_maximum_time'
              );
            });
          } else {
            chart_arr2 = chart_data.filter((item) => {
              return (
                item.code == 'execution_total_time' ||
                item.code == 'execution_average_time' ||
                item.code == 'execution_maximum_time' ||
                item.code == 'total_plan_time'
              );
            });
          }

          let pg_connections_pct2 = res.data.data[0];
          let times2 = [];
          pg_connections_pct2.values.forEach((item_) => {
            times2.push(
              moment(item_.timestamp * 1000).format('MM-DD HH:mm:ss')
            );
          });
          let series_arr2 = [];
          let legend_data2 = [];
          chart_arr2.forEach((item) => {
            series_arr2.push({
              name: item.name,
              type: 'line',
              data: item.values,
              smooth: true,
              showSymbol: false,
              yAxisIndex: 0
            });
            legend_data2.push(item.name);
          });

          const dom2 = document.getElementById('child_2');
          this.chart2 = this.$echarts.init(dom2);

          const option2 = {
            title: {
              text: 'SQL执行概览',
              textStyle: {
                fontSize: 12,
                fontWeight: 500
              }
            },
            grid: {
              left: 50,
              top: 50,
              right: 40,
              bottom: 70
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross'
              }
            },
            legend: {
              icon: 'pin', // 设置图例图标为长方形
              itemWidth: 16, // 设置图例图标的宽度
              itemHeight: 8, // 设置图例图标的高度
              align: 'left',
              // orient: 'vertical', // 设置图例为垂直排列
              x: 'right', // 设置图例位置在左侧
              y: 'top', // 设置图例垂直居中
              data: legend_data2
            },
            xAxis: {
              type: 'category',
              data: times2,
              axisLabel: {
                rotate: 45, // 设置刻度标签旋转角度
                align: 'center', // 设置对齐方式
                margin: 40
              }
            },
            yAxis: [
              {
                type: 'value'
              },
              {
                type: 'value'
              }
            ],
            series: series_arr2
          };
          this.chart2.setOption(option2);
          // 自适应窗口大小
          window.addEventListener('resize', () => {
            this.chart2.resize();
          });
        })
        .catch((e) => {})
        .finally(() => {
          this.loading_chart = false;
        });
    },

    time_ok(e) {
      this.search_form.execution_time = [
        moment(e[0]._d).format('YYYY-MM-DD HH:mm:ss'),
        moment(e[1]._d).format('YYYY-MM-DD HH:mm:ss')
      ];

      this.get_tendency_metrics_chart();
      this.get_detail_data_list();
    },
    moment,
    sql_text_btn() {
      this.sql_text_visable = !this.sql_text_visable;
    },
    initChart1(chart_data) {},
    initChart2() {}
  }
};
</script>

<style lang="less" scoped>
.block {
  margin: 26px 0;
  text-align: right;
}
</style>