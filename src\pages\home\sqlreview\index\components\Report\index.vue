<template>
  <a-drawer
    title="问题总览"
    :width="'70%'"
    wrapClassName="sqlreview-report-drawer"
    :visible="visible"
    @close="onCancel"
  >
    <!-- <div
      :style="{ position: 'absolute', top: 0, right: 0, marginRight: '24px', height: '56px', display: 'flex', alignItems: 'center' }"
    >
      <a-button type="primary">下载PDF</a-button>
    </div>-->
    <a-spin style="position: relative" :spinning="loading">
      <!-- 内容区域 -->
      <div class="srd-content-title">问题分类</div>
      <a-row>
        <a-col class="custom-bg" :span="13">
          <Chart :option="pieOption" />
        </a-col>
        <a-col style="padding: 16px" class="custom-bg" :span="10">
          <div>
            <div>
              <span
                class="status-icon"
                style="background-color: #14c55f"
              ></span>
              <span style="font-weight: 700">通过</span>
            </div>
            <!-- <Table v-bind="tableParams" :dataSource="tableData"></Table> -->
            <div style="height: 160px">
              <Chart :option="passOption" ref="passBar" />
            </div>
            <div>
              <span
                class="status-icon"
                style="background-color: #f73232"
              ></span>
              <span style="font-weight: 700">不通过</span>
            </div>
            <div style="height: 180px">
              <Chart :option="noPassOption" ref="noPassBar" />
            </div>
          </div>
        </a-col>
      </a-row>
      <div
        style="margin-top: 16px; padding-bottom: 32px"
        class="srd-content-title"
      >
        规则触发排行
      </div>
      <a-row>
        <div class="srd-rule-tab">
          <a :class="{ active: ruleType === 'SQL' }" @click="onChange('SQL')"
            >SQL</a
          >
          <a-divider type="vertical" />
          <a :class="{ active: ruleType === 'RULE' }" @click="onChange('RULE')"
            >规则</a
          >
        </div>
        <a-col class="custom-bg" style="height: 400px" :span="13">
          <Chart :option="barOption" ref="bar" />
        </a-col>
        <a-col style="padding: 16px" class="custom-bg" :span="10">
          <div v-if="ruleType === 'SQL'">
            <div class="srd-rule-item-detail">
              <span>SQL ID：</span>
              <a @click="goDetail(sqlDetail.name)">{{ sqlDetail.name }}</a>
              <!-- <span>{{sqlDetail.name}}</span> -->
              <a-popover>
                <template slot="content">
                  <pre>{{ sqlDetail.sql_text }}</pre>
                </template>
                <a-icon style="marginleft: 4px" type="question-circle" />
              </a-popover>
            </div>
            <div class="srd-rule-item-detail">
              <div>触发规则：</div>
              <ol>
                <li v-for="(item, index) in sqlDetail.detail" :key="index">
                  {{ item }}
                </li>
              </ol>
            </div>
            <!-- <div class="srd-rule-item-detail">
              <div>cost指标：{{sqlDetail.cost || 0}}</div>
            </div> -->
          </div>
          <div v-else>
            <!-- <div class="srd-rule-item-detail">
              <span>SQL ID：</span>
              <a @click="goDetail(commonDetail.name)">{{commonDetail.name}}</a>
              <a-popover>
                <template slot="content">
                  <pre>{{commonDetail.sql_text}}</pre>
                </template>
                <a-icon style="marginLeft:4px" type="question-circle" />
              </a-popover>
            </div>-->
            <div class="srd-rule-item-detail">
              <span>规则名：</span>
              <span>{{ sqlDetail.name }}</span>
            </div>
            <div class="srd-rule-item-detail">
              <div>触发次数：{{ sqlDetail.count || 0 }}</div>
            </div>
            <Table
              v-bind="ruleDetailTableParams"
              :dataSource="sqlDetail.detail"
            >
              <template slot="id" slot-scope="{ text }">
                <a @click="goDetail(text)">{{ text }}</a>
              </template>
              <template slot="sql_text" slot-scope="{ text }">
                <LimitLabel
                  style="width: 100%; display: inline-block"
                  :label="text || ''"
                  mode="ellipsis"
                ></LimitLabel>
              </template>
            </Table>
            <!-- <div class="srd-rule-item-detail">
              <div>规则详情：</div>
              <ol>
                <li v-for="(item, index) in sqlDetail.detail" :key="index">{{item}}</li>
              </ol>
            </div>-->
          </div>
        </a-col>
      </a-row>
    </a-spin>
  </a-drawer>
</template>

<script>
import config from './config';
import Chart from '@/components/Chart';
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import { getReviewDetailReport } from '@/api/home';

export default {
  components: { Chart, Table, LimitLabel },
  props: {},
  data() {
    this.config = config(this);
    this.sqlCategoryData = [];
    this.ruleCategoryData = [];
    this.passArr = [];
    this.noPassArr = [];
    return {
      visible: false,
      loading: false,
      pieOption: null,
      tableParams: {
        columns: this.config.columns,
        pagination: false,
        rowKey: 'name'
      },
      tableData: [],
      ruleDetailTableParams: {
        columns: this.config.ruleDetailColumns,
        pagination: false,
        rowKey: 'id',
        scroll: { y: 234 },
        size: 'middle'
      },
      ruleDetailTableData: [],
      ruleType: 'SQL',
      barOption: null,
      sqlDetail: {},
      commonDetail: {},
      passOption: null,
      noPassOption: null
    };
  },
  mounted() {},
  created() {},
  computed: {},
  watch: {},
  methods: {
    show(record) {
      this.visible = true;
      this.ruleType = 'SQL';
      // 发起请求
      this.loading = true;
      ReqUtil.req({
        ctx: this,
        reqInstance: getReviewDetailReport,
        params: {
          review_id: record.id
        },
        needLoading: false,
        cbk: data => {
          this.loading = false;
          const {
            count_type: countType = [],
            sql_category: sqlCategory = [],
            rule_category: ruleCategory = [],
            // df: passAndNoPass = []
            pass: passArr = [],
            not_pass: noPassArr = []
          } = data || {};
          // let passIndex = passArr.findIndex(item => item.name == '通过');
          // let noPassIndex = noPassArr.findIndex(item => item.name == '不通过');
          // passArr.splice(passIndex, 1);
          // noPassArr.splice(noPassIndex, 1);
          // console.log('通过的数组===', passArr, '不通过的数组', noPassArr);
          // 设置相同最大值
          let commentArr = [];
          passArr.forEach(item => {
            commentArr.push(item.count);
          });
          noPassArr.forEach(item => {
            commentArr.push(item.count);
          });
          let max = commentArr.sort((a, b) => b - a)[0];
          // 设置通过bar
          this.passArr = passArr;
          this.passOption = this.config.passNoPassBar({
            data: this.passArr,
            ruleType: 'pass',
            max
          });
          this.noPassArr = noPassArr;
          this.noPassOption = this.config.passNoPassBar({
            data: this.noPassArr,
            ruleType: 'noPass',
            max
          });
          // 设置pie和table
          let countTypeData = countType.sort((a, b) => a.number - b.number);
          this.pieOption = this.config.pieOption(countTypeData);
          this.tableData = countTypeData;

          // 设置bar
          let sqlCategoryData = sqlCategory
            .filter(item => item.count > 0)
            .slice(0, 8);
          let ruleCategoryData = ruleCategory
            .filter(item => item.count > 0)
            .slice(0, 8);
          // 缓存
          this.sqlCategoryData = sqlCategoryData;
          this.ruleCategoryData = ruleCategoryData;
          // 设置
          this.setBarOption({ current: 0 });
          // 绑定事件
          this.bindEvent();
        },
        err: res => {
          this.sqlCategoryData = [];
          this.ruleCategoryData = [];
          this.tableData = [];
          this.passArr = [];
          this.noPassArr = [];
          this.pieOption = this.config.pieOption(this.tableData);
          this.setBarOption({ current: 0 });
          this.passOption = this.config.passNoPassBar({
            data: this.passArr,
            ruleType: 'pass'
          });
          this.noPassOption = this.config.passNoPassBar({
            data: this.noPassArr,
            ruleType: 'noPass'
          });
        }
      })
        .then(() => {
          this.$hideLoading({ duration: 0 });
          this.loading = false;
        })
        .catch(e => {
          this.loading = false;
        });
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onChange(e) {
      this.ruleType = e;
      this.setBarOption({ current: 0 });
    },
    setBarOption(params = {}) {
      const { current } = params;
      const { ruleType } = this;
      const data =
        ruleType === 'SQL' ? this.sqlCategoryData : this.ruleCategoryData;
      this.barOption = this.config.barOption({
        current,
        data: data,
        ruleType
      });
      const sqlData = this.sqlCategoryData;
      this.sqlDetail = data[current] || {};
      this.commonDetail = sqlData[current] || {};
    },
    skipFun(params = {}) {
      if (params.componentType === 'yAxis') {
        let id = params.value.replace(/[^0-9]/gi, '');
        this.$router.push({
          // name: 'home-sqlreview-review',
          name: 'code-review-review',
          params: { id }
        });
      }
    },
    bindEvent() {
      const { ruleType } = this;
      const data =
        ruleType === 'SQL' ? this.sqlCategoryData : this.ruleCategoryData;
      if (data.length <= 0) return;

      setTimeout(() => {
        const { bar } = this.$refs;
        bar &&
          bar.on('click', e => {
            this.setBarOption({ current: e.dataIndex });
            this.skipFun(e);
          });
      }, 300);
    },
    goDetail(id) {
      this.$router.push({
        // name: 'home-sqlreview-review',
        name: 'code-review-review',
        params: { id }
      });
    }
  }
};
</script>

<style lang="less">
.sqlreview-report-drawer {
  .ant-drawer-content-wrapper {
    min-width: 960px;
  }
  .status-icon {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
  }
  .srd-content-title {
    padding: 0 0 16px 0;
  }
  .ant-row {
    display: flex;
    align-items: stretch;
    justify-content: space-between;

    .ant-col {
      position: relative;
      min-height: 320px;
    }

    .srd-rule-tab {
      position: absolute;
      top: -24px;
      left: 16px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      a {
        color: #a7a7a7;
        &:hover {
          color: @primary-color;
        }
        &.active {
          font-weight: bold;
          color: @primary-color;
        }
      }
    }

    .srd-rule-item-detail {
      margin-bottom: 8px;

      > div:first-child,
      > span:first-child {
        font-weight: bold;
      }

      ol {
        padding: 0 16px 0 32px;
      }
      li {
        // list-style: none;
        margin-top: 8px;
      }
    }
  }
}
</style>
