<template>
  <a-empty
    v-bind="emptyProps"
    :class="['custom-empty', useUnifiedEmpty && 'use-unified-empty']"
    :image="emptyImage"
  />
</template>

<script>
import { Empty } from 'ant-design-vue';
const Map = {
  default: Empty.PRESENTED_IMAGE_DEFAULT,
  simple: Empty.PRESENTED_IMAGE_SIMPLE
};

export default {
  components: {},
  props: {
    image: {
      type: String | Object,
      default: 'default'
    }
  },
  data() {
    return {
      useUnifiedEmpty: GLOBAL_CONFIG.useUnifiedEmpty
    };
  },
  computed: {
    emptyImage() {
      return this.useUnifiedEmpty
        ? require('@/assets/img/private/empty.svg')
        : _.isString(this.image)
        ? Map[this.image]
        : this.image;
    },
    emptyProps() {
      return this.$attrs;
    }
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.custom-empty {
}
</style>
