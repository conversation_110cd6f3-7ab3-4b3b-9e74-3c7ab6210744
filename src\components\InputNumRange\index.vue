<template>
  <div class="input-num-range">
    <a-input-number
      class="min-input"
      placeholder="最小值"
      v-bind="rangeProps"
      :value="rangeVal[0]"
      v-on="rangeListeners('min')"
    />
    <span class="connect">~</span>
    <a-input-number
      class="max-input"
      placeholder="最大值"
      v-bind="rangeProps"
      :value="rangeVal[1]"
      v-on="rangeListeners('max')"
    />
  </div>
</template>

<script>
// import _ from 'lodash';
// import Http from '@/utils/request';
// import _ from 'lodash';
const defaultProps = {};

export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {},
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      loacalVal: this.value
    };
  },
  computed: {
    rangeVal() {
      const { loacalVal = [] } = this;
      return loacalVal;
    },
    rangeProps() {
      return { ...defaultProps, ...this.$attrs };
    }
  },
  created() {},
  mounted() {},
  methods: {
    rangeListeners(type) {
      let res = {};
      _.forEach(this.$listeners, (value, key) => {
        res[key] = val => value(val, this.loacalVal, type);
      });
      res['change'] = val => this.onChange(val, type);
      return res;
    },
    onChange(val, type) {
      const { loacalVal = [] } = this;
      const minVal = loacalVal[0];
      const maxVal = loacalVal[1];
      // console.log(minVal, maxVal);

      const emitVal = type === 'min' ? [val, maxVal] : [minVal, val];
      this.loacalVal = emitVal;
      this.$emit('change', emitVal);
    }
  },
  watch: {
    value(newVal) {
      this.loacalVal = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
.input-num-range {
  display: flex;
  align-items: center;
  .min-input,
  .max-input {
    flex-grow: 1;
  }
  .connect {
    display: inline-block;
    width: 40px;
    text-align: center;
  }
}
</style>
