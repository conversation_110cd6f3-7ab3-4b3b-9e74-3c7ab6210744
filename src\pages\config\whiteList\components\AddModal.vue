<template>
  <a-modal
    v-model="visible"
    :title="title"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </a-modal>
</template>

<script>
import format from 'date-fns/format';
import dateFns from 'date-fns';
import moment from 'moment';
import Form from '@/components/Form';
import config from './config';
export default {
  components: { Form },
  data() {
    this.config = config(this);
    return {
      form: this.$form.createForm(this, {}),
      visible: false,
      data: {},
      params: {
        layout: 'horizontal',
        fields: this.config.baseInfo
      },
      title: ''
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(record) {
      this.title = '添加白名单';
      this.visible = true;
      let nowTime = new Date(Date.now());
      let expire = format(dateFns.addDays(nowTime, 31), 'YYYY-MM-DD HH:mm:ss');
      let takeEffect = format(nowTime, 'YYYY-MM-DD HH:mm:ss');
      this.data = Object.assign({}, this.data, {
        white_list_classify: '表级别',
        take_effect: moment(takeEffect),
        expire: moment(expire)
      });
      // if (record) {
      //   this.title = '编辑任务';
      //   if (record.time_range) {
      //     record.time_range = [
      //       moment(record.time_range[0]),
      //       moment(record.time_range[1])
      //     ];
      //   }
      //   this.data = {
      //     name: record.name,
      //     type: record.type,
      //     data_source: record.data_source.id,
      //     schema: record.schema.id || '',
      //     time_range: record.time_range,
      //     rule_set: record.rule_set
      //   };
      // }
    },
    hide() {
      this.visible = false;
      this.$refs.form.resetFields();
      this.data = Object.assign({}, this.data, {
        table_name: []
      });
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          let getData = form.getData() || {};
          getData.white_list_classify = '表级别';
          getData.take_effect = getData.take_effect.format(
            'YYYY-MM-DD HH:mm:ss'
          );
          getData.expire = getData.expire.format('YYYY-MM-DD HH:mm:ss');
          this.$emit('save', getData);
          this.hide();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.ant-row {
  width: 100%;
  .multi-cols-item {
    margin: 0;
    padding: 0;
  }
}
</style>
