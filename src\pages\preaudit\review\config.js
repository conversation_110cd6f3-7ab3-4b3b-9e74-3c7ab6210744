const options = [
  { label: '低档（QPS100以下）', value: 'low' },
  { label: '中档（QPS100~300）', value: 'middle' },
  { label: '高档（QPS300以上）', value: 'high' }
];
const sqlOptions = [
  { label: '低档（低于1000条/天）', value: 'low' },
  { label: '中档（1000-10000条/天）', value: 'middle' },
  { label: '高档（大于10000条/天）', value: 'high' }
];
export default function (ctx) {
  const fields = [
    {
      type: 'RadioGroup',
      label: '平均调用频率',
      key: 'sqlmap_average_frequency',
      props: {
        options: options
      },
      width: 'auto',
      slots: [
        { key: 'average_private', wrapperStyle: { display: 'inline-block' } }
      ]
      // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'RadioGroup',
      label: '峰值调用频率',
      key: 'sqlmap_max_frequency',
      props: {
        options: options
      },
      width: 'auto',
      slots: [{ key: 'max_private', wrapperStyle: { display: 'inline-block' } }]
      // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'TableEdit',
      label: '每月数据量增加',
      key: 'sqlmap_monthly_increase',
      getDataMethod: 'getData',
      resetFieldsMethod: 'resetFields',
      initialValue: [],
      props: {
        columns: [
          {
            title: '表名',
            dataIndex: 'table_name',
            key: 'table_name',
            width: 300
          },
          {
            title: 'SCHEMA名',
            dataIndex: 'schema',
            key: 'schema',
            width: 300
          },
          {
            title: '数据增加档数',
            dataIndex: 'frequency',
            key: 'frequency',
            width: 300,
            scopedSlots: { customRender: 'frequency' }
          }
        ],
        editConfig: {
          frequency: (row, record = {}) => {
            return {
              type: 'Select',
              props: {
                options: sqlOptions,
                placeholder: '请选择'
                // mode: 'tags',
                // maxTags: 1
                // separator: ','
              },
              rules: [
                // { required: true, message: '该项为必填项' }
              ]
            };
          }
        },
        // mode: 'list',
        initEditStatus: true,
        pagination: false,
        size: 'small'
        // leastNum: 1,
        // actionBtns: ['add', 'remove'],
        // actionBtnsIcons: {
        //   add: 'plus-circle',
        //   remove: 'close-circle'
        // }
      },
      width: '80%'
    },
    {
      type: 'RadioGroup',
      label: '白名单申请',
      key: 'sqlmap_white_list',
      props: {
        options: [
          { label: '加入白名单', value: 1 },
          { label: '不加入', value: 0 }
        ]
      }
    },
    {
      type: 'Textarea',
      label: '备注',
      key: 'sqlmap_note',
      width: '80%',
      props: {
        // size: 'small'
      }
      // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    }
  ];

  const pieOption = (data = {}) => {
    const arr = data.data.map((item) => {
      return item.value;
    });
    const arr1 = arr.filter((item) => {
      return item != 0;
    });
    if (data.data.length <= 0) return;
    return {
      color: [
        new ctx.$echarts.graphic.LinearGradient(1, 1, 0, 0, [
          {
            offset: 0,
            color: '#ff4d4f'
          },
          {
            offset: 0.9,
            color: '#FF7545'
          }
        ]),
        new ctx.$echarts.graphic.LinearGradient(1, 1, 0, 0, [
          {
            offset: 0,
            color: '#76C6FF'
          },
          {
            offset: 0.9,
            color: '#3470FF'
          }
        ]),
        new ctx.$echarts.graphic.LinearGradient(1, 1, 0, 0, [
          {
            offset: 0,
            color: '#09D06C'
          },
          {
            offset: 0.9,
            color: '#8AEACF'
          }
        ])
      ],
      title: {
        text: '风险等级',
        textStyle: {
          color: 'rgba(0, 0, 0, 0.85)',
          fontSize: 16,
          fontWeight: 500
        },
        left: 'center',
        top: '45%'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['50%', '75%'],
          center: ['50%', '50%'],
          clockWise: false,
          data: data.data.map((item) => {
            if (arr1.length <= 0) {
              return {
                value: item.value,
                name: item.name
              };
            }
            return {
              value: item.value == 0 ? null : item.value,
              name: item.name
            };
          }),
          // roseType: 'radius',
          itemStyle: {
            borderWidth: 2,
            borderColor: '#ffffff'
          },
          label: {
            show: true,
            position: 'inner',
            color: '#fff',
            formatter: '{c}',
            fontWeight: 'bold'
          }
        }
      ]
    };
  };
  return {
    fields,
    pieOption
  };
}
