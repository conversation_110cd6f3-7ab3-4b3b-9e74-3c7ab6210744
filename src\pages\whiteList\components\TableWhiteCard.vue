<template>
  <div class="card-table">
    <div class="card-table-main-info">
      <div class="left-block">
        <div class="info-box">
          <span class="project-name">
            <span v-if="cardData.table_name.length <= 16">{{
              cardData.table_name
            }}</span>
            <LimitLabel
              :label="cardData.table_name || ''"
              mode="ellipsis"
              :noWrap="true"
              v-else
            ></LimitLabel>
          </span>
          <div class="status-box">
            <span>
              <span>审核状态</span>
              <StatusTag type="dba" :status="auditStatus" />
            </span>
          </div>
        </div>

        <div class="tag-box">
          <a-popover overlayClassName="order-table-white-card-popover">
            <template slot="content">
              <div>
                <span>数据源 :</span>
                <span>{{
                  cardData.data_source_name + '(' + cardData.db_url + ')'
                }}</span>
              </div>
              <!-- <div>
                <span>Schema :</span>
                <span>{{cardData.schema_name}}</span>
              </div> -->
            </template>
            <InstanceItem
              mode="ellipsis"
              :tagText="cardData.env"
              :src="cardData.db_type"
              :text="cardData.data_source_name"
              :isNeedTips="false"
              percent="85%"
            ></InstanceItem>
          </a-popover>
          <span>{{ cardData.schema_name }}</span>
          <span>生效：{{ cardData.take_effect }}</span>
          <span>失效：{{ cardData.expire }}</span>
        </div>

        <div class="side-info-box">
          <span class="avatar-part">
            <a-avatar icon="user" />
          </span>
          <a-tooltip v-if="cardData.ch_creater">
            <template slot="title">
              <span>{{ cardData.created_by }}</span>
            </template>
            <span class="created-by">{{ cardData.ch_creater || '--' }}</span>
          </a-tooltip>
          <span class="created-by" v-else>{{ cardData.created_by }}</span>
          <span>于{{ cardData.created_at + ' ' }}</span>
          <span class="event">创建</span>
        </div>
      </div>
      <div class="right-block">
        <div class="right-block-rules">
          <div>
            <span>审核DBA</span>
            <a-tooltip v-if="cardData.ch_dba">
              <template slot="title">
                <span>{{ cardData.operate_dba }}</span>
              </template>
              <span class="operate-dba">{{ cardData.ch_dba }}</span>
            </a-tooltip>
            <span v-else class="operate-dba">{{
              cardData.operate_dba || '--'
            }}</span>
            <!-- <span class="operate-dba">{{cardData.operate_dba || '--'}}</span> -->
          </div>
        </div>
        <a-divider type="vertical" />
        <div class="right-block-sql-text">
          <div>
            <span>状态</span>
            <span :style="{ color: auditStatusColor }">{{ auditStatus }}</span>
          </div>
        </div>
        <a-divider class="right-block-divider" type="vertical" />
        <div class="right-block-botton">
          <slot name="action"></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';
import StatusTag from '@/components/Biz/Status/Tag';
import InstanceItem from '@/components/Biz/InstanceItem';
export default {
  components: { LimitLabel, StatusTag, InstanceItem },
  props: {
    cardData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  computed: {
    auditStatus() {
      let statusText = {
        0: '待审核',
        1: '已通过',
        '-1': '未通过',
        null: '已通过'
      };
      let auditStatus = this.cardData.audit_status;
      return statusText[auditStatus];
    },
    auditStatusColor() {
      let statusText = {
        0: '#F29339',
        1: '#4CBB3A',
        '-1': '#F38E98',
        null: '#4CBB3A'
      };
      let auditStatus = this.cardData.audit_status;
      return statusText[auditStatus];
    }
  },
  watch: {},
  mounted() {},
  created() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.card-table {
  .card-table-main-info {
    display: flex;
    justify-content: space-between;
    .left-block {
      display: flex;
      flex-direction: column;
      margin-right: 64px;
      .info-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .status-box {
          > span {
            > span {
              margin-right: 4px;
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #a1a1aa;
              font-weight: 400;
            }
          }
        }
        .project-name {
          position: relative;
          white-space: nowrap;
          top: -2px;
          > span {
            font-family: PingFangSC-Semibold;
            font-size: 20px;
            color: #27272a;
            font-weight: 600;
            max-width: 300px;
            display: block;
            margin-right: 28px;
          }
          /deep/.limit-label {
            pre {
              font-family: PingFangSC-Semibold;
              font-size: 20px !important;
              color: #27272a;
              font-weight: 600 !important;
            }
          }
        }
      }
      .tag-box {
        margin: 0 0 16px 0;
        display: flex;
        flex-wrap: wrap;
        /deep/ .biz-instance-item {
          max-width: 300px;
          margin-right: 16px;
          margin-top: 12px;
          white-space: nowrap;
          .instance-item-tag {
            padding: 3px 7px 2px 7px;
            border: 1px solid #e4e4e7;
            margin-bottom: 0;
            .instance-item-tag-tag {
              height: 20px;
              line-height: 18px;
              border-radius: 6px;
              padding: 0 5px;
              margin-right: 0px;
              font-size: 12px;
            }
            > .database-image {
              margin-left: 46px;
              padding-right: 6px;
              .iconClass {
                align-items: center;
                .anticon {
                  padding: 0;
                  font-size: 16px;
                }
                .limit-label {
                  > pre {
                    font-size: 14px;
                    color: #27272a;
                    font-weight: 400;
                  }
                }
              }
            }
          }
        }
        > span {
          margin-top: 12px;
          white-space: nowrap;
          margin-right: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #27272a;
          font-weight: 400;
          text-align: center;
          border: 1px solid #e4e4e7;
          border-radius: 4px;
          padding: 2px 7px;
        }
      }
      .side-info-box {
        display: flex;
        align-items: center;
        > span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          letter-spacing: 0;

          font-weight: 400;
          &.avatar-part {
            width: 20px;
            .ant-avatar {
              width: 20px;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              margin-bottom: 2px;
              color: #fff;
              background: #4ec3f5;
            }
          }
        }
        .created-by {
          margin: 0 4px 0 8px;
          color: rgb(113, 113, 122);
        }
        .event {
          color: rgb(113, 113, 122);
        }
      }
    }
    .right-block {
      display: flex;
      align-items: center;
      .ant-divider-vertical {
        width: 1px;
        height: 32px;
        margin: 0 12px;
      }
      .right-block-rules,
      .right-block-sql-text {
        padding: 0 18px;
        display: flex;
        align-items: center;
        > div {
          display: flex;
          flex-direction: column;
          text-align: center;
          white-space: nowrap;
          & span:first-child {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #71717a;
            font-weight: 400;
            margin-bottom: 8px;
            white-space: nowrap;
          }
          & span:last-child {
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: #27272a;
            font-weight: 500;
          }
        }
      }
      .right-block-botton {
        padding-left: 18px;
        // width: 150px;
        width: auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        > a {
          height: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #4db5f2;
          letter-spacing: 0;
          line-height: 16px;
          font-weight: 400;
          display: flex;
          align-items: center;
          margin-right: 32px;
          > .anticon {
            margin-right: 4px;
          }
          &.highlight {
            padding: 14px 6px 14px 8px;
            background: #4db5f2;
            border-radius: 4px;
            color: #fff;
            margin-right: 0;
            > .anticon {
              font-size: 12px;
              margin-right: 0;
              margin-left: 4px;
            }
          }
          &.check {
            padding: 14px 6px 14px 8px;
            background: #4db5f2;
            border-radius: 4px;
            color: #fff;
            margin-right: 0;
            > .anticon {
              font-size: 12px;
              margin-right: 0;
              margin-left: 4px;
            }
          }
          &:hover {
            color: @primary-color;
            &.highlight {
              color: #fff;
              background: @primary-color;
            }
          }
        }
        > a[disabled] {
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .card-table {
    .card-table-main-info {
      .right-block {
        display: flex;
        justify-content: space-between;
        .right-block-divider {
          display: none;
        }
        .right-block-botton {
          width: 100%;
          min-width: 240px;
          display: none;
        }
      }
      // .left-block {
      //   .info-box {
      //     .project-name {
      //       > span {
      //         max-width: 300px;
      //       }
      //     }
      //   }
      // }
    }
  }
}
</style>
<style lang="less">
.ant-popover {
  &.order-table-white-card-popover {
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 400px;
        > div {
          span {
            font-family: PingFangSC-Semibold;
            font-size: 12px;
            &:first-child {
              color: #27272a;
              font-weight: 600;
            }
            &:last-child {
              color: #71717a;
              font-weight: 400;
            }
          }
        }
      }
    }
  }
}
</style>
