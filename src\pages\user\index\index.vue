<template>
  <div class="content page-list-single">
    <div class="frame-button-wrapper">
      <a-button
        slot="extra"
        class="highlight"
        icon="plus"
        @click="addUserModal"
        v-if="$permission.user('add')"
        >新增用户</a-button
      >
    </div>
    <Table
      ref="table"
      v-bind="tableParams || {}"
      class="new-view-table small-size"
    >
      <template v-slot:groups_name="{ text, record }">
        <LimitTags
          :tags="text.map((item) => ({ label: item }))"
          :limit="2"
        ></LimitTags>
        <span v-if="record.groups_name.length === 0">{{ '--' }}</span>
        <!-- <div class="tagStyle">
          <div :class="(id === record.id) && flag  ? '' : 'tag-item'">
            <a-tag v-for="tag in text" :key="tag">{{tag}}</a-tag>
            <span v-if="record.groups_name.length === 0">{{'--'}}</span>
          </div>
          <a-icon
            v-if="record.groups_name.length > 1"
            :type="flag && (id === record.id) ? 'double-left' : 'double-right'"
            @click="toggleUpDown(record)"
          />
        </div>-->
      </template>
      <custom-btns-wrapper slot="action" slot-scope="{ record }">
        <a
          @click="resetPwdModal(record)"
          actionBtn
          v-if="$permission.user('resetPwd')"
          >重置密码</a
        >
        <a
          @click="userRoleModal(record)"
          actionBtn
          v-if="$permission.user('modifyAuth')"
          >修改权限</a
        >
        <a-popconfirm
          title="确定删除用户?"
          @confirm="() => deleteUser(record)"
          v-if="$permission.user('delete')"
          actionBtn
        >
          <a class="remove">删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </Table>
    <!-- 添加用户弹窗 -->
    <AddModal ref="addModal" @save="saveUser"></AddModal>
    <!-- 修改权限弹窗 -->
    <EditModal ref="editModal" @save="updateRole"></EditModal>
    <!-- 重置密码弹窗 -->
    <ResetModal ref="resetPwdModal" @save="resetPwd"></ResetModal>
  </div>
</template>

<script>
import _ from 'lodash';
import Table from '@/components/Table';
import SearchArea from '@/components/SearchArea';
import LimitTags from '@/components/LimitTags';
import { userDelete, userAdd, updateUserRole, resetPassword } from '@/api/user';
import AddModal from './components/AddModal';
import EditModal from './components/EditModal';
import ResetModal from './components/ResetModal';
import { Base64 } from 'js-base64';
import config from './config';
// import bodyMinWidth from '@/mixins/bodyMinWidth';
const data = [];

export default {
  name: 'userList',
  props: {},
  // mixins: [bodyMinWidth(1280)],
  components: { Table, SearchArea, AddModal, EditModal, ResetModal, LimitTags },
  data() {
    this.config = config(this);
    return {
      flag: false,
      id: null,
      data,
      visible: false,
      confirmLoading: false,
      tableParams: {
        url: '/sqlreview/user/',
        rowKey: 'id',
        columns: this.config.columns,
        isInitReq: false,
        needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 'max-content' }
      },
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 3
      }
    };
  },
  computed: {},
  mounted() {},
  methods: {
    // 上下折叠
    toggleUpDown(record) {
      // console.log();
      if (this.flag && this.id !== record.id) {
        this.flag = true;
        this.id = record.id;
      } else {
        this.flag = !this.flag;
        this.id = record.id;
      }
    },
    resetPwdModal(record) {
      const { resetPwdModal } = this.$refs;
      resetPwdModal.show(record);
    },
    userRoleModal(record) {
      const { editModal } = this.$refs;
      editModal.show(record);
    },
    // 新建用户Modal
    addUserModal() {
      const { addModal } = this.$refs;
      addModal.show();
    },
    // 新建用户保存
    saveUser(data = {}) {
      const { addModal, table } = this.$refs;
      // console.log(JSON.stringify(data), '898989');
      const { pwd, confirm, userRole } = data;
      if (pwd !== confirm) {
        this.$message.error('密码不一致！');
        return;
      }
      if (!userRole) {
        this.$message.error('用户角色不能为空！');
        return;
      }
      this.$showLoading();
      return userAdd({
        name: data.name,
        password: Base64.encode(data.pwd || ''),
        email: data.email,
        role: data.userRole,
        groups: data.groups,
        ch_name: data.ch_name
      })
        .then(res => {
          this.$hideLoading();
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            addModal.hide();
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 更新用户角色
    updateRole(data) {
      const { editModal, table } = this.$refs;
      console.log(JSON.stringify(data), 'dddddd');
      this.$showLoading();
      // 判断当前登录用户与修改权限目标用户是否是同一用户，且权限由管理员修改为其他角色，如果是，则修改权限后，路由回主页面
      // const routerToHome =
      //   data.id === this.$store.state.account.user.id &&
      //   data.userRole !== 'admin';
      return updateUserRole(
        {
          role: data.userRole, // 'dba'
          groups: data.groups,
          email: data.email,
          ch_name: data.ch_name
        },
        data.id
      )
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            editModal.hide();
            // if (routerToHome) {
            //   this.$router.push({ name: 'home' });
            //   table.refresh();
            // } else {
            //   table.refresh();
            // }
            // 老逻辑要回首页，且{ name: 'home' }已经没有了，现在留在本页
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 修改密码
    resetPwd(data) {
      const { name, pwd, confirm } = data;
      const { resetPwdModal, table } = this.$refs;
      if (pwd !== confirm) {
        this.$message.error('新密码和确认密码不一致！');
        return;
      }
      this.$showLoading();
      return resetPassword({
        user_name: name,
        password: Base64.encode(pwd || '')
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            resetPwdModal.hide();
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 删除用户
    deleteUser(data) {
      const { table } = this.$refs;
      this.$showLoading();
      return userDelete(data.id)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.refresh();
    }
  }
};
</script>

<style lang="less" scoped>
.tagStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .tag-item {
    height: 24px;
    overflow: hidden;
  }
  .ant-tag {
    margin-bottom: 5px;
  }
  .anticon {
    margin-right: 30px;
  }
  .anticon-double-right {
    transform: rotate(90deg);
  }
  .anticon-double-left {
    transform: rotate(90deg);
  }
}
</style>
