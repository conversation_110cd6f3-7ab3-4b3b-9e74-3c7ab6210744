<template>
  <a-spin class="spin" :spinning="false">
    <a-card
      v-if="sqlPlanInfo.length > 0 || dbaComment || isOrder"
      type="small"
      :bordered="false"
      class="small-card"
    >
      <a-tabs
        :active-key="activeTab || (sqlPlanInfo.length ? 'sqlPlanInfo' : 'dbaPlanInfo')"
        @change="tabChange"
      >
        <a-tab-pane v-if="sqlPlanInfo.length" key="sqlPlanInfo" tab="优化建议">
          <div
            v-for="(item, index) in sqlPlanInfo"
            :key="index"
            :class="['suggest-part', flag ? 'double-left' : 'double-right']"
          >
            <div>{{ item.message }}</div>
            <span>{{ item.sql }}</span>
          </div>
        </a-tab-pane>
        <a-tab-pane v-if="dbaComment || isOrder" key="dbaPlanInfo" tab="DBA建议">
          <div v-if="isOrder" :class="['tab-content', flag ? 'double-left' : 'double-right']">
            <div class="review-wraper">
              <a-textarea
                placeholder="请输入review 建议"
                class="review-advice"
                :rows="6"
                :value="reviewMessage"
                @change="onChangeMessage"
              />
              <div class="sava-button">
                <a-button type="primary" :disabled="!canDo" @click="saveAdvice">保存</a-button>
              </div>
            </div>
          </div>
          <div v-else :class="['suggest-part', flag ? 'double-left' : 'double-right']">
            <div>{{dbaComment}}</div>
            <div>{{dbaHandleComment}}</div>
          </div>
        </a-tab-pane>
        <a-icon
          v-if="isShowFlag"
          slot="tabBarExtraContent"
          :type="flag ? 'double-left' : 'double-right'"
          @click="toggleUpDown()"
        />
      </a-tabs>
    </a-card>
  </a-spin>
</template>

<script>
export default {
  props: {
    sqlPlanInfo: {
      type: Array,
      default: () => []
    },
    dbaHandleComment: {
      type: String,
      default: ''
    },
    dbaComment: {
      type: String,
      default: ''
    },
    isOrder: {
      type: Boolean,
      default: false
    },
    reviewMessage: {
      type: String,
      default: ''
    },
    isShowFlag: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      activeTab: '',
      canDo: false,
      flag: true
    };
  },
  mounted() {
    let user = this.$store.state.account.user || {};
    this.canDo = user.role === 'dba';
  },
  methods: {
    tabChange(data) {
      this.activeTab = data;
    },
    onChangeMessage(event) {
      this.$emit('onChangeMessage', event.target.value);
    },
    saveAdvice() {
      this.$emit('saveAdvice');
    },
    // 上下折叠
    toggleUpDown() {
      this.flag = !this.flag;
    }
  },
  watch: {
    '$store.state.account.user'(newVal = {}) {
      this.canDo = newVal.role === 'dba';
    }
  }
};
</script>

<style lang="less" scoped>
.small-card {
  margin-bottom: 24px;
  .ai-comment-part {
    padding: 16px;
    background-color: #edf5ff;
    border-radius: 5px;
  }
}
.tab-content {
  min-height: 100px;
}
.review-wraper {
  background: #fff;
  // padding: 16px;
  border-radius: 3px;
}
/deep/ textarea.review-advice {
  background-color: rgba(15, 120, 251, 0.06);
  border: 1px solid transparent;
}
.sava-button {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.suggest-part {
  padding: 16px;
  background-color: #edf5ff;
  border-radius: 5px;
}

.double-left {
  display: block;
}

.double-right {
  display: none;
}

.anticon-double-right {
  transform: rotate(90deg);
}
.anticon-double-left {
  transform: rotate(90deg);
}
</style>