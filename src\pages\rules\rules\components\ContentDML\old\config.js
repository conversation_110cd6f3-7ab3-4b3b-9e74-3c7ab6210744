import Bracket from '@/components/Biz/Bracket';
export default function (ctx) {
  const dbType = window.localStorage.getItem('db_type') || '';
  const baseInfo = [
    {
      type: 'RadioGroup',
      label: '规则类型',
      key: 'rule_type',
      props: {
        mode: 'tips',
        class: 'inline',
        options: ['IMPALA', 'HIVE'].includes(dbType)
          ? [
            {
              label: 'DML规则',
              value: 'DML'
            }
          ]
          : [
            {
              label: 'DML规则',
              value: 'DML'
            },
            {
              label: 'DDL规则',
              value: 'DDL'
            }
          ],
        disabled: ctx.type !== 'add'
      },
      listeners: {
        change: (value) => {
          const baseInfo = ctx.$refs.baseInfo;
          baseInfo.saving({
            rule_type: value,
            db_type: null
          });
          if (value === 'DDL') {
            ctx.$emit('change', true);
            ctx.tableDataConditions = [{ category: '', property: '' }];
          } else {
            ctx.$emit('change', false);
            ctx.tableDataConditions = [];
          }
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    (formData) => {
      return {
        type: 'Select',
        label: '数据库类型',
        key: 'db_type',
        hideComponent: true,
        slots: [{ key: 'db_type' }]
      };
    },
    (formData = {}) => {
      return {
        type: 'Select',
        label: '模式类型',
        key: 'ob_mode',
        visible: formData.db_type == 'OCEANBASE' || formData.db_type == 'TDSQL',
        props: {
          options:
            formData.db_type == 'OCEANBASE'
              ? [
                {
                  label: 'OB_MYSQL',
                  value: 'OB_MYSQL'
                },
                {
                  label: 'OB_ORACLE',
                  value: 'OB_ORACLE'
                }
              ]
              : [
                {
                  label: 'TD_MYSQL',
                  value: 'TD_MYSQL'
                },
                {
                  label: 'TD_PGSQL',
                  value: 'TD_PGSQL'
                }
              ]
        },
        listeners: {
          change: (value) => {
            const { baseInfo } = ctx.$refs;
            baseInfo.saving({
              ob_mode: value,
              rule_set_uids: null
            });
            ctx.$set(ctx, 'dbType', value);
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      };
    },
    (formData = {}) => {
      return {
        type: 'Select',
        label: '加入规则集',
        key: 'rule_set_uids',
        props: {
          // mode: 'tips',
          // class: 'inline',
          // options: [...ctx.rulesOptions]
          mode: 'multiple',
          url: '/sqlreview/project/rule_set_all',
          reqParams: {
            db_type: formData.ob_mode || formData.db_type
          }
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      };
    },
    {
      type: 'RadioGroup',
      label: '规则分类',
      key: 'type',
      props: {
        mode: 'tips',
        class: 'inline',
        options: [
          { label: 'SQL 语法规范', value: 1 },
          { label: 'SQL 执行规范', value: 2 },
          { label: 'SQL 执行计划', value: 3 }
        ]
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Input',
      label: '规则名称',
      key: 'name',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Textarea',
      label: '规则描述',
      key: 'desc',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    }
  ];
  const outportResults = [
    {
      type: 'RadioGroup',
      label: '风险类型',
      key: 'rule_result',
      props: {
        mode: 'icon',
        class: 'inline',
        options: [
          {
            label: '高风险',
            value: 0,
            icon: 'lu-icon-alarm',
            tips: '(审核-不通过)',
            style: { fontSize: '16px', color: '#E71D36' }
          },
          {
            label: '低风险',
            value: 1,
            icon: 'lu-icon-alarm',
            tips: '(审核-通过)',
            style: { fontSize: '16px', color: '#F29339' }
          }
        ]
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Markdown',
      label: '优化建议',
      key: 'suggest',
      className: 'suggest',
      props: {}
    }
  ];
  const condition = {
    columns: [
      {
        dataIndex: 'relation',
        key: 'relation',
        width: 120,
        scopedSlots: { customRender: 'relation' }
      },
      {
        dataIndex: 'desc',
        key: 'desc',
        width: '40%',
        scopedSlots: { customRender: 'desc' }
      },
      {
        dataIndex: 'condition',
        key: 'condition',
        width: 150,
        scopedSlots: { customRender: 'condition' }
      },
      {
        key: 'target_value',
        dataIndex: 'target_value',
        // width: 300,
        scopedSlots: { customRender: 'target_value' }
      },
      {
        key: 'action',
        width: 100,
        align: 'right',
        scopedSlots: { customRender: 'action' }
      }
    ],
    editConfig: (params = {}) => {
      return {
        relation: (row, record = {}, tableEdit, colVal) => {
          let options = [
            {
              label: '空置',
              value: ''
            },
            {
              label: 'AND',
              value: 'and'
            },
            {
              label: 'OR',
              value: 'or'
            }
          ];
          if (row > 0) {
            options = options.slice(1);
          }
          return {
            type: 'Select',
            props: {
              options,
              allowClear: false,
              disabled: row == 0,
              class: `relation-${colVal}`
            },
            initialValue: row == 0 ? '' : 'and',
            extra: (h) => {
              return (
                <Bracket color={colVal === 'and' ? '#d0eecc' : '#fce3cb'} />
              );
            }
            // rules: [
            //   { required: true, message: '该项为必填项' }
            // ]
          };
        },
        desc: {
          type: 'InputModal',
          props: {
            style: 'width: 100%',
            suffixIcon: 'down'
          },
          rules: [{ required: true, message: '该项为必填项' }]
        },
        condition: {
          type: 'Select',
          props: {
            url: '/sqlreview/common/item-list/',
            size: 'default'
          },
          cellProps: (row, record = {}) => {
            let reqParams = {
              enable: 1,
              parent_item_key: 'rule_conditions'
            };
            if (record.value_operator) {
              reqParams.filter = record.value_operator;
            }
            // console.log(row, record, record.value_operator, reqParams, 888)
            return {
              reqParams
            };
          },
          rules: [{ required: true, message: '该项为必填项' }]
        },
        target_value: (row, record = {}) => {
          const { target } = record;
          let type = 'Select';
          let props = {};
          const valueProps = JSON.parse(record.value_props || '{}');

          if (record.value_type === 'range') {
            type = 'RuleCompsRange';
            props = {
              code: target
            };
          } else if (record.value_type === 'number') {
            type = 'InputNumber';
            const unit = valueProps.unit;
            if (unit) {
              props = {
                formatter: (value) => `${value}${unit}`,
                parser: (value) => value.replace(unit, '')
              };
            }
          } else if (record.value_type === 'boolean') {
            props = {
              options: [
                {
                  label: '是',
                  value: 'TRUE'
                },
                {
                  label: '否',
                  value: 'FALSE'
                }
              ]
            };
          } else if (record.value_type === 'string') {
            type = 'Input';
          } else {
            props = {
              url: '/sqlreview/common/item-list/',
              reqParams: {
                enable: 1,
                parent_item_key: target
              }
            };
          }
          return {
            type,
            props: {
              ...(valueProps || {}),
              ...props,
              style: 'width: 100%',
              size: 'default'
            },
            rules: [{ required: true, message: '该项为必填项' }]
          };
        }
      };
    }
  };
  const outputConfig = {
    key: {
      options: [
        {
          label: '审批意见',
          value: 0
        }
      ]
    },
    value: {
      options: [
        {
          label: '拒绝',
          value: 0
        },
        {
          label: '通过',
          value: 1
        }
      ]
    }
  };

  const ruleSetFields = [
    {
      type: 'Select',
      label: '',
      key: 'rule_set',
      // width: '100',
      props: {
        url: '/sqlreview/project/rule_set_all',
        reqParams: { db_type: dbType },
        // placeholder: '请选择规则集',
        mode: 'multiple'
      },
      listeners: {
        change: (value) => {
          // console.log(value, 'ssssssssssss');
        }
      }
    }
  ];
  return {
    baseInfo,
    outportResults,
    condition,
    outputConfig,
    ruleSetFields
  };
}
