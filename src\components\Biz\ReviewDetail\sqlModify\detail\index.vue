<template>
  <a-spin tip="加载中" :spinning="loading">
    <div class="sql-modify-wraper">
      <!-- sql改写模块 -->
      <!-- <SqlRewrite :advice="advice" :info="info" :isShow="isShow"></SqlRewrite> -->
      <SqlRewrite :advice="advice" :isShow="isShow"></SqlRewrite>
    </div>
  </a-spin>
</template>

<script>
import SqlRewrite from './components/SqlRewrite';
import common from '@/utils/common';
import config from './config';
// import { sqlRewrite, sqlAdviceInfo } from '@/api/review';
import { sqlAdviceInfo } from '@/api/review';
// import { sendEmail } from '@/api/home';
export default {
  components: {
    SqlRewrite
  },
  props: {
    isShow: Boolean
  },
  data() {
    this.config = config(this);
    this.id = this.$route.params.id;
    return {
      loading: false,
      advice: [],
      info: {
        ai_message: [],
        card_list: []
      }
    };
  },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      // 获取详情数据
      // this.loading = true;
      // sqlRewrite({
      //   detail_id: this.id
      // })
      //   .then(res => {
      //     console.log(22222)
      //     if (_.get(res, 'data.code') == 0) {
      //       const resData = _.get(res, 'data.data') || {};
      //       this.info = resData;
      //       this.detailId = resData.record_id;
      //       this.reviewId = resData.review_detail_id;
      //       this.setNavi();
      //     } else {
      //       this.$hideLoading({
      //         method: 'error',
      //         tips: _.get(res, 'data.message')
      //       });
      //     }
      //     this.loading = false;
      //   })
      //   .catch(e => {
      //     this.loading = false;
      //     this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
      //   });
      // 获取优化建议数据
      this.getSqlAdviceInfoData();
    },
    getSqlAdviceInfoData() {
      sqlAdviceInfo({ id: this.id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            this.advice = _.get(res, 'data.data') || [];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'home-sqlreview-detail') {
          path = sourcePath.replace(':id', this.detailId);
        }
        if (key === 'home-sqlreview-review') {
          path = sourcePath.replace(':id', this.reviewId);
        }
        if (key === 'orderDetail') {
          path = sourcePath.replace(':id', this.detailId);
        }
        if (key === 'orderReview') {
          path = sourcePath.replace(':id', this.reviewId);
        }
        return path;
      });

      // if (this.id !== this.$route.params.id) {
      //   this.$router.push({
      //     name: 'home-sqlreview-review',
      //     params: { id: this.id, params: this.searchData }
      //   });
      // }
    }
  }
};
</script>

<style lang="less" scoped>
</style>
