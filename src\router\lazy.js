import Vue from 'vue';
import Router from 'vue-router';
import RootView from '@/layouts/RootView';
// import RouteView from '@/layouts/RouteView'
// import MenuView from '@/layouts/MenuView'
import Login from '@/pages/login/Login';
import store from '../store';
import Cookie from 'js-cookie';
import {
  authCheck,
  runAfterAuthCheck,
  ignorePages,
  ignoreAuthCheckPages
} from './config.js';
import { refreshToken, jksRefreshToken, verifyToken } from '@/api/common';
import { setLoginTarget } from '@/utils/login';
// import './authElements';

Vue.use(Router);

const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};

const routes = [
  {
    path: '/login',
    name: 'login',
    desc: '登录页',
    component: Login,
    beforeEnter: (to, from, next) => {
      // 清空token，设置login_target等
      if (from && from.name) {
        sessionStorage.setItem(
          'login_target',
          from.name &&
            ![...ignorePages, ...ignoreAuthCheckPages].includes(from.name)
            ? from.fullPath
            : '/'
        );
      } else {
        setLoginTarget();
      }
      sessionStorage.setItem(
        'lastUserName',
        _.get(store.state.account, 'user.name') || ''
      );
      // 清空user
      store.commit('account/setUser', null);
      Cookie.remove(GLOBAL_CONFIG.TokenKey);
      Cookie.remove(GLOBAL_CONFIG.TokenKey + '_tag');
      next();
    }
  },
  {
    path: '/',
    name: 'root',
    component: RootView,
    redirect: '/data/view',
    children: [
      // ----------- 首页 ------------- //
      // {
      //   path: 'null',
      //   name: 'home',
      //   meta: {
      //     desc: '扫描审核',
      //     icon: 'home'
      //   }
      // },
      {
        path: '/data/view',
        name: 'data-view',
        meta: {
          icon: 'lu-icon-PC',
          desc: '工作台',
          navi: ['data-view'],
          isMenuEntry: true,
          frameType: 'ignorePageTopSpace',
          boxType: 'blank',
          topGroup: 'mine'
        },
        auth: ['admin', 'leader', 'developer', 'dba'],
        component: () => import('@/pages/dataview/DataView.vue')
      },
      {
        path: '/dbacat_chart',
        name: 'dbacat-chart',
        meta: {
          desc: 'DBACAT大盘',
          boxType: 'blank',
          icon: 'ordered-list',
          navi: ['dbacat-chart'],
          isMenuEntry: true,
          frameType: 'ignorePageTopSpace',
          topGroup: 'mine'
        },
        auth: ['admin', 'leader', 'developer', 'dba'],
        component: () => import('@/pages/dbacat_chart/index.vue')
      },

      {
        path: '/dbacat_chart/detail',
        name: 'dbacat-chart-detail',
        meta: {
          desc: '实例详情',
          boxType: 'blank',
          navi: ['dbacat-chart', 'dbacat-chart-detail'],
          // isMenuEntry: true,
          // frameType: 'ignorePageTopSpace',
          // topGroup: 'databaseaudit'
          parent: 'dbacat-chart'
        },
        auth: ['admin', 'leader', 'developer', 'dba'],
        component: () => import('@/pages/dbacat_chart/detail.vue')
      },
      {
        path: '/dbacat',
        name: 'dbacat',
        meta: {
          icon: 'lu-icon-PC',
          desc: 'DBACAT大盘',
          navi: ['dbacat'],
          isMenuEntry: true,
          frameType: 'pageNarrowMargins',
          boxType: 'blank',
          topGroup: 'mine'
        },
        auth: ['admin', 'leader', 'dba'],
        component: () => import('@/pages/dbacat/index.vue')
      },
      {
        path: '/dbacat/detail/:id',
        name: 'dbacat-detail',
        meta: {
          icon: 'lu-icon-PC',
          desc: '实例详情',
          navi: ['dbacat', 'dbacat-detail'],
          parent: 'dbacat',
          // frameType: 'pageNarrowMargins',
          boxType: 'blank'
        },
        auth: ['admin', 'leader', 'dba'],
        component: () => import('@/pages/dbacat/detail/index.vue')
      },

      // {
      //   path: '/home/<USER>',
      //   name: 'home-sqlreview',
      //   meta: {
      //     desc: '代码审核',
      //     icon: 'lu-icon-codereview',
      //     navi: ['home-sqlreview'],
      //     keepAlive: true,
      //     isMenuEntry: true,
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank',
      //     group: 0
      //   },
      //   component: () => import('@/pages/home/<USER>/index/index.vue')
      // },
      // {
      //   path: '/home/<USER>/detail/:id',
      //   name: 'home-sqlreview-detail',
      //   meta: {
      //     desc: '详情',
      //     navi: ['home-sqlreview', 'home-sqlreview-detail'],
      //     parent: 'home-sqlreview',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/detail/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/:id',
      //   name: 'home-sqlreview-review',
      //   meta: {
      //     desc: '评审详情',
      //     autoSetNavis: false,
      //     navi: [
      //       'home-sqlreview',
      //       'home-sqlreview-detail',
      //       'home-sqlreview-review'
      //     ],
      //     parent: 'home-sqlreview',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/review/index.vue')
      // },
      // {
      //   path: '/home/<USER>/plan/:id',
      //   name: 'home-sqlreview-plan',
      //   meta: {
      //     desc: 'SQL改写',
      //     autoSetNavis: false,
      //     navi: [
      //       'home-sqlreview',
      //       'home-sqlreview-detail',
      //       'home-sqlreview-review',
      //       'home-sqlreview-plan'
      //     ],
      //     parent: 'home-sqlreview',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/plan/index.vue')
      // },
      {
        path: 'null',
        name: 'preaudit',
        meta: {
          desc: '事前审核',
          icon: 'lu-icon-codereview',
          navi: ['preaudit'],
          isMenuEntry: true
        }
      },
      {
        path: '/preaudit/project/review',
        name: 'project-review',
        meta: {
          desc: '项目审核',
          icon: 'project',
          navi: ['preaudit', 'project-review'],
          boxType: 'blank',
          frameType: 'pageNarrowMargins',
          isMenuEntry: true,
          keepAlive: true,
          topGroup: 'preaudit'
        },
        component: () => import('@/pages/preaudit/project/index/index.vue')
      },
      {
        path: '/preaudit/project/review/detail/:id',
        name: 'project-review-detail',
        meta: {
          desc: '详情',
          navi: ['project-review', 'project-review-detail'],
          parent: 'project-review',
          // 设置盒子没有默认padding和background
          boxType: 'blank',
          keepAlive: true
        },
        component: () => import('@/pages/preaudit/detail/index.vue')
      },
      {
        path: '/preaudit/project/review/review/:id',
        name: 'project-review-review',
        meta: {
          desc: '评审详情',
          autoSetNavis: false,
          navi: [
            'project-review',
            'project-review-detail',
            'project-review-review'
          ],
          parent: 'project-review',
          // 设置盒子没有默认padding和background
          boxType: 'blank'
        },
        component: () => import('@/pages/preaudit/review/index.vue')
      },
      {
        path: '/preaudit/file/review',
        name: 'file-review',
        meta: {
          desc: '文件审核',
          icon: 'file-search',
          navi: ['preaudit', 'file-review'],
          boxType: 'blank',
          frameType: 'pageNarrowMargins',
          isMenuEntry: true,
          keepAlive: true,
          topGroup: 'preaudit'
        },
        component: () => import('@/pages/preaudit/file/index/index.vue')
      },
      {
        path: '/preaudit/file/review/detail/:id',
        name: 'file-review-detail',
        meta: {
          desc: '详情',
          navi: ['file-review', 'file-review-detail'],
          parent: 'file-review',
          // 设置盒子没有默认padding和background
          boxType: 'blank',
          keepAlive: true,
          trueCompName: 'project-review-detail'
        },
        component: () => import('@/pages/preaudit/detail/index.vue')
      },
      {
        path: '/preaudit/file/review/review/:id',
        name: 'file-review-review',
        meta: {
          desc: '评审详情',
          autoSetNavis: false,
          navi: ['file-review', 'file-review-detail', 'file-review-review'],
          parent: 'file-review',
          // 设置盒子没有默认padding和background
          boxType: 'blank'
        },
        component: () => import('@/pages/preaudit/review/index.vue')
      },
      {
        path: '/preaudit/procedure/review',
        name: 'procedure-review',
        meta: {
          desc: '存储过程审核',
          icon: 'lu-icon-storage',
          boxType: 'blank',
          navi: ['preaudit', 'procedure-review'],
          frameType: 'pageNarrowMargins',
          isMenuEntry: true,
          keepAlive: true,
          topGroup: 'preaudit'
        },
        component: () => import('@/pages/preaudit/procedure/index/index.vue')
      },
      {
        path: '/preaudit/procedure/review/detail/:id',
        name: 'procedure-review-detail',
        meta: {
          desc: '详情',
          navi: ['procedure-review', 'procedure-review-detail'],
          parent: 'procedure-review',
          // 设置盒子没有默认padding和background
          boxType: 'blank',
          keepAlive: true,
          trueCompName: 'project-review-detail'
        },
        component: () => import('@/pages/preaudit/detail/index.vue')
      },
      {
        path: '/preaudit/procedure/review/review/:id',
        name: 'procedure-review-review',
        meta: {
          desc: '评审详情',
          autoSetNavis: false,
          navi: [
            'procedure-review',
            'procedure-review-detail',
            'procedure-review-review'
          ],
          parent: 'procedure-review',
          // 设置盒子没有默认padding和background
          boxType: 'blank'
        },
        component: () => import('@/pages/preaudit/review/index.vue')
      },
      // {
      //   path: 'null',
      //   name: 'home',
      //   meta: {
      //     desc: '事前审核',
      //     icon: 'lu-icon-codereview',
      //     navi: ['home-sqlreview'],
      //     isMenuEntry: true,
      //     group: 0
      //   }
      // },
      // {
      //   path: '/home/<USER>/review',
      //   name: 'code-review',
      //   meta: {
      //     desc: '代码审核',
      //     boxType: 'blank',
      //     navi: ['home', 'code-review'],
      //     isMenuEntry: true,
      //     keepAlive: true
      //   },
      //   component: () => import('@/pages/home/<USER>/index/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/detail/:id',
      //   name: 'code-review-detail',
      //   meta: {
      //     desc: '详情',
      //     navi: ['code-review', 'code-review-detail'],
      //     parent: 'code-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/detail/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/review/:id',
      //   name: 'code-review-review',
      //   meta: {
      //     desc: '评审详情',
      //     autoSetNavis: false,
      //     navi: [
      //       'code-review', 'code-review-detail', 'code-review-review'
      //     ],
      //     parent: 'code-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/review/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/plan/:id',
      //   name: 'code-review-plan',
      //   meta: {
      //     desc: 'SQL改写',
      //     autoSetNavis: false,
      //     navi: ['code-review', 'code-review-detail', 'code-review-review', 'code-review-plan'],
      //     parent: 'code-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/plan/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review',
      //   name: 'itsm-review',
      //   meta: {
      //     desc: 'ITSM单审核',
      //     boxType: 'blank',
      //     navi: ['home', 'itsm-review'],
      //     isMenuEntry: true,
      //     keepAlive: true
      //   },
      //   component: () => import('@/pages/home/<USER>/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/detail/:id',
      //   name: 'itsm-review-detail',
      //   meta: {
      //     desc: '详情',
      //     navi: ['itsm-review', 'itsm-review-detail'],
      //     parent: 'itsm-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/detail/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/review/:id',
      //   name: 'itsm-review-review',
      //   meta: {
      //     desc: '评审详情',
      //     autoSetNavis: false,
      //     navi: [
      //       'itsm-review', 'itsm-review-detail', 'itsm-review-review'
      //     ],
      //     parent: 'itsm-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/review/index.vue')
      // },
      // {
      //   path: '/home/<USER>/cq/review',
      //   name: 'efficiency-cq-review',
      //   meta: {
      //     desc: '效率云/CQ单审核',
      //     boxType: 'blank',
      //     navi: ['home', 'efficiency-cq-review'],
      //     isMenuEntry: true,
      //     keepAlive: true
      //   },
      //   component: () => import('@/pages/home/<USER>/index.vue')
      // },
      // {
      //   path: '/home/<USER>/cq/review/detail/:id',
      //   name: 'efficiency-cq-review-detail',
      //   meta: {
      //     desc: '详情',
      //     navi: ['efficiency-cq-review', 'efficiency-cq-review-detail'],
      //     parent: 'efficiency-cq-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/detail/index.vue')
      // },
      // {
      //   path: '/home/<USER>/cq/review/review/:id',
      //   name: 'efficiency-cq-review-review',
      //   meta: {
      //     desc: '评审详情',
      //     autoSetNavis: false,
      //     navi: [
      //       'efficiency-cq-review', 'efficiency-cq-review-detail', 'efficiency-cq-review-review'
      //     ],
      //     parent: 'efficiency-cq-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/review/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review',
      //   name: 'cc-review',
      //   meta: {
      //     desc: 'CC单审核',
      //     boxType: 'blank',
      //     navi: ['home', 'cc-review'],
      //     isMenuEntry: true,
      //     keepAlive: true
      //   },
      //   component: () => import('@/pages/home/<USER>/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/detail/:id',
      //   name: 'cc-review-detail',
      //   meta: {
      //     desc: '详情',
      //     navi: ['cc-review', 'cc-review-detail'],
      //     parent: 'cc-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/detail/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/review/:id',
      //   name: 'cc-review-review',
      //   meta: {
      //     desc: '评审详情',
      //     autoSetNavis: false,
      //     navi: [
      //       'cc-review', 'cc-review-detail', 'cc-review-review'
      //     ],
      //     parent: 'cc-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/review/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review',
      //   name: 'timing-review',
      //   meta: {
      //     desc: '定时审核',
      //     boxType: 'blank',
      //     navi: ['home', 'timing-review'],
      //     isMenuEntry: true,
      //     keepAlive: true
      //   },
      //   component: () => import('@/pages/home/<USER>/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/tasklist/:id',
      //   name: 'timing-review-tasklist',
      //   meta: {
      //     desc: '任务列表',
      //     navi: ['timing-review', 'timing-review-tasklist'],
      //     parent: 'timing-review',
      //     boxType: 'blank',
      //     keepAlive: true
      //   },
      //   component: () => import('@/pages/home/<USER>/tasklist/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/detail/:id',
      //   name: 'timing-review-detail',
      //   meta: {
      //     desc: '详情',
      //     navi: ['timing-review', 'timing-review-tasklist', 'timing-review-detail'],
      //     parent: 'timing-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/detail/index.vue')
      // },
      // {
      //   path: '/home/<USER>/review/review/:id',
      //   name: 'timing-review-review',
      //   meta: {
      //     desc: '评审详情',
      //     autoSetNavis: false,
      //     navi: [
      //       'timing-review', 'timing-review-tasklist', 'timing-review-detail', 'timing-review-review'
      //     ],
      //     parent: 'timing-review',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/review/index.vue')
      // },
      // {
      //   path: '/home/<USER>/rewrite',
      //   name: 'home-sqlreview-rewrite',
      //   meta: {
      //     desc: '智能改写',
      //     autoSetNavis: false,
      //     navi: [
      //       'home-sqlreview',
      //       'home-sqlreview-detail',
      //       'home-sqlreview-review',
      //       'home-sqlreview-rewrite'
      //     ],
      //     parent: 'home-sqlreview',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/home/<USER>/rewrite/index.vue')
      // },
      // {
      //   path: '/immediate',
      //   name: 'immediate',
      //   meta: {
      //     desc: '实时审核',
      //     icon: 'sync',
      //     navi: ['immediate']
      //   },
      //   component: () => import('@/pages/immediate/index/index.vue')
      // },
      // {
      //   path: '/immediate/report',
      //   name: 'immediateReport',
      //   meta: {
      //     desc: '查看报告',
      //     autoSetNavis: false,
      //     navi: ['home-postaudit', 'immediateReport'],
      //     parent: 'home-postaudit',
      //     keepAlive: true
      //   },
      //   component: () => import('@/pages/immediate/report/index.vue')
      // },
      // {
      //   path: '/immediate/report/plan',
      //   name: 'immediateReportPlan',
      //   meta: {
      //     desc: '详情',
      //     autoSetNavis: false,
      //     navi: ['home-postaudit', 'immediateReport', 'immediateReportPlan'],
      //     parent: 'home-postaudit'
      //   },
      //   component: () => import('@/pages/immediate/plan/index.vue')
      // },
      {
        path: '/home/<USER>',
        name: 'home-procedure',
        meta: {
          desc: '存储过程扫描',
          icon: 'lu-icon-storage',
          navi: ['home-procedure'],
          isMenuEntry: true
        },
        component: () => import('@/pages/home/<USER>/index/index.vue')
      },
      {
        path: '/home/<USER>/detail/:id',
        name: 'home-procedure-detail',
        meta: {
          desc: '详情',
          navi: ['home-procedure', 'home-procedure-detail'],
          parent: 'home-procedure'
        },
        component: () => import('@/pages/home/<USER>/detail/index.vue')
      },
      {
        path: '/home/<USER>/review/:id',
        name: 'home-procedure-review',
        meta: {
          desc: '评审详情',
          navi: [
            'home-procedure',
            'home-procedure-detail',
            'home-procedure-review'
          ],
          parent: 'home-procedure',
          // 设置盒子没有默认padding和background
          boxType: 'blank'
        },
        component: () => import('@/pages/home/<USER>/review/index.vue')
      },
      // 白名单管理
      {
        path: '/white/list',
        name: 'white-list',
        meta: {
          desc: '白名单管理',
          icon: 'lu-icon-whitelist',
          navi: ['white-list'],
          boxType: 'blank',
          isMenuEntry: true,
          frameType: 'pageNarrowMargins',
          topGroup: 'tools',
          keepAlive: true
        },
        component: () => import('@/pages/whiteList/index.vue')
      },
      {
        path: '/white/list/detail/:id',
        name: 'white-list-detail',
        meta: {
          desc: '详情',
          navi: ['white-list', 'white-list-detail'],
          parent: 'white-list',
          boxType: 'blank'
        },
        component: () => import('@/pages/whiteList/detail/index.vue')
      },
      // 数据库审核
      {
        path: 'null',
        name: 'database-audit',
        meta: {
          desc: '数据库审核',
          icon: 'lu-icon-dbreview',
          navi: ['database-audit'],
          isMenuEntry: true
        }
      },
      {
        path: '/database/topsql',
        name: 'top-sql',
        meta: {
          desc: 'TOP SQL',
          boxType: 'blank',
          icon: 'bar-chart',
          navi: ['database-audit', 'top-sql'],
          isMenuEntry: true,
          frameType: 'ignorePageTopSpace',
          topGroup: 'databaseaudit'
          // keepAlive: true
        },
        component: () => import('@/pages/databaseaudit/topsql/index.vue')
      },
      {
        path: '/database/topsqlNew',
        name: 'top-sql-new',
        meta: {
          desc: 'TOP SQL',
          boxType: 'blank',
          icon: 'bar-chart',
          navi: ['database-audit', 'top-sql-new'],
          isMenuEntry: true,
          frameType: 'ignorePageTopSpace',
          topGroup: 'databaseaudit'
          // keepAlive: true
        },
        component: () => import('@/pages/databaseaudit/topsqlNew/index.vue')
      },
      {
        path: '/database/topsqlTemp',
        name: 'top-sql-temp',
        meta: {
          desc: 'TOP SQL',
          boxType: 'blank',
          icon: 'bar-chart',
          navi: ['database-audit', 'top-sql-temp'],
          isMenuEntry: true,
          frameType: 'ignorePageTopSpace',
          topGroup: 'databaseaudit'
          // keepAlive: true
        },
        component: () => import('@/pages/databaseaudit/topsqlaTemp/index.vue')
      },
      {
        path: '/database/realtime',
        name: 'real-time',
        meta: {
          desc: 'Real-Time SQL',
          icon: 'thunderbolt',
          boxType: 'blank',
          navi: ['database-audit', 'real-time'],
          isMenuEntry: true,
          frameType: 'ignorePageTopSpace',
          topGroup: 'databaseaudit'
          // keepAlive: true
        },
        component: () => import('@/pages/databaseaudit/realtime/index.vue')
      },
      {
        path: '/database/slowloganalyze',
        name: 'slow-log-analyze',
        meta: {
          desc: 'Slow Log Analyze',
          icon: 'monitor',
          boxType: 'blank',
          navi: ['database-audit', 'slow-log-analyze'],
          isMenuEntry: true,
          frameType: 'ignorePageTopSpace',
          topGroup: 'databaseaudit'
        },
        component: () =>
          import('@/pages/databaseaudit/slowloganalyze/index.vue')
      },
      {
        path: '/database/monitoring',
        name: 'monitoring',
        meta: {
          desc: '数据库对象监控',
          icon: 'video-camera',
          boxType: 'blank',
          navi: ['database-audit', 'monitoring'],
          isMenuEntry: true,
          frameType: 'pageNarrowMargins',
          topGroup: 'databaseaudit'
          // jumpUrl: () => {
          //   const url = _.get(store.state.project, 'dsmUrl');
          //   return url;
          // }
        },
        component: () => import('@/pages/databaseaudit/monitoring/index.vue')
      },
      // ----------- 快速审核 ------------- //
      {
        path: '/quickaudit',
        name: 'quickAudit',
        meta: {
          desc: '快速审核',
          icon: 'lu-icon-qreview',
          navi: ['quickAudit'],
          isMenuEntry: true,
          boxType: 'blank',
          frameType: 'pageNarrowMargins',
          topGroup: 'preaudit'
        },
        component: () => import('@/pages/quickaudit/index/index.vue')
      },
      // {
      //   path: '/quickaudit',
      //   name: 'quickAudit-upload',
      //   meta: {
      //     desc: '文件上传',
      //     navi: ['quickAudit', 'quickAudit-upload']
      //   },
      //   component: () => import('@/pages/quickaudit/index/index.vue')
      // },
      {
        path: '/quickaudit/detail',
        name: 'quickAuditDetail',
        meta: {
          desc: '详情',
          navi: ['quickAudit', 'quickAuditDetail'],
          parent: 'quickAudit'
        },
        component: () => import('@/pages/quickaudit/detail/index.vue')
      },
      {
        path: '/quickaudit/history',
        name: 'quickAuditHistory',
        meta: {
          desc: '历史记录',
          navi: ['quickAudit', 'quickAuditHistory'],
          boxType: 'blank',
          parent: 'quickAudit'
        },
        component: () => import('@/pages/quickaudit/history/index.vue')
      },
      // ----------- 系统管理 ------------- //
      {
        path: 'null',
        name: 'system',
        meta: {
          desc: '系统配置',
          icon: 'lu-icon-PC'
        }
      },
      {
        path: '/system/config',
        name: 'system-config',
        meta: {
          desc: '系统参数',
          navi: ['system', 'system-config'],
          isMenuEntry: true
        },
        auth: ['admin'],
        component: () => import('@/pages/system/system/index.vue')
      },
      {
        path: '/system/schedulejob',
        name: 'schedule-job',
        meta: {
          desc: '调度任务',
          boxType: 'blank',
          navi: ['system', 'schedule-job'],
          isMenuEntry: true,
          keepAlive: true
        },
        component: () => import('@/pages/system/schedulejob/index.vue')
      },
      {
        path: '/system/schedulejob/detail',
        name: 'schedule-job-detail',
        meta: {
          desc: '详情',
          boxType: 'blank',
          parent: 'schedule-job',
          navi: ['system', 'schedule-job', 'schedule-job-detail']
        },
        component: () => import('@/pages/system/schedulejob/detail/index.vue')
      },
      {
        path: '/system/resource/edit',
        name: 'resource-edit',
        meta: {
          desc: '资源配置',
          navi: ['system', 'resource-edit'],
          isMenuEntry: true
        },
        component: () => import('@/pages/system/resource/edit/index.vue')
      },
      {
        path: '/audit/log',
        name: 'auditLog',
        meta: {
          // icon: 'lu-icon-log',
          desc: '审计日志',
          navi: ['system', 'auditLog'],
          isMenuEntry: true
        },
        auth: ['admin', 'dba'],
        component: () => import('@/pages/auditlog/index.vue')
      },
      {
        path: '/week/report',
        name: 'weekReport',
        meta: {
          // icon: 'lu-icon-log',
          desc: '审核周报',
          navi: ['system', 'weekReport'],
          isMenuEntry: true
        },
        auth: ['admin', 'dba'],
        component: () => import('@/pages/system/weekReport/index.vue')
      },
      // ----------- 数据源配置 ------------- //
      {
        path: 'null',
        name: 'data-source-config',
        meta: {
          desc: '数据源配置',
          icon: 'lu-icon-PC'
        }
      },
      {
        path: '/data-source-config/data-source',
        name: 'data-source',
        meta: {
          desc: '数据源',
          navi: ['data-source-config', 'data-source'],
          // frameType: 'pageNarrowMargins',
          keepAlive: true,
          isMenuEntry: true
        },
        auth: ['admin', 'dba'],
        component: () => import('@/pages/datasource/dataSource/index.vue')
      },
      {
        path: '/data-source-config/data-source-new',
        name: 'data-source-new',
        meta: {
          desc: '数据源',
          navi: ['data-source-config', 'data-source-new'],
          // frameType: 'pageNarrowMargins',
          keepAlive: true,
          isMenuEntry: true
        },
        auth: ['admin', 'dba'],
        component: () => import('@/pages/datasource/dataSourceNew/index.vue')
      },
      {
        path: '/data-source-config/table-detail',
        name: 'table-detail',
        meta: {
          desc: '表列表',
          navi: ['data-source-config', 'data-source', 'table-detail'],
          parent: 'data-source',
          autoSetNavis: false
        },
        auth: ['admin', 'dba'],
        component: () =>
          import('@/pages/datasource/dataSource/tableDetail/index.vue')
      },
      {
        path: '/data-source-config/instance-import',
        name: 'instance-import',
        meta: {
          desc: '数据源导入',
          navi: ['data-source-config', 'data-source', 'instance-import'],
          parent: 'data-source'
        },
        component: () =>
          import('@/pages/datasource/dataSource/instanceImport/index.vue')
      },
      {
        path: '/data-source-config/import-and-export',
        name: 'import-and-export',
        meta: {
          desc: '统计信息导入/导出',
          navi: ['data-source-config', 'data-source', 'import-and-export'],
          parent: 'data-source'
        },
        component: () =>
          import('@/pages/datasource/dataSource/importAndExport/index.vue')
      },
      {
        path: '/data-source-config/audit-config',
        name: 'audit-config',
        meta: {
          desc: '数据库审核配置',
          boxType: 'blank',
          navi: ['data-source-config', 'audit-config'],
          isMenuEntry: true
        },
        component: () => import('@/pages/datasource/auditconfig/index.vue')
      },
      {
        path: '/data-source-config/audit-config-new',
        name: 'audit-config-new',
        meta: {
          desc: '数据库审核配置',
          boxType: 'blank',
          navi: ['data-source-config', 'audit-config-new'],
          isMenuEntry: true
        },
        component: () => import('@/pages/datasource/auditconfigNew/index.vue')
      },
      {
        path: '/data-source-config/audit-config/detail',
        name: 'audit-config-detail',
        meta: {
          desc: '详情',
          boxType: 'blank',
          navi: ['data-source-config', 'audit-config', 'audit-config-detail'],
          parent: 'audit-config'
        },
        component: () =>
          import('@/pages/datasource/auditconfig/detail/index.vue')
      },
      // ----------- 项目配置 ------------- //
      {
        path: 'null',
        name: 'config',
        meta: {
          desc: '项目配置',
          icon: 'lu-icon-config1'
        }
      },
      {
        path: '/config/project',
        name: 'project-config',
        meta: {
          desc: '项目',
          navi: ['config', 'project-config'],
          isMenuEntry: true
        },
        auth: ['admin', 'dba'],
        component: () => import('@/pages/config/project/index.vue')
      },
      {
        path: '/config/project-group',
        name: 'project-group-config',
        meta: {
          desc: '项目组',
          navi: ['config', 'project-group-config'],
          isMenuEntry: true
        },
        auth: ['admin', 'dba'],
        component: () => import('@/pages/config/projectGroup/index.vue')
      },
      {
        path: '/config/project-import',
        name: 'project-import-config',
        meta: {
          desc: '项目导入',
          navi: ['config', 'project-config', 'project-import-config'],
          parent: 'project-config'
        },
        component: () =>
          import('@/pages/config/project/projectImport/index.vue')
      },
      {
        path: '/config/project-group-import',
        name: 'project-group-import-config',
        meta: {
          desc: '项目组导入',
          navi: [
            'config',
            'project-group-config',
            'project-group-import-config'
          ],
          parent: 'project-group-config'
        },
        component: () =>
          import('@/pages/config/projectGroup/projectGroupImport/index.vue')
      },
      // {
      //   path: '/config/white-list',
      //   name: 'white-list',
      //   meta: {
      //     desc: '白名单管理',
      //     navi: ['config', 'white-list']
      //   },
      //   component: () => import('@/pages/config/whiteList/index.vue')
      // },
      // ----------- 规则管理 ------------- //
      {
        path: 'null',
        name: 'rule-conf',
        meta: {
          desc: '审核配置',
          icon: 'lu-icon-rule'
        }
      },
      {
        path: '/config/rules',
        name: 'rules-config',
        meta: {
          desc: '规则',
          boxType: 'blank',
          navi: ['rule-conf', 'rules-config'],
          isMenuEntry: true,
          keepAlive: true
        },
        auth: ['admin', 'dba'],
        component: () => import('@/pages/rules/rules/index/index.vue')
      },
      {
        path: '/config/rules/add',
        name: 'rules-config-add',
        meta: {
          desc: '新增规则',
          boxType: 'blank',
          navi: ['rule-conf', 'rules-config', 'rules-config-add'],
          parent: 'rules-config'
        },
        component: () => import('@/pages/rules/rules/add/index.vue')
      },
      {
        path: '/config/rules/edit/',
        name: 'rules-config-edit',
        meta: {
          desc: '规则修改',
          boxType: 'blank',
          navi: ['rule-conf', 'rules-config', 'rules-config-edit'],
          parent: 'rules-config'
        },
        component: () => import('@/pages/rules/rules/edit/index.vue')
      },
      {
        path: '/config/rules/detail/',
        name: 'rules-config-detail',
        meta: {
          desc: '规则查看',
          boxType: 'blank',
          navi: ['rule-conf', 'rules-config', 'rules-config-detail'],
          parent: 'rules-config'
        },
        component: () => import('@/pages/rules/rules/detail/index.vue')
      },
      {
        path: '/config/ruleGather',
        name: 'ruleGather-config',
        meta: {
          desc: '规则集',
          boxType: 'blank',
          navi: ['rule-conf', 'ruleGather-config'],
          isMenuEntry: true,
          keepAlive: true
        },
        auth: ['admin', 'dba'],
        component: () => import('@/pages/rules/ruleGather/index/index.vue')
      },
      {
        path: '/config/ruleGather/add',
        name: 'ruleGather-config-add',
        meta: {
          desc: '新建规则集',
          boxType: 'blank',
          navi: ['rule-conf', 'ruleGather-config', 'ruleGather-config-add'],
          parent: 'ruleGather-config'
        },
        component: () => import('@/pages/rules/ruleGather/add/index.vue')
      },
      {
        path: '/config/ruleGather/detail/:id',
        name: 'ruleGather-config-detail',
        meta: {
          desc: '规则集查看',
          boxType: 'blank',
          navi: ['rule-conf', 'ruleGather-config', 'ruleGather-config-detail'],
          parent: 'ruleGather-config'
        },
        component: () => import('@/pages/rules/ruleGather/edit/index.vue')
      },
      {
        path: '/config/ruleGather/edit/:id',
        name: 'ruleGather-config-edit',
        meta: {
          desc: '规则集修改',
          boxType: 'blank',
          navi: ['rule-conf', 'ruleGather-config', 'ruleGather-config-edit'],
          parent: 'ruleGather-config'
        },
        component: () => import('@/pages/rules/ruleGather/edit/index.vue')
      },
      {
        path: '/config/comments',
        name: 'comments-config',
        meta: {
          desc: '评审意见',
          navi: ['rule-conf', 'comments-config'],
          isMenuEntry: true
        },
        auth: ['admin', 'dba'],
        component: () => import('@/pages/config/comments/index.vue')
      },
      {
        path: '/config/agent',
        name: 'agent-config',
        meta: {
          desc: 'agent管理',
          boxType: 'blank',
          navi: ['rule-conf', 'agent-config'],
          isMenuEntry: true,
          keepAlive: true
        },
        component: () => import('@/pages/agent/index.vue')
      },
      // {
      //   path: '/config/ldap',
      //   name: 'system-ldap',
      //   meta: {
      //     desc: 'LDAP认证',
      //     navi: ['config', 'system-ldap']
      //   },
      //   auth: ['admin', 'dba'],
      //   component: () => import('@/pages/config/ldap/index.vue')
      // },
      // ----------- 用户中心 ----------- //
      {
        path: 'null',
        name: 'user',
        meta: {
          desc: '用户管理',
          icon: 'lu-icon-user1'
        }
      },
      {
        path: '/user/setting',
        name: 'userList',
        meta: {
          desc: '用户',
          navi: ['user', 'userList'],
          isMenuEntry: true
        },
        auth: ['admin'],
        component: () => import('@/pages/user/index/index.vue')
      },
      {
        path: '/user/group',
        name: 'groupList',
        meta: {
          desc: '用户组设置',
          navi: ['user', 'groupList'],
          isMenuEntry: true
        },
        auth: ['admin'],
        component: () => import('@/pages/user/group/index.vue')
      },
      {
        path: '/user/role',
        name: 'roleList',
        meta: {
          desc: '角色资源权限',
          navi: ['user', 'roleList'],
          isMenuEntry: true
        },
        component: () => import('@/pages/user/role/index.vue')
      },
      {
        path: '/user/role/add',
        name: 'roleAdd',
        meta: {
          desc: '角色新增',
          navi: ['user', 'roleList', 'roleAdd'],
          parent: 'roleList',
          // 设置盒子没有默认padding和background
          boxType: 'blank'
        },
        component: () => import('@/pages/user/role/operate/add/index.vue')
      },
      {
        path: '/user/role/edit/:id',
        name: 'roleEdit',
        meta: {
          desc: '角色编辑',
          navi: ['user', 'roleList', 'roleEdit'],
          parent: 'roleList',
          // 设置盒子没有默认padding和background
          boxType: 'blank'
        },
        component: () => import('@/pages/user/role/operate/edit/index.vue')
      },
      // {
      //   path: '/resource/edit/',
      //   name: 'resourceEdit',
      //   meta: {
      //     desc: '资源配置',
      //     navi: ['user', 'resourceEdit'],
      //     isMenuEntry: true
      //   },
      //   component: () => import('@/pages/user/resource/edit/index.vue')
      // },
      // ----------- 工单管理 ------------- //
      {
        path: '/order/list',
        name: 'orderList',
        meta: {
          icon: 'lu-icon-order',
          desc: '工单管理',
          navi: ['orderList'],
          keepAlive: true,
          isMenuEntry: true,
          frameType: 'pageNarrowMargins',
          // 设置盒子没有默认padding和background
          boxType: 'blank',
          topGroup: 'mine'
        },
        component: () => import('@/pages/order/index/index.vue')
      },
      {
        path: '/order/detail/:id',
        name: 'orderDetail',
        meta: {
          desc: '详情',
          navi: ['orderList', 'orderDetail'],
          parent: 'orderList',
          boxType: 'blank',
          keepAlive: true
        },
        component: () => import('@/pages/order/detail/index.vue')
      },
      {
        path: '/order/review/:id',
        name: 'orderReview',
        meta: {
          desc: '评审详情',
          autoSetNavis: false,
          navi: ['orderList', 'orderDetail', 'orderReview'],
          parent: 'orderList',
          // 设置盒子没有默认padding和background
          boxType: 'blank'
        },
        component: () => import('@/pages/order/review/index.vue')
      },
      // {
      //   path: '/order/plan/:id',
      //   name: 'orderPlan',
      //   meta: {
      //     desc: 'SQL改写',
      //     autoSetNavis: false,
      //     navi: ['orderList', 'orderDetail', 'orderReview', 'orderPlan'],
      //     parent: 'orderList',
      //     // 设置盒子没有默认padding和background
      //     boxType: 'blank'
      //   },
      //   component: () => import('@/pages/order/plan/index.vue')
      // },
      // 我的应用
      {
        path: '/application',
        name: 'application',
        meta: {
          icon: 'lu-icon-apps',
          desc: '我的项目',
          navi: ['application'],
          boxType: 'blank',
          isMenuEntry: true,
          frameType: 'pageNarrowMargins',
          topGroup: 'mine'
        },
        component: () => import('@/pages/application/index.vue')
      },
      // ----------- 报表统计 ------------- //
      // {
      //   path: '/report',
      //   name: 'report',
      //   meta: {
      //     icon: 'pie-chart',
      //     desc: '报表统计',
      //     navi: ['report'],
      //     isMenuEntry: true,
      //     frameType: 'ignorePageTopSpace',
      //     boxType: 'blank',
      //     group: 0
      //   },
      //   auth: ['admin', 'dba'],
      //   component: () => import('@/pages/report/index/index.vue')
      // },
      // {
      //   path: '/report/detail',
      //   name: 'reportDetail',
      //   meta: {
      //     desc: '详情',
      //     navi: ['report', 'reportDetail'],
      //     parent: 'report'
      //   },
      //   auth: ['admin', 'dba'],
      //   component: () => import('@/pages/report/detail/index.vue')
      // },
      // ----------- sql编辑器 ------------- //
      {
        path: '/sqlresolver',
        name: 'sqlresolver',
        meta: {
          // desc: 'SQL解析器',
          icon: 'lu-icon-sqledit',
          desc: 'SQL编辑器',
          navi: ['sqlresolver'],
          isMenuEntry: true,
          isQuick: true,
          // layout: 'up-down',
          // 设置盒子没有默认padding和background
          boxType: 'blank',
          frameType: 'fullsize',
          keepAlive: true,
          topGroup: 'tools',
          jumpUrl: () => {
            const url = _.get(store.state.project, 'sqlresolverUrl');
            if (!url) return;
            // const baseUrl = 'https://opstools-prd-ops-db-sqlplus.lujs.net/';
            const LuTokenKey = Cookie.get(GLOBAL_CONFIG.LuTokenKey);
            const search = [];
            if (LuTokenKey) {
              search.push(`_token=${LuTokenKey}`);
            }
            // search.push(`backUrl=${location.origin + location.search}`)
            return url + `?${search.join('&')}`;
          }
        },
        component: () => import('@/pages/sqlresolver/index.vue'),
        children: []
      },
      // ----------- 帮助页面 ----------- //
      {
        path: '/help',
        name: 'help',
        meta: {
          desc: '帮助页',
          navi: ['home', 'help']
        },
        component: () => import('@/pages/help/index.vue')
      },
      // ----------- 异常页 ------------- //
      {
        path: '/exception/:status?',
        name: 'exception',
        meta: {
          desc: '异常页',
          // 设置盒子没有默认padding和background
          boxType: 'blank'
        },
        component: () => import('@/pages/exception')
      },
      // ----------- 优化建议指令页 ------------- //
      {
        path: '/optimizeDirective',
        name: 'optimizeDirective',
        meta: {
          desc: '优化建议指令页',
          boxType: 'blank'
        },
        component: () => import('@/pages/optimizeDirective/index.vue')
      }
    ]
  },
  {
    path: '*',
    component: () => import('@/pages/exception'),
    redirect: '/data/view'
  }
];

// 初始化处理routes
let routesMap = {};
const init = function() {
  // 递归处理
  function deal(list) {
    list.forEach(item => {
      routesMap[item.name] = item;

      if (item.children && item.children.length > 0) {
        deal(item.children);
      }
    });
  }
  deal(routes);
};
init();
window.routesMap = routesMap;

const router = new Router({
  routes
});

// 切换路由时，清空请求
window._axiosPromiseArr = [];
router.beforeEach((to, from, next) => {
  window._axiosPromiseArr.forEach((ele, index) => {
    ele.cancel();
    delete window._axiosPromiseArr[index];
  });
  window._axiosPromiseArr = [];
  // 打开弹窗/抽屉 然后从弹窗/抽屉跳转路由，返回后是否打开弹窗/抽屉
  if (_.get(from, 'meta.keepAlive')) {
    document.body
      .querySelectorAll('.ant-modal-root > .ant-modal-wrap')
      .forEach(node => {
        if (node && node.style.display !== 'none') {
          node.parentNode.style.display = 'none';
          node.parentNode.setAttribute('wait-open', from.name);
        }
      });
    document.body
      .querySelectorAll('.ant-drawer.ant-drawer-open')
      .forEach(node => {
        if (node && node.style.display !== 'none') {
          node.style.display = 'none';
          node.setAttribute('wait-open', from.name);
        }
      });
  }
  if (_.get(to, 'meta.keepAlive')) {
    document.body
      .querySelectorAll(`.ant-modal-root[wait-open=${to.name}]`)
      .forEach(node => {
        node.style.display = 'block';
        node.removeAttribute('wait-open');
      });
    document.body
      .querySelectorAll(`.ant-drawer[wait-open=${to.name}]`)
      .forEach(node => {
        node.style.display = 'block';
        node.removeAttribute('wait-open');
      });
  }

  let userName = _.get(store.state.account, 'user.name');
  const auth = store.state.auth || {};
  const authSource = auth.source;
  // 页面权限判断（非刷新）
  if (userName != null && authSource != null) {
    // if (!pageAuthCheck()) {
    //   router.push({ path: '/exception/403?desc=抱歉，你没有权限操作' });
    //   return;
    // } else if (!virtualLoginAuthCheck(store, to.name)) {
    //   router.push({ path: '/exception/403?desc=抱歉，你没有权限操作' });
    //   return;
    // }
    const authParams = { store, to, router, routesMap, pages: auth.pages };
    if (authCheck(authParams) === false) {
      runAfterAuthCheck(authParams);
      return;
    }
  }
  // 清空message
  if (to.name === 'login') {
    store.commit('auth/setAuth', null);
    setTimeout(() => {
      Vue.prototype.$message.destroy();
      Vue.prototype.$notification.destroy();
    });
  }
  // 清空全屏元素
  const fullscreenNode = document.body.querySelector('.fullscreen');
  fullscreenNode && document.body.removeChild(fullscreenNode);
  // 处理缓存
  let toNavi = _.get(to, 'meta.navi');
  let fromNavi = _.get(from, 'meta.navi');
  toNavi = toNavi === true ? [to.name] : toNavi || [];
  fromNavi = fromNavi === true ? [from.name] : fromNavi || [];
  let toNaviString = toNavi.join(',');
  let fromNaviString = fromNavi.join(',');

  // console.log(toNavi, toNaviString, fromNavi, fromNaviString);
  if (toNavi.length > fromNavi.length) {
    if (!toNaviString.startsWith(fromNaviString)) {
      // 清空
      store.commit('common/clearCache');
    }
  } else if (toNavi.length == fromNavi.length) {
    if (to.name !== from.name) {
      // 清空
      store.commit('common/clearCache');
    }
  } else {
    if (!fromNaviString.startsWith(toNaviString)) {
      // 清空
      store.commit('common/clearCache');
    } else {
      // 清空中间
      store.commit('common/clearCacheByKeys', fromNavi.slice(toNavi.length));
    }
  }
  // 获取用户权限 + 用户信息
  // 目前没有完整权限系统，只用作进入系统前获取用户信息，并判断去到指定页面
  // console.log(from.name, ignorePages, to.name, userName, 89898);
  if (
    userName == null &&
    authSource == null &&
    (from.name === 'login' || !ignorePages.includes(to.name))
  ) {
    // console.log('初始化获取用户权限');
    // 请求

    let resStr = CommonUtil.getQueryParams();

    // 第三方登录跳转回来url里面会带cookie过来
    // 第一次储存校验成功后，地址栏再次输入网址，可不用带_token，直接用缓存
    // 缓存了_token后 在地址栏删除token 以防复制地址发送给他人后能直接使用，而非去三方登录

    // 普惠rul => ?_token=xxxx&...#/data/view
    let { _token, code } = resStr;
    if (_token) {
      Cookie.set(GLOBAL_CONFIG.LuTokenKey, _token);
      let searchStr = location.search
        .slice(1)
        .split('&')
        .filter(item => item && !item.startsWith('_token='))
        .join('&');
      searchStr = searchStr ? `?${searchStr}` : searchStr;
      let redirectUrl =
        location.origin + location.pathname + searchStr + location.hash;
      location.href = redirectUrl;
      return;
    }
    // 碧桂园url => #/data/view?code=xxxx&...
    if (code) {
      Cookie.set(GLOBAL_CONFIG.LuTokenKey, code);
      let hashStr = location.hash.replace(`code=${code}&`, '');
      let redirectUrl =
        location.origin + location.pathname + location.search + hashStr;
      location.href = redirectUrl;
      location.reload();
      return;
    }
    // 免密登录进入 在免密页面点击登录成功后
    // 再从免密登录进入，放开权限，不用再点击登录
    if (resStr.user === 'jksuser') {
      verifyToken()
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const bool = _.get(res, 'data.data.verify');
            let redirectUrl =
              location.origin + location.pathname + location.hash;
            bool && location.replace(redirectUrl);
          } else {
            // do nothing
          }
        })
        .catch(e => {
          console.error(e);
        });
    }
    const tokenReq = () => {
      if (resStr.user === 'jksuser') {
        return jksRefreshToken({ user: 'jksuser' });
      } else {
        if (Cookie.get(GLOBAL_CONFIG.TokenKey + '_tag') === 'jksuser') {
          // 清空user
          // store.commit('account/setUser', null);
          Cookie.remove(GLOBAL_CONFIG.TokenKey);
          Cookie.remove(GLOBAL_CONFIG.TokenKey + '_tag');
        }
        return refreshToken();
      }
    };

    tokenReq()
      .then(res => {
        const code = _.get(res, 'data.code');
        if (code == 0) {
          if (_.get(res, 'data.data.lu_dsm_enable') === true) {
            const url = _.get(res, 'data.data.lu_dsm_url');
            const newUrl = url.replace('#', '?businessLine=SQLReview/#');
            const data = { ..._.get(res, 'data.data'), lu_dsm_url: newUrl };
            store.commit('account/setLuDsm', data);
          }
          if (resStr.user === 'jksuser') {
            store.commit('common/setMode', 'inset');
            Cookie.set(GLOBAL_CONFIG.TokenKey + '_tag', 'jksuser');
            setLoginTarget('');
          } else {
            store.commit('common/setMode', 'default');
          }

          store.commit('auth/setAuth', {
            resource: _.get(res, 'data.data.resource') || [],
            permissions: _.get(res, 'data.data.auth_resource') || []
          });
          store.commit(
            'common/setQuickEntrance',
            _.get(res, 'data.data.quick_entrance_menu') || []
          );
          // store.commit('account/setCompany', _.get(res, 'data.data.company') || []);
          // store.commit('account/setSubCompany', _.get(res, 'data.data.sub_company') || '');
          let user = _.get(res, 'data.data.user') || {};
          let sqlreviewVersion =
            _.get(res, 'data.data.sqlreview_version') || '';
          store.commit('project/setVersion', sqlreviewVersion);
          // permission_management 用户组逻辑，判断我的应用 是否显示
          let permissionManagement =
            _.get(res, 'data.data.permission_management') || '';
          store.commit('project/setPermissionManagement', permissionManagement);
          // sql_tool_switch 判断sql编辑器，是否显示 0 不展示，1 展示
          let sqlToolSwitch = _.get(res, 'data.data.sql_tool_switch') || '';
          store.commit('project/setSqlToolSwitch', sqlToolSwitch);
          // cybrebark的开关 1：开，0 ：关。 开的话数据源配置 新增实例密码不做必填校验
          let cybrearkSwitch = _.get(res, 'data.data.cybreark_switch') || '';
          store.commit('project/setCybrearkSwitch', cybrearkSwitch);
          // review_procedure_switch开关 1：开，0 ：关。 0 不展示，1 展示
          let reviewProcedureSwitch =
            _.get(res, 'data.data.review_procedure_switch') || 0;
          store.commit(
            'project/setReviewProcedureSwitch',
            reviewProcedureSwitch
          );
          // dml_switch开关 1：开，0 ：关。 0 不展示，1 展示
          let dmlSwitch = _.get(res, 'data.data.dml_switch') || 0;
          store.commit('project/setDmlSwitch', dmlSwitch);
          // is_third_login true为第三方登录
          let isThirdLogin = _.get(res, 'data.data.is_third_login') || null;
          store.commit('project/setIsThirdLogin', isThirdLogin);
          // 设置sql查询平台地址
          store.commit(
            'project/setSqlresolverUrl',
            _.get(res, 'data.data.sqlresolver_url')
          );
          // 设置数据源地址
          store.commit('project/setDsmUrl', _.get(res, 'data.data.dsm_url'));
          store.commit(
            'project/setOperatingWiki',
            _.get(res, 'data.data.operating_wiki')
          );
          // 设置第三方规则地址
          store.commit('project/setRuleUrl', _.get(res, 'data.data.rule_url'));
          // 设置第三方规则集地址
          store.commit(
            'project/setRuleSetUrl',
            _.get(res, 'data.data.ruleset_url')
          );
          // 设置topsql集地址
          store.commit(
            'project/setTopSqlUrl',
            _.get(res, 'data.data.top_sql_url')
          );
          // 设置audit_config_url集地址
          store.commit(
            'project/setAuditConfigUrl',
            _.get(res, 'data.data.audit_config_url')
          );
          // 设置数据监控页面地址
          store.commit('project/setMetaUrl', _.get(res, 'data.data.meta_url'));
          let lastUserName = sessionStorage.getItem('lastUserName');
          let isOther = lastUserName && user.name !== lastUserName;
          localStorage.setItem('changedUserName', user.name);
          let isFirstLogin = _.get(res, 'data.data.is_first_login') || 0;
          store.commit('project/setIsFirstLogin', isFirstLogin);
          // user && store.commit('account/setUser', user);
          store.commit('account/setUser', user);
          Cookie.set(GLOBAL_CONFIG.TokenKey, _.get(res, 'data.data.token'));
          // if (!pageAuthCheck(store.state.auth.pages)) {
          //   router.push({ path: '/exception/403?desc=抱歉，你没有权限操作' });
          //   return;
          // }
          // 去到指定页面
          const channelConfig = window.CHANNEL_INFO;
          const homePath = channelConfig.getHome(user.role);
          const loginTarget = sessionStorage.getItem('login_target');
          let path = '';
          // console.log(to, homePath, sessionStorage.getItem('login_target'), 77)
          if (loginTarget) {
            if (loginTarget.length > 1) {
              path =
                !isOther &&
                !ignorePages.find(item => loginTarget.includes(item))
                  ? loginTarget || homePath
                  : homePath;
            } else if (loginTarget === '/') {
              path = homePath;
            }
          }
          path && console.log('login_target: ', path);
          setLoginTarget('');

          // 当根据user.role、loginTarget确定了最终path后，再进行权限判断
          // 如果path在后端返回的routesMap中，to.name用routeItem.name否则用to.name
          // authCheck做判断 runAfterAuthCheck做执行（判断和执行分开）
          const authParams = {
            store,
            to,
            router,
            routesMap,
            pages: store.state.auth.pages
          };
          if (path) {
            const routeItem = Object.values(routesMap).find(
              item => item.path === path
            );
            //
            authParams.to = {
              ...to,
              name: routeItem ? routeItem.name : to.name
            };
            if (authCheck(authParams) === false) {
              runAfterAuthCheck(authParams);
              return;
            }
            path && next({ path });
          } else {
            if (authCheck(authParams) === false) {
              runAfterAuthCheck(authParams);
              return;
            }
          }
        } else {
          // router.push({ name: 'login' });
          // store.commit('auth/setAuth', null);
          // return;
        }
        next(
          window.Login.isGotoOwnLogin('success', res)
            ? { path: '/login' }
            : undefined
        );
      })
      .catch(e => {
        console.error(e);
        next(
          window.Login.isGotoOwnLogin('error', e)
            ? { path: '/login' }
            : undefined
        );
      });
    return;
  }
  next();
});

router.onError(error => {
  console.log(error);
  const pattern = /Loading chunk (\d)+ failed/g;
  const isChunkLoadFailed = error.message.match(pattern);
  // const targetPath = router.history.pending.fullPath;
  if (isChunkLoadFailed) {
    // console.log(targetPath)
    // router.replace(targetPath);
    Vue.prototype.$confirm({
      title: '重新加载',
      content: '系统更新，需要强制刷新以加载最新资源！',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        location.reload(true);
      }
    });
  }
});

export default router;
export { routesMap, routes };
