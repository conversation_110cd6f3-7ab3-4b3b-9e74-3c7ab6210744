<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <a-modal
    v-model="visible"
    title="新建存储过程扫描"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
// import common from '@/utils/common';
const formParams = ctx => {
  return {
    layout: 'vertical',
    fields: [
      {
        type: 'Select',
        label: '项目名称',
        key: 'project_id',
        props: {
          url: '/sqlreview/review/all_project',
          reqParams: {
            _t: +new Date()
          }
        },
        listeners: {
          change: value => {
            console.log(value);
            // ctx.$set(ctx.params.fields[1].props, 'reqParams', {
            //   project_id: value
            // });
            ctx.$refs.form.saving({
              project_id: value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '项目版本',
          key: 'review_point',
          props: {
            url: '/sqlreview/review/project-ls-git/',
            reqParams: {
              project_id: formData.project_id
            },
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      }
    ]
  };
};

export default {
  components: { Form },
  props: {},
  data() {
    return {
      review_point_id: null,
      visible: false,
      data: {},
      params: formParams(this)
    };
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.data = {
        mode: '1'
      };
      this.params = formParams(this);
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { data } = this;
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
