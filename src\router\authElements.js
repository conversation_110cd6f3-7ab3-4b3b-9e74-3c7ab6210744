// import { getRoles } from '@/api/user';
import Vue from 'vue';
import sourceData from './authElementsSource';

// 受控的元素
const controlElements = {
  // -----------------  变更单 ------------------ //
  'changeOrderList': [
    {
      'label': '新增变更单按钮',
      'type': 'DefaultShow',
      'value': 'changeOrderList_create_order'
    },
    {
      'label': '提交评审按钮',
      'type': 'DefaultShow',
      'value': 'changeOrderList_submitReview'
    },
    {
      'label': '变更单列表tabs',
      'type': 'ArrayMerge',
      'value': 'changeOrderList_tabs',
      'source': [
        {
          'label': '我参与的',
          'value': 'Join'
        },
        {
          'label': '待我部署',
          'value': 'Deploy'
        },
        {
          'label': '待我审核',
          'value': 'Check'
        },
        {
          'label': '已归档',
          'value': 'End'
        },
        {
          'label': '已作废',
          'value': 'Discard'
        }
      ]
    },
    {
      'label': '变更单列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'changeOrderList_join_table_actions',
      'source': [
        {
          'label': '提审',
          'value': 'review'
        },
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '取消评审',
          'value': 'cancelReview'
        },
        {
          'label': '查看',
          'value': 'view'
        },
        {
          'label': '删除',
          'value': 'remove'
        },
        {
          'label': '重试',
          'value': 'repush'
        },
        {
          'label': '生成发布任务',
          'value': 'generateTask'
        },
        {
          'label': '任务详情',
          'value': 'publishDetail'
        },
        {
          'label': '复制',
          'value': 'copyOrder'
        }
      ]
    }
  ],
  'changeOrderEdit': [
    {
      'label': '变更单详情(提交评审按钮)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_rb_submitReview'
    },
    {
      'label': '变更单详情(右下角保存按钮)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_update_order'
    },
    {
      'label': '变更单类型',
      'type': 'ArrayMerge',
      'matchKey': 'value',
      'value': 'changeOrderEdit_project_type',
      'source': [
        {
          'label': '日常发版',
          'value': 'PROJECT'
        },
        {
          'label': '数据修改',
          'value': 'DM'
        }
      ]
    },
    {
      'label': '变更评审',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_order_audit'
    },
    {
      'label': '变更单详情(变更内容标签)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_order_tabs',
      'source': [
        {
          'label': 'DDL',
          'value': 'DDL'
        },
        {
          'label': 'DML',
          'value': 'DML'
        },
        {
          'label': 'SPECIAL',
          'value': 'Special'
        }
      ]
    },
    {
      'label': '变更单详情（操作记录）',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_order_top_btns',
      'source': [
        {
          'label': '操作记录',
          'value': 'history'
        },
        {
          'label': '流程切换',
          'value': 'changeFlow'
        },
        {
          'label': '同意执行',
          'value': 'agreeExcute'
        },
        {
          'label': '不同意执行',
          'value': 'noAgreeExcute'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(批量新增字段)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_batch_add_columns'
    },
    {
      'label': 'ddl(复制线上表)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_copy_online_table'
    },
    {
      'label': '发布(执行到当前实例)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_deploy_excute'
    },
    {
      'label': '发布(执行到全部环境)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_deploy_excute_all'
    },
    {
      'label': '变更单详情(上传脚本/清空内容按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_changeContent_tabBarExtra_actions',
      'initialValue': {
        '_excludes': 'uploda'
      },
      'source': [
        {
          'label': '上传脚本',
          'value': 'uploda'
        },
        {
          'label': '清空内容',
          'value': 'clear'
        }
      ]
    },
    {
      'label': '变更单详情>上传脚本弹窗(脚本类型选项)',
      'type': 'ArrayMerge',
      'matchKey': 'value',
      'value': 'changeOrderEdit_uploadScriptFileDrawer_script_type_options',
      'source': [
        {
          'label': 'DDL',
          'value': 'DDL'
        },
        {
          'label': 'DML',
          'value': 'DML'
        },
        {
          'label': 'DDL&DML',
          'value': 'DDL&DML'
        },
        {
          'label': '特殊脚本',
          'value': 'SPECIAL'
        }
      ]
    },
    {
      'label': 'ddl>新增表/绑定线上表按钮',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_ddl_rt_actions'
    },
    {
      'label': 'ddl>表列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_table_actions',
      'source': [
        {
          'label': '解绑',
          'value': 'untie'
        },
        {
          'label': '结构维护',
          'value': 'struct'
        },
        {
          'label': '发布',
          'value': 'deploy'
        },
        {
          'label': '查看',
          'value': 'look'
        },
        {
          'label': '评审',
          'value': 'review'
        },
        {
          'label': '删除',
          'value': 'edit'
        },
        {
          'label': '编辑',
          'value': 'remove'
        }
      ]
    },
    {
      'label': 'ddl>表列表展示',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_table_columns',
      'source': [
        {
          'label': '序号',
          'value': 'index'
        },
        {
          'label': '状态',
          'value': 'editable'
        },
        {
          'label': '表名称',
          'value': 'table_name'
        },
        {
          'label': '实体',
          'value': 'entity_id'
        },
        {
          'label': 'Schema',
          'value': 'schema_id'
        },
        {
          'label': '数据库类型',
          'value': 'db_type'
        },
        {
          'label': '表注释',
          'value': 'table_comment'
        },
        {
          'label': '文件名',
          'value': 'bak_file_name'
        },
        {
          'label': '归档路径',
          'value': 'bak_file_path'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(新增字段按钮)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_ddl_struct_extend_field_btn'
    },
    {
      'label': 'ddl>表结构维护(新增索引按钮)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_ddl_struct_extend_ref_btn'
    },
    {
      'label': 'ddl>表结构维护(新增权限按钮)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_ddl_struct_extend_auth_btn'
    },
    {
      'label': 'ddl>表结构维护(新增触发器按钮)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_ddl_struct_extend_trigger_btn'
    },
    {
      'label': 'ddl>表结构维护(新增分区按钮)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_ddl_struct_extend_partitions_btn'
    },
    {
      'label': 'ddl>表结构维护(新增约束按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_extend_constraint_rt_btns',
      'source': [
        {
          'label': '添加约束',
          'value': 'add'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(字段列表展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_extend_field_table_columns',
      'source': [
        {
          'label': '排序',
          'value': 'index'
        },
        {
          'label': '字段名',
          'value': 'column_name'
        },
        {
          'label': '要素名称',
          'value': 'element_name'
        },
        {
          'label': '要素编码',
          'value': 'element_code'
        },
        {
          'label': '字段类型',
          'value': 'column_type'
        },
        {
          'label': '长度',
          'value': 'column_length'
        },
        {
          'label': '精度',
          'value': 'accuracy'
        },
        {
          'label': '字段备注',
          'value': 'column_comment'
        },
        {
          'label': '分区列',
          'value': 'is_partition'
        },
        {
          'label': '虚拟列',
          'value': 'virtual_column'
        },
        {
          'label': '空值',
          'value': 'is_nullable'
        },
        {
          'label': '自增',
          'value': 'is_autoincrement'
        },
        {
          'label': '默认值',
          'value': 'column_default'
        },
        {
          'label': '状态',
          'value': 'project_id'
        },
        {
          'label': '数据标准',
          'value': 'source_code'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(索引列表展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_extend_ref_table_columns',
      'source': [
        {
          'label': '排序',
          'value': 'index'
        },
        {
          'label': '索引类型',
          'value': 'index_type'
        },
        {
          'label': '索引名称',
          'value': 'index_name'
        },
        {
          'label': '索引字段',
          'value': 'column_name_list'
        },
        {
          'label': '状态',
          'value': 'project_id'
        },
        {
          'label': '本地/全局',
          'value': 'partitioned'
        },
        {
          'label': '是否有效',
          'value': 'status'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(索引列表操作按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_extend_ref_table_actions',
      'source': [
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '删除',
          'value': 'remove'
        },
        {
          'label': '解绑',
          'value': 'abandon'
        },
        {
          'label': '重建索引/取消重建索引',
          'value': 'rebuildindex'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(权限列表展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_extend_auth_table_columns',
      'source': [
        {
          'label': '排序',
          'value': 'index'
        },
        {
          'label': '用户名',
          'value': 'grantee'
        },
        {
          'label': '权限',
          'value': 'privilege'
        },
        {
          'label': '状态',
          'value': 'project_id'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(约束列表展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_extend_constraint_table_columns',
      'source': [
        {
          'label': '排序',
          'value': 'index'
        },
        {
          'label': '约束名',
          'value': 'constraint_name'
        },
        {
          'label': '约束类型',
          'value': 'constraint_type'
        },
        {
          'label': '约束字段',
          'value': 'column_name_list'
        },
        {
          'label': '约束内容',
          'value': 'search_condition'
        },
        {
          'label': '关联索引',
          'value': 'index_name'
        },
        {
          'label': '外键表',
          'value': 'r_table_name'
        },
        {
          'label': '外键字段',
          'value': 'r_column_name'
        },
        {
          'label': '状态',
          'value': 'project_id'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(分区策略列表展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_extend_strategy_table_columns',
      'source': [
        {
          'label': '状态',
          'value': 'status_display'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(回滚信息)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_ddl_struct_roll_back_actions'
    },
    {
      'label': 'ddl>表结构维护(新增回滚语句)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_rollback_rt_btns',
      'source': [
        {
          'label': '新增一条',
          'value': 'add'
        },
        {
          'label': '保存',
          'value': 'save'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(回滚列表展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_rollback_table_columns',
      'source': [
        {
          'label': 'id',
          'value': 'id'
        },
        {
          'label': '回滚语句',
          'value': 'content'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'ddl>表结构维护(底部关闭按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_bt_btns',
      'source': [
        {
          'label': '关闭',
          'value': 'close'
        }
      ]
    },
    {
      'label': 'ddl>结构维护(分区配置保存按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_struct_parting_bt_btns',
      'source': [
        {
          'label': '保存',
          'value': 'save'
        }
      ]
    },
    {
      'label': 'ddl>序列列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_seq_table_actions',
      'source': [
        {
          'label': '发布',
          'value': 'deploy'
        },
        {
          'label': '查看',
          'value': 'view'
        },
        {
          'label': '评审',
          'value': 'review'
        },
        {
          'label': '授权',
          'value': 'auth'
        },
        {
          'label': '解绑',
          'value': 'untie'
        },
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '删除',
          'value': 'remove'
        }
      ]
    },
    {
      'label': 'ddl>special列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_special_table_actions',
      'source': [
        {
          'label': 'SQL维护',
          'value': 'struct'
        },
        {
          'label': '解绑',
          'value': 'untie'
        },
        {
          'label': '发布',
          'value': 'deploy'
        },
        {
          'label': '查看',
          'value': 'view'
        },
        {
          'label': '评审',
          'value': 'review'
        },
        {
          'label': '删除',
          'value': 'remove'
        }
      ]
    },
    {
      'label': 'ddl>序列(新增/绑定序列按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_seq_rt_btns',
      'source': [
        {
          'label': '新增序列',
          'value': 'add'
        },
        {
          'label': '绑定线上序列',
          'value': 'bindonline'
        }
      ]
    },
    {
      'label': 'ddl>特殊脚本(新增/绑定按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_ddl_special_rt_btns',
      'source': [
        {
          'label': '新增视图/物化视图/函数/存储过程/包',
          'value': 'add'
        },
        {
          'label': '绑定线上视图/物化视图/函数/存储过程/包',
          'value': 'bindonline'
        }
      ]
    },
    {
      'label': 'dml>新增变更按钮',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_dml_rt_actions'
    },
    {
      'label': 'dml>脚本下载按钮',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_dml_rt_download_btn'
    },
    {
      'label': 'dml>变更列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_table_actions',
      'source': [
        {
          'label': 'SQL维护',
          'value': 'content'
        },
        {
          'label': '人工通过',
          'value': 'pass'
        },
        {
          'label': '发布',
          'value': 'deploy'
        },
        {
          'label': '强制通过',
          'value': 'forcedPass'
        },
        {
          'label': '取消强制通过',
          'value': 'cancelPass'
        },
        {
          'label': '评审',
          'value': 'review'
        },
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '删除',
          'value': 'remove'
        }
      ]
    },
    {
      'label': 'dml>SQL维护(保存按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_content_bt_btns',
      'source': [
        {
          'label': '保存',
          'value': 'save'
        }
      ]
    },
    {
      'label': 'dml>变更列表展示',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_table_columns',
      'source': [
        {
          'label': '序号',
          'value': 'index'
        },
        {
          'label': '评审状态',
          'value': 'status'
        },
        {
          'label': '脚本简介',
          'value': 'name'
        },
        {
          'label': '实体',
          'value': 'entity_id'
        },
        {
          'label': 'Schema',
          'value': 'schema_id'
        },
        {
          'label': '生成备份语句',
          'value': 'need_backup_piece'
        },
        {
          'label': '文件名',
          'value': 'bak_file_name'
        },
        {
          'label': '归档路径',
          'value': 'bak_file_path'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'dml>SQL维护(tabs)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_content_tabs',
      'source': [
        {
          'label': '脚本内容',
          'value': 'JavaScriptContent'
        },
        {
          'label': '备份语句',
          'value': 'BackUp'
        },
        {
          'label': '附件',
          'value': 'FileUpload'
        }
      ]
    },
    {
      'label': 'dml>SQL维护(脚本内容列表展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_content_js_table_columns',
      'source': [
        {
          'label': '排序',
          'value': 'index'
        },
        {
          'label': 'SQL',
          'value': 'content'
        },
        {
          'label': '预估/实际影响条数',
          'value': 'influence_counts'
        },
        {
          'label': '备份语句',
          'value': 'bak_sql'
        },
        {
          'label': '回滚语句',
          'value': 'rollback_sql'
        },
        {
          'label': '警告信息',
          'value': 'warning'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'dml>SQL维护(脚本内容新增/批量删除按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_content_js_table_rt_btns',
      'source': [
        {
          'label': '新增',
          'value': 'add'
        },
        {
          'label': '全量获取影响条数',
          'value': 'getInfluence'
        },
        {
          'label': '全量修改影响条数',
          'value': 'updateInfluence'
        },
        {
          'label': '批量删除',
          'value': 'mutliDel'
        }
      ]
    },
    {
      'label': 'dml>SQL维护(脚本提交方式)',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_dml_content_js_submit_type'
    },
    {
      'label': 'dml>SQL维护(编辑页面展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_content_js_edit_form_params',
      'source': [
        {
          'label': 'SQL代码片段',
          'value': 'content'
        },
        {
          'label': '预估影响条数',
          'value': 'influence_counts'
        },
        {
          'label': '备份语句',
          'value': 'bak_sql'
        },
        {
          'label': '回滚语句',
          'value': 'rollback_sql'
        }
      ]
    },
    {
      'label': 'dml>SQL维护(附件上传按钮）',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_dml_content_fileupload_btn'
    },
    {
      'label': 'dml>SQL维护(附件列表展示）',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_content_upload_table_columns',
      'source': [
        {
          'label': '排序',
          'value': 'index'
        },
        {
          'label': '文档名称',
          'value': 'file_name'
        },
        {
          'label': '上传时间',
          'value': 'updated_at'
        },
        {
          'label': 'commit方式',
          'value': 'commit_type'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'dml>SQL维护(备份语句列表展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_content_backup_table_columns',
      'source': [
        {
          'label': '排序',
          'value': 'index'
        },
        {
          'label': '备份语句',
          'value': 'content'
        },
        {
          'label': '警告信息',
          'value': 'warning'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'dml>SQL维护(备份语句新增/批量删除按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_dml_content_backup_table_rt_btns',
      'source': [
        {
          'label': '新增',
          'value': 'add'
        },
        {
          'label': '批量删除',
          'value': 'mutliDel'
        }
      ]
    },
    {
      'label': 'DDL比对源和实例范围配置',
      'type': 'BACK',
      'value': 'CHANGEORDER_ROLE_DDL_CONFIG',
      'initialValue': {
        '_includes': 'DEV,STG'
      },
      'source': [
        {
          'label': 'DEV',
          'value': 'DEV'
        },
        {
          'label': 'STG',
          'value': 'STG'
        },
        {
          'label': 'UAT',
          'value': 'UAT'
        }
      ]
    },
    {
      'label': 'special>新增变更按钮',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_special_rt_actions'
    },
    {
      'label': 'special>脚本下载按钮',
      'type': 'DefaultShow',
      'value': 'changeOrderEdit_special_rt_download_btn'
    },
    {
      'label': 'special>变更列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_special_table_actions',
      'source': [
        {
          'label': 'SQL维护',
          'value': 'content'
        },
        {
          'label': '人工通过',
          'value': 'pass'
        },
        {
          'label': '发布',
          'value': 'deploy'
        },
        {
          'label': '强制通过',
          'value': 'forcedPass'
        },
        {
          'label': '取消强制通过',
          'value': 'cancelPass'
        },
        {
          'label': '评审',
          'value': 'review'
        },
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '删除',
          'value': 'remove'
        }
      ]
    },
    {
      'label': 'special>变更列表展示',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_special_table_columns',
      'source': [
        {
          'label': '#',
          'value': 'index'
        },
        {
          'label': '状态',
          'value': 'status'
        },
        {
          'label': '脚本简介',
          'value': 'name'
        },
        {
          'label': '实体',
          'value': 'entity_id'
        },
        {
          'label': '数据库',
          'value': 'schema_id'
        },
        {
          'label': '文件名',
          'value': 'bak_file_name'
        },
        {
          'label': '归档路径',
          'value': 'bak_file_path'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': 'special>SQL维护(保存按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_special_content_bt_btns',
      'source': [
        {
          'label': '保存',
          'value': 'save'
        }
      ]
    },
    {
      'label': 'special>SQL维护(类型选择选项)',
      'type': 'ArrayMerge',
      'matchKey': 'value',
      'value': 'changeOrderEdit_special_content_js_content_type',
      'source': [
        {
          'label': '通用SQL',
          'value': 'COMMON'
        },
        {
          'label': '语句解析',
          'value': 'PARSED'
        }
      ]
    },
    {
      'label': 'special>SQL维护(新增/批量删除按钮)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_special_content_table_rt_btns',
      'source': [
        {
          'label': '新增',
          'value': 'add'
        },
        {
          'label': '批量删除',
          'value': 'multiDel'
        }
      ]
    },
    {
      'label': 'special>SQL维护(脚本内容列表展示)',
      'type': 'ArrayMerge',
      'value': 'changeOrderEdit_special_content_js_table_columns',
      'source': [
        {
          'label': '#',
          'value': 'index'
        },
        {
          'label': 'SQL',
          'value': 'content'
        },
        {
          'label': '类型',
          'value': 'content_type'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    }
  ],
  // -----------------  发版 ------------------ //
  'publishList': [
    {
      'label': '新增任务按钮',
      'type': 'DefaultShow',
      'value': 'publishList_wait_add'
    },
    {
      'label': '新增任务(任务类型)',
      'type': 'ArrayMerge',
      'matchKey': 'value',
      'value': 'publishList_wait_add_task_type',
      'source': [
        {
          'label': '数据修改',
          'value': 'DM'
        },
        {
          'label': '日常发版',
          'value': 'PROJECT'
        }
      ]
    },
    {
      'label': '列表展示',
      'type': 'ArrayMerge',
      'value': 'publishList_table_columns',
      'source': [
        {
          'label': '排序',
          'value': 'index'
        },
        {
          'label': '任务名称',
          'value': 'task_name'
        },
        {
          'label': '任务号',
          'value': 'task_id'
        },
        {
          'label': '任务类型',
          'value': 'task_type'
        },
        {
          'label': '任务状态',
          'value': 'status'
        },
        {
          'label': '任务来源',
          'value': 'task_source'
        },
        {
          'label': '任务说明',
          'value': 'task_comment'
        },
        {
          'label': '变更单号',
          'value': 'task_content'
        },
        {
          'label': '创建时间',
          'value': 'created_at'
        },
        {
          'label': '开始时间',
          'value': 'started_at'
        },
        {
          'label': '结束时间',
          'value': 'ended_at'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': '待发布列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'publishList_wait_table_actions',
      'source': [
        {
          'label': '查看',
          'value': 'view'
        },
        {
          'label': '新建变更单',
          'value': 'createChange'
        },
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '重新发布',
          'value': 'repeatDeploy'
        },
        {
          'label': '一键review',
          'value': 'review'
        },
        {
          'label': '回滚',
          'value': 'rollBack'
        },
        {
          'label': '任务结束',
          'value': 'taskFinish'
        },
        {
          'label': '取消发布',
          'value': 'cancelDeploy'
        },
        {
          'label': '归档',
          'value': 'bakFile'
        },
        {
          'label': '作废',
          'value': 'taskDiscard'
        }
      ]
    }
  ],
  'publisDetail': [
    {
      'label': '任务详情(废弃任务按钮)',
      'type': 'ArrayMerge',
      'value': 'publisDetail_rt_actions',
      'source': [
        {
          'label': '废弃任务',
          'value': 'discard'
        }
      ]
    },
    {
      'label': '任务详情(全量发布按钮)',
      'type': 'DefaultShow',
      'value': 'publisDetail_fullDeploy_btn'
    },
    {
      'label': '任务详情(列表展示)',
      'type': 'ArrayMerge',
      'value': 'publisDetail_table_columns',
      'source': [
        {
          'label': '变更单号',
          'value': 'project_id'
        },
        {
          'label': '实体',
          'value': 'entity_name'
        },
        {
          'label': '任务发布时间',
          'value': 'started_at'
        },
        {
          'label': '变更单属主',
          'value': 'created_by'
        },
        {
          'label': '变更单状态',
          'value': 'status'
        },
        {
          'label': '执行结果',
          'value': 'all_num'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': '任务详情(列表操作按钮)',
      'type': 'ArrayMerge',
      'value': 'publisDetail_table_actions',
      'source': [
        {
          'label': '发布',
          'value': 'deploy'
        },
        {
          'label': '详情',
          'value': 'detail'
        },
        {
          'label': '发布设置',
          'value': 'publishSetting'
        },
        {
          'label': '归档',
          'value': 'bakFile'
        },
        {
          'label': '一键review',
          'value': 'review'
        }
      ]
    },
    {
      'label': '任务详情>发布明细(列表展示)',
      'type': 'ArrayMerge',
      'value': 'publisDetail_changeOrder_table_columns',
      'source': [
        {
          'label': 'group_id',
          'value': 'group_id'
        },
        {
          'label': 'SQL内容',
          'value': 'sql_content'
        },
        {
          'label': '状态',
          'value': 'status'
        },
        {
          'label': '数据库（实例）',
          'value': 'instance_name'
        },
        {
          'label': '实际影响行数',
          'value': 'sql_effect_row'
        },
        {
          'label': '执行结果',
          'value': 'sql_exec_result'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': '任务详情>发布明细(内嵌列表展示)',
      'type': 'ArrayMerge',
      'value': 'publisDetail_changeOrder_table_inner_columns',
      'source': [
        {
          'label': '#',
          'value': 'id'
        },
        {
          'label': 'SQL内容',
          'value': 'sql_content'
        },
        {
          'label': '状态',
          'value': 'status'
        },
        {
          'label': '数据库（实例）',
          'value': 'instance_name'
        },
        {
          'label': '实际影响行数',
          'value': 'sql_effect_row'
        },
        {
          'label': '执行结果',
          'value': 'sql_exec_result'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': '任务详情>发布明细(列表操作按钮)',
      'type': 'ArrayMerge',
      'value': 'publisDetail_changeOrderDetail_table_actions',
      'source': [
        {
          'label': '跳过',
          'value': 'pass'
        }
      ]
    },
    {
      'label': '任务详情>发布明细(内嵌列表操作按钮)',
      'type': 'ArrayMerge',
      'value': 'publisDetail_changeOrderDetail_table_inner_actions',
      'source': [
        {
          'label': '跳过',
          'value': 'pass'
        },
        {
          'label': '终止',
          'value': 'toEnd'
        }
      ]
    }
  ],
  'publishPlanList': [
    {
      'label': '发版计划(列表展示)',
      'type': 'ArrayMerge',
      'value': 'publishPlanList_table_columns',
      'source': [
        {
          'label': 'id',
          'value': 'id'
        },
        {
          'label': '计划名称',
          'value': 'plan_name'
        },
        {
          'label': '预计发版时间',
          'value': 'plan_date'
        },
        {
          'label': '状态',
          'value': 'collect_status_display'
        },
        {
          'label': '上次收集完成时间',
          'value': 'last_collect_date'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': '发版计划(列表操作按钮)',
      'type': 'ArrayMerge',
      'value': 'publishPlanList_table_actions',
      'source': [
        {
          'label': '查看',
          'value': 'detail'
        },
        {
          'label': '收集变更单',
          'value': 'collect'
        },
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '删除',
          'value': 'remove'
        }
      ]
    }
  ],
  // -----------------  元数据 ------------------ //
  'metadataImport': [
    {
      'label': '元数据导入按钮',
      'type': 'DefaultShow',
      'value': 'metadataImport_import_btn'
    }
  ],
  'metadataSearch': [
    {
      'label': '元数据查询tabs',
      'type': 'ArrayMerge',
      'value': 'metadataSearch_tabs',
      'source': [
        {
          'label': '表结构信息',
          'value': 'Tables'
        },
        {
          'label': '序列信息',
          'value': 'Columns'
        },
        {
          'value': 'Procedure',
          'label': '存储过程'
        },
        {
          'value': 'Functions',
          'label': '函数'
        },
        {
          'value': 'Package',
          'label': '包'
        },
        {
          'value': 'Views',
          'label': '视图'
        },
        {
          'value': 'MViews',
          'label': '物化视图'
        }
      ]
    },
    {
      'label': '元数据查询(表列表)',
      'type': 'ArrayMerge',
      'value': 'metadataSearch_tables_table_columns',
      'source': [
        {
          'label': 'id',
          'value': 'id'
        },
        {
          'label': '表名',
          'value': 'table_name'
        },
        {
          'label': '数据库',
          'value': 'schema_name'
        },
        {
          'label': '状态',
          'value': 'project_id'
        },
        {
          'label': '表注释',
          'value': 'table_comment'
        },
        {
          'label': '表大小(M)',
          'value': 'table_size'
        },
        {
          'label': '表行数',
          'value': 'table_rows'
        },
        {
          'label': '表类型',
          'value': 'table_type_display'
        },
        {
          'label': '实例',
          'value': 'instance_name'
        },
        {
          'label': '子系统',
          'value': 'app_names'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': '元数据查询序列信息表格列',
      'type': 'ArrayMerge',
      'value': 'metadataSearch_columns_table_columns',
      'source': [
        {
          'label': 'id',
          'value': 'id'
        },
        {
          'label': '序列名',
          'value': 'sequence_name'
        },
        {
          'label': '数据库',
          'value': 'schema_name'
        },
        {
          'label': '初始值',
          'value': 'start_with'
        },
        {
          'label': '最大值',
          'value': 'max_value'
        },
        {
          'label': '最小值',
          'value': 'min_value'
        },
        {
          'label': '步长',
          'value': 'increment_by'
        },
        {
          'label': '序列缓存',
          'value': 'cache_size'
        },
        {
          'label': '实例',
          'value': 'instance_name'
        },
        {
          'label': '子系统',
          'value': 'app_name'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': '元数据查询特殊信息表格列(存储过程等)',
      'type': 'ArrayMerge',
      'value': 'metadataSearch_specials_table_columns',
      'source': [
        {
          'label': '#',
          'value': 'id'
        },
        {
          'label': '脚本名',
          'value': 'special_name'
        },
        {
          'label': '数据库',
          'value': 'schema_name'
        },
        {
          'label': '状态',
          'value': 'project_id'
        },
        {
          'label': '实例',
          'value': 'entity_names'
        },
        {
          'label': '子系统',
          'value': 'app_names'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    }
  ],
  // -----------------  配置管理 ------------------ //
  'instance-config': [
    {
      'label': '新增实例按钮',
      'type': 'DefaultShow',
      'value': 'instance-config_add_instance'
    },
    {
      'label': '实体名称管理按钮',
      'type': 'DefaultShow',
      'value': 'instance-config_entity_name_manage'
    },
    {
      'label': '实例管理(列表操作按钮)',
      'type': 'ArrayMerge',
      'value': 'instance-config_table_actions',
      'source': [
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '删除',
          'value': 'remove'
        },
        {
          'label': '测试连接',
          'value': 'test'
        },
        {
          'label': '授权',
          'value': 'auth'
        }
      ]
    },
    {
      'label': '实例管理(列表展示)',
      'type': 'ArrayMerge',
      'value': 'instance-config_table_columns',
      'source': [
        {
          'label': '#',
          'value': 'index'
        },
        {
          'label': '实例名称',
          'value': 'instance_name'
        },
        {
          'label': '实体名称',
          'value': 'entityName'
        },
        {
          'label': '实例环境',
          'value': 'instance_usage'
        },
        {
          'label': '实例类型',
          'value': 'instance_type'
        },
        {
          'label': '实例IP',
          'value': 'master_ip'
        },
        {
          'label': '实例端口',
          'value': 'master_port'
        },
        {
          'label': 'domain',
          'value': 'master_domain'
        },
        {
          'label': 'vip',
          'value': 'master_vip'
        },
        {
          'label': 'host',
          'value': 'master_host'
        },
        {
          'label': '用户名',
          'value': 'master_username'
        },
        {
          'label': '连接串',
          'value': 'master_tns'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    }
  ],
  'database-config': [
    {
      'label': '新增数据库按钮',
      'type': 'DefaultShow',
      'value': 'database-config_add_database'
    },
    {
      'label': '列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'database-config_table_actions',
      'initialValue': {
        '_includes': ''
      },
      'source': [
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '删除',
          'value': 'remove'
        },
        {
          'label': '授权',
          'value': 'auth'
        }
      ]
    }
  ],
  'application-config': [
    {
      'label': '新增应用按钮',
      'type': 'DefaultShow',
      'value': 'application-config_add_application'
    },
    {
      'label': '列表展示',
      'type': 'ArrayMerge',
      'value': 'application-config_table_columns',
      'source': [
        {
          'label': '#',
          'value': 'index'
        },
        {
          'label': '系统英文简称',
          'value': 'app_code'
        },
        {
          'label': '中文简称',
          'value': 'app_name'
        },
        {
          'label': '应用运维团队',
          'value': 'extend_source_code'
        },
        {
          'label': '备注',
          'value': 'app_comments'
        },
        {
          'label': '审批关联模板',
          'value': 'rule_comments'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': '列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'application-config_table_actions',
      'initialValue': {
        '_includes': ''
      },
      'source': [
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '授权',
          'value': 'auth'
        },
        {
          'label': 'sql归档',
          'value': 'sqlPath'
        },
        {
          'label': '删除',
          'value': 'remove'
        }
      ]
    },
    {
      'label': '审批模板列表展示',
      'type': 'ArrayMerge',
      'value': 'application-config-authModal_table_columns',
      'source': [
        {
          'label': '节点描述(item_flag)',
          'value': 'item_flag(通用)'
        },
        {
          'label': '节点描述（item_desc）',
          'value': 'item_desc(证券独有)'
        },
        {
          'label': '审批要求',
          'value': 'hand_type'
        },
        {
          'label': '角色',
          'value': 'role_code'
        },
        {
          'label': '审批人(单选)',
          'value': 'um_name(单选)'
        },
        {
          'label': '审批人(多选)',
          'value': 'users(多选)'
        },
        {
          'label': '节点类型',
          'value': 'item_node_type'
        },
        {
          'label': '操作',
          'value': 'action'
        }
      ]
    },
    {
      'label': '审批模板Form字段',
      'type': 'ArrayMerge',
      'value': 'application-config-authModal_form_fields',
      'source': [
        {
          'label': '审核模板名称',
          'value': 'template_name'
        },
        {
          'label': '模板类型',
          'value': 'template_type'
        }
      ]
    },
    {
      'label': '编辑应用Form字段',
      'type': 'ArrayMerge',
      'value': 'application-config-editModal_form_fields',
      'source': [
        {
          'label': '系统英文简称',
          'value': 'app_code'
        },
        {
          'label': '系统中文简称',
          'value': 'app_name'
        },
        {
          'label': '应用运维团队',
          'value': 'source_code'
        },
        {
          'label': '备注',
          'value': 'app_comments'
        },
        {
          'label': '实体名称',
          'value': 'entity_ids'
        },
        {
          'label': '数据库',
          'value': 'schema_ids'
        }
      ]
    }
  ],
  // -----------------  规范管理 ------------------ //
  'ruleConfig': [
    {
      'label': '列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'standard-config_table_actions',
      'source': [
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '复制',
          'value': 'copy'
        },
        {
          'label': '查看',
          'value': 'view'
        },
        {
          'label': '删除',
          'value': 'remove'
        }
      ]
    },
    {
      'label': '列表(规范状态操作控制)',
      'type': 'ArrayMerge',
      'value': 'standard-config_table_column_enabled',
      'initialValue': {
        '_includes': 'undisable'
      },
      'source': [
        {
          'label': '可编辑状态',
          'value': 'undisable'
        },
        {
          'label': '不可编辑状态',
          'value': 'disable'
        }
      ]
    },
    {
      'label': '新增规范按钮',
      'type': 'ArrayMerge',
      'value': 'standard-config_table_rt_actions',
      'source': [
        {
          'label': '新增规范',
          'value': 'add'
        }
      ]
    }
  ],
  // -----------------  用户管理 ------------------ //
  'userList': [
    {
      'label': '新增用户按钮',
      'type': 'DefaultShow',
      'value': 'userList_add_btn'
    },
    {
      'label': '列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'userList_table_actions',
      'source': [
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '重置密码',
          'value': 'reset'
        },
        {
          'label': '删除',
          'value': 'remove'
        }
      ]
    }
  ],
  // -----------------  组织树管理 ------------------ //
  'groupList': [
    {
      'label': '新增组织',
      'type': 'DefaultShow',
      'value': 'userList_group_add_btn'
    },
    {
      'label': '删除组织',
      'type': 'DefaultShow',
      'value': 'userList_group_remove_btn'
    }
  ],
  // -----------------  角色管理 ------------------ //
  'roleList': [
    {
      'label': '新增角色按钮',
      'type': 'DefaultShow',
      'value': 'userList_role_add_btn'
    },
    {
      'label': '列表操作按钮',
      'type': 'ArrayMerge',
      'value': 'userList_role_table_actions',
      'source': [
        {
          'label': '编辑',
          'value': 'edit'
        },
        {
          'label': '删除',
          'value': 'remove'
        }
      ]
    }
  ]
};

// 全局权限（添加后所有角色生效，无需重新勾选）
const systemAuth = [
  // 科技专用
  { label: '提交评审(科技专用)', type: 'DefaultHide', value: 'systemAuth_changeOrderList_submitReview' },
  { label: '元数据导入步骤一搜索子系统(科技专用)', type: 'DefaultHide', value: 'systemAuth_metadataImport_import_step1_search' },
  { label: '数据库配置拉取数据库(科技专用)', type: 'DefaultHide', value: 'systemAuth_databaseConfig_import_database' },
  { label: '实例配置拉取实例(科技专用)', type: 'DefaultHide', value: 'systemAuth_instanceConfig_import_instance' },
  // 证券专用
  { label: '发布管理详情审核信息（证券专用）', type: 'DefaultHide', value: 'systemAuth_publisDetail_review_info' }
];

let AuthMap = {};
_.forEach(controlElements, (page, pageKey) => {
  _.forEach(page, authItem => {
    const _sourceItem = sourceData.find(itm => itm.value === authItem.value);
    _sourceItem && (authItem.sourceData = _sourceItem.source);
    AuthMap[authItem.value] = {
      ...authItem,
      code: authItem.value
    };
  });
});
_.forEach(systemAuth, authItem => {
  AuthMap[authItem.value] = {
    ...authItem,
    code: authItem.value
  };
});
window.AuthMap = AuthMap;
Vue.prototype.AuthMap = AuthMap;

// 根据角色生成元素权限树
// const getAuthElementsByRoles = async () => {
//   const res = await getRoles({});
//   const { code, data } = _.get(res, 'data') || {};
//   let tree = [];
//   if (code == 0) {
//     const roles = data.results || [];

//     roles.forEach(role => {
//       tree.push({
//         label: role.role_name,
//         value: role.role_code,
//         selectable: false,
//         children: getTreeFromElements(role)
//       })
//     })
//     return tree;
//   }
//   return [];
// }

// let authItemsMap = {};
// const getTreeFromElements = (role) => {
//   let res = [];
//   _.forEach(controlElements, (item, key) => {
//     const route = window.routesMap[key] || {};
//     const { desc } = route.meta || {};

//     res.push({
//       label: desc,
//       value: key + '_' + role.role_code,
//       selectable: false,
//       children: item.map(authItem => {
//         let roleCode = authItem.type === 'BACK' ? role.role_code.toUpperCase() : role.role_code.toLowerCase();
//         let roleName = authItem.type === 'BACK' ? role.role_name + '_后端' : role.role_name;
//         let resItem = {
//           ...authItem,
//           label: authItem.label + `(${roleName})`,
//           value: authItem.value + '_' + roleCode
//         };
//         authItemsMap[resItem.value] = resItem;
//         return resItem;
//       })
//     })
//   })
//   return res;
// }

const getAuthElementsByPage = (pageKey) => {
  return controlElements[pageKey];
};

export default {
  getAuthElementsByPage,
  // authItemsMap,
  systemAuth
};