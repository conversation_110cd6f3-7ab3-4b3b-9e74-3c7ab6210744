<template>
  <div class="config-datasource-table-detail page-list-with-tabs">
    <div class="content">
      <div class="custom-page-header">
        <div class="header-info">
          <Tag type="Env" :text="instanceData.env" />
          <DbImg :value="instanceData.db_type" :schemaName="instanceData.name" :limit="36" />
        </div>

        <div>
          <InputSearch placeholder="请输入表名称搜索" @search="search"></InputSearch>
        </div>
      </div>
      <Table
        ref="table"
        v-bind="tableParams || {}"
        :dataSource="dataSource || []"
        @selectChange="selectChange"
        class="new-view-table small-size"
      >
        <Shower
          slot="Shower"
          slot-scope="{ text, record, column }"
          v-bind="{ showerValue: text, showerData: record, config: column.shower }"
        >
          <div slot="table_name" class="table-info">
            <div>
              <custom-icon type="lu-icon-list" style="color: blue" />
              <span>{{record.table_name}}</span>
            </div>
            <div>
              <custom-icon type="lu-icon-sequence" style="color: purple" />
              <span>行数：{{record.row}}</span>
            </div>
            <div>
              <custom-icon type="lu-icon-capacity" style="color: green" />
              <span>容量：{{record.volume || '--'}}</span>
            </div>
          </div>
          <!-- table插槽 -->
          <div slot="permission_status" class="permission-status-and-action">
            <div class="permission-status">
              <span>权限管理</span>
              <a-switch
                checked-children="开"
                un-checked-children="关"
                :checked="record.permission_status === 1"
                @change="Switch(record)"
              ></a-switch>
            </div>
            <a
              @click="authManager(record, 'tableDetail')"
              actionBtn
              :disabled="record.permission_status !== 1"
            >权限管理</a>
          </div>
        </Shower>

        <!-- 嵌套子表格 -->
        <div class="inner-table" slot="expandedRowRender" slot-scope="{text}">
          <Tabs v-model="text._activeTab" :tabsList="tabsList" :mode="'tag'" @change="onChange">
            <!-- 字段 -->
            <Table
              slot="field"
              v-bind="fieldParams || {}"
              :reqParams="{table_id: text.id}"
              :dataSource="fieldData || []"
              bordered
              :ref="`field_${text.id}`"
              @selectChange="data=>fieldSelectChange(text,data)"
            >
              <template slot="permission_status" slot-scope="{record}">
                <a-switch
                  checked-children="开"
                  un-checked-children="关"
                  :checked="record.permission_status === 1"
                  @change="fieldSwitch(record, text)"
                ></a-switch>

                <a
                  @click="authManager(record, 'field')"
                  actionBtn
                  :disabled="record.permission_status !== 1"
                  style="margin-left: 8px"
                >权限管理</a>
              </template>
              <template slot="sensitive_status" slot-scope="{record}">
                <a-switch
                  checked-children="开"
                  un-checked-children="关"
                  :checked="record.sensitive_status === 1"
                  @change="sensitiveSwitchFn(record, text)"
                ></a-switch>

                <!-- <a
                  @click="authManager(record, 'field')"
                  actionBtn
                  :disabled="record.permission_status !== 1"
                  style="margin-left: 8px"
                >脱敏控制</a>-->
              </template>
            </Table>
            <!-- 索引 -->
            <Table
              slot="index"
              v-bind="indexParams || {}"
              :reqParams="{table_id: text.id}"
              :dataSource="indexData || []"
              bordered
              ref="index"
            ></Table>
          </Tabs>
        </div>
      </Table>

      <AuthManager
        :DropdownSearchList="DropdownSearchList"
        :activeKey="activeKey"
        ref="authManager"
      ></AuthManager>
      <BatchAuthDrawer :activeKey="activeKey" ref="batchAuth" @refresh="reset"></BatchAuthDrawer>
      <div class="frame-button-wrapper">
        <a-button @click="toBack">返回</a-button>
        <a-button @click="batchAuth">+批量授权用户</a-button>
        <a-button @click="fieldBatchAuth">+批量授权字段</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  addDataSource,
  editDataSource,
  schemaPermissionSwitch,
  fieldPermissionSwitch,
  sensitiveSwitch
} from '@/api/config/dataSource';
import Tabs from '@/components/Tabs';
import Table from '@/components/Table';
import Tag from '@/components/Biz/Tag';
import Shower from '@/components/Shower';
import Status from '@/components/Biz/Status';
import SearchArea from '@/components/SearchArea';
import DbImg from '@/components/CustomImg/DbImg';
import LimitLabel from '@/components/LimitLabel';
import AuthManager from '../components/authManager';
import InputSearch from '@/components/InputSearch';
import BatchAuthDrawer from '../components/batchAuthDrawer';
import config from './config';
import { Base64 } from 'js-base64';
import _ from 'lodash';
import common from '@/utils/common';

export default {
  components: {
    Tag,
    Tabs,
    DbImg,
    Table,
    Shower,
    Status,
    LimitLabel,
    SearchArea,
    AuthManager,
    InputSearch,
    BatchAuthDrawer
  },
  data() {
    this.config = config(this);
    return {
      instanceData: {},
      activeKey: 'tableDetail', // 默认选中 tab
      activeTab: 'field',
      tabsList: [
        {
          tab: '字段',
          key: 'field'
        },
        {
          tab: '索引',
          key: 'index'
        }
      ],
      indexData: [],
      fieldData: [],
      indexParams: {
        url: '/sqlreview/project/index_list',
        reqParams: {},
        columns: this.config.indexColumns,
        rowKey: 'id',
        size: 'small',
        pagination: {
          simple: true
        },
        scroll: { x: 'max-content' }
      },
      fieldSelectedRowKeys: [],
      isFieldSelectAll: false,
      fieldParams: {
        url: '/sqlreview/project/columns_list',
        reqParams: {},
        columns: this.config.fieldColumns,
        rowKey: 'id',
        size: 'small',
        // loaded: this.onFieldTableLoaded,
        pagination: {
          simple: true
        },
        rowSelection: {
          type: 'checkbox', // 多选单选
          columnWidth: '40',
          onSelectAll: this.onFieldSelectAll,
          getCheckboxProps: record => ({
            // 选择框的默认属性配置
            props: {
              disabled: record.permission_status == 0 // 权限控制为关不可勾选
            }
          })
        },
        scroll: { x: 'max-content' }
      },
      dataSource: [],
      tableParams: {
        url: '/sqlreview/project/table_list',
        reqParams: {
          id: this.$route.query.id
        },
        columns: this.config.columns,
        rowKey: 'id',
        // needTools: true,
        // needSearchArea: true,
        // searchFields: this.config.searchFields,
        showHeader: false,
        loaded: this.onTableLoaded,
        // scroll: { x: '1366px' },
        rowSelection: {
          type: 'checkbox', // 多选单选
          columnWidth: '40',
          onSelectAll: this.onSelectAll,
          getCheckboxProps: record => ({
            // 选择框的默认属性配置
            props: {
              disabled: record.permission_status == 0 // 自由操作不需要选中
            }
          })
        },
        scroll: { x: 'max-content' }
      },
      selectedRowKeys: [],
      isSelectAll: false,
      DropdownSearchList: [
        {
          key: 'user_type',
          title: '用户类型',
          default: '全部',
          options: [
            { label: '全部', value: -1 },
            { label: '用户', value: 0 },
            {
              label: '用户组',
              value: 1
            }
          ]
        },
        {
          key: 'permission_type',
          title: '权限类型',
          default: '全部',
          options: [
            { label: '全部', value: -1 },
            { label: '查询', value: 0 },
            {
              label: 'DDL',
              value: 1
            },
            {
              label: 'DML',
              value: 2
            }
          ]
        }
      ],
      selectedObj: {}
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.setNavi();
  },
  methods: {
    // 搜索
    search(params) {
      this.table_name = params;
      // this.$refs.table.refresh(null, { table_name: params });
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        table_name: params
      });
    },
    // 权限控制 启用禁用
    Switch(data) {
      this.$showLoading();
      schemaPermissionSwitch({
        value: data.permission_status === 0 ? 1 : 0,
        id: data.id,
        type: 'table'
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const { table } = this.$refs;
            table.refreshKeep(null, { _clear: true });
            this.selectedRowKeys = [];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 字段权限控制 启用禁用
    fieldSwitch(data, text) {
      this.$showLoading();
      fieldPermissionSwitch({
        value: data.permission_status === 0 ? 1 : 0,
        id: data.id,
        type: 'columns'
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const ref = `field_${text.id}`;
            const field = this.$refs[ref];
            // 单一子表格权限切换，只是清楚该子表格的全选中项
            field.refreshKeep(null, { _clear: true });
            const _id = text.id;
            this.selectedObj[_id] = [];
            let arr = [];
            Object.values(this.selectedObj).forEach(item => {
              arr = [...arr, ...item];
            });
            this.fieldSelectedRowKeys = arr;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 字段脱敏控制 启用禁用
    sensitiveSwitchFn(data, text) {
      this.$showLoading();
      sensitiveSwitch({
        value: data.sensitive_status === 0 ? 1 : 0,
        id: data.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const ref = `field_${text.id}`;
            const field = this.$refs[ref];
            field.refreshKeep();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onTableLoaded(req, res) {
      this.dataSource = res.data.data.results.map(item => {
        return { ...item, _activeTab: 'field' };
      });
      this.instanceData = res.data.data.schema_info;
    },
    // 切换 tab 选项卡
    onChange(activeTab) {
      this.activeTab = activeTab;
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      const fieldsRefs = Object.keys(this.$refs);
      fieldsRefs.forEach(item => {
        if (item.startsWith('field')) {
          this.$refs[item].refreshClear();
        }
      });
      table.refreshClear();
      this.selectedRowKeys = [];
      this.fieldSelectedRowKeys = [];
    },
    // 权限管理
    authManager(record, type) {
      type == 'field'
        ? this.$refs.authManager.show(record, null, type)
        : this.$refs.authManager.show(record, this.instanceData, type);
    },
    batchAuth() {
      if (!this.selectedRowKeys || this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择表');
        return;
      }
      this.$refs.batchAuth.show(
        this.selectedRowKeys,
        this.isSelectAll,
        'tableDetail'
      );
    },
    fieldBatchAuth() {
      if (!this.fieldSelectedRowKeys || this.fieldSelectedRowKeys.length <= 0) {
        this.$message.warning('请选择字段');
        return;
      }
      this.$refs.batchAuth.show(
        this.fieldSelectedRowKeys,
        this.isFieldSelectAll,
        'field'
      );
    },
    toBack() {
      this.$router.push({
        name: 'data-source',
        query: { activeKey: 'database' }
      });
    },
    // 表格全选
    onSelectAll(e) {
      this.isSelectAll = e;
    },
    // 字段表格全选
    onFieldSelectAll(e) {
      this.isFieldSelectAll = e;
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    // 字段勾选值
    fieldSelectChange(text, data = {}) {
      const _id = text.id;
      const _keys = data.selectedRowKeys;
      this.selectedObj[_id] = _keys;
      let arr = [];
      Object.values(this.selectedObj).forEach(item => {
        arr = [...arr, ...item];
      });
      this.fieldSelectedRowKeys = arr;
    },
    // 项目保存
    saveProject(params = {}) {
      const { editModal, table } = this.$refs;
      const { type, data } = params;
      const reqMethod = type === 'add' ? addDataSource : editDataSource;
      // 请求
      this.$showLoading();
      reqMethod({
        url_type: data.type,
        ...data,
        password: Base64.encode(data.password || '')
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            editModal.hide();
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'data-source-config') {
          path = sourcePath + '?activeKey=' + 'database';
        }
        return path;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.config-datasource-table-detail {
  .custom-page-header {
    padding: 16px 0;
    border-bottom: 1px solid #eee;
  }
  .header-info {
    display: flex;
    align-items: center;
    margin-left: 12px;
    /deep/ .database-image .iconClass {
      display: flex;
      align-items: center;
      height: 16px;
      line-height: 16px;
      .anticon {
        padding-top: 0;
      }
    }
  }
  .table-info {
    display: flex;
    > div:nth-child(1) {
      width: 220px;
      margin-right: 12px;
    }
    > div:nth-child(2) {
      width: 120px;
      margin-right: 12px;
    }
    > div:nth-child(3) {
      width: 150px;
    }
  }
  .permission-status-and-action {
    display: flex;
    justify-content: space-around;
    .permission-status {
      margin-right: 12px;
    }
  }
  .inner-table {
    /deep/ .ant-tabs-tabpane {
      display: flex;
      .custom-table {
        flex-grow: 1;
      }
    }
  }
}
</style>
