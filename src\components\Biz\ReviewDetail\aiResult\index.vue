<template>
  <a-card type="small" :bordered="false" class="small-card">
    <div slot="title">
      <a-icon type="robot" />
      <span style="margin-left: 4px;">AI判定结果</span>
    </div>
    <div class="ai-comment-part">
      <div class="result">
        <div class="result-icon">
          <a-icon
            :style="{color: statusColor[dataInfo.ai_status]}"
            :type=" statusType "
            theme="filled"
          />
        </div>
        <div class="result-text">
          <div class="no-pass">{{ dataInfo.ai_status | aiStatus }}</div>
          <!-- SQL改写 -->
          <sqlModify @viewModifyPlan="onViewModifyPlan" v-if="!isTaiLong && flag"></sqlModify>
        </div>
      </div>
      <div class="chart">
        <div class="chart-container">
          <Chart :option="pieOption" />
        </div>
        <div class="chart-level">
          <div class="chart-item">
            <div class="chart-name" v-for="(item, index) in dataInfo.rule_category" :key="index">
              <a-badge :status="item.name| badgeStatus" />
              <span>{{item.name}}</span>
              <span class="nummber" :style="{color: chartLevelColor[item.name] }">{{item.value}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="part">
        <div v-for="(item, index) in dataInfo.ai_comment" :key="index" class="level">
          <span :style="{color: levelColor[item.level] }">【{{ item.level | levelStatus }}】</span>
          <span>{{ item.ai_comment }}</span>
        </div>
        <div v-if="!dataInfo.ai_comment.length" class="part-no">
          <custom-empty />
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
import Chart from '@/components/Chart';
import sqlModify from '@/components/Biz/ReviewDetail/sqlModify';
import config from './config';
export default {
  components: { Chart, sqlModify },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    },
    pieOption: {
      type: Object,
      default: () => {}
    },
    flag: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    statusType() {
      if (this.dataInfo.ai_status === 1 || this.dataInfo.ai_status === 2) {
        return 'check-circle';
      } else if (
        this.dataInfo.ai_status === -1 ||
        this.dataInfo.ai_status === 9
      ) {
        return 'exclamation-circle';
      } else {
        return 'exclamation-circle';
      }
    },
    // 泰隆银行特有
    isTaiLong() {
      const specificConfig = process.channel === 'TaiLongBank';
      return specificConfig;
    }
  },
  data() {
    this.config = config(this);
    return {
      statusColor: this.config.statusColor,
      levelColor: this.config.levelColor,
      chartLevelColor: this.config.chartLevelColor
    };
  },
  methods: {
    // 查看sql改写方案
    onViewModifyPlan() {
      this.$router.push({
        name: 'orderPlan',
        params: {
          id: this.$route.params.id,
          params: this.$route.params.searchData
        }
      });
    }
  },
  filters: {
    levelStatus(value) {
      let obj = {
        0: '未知',
        1: '高风险',
        2: '中风险',
        3: '低风险',
        9: '无风险'
      };
      return obj[value];
    },
    aiStatus(value) {
      let obj = {
        0: '未知',
        1: '通过',
        2: '白名单通过',
        9: '错误',
        '-1': '不通过'
      };
      return obj[value];
    },
    badgeStatus(value) {
      let obj = {
        高风险: 'error',
        中风险: 'processing',
        低风险: 'success'
      };
      return obj[value];
    }
  }
};
</script>

<style lang="less" scoped>
.small-card {
  margin-bottom: 24px;
  .ai-comment-part {
    display: flex;
    align-items: center;
    text-align: center;
    height: 200px;
    .result {
      width: 15%;
      height: 100%;
      padding: 0 12px;
      border-right: 1px solid #ccc;
      display: flex;
      flex-direction: column;
      justify-content: center;
      // align-items: center;
      .result-icon {
        // flex: 1;
        width: 100%;
        text-align: center;
        // margin-left: 4px;
        // position: relative;
        // .anticon {
        //   position: absolute;
        //   left: 50%;
        //   bottom: 0;
        //   transform: translate(-50%, 0%);
        // }
      }
      .anticon-exclamation-circle {
        font-size: 42px;
      }
      .anticon-check-circle {
        font-size: 42px;
      }
      .result-text {
        // flex: 1;
        width: 100%;
        text-align: center;
      }
      .no-pass {
        text-align: center;
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 32px;
        font-weight: 500;
        margin: 8px 0;
      }
    }
    .chart {
      width: 35%;
      height: 100%;
      border-right: 1px solid #ccc;
      display: flex;
      align-items: center;
      .chart-container {
        width: 60%;
        height: 100%;
      }
      .chart-level {
        text-align: left;
        width: 40%;
        // position: relative;
        .chart-item {
          // position: absolute;
          // top: 50%;
          // transform: translateY(-50%);
          font-size: 14px;
          .chart-name {
            margin-bottom: 10px;
            .nummber {
              margin-left: 16px;
            }
          }
        }
      }
    }
    .part {
      position: relative;
      width: 50%;
      height: 100%;
      text-align: left;
      padding: 0 20px;
      .level {
        width: 100%;
        padding: 0;
        margin: 2px 0;
        background-color: transparent;
        color: #0f78fb;
        line-height: 25px;
        .level0 {
          color: #b0aeae;
        }
        .level1 {
          color: #ff4d4f;
        }
        .level2 {
          color: #ff9358;
        }
        .level3 {
          color: #1edfa9;
        }
        .level9 {
          color: #52c41a;
        }
      }
      .part-no {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}
/deep/.ant-badge-status-processing:after {
  -webkit-animation: none;
  animation: none;
}
</style>