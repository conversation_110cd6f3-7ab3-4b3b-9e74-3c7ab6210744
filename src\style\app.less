@import '../assets/fonts/iconfont.css';
// ----------------------- 基础样式 ----------------------- //
body {
  min-width: 1024px;
  font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system,
    BlinkMacSystemFont, 'Segoe UI', 'Hiragino Sans GB', 'Helvetica Neue',
    Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol';

  &.no-select {
    * {
      user-select: none !important;
    }
  }
  &.cursor-copy {
    * {
      cursor: copy !important;
    }
  }

  [type='button'] {
    appearance: none;
  }
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: #d2d2d2;
}
::-webkit-scrollbar-thumb:hover {
  border-radius: 5px;
  background-color: #c2c2c2;
}
::-webkit-scrollbar-track {
  background: #f2f2f2;
}

#app {
  height: 100%;
}
td {
  white-space: pre-wrap;
  word-break: break-all;
}

.CodeMirror {
  box-sizing: content-box;
  * {
    box-sizing: content-box;
  }
}
.CodeMirror-hints {
  z-index: 1000;
}

// 顶部progress样式
#nprogress .bar {
  background: #1890ff;
  height: 4px;
}

// 全局loading
.full-page-loading,
.full-page-loading-container {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2000;

  .ant-spin-dot {
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -0.5rem 0 0 -0.5rem;
  }
}
div.full-page-loading-container {
  transition: opacity 0.3s;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  opacity: 0.15;
  z-index: 1999;

  &.nprogress {
    opacity: 0;
  }
}

// 最老的button样式，目前只有help文件使用，暂时保留不要使用
.btn-green {
  background: #4caf50;
  color: white;
  border: none;
  &:hover,
  &:active,
  &:focus {
    background: #52c41a;
    color: white;
  }
}
.btn-blue {
  background: #2196f3;
  color: white;
  border: none;
  &:hover,
  &:active,
  &:focus {
    background: #03a9f4;
    color: white;
  }
}
.btn-yellow {
  background: #ffc107;
  color: white;
  border: none;
  &:hover,
  &:active,
  &:focus {
    background: #ff9800;
    color: white;
  }
}
.btn-red {
  background: #e26148;
  color: white;
  border: none;
  &:hover,
  &:active,
  &:focus {
    background: #f44336;
    color: white;
  }
}

// ----------------------- 公用样式 ----------------------- //
// 隐藏
.hide {
  display: none;
}

// tips清爽模式
.tooltip-weak {
  font-size: 12px;
  padding: 0;

  .ant-tooltip-arrow {
    display: none;
  }
  .ant-tooltip-inner {
    min-height: 0;
    background-color: #9a9a9a;
    padding: 4px 6px;
  }
}

// 通用盒子
.common-box {
  background: #fff;
  padding: 24px;
  margin-bottom: 16px;
}

// 通用页面card样式
.common-page-card {
  > .ant-card-head {
    min-height: 0;
    padding: 0 0px;
    border-bottom: none;

    > .ant-card-head-wrapper {
      > .ant-card-head-title,
      > .ant-card-extra {
        padding: 0 0 16px 0;
        line-height: 32px;
      }
    }
  }
  > .ant-card-body {
    padding: 0;
  }
  &.head-bordered {
    > .ant-card-head {
      border-bottom: 1px solid #e8e8e8;
    }
  }
}
// 通用盒子card样式
.common-box-card {
  background-color: #ffffff;
  border: 1px solid;
  border-color: #e7f0ff;
  border-radius: 2px;
  box-shadow: 0px 1px 8px rgba(26, 26, 29, 0.07);
  margin-bottom: 24px;
  .common-box-card-title {
    font-size: 14px;
    i {
      color: #1890ff;
      margin-right: 4px;
    }
  }
  .ant-card-head-title {
    font-size: 14px;

    span.card-title {
      .anticon {
        margin-right: 8px;
        color: @primary-color;
      }
    }
  }
}
.common-pure-card {
  background-color: #ffffff;
  border-radius: 16px;
  margin-bottom: 16px;
  > .ant-card-body {
    padding: 32px;
  }
  .title {
    font-size: 16px;
    color: #27272a;
    font-weight: 600;
    margin-bottom: 12px;
    display: block;

    .anticon {
      margin-right: 8px;
    }
  }
}
// 背景
.custom-bg {
  border-radius: 8px;
  border: 1px solid #f2f2f2;
  // box-shadow: 2px 2px 2px #f1f5ff;
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.05);
}
.custom-pure-bg {
  background-color: #ffffff;
  border-radius: 16px;
  margin-bottom: 16px;
  padding: 32px;
}

// 元素禁用
.ele-disabled-normal {
  position: relative;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #f5f5f5;
    opacity: 0.5;
    z-index: 10;
    border-radius: 2px;
    cursor: not-allowed;
  }
}
// 块禁用（透明）
.view-disabled {
  position: relative;
  &::before {
    content: '';
    display: block;
    position: absolute;
    z-index: 10;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    cursor: not-allowed !important;
  }
}

// a标签
a.remove {
  color: @primary-red;
}

// 不换行
.no-wrap {
  white-space: nowrap;
}

// frame右上角按钮wrapper
.frame-button-wrapper {
  position: absolute;
  right: 24px;
  top: 12px;
  z-index: 10;
  background: #f9f9f9;
}
.frame-button-wrapper-relative {
  position: absolute;
  right: -24px;
  top: -64px;
  z-index: 10;
  background: #f9f9f9;
}
.frame-button-wrapper-relative-blank {
  position: absolute;
  right: 0px;
  top: -40px;
  z-index: 10;
  background: #f9f9f9;
}

// 列表页
div.page-list-single {
  .search-area {
    // border: 0;
    box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.3);
    // padding: 20px 16px 8px 16px;
    padding: 20px 24px 8px 24px;
    // margin-bottom: 16px;

    // &.need-toggle {
    //   .seach-area-btns {
    //     bottom: 46px;
    //   }
    // }
    // &.need-toggle.expanded {
    //   .seach-area-btns {
    //     bottom: 40px;
    //   }
    // }
  }
}
div.page-list-with-tabs {
  .ant-tabs-top-bar {
    margin-bottom: 0;
  }
  .search-area {
    box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.3);
    padding: 20px 24px 8px 24px;
    margin-bottom: 16px;

    // .seach-area-btns {
    //   bottom: 22px;
    // }

    // &.is-full-line {
    //   .seach-area-btns {
    //     bottom: 16px;
    //   }
    // }

    // &.need-toggle {
    //   .seach-area-btns {
    //     bottom: 40px;
    //   }
    // }
  }
}

// form横向布局文本宽度范围
.formRangeLabel(@min: auto, @max: auto) {
  .ant-form-item {
    display: flex !important;
    align-items: flex-start;
  }
  .ant-form-item-label {
    max-width: @max;
    min-width: @min;
  }
  .ant-form-item-control-wrapper {
    flex-grow: 1;
    flex: auto;
    width: auto;
  }
}

// ----------------------- 覆盖样式 ----------------------- //
.ant-table {
  &.ant-table-bordered {
    div.ant-table-content .ant-table-tbody tr td {
      border-left: 0;
      border-bottom: 1px solid #e8e8e8;
    }
  }
  .ant-table-content {
    th {
      color: rgba(0, 0, 0, 0.65) !important;
      // background:#f8faff;
      background: rgba(0, 0, 0, 0.02);
      background-color: rgba(0, 0, 0, 0.02) !important;
      // text-align: center;
    }
    td {
      padding: 16px;
      color: rgba(0, 0, 0, 0.65) !important;
      // border-bottom: 1px solid #e9e9e9;
      border-bottom: 0;
    }
    tbody {
      tr:last-child {
        td {
          border-bottom: 1px solid #f3f3f3;
        }
      }
    }
    .ant-table-scroll {
      td {
        .ant-tag {
          white-space: pre-wrap;
          margin-bottom: 4px;
        }
      }
    }

    .database-image {
      img {
        width: 80px;
      }
    }
  }
}

// 小表格表头左右外边距
.ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0;
}

// 小表格无边框
.ant-table-small {
  border: none;
}

// radio 可选 颜色
.ant-radio-inner {
  border-color: #666;
}
.ant-radio-group-small {
  font-size: 12px;
}

// tooltip 最大宽度
.ant-tooltip {
  max-width: 400px;
}

// popover美化
.ant-popover {
  .ant-popover-inner-content {
    max-width: 800px;
    max-height: 400px;
    overflow: auto;
    word-break: break-all;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 4px;

    pre:not([class*='CodeMirror']) {
      overflow: visible;
      white-space: pre-wrap;
      font-size: 12px;
      margin-bottom: 0;
    }
  }
}

// 表格列最小宽度兼容
colgroup col {
  min-width: 0 !important;
}

// 按钮
.ant-btn {
  border-radius: 6px;
}
.ant-btn-sm {
  font-size: 12px;
}
.ant-btn-group {
  .ant-btn {
    border-radius: 2px !important;
  }
}
.ant-btn-primary {
  // border: none;
  // background-image: @primary-background-image;
}
// 高亮按钮(不需要type)
.ant-btn.highlight {
  color: @primary-color;
  border-color: #7fc4ed;
  font-weight: 600;
  &:hover {
    color: @primary-5;
    border-color: @primary-5;
  }
}
.ant-btn.agree {
  border-color: @primary-green;
  background: @primary-green;
  color: #fff;
  &:hover {
    background: fade(@primary-green, 80%);
  }
}
.ant-btn[disabled] {
  color: rgba(0, 0, 0, 0.25) !important;
  background: #f5f5f5 !important;
  border: 1px solid #d9d9d9 !important;
}

// 弹窗
.ant-modal-root {
  .ant-modal-content {
    .ant-modal-close-x {
      // color: #ffffff;
    }
  }
  .ant-modal-header {
    // border-bottom: none;
    // background: @primary-color;
    // background-image: @primary-background-image;
    .ant-modal-title {
      // color: #ffffff;
    }
  }

  .ant-modal-wrap.big-title {
    .ant-modal-header {
      .ant-modal-title {
        font-size: 20px;
      }
    }
  }
}

// step箭头样式
.ant-steps.arrow {
  @itemHeight: 40px;
  @arrowWidth: 16px;
  .ant-steps-item {
    display: flex;
    justify-content: center;
    // padding: 8px 0;
    background: #ffffff;
    margin-right: 40px !important;
    flex-grow: 1;
    flex: auto;
    overflow: visible;

    .ant-steps-item-container {
      flex-grow: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: 24px;
      box-shadow: 0px 2px 4px 1px rgba(56, 45, 45, 0.15);
      height: 40px;
    }

    .ant-steps-item-title {
      &::after {
        display: none;
      }
    }

    &::before {
      content: '';
      display: block;
      width: 30px;
      height: 30px;
      left: -14px;
      top: 6px;
      box-shadow: inset 2px 2px 3px 0 rgba(200, 200, 200, 0.5);
      transform: rotate(135deg);
      background: #ffffff;
      position: absolute;
    }

    &::after {
      content: '';
      display: block;
      width: 28px;
      height: 28px;
      bottom: 6px;
      right: -14px;
      box-shadow: 2px 2px 3px 0 rgba(200, 200, 200, 0.5);
      transform: rotate(-45deg);
      background: #ffffff;
      position: absolute;
      z-index: 1;
    }

    &:first-child {
      &::before {
        display: none;
      }
      .ant-steps-item-container {
        padding-left: @arrowWidth;
      }
    }

    &:last-child {
      &::after {
        display: none;
      }
      .ant-steps-item-container {
        padding-right: @arrowWidth;
      }
      margin-right: 0 !important;
    }

    @waitColor: #ffffff;
    &.ant-steps-item-wait {
      background: @waitColor;
      // background: #FFFFFF;
      // box-shadow: 0px 2px 4px 0px rgba(200,200,200,0.5);
      &::after {
        border-color: transparent transparent transparent @waitColor;
      }
      .ant-steps-item-title {
        color: rgba(0, 0, 0, 0.65);
      }

      .ant-steps-item-icon {
        background: #f9f9f9;
        border-color: transparent;
        .ant-steps-icon {
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }

    @finishColor: #e8e8e8;
    &.ant-steps-item-finish {
      background: @finishColor;
      &::after {
        border-color: transparent transparent transparent @finishColor;
        background: @finishColor;
      }
      .ant-steps-item-title {
        color: @primary-6;
      }

      .ant-steps-item-icon {
        background: transparent;
        border-color: @primary-6;
        .ant-steps-icon {
          color: @primary-6;
        }
      }
    }

    @activeColor: @primary-6;
    &.ant-steps-item-active {
      background: @activeColor;
      &::after {
        border-color: transparent transparent transparent @activeColor;
        background: @activeColor;
      }

      .ant-steps-item-title {
        color: #fff;
      }

      .ant-steps-item-icon {
        background: #fff;
        .ant-steps-icon {
          color: @activeColor;
        }
      }
    }
  }
}

// message样式
.ant-message-notice-content {
  max-width: 70%;
}

// notice样式
.ant-notification-notice-with-icon .ant-notification-notice-description {
  word-break: break-word;
}

// 评分
.ant-rate {
  color: #f7b500;
  // 不缩放
  &.no-scale {
    .ant-rate-star {
      > div {
        transform: scale(1) !important;
      }
    }
  }
}

// 通用千分位样式
.ant-statistic-content {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  .ant-statistic-content-value-decimal {
    font-size: 14px;
  }
}

// v-tooltip基础样式
.tooltip.vue-tooltip-theme,
.tooltip.vue-popover-theme {
  display: block !important;
  z-index: 1051;

  .tooltip-inner {
    background: black;
    color: white;
    border-radius: 4px;
    padding: 5px 10px 4px;
  }

  .tooltip-arrow {
    width: 0;
    height: 0;
    border-style: solid;
    position: absolute;
    margin: 5px;
    border-color: black;
    z-index: 1;
  }

  &[x-placement^='top'] {
    margin-bottom: 5px;

    .tooltip-arrow {
      border-width: 5px 5px 0 5px;
      border-left-color: transparent !important;
      border-right-color: transparent !important;
      border-bottom-color: transparent !important;
      bottom: -5px;
      left: calc(50% - 5px);
      margin-top: 0;
      margin-bottom: 0;
    }
  }

  &[x-placement^='bottom'] {
    margin-top: 5px;

    .tooltip-arrow {
      border-width: 0 5px 5px 5px;
      border-left-color: transparent !important;
      border-right-color: transparent !important;
      border-top-color: transparent !important;
      top: -5px;
      left: calc(50% - 5px);
      margin-top: 0;
      margin-bottom: 0;
    }
  }

  &[x-placement^='right'] {
    margin-left: 5px;

    .tooltip-arrow {
      border-width: 5px 5px 5px 0;
      border-left-color: transparent !important;
      border-top-color: transparent !important;
      border-bottom-color: transparent !important;
      left: -5px;
      top: calc(50% - 5px);
      margin-left: 0;
      margin-right: 0;
    }
  }

  &[x-placement^='left'] {
    margin-right: 5px;

    .tooltip-arrow {
      border-width: 5px 0 5px 5px;
      border-top-color: transparent !important;
      border-right-color: transparent !important;
      border-bottom-color: transparent !important;
      right: -5px;
      top: calc(50% - 5px);
      margin-left: 0;
      margin-right: 0;
    }
  }

  &.popover {
    // @color: #f9f9f9;
    @color: #ffffff;

    .popover-inner {
      background: @color;
      color: black;
      padding: 24px;
      border-radius: 5px;
      box-shadow: 0 5px 30px rgba(black, 0.1);
    }

    .popover-arrow {
      border-color: @color;
    }
  }

  &[aria-hidden='true'] {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.15s, visibility 0.15s;
  }

  &[aria-hidden='false'] {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.15s;
  }
}

// tabs切换 设置animated=false 或者type=card时，取消切换效果
.ant-tabs {
  .ant-tabs-content-no-animated > .ant-tabs-tabpane {
    transition: none;
  }
  &.ant-tabs-no-animation .ant-tabs-tab {
    transition: none;
  }
}

// 旋转loading
// .loading-rotate {
//   animation: rotate360 1s linear infinite;
//   // animation: rotate360 1s steps(64) infinite;
// }
// @keyframes rotate360 {
//   /* 起始点是0秒，此处可省略动画起点from */
//   to {
//       /* 避免translate被覆盖，此处可再添加translate */
//       transform: rotate(360deg);
//   }
// }
