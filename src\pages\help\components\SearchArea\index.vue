<template>
  <SearchArea :fields="fields" :multiCols="3" @reset="reset" @search="search"></SearchArea>
</template>

<script>
import SearchArea from '@/components/SearchArea';
import config from './config';

export default {
  components: { SearchArea },
  props: {},
  data() {
    this.config = config(this);
    return {
      fields: this.config.fields
    };
  },
  mounted() {},
  created() {},
  methods: {
    reset() {
      console.log('reset');
    },
    search(data) {
      console.log('search', data);
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>