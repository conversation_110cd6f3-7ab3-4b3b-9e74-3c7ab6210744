export default function (ctx) {
  // const follow = {
  //   columns: [
  //     {
  //       title: 'ID',
  //       dataIndex: 'id',
  //       key: 'id',
  //       width: 100
  //     },
  //     {
  //       title: '应用名称',
  //       dataIndex: 'project_name',
  //       key: 'project_name',
  //       width: 100
  //     },
  //     {
  //       title: '项目组',
  //       dataIndex: 'group',
  //       key: 'group',
  //       width: 200,
  //       scopedSlots: { customRender: 'group' }
  //     },
  //     {
  //       title: '关注人数',
  //       dataIndex: 'subscribe_count',
  //       key: 'subscribe_count',
  //       sorter: true,
  //       width: 100
  //     },
  //     {
  //       title: '状态',
  //       dataIndex: 'subscribe_status',
  //       key: 'subscribe_status',
  //       scopedSlots: { customRender: 'subscribe_status' },
  //       width: 100
  //     },
  //     {
  //       title: '操作',
  //       key: 'action',
  //       scopedSlots: { customRender: 'action' },
  //       width: 100
  //     }
  //   ],
  //   searchFields: [
  //     {
  //       type: 'Input',
  //       label: 'ID',
  //       key: 'id',
  //       props: {
  //         placeholder: '请输入ID'
  //       }
  //     },
  //     {
  //       type: 'Input',
  //       label: '应用名称',
  //       mainSearch: true,
  //       key: 'project_name',
  //       props: {
  //         placeholder: '请输入应用名称'
  //       }
  //     },
  //     {
  //       type: 'Input',
  //       label: '项目组',
  //       key: 'group',
  //       props: {
  //         placeholder: '请输入项目组'
  //       }
  //     },
  //     {
  //       type: 'Select',
  //       label: '状态',
  //       key: 'subscribe_status',
  //       props: {
  //         placeholder: '请选择状态',
  //         options: [
  //           {
  //             label: '未关注',
  //             value: 0
  //           },
  //           {
  //             label: '已关注',
  //             value: 1
  //           }
  //         ]
  //       }
  //     }
  //   ]
  // }

  // const userGroup = {
  //   columns: [
  //     {
  //       title: 'ID',
  //       dataIndex: 'id',
  //       key: 'id',
  //       width: 100
  //     },
  //     {
  //       title: '应用名称',
  //       dataIndex: 'project_name',
  //       key: 'project_name',
  //       width: 150
  //     },
  //     {
  //       title: '项目组',
  //       dataIndex: 'group',
  //       key: 'group',
  //       width: 200,
  //       scopedSlots: { customRender: 'group' }
  //     },
  //     {
  //       title: '数据源',
  //       dataIndex: 'data_source_list',
  //       key: 'data_source_list',
  //       width: 150,
  //       scopedSlots: { customRender: 'data_source_list' }
  //     },
  //     {
  //       title: '最后一次review时间',
  //       dataIndex: 'last_review_time',
  //       key: 'last_review_time',
  //       width: 150
  //     }
  //   ].map(item => {
  //     return {
  //       ...item,
  //       width: undefined
  //     }
  //   }),
  //   searchFields: [
  //     {
  //       type: 'Input',
  //       label: 'ID',
  //       key: 'id',
  //       props: {
  //         placeholder: '请输入ID'
  //       }
  //     },
  //     {
  //       type: 'Input',
  //       label: '应用名称',
  //       mainSearch: true,
  //       key: 'project_name',
  //       props: {
  //         placeholder: '请输入应用名称'
  //       }
  //     },
  //     {
  //       type: 'Input',
  //       label: '项目组',
  //       key: 'group',
  //       props: {
  //         placeholder: '请输入项目组'
  //       }
  //     }
  //     // {
  //     //   type: 'Select',
  //     //   label: '数据源',
  //     //   key: 'data_source_id',
  //     //   props: {
  //     //     url: '/sqlreview/project/data_source_choices',
  //     //     allowSearch: true,
  //     //     backSearch: true,
  //     //     limit: 50
  //     //     // disabled: ctx.isDisabled
  //     //   }
  //     // }
  //   ]
  // }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: '项目名',
      dataIndex: 'project_name',
      key: 'project_name'
    },
    {
      title: '数据源',
      dataIndex: 'data_source_list',
      key: 'data_source_list',
      scopedSlots: { customRender: 'data_source_list' }
    },
    {
      title: '累计SQL总数',
      dataIndex: 'sql_count',
      key: 'sql_count',
      sorter: true
    },
    {
      title: '白名单',
      dataIndex: 'whitelist_sql',
      key: 'whitelist_sql',
      sorter: true
    },
    {
      title: '整改中',
      dataIndex: 'modifying_sql',
      key: 'modifying_sql',
      sorter: true
    },
    {
      title: '高风险',
      dataIndex: 'high_risk',
      key: 'high_risk',
      sorter: true
    },
    {
      title: '低风险',
      dataIndex: 'lower_risk',
      key: 'lower_risk',
      sorter: true
    },
    {
      title: '无风险',
      dataIndex: 'no_risk',
      key: 'no_risk',
      sorter: true
    },
    {
      title: '异常',
      dataIndex: 'error',
      key: 'error',
      sorter: true
    },
    {
      title: '最后一次审核时间',
      dataIndex: 'last_review_time',
      key: 'last_review_time',
      sorter: true,
      scopedSlots: { customRender: 'last_review_time' }
    }
  ]

  const fields = [
    {
      type: 'Select',
      label: '项目组',
      key: 'group_id',
      props: {
        placeholder: '该项必填,默认第一个',
        url: '/sqlreview/subscribe/user_groups',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'Input',
      label: 'ID/项目名',
      key: 'project'
    },
    {
      type: 'Input',
      label: '',
      key: 'btns',
      hideComponent: true,
      slots: [{ key: 'btns' }],
      rules: []
    }
  ];

  return {
    // columns: (isFollow) => {
    //   return isFollow ? follow.columns : userGroup.columns;
    // },
    // searchFields: (isFollow) => {
    //   return isFollow ? follow.searchFields : userGroup.searchFields;
    // },
    columns,
    fields
  };
}
