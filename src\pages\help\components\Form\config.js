export default function (ctx) {
  const fields = [
    {
      type: 'Input',
      label: '测试input',
      key: 'testSearch',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    () => {
      return {
        type: 'Select',
        label: '测试select',
        key: 'testSelect',
        width: 300,
        props: {
          url: '/api/home/<USER>/select',
          reqParams: {
            from: 'pageListAllConfig'
          }
        },
        listeners: {
          change: (value) => {
            const visible = value === 0 ? undefined : false;
            ctx.$set(ctx.params.fields[2], 'visible', visible);
            console.log(ctx.$refs.form1.getData())
          }
        },
        tips: 'dfjkdjfkdjfkdjfkdjf',
        // tipsPlacement: 'end',
        tipsInfo: {
          // mode: 'expanded'
        }
      }
    },
    {
      type: 'Input',
      label: '项目编号|项目名|app|创建人UM|项目编号|项目名|app|创建人UM',
      key: 'langInput',
      width: '80%',
      visible: false
    },
    {
      type: 'InputNumRange',
      label: '测试InputNumRange',
      key: 'InputNumRange',
      width: '80%',
      props: {
        // size: 'small'
      },
      rules: [{
        validator: function (rule, value, callback) {
          if (value) {
            if (value[0] == null || value[1] == null) {
              callback(new Error('有输入项为空'));
            }
            if (value[0] >= value[1]) {
              callback(new Error('最小值不能大于等于最大值'));
            }
            callback();
          } else {
            callback(new Error('有输入项为空'));
          }
        }
      }]
    },
    {
      type: 'YearPicker',
      label: '年picker',
      key: 'YearPicker',
      width: 300,
      props: {},
      listeners: {
        change(val) {
          // console.log(val, 8989)
        }
      }
    },
    {
      type: 'Coder',
      label: 'Coder',
      key: 'Coder',
      // width: 300,
      props: {
        // class: 'default-full-white',
        options: {
          // theme: 'default',
          gutters: [
            'CodeMirror-linenumbers',
            'CodeMirror-foldgutter',
            'CodeMirror-errTips'
            // 'CodeMirror-lint-markers'
          ]
          // readOnly: true
        },
        // height: 'auto',
        needFormat: true,
        tools: [
          { key: 'fullscreen' },
          { key: 'reload', icon: 'reload', tooltip: '刷新', callback(coder, instance) { console.log(coder, 'hhhhh'); } },
          { key: 'undo', icon: 'undo', callback(coder, instance) { instance && instance.doc.undo(); } },
          { key: 'redo', icon: 'redo', callback(coder, instance) { instance && instance.doc.redo(); } }
        ],
        toolsFixed: true
      },
      listeners: {
        change(val) {
          // console.log(val, 8989)
        }
      }
    },
    {
      type: 'RadioGroup',
      key: 'RG',
      label: 'RadioGroup',
      width: 300,
      props: {
        mode: 'tips', // normal, tips, lineTips, button, buttonAlone, icon
        options: [{ label: 111, value: 1, tips: 'dkfjdkfjdkfjkd' }, { label: 222, value: 2, tips: 'bbbbbbbbbbbbbb' }, { label: 333, value: 3, tips: 'nnnnnnnnnnnnn' }]
      }
    },
    {
      type: 'RadioGroup',
      key: 'RG1',
      label: '单选button类型',
      props: {
        mode: 'button',
        options: [
          { label: '数据修改', value: 1 },
          { label: '结构变更', value: 2 },
          { label: '序 列', value: 3 },
          { label: '可编程对象', value: 4 }
        ]
      }
    },
    {
      type: 'RadioGroup',
      key: 'RG2',
      label: '单选buttonAlone类型',
      props: {
        mode: 'buttonAlone',
        options: [
          { label: '数据修改', value: 1 },
          { label: '结构变更', value: 2 },
          { label: '序 列', value: 3 },
          { label: '可编程对象', value: 4 }
        ]
      }
    },
    {
      type: 'RichEditor',
      key: 'RichEditor',
      label: 'RichEditor',
      // width: 300,
      props: {
        options: {
          height: 200
        }
      }
    },
    {
      type: 'Cascader',
      key: 'Cascader',
      label: 'Cascader',
      // width: 300,
      props: {
        // options: [
        //   {
        //     a: '111',
        //     b: '111',
        //     children: [
        //       { a: '111-1', b: '111' },
        //       { a: '111-2', b: '111-2' },
        //       { a: '111-3', b: '111-3' }
        //     ]
        //   },
        //   { a: '222', b: '222' },
        //   { a: '333', b: '333' }
        // ],
        // options: [
        //   {
        //     label: '111',
        //     value: '111',
        //     children: [
        //       { label: '111-1', value: '111' },
        //       { label: '111-2', value: '111-2' },
        //       { label: '111-3', value: '111-3' }
        //     ]
        //   },
        //   { label: '222', value: '222' },
        //   { label: '333', value: '333' }
        // ],
        url: '/api/home/<USER>/cascader',
        reqParams: (pnode = {}) => {
          return {
            default: 'df',
            relation: pnode.value
          }
        }
      }
    },
    {
      type: 'InputSelect',
      key: 'InputSelect',
      label: 'InputSelect',
      // width: 300,
      props: {
        options: [
          { label: 'varchar(100)', value: 'varchar(100)' },
          { label: 'varchar(20)', value: 'varchar(20)' },
          { label: 'int(10,2)', value: 'int(10,2)' }
        ]
        // url: '/api/home/<USER>/select',
        // reqParams: {
        //   from: 'pageListAllConfig'
        // }
      }
    },
    // {
    //   type: 'UserRoleModal',
    //   label: 'UserRoleModal',
    //   key: 'UserRoleModal',
    //   width: 300,
    //   props: {},
    //   listeners: {
    //     change(val) {
    //       // console.log(val, 8989)
    //     }
    //   },
    //   rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    // }
    {
      type: 'Email',
      key: 'Email',
      label: 'Email',
      width: 400,
      props: {
        suffixOptions: [
          {label: '@lu.com', value: '@lu.com'},
          {label: '@qq.com', value: '@qq.com'},
          {label: '@baidu.com', value: '@baidu.com'}
        ],
        suffixProps: {
          style: { width: '153px' }
        }
      }
    },
    {
      type: 'Markdown',
      key: 'Markdown',
      label: 'Markdown',
      props: {
      }
    }
  ];
  const fields1 = [
    {
      type: 'Input',
      label: '测试input',
      key: 'testSearch',
      initialValue: 'initialValue test',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Select',
      label: '测试select',
      key: 'testSelect',
      props: {
        url: '/api/home/<USER>/select',
        reqParams: {
          from: 'pageListAllConfig'
        }
      }
    },
    {
      type: 'Input',
      label: '项目编号|项目名|app|创建人UM|项目编号|项目名|app|创建人UM',
      key: 'langInput'
    },
    {
      type: 'Input',
      label: '测试input',
      key: 'testSearch1'
    },
    {
      type: 'Select',
      label: '测试select',
      key: 'testSelect1',
      props: {
        url: '/api/home/<USER>/select',
        reqParams: {
          from: 'pageListAllConfig'
        }
      }
    },
    {
      type: 'Input',
      label: '$blank',
      hidden: true
    },
    {
      type: 'Input',
      label: '项目编号|项目名|app|创建人UM|项目编号|项目名|app|创建人UM',
      key: 'langInput1'
    },
    {
      type: 'TreeSelect',
      label: 'TreeSelect测试',
      key: 'TreeSelect',
      props: {
        treeCheckable: true,
        showSearch: true,
        treeData: [
          { value: '1', label: '1', children: [{ value: '1-1', label: '1-1', children: [] }, { value: '1-2', label: '1-2', children: [] }] }
        ]
      },
      listeners: {
        change(e) {
          console.log(e)
        }
      }
    }
    // {
    //   type: 'Select',
    //   label: '测试select',
    //   key: 'testSelect',
    //   props: {
    //     // url: '/api/home/<USER>/select',
    //     url: '/bettle/project/get-schemas',
    //     reqParams: {
    //       from: 'pageListAllConfig',
    //       project_id: 378
    //     },
    //     allowSearch: true,
    //     labelInValue: true,
    //     mode: 'multiple',
    //     labelKey: 'schema_name',
    //     valueKey: 'id',
    //     backSearch: true
    //   }
    // }
  ];
  const radioFormConfig = [
    {
      label: 'git tag',
      key: 'git tag',
      form: [
        {
          type: 'Input',
          label: '测试input',
          key: 'testSearch',
          rules: [
            { required: true, message: '该项为必填项', trigger: 'blur' }
          ]
        },
        {
          type: 'Select',
          label: '测试select',
          key: 'testSelect',
          // width: 300,
          props: {
            url: '/api/home/<USER>/select',
            reqParams: {
              from: 'pageListAllConfig'
            }
          }
        }
      ]
    },
    {
      label: 'svn',
      key: 'svn',
      form: [
        {
          type: 'Select',
          label: '测试select1',
          key: 'testSelect1',
          tips: '<div>这是xxxx</div>dfkdfjkdfjkdf',
          // width: 300,
          props: {
            url: '/api/home/<USER>/select',
            reqParams: {
              from: 'pageListAllConfig'
            }
          }
        }
      ],
      active: true
    }
  ];
  return {
    fields,
    fields1,
    radioFormConfig
  };
};