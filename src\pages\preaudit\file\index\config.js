export default function (ctx) {
  const statusColor = {
    待评审: '#FC925E',
    评审中: '#4F96FB',
    已通过: '#6EC84A',
    未通过: '#F9676B',
    未提交: '#AFADAD'
  };
  const cardColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sorter: true,
      width: 100,
      colSpan: 10,
      scopedSlots: { customRender: 'cardTable' }
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });

  const listColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sorter: true,
      scopedSlots: { customRender: 'id' },
      width: 100
    },
    {
      title: '任务名称',
      dataIndex: 'review_name',
      key: 'review_name',
      scopedSlots: { customRender: 'review_name' },
      width: 150
    },
    {
      title: '文件名',
      dataIndex: 'review_point',
      key: 'review_point',
      scopedSlots: { customRender: 'review_point' },
      width: 150
    },
    {
      title: 'AI审核',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'status' },
      width: 100
    },
    {
      title: 'SQL数量',
      dataIndex: 'sql_count',
      key: 'sql_count',
      sorter: true,
      scopedSlots: { customRender: 'sql_count' },
      width: 100
    },
    {
      title: 'AI通过率',
      key: 'passing_rate',
      sorter: true,
      dataIndex: 'passing_rate',
      width: 130
    },
    {
      title: '审核耗时',
      key: 'ai_review_total_time',
      dataIndex: 'ai_review_total_time',
      customRender: (text) => {
        return text ? text + '分钟' : '--';
      },
      width: 120
    },
    {
      title: '审核方式',
      key: 'audit_type',
      dataIndex: 'audit_type',
      customRender: (text) => {
        return text == 'online' ? '在线' : '离线';
      },
      width: 120
    },
    {
      title: '发起人',
      dataIndex: 'created_by',
      key: 'created_by',
      scopedSlots: { customRender: 'created_by' },
      width: 180
    },
    {
      title: '发起时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 180,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = [
    {
      type: 'Input',
      label: '任务名称/ID',
      key: 'id',
      props: {
        placeholder: '搜索任务名称/ID'
      }
    },
    {
      type: 'Input',
      label: '文件名',
      key: 'project_name'
    },
    {
      type: 'Select',
      label: 'AI审核',
      key: 'status',
      props: {
        options: [
          {
            label: '队列中',
            value: '0'
          },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '未通过',
            value: '-1,2'
          },
          {
            label: 'Review失败',
            value: '9'
          },
          {
            label: '拉取代码中',
            value: '3'
          },
          {
            label: '代码解析中',
            value: '4'
          },
          {
            label: '代码审核中',
            value: '5'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '审核方式',
      key: 'audit_type',
      props: {
        options: [
          {
            label: '在线',
            value: 'online'
          },
          {
            label: '离线',
            value: 'offline'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '发起人',
      key: 'created_by',
      props: {
        url: '/sqlreview/review/select_user',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 50
      }
    },
    {
      type: 'RangePicker',
      label: '发起时间',
      key: 'created_at',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    },
    {
      type: 'Input',
      label: '',
      key: 'btns',
      hideComponent: true,
      slots: [{ key: 'btns' }],
      rules: []
    }
  ];
  return {
    cardColumns,
    listColumns,
    statusColor,
    searchFields: fields
  };
}
