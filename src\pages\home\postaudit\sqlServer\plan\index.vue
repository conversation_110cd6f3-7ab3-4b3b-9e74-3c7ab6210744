<template>
  <a-spin :spinning="spinning">
    <a-card type="small" class="a-card-header">
      <div class="content">
        <div class="title">
          <!-- <div class="title-project">SQLID</div> -->
          <!-- <div class="title-project-name">{{ systemInfo.query_id || '--' }}</div> -->
        </div>
        <div>
          <a-button class="review-detail-toback-btn" @click="toBack">返回</a-button>
          <a-button :disabled="dataInfo.index === 1" @click="onPrev">
            <a-icon type="double-left" />prev
          </a-button>
          <a-button
            :disabled="dataInfo.count === dataInfo.index"
            style="margin-left: 8px"
            @click="onNext"
          >
            next
            <a-icon type="double-right" />
          </a-button>
          <span class="pageInfo">{{ dataInfo.index }}/{{ dataInfo.count }}</span>
        </div>
      </div>
    </a-card>
    <!-- AI判定结果 -->
    <aiResult :dataInfo="dataInfo"></aiResult>
    <!-- SQL执行指标 -->
    <a-card type="small" class="sql-text-card a-card body-12">
      <div class="title" slot="title">
        <a-icon type="solution" />
        <span style="margin-left: 4px">SQL执行指标</span>
      </div>
      <div>
        <div class="common-content system-info">
          <!-- <div>
            <span>执行次数：</span>
            <a-statistic :value="systemInfo.calls" :precision="2"></a-statistic>
          </div>
          <div>
            <span>更新时间：</span>
            <DateFormat :text="systemInfo.created_at" :limit="20" />
          </div>
          <div>
            <span>用户名：</span>
            <a-statistic :value="systemInfo.username " :precision="2"></a-statistic>
          </div>
          <div>
            <span>数据库名：</span>
            <a-statistic :value="systemInfo.db_name" :precision="2"></a-statistic>
          </div>
          <div>
            <span>总耗时(ms)：</span>
            <a-statistic :value="systemInfo.total_exec_time" :precision="2"></a-statistic>
          </div>
          <div>
            <span>平均耗时(ms)：</span>
            <a-statistic :value="systemInfo.mean_plan_time" :precision="2"></a-statistic>
          </div>
          <div>
            <span>读IO耗时(ms)：</span>
            <a-statistic :value="systemInfo.blk_read_time" :precision="2"></a-statistic>
          </div>
          <div>
            <span>写IO耗时(ms)：</span>
            <a-statistic :value="systemInfo.blk_write_time" :precision="2"></a-statistic>
          </div>
          <div>
            <span>单次最长时间(ms)：</span>
            <a-statistic :value="systemInfo.max_exec_time" :precision="2"></a-statistic>
          </div>
          <div>
            <span>单次最短时间(ms)：</span>
            <a-statistic :value="systemInfo.min_exec_time" :precision="2"></a-statistic>
          </div>-->
          <div>
            <span>执行总次数</span>
            <a-statistic :value="systemInfo.in_execution_count"></a-statistic>
          </div>
          <div>
            <span>最近一次执行时间</span>
            <DateFormat :text="systemInfo.last_execution_time" :limit="20" />
          </div>
          <div>
            <span>总耗时(ms)</span>
            <a-statistic :value="systemInfo.in_total_time" :precision="2"></a-statistic>
          </div>
          <div>
            <span>最近一次耗时(ms)</span>
            <a-statistic :value="systemInfo.last_elapsed_time" :precision="2"></a-statistic>
          </div>
          <div>
            <span>总影响行数</span>
            <a-statistic :value="systemInfo.total_rows"></a-statistic>
          </div>
          <div>
            <span>最近一次影响行数</span>
            <a-statistic :value="systemInfo.last_rows"></a-statistic>
          </div>
          <div>
            <span>CPU总耗时(ms)</span>
            <a-statistic :value="systemInfo.in_total_worker_time" :precision="2"></a-statistic>
          </div>
          <div>
            <span>CPU最近一次耗时(ms)</span>
            <a-statistic :value="systemInfo.last_worker_time" :precision="2"></a-statistic>
          </div>
          <div>
            <span>总物理读</span>
            <a-statistic :value="systemInfo.in_total_physical_reads"></a-statistic>
          </div>
          <div>
            <span>最近一次物理读</span>
            <a-statistic :value="systemInfo.last_physical_reads"></a-statistic>
          </div>
          <div>
            <span>总逻辑读</span>
            <a-statistic :value="systemInfo.in_total_logical_reads"></a-statistic>
          </div>
          <div>
            <span>最近一次逻辑读</span>
            <a-statistic :value="systemInfo.last_logical_reads"></a-statistic>
          </div>
        </div>
      </div>
    </a-card>
    <!-- SQL文本结果 -->
    <a-card type="small" class="a-card">
      <div slot="title">
        <a-icon type="edit" />
        <span style="margin-left: 4px">SQL文本</span>
      </div>
      <sql-highlight v-if="dataInfo.sql_text" :sql="dataInfo.sql_text"></sql-highlight>
      <div class="ai-comment-part" v-else>
        <span>暂无数据</span>
      </div>
    </a-card>
    <!--  执行计划 -->
    <a-card type="small" class="a-card">
      <div class="sql-plan" slot="title">
        <div>
          <custom-icon type="lu-icon-table" />
          <span style="margin-left: 4px">执行计划</span>
        </div>
        <!-- <a @click="showInfoModal" style="font-size: 14px; font-weight: normal;">表结构信息</a> -->
      </div>
      <div v-if="dataInfo.plan">
        <pre>{{ dataInfo.plan }}</pre>
      </div>
      <div v-else>
        <span>暂无数据</span>
      </div>
    </a-card>
    <!-- 统计信息 -->
    <MysqlInfoModal ref="MysqlInfoModal"></MysqlInfoModal>
  </a-spin>
</template>

<script>
import SqlHighlight from '@/components/SqlHighlight';
import reviewHeader from './components/reviewHeader/index';
import aiResult from './components/aiResult/index';
import DateFormat from '@/components/DateFormat';
import MysqlInfoModal from '@/pages/home/<USER>/sqldetail/components/MysqlInfoModal';
import { getSqlserverCheckDetail } from '@/api/home';
import common from '@/utils/common';
import moment from 'moment';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    SqlHighlight,
    reviewHeader,
    aiResult,
    MysqlInfoModal,
    DateFormat
  },
  data() {
    this.ai_status = this.$route.query.ai_status || '';
    this.start_time = this.$route.query.start_time || '';
    this.end_time = this.$route.query.end_time || '';
    this.activeKey = this.$route.query.activeKey || '';
    this.click_time = this.$route.query.click_time || '';
    this.query = this.$route.query.query || '';
    this.username = this.$route.query.username || '';
    this.db_name = this.$route.query.db_name || '';
    return {
      spinning: false,
      // AI判定结果
      dataInfo: {
        ai_comment: [],
        rule_category: [] // 风险等级数据
      },
      // 顶部数据
      headerInfo: {},
      info: {
        rewrite_sql: '',
        sql_plan: ''
      },
      searchData: {}, // 上一页搜索参数
      isWhite: true,
      idList: [],
      currentNum: 0,
      isInfo: false,
      coderParams: {
        height: '500',
        options: {
          theme: 'default',
          readOnly: true
        },
        formatOptions: {
          keywordCase: 'upper'
        },
        needFormat: true
      },
      id: null,
      aiCmoment: [],
      systemInfo: {},
      sqlserver_detail_id: this.$route.query.id,
      detail_id: null,
      db_type: null,
      name: null,
      env: null,
      index: null
    };
  },
  created() {
    // document.body.style.minWidth = '1366px';
  },
  destroyed() {
    // document.body.style.minWidth = '1024px';
  },
  mounted() {
    this.getPostgreReportDetailFn({
      sqlserver_detail_id: this.sqlserver_detail_id,
      ai_status: this.ai_status,
      query: this.query,
      start_time: this.start_time,
      end_time: this.end_time,
      click_time: this.click_time || ''
    });
  },
  methods: {
    moment,
    getPostgreReportDetailFn(params) {
      this.spinning = true;
      getSqlserverCheckDetail(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            this.dataInfo = resData;
            this.chartList = resData.chart_list || [];
            this.systemInfo = resData.system_info || [];
            this.id = resData.id;
            this.pg_detail_id = resData.pg_detail_id;
            this.detail_id = resData.detail_id;
            this.task_id = resData.task_id;
            this.db_type = resData.db_type;
            this.name = resData.name;
            this.env = resData.env;
            this.index = resData.index;
            this.setNavi();
            this.spinning = false;
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 打开弹窗
    showInfoModal() {
      this.$refs.MysqlInfoModal.show({ id: this.detail_id });
    },
    // prev
    onPrev() {
      this.getPostgreReportDetailFn({
        sqlserver_detail_id: this.id,
        paging: 'prev',
        ai_status: this.ai_status,
        query: this.query,
        click_time: this.click_time,
        start_time: this.start_time,
        end_time: this.end_time
        // index: this.index
      });
    },
    // next
    onNext() {
      this.getPostgreReportDetailFn({
        sqlserver_detail_id: this.id,
        paging: 'next',
        ai_status: this.ai_status,
        query: this.query,
        click_time: this.click_time,
        start_time: this.start_time,
        end_time: this.end_time
        // index: this.index
      });
    },
    // 返回
    toBack() {
      this.$router.push({
        name: 'home-postaudit-sqlserver-report',
        query: {
          task_id: this.task_id,
          db_type: this.db_type,
          name: this.name,
          env: this.env,
          activeKey: this.activeKey
        }
      });
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'home-postaudit-sqlserver-report') {
          path =
            sourcePath +
            '?task_id=' +
            this.task_id +
            '&db_type=' +
            this.db_type +
            '&name=' +
            this.name +
            '&env=' +
            this.env +
            '&activeKey=' +
            this.activeKey +
            '&ai_status=' +
            this.ai_status +
            '&query=' +
            this.query;
        }
        return path;
      });

      if (this.sqlserver_detail_id !== this.$route.query.id) {
        this.$router.push({
          name: 'home-postaudit-sqlserver-plan',
          query: { id: this.sqlserver_detail_id }
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.a-card {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
  border-radius: 8px;
  margin-bottom: 24px;
  .sql-plan {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.sql_index {
  width: 100%;
  font-size: 12px;
  li {
    list-style: none;
    height: 30px;
    line-height: 30px;
    width: 30%;
    text-align: center;
    display: inline-block;
    span {
      width: 140px;
      display: inline-block;
    }
  }
}
.common-content {
  @border-color: #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  border: 1px solid @border-color;
  border-radius: 2px;
  div {
    width: 50%;
    // &:nth-child(n + 5) {
    //   margin-top: 10px;
    // }
  }

  &.system-info {
    > div {
      color: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      border-bottom: 1px solid;
      border-right: 1px solid;
      border-color: @border-color;
      &:nth-child(even) {
        border-right: 0;
      }
      &:nth-last-child(1),
      &:nth-last-child(2) {
        border-bottom: 0;
      }
      > span:nth-child(1) {
        color: rgba(0, 0, 0, 0.85);
        flex-grow: 1;
        background: #edf5ff;
        padding: 4px;
        text-align: center;
        border-right: 1px solid @border-color;
      }
      > .ant-statistic,
      > .limit-label {
        width: 65%;
        text-align: center;
      }
    }
  }
}
.review-detail-toback-btn {
  margin-right: 8px;
}
.a-card-header {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
  border-radius: 8px;
  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      // .title-project {
      //   margin-left: 40px;
      // }
      .title-project-name {
        display: inline-block;
        padding: 0 24px;
        color: @primary-color;
        font-size: 12px;
        border: 1px solid @primary-color;
        border-radius: 24px;
        line-height: 24px;
        margin-left: 12px;
        &::before {
          content: '';
          display: inline-block;
          position: relative;
          left: -8px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: @primary-color;
        }
      }
    }
  }
  .pageInfo {
    margin-left: 16px;
  }
}
.a-card-header,
.ant-spin-container > * {
  background: #fff;
  padding: 0px 0px;
  margin-bottom: 16px;
  .ant-card {
    .card-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      .title-project-name {
        display: inline-block;
        padding: 0 24px;
        color: @primary-color;
        font-size: 12px;
        border: 1px solid @primary-color;
        border-radius: 24px;
        line-height: 24px;
        margin-left: 8px;
        &::before {
          content: '';
          display: inline-block;
          position: relative;
          left: -8px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: @primary-color;
        }
      }
    }
  }
}
</style>
