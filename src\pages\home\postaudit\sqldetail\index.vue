<template>
  <div class="home-review-wraper">
    <a-spin tip="加载中" :spinning="loading">
      <!-- SQLID -->
      <div class="review-header">
        <div slot="title" class="card-title" v-if="dbType != 'mysql'">
          <span>SQLID</span>
          <span class="title-project-name">{{ dataInfo.sql_id }}</span>
        </div>
        <div v-else></div>
        <div>
          <a-button :disabled="dataInfo.index === 1" @click="onPrev">
            <a-icon type="double-left" />prev
          </a-button>
          <a-button
            :disabled="dataInfo.count === dataInfo.index"
            style="margin-left: 8px;"
            @click="onNext"
          >
            next
            <a-icon type="double-right" />
          </a-button>
          <span class="pageInfo">{{ dataInfo.index }}/{{ dataInfo.count }}</span>
        </div>
      </div>
      <!-- AI判定结果 -->
      <a-card type="small" :bordered="false">
        <div slot="title">
          <a-icon type="robot" />
          <span style="margin-left: 4px;">AI判定结果</span>
          <a-tag :color="statusColor[dataInfo.status]">{{ dataInfo.status | aiStatus }}</a-tag>
        </div>
        <div v-if="dataInfo.ai_comment" class="ai-comment-part">
          <div v-for="(item, index) in dataInfo.ai_comment" :key="index" class="ai-comment-div">
            <span
              :class="{
                level0: item.level === 0,
                level1: item.level === 1,
                level2: item.level === 2,
                level3: item.level === 3,
                level9: item.level === 9
              }"
            >【{{ item.level | levelStatus }}】</span>
            <span>{{ item.ai_comment }}</span>
          </div>
        </div>
        <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>
      </a-card>
      <!-- 优化建议 -->
      <a-card v-if="sqlPlanInfo.length > 0" type="small" :bordered="false">
        <div slot="title">
          <a-icon type="like" />
          <span style="margin-left: 4px;">优化建议</span>
        </div>
        <div class="ai-comment-part" v-for="(item, index) in sqlPlanInfo" :key="index">
          <div style="color: #0f78fb;">{{ item.message }}</div>
          <span style="color: #0f78fb;">{{ item.sql }}</span>
        </div>
      </a-card>
      <!-- SQL执行指标 -->
      <a-card type="small" :bordered="false">
        <div slot="title">
          <a-icon type="exception" />
          <span style="margin-left: 4px;">SQL执行指标</span>
          <span style="margin-left: 10px; font-size: 14px; font-weight: normal;">
            ( 执行时间范围：{{
            dataInfo.system_info.time_range || '--'
            }}
            )
          </span>
        </div>
        <div v-if="dbType!=='mysql' && dataInfo.system_info">
          <div class="common-content system-info">
            <div>
              <span>执行次数：</span>
              <a-statistic :value="dataInfo.system_info.executions_total + ' 次' || 0"></a-statistic>
            </div>
            <div>
              <span>执行用户：</span>
              {{ dataInfo.system_info.user || '--' }}
            </div>
            <div>
              <span>平均执行时间：</span>
              <a-statistic :value="dataInfo.system_info.avg_time_ms + ' ms' || 0" :precision="2"></a-statistic>
              <!-- {{ dataInfo.system_info.avg_time_ms || 0 }}/ms -->
            </div>
            <div>
              <span>平均逻辑读：</span>
              <a-statistic :value="dataInfo.system_info.avg_buffer_gets + ' 次' || 0"></a-statistic>
              <!-- {{ dataInfo.system_info.avg_buffer_gets || 0 }}/次 -->
            </div>
            <div>
              <span>平均物理读：</span>
              <a-statistic :value="dataInfo.system_info.avg_physical_read_requests + ' 次' || 0"></a-statistic>
              <!-- {{ dataInfo.system_info.avg_physical_read_requests || 0 }}/次 -->
            </div>
            <div>
              <span>平均消耗排序：</span>
              <a-statistic :value="dataInfo.system_info.avg_sort_cost + ' 次' || 0 "></a-statistic>
              <!-- {{ dataInfo.system_info.avg_sort_cost || 0 }}/次 -->
            </div>
            <div>
              <span>平均IO等待时间：</span>
              <a-statistic :value="dataInfo.system_info.avg_io_wait + ' ms' || 0" :precision="2"></a-statistic>
              <!-- {{ dataInfo.system_info.avg_io_wait || 0 }}/ms -->
            </div>
            <div>
              <span>平均集群等待时间：</span>
              <a-statistic :value="dataInfo.system_info.avg_cluster_wait + ' ms' || 0" :precision="2"></a-statistic>
              <!-- {{ dataInfo.system_info.avg_cluster_wait || 0 }}/ms -->
            </div>
          </div>
        </div>
        <div v-else-if="dbType=='mysql' && dataInfo.system_info">
          <div class="common-content system-info">
            <div>
              <span>最大查询时间：</span>
              <a-statistic :value="dataInfo.system_info.query_time_max + ' ms' || 0" :precision="2"></a-statistic>
              <!-- {{ dataInfo.system_info.query_time_max || 0 }}毫秒 -->
            </div>
            <div>
              <span>最小查询时间：</span>
              <a-statistic :value="dataInfo.system_info.query_time_min + ' ms'|| 0" :precision="2"></a-statistic>
              <!-- {{ dataInfo.system_info.query_time_min || 0 }}毫秒 -->
            </div>
            <div>
              <span>查询时间平均值：</span>
              <a-statistic :value="dataInfo.system_info.query_time_avg + ' ms'|| 0" :precision="2"></a-statistic>
              <!-- {{ dataInfo.system_info.query_time_avg || 0 }}毫秒 -->
            </div>
            <div>
              <span>查询时间95值：</span>
              <a-statistic :value="dataInfo.system_info.query_time_95 + ' ms'|| 0" :precision="2"></a-statistic>
              <!-- {{ dataInfo.system_info.query_time_95 || 0 }}毫秒 -->
            </div>
            <div>
              <span>锁时间95值：</span>
              <a-statistic :value="dataInfo.system_info.lock_time_95 + ' ms'|| 0" :precision="2"></a-statistic>
              <!-- {{ dataInfo.system_info.lock_time_95 || 0 }}毫秒 -->
            </div>
            <div>
              <span>影响行数平均值：</span>
              <a-statistic :value="dataInfo.system_info.rows_affected_avg || 0"></a-statistic>
              <!-- {{ dataInfo.system_info.rows_affected_avg || 0 }} -->
            </div>
            <div>
              <span>影响行数95值：</span>
              <a-statistic :value="dataInfo.system_info.rows_affected_95 || 0"></a-statistic>
              <!-- {{ dataInfo.system_info.rows_affected_95 || 0 }} -->
            </div>
            <div>
              <span>返回行数平均值：</span>
              <a-statistic :value="dataInfo.system_info.rows_sent_avg || 0"></a-statistic>
              <!-- {{ dataInfo.system_info.rows_sent_avg || 0 }} -->
            </div>
            <div>
              <span>返回行数95值：</span>
              <a-statistic :value="dataInfo.system_info.rows_sent_95 || 0"></a-statistic>
              <!-- {{ dataInfo.system_info.rows_sent_95 || 0 }} -->
            </div>
            <div>
              <span>查询次数：</span>
              <a-statistic :value="dataInfo.system_info.slow_query_count || 0"></a-statistic>
              <!-- {{ dataInfo.system_info.slow_query_count || 0 }} -->
            </div>
          </div>
        </div>
        <div v-else>
          <div class="common-content system-info">
            <span>暂无数据</span>
          </div>
        </div>
      </a-card>
      <!-- SQL文本 -->
      <a-card type="small" :bordered="false">
        <div slot="title">
          <a-icon type="edit" />
          <span style="margin-left: 4px;">SQL文本</span>
        </div>
        <sql-highlight v-if="dataInfo.sql_text" :sql="dataInfo.sql_text"></sql-highlight>
        <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>
      </a-card>
      <!-- 执行计划 -->
      <a-card type="small" :bordered="false">
        <div slot="title" class="sql-plan">
          <div>
            <a-icon type="flag" />
            <span style="margin-left: 4px;">执行计划</span>
          </div>
          <a @click="showInfoModal" style="font-size: 14px; font-weight: normal;">表结构信息</a>
        </div>
        <div v-if="dataInfo.plan">
          <pre v-for="(item, index) in dataInfo.plan" :key="index">{{ item }}</pre>
        </div>
        <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>
      </a-card>
    </a-spin>
    <!-- 统计信息 -->
    <InfoModal ref="InfoModal"></InfoModal>
    <MysqlInfoModal ref="MysqlInfoModal"></MysqlInfoModal>
  </div>
</template>
<script>
import { sqlAdviceInfo, sqlDetailInfo, sqlDetailInfoMysql } from '@/api/home';
import SqlHighlight from '@/components/SqlHighlight';
import common from '@/utils/common';
import InfoModal from './components/InfoModal';
import MysqlInfoModal from './components/MysqlInfoModal';
import Draggable from '@/utils/drag';
import config from './config';

export default {
  components: {
    SqlHighlight,
    MysqlInfoModal,
    InfoModal
  },
  data() {
    this.config = config(this);
    return {
      statusColor: this.config.statusColor,
      loading: false,
      dbType: this.$route.query.db_type,
      report_id: this.$route.query.report_id,
      task_id: this.$route.query.task_id,
      id: this.$route.query.id,
      db_type: this.$route.query.db_type,
      detail_id: this.$route.query.detail_id,
      searchData: this.$route.params.searchData,
      sqlPlanInfo: [], // 索引优化建议
      dataInfo: {
        system_info: {},
        plan: null,
        ai_comment: null
      }
    };
  },
  created() {
    this.setNavi();
  },
  mounted() {
    this.getSqlDetailInfoData({ id: this.id, params: this.searchData });
  },
  watch: {},
  destroyed() {
    if (this.infoModalDragInstance) {
      this.infoModalDragInstance.destroy();
      this.infoModalDragInstance = null;
    }
  },
  computed: {},
  methods: {
    // 数据详情
    getSqlDetailInfoData(payload) {
      this.loading = true;
      // 根据dbType的不同调用不同接口
      if (this.dbType == 'mysql') {
        sqlDetailInfoMysql(payload)
          .then(res => {
            this.loading = false;
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ duration: 0 });
              this.dataInfo = res.data.data || {};
              this.id = this.dataInfo.id;
              this.setNavi();
              if (this.dataInfo.detail_id) {
                this.getSqlAdviceInfoData(this.dataInfo.detail_id);
              }
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(() => {
            this.loading = false;
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      } else {
        sqlDetailInfo(payload)
          .then(res => {
            this.loading = false;
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ duration: 0 });
              this.dataInfo = res.data.data || {};
              this.id = this.dataInfo.id;
              this.setNavi();
              if (this.dataInfo.detail_id) {
                this.getSqlAdviceInfoData(this.dataInfo.detail_id);
              }
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(() => {
            this.loading = false;
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      }
    },
    // 优化建议
    getSqlAdviceInfoData(id) {
      sqlAdviceInfo({ id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            this.sqlPlanInfo = res.data.data || [];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    // 面包屑跳转
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'home-postaudit-detail') {
          path = sourcePath + '?task_id=' + this.task_id;
        }
        if (key === 'home-postaudit-review') {
          path =
            sourcePath +
            '?task_id=' +
            this.task_id +
            '&report_id=' +
            this.report_id +
            '&db_type=' +
            this.db_type;
        }
        if (this.id !== this.$route.query.id) {
          this.$router.push({
            name: 'home-postaudit-sqldetail',
            query: {
              id: this.id,
              task_id: this.task_id,
              report_id: this.report_id,
              db_type: this.db_type,
              detail_id: this.detail_id
            }
          });
        }
        return path;
      });
    },
    // 打开弹窗
    showInfoModal() {
      if (this.dbType == 'mysql') {
        this.$refs.MysqlInfoModal.show({ id: this.dataInfo.detail_id });
        this.setInfoModalDraggable();
      } else {
        this.$refs.InfoModal.show();
        this.setInfoModalDraggable();
      }
    },
    // 弹窗拖动
    setInfoModalDraggable() {
      !this.infoModalDragInstance &&
        setTimeout(() => {
          const modalElement = this.$refs.InfoModal.$el;
          if (modalElement && modalElement.querySelector) {
            this.infoModalDragInstance = new Draggable({
              el: modalElement.querySelector('.ant-modal-content'),
              handler: modalElement.querySelector('.ant-modal-header')
            });
          } else {
            this.setInfoModalDraggable();
          }
        }, 800);
    },
    // prev
    onPrev() {
      this.getSqlDetailInfoData({
        id: this.id,
        paging: 'prev',
        params: this.searchData
      });
    },
    // next
    onNext() {
      this.getSqlDetailInfoData({
        id: this.id,
        paging: 'next',
        params: this.searchData
      });
    }
  },
  filters: {
    levelStatus(value) {
      let obj = {
        0: '未知',
        1: '高风险',
        2: '中风险',
        3: '低风险',
        9: '无风险'
      };
      return obj[value];
    },
    aiStatus(value) {
      let obj = {
        0: '未知',
        1: '通过',
        2: '白名单通过',
        9: '错误',
        '-1': '不通过'
      };
      return obj[value];
    }
  }
};
</script>
<style lang="less"></style>
<style lang="less" scoped>
.home-review-wraper {
  color: rgba(86, 87, 89, 1);
  /deep/ .anticon-robot,
  .anticon-edit,
  .anticon-flag,
  .anticon-like,
  .anticon-exception {
    color: #1890ff;
  }
  .review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .card-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      .title-project {
        margin-left: 40px;
      }
      .title-project-name {
        display: inline-block;
        padding: 0 24px;
        color: @primary-color;
        font-size: 12px;
        border: 1px solid @primary-color;
        border-radius: 24px;
        line-height: 24px;
        margin-left: 12px;
        &::before {
          content: '';
          display: inline-block;
          position: relative;
          left: -8px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: @primary-color;
        }
      }
    }
    .pageInfo {
      margin-left: 16px;
    }
  }
  .review-header,
  .ant-spin-container > * {
    background: #fff;
    padding: 16px 24px;
    margin-bottom: 16px;
    .ant-card {
      .card-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        .title-project-name {
          display: inline-block;
          padding: 0 24px;
          color: @primary-color;
          font-size: 12px;
          border: 1px solid @primary-color;
          border-radius: 24px;
          line-height: 24px;
          margin-left: 8px;
          &::before {
            content: '';
            display: inline-block;
            position: relative;
            left: -8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: @primary-color;
          }
        }
      }
    }
  }
  /deep/ .ant-tag {
    margin-left: 8px;
  }
  /deep/ .ant-card-head-title {
    padding-top: 0;
    padding-bottom: 8px;
  }
  /deep/ .ant-card-head {
    padding: 0;
    min-height: 32px;
  }
  /deep/ .ant-card-body {
    padding: 24px 0;
    padding-bottom: 0;
  }
  pre {
    border-radius: 2px;
    padding: 16px;
    background-color: rgba(125, 125, 125, 0.1);
    color: #0f78fb;
    margin-bottom: 0;
  }
  .common-content {
    display: flex;
    justify-content: stretch;
    align-items: center;
    flex-wrap: wrap;
    div {
      width: 25%;
      &:nth-child(n + 5) {
        margin-top: 10px;
      }
    }

    &.system-info {
      > div {
        display: flex;
        color: rgba(0, 0, 0, 0.6);
        > span {
          color: #000000;
        }
      }
    }
  }
  @media screen and (max-width: 1500px) {
    .common-content > div {
      width: 25%;
    }
  }
  .sql-plan {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.ai-comment-part {
  padding: 16px;
  background-color: #edf5ff;
  border-radius: 5px;
  .ai-comment-div {
    padding: 0;
    margin: 2px 0;
    background-color: transparent;
    color: #0f78fb;
    line-height: 25px;
    .level0 {
      color: #b0aeae;
    }
    .level1 {
      color: #ff4d4f;
    }
    .level2 {
      color: #ff9358;
    }
    .level3 {
      color: #1edfa9;
    }
    .level9 {
      color: #52c41a;
    }
  }
}
</style>
