<!--
 * @Descripttion: 单元格编辑
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2020-12-28 09:55:51
-->

<!--
 * @Descripttion: asset
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2020-12-16 19:11:37
-->
<template>
  <span v-if="editMode">
    <a-select
      v-bind:value="editValue"
      @change="onCellChange"
    >
      <a-select-option
        v-for="item in dataList"
        v-bind:key="item.value"
        :value="item.value"
      >
        {{ item.label }}
      </a-select-option>
    </a-select>
  </span>
  <span v-else>
    {{getLabel(value)}}
  </span>
</template>
<script>
export default {
  name: 'report-cell',
  computed: {
  },
  props: {
    action: {
      type: Function,
      required: true,
      default: () => () => {}
    },
    type: {
      type: String,
      required: true,
      default: () => ''
    },
    columnKey: {
      type: String,
      required: false,
      default: () => ''
    },
    search: {
      type: Object,
      required: false,
      default: function () {
        return {}
      }
    },
    rowEdit: {
      type: Boolean,
      required: false,
      default: () => {
        return false
      }
    },
    rowData: {
      type: Object,
      required: false,
      default: function () {
        return {}
      }
    },
    value: {
      type: [Boolean, String, Number],
      required: true,
      default: () => true
    }
  },
  data (vm) {
    return {
      parmas: vm.search,
      dataList: [],
      editValue: vm.value,
      editMode: vm.rowEdit,
      editRowData: vm.rowData
    }
  },
  mounted () {
    
  },
  watch: {
    search: {
      handler: function (newValue) {
        this.parmas = newValue
      },
      deep: true
    },
    rowData: {
      handler: function (newValue) {
        this.editRowData = newValue
      },
      deep: true
    },
    rowEdit: function (newValue) {
      this.editMode = newValue
      if (this.editMode === true) {
        this.initDataList()
      }
      // this.forceUpdate()
    }
  },
  methods: {
    initDataList () {
      let parmas = this.parmas
      if (parmas.dataSource) {
        if (typeof parmas.dataSource === 'function') {
          parmas.dataSource(this.editRowData).then((row) => {
            this.dataList = row
          })
        }
      }
    },
    forceUpdate () {
      this.$forceUpdate()
    },
    getLabel (txt) {
      if (this.dataList && this.dataList.length > 0) {
        let find = this.dataList.find((it) => {
          return it.value === txt
        })

        if (find) {
          return find.label
        }
      }

      return txt
    },
    onCellChange (value) {
      this.$emit('change', {
        value: value,
        columnKey: this.columnKey,
        row: this.rowData
      })
      this.editValue = value
    }
  }
}
</script>
<style lang="less">
.ant-select-selection__rendered {
  min-width: 100px;
}
</style>
