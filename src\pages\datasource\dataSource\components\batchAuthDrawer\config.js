export default function (ctx) {
  const userFields = [
    {
      type: 'Select',
      label: '添加用户',
      key: 'user_type',
      props: {
        url: '/sqlreview/project/authorization_user',
        reqParams: {}
      },
      listeners: {
        change: (value) => {
          ctx.$refs.user.saving({
            user_type: value,
            user: null
          });
        }
      },
      width: '400',
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    },
    (formData = {}) => {
      return {
        type: 'Select',
        label: ' ',
        key: 'user',
        props: {
          mode: 'multiple',
          url: '/sqlreview/project/user_group_list',
          reqParams: {
            user_type: formData.user_type && formData.user_type
          }
        },
        className: 'hidden-label',
        width: '400',
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      };
    }
  ];

  const authFields = (dmlSwitch) => {
    return [
      {
        type: 'Select',
        label: '权限设置',
        key: 'auth_set',
        width: '400',
        props: {
          options: [
            {
              label: 'SQL编辑器',
              value: 0
            }
          ]
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      },
      {
        type: 'CheckboxGroup',
        label: '',
        key: 'permission_type',
        width: '400',
        props: {
          options: [
            {
              label: '实例登录',
              value: 0
            }
            // {
            //   label: '数据库登录',
            //   value: 1
            // }
          ]
        },
        className: 'hidden-label',
        visible: ctx.activeKey == 'instance',
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      },
      {
        type: 'CheckboxGroup',
        label: '',
        key: 'permission_type',
        width: '400',
        props: {
          options:
            dmlSwitch == 1
              ? [
                  {
                    label: '查询',
                    value: 0
                  },
                  {
                    label: 'DDL',
                    value: 1
                  },
                  {
                    label: 'DML',
                    value: 2
                  }
                ]
              : [
                  {
                    label: '查询',
                    value: 0
                  }
                ]
        },
        className: 'hidden-label',
        visible: ['database', 'tableDetail', 'field'].includes(ctx.activeKey),
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      }
    ];
  };

  const fields = [
    {
      type: 'Select',
      label: '有限期',
      key: 'deadline',
      props: {},
      width: '200',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    }
  ];

  const columns = [
    {
      title: '表名称',
      dataIndex: 'name',
      key: 'name',
      scopedSlots: { customRender: 'name' }
      // width: 100
    }
  ];
  const fieldColumns = [
    {
      title: '字段名',
      dataIndex: 'name',
      key: 'name',
      scopedSlots: { customRender: 'name' }
    },
    {
      title: '字段类型',
      dataIndex: 'type',
      key: 'type',
      scopedSlots: { customRender: 'type' }
    },
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name',
      scopedSlots: { customRender: 'table_name' }
    }
  ];
  return {
    fields,
    columns,
    userFields,
    authFields,
    fieldColumns
  };
}
