<template>
  <a-modal
    v-model="visible"
    okText="强制提交"
    cancelText="取消"
    @ok="save"
    @cancel="onCancel"
    width="420px"
    wrapClassName="import-export-commit-modal"
  >
    <div slot="title" class="title">
      <custom-icon type="exclamation-circle" theme="filled" />
      <span>{{ `目标库中存在统计信息，是否强制导入？` || '...' }}</span>
    </div>
    <div class="content">
      <div>
        <div class="des">强制导入会覆盖已有信息</div>
      </div>
    </div>
  </a-modal>
</template>
<script>
export default {
  props: {},
  components: {},
  data() {
    return {
      id: '',
      value: null,
      visible: false,
      message: ''
    };
  },
  methods: {
    show(id) {
      this.visible = true;
    },
    onCancel() {
      this.hide();
      this.$emit('reset');
    },
    hide() {
      this.visible = false;
    },
    save() {
      this.hide();
      this.$emit('submit');
    }
  },
  watch: {}
};
</script>

<style lang="less">
.import-export-commit-modal {
  .ant-modal-header {
    padding: 32px 40px 12px 40px;
    background: #fff !important;
    .title {
      display: flex;
      font-size: 16px;
      color: #27272a;
      text-align: justify;
      font-weight: 600;
      .anticon {
        color: #e71d36;
        font-size: 16px;
        margin-right: 4px;
        padding-top: 2px;
      }
    }
  }
  .ant-modal-close {
    .ant-modal-close-x {
      color: #27272a;
    }
  }
  .ant-modal-body {
    padding: 0 40px;
    .content {
      > div {
        padding: 0 24px 48px 24px;
        .des {
          font-size: 13px;
          color: #27272a;
          text-align: justify;
        }
      }
    }
  }
  .ant-modal-footer {
    .ant-btn {
      width: 104px;
      height: 36px;
      background: #ffffff;
      border: 1px solid #008adc;
      font-size: 14px;
      color: #008adc;
      font-weight: 600;
      border-radius: 6px;
    }
    .ant-btn-primary {
      background: #008adc;
      color: #ffffff;
      font-weight: 600;
    }
  }
}
</style>
