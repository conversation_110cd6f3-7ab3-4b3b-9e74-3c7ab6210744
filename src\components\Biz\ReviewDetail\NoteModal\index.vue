<template>
  <a-modal
    v-model="visible"
    @ok="onOk"
    width="620px"
    wrapClassName="biz-review-detail-note-modal"
  >
    <div slot="title" class="title">
      <div>SQL备注</div>
      <div class="des">请填写SQL备注，说明运行情况，便于DBA评审。</div>
    </div>
    <Form ref="form" v-bind="params" :formData="formData"></Form>
  </a-modal>
</template>
<script>
import Form from '@/components/Form';
import config from './config';
import { getSqlmapTable } from '@/api/home';
export default {
  props: {},
  components: { Form },
  data() {
    this.config = config(this);
    return {
      visible: false,
      formData: {
        sqlmap_monthly_increase: []
      },
      params: {
        fields: [],
        fixedLabel: true,
        layout: 'vertical',
        // colon: true,
        labelCol: { span: 24 },
        wrapperCol: { span: 24 }
      },
      id: null
    };
  },
  methods: {
    show(data) {
      this.id = data.id
      this.visible = true;
      const fields = this.config.fields();
      this.$set(this.params, 'fields', fields);
      this.$showLoading();
      getSqlmapTable({ id: data.id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const sqlMapData = _.get(res, 'data.data');
            this.$set(this.formData, 'sqlmap_monthly_increase', sqlMapData);
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const data = this.$refs.form.getData()
      data.id = this.id
      this.$emit('saveNote', data)
      this.hide();
    }
  },
  watch: {}
};
</script>

<style lang="less">
.biz-review-detail-note-modal {
  .ant-modal-header {
    background: #fff !important;
    padding: 21px 24px;
    .title {
      > div {
        font-size: 20px;
        color: #27272a;
        font-weight: 600;
      }
      .des {
        font-size: 12px;
        color: #a1a1aa;
        font-weight: 400;
        margin: 6px 0 20px 0;
      }
    }
  }
  .ant-modal-close {
    .ant-modal-close-x {
      color: #27272a;
    }
  }
  .ant-modal-body {
    padding: 0 24px;
    .ant-form {
      .ant-form-item-label {
        min-height: 0 !important;
        line-height: 0 !important;
        > label {
          justify-content: flex-start !important;
          > span {
            color: #27272a;
            font-weight: 600;
            > .custom-icon {
              font-size: 16px;
              color: #27272a;
              margin-right: 2px;
            }
          }
        }
      }
      .ant-form-item:nth-child(2) {
        .ant-form-item-label {
          padding: 10px;
          background: #f4f5f7;
        }
        .ant-form-item-control-wrapper {
          .ant-form-item-children {
            .table-edit {
              .ant-table-content {
                border: 1px solid #e8e8e8;
                border-top: none;
                .ant-table-thead {
                  tr {
                    th {
                      font-size: 14px;
                      color: #a1a1aa !important;
                      font-weight: 400;
                      background: #fff !important;
                    }
                  }
                }
              }
            }
          }
        }
      }
      .ant-form-item-control-wrapper {
        .ant-form-item-children {
          .ant-radio-group {
            display: flex;
            justify-content: space-between;
            .ant-radio-wrapper {
              &:last-child {
                margin-right: 0;
              }
              > span {
                padding-right: 0;
                color: #71717a;
              }
              .ant-radio {
                .ant-radio-input {
                  background: #a1a1aa;
                }
              }
            }
          }
          .ant-input {
            min-height: 80px;
          }
        }
      }
    }
  }

  .ant-modal-footer {
    .ant-btn {
      width: 76px;
      height: 36px;
      background: #ffffff;
      border: 1px solid #008adc;
      font-size: 14px;
      color: #008adc;
      font-weight: 600;
      border-radius: 6px;
    }
    .ant-btn-primary {
      background: #008adc;
      color: #ffffff;
      font-weight: 600;
    }
  }
}
</style>