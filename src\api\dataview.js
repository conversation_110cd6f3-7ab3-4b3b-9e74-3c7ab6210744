import Http from '@/utils/request';
// 获取代办、催办、代办任务sql
export function getTask(params) {
  return Http({
    url: `/sqlreview/review/dba-work-platform/ `,
    method: 'get',
    data: params
  });
}

// 我关注的应用弹窗获取全部应用
export function getFirstChoose(params) {
  return Http({
    url: `/sqlreview/subscribe/first-choose/`,
    method: 'get',
    params: params
  });
}

// 保存我关注的应用
export function saveFirstChoose(params) {
  return Http({
    url: `/sqlreview/subscribe/sub-op/`,
    method: 'get',
    params
  });
}

// 获取工作台的概况信息
export function getReportIndex(params) {
  return Http({
    url: `/sqlreview/report/report_index`,
    method: 'post',
    data: params
  });
}

// 获取版本更新信息
export function getReportVersion(params) {
  return Http({
    url: `/sqlreview/report/get_version`,
    method: 'get',
    params
  });
}

// 反馈信息展示
export function getFeedBack(params) {
  return Http({
    url: `/sqlreview/report/feed_back_list`,
    method: 'get',
    params: params
  });
}

// 代码审核图表
export function getReportCodeReview(params) {
  return Http({
    url: `/sqlreview/report/report_code_review`,
    method: 'post',
    data: params
  });
}

// top sql
export function getReportTopSql(params) {
  return Http({
    url: `/sqlreview/report/report_top_sql`,
    method: 'post',
    data: params
  });
}

// 数据库审核
export function getReportDatabaseAudit(params) {
  return Http({
    url: `/sqlreview/report/report_database_audit`,
    method: 'post',
    data: params
  });
}

// 数据库审核
export function addFeedBack(params) {
  return Http({
    url: `/sqlreview/report/feed_back_add`,
    method: 'post',
    data: params
  });
}

// 导出报表
export function exportIndexExecl(params) {
  return Http({
    url: `/sqlreview/report/export_index_execl`,
    method: 'post',
    data: params,
    responseType: 'blob'
  });
}

// 数据库对象监控
export function getDatabaseMonitorData(params) {
  return Http({
    url: `/sqlreview/report/report_database_monitor`,
    method: 'post',
    data: params
  });
}

export default {};
