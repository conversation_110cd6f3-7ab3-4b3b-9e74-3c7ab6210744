<template>
  <a-drawer
    title="实例详情"
    :visible="visible"
    @close="hide"
    :width="'50%'"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="config-datasource-instance-detail-drawer"
  >
    <a-spin :spinning="spinning" class>
      <!-- <Form v-bind="params" :formData="formData" ref="form"></Form> -->
      <div class="datarsource-instance-detail-form">
        <a-row>
          <a-col :span="6">实例名称：</a-col>
          <a-col :span="12">{{data.name}}</a-col>
        </a-row>
        <a-row>
          <a-col :span="6">数据库类型：</a-col>
          <a-col :span="12">
            <DbImg :type="data.db_type" />
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">环境：</a-col>
          <a-col :span="12">
            <Tag type="Env" :text="data.env && data.env.toUpperCase()" />
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">连接串：</a-col>
          <a-col :span="12">{{data.db_url}}</a-col>
        </a-row>
        <a-row>
          <a-col :span="6">账号：</a-col>
          <a-col :span="12">{{data.user}}</a-col>
        </a-row>
        <!-- <a-row>
        <a-col :span="12">密码：</a-col>
        <a-col :span="12">col-12</a-col>
        </a-row>-->
        <a-row>
          <a-col :span="6">链接状态：</a-col>
          <a-col :span="12">
            <span v-if="data.connect_status == 0">
              <custom-icon type="lu-icon-warning" />
              {{'未链接测试'}}
            </span>
            <!-- <span v-if="data.connect_status == 1"><custom-icon type="lu-icon-warning" />{{'链接测试成功'}}</span> -->
            <span v-if="data.connect_status == 2">
              <a-icon type="minus-circle" theme="filled" style="color: #D9363E;" />
              {{'链接测试失败'}}
            </span>
          </a-col>
        </a-row>
        <!-- <a-row>
          <a-col :span="6">管控模式：</a-col>
          <a-col :span="12">
            <custom-icon
              :type="data.is_permission_control == 1 ? 'lu-icon-safe' : 'lu-icon-operation'"
            ></custom-icon>
            <span style="margin-left: 8px">{{data.is_permission_control == 0 ? '自由操作' : '安全协同'}}</span>
          </a-col>
        </a-row> -->
      </div>
    </a-spin>
    <div
      :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
    >
      <a-button @click="handleCancel">关闭</a-button>
    </div>
  </a-drawer>
</template>

<script>
import { getInstanceDetailData } from '@/api/config/dataSource';
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import Form from '@/components/Form';
import common from '@/utils/common';
import config from './config';

export default {
  components: {
    Form,
    DbImg,
    Tag
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      spinning: false,
      // params: {
      //   fields: this.config.fields,
      //   layout: 'horizontal',
      //   labelCol: { span: 6 },
      //   wrapperCol: { span: 16 },
      //   labelAlign: 'left'
      // },
      // formData: {}，
      data: {}
    };
  },
  created() {},
  mounted() {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.init(data);
    },
    hide() {
      this.visible = false;
    },
    handleCancel() {
      this.hide();
    },
    init(data) {
      this.spinning = true;
      const params = { name: data.datasource_name };
      getInstanceDetailData(params)
        .then(res => {
          if (common.isSuccessCode(res)) {
            const data = _.get(res, 'data.data.results');
            // this.formData = data[0] || {};
            this.data = data[0] || {};
            this.spinning = false;
          } else {
            this.spinning = false;

            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.spinning = false;
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.datarsource-instance-detail-form {
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  .ant-row {
    display: flex;
    align-items: center;
    border-bottom: 1px dashed #d9d9d9;
    &:last-child {
      border-bottom: none;
    }

    > .ant-col-6 {
      text-align: right;
      border-right: 1px solid #e8e8e8;
      margin-right: 24px;
      padding: 12px;
      background: #e6f7ff;
    }
  }
}
</style>
