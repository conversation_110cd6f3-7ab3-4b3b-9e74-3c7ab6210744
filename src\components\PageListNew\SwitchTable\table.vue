<template>
  <Table
    ref="table"
    v-bind="tableConfig || {}"
    v-on="tableListeners"
    :class="['new-view-table', 'page-list-switch-table', view]"
    :extraToolsAfter="extraToolsAfter"
    :columns="columns"
    :dataSource="dataSource"
    :showHeader="showHeader"
    :scroll="scroll"
  >
    <template v-for="sItem in scopedSlots" v-slot:[sItem]="{text, record, index, column}">
      <slot :name="sItem" v-bind="{text, record, index, column}"></slot>
    </template>
    <template v-for="item in slots" v-slot:[item]>
      <slot :name="item"></slot>
    </template>
  </Table>
</template>

<script>
import Table from '@/components/Table';
export default {
  props: {
    cardColumns: {
      type: Array,
      default: () => []
    },
    listColumns: {
      type: Array,
      default: () => []
    },
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  components: { Table },
  data() {
    let _showHeader = false;
    const { showHeader } = this.$attrs;
    if (showHeader) _showHeader = showHeader;
    return {
      view: 'card', // card | list
      extraToolsAfter: [
        {
          tips: '切换视图',
          icon: 'lu-icon-viewlist',
          key: 'lu-icon-viewgrid',
          callback: this.switchView,
          isDivider: true
        }
      ],
      columns: this.cardColumns,
      showHeader: _showHeader,
      scroll: {}
    };
  },
  computed: {
    scopedSlots() {
      return Object.keys(this.$scopedSlots);
    },
    slots() {
      return Object.keys(this.$slots);
    },
    tableConfig() {
      return {
        ...this.$attrs
      };
    },
    tableListeners() {
      return {
        ...this.$listeners
      };
    }
  },
  mounted() {
    const { cacheKey } = this.$attrs;
    const viewType = localStorage.getItem(cacheKey);
    if (viewType) this.switchView(viewType);
  },
  methods: {
    switchView(viewType) {
      if (viewType) {
        this.view = viewType;
      } else {
        this.view = this.view == 'card' ? 'list' : 'card';
      }
      // 储存viewType到本地
      const { cacheKey, scroll, showHeader } = this.$attrs;
      localStorage.setItem(cacheKey, this.view);
      // 切换columns
      const columns = this.view == 'card' ? this.cardColumns : this.listColumns;
      this.$set(this, 'columns', columns);
      // 切换icon
      const icon =
        this.view == 'card' ? 'lu-icon-viewlist' : 'lu-icon-viewgrid';
      this.$set(this, 'extraToolsAfter', [
        {
          tips: '切换视图',
          icon,
          key: 'lu-icon-viewgrid',
          callback: this.switchView,
          isDivider: true
        }
      ]);
      // 表头展示与否
      this.$set(this, 'showHeader', showHeader || this.view == 'list');

      if (scroll) {
        this.$set(this, 'scroll', this.view == 'list' ? scroll : {});
      }
    },
    execute(method, ...arg) {
      this.$refs.table[method](...arg);
    },
    getInstance() {
      const { table } = this.$refs;
      return table;
    }
  }
};
</script>

<style lang="less">
.page-list-switch-table {
  // .ant-table-pagination {
  //   padding: 0 24px;
  // }
  &.card {
    .ant-table-wrapper {
      .ant-spin-container {
        .ant-table {
          .ant-table-tbody {
            tr {
              > td {
                padding: 32px;
                height: 166px;
              }
              & > .ant-table-selection-column {
                padding: 36px 0 16px 32px !important;
                vertical-align: top;
              }
            }
          }
          .ant-table-thead {
            tr {
              & > .ant-table-selection-column {
                padding: 16px 0 16px 32px !important;
              }
            }
          }
        }
      }
    }
  }
  &.list {
    .ant-table-wrapper {
      .ant-spin-container {
        .ant-table {
          .ant-table-tbody {
            tr {
              > td {
                padding: 16px 32px;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #27272a !important;
                font-weight: 400;
                .ant-tag-has-color {
                  height: 22px;
                  line-height: 20px;
                  // border-radius: 12px;
                  text-align: center;
                  width: 62px;
                  margin-bottom: 0;
                }
                .limit-tags {
                  .ant-tag {
                    height: 22px;
                    line-height: 20px;
                    border: 1px solid #e4e4e7;
                    background: #fff;
                  }
                }
              }
              & > .ant-table-selection-column {
                padding: 16px 0 16px 32px !important;
              }
            }
          }
          .ant-table-thead {
            tr {
              > th {
                padding: 16px 32px;
                height: 56px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #a1a1aa;
                font-weight: 400;
              }
              & > .ant-table-selection-column {
                padding: 16px 0 16px 32px !important;
              }
            }
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .page-list-switch-table {
    &.custom-table .ant-table .ant-table-tbody {
      @keyframes fadeIn {
        0% {
          opacity: 0;
        }
        25% {
          opacity: 0.25;
        }
        50% {
          opacity: 0.5;
        }
        75% {
          opacity: 0.75;
        }
        100% {
          opacity: 1;
        }
      }
      tr:hover {
        .card-table {
          .right-block-botton {
            display: flex;
            animation: fadeIn 0.2s;
            justify-content: flex-end;
            > a {
              margin-left: 30px;
            }
          }
          .right-block-rules {
            display: none;
          }
          .right-block-sql-text {
            display: none;
          }
          .ant-divider-vertical {
            display: none;
          }
        }
      }
    }
  }
}
</style>