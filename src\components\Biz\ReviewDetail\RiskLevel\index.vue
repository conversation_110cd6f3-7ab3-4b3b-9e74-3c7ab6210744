<template>
  <div class="rule-label">
    <a-popover
      overlayClassName="rule-label-popover"
      v-if="[-1, 2, 9].includes(value)"
    >
      <template slot="content">
        <div class="rule-block" v-if="ruleShowLabel.length > 0">
          <div class="title">触发规则</div>
          <div class="des" v-for="(item, index) in ruleShowLabel" :key="index">
            <div>
              <!-- 1是低风险 0高风险 -->
              <custom-icon
                type="lu-icon-alarm"
                :style="{
                  color: item.rule_result == 1 ? '#f29339' : '#e71d36'
                }"
              />
              <span>{{ item.desc }}</span>
            </div>
          </div>
        </div>
        <div class="exception-block" v-if="exceptionShowLabel.length > 0">
          <div class="title">审核异常</div>
          <div
            class="des"
            v-for="(item, index) in exceptionShowLabel"
            :key="index"
          >
            <div>
              <custom-icon type="lu-icon-unusual" style="color: #71717a" />
              <span>{{ item }}</span>
            </div>
          </div>
        </div>
        <slot name="popoverBottom"></slot>
      </template>
      <span :class="['risk-level', `${riskClass[value]}`]">{{
        riskText[value]
      }}</span>
    </a-popover>
    <span v-else :class="['risk-level', `${riskClass[value]}`]">{{
      riskText[value]
    }}</span>
  </div>
</template>

<script>
export default {
  inheritAttrs: false,
  components: {},
  props: {
    aiComment: {
      type: Object,
      default: () => {}
    },
    value: String | Number
  },
  data() {
    return {
      riskText: {
        0: '待审核',
        3: '审核中',
        '-1': '高风险',
        2: '低风险',
        9: '异常',
        1: '无风险'
      },
      riskClass: {
        0: 'risk-wait',
        3: 'risk-auditing',
        '-1': 'risk-high',
        2: 'risk-low',
        9: 'risk-error',
        1: 'risk-no'
      }
    };
  },
  computed: {
    ruleShowLabel() {
      return this.aiComment.rule || [];
    },
    exceptionShowLabel() {
      return this.aiComment.error || [];
    }
  },
  created() {},
  beforeDestroy() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.rule-label {
  cursor: pointer;
  display: flex;
  .risk-level {
    width: 56px;
    height: 22px;
    font-size: 12px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .risk-high {
    background-color: rgb(255, 241, 240);
    color: rgb(245, 34, 45);
    border: 1px solid rgb(255, 163, 158);
  }
  .risk-low {
    background: #fff2e8;
    color: #fa541c;
    border: 1px solid rgba(255, 187, 150, 1);
  }
  .risk-no {
    background-color: rgb(246, 255, 237);
    color: rgb(82, 196, 26);
    border: 1px solid rgb(183, 235, 143);
  }
  .risk-error {
    background: #fafafa;
    color: #71717a;
    border: 1px solid rgba(217, 217, 217, 1);
  }
  .risk-wait {
    background-color: rgba(242, 147, 57, 0.17);
    color: rgb(153, 27, 27);
    border: 1px solid rgb(255, 213, 145);
  }
  .risk-auditing {
    background-color: rgba(91, 147, 255, 0.15);
    color: rgb(33, 74, 192);
    border: 1px solid rgb(173, 198, 255);
  }
}
</style>
<style lang="less">
.ant-popover {
  &.rule-label-popover {
    z-index: 1051;
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 400px;
        max-height: 200px;
        .exception-block,
        .rule-block {
          .title {
            // height: 24px;
            font-size: 12px;
            color: #27272a;
            // line-height: 24px;
            font-weight: 400;
            margin-bottom: 6px;
          }
          .des {
            > div {
              // height: 24px;
              font-size: 12px;
              color: #27272a;
              // line-height: 24px;
              font-weight: 400;
              margin-bottom: 6px;
            }
          }
        }
      }
    }
  }
}
</style>
