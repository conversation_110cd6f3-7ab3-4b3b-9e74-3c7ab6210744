<template>
  <div class="container">
    <a-spin class="spin" :spinning="commonViewLoading">
      <div class="report-data">
        <Chart :option="option" ref="line" />
      </div>
    </a-spin>
  </div>
</template>

<script>
import Chart from '@/components/Chart';
import config from './config';
// import moment from 'moment';

export default {
  components: { Chart },
  props: {
    commonViewTitle: {
      type: String,
      default: ''
    },
    chartList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    this.config = config(this);
    return {
      commonViewLoading: false,
      option: {},
      commonViewOption: [],
      markAreaArr: [],
      markLineArr: []
    };
  },
  mounted() {
    this.bindEvent();
  },
  methods: {
    onSelect(params) {
      // 点击折线上的点
      if (params.componentType === 'series') {
        const value = _.get(params, 'value[0]');
        this.$emit('onClickLine', value);
        let markArea = { data: [[{ xAxis: '' }, { xAxis: '' }]] };

        let markLine = {
          symbol: ['none', 'none'],
          label: { show: false },
          data: [{ xAxis: -1 }, { xAxis: -1 }]
        };

        this.option = this.config.option({
          markLine: markLine,
          markArea: markArea,
          commonViewOption: this.commonViewOption
        });
      }
      // 点击x轴
      if (params.componentType === 'xAxis') {
        const value = _.get(params, 'value');
        this.markAreaArr.push(value);
        let markArea = { data: [[{ xAxis: '' }, { xAxis: '' }]] };

        this.commonViewOption.forEach((item, index) => {
          item[0] == value && this.markLineArr.push(index);
        });

        let markLine = {
          symbol: ['none', 'none'],
          label: { show: false },
          data: [
            { xAxis: this.markLineArr[0] ? this.markLineArr[0] : -1 },
            { xAxis: this.markLineArr[1] ? this.markLineArr[1] : -1 }
          ]
        };

        if (this.markAreaArr.length > 1) {
          markArea = {
            data: [
              [{ xAxis: this.markAreaArr[0] }, { xAxis: this.markAreaArr[1] }]
            ]
          };
        }
        this.option = this.config.option({
          // visualMap: visualMap,
          markLine: markLine,
          markArea: markArea,
          commonViewOption: this.commonViewOption
        });

        if (this.markAreaArr.length == 2) {
          this.$emit('onClickxAxisTwice', this.markAreaArr);
          this.markAreaArr = [];
          this.markLineArr = [];
        }
      }
    },
    datazoom(params) {
      // const { line } = this.$refs;
      // const chart = line.chart;
      // let startValue = chart.getModel().option.dataZoom[0].startValue;
      // let endValue = chart.getModel().option.dataZoom[0].endValue;
      // const startTime = this.commonViewOption.filter((item, index) => {
      //   if (index == startValue) return item;
      // });
      // const endTime = this.commonViewOption.filter((item, index) => {
      //   if (index == endValue) return item;
      // });
      // const res = [startTime[0][0], endTime[0][0]];
      // this.$emit('onClickdatazoom', res);
    },
    setLineOption() {
      this.option = this.config.option({
        commonViewOption: this.commonViewOption,
        commonViewTitle: this.commonViewTitle
      });
    },
    bindEvent() {
      setTimeout(() => {
        const { line } = this.$refs;
        line &&
          line.on('click', e => {
            this.onSelect(e);
            // this.getZrFn(e);
          });
        line &&
          line.on('datazoom', e => {
            this.datazoom(e);
          });
      }, 300);
    },
    reset() {
      let markArea = { data: [[{ xAxis: '' }, { xAxis: '' }]] };

      let markLine = {
        symbol: ['none', 'none'],
        label: { show: false },
        data: [{ xAxis: -1 }, { xAxis: -1 }]
      };

      this.option = this.config.option({
        markLine: markLine,
        markArea: markArea,
        commonViewOption: this.commonViewOption
      });
    }
  },
  watch: {
    chartList: {
      handler(newVal) {
        this.$set(this, 'commonViewOption', newVal);
        this.setLineOption();
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  flex: 1;
  margin-left: 24px;
  &:first-child {
    margin-left: 0;
  }
  .report-data {
    height: 320px;
  }
}
</style>
