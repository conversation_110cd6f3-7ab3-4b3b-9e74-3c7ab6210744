export default function (ctx) {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 50
    },
    {
      title: '问题存储过程',
      dataIndex: 'function_title',
      key: 'function_title',
      scopedSlots: { customRender: 'function_title' },
      width: 200
    },
    {
      title: '所属文件',
      dataIndex: 'file_path',
      key: 'file_path',
      width: 150
    },
    {
      title: '未关闭游标',
      dataIndex: 'cursors',
      key: 'cursors',
      width: 150,
      scopedSlots: { customRender: 'cursors' }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      scopedSlots: { customRender: 'status' }
    },
    {
      title: 'AI/人工',
      key: 'manual_status',
      dataIndex: 'manual_status',
      width: 100
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 80
    }
  ]
  const searchFields = [
    // {
    //   type: 'Input',
    //   label: '问题存储过程',
    //   key: 'function_body'
    // },
    {
      type: 'Input',
      label: '所属文件',
      key: 'file_path',
      mainSearch: true,
      props: {
        placeholder: '所属文件'
      }
    },
    {
      type: 'Input',
      label: '问题存储过程',
      key: 'function_title'
    },
    {
      type: 'Select',
      label: '状态',
      key: 'status',
      props: {
        mode: 'multiple',
        separator: ',',
        options: [
          {
            label: '未知',
            value: '0'
          },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '不通过',
            value: '-1'
          },
          {
            label: '白名单通过',
            value: '2'
          },
          {
            label: '错误',
            value: '9'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'AI/人工',
      key: 'ai_manual',
      sourceKey: 'manual_status',
      props: {
        options: [
          {
            label: 'AI',
            value: 'ai'
          },
          {
            label: '人工',
            value: 'manual'
          }
        ]
      }
    }
  ]
  return {
    columns, searchFields
  }
}