<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码审核-在线</title>
    <style>
        body {
            background-color: #f0f0f0;
        }

        .header {
            height: 40px;
            background: #002c8c;
            margin-bottom: 20px;
            border-radius: 8px 8px 0 0;
        }

        h2 {
            font-size: 38px;
            color: #000000;
            letter-spacing: 0;
        }

        h3 {
            padding: 0;
            margin: 10px 0;
        }

        .mlr10 {
            margin: 0 10px;
        }

        .ml10 {
            margin-left: 10px;
        }

        .plr10 {
            padding: 0 10px;
        }

        .flex1 {
            flex: 1;
        }

        .margin-right-10 {
            margin-right: 10px;
        }

        .margin-right-5 {
            margin-right: 5px;
        }

        .rowBC {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .rowCC {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .rowSC {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .rowAC {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .rowCS {
            display: flex;
            justify-content: space-around;
            align-items: flex-start;
        }

        .top {
            border-radius: 8px;
            box-shadow: 2px 2px 20px rgb(0 0 0 / 50%);
            margin: 10px 20px;
            background-color: #fff;
        }

        .h_right {
            color: #fff;
        }

        .sql-review-text {
            background-image: linear-gradient(bottom, #3facff, #6affbe);
            background-image: -webkit-linear-gradient(bottom, #3facff, #6affbe);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-fill-color: transparent;
            color: #3facff;
            font-weight: 600;
            font-size: 16px;
        }


        .date-time {
            margin-left: 15px;
        }


        .h_left {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #ffffff;
            letter-spacing: 0;
            line-height: 22px;
            font-weight: 400;
        }

        .top_content {
            margin: 0 20px;
            padding-bottom: 20px;
        }

        .statistics>div {
            padding: 10px 30px;
            text-align: center;
            border-radius: 5px;
        }

        .nopass {
            background: rgb(236, 208, 213);
            border: 1px solid rgb(246, 164, 164);
        }

        .border_left {
            border-left: 1px solid rgba(170, 170, 170, 0.274);
            height: 60px;
            display: inline-block;
        }

        .border_bottom {
            border-bottom: 1px solid rgba(170, 170, 170, 0.274);
            width: 100%;
        }

        .nopass_color {
            color: red;
        }

        .top_table {
            margin-top: 20px;
            margin: 0 20px;
            padding: 20px 0;
        }

        .top_table>div {
            width: 33%;
        }

        .top_table h3 {
            font-family: PingFangSC-Semibold;
            font-size: 20px;
            color: #000000;
            letter-spacing: 0;
            line-height: 28px;
            font-weight: 600;
        }

        .table_th {
            text-align: left;
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #a1a1aa;
            letter-spacing: 0;
            line-height: 22px;
            font-weight: 500;
        }

        .top th {
            text-align: left;
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #a1a1aa;
            letter-spacing: 0;
            line-height: 22px;
            font-weight: 500;
            height: 45px;
        }

        tr {
            border-top: 1px solid rgba(233, 229, 229, 0.705);
        }

        .top td {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #27272a;
            letter-spacing: 0;
            line-height: 16.38px;
            font-weight: 500;
            height: 44px;
        }

        .risk_table {
            border: 1px solid rgba(145, 202, 255, 1);
            width: 100%;
            border-radius: 8px;
            margin-left: 20px;
        }

        .risk_table_body {
            height: 300px;
            overflow: hidden;
            overflow-y: scroll;
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #27272a;
            letter-spacing: 0;
            line-height: 16.38px;
            font-weight: 500;
        }

        .risk_table .th {
            background: #91caff61;
            color: black;
        }

        table {
            border-collapse: collapse;
        }

        .risk {
            height: 46px;
            padding: 0 10px;
            border-top: 1px solid rgba(233, 229, 229, 0.705);
        }

        .examine {
            height: 46px;
            padding: 0 10px;
            border-top: 1px solid rgba(233, 229, 229, 0.705);
        }

        .risk div:first-child {
            width: 80%;
            display: flex;
            align-items: center;
        }

        .risk div:last-child {
            width: 20%;
        }

        .risk div:last-child span {
            margin-left: 10px;
        }

        .top tr td:not(:first-child) {
            padding-left: 18px;
        }

        .no_border {
            border: none;
        }

        .div_border_left {
            border-left: 1px solid rgba(233, 229, 229, 0.705);
            width: 1px;
            height: 347px;
            margin: 0 20px;
        }

        .high_risk {
            border: 1px solid #f5222d;
            background: #ffa39e50;
            padding: 1px 10px;
            border-radius: 5px;
            font-size: 14px;
            color: #f5222d;
            letter-spacing: 0;
            line-height: 22px;
            font-weight: 400;
            width: fit-content;
        }

        .low_risk {
            border: 1px solid #ffd591;
            background: #fff7e6;
            padding: 1px 10px;
            border-radius: 5px;
            font-size: 14px;
            color: #fa8c16;
            letter-spacing: 0;
            line-height: 22px;
            font-weight: 400;
            width: fit-content;
        }

        .no_risk {
            border: 1px solid #b7eb8f;
            background: #f6ffed;
            padding: 1px 10px;
            border-radius: 5px;
            font-size: 14px;
            color: #52c41a;
            letter-spacing: 0;
            line-height: 22px;
            font-weight: 400;
            width: fit-content;
        }

        .abnormal {
            border: 1px solid #d9d9d9;
            background: #fafafa;
            padding: 1px 10px;
            border-radius: 5px;
            font-size: 14px;
            color: #000000;
            letter-spacing: 0;
            line-height: 22px;
            font-weight: 400;
            width: fit-content;
        }

        .risk_table_body::-webkit-scrollbar {
            width: 10px;
            /* 水平滚动条的高度 */
            height: 10px;
            /* 垂直滚动条的宽度 */
        }

        /* 设置滚动条的滑块悬停样式 */
        ::-webkit-scrollbar-thumb:hover {
            background: #cfcccc;
            /* 滑块悬停时的颜色 */
        }

        .icon {
            background-size: 17px;
            background-repeat: no-repeat;
            height: 20px;
            width: 17px;
            display: inline-block;
        }

        .height-warn {
            height: 22px;
            width: 20px;
            display: inline-block;
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAtCAYAAADV2ImkAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIySURBVHgB7ZdPbtNAFIffG+cPRSK4G1TTRnJvYCQo7FqfoDlCcgLKCRAnQJwAeoLCCZIlDSx8g46UVkasIrFAxKkfM04rNW0T+8XjqIv5NlasZ+fT6PeeZwAsFovFYmGAYIAzN3CbrVpXkNgngEDd8mcvp7H6HV0CHDtJMvB+RRJKUkpYiz5qNd4i0REBuvlP4BdI/n0oI76ycLwV+FBv9OFqNRlISCbhqtIrCZeQvf7T8TSlcOfiRwRMBDDRMSgjq1G5dh2BJ9m7mLCFN1r191BC9gb+xpPGZ2DCisRVFM7AJJSG3vnPQdFy1gqntUaXU6/HWjYZdKMt4BKwAwxYwgJwn1EuyXFeeKPTnlrF3qIiB/EQGPAyjHRQsFKCI0JPfpez59BfUutzmq+wcOy/8QuWzsn+3g4C1Sgflz3Q3Hz8FArCnhI53JFNRb2f9xXEZLILBTEpPB8DxRSbXaUzmDWfGWpgCtVYnhzKm7een58e6Wu88/JA5bgPBjAdiXuZgjmMrTCiOInbryIEHP/9M+ntjqMsBtnHBgX7i7YIY8J6f6CmQSAcCudkS+47bmMsErqxhAPhMznbgVUhqzEmrHL6rmpZjTFhh0jqa5WyGnNjTTVW3H6tZm7a0XmGijAnnK0odcnMuXYha5nDJrHCVZMbuIv2Xkdt3A/VnHVVM7FOB8WhgVo7mQJ92x4Nvy6rzBWO23sEa0If/7dGw81lNbmRIKJPsCZSomOwWCwWy4PmP1vwtZLVHWWgAAAAAElFTkSuQmCC);
            background-size: 20px;
            background-repeat: no-repeat;
        }

        .xiaojin {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJVSURBVHgB7VZBctpAEOwRBK4+2Kkc5RcEvwB4QcgLgl9gDknZNzs3cA6EFxj/AF4APwh+QXR0IFXRMTaJNj2rJYVTEpGxFFel3FWqXa1WO729szMDPOGRIWkfdjpm51kVp5zQ4mvIdvilJwP8CwJqvFLFJ3b99XFDEoueHKJoAnvvTFs8XLAbRIJDTvLFoK/cTISDxQeZpS1oyVdwxJU7jvRoKXgfdiVImu8lshI0bGsw+NqV6aIrQ640dp9r2AAeW5/Gz5SLPiTfrhhMlFhmAkRg2Qvqdlcnxofrm1L8LQm7b01DDSJWrukJ9klc1fKrFTuejYDnYcgm5NN6fmy+cQefEftDoIogBVTOd90rnXdN2Q1VdJt5mZmA/TFCE7ESVjqe5ZQ7amID1tSpq2oqO33pyJIzuEr6RzYtqJJ6JUy0P+/Jxrkr7B2bCSc2/hgOlPx1giOm+cDWWN7gNeW6XBuapRkvhED4UcL5ubSxcuQIgzTjhRC4Lx6dQBk5wsaBEl65WGBvD103RFEE9JqVy6jR6BuXtO5EO726i3MZIU8CWYwy+Ix/3GKoDvm39TIT0ARFj6nnYXQrAi475mJ0KwJESKMzxs7L5XeMHmJ0KwK3N9jPy+g6UuMAz7zGrNhfvbNCurBpOWckKqCGmL00Camzha5tMS37bA+QIxIVqES/A8lIpbeFRUyktntiGiiaAFwxykQy1nN3yWSqY/LzbqH6UCQeQaQpFAw0Hk4ZXgM33HDtDDnivmX5gGV5B0UTULygI0aRrW7jWs7DeN6VMzzhf8Mv0N3djRKAxR0AAAAASUVORK5CYII=);
        }

        .right_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAhCAYAAAC4JqlRAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKZSURBVHgB7VdNdhJBEK7qCS50wxKSzeQEIUsNieEEDniAkBMYTgCcIM8TiAcQxhOQaKJL8ATOxsByVrqIdFk1QBDmhx5+Xjb53uPRf9NfdXVVdRXAEx4ZmGax8zV3qhDfEOApEtqAlA0mCH1C3efN+pros3s8vDLd00gA53avqojq3LTBDB4gNttHv1rLFiYK4HRztpVRHQIowGrw9L0uuaWhF7dAxU28/b53pnas3hrkAltlVM/5knfiFkRqwPmWd5TGDmwQaOnqp5fDj6HxxQFRu5z8wcA2B5+v43DxOkJXwCrrboFckEW2pxDf/x2xdjC39NRgdRcqY45oASautl0scDwIIEEG1j09QbNdvEO+630+rx+zyp5wzQuAYDmwHrvXPr5rBJvuqEvux9oRAjohAQD1AazMLRZOJWlWbnfrzJB8GITXIQEQVCGZg67kFzmpdE3cS1yYFzZgCVgDdkiAJJUFs0TNTnFQ0prKC1Ot9tGw5XSz2cCFzZCNECAZCtUHIXFPBi4Rvp+IxbH+dy2Yz7xI81jN9p01Y612Co7rzy8D2r+Zxpic+KHx/XH8oAswhx8SAOUJXY5q5SZ/4ZY8Nro/h9N7Txs/2Jb6IQE0wbXZ51gXUjm59KxxeLUhDQh+TJs7s7GRy57wzuDzIKazEGX1TJ0RpX+uWQPutD33GrJ6f/KQDVsFB6ziYH/am/MCjdSEbWOBI5QPlG92e7heFpSA+dMLQnGA7nXZwCVX4J6F60QBxLW0HtVgw9AWnUclp5GR0D0ZtsYhdwOa4JNzin7uvhq4UdNL03KVwe6qniGFyoivNCktNyxMclyYYN1cEPLE2uWRWrYydWkWJBMIB1yaFfh/UpoBl2YcyomuJcikKc2e8A973A9aM5e/oQAAAABJRU5ErkJggg==);
        }

        .err_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAhCAYAAAC4JqlRAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJKSURBVHgB7ZbNUeswEIB3bfyO7/GoIHQAFeDcGIYEd0BSAVBBnA7SAaEDQyDDDagAl5ASfGQI1rL6cZgE25LNgUs241gaa1efpNXuAmxlK78s2GQwhdEu/BUXQBhyt2MeKRk/KYC4haWf4GOycDTpBkDHUQcCuuZmCG4yhSWOXUA82wDqRxc8+etqcoJnyMUQfNzn5z/ObpHfh7yWIRDdGLWB1KFe79Jmv3YH6DQaAVK8mngHh5jUr4oi3q0PEQPiuZ6BYry7GzcGUCsnmphuxlt62ORsGZ4haKR74gpns4kzgDlzue27oB1MvhcM0W0JUbmAch8IlJKcdKrPF6SihHpScI6C90msjk7a0k4MVgAzwUB1fPZkeeY+dttCSL8xrVBdYxsABBCZ1rRwuJ9AKF29C8Ax5NIOQHRmVF++GWq7E0Lo60l4ZAdAOFDvXEa2jU9tIf74z6bVsQNo5wOcJ2nJt7YQWRMAJWUO0xaCx2dV38oAFup/z/sHNdIEgk6iA9PM7ABkAN6WXbCIM4SfFwCpHaDwft87Bwdxg/C09wtO11aAHShidtjorldArAW2wE+sAMphisBRET4bQQRFQvoKbLUASnT4lA4TUr8/gvYQMqENlC0O62U6XqUhMAqEcUuIIovy2ZucUja+zth6Tncrs76Vb0hjLkjiqvHWmpB6ESeQVXrWICBeIM9TnM9TFbDk7z0Pzc0JzbhMrfwhmdTZdytKN8ssq4Jb+eYMsAaSc7qWGVMnrWJXFjqAcQzha1wXereylU35BMCwPD5IxVzrAAAAAElFTkSuQmCC);
        }

        .oracle_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAI8SURBVHgB7ZdfbtNAEMa/2SCBhBDmBDggHnhzuEDccICmJ6CcoOEESW4AJ0CcgHKBxlyAOE+8APENCBIPrdTudMZKJHttN/0Tuy/+PcTJ7Mj7aXZ2vw3Q0tJSL3SdpHkYeI8BX5I93JL/QNyL4tW2PLpKhGfMERMPwQiwCwgxMR2fWvvldRQn5SklLAdvxgwe4Q4V2a6NJt2TH9NiPMPPMPAfdujrziqyneTM8l62WpQTY2gmX300S07Ug030EdFnrhLDiJj5GxtsbcoyyELa0fSlDYYlw77OLc+9NFc/fg2CQ4M0WBBCzO+7FQ14U5ayCkyYgOhdcSr+8PIk/pgK+jPoLeFURxKmkjBBDfweBBNp6rETXv2z3KXS6jCSF7N5FzWyfNubMSPMxizxgSGYfTf5QpYJNXN+wYUt32HsGyL2c1GpzqsojlAz6zkS5Kam0BTPHF6gKZi/OxHfuDlE+It7RAXlzhZptGdoCDL03AmtZIPl11FK1FdjRQOw0y7EiA0X19F7YjBEzehxA8e8ZdsvjLU4dpOlbGP1NtSEvtsUD0aoFqPbTxo5csZS169DlLZDeqNwfVOWS7Wku6zskNLjQN1/XdqdCNF3PTU0L7veiGcepM9NoMJfNqy04aThE9wOT7wxRMWFL+ubuQuaiBK3pSM0iIj5JGJGm9+5g1EHVC0aYl2ZUTZWfqfWZu7Ihc1x4x0qicTAp2WeeeXfIBVmDQ7FgfsizscdkFaILexCtnbUhHm3tLRUcQlj1e4+HyxjhAAAAABJRU5ErkJggg==);
        }

        .DB_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEuSURBVHgB7ZbLbYNAEIZ/lsc5JTgVhFSQuIOU4BYiBBK35ITEQ27BLaQCp4O4g1CCJa4IPGvfLLM7guFi80lopd1l59/ZmdEAj45za7Kqqv++71eQNOQ4dRRFz9fz6tZmaeOmMz3LT580HDCNkG6/HVo0CtDG4zj+xQSKojCu2wScybJs5fv+lm5yoHf8hiCKs8nzvA0NH/QkXxCG5YG2bXfkgTcS8ANhWALSNK1pWGMGbAJCWxDZcF33pes6jBJgSh8uJuNWAbjUgCOm8URfiDECdCESqAPv5Mn90DorDTV5noe6HkAYlgAyvlFK/QVBsIcwbA/MBUtAkiQ7iod10zSvEIZViDRTg3GUAF0HyrKUSMNxAmDIXymWhmRpSJaGZGlI1IDhGsLMceZ9cAJ8GHps+hqfXQAAAABJRU5ErkJggg==);
        }

        .right1_icon,
        .down_icon {
            height: 14px;
            width: 14px;
            background-size: 14px;
            display: inline-block;
            background-repeat: no-repeat;
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAZCAYAAAArK+5dAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAF8SURBVHgB7VQ7TsQwEHWytFE2HUj53QD2BqGjDCcAbgAnIHsClhMsnAAoqdg9AeEEmFR0OKnowhvJkVYRduwsFEg8ydI4M57xe5MxY/8YwETliOP4xvf9/bqu12wLuDqn4zhFkiR3KcB+uoDruqU087Ztn8YWURbgnC/AYEYmVooir5DtnFlionMKId6DIHiAmWHtouAR+jJFXx6ZIRzTQChUgMWl3HIUOwRLPnRuwgwBNiuweWOSDVbued5L0zRcd86YQQdqNjWdTNrDLqqqmqvijRl0ABMBJrdIvAeZDrAy9CXFtzVcn/14awab+KYvM7RFbMa4bDt8DAWMYoCbT3HzJcxcfrrH7c/6tx9VoN9kJL6goVTFW0mE3CdI/iyTd7Ow0J0xZoBn4goJu6eihH1sMmiDBaQkpHdGe9jX+O+N3yStRGEYZlLvDEuQ3jbJCUoGURSd4sleyi2XkpTMEkoGNKHSXMkBsk5O2NE5Ic8ckhTsN0DDxP4CvgDTC5lqITWGzgAAAABJRU5ErkJggg==);
        }

        .down_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAZCAYAAAArK+5dAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGESURBVHgB7VSrVsNAEJ1NFKIvn1cdsnWVwSHBIcMXAA7XUwcOvoBKHLYoisNBHbhNFDJEgUm402560p5uEx4y95zpTrszdyZ3piGqUeOvEPzheV47y7IHuHyOoyga0S8AniHyA7ixEGJPShmbfBHH8Wen09mBe4QLv9FoPCZJIn/ATZZl+cgdc5M4L0E+XT5BDtd173AcwCSC+txBFXJ07ikFPKXAcX5nFANByhdSBd5QRaRpOuQczjUMY0Ves/hFSTWDG8B2W63WB/BE27s/wXGuGmTdX7UFVBGJIiwdazqAf4/f3jXk/KQT9nGOwjC8XY8RpAHmwZr6pJmH2rxnWkjzAvL+Jh5DV0DNg0k9pfEKiroj9lDHY+ouIEsMed5osbqDZrM5w+rO9bVtO8AwL1QjZ/lKbmyUSuA4zhVIeJD855nLUFjJa6zk6bb80gJrWk9hbVgPJqF7tyy/tIAq0stfJctEIbqQRpblmlQBvKaYxxfcfUXOuk/ov8HzYKMaNYr4BskpsELwGaK+AAAAAElFTkSuQmCC);
        }

        .sql-list {
            border-radius: 8px;
            background-color: #fff;
            width: 100%;
        }

        table.sql-list tr,
        table.sql-list tr td,
        table.sql-list tr th {
            height: 54px;

            text-align: left;
            padding: 0 15px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            font-weight: 400;
        }

        table.sql-list tr td:first-child,
        table.sql-list tr th:first-child {
            padding: 0 0 0 24px;
        }

        table.sql-list tr th:first-child {
            font-size: 20px;
            font-weight: 600;
        }

        table.sql-list thead tr th {
            padding: 0 15px 0 0;
        }

        .sql-list tbody tr td {
            color: #1f1f1f;
        }

        .tr-detail-wrap {
            background-color: #fafafa;

        }

        .tr-detail-wrap.hidden {
            display: none;
        }

        .title-desc {
            margin-left: 20px;
            font-size: 14px;
            color: #1F1F1F;
            font-weight: 400;
        }

        .detail-wrap {
            display: flex;
            height: 650px;
        }

        .detail-left {
            width: 35%;
            border-right: 1px solid #f0f0f0;
            overflow: hidden;
        }

        .detail-item-title {
            border-bottom: 1px solid #f0f0f0;
            height: 46px;
            display: flex;
            align-items: center;
        }

        .check-detail-list>li>div {
            height: 46px;
        }

        ul {
            margin: 0;
            padding: 0;
        }

        ul li {
            list-style: none;
            padding: none;
            height: 46px;
        }

        li>div {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        li span.list-item-title {
            flex: 1;
        }

        .border-line {
            height: 22px;
            width: 1px;
            background-color: #f0f0f0;
            margin-right: 14px;
        }

        .ver-center {
            display: flex;
            align-items: center;
        }

        .item-detail {
            font-size: 12px;
            padding-left: 20px;
            height: auto;
        }

        .item-detail.hidden {
            display: none;
        }

        .item-detail li {
            height: 28px;
            display: flex;
            align-items: center;
        }

        .item-title-button {
            height: 18px;
            border: 1px solid rgba(217, 217, 217, 1);
            text-align: center;
            line-height: 1;
            border-radius: 4px;
            font-size: 12px;
            background-color: #fafafa;
        }

        .check-detail-list>li {
            border-bottom: 1px solid #f0f0f0;

            padding-right: 10px;
        }

        .check-detail-list>li.item-detail {
            border-bottom: none;
        }

        .check-detail-list>li.item-detail>div {
            height: auto;
        }

        .check-detail-list>li:last-child {
            border-bottom: none;
        }

        .DB_list_info tbody tr,
        .DB_list_info tbody tr td {
            border-bottom: none;
            border-top: none;
            height: 46px;
            line-height: 1;
        }

        .padding-left-20 {
            margin-left: 20px;
        }

        .tabs {
            position: relative;
            margin: 18px 24px;
        }

        .tabs li {
            height: initial;
        }

        .tabs li input {
            display: none;
        }

        .tabs li label {
            float: left;
            text-align: center;

            line-height: 32px;
            box-sizing: border-box;
            cursor: pointer;
            transition: all 0.3s;
            background-color: #ebebeb;
        }

        .tabs .tab1 {
            border-radius: 6px 0 0 6px;
        }

        .tabs .tab2 {
            border-radius: 0 6px 6px 0;
        }

        .tabs li label {
            padding: 2px;
            display: flex;
            align-items: center;
        }

        .tabs li label>span {

            display: flex;
            align-items: center;
            height: 28px;
            padding: 0 5px;
            color: rgba(0, 0, 0, 0.65);
            border-radius: 4px;
        }

        .tabs li input:checked+label>span {
            color: #000;
            background-color: #fff;
        }

        /* .tabs li:last-child label{ border-right: 1px solid #000;} */
        .tabs li .content {
            opacity: 0;
            visibility: hidden;
            position: absolute;
            left: 0;
            top: 31px;
            width: 100%;
            margin-top: 8px;
            box-sizing: border-box;
            text-align: center;
            transition: all 0.3s;
            display: initial;
            text-align: left;
            background-color: #fafafa;
        }

        .tabs li input:checked~.content {
            opacity: 1;
            visibility: visible;
        }

        .content-code,
        .content-plan {
            background-color: #1F1F1F;
            border-radius: 6px;
            color: #fff;
            padding: 10px;
            overflow: auto;
            line-height: 24px;
            font-size: 14px;
        }

        .content-code::-webkit-scrollbar,
        .content-plan::-webkit-scrollbar {
            width: 6px;
            height: 6px;
            background: transparent;
        }

        .content-code::-webkit-scrollbar-thumb,
        .content-plan::-webkit-scrollbar-thumb {
            background: #c7c7c7;
            border-radius: 4px;
        }

        .content-code:hover::-webkit-scrollbar-thumb,
        .content-plan:hover::-webkit-scrollbar-thumb {
            background: #c7c7c7;
        }

        /* .content-code:hover::-webkit-scrollbar-track,
        .content-plan:hover::-webkit-scrollbar-thumb {
            background: transparent;
        } */

        .content-code {
            height: 300px;
        }

        .content-plan {
            height: 200px;


        }

        .tr-sql-row,
        .open-li {
            cursor: pointer;
        }

        .padding0 {
            padding: 0;
        }

        .height-550 {
            height: 550px;
        }

        .rowC {
            display: flex;
            align-items: center;
        }

        .top .header {
            padding: 0 26px 0 23px;
        }





        .height-warn,.yellow-warn {
            height: 22px;
            width: 20px;
            display: inline-block;
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAtCAYAAADV2ImkAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIySURBVHgB7ZdPbtNAFIffG+cPRSK4G1TTRnJvYCQo7FqfoDlCcgLKCRAnQJwAeoLCCZIlDSx8g46UVkasIrFAxKkfM04rNW0T+8XjqIv5NlasZ+fT6PeeZwAsFovFYmGAYIAzN3CbrVpXkNgngEDd8mcvp7H6HV0CHDtJMvB+RRJKUkpYiz5qNd4i0REBuvlP4BdI/n0oI76ycLwV+FBv9OFqNRlISCbhqtIrCZeQvf7T8TSlcOfiRwRMBDDRMSgjq1G5dh2BJ9m7mLCFN1r191BC9gb+xpPGZ2DCisRVFM7AJJSG3vnPQdFy1gqntUaXU6/HWjYZdKMt4BKwAwxYwgJwn1EuyXFeeKPTnlrF3qIiB/EQGPAyjHRQsFKCI0JPfpez59BfUutzmq+wcOy/8QuWzsn+3g4C1Sgflz3Q3Hz8FArCnhI53JFNRb2f9xXEZLILBTEpPB8DxRSbXaUzmDWfGWpgCtVYnhzKm7een58e6Wu88/JA5bgPBjAdiXuZgjmMrTCiOInbryIEHP/9M+ntjqMsBtnHBgX7i7YIY8J6f6CmQSAcCudkS+47bmMsErqxhAPhMznbgVUhqzEmrHL6rmpZjTFhh0jqa5WyGnNjTTVW3H6tZm7a0XmGijAnnK0odcnMuXYha5nDJrHCVZMbuIv2Xkdt3A/VnHVVM7FOB8WhgVo7mQJ92x4Nvy6rzBWO23sEa0If/7dGw81lNbmRIKJPsCZSomOwWCwWy4PmP1vwtZLVHWWgAAAAAElFTkSuQmCC);
            background-size: 20px;
            background-repeat: no-repeat;
        }

        .xiaojin {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJVSURBVHgB7VZBctpAEOwRBK4+2Kkc5RcEvwB4QcgLgl9gDknZNzs3cA6EFxj/AF4APwh+QXR0IFXRMTaJNj2rJYVTEpGxFFel3FWqXa1WO729szMDPOGRIWkfdjpm51kVp5zQ4mvIdvilJwP8CwJqvFLFJ3b99XFDEoueHKJoAnvvTFs8XLAbRIJDTvLFoK/cTISDxQeZpS1oyVdwxJU7jvRoKXgfdiVImu8lshI0bGsw+NqV6aIrQ640dp9r2AAeW5/Gz5SLPiTfrhhMlFhmAkRg2Qvqdlcnxofrm1L8LQm7b01DDSJWrukJ9klc1fKrFTuejYDnYcgm5NN6fmy+cQefEftDoIogBVTOd90rnXdN2Q1VdJt5mZmA/TFCE7ESVjqe5ZQ7amID1tSpq2oqO33pyJIzuEr6RzYtqJJ6JUy0P+/Jxrkr7B2bCSc2/hgOlPx1giOm+cDWWN7gNeW6XBuapRkvhED4UcL5ubSxcuQIgzTjhRC4Lx6dQBk5wsaBEl65WGBvD103RFEE9JqVy6jR6BuXtO5EO726i3MZIU8CWYwy+Ix/3GKoDvm39TIT0ARFj6nnYXQrAi475mJ0KwJESKMzxs7L5XeMHmJ0KwK3N9jPy+g6UuMAz7zGrNhfvbNCurBpOWckKqCGmL00Camzha5tMS37bA+QIxIVqES/A8lIpbeFRUyktntiGiiaAFwxykQy1nN3yWSqY/LzbqH6UCQeQaQpFAw0Hk4ZXgM33HDtDDnivmX5gGV5B0UTULygI0aRrW7jWs7DeN6VMzzhf8Mv0N3djRKAxR0AAAAASUVORK5CYII=);
        }

        .right_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAhCAYAAAC4JqlRAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKZSURBVHgB7VdNdhJBEK7qCS50wxKSzeQEIUsNieEEDniAkBMYTgCcIM8TiAcQxhOQaKJL8ATOxsByVrqIdFk1QBDmhx5+Xjb53uPRf9NfdXVVdRXAEx4ZmGax8zV3qhDfEOApEtqAlA0mCH1C3efN+pros3s8vDLd00gA53avqojq3LTBDB4gNttHv1rLFiYK4HRztpVRHQIowGrw9L0uuaWhF7dAxU28/b53pnas3hrkAltlVM/5knfiFkRqwPmWd5TGDmwQaOnqp5fDj6HxxQFRu5z8wcA2B5+v43DxOkJXwCrrboFckEW2pxDf/x2xdjC39NRgdRcqY45oASautl0scDwIIEEG1j09QbNdvEO+630+rx+zyp5wzQuAYDmwHrvXPr5rBJvuqEvux9oRAjohAQD1AazMLRZOJWlWbnfrzJB8GITXIQEQVCGZg67kFzmpdE3cS1yYFzZgCVgDdkiAJJUFs0TNTnFQ0prKC1Ot9tGw5XSz2cCFzZCNECAZCtUHIXFPBi4Rvp+IxbH+dy2Yz7xI81jN9p01Y612Co7rzy8D2r+Zxpic+KHx/XH8oAswhx8SAOUJXY5q5SZ/4ZY8Nro/h9N7Txs/2Jb6IQE0wbXZ51gXUjm59KxxeLUhDQh+TJs7s7GRy57wzuDzIKazEGX1TJ0RpX+uWQPutD33GrJ6f/KQDVsFB6ziYH/am/MCjdSEbWOBI5QPlG92e7heFpSA+dMLQnGA7nXZwCVX4J6F60QBxLW0HtVgw9AWnUclp5GR0D0ZtsYhdwOa4JNzin7uvhq4UdNL03KVwe6qniGFyoivNCktNyxMclyYYN1cEPLE2uWRWrYydWkWJBMIB1yaFfh/UpoBl2YcyomuJcikKc2e8A973A9aM5e/oQAAAABJRU5ErkJggg==);
        }

        .err_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAhCAYAAAC4JqlRAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJKSURBVHgB7ZbNUeswEIB3bfyO7/GoIHQAFeDcGIYEd0BSAVBBnA7SAaEDQyDDDagAl5ASfGQI1rL6cZgE25LNgUs241gaa1efpNXuAmxlK78s2GQwhdEu/BUXQBhyt2MeKRk/KYC4haWf4GOycDTpBkDHUQcCuuZmCG4yhSWOXUA82wDqRxc8+etqcoJnyMUQfNzn5z/ObpHfh7yWIRDdGLWB1KFe79Jmv3YH6DQaAVK8mngHh5jUr4oi3q0PEQPiuZ6BYry7GzcGUCsnmphuxlt62ORsGZ4haKR74gpns4kzgDlzue27oB1MvhcM0W0JUbmAch8IlJKcdKrPF6SihHpScI6C90msjk7a0k4MVgAzwUB1fPZkeeY+dttCSL8xrVBdYxsABBCZ1rRwuJ9AKF29C8Ax5NIOQHRmVF++GWq7E0Lo60l4ZAdAOFDvXEa2jU9tIf74z6bVsQNo5wOcJ2nJt7YQWRMAJWUO0xaCx2dV38oAFup/z/sHNdIEgk6iA9PM7ABkAN6WXbCIM4SfFwCpHaDwft87Bwdxg/C09wtO11aAHShidtjorldArAW2wE+sAMphisBRET4bQQRFQvoKbLUASnT4lA4TUr8/gvYQMqENlC0O62U6XqUhMAqEcUuIIovy2ZucUja+zth6Tncrs76Vb0hjLkjiqvHWmpB6ESeQVXrWICBeIM9TnM9TFbDk7z0Pzc0JzbhMrfwhmdTZdytKN8ssq4Jb+eYMsAaSc7qWGVMnrWJXFjqAcQzha1wXereylU35BMCwPD5IxVzrAAAAAElFTkSuQmCC);
        }

        .oracle_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAI8SURBVHgB7ZdfbtNAEMa/2SCBhBDmBDggHnhzuEDccICmJ6CcoOEESW4AJ0CcgHKBxlyAOE+8APENCBIPrdTudMZKJHttN/0Tuy/+PcTJ7Mj7aXZ2vw3Q0tJSL3SdpHkYeI8BX5I93JL/QNyL4tW2PLpKhGfMERMPwQiwCwgxMR2fWvvldRQn5SklLAdvxgwe4Q4V2a6NJt2TH9NiPMPPMPAfdujrziqyneTM8l62WpQTY2gmX300S07Ug030EdFnrhLDiJj5GxtsbcoyyELa0fSlDYYlw77OLc+9NFc/fg2CQ4M0WBBCzO+7FQ14U5ayCkyYgOhdcSr+8PIk/pgK+jPoLeFURxKmkjBBDfweBBNp6rETXv2z3KXS6jCSF7N5FzWyfNubMSPMxizxgSGYfTf5QpYJNXN+wYUt32HsGyL2c1GpzqsojlAz6zkS5Kam0BTPHF6gKZi/OxHfuDlE+It7RAXlzhZptGdoCDL03AmtZIPl11FK1FdjRQOw0y7EiA0X19F7YjBEzehxA8e8ZdsvjLU4dpOlbGP1NtSEvtsUD0aoFqPbTxo5csZS169DlLZDeqNwfVOWS7Wku6zskNLjQN1/XdqdCNF3PTU0L7veiGcepM9NoMJfNqy04aThE9wOT7wxRMWFL+ubuQuaiBK3pSM0iIj5JGJGm9+5g1EHVC0aYl2ZUTZWfqfWZu7Ihc1x4x0qicTAp2WeeeXfIBVmDQ7FgfsizscdkFaILexCtnbUhHm3tLRUcQlj1e4+HyxjhAAAAABJRU5ErkJggg==);
        }

        .DB_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEuSURBVHgB7ZbLbYNAEIZ/lsc5JTgVhFSQuIOU4BYiBBK35ITEQ27BLaQCp4O4g1CCJa4IPGvfLLM7guFi80lopd1l59/ZmdEAj45za7Kqqv++71eQNOQ4dRRFz9fz6tZmaeOmMz3LT580HDCNkG6/HVo0CtDG4zj+xQSKojCu2wScybJs5fv+lm5yoHf8hiCKs8nzvA0NH/QkXxCG5YG2bXfkgTcS8ANhWALSNK1pWGMGbAJCWxDZcF33pes6jBJgSh8uJuNWAbjUgCOm8URfiDECdCESqAPv5Mn90DorDTV5noe6HkAYlgAyvlFK/QVBsIcwbA/MBUtAkiQ7iod10zSvEIZViDRTg3GUAF0HyrKUSMNxAmDIXymWhmRpSJaGZGlI1IDhGsLMceZ9cAJ8GHps+hqfXQAAAABJRU5ErkJggg==);
        }

        .db2_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAM7SURBVHgB7Vg7UhtBEO0eUWUTWWRObA2ZM/YGiBPIJAY5kbgBnADpBLZOgBy4UDmxOAHiBNYRRjgiQg5c/lRp2t27s+xopf0UNkKBX9XT7vTMzr7pnu5ZAEgQMM+Yt0xaEW/dOzWk0FqxkGVsxWL0IwvxvRVU+OedC9dj46kQIVJWhfXAVARRRucx85vXFi8+Y14tGVuHaA90mcazi/fjxR559qrrW8AGZKPOnHptnTNWFjVi7jBrnn3s5jHM3ZSgpVCQjSokD1ZTjG3aa09TbQG66yQ1r4Z7CKq7hydugsCjhC1efeDGTN29dm3hyNnHnq0KOUmUFzJwk5kMm3DPs3/OeKaXatcgB0WCfBE+dMYYWX0cpjrziydG2iMoyGipQ52c/p/MTSfgubNJvfgF0QY+dH1xNr5ywm4g8kTg+nbc/Y0bo7NemLeHjMepx9gWb1R/UxuYLxU6dfXHLUVeyN57kxuI3K1hvg6NvXupUReerebGy7MtmF+gzLG0DuV5aAvKQ154nLLF++4UFstBJvIq9aNAwZph7QRtqEEN1gn/Q1aEBxfEKWxo8ejJRAlB1LeHE7wj4TYQfXCdZrEPu7EQbu/R4WSbuWUBT6AEyh6uoAYvz+SKNLuaNb+2uc2rxobYKucv2oS4C2iNPbju4KC2i0AX1JyM+P4Skcb2wJzQoNbA6JD9e0H88rb88ovbLABmP1RHbVIoiADr3N8iCo+FLpfbKRt33CLqRBhV6cgO/0hQgtAbR6YPrmTY5nWbL+2w8UkHHLvXyVcIe5ewNzvXGonqRXPfSxDYuRM9CRlvXvudPfSU9xHSqWxm9s4+241CuoQS59m9BFlSffiog/gYjEMWNp7AleUvA/YPhxDDL0onRpeZu3TaW7JHETmTfvOKN+jsrg9U3/XtwVszVMo2SLKtaQx3Vtke9hHCsOg9yEdH7jaTScjOencrIBVAhb1B4Rcgp/Ys+XtLYZX3S0MSwFq7z2Gbqz+IldOiLCsUtGpIyEpX0QcHlwXFVfcC1gVEQ2UrSr6d18JLnL1dBW/MmDOg1DnzUJB6hZyJkpVJOeVKqsB2uMDJebOSf8+EQoCGVqmeOEZsfwC/x5Mq4krTTgAAAABJRU5ErkJggg==);
        }

        .hive_icon {
            background-image: url(data:image/png;base64,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);
        }

        .goldendb_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAOtSURBVHgB7VZNbhRHFP5edY/lJELp7ANuToBzgngUlIiVzQlgFiGysmBGsSJlhb0GY7OILGBhOAH2CiGMPJyA5gYNXKCRkDDT3fV4VdNl5qenPfYwYjOfVOquV6/qfV3vr4EZZphhuiD3Eq4/CdLvfwiJKHAynzmJ//09GrX5wubBrsr1vUEdI2fO99+t/bGHU8J3L3zu3CuZhL2LzNSWR33UZiG/lBE9LpNzTi9xBlhC4e3nSyyPN/9cJnxj+OMoLdw9OARjUV6NOxNWtP229duGWz9/59mKUv7W8QbmsE9ezCVAIiKvzjoXl+I9EZbtmYU8btUTdRKZcOtpKGSWqJP+IuOiBrVIc7NXh0ArYmSfPnXqZoh+1CeXfWaIJMiybFEWAiEZuTPtx6ZpOPYNGcT/XYktwdvPY7mhYEiBKXE6C3dfBNCl8i/qJmGcfPMAWZFMpYTEaFMX7gGyU2fKJCglpIlCie5Lki6hTCciJBm3LDH4q524WKpAaQy9XbvcJOYNfAUw9Ev6lDbskHjx/Q9Rlf7YMXR2Rl9iyEFuDNWE/CyGruH85ovrbkErLFDOiZv/LLXKyj3/EnGeDJ2k1ILTEdcEpXJjyvcjqeKoJBS3rsQX7hzcU+CbcK1DyJCWtrDWXfOItuyazpNjd0rqmvYiftlmn7c8pXZFFjPrfY9y+UivK/foVo/Nq6z1a7vuQGjbc2YYA5W9K9vBdVYDDVdqE2lE/ryU+wb6rrnzPxbJx8qQEY04BdrfrSLGRIQe4tC2DXQP4oG/AelF696f2Oj9AHjYdfrFHhOTQaG/LfqtKpsn9jJzuHcDF83wb4DMkCSpG7k0yPXOfTQH9+gcjZ49P6U5TL/aE/2mucWJCJWhtop2J7f/SYki3OJdBFX6hau6/021KRByRrhrJMiOqo30GWQkUyFkQIy2eUrgVxJKdyQOCeZ/KfbmuntGYaLWwVoyzrNx1ucykR1mD4bUI52iITGVTI3QKEjwbphUP55L6ZAbuqZqOJSgrs/9jWgqhIyrTN2Q0W9ASytY7XfNxx08qnl4Jbe3LtOVUWdOFEOSYfY/J9WITtI1SUCwLv6x8kycEfkD3IT5UhqvApvSMFhYy3Ciy0yltRW4gPyWBEphWeJkSaZxmqExtEdcKZllQaIvLgzyDNeKAx9jEkIwGSTtwE1U99DEBK4/h+2yrFEmxb0egdfdoxmtub/wCGcl5Pm4enTUn9Lz8xIHjfLU9eaxJ/rtQXnVnhlOi8++e5U4v1YWlQAAAABJRU5ErkJggg==);
        }

        .impala_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAK6SURBVHgB7ZffThNBFMa/mV0iIV7gG9Q7pSXpI9QnsD4BmCDUK5knKDzBtmIiogn6BOoTsG9gicV4Z9/AGo0idOd4zmwRTGrpLLutF/4Ssn+YZr49Z843Z4D/zIBbjaiEjGhk4PZ6VB/3f2VRrW5Gi5iGIPn6AKiOG3PtBPHpj6CJaQgKKGhawptxYzovTR+U3KisRTUUKUiio0D1D89N57KxmhBToFbgiZcgnaAG0KVihGQOHQVVhyd+gnRwl0BfJhlrLfp8WVx6EFXhgecaohLX0Gf4UaAgQgmeKAWv8veuMkVqoi8OkYpXKFAQf22fZyhNMtYm6Ti2iB488BJEUFJhi+WNx/vjxjnzVDpyExQpaKASQ0CPvWi1sr7T/JuYOdIHJGuH7Hb3hYnhgZegj7umlyh7h0PFqbNbo5w4pGCf3Pqx7e6e2YIn3otaRFFgt91D8Od+xZ6zyomtSRS7z8ymvCuvtyJ44CWo/PCJ2wqOnpoWL/FYJr8YJa31I7m6KDKVxk6TC6E4H1J0evNMAFHy1l11OmF11bUbfE+xRNGNI7vFz4fwwEtQeIyWpEl6HQoRO5FDnzmeP/MbOpTtQmn9Wp4Gin9TlCBpK7jS7p8co+72KmW3vy2kE0pUJI2K9AqX/O8qS99PjkKOVBqSJn0g90TWHO0Zr+gUwvJG+5P8ISOZemqhvBZtjmrm2c174kPLl/TduQvCnJigfnexmXcpG/rQ14V00U9NkPMiZe/9/C5d5BAaGiXZdq9l+shA9ggx3V0Th8P71IeoJvcWfhtqboKE93vGnUAG8+eOHKTt62wEjSK8jokOAqPITdDgPE39Tsb1I+Qm6NyRKXN0hBA5UNmIWorPtHxEglX0ClcglwhJ78NiXP/DFRbjCuQiKN3DpLJsm1Mnr0qYNUsN6RYdJXgeff5pfgFmBAlQI5UpbQAAAABJRU5ErkJggg==);
        }

        .kingbase_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHrSURBVHgB7ZY7TsNAEIb/teOUyAUgStMhBBIWFwg34AaYEwROALkBOUGSEwAnAFoQciQiREdKpKRwnYeHsaOgoDjxOPYWoPyF14+d9efxPBZYa61/JiWZ9LJ5UCkpdTX/ZHzp9t7byCDfdmyUN6qg0X2SbUmyCMM4PFTmHpBhQ6gfkJAuQGSz7VPiu6BZcyAp/0QbkG/vOYZlVUOCJwHRBhTHG1SVAU7D6IYQpHCgmcCvIIdyA2UGIXrkY83tdx5ROJBSDV7AEc0luuNjfRFIMUBpMIQAilowSzfuV7sLgfRkWQQCqmPEIEE7yGK6FOhXDZFkSw6QqZQAJL0aFwCSCPS8tX9UhlklcDETidohVP2499ZEQTJmLyyzHFCmUqbEvUy8YtJNf+fIwWjkcVqfQZbWXZ7bxGDQcoOPLnJoqTdisPG4wqdXIrA4zXGH4bC2Kpjo9/hbhx4PDWRTcxUwA/rkwbI++WMaUeeXGuUDIpLsFidg24e3Ud9Lm5zXQ5cMdcJjK3Um4ZSb8AN77OF1EgJagBA1S7f35sE0d0VgvCvglzb8Bd4qLIai5pkRDFqBpkoA62ax15ZlM2AnHGc1CMF0pn2sGKzfuWa4yGPnSAGT7YdCrsBGNtcnwk2acDMutCrMtStYa60/o28Qj8qh2CdXpAAAAABJRU5ErkJggg==);
        }

        .mysql_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAANcSURBVHgBzVe9dhJBGL0zSxALzxnfYDvtQpVCm1B4PFYmnZ2ktMqSQNQqpNLDj+ATQJ4gWFp4Qpku+gSSJ3A8Nghkx29mCWTDJuwPkdxz2J0ZZnfu3m++nwHuGJhwygKpB3nTGw06svm+hyWCiVL9J93tS2NtDAcHyyLGRLH+CwwCUN+pm53+w8sY9A9183+SI0K1Nhh7TU1NaAvq3PH6l6AgiXQHyj2UtVIXtwgrs/aii5R6Re3HYKovq8U3mafPz6CUTcT6NC6ITIbuWern6T87s5b70T/5JnELYPoi3jVsjNxTYzrl5q6qIEqVLBT3K0cmlZXCARYMNlm0WKcF0aChrqzu5IIme8TPy1NiZObh381F7jHmW/Bigweo5JtXqudp3j7tLZu6PfLK3KJIcX/X/TxuZG96SFZ32+A8R6R60CFjJX0snA82Fk8IXXNl1st5D8qPhZ6f1L0j4TQEEsJHyJjJLKDWRbG6Pu9hPymVRdrdR0LwmREXntmYFerlE1I6Vik4Yq+2gQSYISRru03taUalvUZ4UoAXAhRrJTEdDxy1dMTWX+yWERLTDyEvTY0cxEQgIfPFDDoSR4vG6txTifHtuCoFEhJORbu9Tb8OIsCLXROV8oiBYJOlmLcxKZkiKiYqzQ8d4QkxvqpvcTL7VKVwoSMcIapKABPwDETp0za9vIzQUJ6yIUPHfELKPZuQMSlB6YQa2gQmtVwE2Igp5RpCxrs8L0mtHNG1QFNlJBOocVxaSUdSKZgQ19UjhHhbXyVlshjyjtmsnG8jJIxKnsflTT2ViJAhQCq5rKnfLZsFaTarq35HMsGFx8E6pZKlFUbh4MBIBLxSRK3jcnAcWQ7S6fAqeR+xSemkS12qofixPuWYeioKIW/xVJPq6kNSqu0j6uIsiglkvdiRNapAh/whdSklsR7dW+S5C6uhIHYqeSSAVkjsGaVm1OKIA3elk6jMGP7pmDJHGU9uEblJMmaICa/gH9lRormnhj4gmL3ZG5uPhlSPvHJLN1OICV0RUEaPWA3QqYa7WxhYm57jzMJCAvRPvvajzM88eXafPO2RrO98wV2BPrrfVInG3kNJYA6lcAXtwfbV89xSCGmMnWKDTCh1ZrhuTy0FOqUYgliiQldhCPUh/wF1tnXWmkZ/GQAAAABJRU5ErkJggg==);
        }

        .oceanbase_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAM7SURBVHgB7VdLUhNRFD2vk1g4st1BU4o4I6zAZAWGFQArCAzEMAImVKocACuQHagrIK5AmFni4LkCM6SC6eu5rz8knZdv4cTKqUql+33uO+/+G1hiiSX+LcysC1+2pFaKUY0DbBhBqGMCWEq4uQc6tm0sHgETCUV7Ej5ZQZMn73FlOGktyXVIbFeJKfkA+IgpEIOuiWH5/+W2bS51rDxu8fp7afKQYz6Gs+iRS2qVhERdNSkBoql7xG2scm9jrSXbJFUfIZRq5YxrdzAvBDfuz+AN5oReaP1Q9spFMpWnuKLgKhZAENCnUlFYABJDTf0AJWMmkRF0AXeo9U33BdfuwXhkxNjqAav6o/Z3U1nD4g2e5d6xdihnRp3XywMd/k5+tk0nG3PaXEGDe84yh+/d4XmlgsiU8G1ERh+btx/MdX5eS855eHNojeDCaejFgTTGkSFO1NkGySjsuelqZEgJdXdbRoyOxcZjLs4NkhkHauizI1QKeMsxZH60zfEkIbenPMjgQlJz0Y9qIwdlpkzxuiURtfO2sMzqpcuaM+B3wstpZDLENGmWLImN4jwPj2iiPC/FgsZQXqOGe4aaJspU0bbvENr8AjOCoWoljTAXFKN5SzWyM8jwgQs6KGHfniaZPvCG+Iw2z9DvI9QIU0efltE9Z4Wmj6O1d1JNCBlvmFvMgxKqf+5wXV6ZP3+lGm1oZI4kxgFEmAcGTUbY5fqBaMnw4WToTejQHkXQ+ZvlNGTDwoZQnb0Y6j68asmRpEmOZDY8S6wnOI7p5FdaLgrjUcDY9/oKFx9hBjIqPK9hHs3KGPNT/lfPYDfwTiQbanqLiDmjOKfa0zkk3UBew7xlJyU7OjxagDVfmbS6/8YEOC3SLCYxTa1oYuahOiOl6y0ZrAD37Hey97KaJUk1Ox6Suy4j+OrKPNAaVnqCGjP+JywO9bVVFxP3d071FosgrWEks1DL4kDNMyC29NERUoF8qC9CKq9T4o2wWWC1QLuamBFSfGcvrCpDkjPsVDF6K7YL2kenIxHmgJYM9tP7NPdmRkbh7ZazXgdJFs2/Mkjfsqv7xeLY6ff4pUHN5ns80TgOj/WFssQSS/wX+As6OlIytjgi8gAAAABJRU5ErkJggg==);
        }

        .postgre_icon {
            background-image: url(data:image/png;base64,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);
        }

        .presto_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAPNSURBVHgB7VfdbhtFFD4zs7ZTqaBFNBUXtBkjaAoqwn6DdZ/Al6iirXmCOAKum1whFbUmT4AN3OMXQFmewBZEIfyILIILaCvZUps0sXf39Jx17Xr/3I3TSm2VT7I9s9/uzNk533xnDHCCE7zKOPOuZS0uX26Y2jKT+Ku39htXb+19k8TprUFp6Vd39/z2sAHHgAz3BA2G9XwerOiNn3x13xIC60JArdZ4UIryvsQ6IGoBUC/+hUswJ4zpDgKu04DWYAB27EbD7fqYa3G7uXq6G+WlL75GCR9Rs7/7jvgbXkTozkMNR4SYRS4uVzZp2czhACt9x+5Pcx9/2dP5Qq5DQ/SlGJSbq2+EeNYSp494RxyqslMWfcgAmUa8ddHS9GNRyKUkTeXzRo0mY/Fr1zVimhIgqqMWasjtaciI1ID+27EdlLhKA27c+d1uxx6URpM0ZxPf+v6L1+xYQL6/QT+8Km3nUlxzzwRsB2mWwLhy80GJU5nEsZ7ObQ1qT9PVTA2FgqEUGig6wQM+Vu7+YYfe+npjv4FIW59WhTRVnNaU7qCJBa8TpI94cWgU0zQlISNypCcKhlfHJC/SUd73cXzNdD1lhdmhfhzMiDfcUto8mQMaHkAbBLZQ4HqSpjwP12nSbqCpz18P8U4530UfWFPsda1/P8zZafNkTlkS3rxgVYUP/Xt/2naUu3KzVzKUUUXa9t99droZ5fXOYAVRVH0PW/9cyjePHdDZC9YaCnGD27RqlXs74aCu3d7b5Xm57Xt+ZXonsrilFJOaKBcMPXb3zCmLAuGJjoQX1lSt0Qv8aXJBYGhnKumH+sM9tzgJDuaEkrBGemizpmjHNae5YIehP9bURlRTcFhosoNzM6qpI6WMty/AgemUTzlRbvEDqwQurEihuv//9uNGlL92e3+Fpq+BkO1vV0+tp82ROSA2NCwYneChBB+hujfRDPnUp9OrxkcXqeTmuB/V1DSOkDIVeFDwWTiIuTWOuGyIaGqugNhLhHCLwsey8348ZUJhhWtbkqZGqxHUNidRU1nBx9K3fxlaaTxzabWJbOHGmeVK7+zy5R9m1b8oUlcoqD9SbCoFm0lBnfvZrTLHuhqJ/QnM96wSedQalxpatapRgBocN6ARRsI1BMYKoZpcixfJBQWha+zmkBEzd9n4zdMq8yx+8aJVQ4TrNMNP5OJrMA9YM2zraTynaRZ/fvugPs85OhVL2+7u0vYQeeAop0lHzPFHb8X/BnGgj/keHANGuIsOfZnSU/Ejp2t0QXlO0FYqrikUDo6az+e4eoITvKx4BOyor74OM15bAAAAAElFTkSuQmCC);
        }

        .sqlserver_icon {
            background-image: url(data:image/png;base64,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);
        }

        .tdsql_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAK9SURBVHgB7VRBUttAEJxdycSBi1IJAapShfICxAui3IBLzA/MC2xeAH5B4AXACzAXoIpDlBfgvCC6xTFQ0SUpYZc06RG7jmJyzInarpJ3d2ZW09MzFpGDg4ODg4PDf4UaLQYReY3oX86f+aS/0GiEhceBnHXJQalVtjK8TepxcRwHzWazVTOll5eXSd2ntQ7FXpZlAl9qAzc3N1vMnNl4nz2vq4hXiRgXVEhMA1KUyTo352Vg8dF74J6SVoR9PFp+mY4Lvf3m5mawsbERep53jZcSnoFSSsif4Um2trb2sO7jGUhS+DogFuBOGwROWq1WMB6PT+HflfiK0NLwri0bJDnC0n79/XZdzj+CIFBNv8NEIZe8vTS67Yv923IQoorrZ5olfh0JukgWTCaT9aurq4Gt3JKBb/fi4uJAbELg/v7+FHcOoNwZ9jFIVoXYe3raO1aRZSnIKiNVrZqwTq19ZZilohYriuqhjUajY2NENaPMsSUj6Pf7otIJtgHiQ+xjuW/bNSUkakiCktUX62g2fZz5hbRPWkM1IDZDg1LZn5+fS+JDUReqHFUv1VoSEeblhGYgatb2a3V1poRyJH9QqUzqTqi2Vs3UDBRzpGpqglQXS8+QklaFhthXWWHrYHi7JvwDSKTSXqPQ2SNC+BEH/RoXSa2WkGZUE9wsv9pjDG5BRa9uF6WQQMjvQ5nUmHfkB2dJKqQ+CQkQ6qGtsVHpr4L9qmKid6LE2yyr5kEG1zMBnmYeLS5GrMpQaSVDHuPpySyh8gjJpp8MaYdUn+d5f35+XmZqDzGriPlsyMqnQXIkONs9WXJFUWT+w5swvOoP0zynbOE5hpYxvIyXeNRSlZicFsQ7K8O7Y0MgRlvWagVKosMkSTL8o97jX9SWFpnWDEBsx3yP5ByZT8HRVB3ffzQeDg4ODg4ODk8NvwFPn1g5qtGooAAAAABJRU5ErkJggg==);
        }

        .tidb_icon {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALSSURBVHgB7Zixb9NAFMbfxbYIqtNGSCAhQSn/QQV0RIQVdWCkLDQDqKJDKBJCsCReQCw0HUAVDC1DBSNDxEoQElOBSowMjVokBgSkTUKLfM5xL4kj143vznZTlv6kyBff8+nz3Xfne0cgJqvJ0RFd1xcIYxXbcazT2ysViAGBiKymR9M61XO8WPDeZwBFh9K5qMIiCVofGMsBYQVeTPdslJFKExxruPFpEUISStC6eS7DL3n+y6jEozBGWPZkfbkMiigJavtEm+XhlyEC3F+Lqv4SCvL45BYEDE9IClTn/qquVIMCEkEVawNnJg3b+Axt0waK0YePw9E383BkPt8qywRhm9h2UMCuHlL1SWIoBebNKzB4/8aO+5sPnsGfpRLQte+ix1v+sh37on8Yu4JweAxbm2WETIIEVwiK6gWKqXFhDS5Mht9fuluRsLUiF3NN9PCh82dh8N711lWENmQCSadABewAQzOwmN0hSAR6I/3oNiTHM+LGN2pQe/oK6k9eQpOXoyAU5PrEnJ4IHB6Xv+8/wu8pS+qdWIKS4xd2mTaIH5emhPXuy9lfvsJWqRwcB/vAYT7Uxz4sdSaCKYxV8lBUVCeBl1A9hD75OXFHOT6sGESphxxu1F/csChIYTWOhVAQ26jD5sPnsabxngrC2SCaEf1gX2ZZGA4EyTgQJKOvK7UXdyewVXoH/13QNl86qncfK+0E+ioIV3ZcWPGqStdDTccpMMZeyB6gnc+Io/C2KmJaW1jDnun+9wdgDmZo2lu+tRwRNYTfNHP6amuPg3xLjUFIyvxn+ZPIwLwMU5UEkLyKMNznYK+pgIcSTWCBabZKoohJYh5iw3hySOZ4olgUJYrKqbSmaQUiyUoEYl5T6szETqX9YBLJu3xBNoweytDDJ3smyEXuL1blmal1orFchJBEP7Bqn4hwf5Gcr8qS+aQvgrzCOv46RSnNxj3S+wfxUzPYFxFAnQAAAABJRU5ErkJggg==);
        }

        .yellow-warn {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIqSURBVHgB7ZhPbtNAFId/MzauIlVhUCPW5gbpCdqcoKlSkNglJ6CcAHECxAlCV0hAKZygvgG5Ad7TSKMKEerWGd6kDepfZ8Yep13Mt4ij5M3oy+TNe2MDHo/H47lPGByghkKcNHmfgW8wsDZ9El98I8HYCErt5VmeNF7KFBWpJKxFz0T4Sk3VLk0lDEZ8IPG3VcRLC08+ijiIgkOaIoYVKiXpTlnpUsLlZf8js5x1Vl/8GsESa2GdBqfN4EcF2flM6aPjfJ0NpLQZxWEJ5eyb6rIaFmfNcAhLrFb4PBXCn3BIPkWn8fwoMY23WmEehn1YQWWNKoP++++KCBjrwsbBJpgxbJhHUzXg+XrUGw/yKRvcPSm2YIFdDjNsmgXOZDuN7YvSxeeN5NbYWG9kGGIsPDkQsVnkVdnfn0Q7AN4VDnmKxzDEukoUc1M2Cmb1unAF//4Jn8EQh8LX0oCIeNBXQHK++dwQwhF6YzV6V9tttDPe1dfJ59ZmwHEIBzhOidtxtiou56IVPMi+tkZKQUbHZ4N5y9XNRnE1dHSSdfrjBZ1726eUxyuXZCsekm7gctPJjGRXt+XsBFaHrMZlDr+uW1bjTJiqRKqvdcpqHG46Ncz21xJ62zW7XSqHw03HYnrpo2aWUodd4oXrZmH7Ofmy1qWD+xYdYgQ9JLG6O7CwSKjppNQlv6/sjL8Vhy4g228pLA8Z9Y6eFAUsTgml3mNZKLYHj8fj8Txo/gGi3M3lJP1ctgAAAABJRU5ErkJggg==);
        }


        /* 
---------------------------------------

*/
        .top_content {
            margin: 0;
            padding: 32px 47px;
        }

        .rowC {
            display: flex;
            align-items: center;
        }

        .top .header {
            padding: 0 26px 0 23px;
            margin-bottom: 0;
        }

        .top_title h2 {
            margin: 0;
            font-family: PingFangSC-Semibold;
            font-size: 38px;
            color: #000000;
            line-height: 46px;
            height: 46px;
            font-weight: 600;
        }

        .top_title .scence {
            margin-top: 8px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #1f1f1f;
            letter-spacing: 0;
            line-height: 22px;
            height: 22px;
            font-weight: 400;
        }

        .top_title .scence div:nth-child(2) {
            margin-right: 31px;
        }

        .top_title .scence div:nth-child(2) .icon {
            margin-right: 8px;
        }

        .top_title .scence-icon {
            height: 22px;
            padding: 1px 7px;
            margin-right: 8px;
        }

        .top_title .oracle_icon {
            height: 18px;
        }

        .statistics .mlr10 div {
            height: 22px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #1f1f1f;
            letter-spacing: 0;
            line-height: 22px;
            font-weight: 400;
        }

        .statistics .mlr10 h3 {
            margin: 0;
            height: 32px;
            font-family: PingFangSC-Semibold;
            font-size: 24px;
            color: #27272a;
            letter-spacing: 0;
            line-height: 32px;
            font-weight: 600;
        }

        .top {
            margin: 16px 20px;
        }

        .top .top_table {
            display: flex;
            justify-content: space-between;
            margin: 0;
            padding: 24px;
        }

        .top .top_table .risk_table {
            width: 38.5%;
            margin-left: 16px;
        }

        .top .top_table .ai-result {
            width: 61%;
        }

        .ai-result-hd {
            height: 48px;
        }

        .ai-result-hd .title {
            margin: 0 7px 0 0;
            font-family: PingFangSC-Semibold;
            font-size: 20px;
            color: #000000;
            line-height: 28px;
            font-weight: 600;
        }

        .ai-result-hd .no-notice {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #f5222d;
            line-height: 22px;
            height: 22px;
            font-weight: 400;
        }

        .purple-color {
            color: #b37feb;
        }

        /* -------------- */
        .ellipsis {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            -moz-box-orient: vertical;
            word-break: break-all;
        }

        .width-100 {
            width: 100px;
        }
        
        .item-detail>div {
            display: block;
            margin-top: 10px ;
        }

        .item-detail h6 {
            margin: 0 0 5px 0;
            height: 18px;
            border: 1px solid rgba(217, 217, 217, 1);
            text-align: center;
            border-radius: 4px;
            font-size: 12px;
            background-color: #fafafa;
            width: 60px;
            font-weight: 400;
        }
        .item-detail pre{
            margin: 0;
        }
        pre code{
            white-space: pre-wrap;
            line-height: 24px;
            text-align: center;
        }
        .width-350{
            width: 350px;
        }
    </style>

</head>

<body>
    <div>
        <div class="top">
            <div class="header rowBC ">
                <div class="h_left">项目代码扫描报告(增量) | 李勇 于2024-04-29 17:20 发起审核,审核耗时0.67分钟</div>
                <div class="h_right">
                    <span class="sql-review-text">SQL Review</span>
                    <span class="date-time">2024-5-14 15:56</span>
                </div>
            </div>
            <div class="rowBC top_content">
                <div class="top_title">
                    <h2>消金:ph_sp_progress</h2>
                    <div class="rowC scence">
                        <div class="low_risk scence-icon">生产</div>

                        <div class="ver-center ">
                            <span class="icon oracle_icon margin-right-10"></span>
                            <span>Customer(127.0.0.1)</span>
                        </div>
                        <div class="ver-center ">
                            <span class="icon DB_icon margin-right-10"></span>
                            <span>DB_NAME1</span>
                        </div>
                    </div>
                </div>
                <div class="statistics rowBC">
                    <span class="border_left"></span>
                    <div class="mlr10">
                        <div>SQL总量</div>
                        <h3>687</h3>
                    </div>
                    <span class="border_left"></span>
                    <div class="mlr10">
                        <div>AI通过率</div>
                        <h3>95%</h3>
                    </div>
                    <span class="border_left"></span>
                    <div class="nopass mlr10">
                        <div>评审结果</div>
                        <h3 class="nopass_color">未通过</h3>
                    </div>
                </div>
            </div>
            <div class="border_bottom"></div>
            <div class="top_table  ">
                <div class="ai-result">
                    <div class="ai-result-hd rowC">
                        <h3 class="title">AI审核结果</h3>
                        <span class="no-notice">未通过</span>
                    </div>
                    <table width="100%">
                        <tr class="table_th no_border">
                            <th>风险等级</th>
                            <th>
                                <div class="ver-center"><span class="border-line"></span><span>SQL数量 </span></div>
                            </th>
                            <th>
                                <div class="ver-center"><span class="border-line"></span><span>百分比 </span></div>
                            </th>
                        </tr>
                        <tr>
                            <td>
                                <div class="high_risk">高风险</div>
                            </td>
                            <td>35</td>
                            <td>3%</td>
                        </tr>
                        <tr>
                            <td>
                                <div class="low_risk">低风险</div>
                            </td>
                            <td>35</td>
                            <td>3%</td>
                        </tr>
                        <tr>
                            <td>
                                <div class="no_risk">无风险</div>
                            </td>
                            <td>35</td>
                            <td>3%</td>
                        </tr>
                        <tr>
                            <td>
                                <div class="abnormal">异常</div>
                            </td>
                            <td>35</td>
                            <td>3%</td>
                        </tr>

                    </table>
                </div>
                <!-- <span class="div_border_left"></span>
                <div>
                    <h3>人工审核结果</h3>
                    <table width="100%" cellspacing="0">
                        <tr class="no_border">
                            <th>审核状态</th>
                            <th>
                                <div class="ver-center"><span class="border-line"></span><span>SQL数量 </span></div>
                            </th>
                        </tr>
                        <tr>
                            <td class="rowSC"><span class="right_icon icon margin-right-5"></span>白名单</td>
                            <td>35</td>
                        </tr>
                        <tr>
                            <td class="rowSC"> <span class="right_icon icon margin-right-5"></span>整改中</td>
                            <td>35</td>
                        </tr>
                        <tr>
                            <td class="rowSC"> <span class="right_icon icon margin-right-5"></span>豁免</td>
                            <td>35</td>
                        </tr>
                        <tr>
                            <td class="rowSC"><span class="err_icon icon margin-right-5"></span>驳回</td>
                            <td>35</td>
                        </tr>
                        <tr>
                            <td class="rowSC"><span class="err_icon icon margin-right-5"></span>待评审</td>
                            <td>35</td>
                        </tr>
                    </table>
                </div> -->
                <div class="risk_table">
                    <div width="100%">
                        <div class="rowAC th risk">
                            <div>风险提示</div>
                            <div>触发条数</div>
                        </div>
                        <div class="risk_table_body">
                            <div class="rowAC risk">
                                <div><span class="height-warn"></span>白名单白名单白名单白名单白名单白名单白名单</div>
                                <div><span>35</span></div>
                            </div>
                            <div class="rowAC risk">
                                <div><span class="height-warn"></span>整改中白名单白名单白名单白名单</div>
                                <div><span>35</span></div>
                            </div>
                            <div class="rowAC risk">
                                <div><span class="height-warn"></span>豁免白名单白名单白名单</div>
                                <div><span>35</span></div>
                            </div>
                            <div class="rowAC risk">
                                <div><span class="height-warn"></span>驳回白名单</div>
                                <div><span>35</span></div>
                            </div>
                            <div class="rowAC risk">
                                <div><span class="height-warn"></span>待评审白名单</div>
                                <div><span>35</span></div>
                            </div>
                            <div class="rowAC risk">
                                <div><span class="height-warn"></span>待评审白名单</div>
                                <div><span>35</span></div>
                            </div>
                            <div class="rowAC risk">
                                <div><span class="height-warn"></span>待评审白名单</div>
                                <div><span>35</span></div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="top_table padding0 rowCS">
            <table class="sql-list">
                <thead>
                    <tr>
                        <th colspan="2">SQL代码<span class="title-desc">( 共160条 )</span></th>
                        <th>
                            <div class="ver-center"><span class="border-line"></span><span>AI风险等级 </span></div>
                        </th>
                        <th>
                            <div class="ver-center"><span class="border-line"></span><span>审核结果 </span></div>
                        </th>
                        <th>
                            <div class="ver-center"><span class="border-line"></span><span>SQL来源 </span></div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="tr-sql-row">
                        <td><span class="right1_icon"></span></td>
                        <td  title="select t1.CREDIT_APPLY_ID, t1.TENANT_ID, t1.APPLY_AMOUNT,
                        t1.PRE_CREDIT_AMOUNT,
                        t1.CREDIT_TYPE,
                        t1.APPLY_STATUS, t1.CREDIT_APPLY_ID, t1.TENANT_ID, t1.APPLY_AMOUNT,
                        t1.PRE_CREDIT_AMOUNT,
                        t1.CREDIT_TYPE,
                        t1.APPLY_STATUS, t1">
                            <div class="ellipsis">select t1.CREDIT_APPLY_ID, t1.TENANT_ID, t1.APPLY_AMOUNT,
                                t1.PRE_CREDIT_AMOUNT,
                                t1.CREDIT_TYPE,
                                t1.APPLY_STATUS, t1.CREDIT_APPLY_ID, t1.TENANT_ID, t1.APPLY_AMOUNT,
                                t1.PRE_CREDIT_AMOUNT,
                                t1.CREDIT_TYPE,
                                t1.APPLY_STATUS, t1 </div>
                        </td>
                        <td  class="width-100">
                            <div class="high_risk">高风险</div>
                        </td>
                        <td class="rowC width-100"><span class="err_icon icon margin-right-5 "></span><span>人工驳回</span></td>
                           <td class="width-350 "><div class="ellipsis">/work-server/src/main/resources/mapping/cases/CaseDetaiMapper.xml</div> </td>
                    </tr>
                    <tr class="tr-detail-wrap hidden">
                        <td></td>
                        <td colspan="4">
                            <div class="detail-wrap">
                                <div class="detail-left">
                                    <div>
                                        <div class="detail-item-title">
                                            风险提示
                                        </div>
                                        <ul class="check-detail-list">
                                            <li class="open-li">
                                                <div><span class="height-warn"></span><span
                                                        class="list-item-title">SELECT
                                                        执行计划COST阔值超过20000;</span><span class="right1_icon"></span></div>

                                            </li>
                                            <li class="item-detail hidden">
                                                <div>
                                                    <ul>
                                                        <li>
                                                            <button class="item-title-button">优化建议</button>
                                                        </li>
                                                        <li>
                                                            这是一段描述文字
                                                        </li>
                                                        <li>
                                                            样例：select xxx,xxx, rome xxx xxx xx=1;
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                            <li class="open-li">
                                                <div><span class="height-warn"></span><span
                                                        class="list-item-title">SELECT
                                                        扫描行数超过1000;</span><span class="right1_icon"></span></div>
                                            </li>
                                            <li class="item-detail hidden">
                                                <div>
                                                    <ul>
                                                        <li>
                                                            <button class="item-title-button">优化建议</button>
                                                        </li>
                                                        <li>
                                                            这是一段描述文字
                                                        </li>
                                                        <li>
                                                            样例：select xxx,xxx, rome xxx xxx xx=1;
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                            <li class="open-li">
                                                <div><span class="height-warn"></span><span
                                                        class="list-item-title">SELECT
                                                        语句表关联次数超过4次:</span><span class="right1_icon"></span></div>
                                            </li>
                                            <li class="item-detail hidden">
                                                <div>
                                                    <ul>
                                                        <li>
                                                            <button class="item-title-button">优化建议</button>
                                                        </li>
                                                        <li>
                                                            这是一段描述文字
                                                        </li>
                                                        <li>
                                                            样例：select xxx,xxx, rome xxx xxx xx=1;
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                        </ul>

                                    </div>
                                    <div>
                                        <div class="detail-item-title">
                                            DBA建议
                                        </div>
                                        <ul class="check-detail-list">
                                            <li>
                                                <div><span
                                                        class="list-item-title padding-left-20">DBA-A:未通过。(SQL高频调用，需要整改)</span>
                                                </div>
                                            </li>
                                        </ul>

                                    </div>
                                    <div>
                                        <div class="detail-item-title">
                                            数据库信息
                                        </div>
                                        <ul class="check-detail-list">
                                            <li>
                                                <div>
                                                    <table class="DB_list_info">
                                                        <tbody>
                                                            <tr>
                                                                <td>
                                                                    <div class="low_risk">生产</div>
                                                                </td>
                                                                <td>
                                                                    <div class="ver-center ">
                                                                        <span
                                                                            class="icon oracle_icon margin-right-10"></span>
                                                                        <span>Customer(127.0.0.1)</span>
                                                                    </div>

                                                                </td>
                                                                <td>
                                                                    <div class="ver-center ">
                                                                        <span
                                                                            class="icon DB_icon margin-right-10"></span>
                                                                        <span>DB_NAME1</span>
                                                                    </div>
                                                                </td>
                                                            </tr>

                                                        </tbody>
                                                    </table>
                                                </div>
                                            </li>
                                            <li>
                                                <div>
                                                    <table class="DB_list_info">
                                                        <tbody>
                                                            <tr>
                                                                <td>
                                                                    <div class="no_risk">测试</div>
                                                                </td>
                                                                <td>
                                                                    <div class="ver-center ">
                                                                        <span
                                                                            class="icon oracle_icon margin-right-10"></span>
                                                                        <span>Customer(127.0.0.1)</span>
                                                                    </div>

                                                                </td>
                                                                <td>
                                                                    <div class="ver-center ">
                                                                        <span
                                                                            class="icon DB_icon margin-right-10"></span>
                                                                        <span>DB_NAME1</span>
                                                                    </div>
                                                                </td>
                                                            </tr>

                                                        </tbody>
                                                    </table>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="detail-right flex1">
                                    <ul class="tabs">
                                        <li>
                                            <input id="tab1" type="radio" name="tab1" checked>
                                            <label class="tab1" for="tab1"><span>SQL文本</span></label>
                                            <div class="content">
                                                <div class="content-code">
                                                    SELECT<br>
                                                     a.LUFLEX ACT_ID luflexActId,<br>
                                                    a.CREATED _BY createdBy,<br>
                                                    a.ACTION NAME actionName,<br>
                                                    a.ACT_TYPE actType,<br>
                                                    t.TOTAL total,<br>
                                                    y.USER_COUNT userCount,<br>
                                                    1.EXECUTE TIME executeTime,<br>
                                                    1.LAST PK ID lastPkId,<br>
                                                    r.STATUS AS status,<br>
                                                    r.REQUEST DATA_TYPE requestDataType,<br>
                                                    a.FIRST SUCCESS TIME firstSuccessTime,
                                                    a.START TIME startTime,
                                                    a.END TIME AS endTime
                                                    FROM<br>
                                                </div>
                                                <p>执行计划</p>
                                                <div class="content-plan">111</div>
                                            </div>
                                        </li>
                                        <li>
                                            <input id="tab2" type="radio" name="tab1">
                                            <label for="tab2" class="tab2"><span>SQL代码</span></label>
                                            <div class="content">
                                                <div class="content-code height-550">
                                                    SELECT<br>
                                                    a.LUFLEX ACT_ID luflexActId,<br>
                                                   a.CREATED _BY createdBy,<br>
                                                   a.ACTION NAME actionName,<br>
                                                   a.ACT_TYPE actType,<br>
                                                   t.TOTAL total,<br>
                                                   y.USER_COUNT userCount,<br>
                                                   1.EXECUTE TIME executeTime,<br>
                                                   1.LAST PK ID lastPkId,<br>
                                                   r.STATUS AS status,<br>
                                                   r.REQUEST DATA_TYPE requestDataType,<br>
                                                   a.FIRST SUCCESS TIME firstSuccessTime,
                                                   a.START TIME startTime,
                                                   a.END TIME AS endTime
                                                   FROM<br>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                        </td>
                    </tr>
                    <tr class="tr-sql-row">
                        <td><span class="right1_icon"></span></td>
                        <td>
                            <div class="ellipsis">select t1.CREDIT_APPLY_ID, t1.TENANT_ID, t1.APPLY_AMOUNT,
                                t1.PRE_CREDIT_AMOUNT,
                                t1.CREDIT_TYPE,
                                t1.APPLY_STATUS, t1.CREDIT_APPLY_ID, t1.TENANT_ID, t1.APPLY_AMOUNT,
                                t1.PRE_CREDIT_AMOUNT,
                                t1.CREDIT_TYPE,
                                t1.APPLY_STATUS, t1 </div>
                        </td>
                        <td  class="width-100">
                            <div class="low_risk">低风险</div>
                        </td>
                        <td class="rowC width-100"><span class="err_icon icon margin-right-5 "></span><span>人工驳回</span></td>
                           <td class="width-350 "><div class="ellipsis">/work-server/src/main/resources/mapping/cases/CaseDetaiMapper.xml</div> </td>
                    </tr>
                    <tr class="tr-detail-wrap hidden">
                        <td></td>
                        <td colspan="4">
                            <div class="detail-wrap">
                                <div class="detail-left">
                                    <div>
                                        <div class="detail-item-title">
                                            风险提示
                                        </div>
                                        <ul class="check-detail-list">
                                            <li class="open-li">
                                                <div><span class="height-warn"></span><span
                                                        class="list-item-title">SELECT
                                                        执行计划COST阔值超过20000;</span><span class="right1_icon"></span></div>

                                            </li>
                                            <li class="item-detail hidden">
                                                <div>
                                                    <ul>
                                                        <li>
                                                            <button class="item-title-button">优化建议</button>
                                                        </li>
                                                        <li>
                                                            这是一段描述文字
                                                        </li>
                                                        <li>
                                                            样例：select xxx,xxx, rome xxx xxx xx=1;
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                            <li class="open-li">
                                                <div><span class="height-warn"></span><span
                                                        class="list-item-title">SELECT
                                                        扫描行数超过1000;</span><span class="right1_icon"></span></div>
                                            </li>
                                            <li class="item-detail hidden">
                                                <div>
                                                    <ul>
                                                        <li>
                                                            <button class="item-title-button">优化建议</button>
                                                        </li>
                                                        <li>
                                                            这是一段描述文字
                                                        </li>
                                                        <li>
                                                            样例：select xxx,xxx, rome xxx xxx xx=1;
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                            <li class="open-li">
                                                <div><span class="height-warn"></span><span
                                                        class="list-item-title">SELECT
                                                        语句表关联次数超过4次:</span><span class="right1_icon"></span></div>
                                            </li>
                                            <li class="item-detail hidden">
                                                <div>
                                                    <ul>
                                                        <li>
                                                            <button class="item-title-button">优化建议</button>
                                                        </li>
                                                        <li>
                                                            这是一段描述文字
                                                        </li>
                                                        <li>
                                                            样例：select xxx,xxx, rome xxx xxx xx=1;
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                        </ul>

                                    </div>
                                    <div>
                                        <div class="detail-item-title">
                                            DBA建议
                                        </div>
                                        <ul class="check-detail-list">
                                            <li>
                                                <div><span
                                                        class="list-item-title padding-left-20">DBA-A:未通过。(SQL高频调用，需要整改)</span>
                                                </div>
                                            </li>
                                        </ul>

                                    </div>
                                    <div>
                                        <div class="detail-item-title">
                                            数据库信息
                                        </div>
                                        <ul class="check-detail-list">
                                            <li>
                                                <div>
                                                    <table class="DB_list_info">
                                                        <tbody>
                                                            <tr>
                                                                <td>
                                                                    <div class="low_risk">生产</div>
                                                                </td>
                                                                <td>
                                                                    <div class="ver-center ">
                                                                        <span
                                                                            class="icon oracle_icon margin-right-10"></span>
                                                                        <span>Customer(127.0.0.1)</span>
                                                                    </div>

                                                                </td>
                                                                <td>
                                                                    <div class="ver-center ">
                                                                        <span
                                                                            class="icon DB_icon margin-right-10"></span>
                                                                        <span>DB_NAME1</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>

                                                </div>

                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="detail-right flex1">
                                    <ul class="tabs">
                                        <li>
                                            <input id="tab3" type="radio" name="tab3" checked>
                                            <label class="tab1" for="tab3"><span>SQL文本</span></label>
                                            <div class="content">
                                                <div class="content-code">
                                                    SELECT<br>
                                                     a.LUFLEX ACT_ID luflexActId,<br>
                                                    a.CREATED _BY createdBy,<br>
                                                    a.ACTION NAME actionName,<br>
                                                    a.ACT_TYPE actType,<br>
                                                    t.TOTAL total,<br>
                                                    y.USER_COUNT userCount,<br>
                                                    1.EXECUTE TIME executeTime,<br>
                                                    1.LAST PK ID lastPkId,<br>
                                                    r.STATUS AS status,<br>
                                                    r.REQUEST DATA_TYPE requestDataType,<br>
                                                    a.FIRST SUCCESS TIME firstSuccessTime,
                                                    a.START TIME startTime,
                                                    a.END TIME AS endTime
                                                    FROM<br>
                                                </div>
                                                <p>执行计划</p>
                                                <div class="content-plan">111</div>
                                            </div>
                                        </li>
                                        <li>
                                            <input id="tab4" type="radio" name="tab3">
                                            <label for="tab4" class="tab2"><span>SQL代码</span></label>
                                            <div class="content">选项二内容</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                        </td>
                    </tr>
                    <tr class="tr-sql-row">
                        <td><span class="right1_icon"></span></td>
                        <td>
                            <div class="ellipsis">select t1.CREDIT_APPLY_ID, t1.TENANT_ID, t1.APPLY_AMOUNT,
                                t1.PRE_CREDIT_AMOUNT,
                                t1.CREDIT_TYPE,
                                t1.APPLY_STATUS, t1.CREDIT_APPLY_ID, t1.TENANT_ID, t1.APPLY_AMOUNT,
                                t1.PRE_CREDIT_AMOUNT,
                                t1.CREDIT_TYPE,
                                t1.APPLY_STATUS, t1 </div>
                        </td>
                        <td  class="width-100">
                            <div class="no_risk">无风险</div>
                        </td>
                        <td class="rowC width-100"><span class="err_icon icon margin-right-5 "></span><span>人工驳回</span></td>
                           <td class="width-350 "><div class="ellipsis">/work-server/src/main/resources/mapping/cases/CaseDetaiMapper.xml</div> </td>
                    </tr>
                    <tr class="tr-detail-wrap hidden">
                        <td></td>
                        <td colspan="4">
                            <div class="detail-wrap">
                                <div class="detail-left">
                                    <div>
                                        <div class="detail-item-title">
                                            风险提示
                                        </div>
                                        <ul class="check-detail-list">
                                            <li class="open-li">
                                                <div><span class="height-warn"></span><span
                                                        class="list-item-title">SELECT
                                                        执行计划COST阔值超过20000;</span><span class="right1_icon"></span></div>

                                            </li>
                                            <li class="item-detail hidden">
                                                <div>
                                                    <ul>
                                                        <li>
                                                            <button class="item-title-button">优化建议</button>
                                                        </li>
                                                        <li>
                                                            这是一段描述文字
                                                        </li>
                                                        <li>
                                                            样例：select xxx,xxx, rome xxx xxx xx=1;
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                            <li class="open-li">
                                                <div><span class="height-warn"></span><span
                                                        class="list-item-title">SELECT
                                                        扫描行数超过1000;</span><span class="right1_icon"></span></div>
                                            </li>
                                            <li class="item-detail hidden hidden hidden">
                                                <div>
                                                    <ul>
                                                        <li>
                                                            <button class="item-title-button">优化建议</button>
                                                        </li>
                                                        <li>
                                                            这是一段描述文字
                                                        </li>
                                                        <li>
                                                            样例：select xxx,xxx, rome xxx xxx xx=1;
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                            <li class="open-li">
                                                <div><span class="height-warn"></span><span
                                                        class="list-item-title">SELECT
                                                        语句表关联次数超过4次:</span><span class="right1_icon"></span></div>
                                            </li>
                                            <li class="item-detail hidden hidden">
                                                <div>
                                                    <ul>
                                                        <li>
                                                            <button class="item-title-button">优化建议</button>
                                                        </li>
                                                        <li>
                                                            这是一段描述文字
                                                        </li>
                                                        <li>
                                                            样例：select xxx,xxx, rome xxx xxx xx=1;
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                        </ul>

                                    </div>
                                    <div>
                                        <div class="detail-item-title">
                                            DBA建议
                                        </div>
                                        <ul class="check-detail-list">
                                            <li>
                                                <div><span
                                                        class="list-item-title padding-left-20">DBA-A:未通过。(SQL高频调用，需要整改)</span>
                                                </div>
                                            </li>
                                        </ul>

                                    </div>
                                    <div>
                                        <div class="detail-item-title">
                                            数据库信息
                                        </div>
                                        <ul class="check-detail-list">
                                            <li>
                                                <div>
                                                    <table class="DB_list_info">
                                                        <tbody>
                                                            <tr>
                                                                <td>
                                                                    <div class="low_risk">生产</div>
                                                                </td>
                                                                <td>
                                                                    <div class="ver-center ">
                                                                        <span
                                                                            class="icon oracle_icon margin-right-10"></span>
                                                                        <span>Customer(127.0.0.1)</span>
                                                                    </div>

                                                                </td>
                                                                <td>
                                                                    <div class="ver-center ">
                                                                        <span
                                                                            class="icon DB_icon margin-right-10"></span>
                                                                        <span>DB_NAME1</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>

                                                </div>

                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="detail-right flex1">
                                    <ul class="tabs">
                                        <li>
                                            <input id="tab5" type="radio" name="tab5" checked>
                                            <label class="tab1" for="tab5"><span>SQL文本</span></label>
                                            <div class="content">
                                                <div class="content-code">
                                                    SELECT<br>
                                                     a.LUFLEX ACT_ID luflexActId,<br>
                                                    a.CREATED _BY createdBy,<br>
                                                    a.ACTION NAME actionName,<br>
                                                    a.ACT_TYPE actType,<br>
                                                    t.TOTAL total,<br>
                                                    y.USER_COUNT userCount,<br>
                                                    1.EXECUTE TIME executeTime,<br>
                                                    1.LAST PK ID lastPkId,<br>
                                                    r.STATUS AS status,<br>
                                                    r.REQUEST DATA_TYPE requestDataType,<br>
                                                    a.FIRST SUCCESS TIME firstSuccessTime,
                                                    a.START TIME startTime,
                                                    a.END TIME AS endTime
                                                    FROM<br>
                                                </div>
                                                <p>执行计划</p>
                                                <div class="content-plan">111</div>
                                            </div>
                                        </li>
                                        <li>
                                            <input id="tab6" type="radio" name="tab5">
                                            <label for="tab6" class="tab2"><span>SQL代码</span></label>
                                            <div class="content">选项二内容</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                        </td>
                    </tr>
                </tbody>


            </table>


        </div>
    </div>
    <script>
        var trSqlDomArr = document.getElementsByClassName("tr-sql-row");
        var liDomArr = document.getElementsByClassName("open-li");
        for (var i = 0; i < trSqlDomArr.length; i++) {
            trSqlDomArr[i].onclick = trSqlClick
        }
        for (var i = 0; i < liDomArr.length; i++) {
            liDomArr[i].onclick = liOpenClick
        }

        //表格详情展示
        function trSqlClick(e) {
            var nextBroClassName = e.currentTarget.nextElementSibling.className
            var nextBro = e.currentTarget.nextElementSibling

            console.log(nextBroClassName)
            if (nextBroClassName == "tr-detail-wrap hidden") {
                e.currentTarget.firstElementChild.firstChild.className = "down_icon"
                e.currentTarget.nextElementSibling.className = "tr-detail-wrap"
                setTimeout(function () {
                    if (nextBro) {
                        nextBro.querySelector("label").click()
                    }
                }, 200)
            } else {
                e.currentTarget.firstElementChild.firstChild.className = "right1_icon"
                e.currentTarget.nextElementSibling.className = "tr-detail-wrap hidden"
            }
            console.log(e.currentTarget.nextElementSibling.className)
        }
        function liOpenClick(e) {
            var nextBroClassName = e.currentTarget.nextElementSibling.className

            console.log(nextBroClassName)
            if (nextBroClassName == "item-detail hidden") {
                e.currentTarget.firstElementChild.lastChild.className = "down_icon"
                e.currentTarget.nextElementSibling.className = "item-detail"
            } else {
                e.currentTarget.firstElementChild.lastChild.className = "right1_icon"
                e.currentTarget.nextElementSibling.className = "item-detail hidden"
            }
            console.log(e.currentTarget.nextElementSibling.className)
        }
    </script>
</body>

</html>