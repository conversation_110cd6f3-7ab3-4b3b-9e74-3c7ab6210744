<template>
  <div class="custom-shower" :style="shower.style || {}">
    <div class="shower-title" v-if="shower.title">{{shower.title}}</div>
    <div class="shower-content">
      <slot v-if="shower.useSlot" :name="shower.useSlot"></slot>
      <component v-bind="shower.props" :is="shower.realType" v-else-if="shower.type"></component>
      <span v-else>{{showerValue || '--'}}</span>
    </div>
  </div>
</template>

<script>
import CustomShowerComponents from './register';
import Label from '@/components/Label';
import LimitLabel from '@/components/LimitLabel';
import LimitTags from '@/components/LimitTags';
import DateFormat from '@/components/DateFormat';
import DbImg from '@/components/CustomImg/DbImg';

const compMap = {
  Label: {
    compName: 'Label'
  },
  LimitLabel: {
    compName: 'LimitLabel'
  },
  LimitTags: {
    compName: 'LimitTags'
  },
  DateFormat: {
    compName: 'DateFormat'
  },
  DbImg: {
    compName: 'DbImg'
  },
  Status: {
    compName: 'Status'
  }
};

export default {
  components: {
    Label,
    LimitLabel,
    LimitTags,
    DateFormat,
    DbImg
  },
  props: {
    config: Object | Function,
    showerValue: String | Number,
    showerData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data() {
    return {
      shower: {}
    };
  },
  computed: {},
  created() {
    this.initShower();
  },
  mounted() {},
  methods: {
    initShower() {
      const { config } = this;
      let item = _.isFunction(config) ? config(this.showerData) : config || {};
      const matchItem = compMap[item.type] || CustomShowerComponents.comp[item.type] || {}
      item.realType = matchItem.compName || '';
      item.props = { ...(matchItem.props || {}), ...(item.showerProps || {}) };
      this.shower = item;
    }
  },
  watch: {
    config: {
      handler(newVal = {}, oldVal) {
        this.initShower();
      }
    },
    showerData: {
      handler(newVal = {}, oldVal) {
        this.initShower();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.custom-shower {
  display: flex;
  flex-direction: column;
  .shower-title {
    font-size: 12px;
    margin-bottom: 4px;
    color: rgba(0, 0, 0, 0.45);
    text-align: left;
    // line-height: 20px;
    // white-space: nowrap;
  }
  .shower-content {
    // float: left;
    // font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    text-align: left;
    // line-height: 22px;
  }
}
</style>
