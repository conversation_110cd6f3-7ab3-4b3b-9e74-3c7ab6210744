export default function (ctx) {
  const option = (params = {}) => {
    const {
      markLine = {},
      markArea = {},
      commonViewOption = [],
      commonViewTitle = ''
    } = params;
    const unit = ' ms';
    return () => ({
      grid: {
        top: '5%',
        right: '7%',
        left: '7%',
        bottom: '7%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: function (params) {
          // 设置浮窗信息
          return (
            params[0].name +
            `<br>${commonViewTitle}: ` +
            params[0].data[1] +
            unit
          );
        }
      },
      xAxis: {
        type: 'category',
        // boundaryGap: false,
        triggerEvent: true, // 点击事件
        axisLabel: {
          color: '#1890FF',
          padding: [0, 8, 0, 0],
          overflow: 'break'
        }
      },
      yAxis: {
        type: 'value',
        boundaryGap: [0, '20%'],
        axisLabel: {
          formatter: '{value} ms'
        }
      },
      // dataZoom: [
      //   {
      //     show: true,
      //     type: 'slider',
      //     start: 0,
      //     end: 10
      //   }
      // ],
      series: [
        {
          type: 'line',
          // smooth: 0.6,
          // symbol: 'none',
          lineStyle: {
            color: '#5470C6',
            width: 2,
            type: 'solid'
          },
          markArea: markArea,
          markLine: markLine,
          data: commonViewOption
        }
      ]
    });
  };
  return {
    option
  };
}
