import Http from '@/utils/request';
// 获取表头信息
export function getTableHeaderInfo(params) {
  return Http({
    url: `/sqlreview/after_audit/get_header_columns`,
    method: 'get',
    params: params
  });
}

// 表头设置保存
export function saveTableHeaderInfo(params) {
  return Http({
    url: `/sqlreview/after_audit/add_header_columns`,
    method: 'post',
    data: params
  });
}

// topsql 导出功能
export function download(params) {
  return Http({
    url: `/sqlreview/after_audit/export_top_sql`,
    method: 'post',
    data: params,
    responseType: 'blob'
  });
}

// ai 审核
export function afterAudit(params) {
  return Http({
    url: `/sqlreview/after_audit/examine_sql`,
    method: 'post',
    data: params
  });
}

// 执行计划
export function afterExplain(params) {
  return Http({
    url: `/sqlreview/after_audit/after_explain`,
    method: 'post',
    data: params
  });
}

// plan_hash_value 执行计划
export function planHashValueExplain(params) {
  return Http({
    url: `/sqlreview/after_audit/get_explain_by_plan_hash_value`,
    method: 'post',
    data: params
  });
}

// topsql-查询当前数据源sql采集配置
export function getCollectConfig(params) {
  return Http({
    url: `/sqlreview/after_audit/get_collect_config`,
    method: 'get',
    params
  });
}

// topsql-采集信息配置保存
export function saveCollectConfig(params) {
  return Http({
    url: `/sqlreview/after_audit/save_collect_config`,
    method: 'post',
    data: params
  });
}

// 创建sql采集配置信息（sql采集开关是关闭时使用）
export function addCollectConfig(params) {
  return Http({
    url: `/sqlreview/after_audit/add_collect_config`,
    method: 'post',
    data: params
  });
}

// 立即采集 oracle
export function oracleImmediateCollection(params) {
  return Http({
    url: `/sqlreview/after_audit/oracle_immediate_collection`,
    method: 'post',
    data: params
  });
}

// 图表模式的接口
// 获取top 5 数据
export function getTopFiveChart(params) {
  return Http({
    url: `/sqlreview/after_audit/get_top_chart`,
    method: 'get',
    params: params
  });
}
// 全指标图表
export function getAllTargetChart(params) {
  return Http({
    url: `/sqlreview/after_audit/get_all_target_chart`,
    method: 'get',
    params: params
  });
}
// 耗时报表
export function getExeTimeChart(params) {
  return Http({
    url: `/sqlreview/after_audit/get_exe_time_chart`,
    method: 'get',
    params: params
  });
}

// ob 采集配置检测接口
export function testObCollectConfig(params) {
  return Http({
    url: `/sqlreview/after_audit/test_collect_config`,
    method: 'get',
    params: params
  });
}

// gaussdb 执行计划
export function getGaussdbPlan(params) {
  return Http({
    url: `/sqlreview/after_audit/get_gaussdb_plan`,
    method: 'get',
    params: params
  });
}

// 校验接口
export function checkPermission(params) {
  return Http({
    url: `/sqlreview/after_audit/check_permission`,
    method: 'get',
    params: params
  });
}

// 获取标签信息
export function getLabelCardData(params) {
  return Http({
    url: `/sqlreview/review/label_card/${params.id}/`,
    method: 'get'
    // params: params
  });
}

// 删除获取标签信息
export function delteteLabel(params) {
  return Http({
    url: `/sqlreview/review/label_card/${params.id}/`,
    method: 'delete',
    params: {}
  });
}

// 页面批量打标
export function saveLabel(params) {
  return Http({
    url: `/sqlreview/after_audit/save_top_sql_label`,
    method: 'post',
    data: params
  });
}

export default {};
