
export default function (ctx) {
  const fieldColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '字段类型',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: '空值',
      dataIndex: 'is_nullable',
      key: 'is_nullable',
      customRender: (text, record, index) => {
        return text == false ? '否' : '是'
      }
    },
    {
      title: '默认值',
      key: 'default',
      dataIndex: 'default'
    },
    {
      title: '注释',
      key: 'comment',
      dataIndex: 'comment'
    }
  ].map(item => {
    return {
      ...item,
      onFilter: (value, record) => CommonUtil.tableFilters.includes(value, record, item.key),
      scopedSlots: Object.assign(item.scopedSlots || {}, { filterDropdown: 'defaultFilterDropdown' })
    }
  });

  const indexColumns = [
    {
      title: '名称',
      dataIndex: 'index_name',
      key: 'index_name'
    },
    {
      title: '字段',
      dataIndex: 'index_columns',
      key: 'index_columns'
    },
    {
      title: '索引类型',
      dataIndex: 'index_type',
      key: 'index_type'
    }
    // {
    //   title: '注释',
    //   key: 'index_comment',
    //   dataIndex: 'index_comment'
    //   // scopedSlots: { customRender: 'trigger_body' }
    // }
  ].map(item => {
    return {
      ...item,
      onFilter: (value, record) => CommonUtil.tableFilters.includes(value, record, item.key),
      scopedSlots: Object.assign(item.scopedSlots || {}, { filterDropdown: 'defaultFilterDropdown' })
    }
  });

  const triggerColumns = [
    {
      title: '名称',
      dataIndex: 'trigger_name',
      key: 'trigger_name'
    },
    {
      title: '触发器类型',
      dataIndex: 'trigger_type',
      key: 'trigger_type'
    },
    {
      title: '触发器事件',
      dataIndex: 'trigger_event',
      key: 'trigger_event'
    },
    {
      title: '触发器内容',
      key: 'trigger_body',
      dataIndex: 'trigger_body',
      scopedSlots: { customRender: 'trigger_body' }
    }
  ].map(item => {
    return {
      ...item,
      onFilter: (value, record) => CommonUtil.tableFilters.includes(value, record, item.key),
      scopedSlots: Object.assign(item.scopedSlots || {}, { filterDropdown: 'defaultFilterDropdown' })
    }
  });
  return {
    fieldColumns,
    indexColumns,
    triggerColumns
  };
};
