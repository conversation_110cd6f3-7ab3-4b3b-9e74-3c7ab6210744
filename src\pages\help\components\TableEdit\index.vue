<template>
  <div>
    <a-alert message="适用范围" type="success" style="marginBottom: 8px">
      <ul slot="description">
        <li>多行编辑：适用于统一新增、编辑，最后提交后台</li>
        <li>单行编辑：适用于每行编辑、删除直接提交后台</li>
      </ul>
    </a-alert>
    <!-- <a-alert message="注意事项" type="warning" style="marginBottom: 8px">
      <ul slot="description">
        <li>全部编辑：新增一行，必须全部列可编辑或至少列有默认规则</li>
        <li>全部编辑：【取消编辑】和【新增】不共存，因为【取消编辑】会导致新增行空白情况</li>
        <li>每行编辑：不要使用行内新增</li>
      </ul>
    </a-alert> -->
    <a-collapse v-model="activeKey">
      <a-collapse-panel key="1" header="全部编辑（仅编辑）">
        <div style="marginBottom: 8px">
          <a-button class="btn-green" @click="editAll('tableEdit')">编辑</a-button>
          <a-button class="btn-yellow" @click="cancelAll('tableEdit')">取消编辑</a-button>
          <a-button class="btn-blue" @click="saveAll('tableEdit')">模拟保存</a-button>
        </div>
        <TableEdit ref="tableEdit" v-bind="params || {}">
          <!-- 显示插槽 -->
          <a slot="name" slot-scope="{ text }">{{ text }}</a>
          <span slot="customTitle">
            <a-icon type="smile-o" />Name
          </span>
          <span slot="tags" slot-scope="{ text }">
            <a-tag
              v-for="tag in text"
              :key="tag"
              :color="tag === 'loser' ? 'volcano' : tag.length > 5 ? 'geekblue' : 'green'"
            >{{ tag }}</a-tag>
          </span>
          <span slot="address" slot-scope="{ text, record }">{{record['addressName']}}</span>
        </TableEdit>
      </a-collapse-panel>
      <a-collapse-panel key="2" header="全部编辑（可新增）">
        <div style="marginBottom: 8px">
          <a-button class="btn-green" @click="addLine('tableEdit1')">新增</a-button>
          <a-button class="btn-yellow" @click="editAll('tableEdit1')">编辑</a-button>
          <a-button class="btn-blue" @click="saveAll('tableEdit1')">模拟保存</a-button>
        </div>
        <TableEdit ref="tableEdit1" v-bind="params1 || {}">
          <!-- 显示插槽 -->
          <a slot="name" slot-scope="{ text }">{{ text }}</a>
          <span slot="customTitle">
            <a-icon type="smile-o" />Name
          </span>
          <span slot="tags" slot-scope="{ text }">
            <a-tag
              v-for="tag in text"
              :key="tag"
              :color="tag === 'loser' ? 'volcano' : tag.length > 5 ? 'geekblue' : 'green'"
            >{{ tag }}</a-tag>
          </span>
          <span slot="address" slot-scope="{ text, record }">{{record['addressName']}}</span>
        </TableEdit>
      </a-collapse-panel>
      <a-collapse-panel key="3" header="每行编辑（支持行内和弹窗编辑）">
        <div style="marginBottom: 8px">
          <a-button class="btn-green" @click="addLine('tableEdit2')">新增</a-button>
        </div>
        <TableEdit ref="tableEdit2" v-bind="params2 || {}">
          <!-- 显示插槽 -->
          <a slot="name" slot-scope="{ text }">{{ text }}</a>
          <span slot="customTitle">
            <a-icon type="smile-o" />Name
          </span>
          <span slot="tags" slot-scope="{ text }">
            <a-tag
              v-for="tag in text"
              :key="tag"
              :color="tag === 'loser' ? 'volcano' : tag.length > 5 ? 'geekblue' : 'green'"
            >{{ tag }}</a-tag>
          </span>
          <span slot="address" slot-scope="{ text, record }">{{record['addressName']}}</span>
          <!-- 操作列按钮slot -->
          <a slot="test" slot-scope="{ record }" @click="actionTest(record)">测试</a>
          <!-- 弹窗slot （需要时请将slot改为modalFooter）-->
          <template slot="modalFooter..." slot-scope="{ data }">
            <a-button key="test" @click="modalTest(data)">测试</a-button>
            <a-button key="ok" type="primary" @click="modalOk(data)">确定</a-button>
          </template>
        </TableEdit>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script>
import TableEdit from '@/components/TableEdit';
import Select from '@/components/Select';
// import common from '@/utils/common';
import config from './config';

export default {
  components: { TableEdit, Select },
  props: {},
  data() {
    this.config = config(this);
    return {
      activeKey: [],
      params: {
        url: '/api/home/<USER>/table',
        reqParams: {
          from: 'tableEdit'
        },
        actionBtns: ['remove'],
        editConfig: this.config.editConfig,
        columns: this.config.columns,
        pagination: false
      },
      params1: {
        url: '/api/home/<USER>/table',
        reqParams: {
          from: 'tableEdit'
        },
        initEditStatus: true,
        actionBtns: ['add', 'remove'],
        editConfig: this.config.editConfig,
        columns: this.config.columns,
        pagination: false
      },
      params2: {
        type: 'lineEdit',
        url: '/api/home/<USER>/table',
        reqParams: {
          from: 'tableEdit'
        },
        actionBtns: ['edit', 'remove', 'test'],
        actionCbks: {
          save: this.reqSave,
          remove: this.reqDelete
        },
        // modalEdit: true,
        editConfig: this.config.editConfig,
        columns: this.config.columns,
        pagination: false,
        modalConfig: {
          title: '编辑xxxxx'
        }
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    addLine(refKey) {
      this.$refs[refKey].addLine();
    },
    editAll(refKey) {
      this.$refs[refKey].editAll();
    },
    cancelAll(refKey) {
      const tableEdit = this.$refs[refKey];
      tableEdit.cancelAll();
    },
    saveAll(refKey) {
      const tableEdit = this.$refs[refKey];
      tableEdit.validate(() => {
        tableEdit.saveAll();
      });
    },

    // modal测试（自定义footer）
    modalTest(data) {
      console.log(JSON.stringify(data, 888888));
      const tableEdit = this.$refs.tableEdit2;
      const modalForm = tableEdit.$refs.modalForm;

      modalForm.validate((valid, error) => {
        if (!valid) {
          return;
        }
        this.$message.success('测试通过');
        // tableEdit.modalCancel();
      });
    },
    modalOk(data) {
      console.log(JSON.stringify(data, 888888));
      const tableEdit = this.$refs.tableEdit2;
      const modalForm = tableEdit.$refs.modalForm;

      modalForm.validate((valid, error) => {
        if (!valid) {
          return;
        }
        // 模拟请求，刷新表格
        this.$showLoading();
        setTimeout(() => {
          this.$hideLoading();
          tableEdit.modalCancel();
          tableEdit.refresh();
        }, 2000);
      });
    },

    // 操作列测试
    actionTest(record) {
      console.log(record);
    },
    reqSave(record) {
      console.log(record, 'pppppppppp')
      const tableEdit = this.$refs.tableEdit2;
      // 模拟请求，刷新表格
      // 需要传promise
      return new Promise((resolve, reject) => {
        this.$showLoading();
        setTimeout(() => {
          this.$hideLoading();
          const flag = 1;
          if (flag) {
            tableEdit.refresh();
            resolve();
          } else {
            this.$message.error({
              content: '错误。。。',
              key: 'globalLoading'
            });
            reject(new Error('sssssss'));
          }
        }, 1000);
      });
    },
    reqDelete(record) {
      const tableEdit = this.$refs.tableEdit2;
      // 模拟请求，刷新表格
      this.$showLoading();
      setTimeout(() => {
        this.$hideLoading();
        tableEdit.refresh();
      }, 2000);
    }
  }
};
</script>

<style lang="less" scoped>
</style>
