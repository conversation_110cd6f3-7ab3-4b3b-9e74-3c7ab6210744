<template>
  <div class="form-trigger-comp">
    <template v-if="supportTypeList.indexOf(fieldOption.type) > -1">
      <div class="value" :style="{'flex-grow': flexGrow}">
        <div class="value-text" v-show="!isEdit">{{dataTxt || '--'}}</div>
        <component
          v-show="isEdit"
          :is="realType"
          v-model="data"
          v-bind="fieldOption.props || {}"
          v-on="fieldOption.listeners || {}"
          :loaded="loaded"
          :ref="fieldOption.key"
          :style="{ width: compWidth }"
          @blur="saveData"
        ></component>
      </div>
      <div class="icon-box">
        <template v-if="fieldOption.mode != 'autoSave'">
          <Actions
            v-show="isEdit"
            :hideToolTip="true"
            :btnList="saveBtnList"
            @btnsFn="type=>btnsFn(type, fieldOption.key)"
          ></Actions>
        </template>
        <Actions
          v-show="!isEdit"
          :hideToolTip="true"
          :btnList="editBtnList"
          @btnsFn="type=>btnsFn(type, fieldOption.key)"
        ></Actions>
      </div>
    </template>
    <div class="no-support-tip" v-else>暂不支持{{fieldOption.type}}类型</div>
  </div>
</template>

<script>
import Actions from '@/components/Actions';
import CustomFormComponents from './register';
import config from './config';

const compMap = config().compMap;
export default {
  model: {
    prop: 'value',
    event: 'save'
  },
  components: {
    Actions,
    ...config().defineComp
  },
  props: {
    // hideLabel: {
    //   type: Boolean,
    //   default: false
    // },
    field: {
      type: Object | Function,
      default: () => {
        return {};
      }
    },
    value: {
      type: String | Number | Object | Array | Function,
      default: undefined
    }
  },
  data() {
    this.config = config(this);
    return {
      isInit: true,
      isEdit: false,
      fieldOption: this.field,
      data: this.value,
      dataTxt: '',
      realType: '',
      sourceData: [],
      editBtnList: [
        {
          name: '编辑',
          type: 'edit',
          icon: 'lu-icon-edit'
        }
      ],
      saveBtnList: [
        {
          name: '取消',
          type: 'cancel',
          icon: 'lu-icon-wrong'
        },
        {
          name: '保存',
          type: 'save',
          icon: 'lu-icon-right1'
          // confirm: '确定保存'
        }
      ],
      supportTypeList: ['Input', 'Select']
    }
  },
  created() {
    const matchItem = compMap[this.fieldOption.type] || CustomFormComponents.comp[this.fieldOption.type] || {};
    this.realType = matchItem.compName ? matchItem.compName : `a${this.fieldOption.type.replace(/([A-Z])/g, $0 => '-' + $0.toLowerCase())}`;
  },
  computed: {
    flexGrow() {
      return this.isEdit && this.fieldOption.width && (this.fieldOption.width + '').endsWith('%') ? parseInt(this.fieldOption.width) / 100 : 0;
    },
    // labelWidth() {
    //   return this.fieldOption.labelWidth ? (this.fieldOption.labelWidth + '').endsWith('%') ? this.fieldOption.labelWidth : this.fieldOption.labelWidth + 'px' : '100px';
    // },
    compWidth() {
      return this.fieldOption.width ? (this.fieldOption.width + '').endsWith('%') ? '100%' : this.fieldOption.width + 'px' : '200px';
    }
  },
  watch: {
    value: {
      handler(newVal, oldVal) {
        this.data = newVal;
        this.dataTxt = this.data;
        this.config.setDataText(this.data); // 文字回显
      },
      immediate: true
    },
    field: {
      handler(newVal, oldVal) {
        this.fieldOption = {...this.field};
        const matchItem = compMap[this.fieldOption.type] || CustomFormComponents.comp[this.fieldOption.type] || {};
        this.realType = matchItem.compName ? matchItem.compName : `a${this.fieldOption.type.replace(/([A-Z])/g, $0 => '-' + $0.toLowerCase())}`;
      },
      immediate: true
    }
  },
  methods: {
    loaded(options = []) {
      if (this.fieldOption.type == 'Select') {
        this.sourceData = options;
        if (this.isInit) {
          this.dataTxt = this.data;
          this.config.setDataText(this.data); // 文字回显
          this.isInit = false;
        }
      }
    },
    btnsFn(type, key) {
      if (type == 'edit') {
        this.$emit('edit', key);
        this.isEdit = true;
      } else if (type == 'cancel') {
        this.data = this.value;
        this.dataTxt = this.data;
        this.config.setDataText(this.data); // 文字回显
        this.isEdit = false;
      } else if (type == 'save') {
        this.dataTxt = this.data;
        this.config.setDataText(this.data); // 文字回显
        this.$emit('save', this.data, this.dataTxt, key);
        this.isEdit = false;
      }
    },
    saveData() {
      const {mode, type, key} = this.fieldOption;
      if (mode == 'autoSave' && type == 'Input') {
        this.dataTxt = this.data;
        this.config.setDataText(this.data); // 文字回显
        this.$emit('save', this.data, this.dataTxt, key);
        this.isEdit = false;
      }
    }
  }
}
</script>

<style lang="less" scoped>
.form-trigger-comp {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .value {
    .font-size(14px);
    color: @font-color-strong;
    word-break: break-all;
    .value-text {
      line-height: 24px;
    }
  }
  .icon-box {
    padding-left: 8px;
    font-size: 16px;
    flex-shrink:0
  }
}
</style>