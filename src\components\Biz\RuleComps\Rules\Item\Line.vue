<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    version="1.1"
    :width="width"
    :height="height"
  >
    <path
      v-for="(item, index) in path"
      :key="index"
      :d="item"
      stroke="#d9d9d9"
      stroke-width="1"
      fill="none"
    />
  </svg>
</template>

<script>
export default {
  components: {},
  props: {
    list: Array
  },
  data() {
    return {
      path: [],
      width: 0,
      height: 0
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {
    this.draw();
  },
  methods: {
    draw() {
      const parentDom = this.$parent.$el;
      const baseNode = parentDom.querySelector('.base-content');
      const children = (
        parentDom.querySelector('.rule-tree-item-children') || {}
      ).children;
      const nodes = [...(children || [])].slice(0, -1);
      // console.log(nodes, 'jhhhhhhh');
      const path = [];
      const baseNodeRect = baseNode.getBoundingClientRect();
      let lastY = 0;
      nodes.forEach((item, index) => {
        const rect = item.getBoundingClientRect();
        const x = rect.x - baseNodeRect.x + 4;
        const y = rect.y - baseNodeRect.y + 24;
        // console.log(x, y, 'xxyyy');
        if (index === nodes.length - 1) {
          path.push(
            `M 15 40 15 ${y - 10} Q 15 ${y} ${15 + 10} ${y} M ${15 +
              10} ${y} ${x} ${y}`
          );
          lastY = y;
        } else {
          path.push(
            `M 15 ${y - 10} Q 15 ${y} ${15 + 10} ${y} M ${15 +
              10} ${y} ${x} ${y}`
          );
        }
      });
      this.width = 120;
      this.height = lastY + 20;
      this.path = path;
      // console.log(this.width, this.height, this.path);
    }
  }
};
</script>

<style scoped lang="less">
svg {
  position: absolute;
  left: 0;
  top: 0;
}
</style>
