<template>
  <div class="white-list-detail-container">
    <div class="frame-button-wrapper">
      <div class="page-change-container">
        <span class="page-info">{{ current }}/{{ count }}</span>
        <a-button-group>
          <a-button @click="onPrev" :disabled="count < 0 || current === 1">
            <custom-icon type="left" />
          </a-button>
          <a-button @click="onNext" :disabled="count < 0 || current === count">
            <custom-icon type="right" />
          </a-button>
        </a-button-group>
      </div>
      <div class="line"></div>
      <a-button @click="toBack" class="highlight">返回</a-button>
      <a-popconfirm title="确定删除该数据?" @confirm="remove">
        <a-button type="primary" v-if="$permission.whiteList('del')"
          >移除</a-button
        >
      </a-popconfirm>
    </div>
    <!-- 顶部展示信息 -->
    <a-card
      style="width: 100%"
      class="common-page-card header-content"
      :bordered="false"
    >
      <div class="info-list">
        <div>
          <span>标签属性：</span>
          <span>{{ topData.label_attribute }}</span>
        </div>
        <div>
          <span>标签ID：</span>
          <LimitLabel :limit="20" :label="topData.label_id" :needCopy="true" />
        </div>
        <div class="no-border">
          <span>标签ID类型：</span>
          <span>{{ topData.label_type }}</span>
        </div>
      </div>
      <div class="info-list">
        <div>
          <span>状态：</span>
          <span>{{ labelStatus[topData.label_status] }}</span>
        </div>
        <div>
          <span>标签来源：</span>
          <span>{{ topData.label_source }}</span>
        </div>
        <div class="no-border">
          <span>项目：</span>
          <span>{{ topData.projects && topData.projects.join('') }}</span>
        </div>
        <!-- <div>
          <span>数据源：</span>
          <DataSourceTag :dataSource="topData.datasource_info" width="160px" />
        </div>
        <div>
          <span>Schema：</span>
          <span>{{ topData.schema }}</span>
        </div> -->
      </div>
      <div class="info-list">
        <div>
          <span>生效时间：</span>
          <span>{{ topData.created_at }}</span>
        </div>
        <div>
          <span>失效时间：</span>
          <span>{{ topData.expired_time }}</span>
        </div>
        <div class="no-border">
          <span>有效期：</span>
          <span>{{
            topData.permanent_day == 0 ? '永久' : topData.permanent_day + '天'
          }}</span>
        </div>
      </div>
      <div class="info-list">
        <div>
          <span>申请人：</span>
          <span>{{ topData.created_by }}</span>
        </div>
        <div>
          <span>审批人：</span>
          <span>{{ topData.audit_user }}</span>
        </div>
      </div>
    </a-card>
    <a-card
      style="width: 100%"
      class="common-page-card result-detail"
      :bordered="false"
    >
      <a-tabs
        type="card"
        :active-key="activeKey"
        @change="tabsChange"
        class="xml-or-sql"
      >
        <div slot="tabBarExtraContent">
          <div v-if="activeKey == 'sql'">
            <span>数据源：</span>
            <DataSourceTag
              v-if="topData.datasource_info && topData.datasource_info.env"
              :dataSource="topData.datasource_info"
              width="160px"
            />
            <span v-else>{{ '--' }}</span>
          </div>
          <div v-if="activeKey == 'sql'">
            <span>schema：</span>
            <span>{{ topData.schema || '--' }}</span>
          </div>

          <div v-if="activeKey == 'xml'">
            <span>分支：</span>
            <span>{{ sqlMap.review_point || '--' }}</span>
          </div>
          <div v-if="activeKey == 'xml'">
            <span>代码框架：</span>
            <span>{{ sqlMap.sql_map_type || '--' }}</span>
          </div>

          <div v-if="activeKey == 'xml'">
            <span>SQL来源：</span>
            <LimitLabel :limit="36" :label="sqlMap.file_path" mode="simple" />
          </div>
        </div>
        <a-tab-pane key="sql" tab="SQL">
          <div v-if="sqlList.length > 1" class="sql-list-container">
            <div class="sql-list-item">
              <div
                v-for="item in sqlList"
                :key="item.sql_id"
                @click="sqlChange(item.sql_id)"
                :class="item.sql_id == sqlActiveKey && 'active'"
              >
                <span :class="[item.risk]">{{ riskText[item.risk] }}</span>
                {{ item.sql_id }}
              </div>
            </div>
            <div class="coder-container">
              <!-- <div class="sql-id-content">[ SQL ID：{{ sqlItem.sql_id }} ]</div> -->
              <Prettier :value="sqlItem.sql_source" type="sql" />
            </div>
          </div>

          <div v-else-if="sqlList.length == 1" class="sql-id-content">
            <div class="sql-id">[ SQL ID：{{ sqlItem.sql_id }} ]</div>
            <div>
              <Prettier :value="sqlItem.sql_source" type="sql" />
            </div>
          </div>
          <div v-else style="height: 100px">
            <Prettier value="" />
          </div>
        </a-tab-pane>
        <a-tab-pane
          key="xml"
          tab="XML"
          class="xml-tab-pane"
          v-if="!isEmptySqlMap"
        >
          <!-- <div class="sql-id-content">[ 分支：{{ sqlMap.review_point }} ]</div> -->
          <Prettier
            :value="sqlMap.sql_text"
            class="audit-result-xml"
            type="xml"
          />
        </a-tab-pane>
      </a-tabs>

      <div class="result-detail-wrapper" v-show="activeKey == 'sql'">
        <div class="result">
          <div class="risk" v-if="sqlItem.risk">
            <div class="title">
              AI风险等级
              <span :class="sqlItem.risk">{{ riskText[sqlItem.risk] }}</span>
            </div>
            <a-collapse
              activeKey="0"
              :bordered="false"
              :accordion="true"
              expandIconPosition="right"
            >
              <a-collapse-panel
                v-for="(item, index) in sqlItem.ai_comment"
                :key="index"
              >
                <div slot="header" class="header">
                  <custom-icon
                    type="lu-icon-alarm"
                    :style="{
                      color: item.rule_result == 1 ? '#f29339' : '#e71d36'
                    }"
                  />
                  <span>{{ item.ai_comment }}</span>
                </div>
                <MarkdownViewer
                  class="rich-editor-preview"
                  :value="item.suggest"
                  v-if="item.suggest"
                ></MarkdownViewer>
              </a-collapse-panel>
            </a-collapse>
            <!-- <div
              v-for="(item, index) in sqlItem.ai_comment"
              :key="index"
              class="rule-text"
            >
              <custom-icon
                type="lu-icon-alarm"
                :style="{
                  color: item.rule_result == 1 ? '#f29339' : '#e71d36'
                }"
              />
              <span>{{ item.ai_comment }}</span>
              <MarkdownViewer
                class="rich-editor-preview"
                :value="item.suggest"
                v-if="item.suggest"
              ></MarkdownViewer>
            </div> -->
          </div>
          <custom-empty v-else></custom-empty>
        </div>
        <div class="sql-plan">
          <SqlPlan ref="sqlPlan" :resultData="resultData" :id="id"></SqlPlan>
        </div>
      </div>
    </a-card>
  </div>
</template>
<script>
import bodyMinWidth from '@/mixins/bodyMinWidth';
import Prettier from '@/components/Prettier';
import LimitLabel from '@/components/LimitLabel';
import DataSourceTag from '@/components/Biz/DataSourceTag';
import MarkdownViewer from '@/components/Markdown/viewer';
import SqlPlan from './SqlPlan';
import { removeRecord, getDetailPageData } from '@/api/whitelist';

export default {
  // name: 'white-list-detail',
  components: { Prettier, LimitLabel, DataSourceTag, MarkdownViewer, SqlPlan },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    const id = this.$route.params.id;
    this.searchData = _.merge(
      {},
      this.$store.state.common.searchCache['whiteList'] || {}
    );
    return {
      id,
      activeKey: 'sql',
      sqlActiveKey: null,
      nextId: null,
      prevId: null,
      current: null,
      count: null,
      riskText: {
        high: '高',
        low: '低',
        no_risk: '无',
        error: '异常'
      },
      sqlMap: {},
      topData: {},
      sqlList: [],
      sqlItem: {},
      resultData: {},
      labelStatus: {
        '-1': '审核未通过',
        0: '待审核',
        1: '审核通过',
        2: '已删除',
        3: '已失效'
      },
      ruleActiveKey: null
    };
  },
  computed: {
    isEmptySqlMap() {
      return _.isEmpty(this.sqlMap);
    }
  },
  created() {},
  mounted() {
    this.init(this.id);
  },
  methods: {
    init(id) {
      this.$showLoading();
      getDetailPageData({ ...this.searchData, id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ duration: 0 });
            const resData = _.get(res, 'data.data');
            this.topData = {
              audit_time: resData.audit_time,
              audit_user: resData.audit_user,
              created_at: resData.created_at,
              created_by: resData.created_by,
              data_source_id: resData.data_source_id,
              datasource_info: resData.datasource_info,
              expired_time: resData.expired_time,
              id: resData.id,
              label_attribute: resData.label_attribute,
              label_id: resData.label_id,
              label_source: resData.label_source,
              label_status: resData.label_status,
              label_type: resData.label_type,
              permanent_day: resData.permanent_day,
              projects: resData.projects,
              schema: resData.schema
            };
            this.id = resData.id;
            this.nextId = resData.next_id;
            this.prevId = resData.prev_id;
            this.current = resData.curr_idx;
            this.count = resData.count;
            this.sqlMap = resData.sql_map;
            if (!_.isEmpty(resData.sql_list)) {
              this.sqlList = resData.sql_list;
              this.sqlItem = resData.sql_list[0];
            }
            this.sqlActiveKey = this.sqlItem.sql_id;
            this.resultData = {
              ...this.resultData,
              table_list: resData.table_list,
              review_detail_id: resData.review_detail_id,
              data_source_id: resData.data_source_id,
              schema: resData.schema,
              ...this.sqlItem
            };
            this.setNavi();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    toBack() {
      this.$router.push({
        name: 'white-list'
      });
    },
    remove() {
      this.$showLoading();
      removeRecord({ id: this.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            if (this.current == this.count && this.count >= 2) {
              this.$nextTick(() => {
                this.onPrev();
              }, 1000);
            } else if (this.count == 1) {
              this.toBack();
            } else {
              this.$nextTick(() => {
                this.onNext();
              }, 1000);
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onPrev() {
      this.init(this.prevId);
    },
    onNext() {
      this.init(this.nextId);
    },
    // sql xml 切换
    tabsChange(activeKey) {
      this.activeKey = activeKey;
    },
    sqlChange(id) {
      if (id) {
        this.sqlActiveKey = id;
        this.sqlList.forEach(item => {
          if (item.sql_id == id) {
            this.sqlItem = item;
            this.resultData = { ...this.resultData, ...item };
          }
        });
      }
    },
    setNavi() {
      if (this.id !== this.$route.params.id) {
        this.$router.push({
          name: 'white-list-detail',
          params: { id: this.id }
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.white-list-detail-container {
  background: #f9f9f9;
  .frame-button-wrapper {
    display: flex;
    .page-change-container {
      // position: absolute;
      // right: 172px;
      // top: 14px;
      // z-index: 10;
      // background: transparent;
      .page-info {
        margin-right: 12px;
      }
      .ant-btn-group {
        .ant-btn {
          width: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          &:first-child {
            border-radius: 6px 0 0 6px !important;
          }
          &:last-child {
            border-radius: 0 6px 6px 0 !important;
          }
          .anticon {
            font-size: 12px;
          }
        }
      }
    }
    .line {
      width: 1px;
      height: 32px;
      margin: 0 8px 0 16px;
      background: #f0f0f0;
    }
  }
  .common-page-card {
    margin-bottom: 12px;
    border-radius: 8px;
    padding: 16px 24px;
    &.header-content {
      /deep/.ant-card-body {
        display: flex;
        .info-list {
          width: 25%;
          margin-right: 40px;
          &:last-child {
            margin-right: 0px;
          }
          > div {
            height: 38px;
            line-height: 38px;
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            .limit-label {
              pre {
                white-space: nowrap;
              }
            }
            &.no-border {
              border: none;
            }
            > span {
              font-family: PingFangSC-Regular;
              white-space: nowrap;
              &:first-child {
                width: 100px;
                color: #1f1f1f;
              }
              &:last-child {
                color: #595959;
              }
            }
          }
        }
      }
    }
  }
  .result-detail {
    margin: 0;
    /deep/.ant-card-body {
      .xml-or-sql {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        &.ant-tabs {
          .ant-tabs-bar {
            border: none;
            position: relative;
            .ant-tabs-nav-container {
              .ant-tabs-nav-scroll {
                .ant-tabs-nav {
                  > div {
                    background: rgb(239, 239, 239);
                    padding: 2px;
                    border-radius: 8px;
                    .ant-tabs-tab {
                      border-radius: 0;
                      border: none;
                      margin-right: 0 !important;
                      height: 32px;
                      line-height: 32px;
                      color: #0f0f0f;
                      &:first-child {
                        margin-left: 0 !important;
                      }
                      background: transparent;
                      &.ant-tabs-tab-active {
                        border-bottom: none;
                        background: #fff;
                        border-radius: 8px;
                        color: #595959;
                      }
                    }
                  }
                }
              }
            }
            .ant-tabs-extra-content {
              height: 36px;
              line-height: 36px;
              // text-align: right;
              padding-right: 0;
              font-family: PingFangSC-Regular;
              color: #1f1f1f;
              > div {
                display: flex;
                align-items: center;
                > div {
                  display: flex;
                  align-items: center;
                  margin-left: 36px;
                }
              }
            }
          }
          .ant-tabs-content {
            background: #e6f4ff;
            border-radius: 6px;
            .ant-tabs-tabpane-active {
              // max-height: 350px;
              // overflow-y: auto;
              // &.xml-tab-pane {
              //   max-height: 100%;
              // }
              .custom-prettier {
                background: none;
              }
              .sql-list-container {
                display: flex;
                height: 100%;
                .sql-list-item {
                  width: 36%;
                  min-width: 360px;
                  padding: 16px 8px;
                  max-height: 350px;
                  overflow-y: auto;
                  border-right: 1px solid #e8e8e8;
                  > div {
                    padding: 8px 16px;
                    cursor: pointer;
                    &.active {
                      background: rgba(0, 0, 0, 0.05);
                    }
                    > span {
                      display: inline-block;
                      margin-right: 8px;
                      width: 22px;
                      height: 22px;
                      border-radius: 4px;
                      font-size: 12px;
                      text-align: center;
                    }
                    .high {
                      color: #f5222d;
                      border: 1px solid #ffa39e;
                      background: #fff1f0;
                    }
                    .low {
                      color: #faad14;
                      border: 1px solid #ffe58f;
                      background: #fffbe6;
                    }
                    .no_risk {
                      color: #52c41a;
                      border: 1px solid #b7eb8f;
                      background: #f6ffed;
                    }
                    .error {
                      display: inline-block;
                      white-space: nowrap;
                      padding: 0 2px;
                      width: auto;
                      color: rgba(0, 0, 0, 0.85);
                      background: #f5f5f5;
                      border: 1px solid #d9d9d9;
                    }
                  }
                }
                .coder-container {
                  width: 70%;
                  .custom-prettier {
                    max-height: 350px;
                    overflow-y: auto;
                    width: 100%;
                  }
                }
              }
              .sql-id-content {
                max-height: 350px;
                overflow-y: auto;
                &:hover {
                  .fullscreen-tools {
                    position: absolute;
                    top: -52px;
                    right: 0;
                    display: block;
                  }
                }
                .sql-id {
                  display: inline-block;
                  margin: 16px 16px 0 16px;
                  padding: 8px 8px;
                  background: rgba(0, 0, 0, 0.05);
                  border-radius: 4px;
                }
              }
            }
          }
        }
      }

      .result-detail-wrapper {
        height: 100%;
        display: flex;
        .result {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          width: 30%;

          .risk {
            .title {
              font-family: PingFangSC-Regular;
              color: #1f1f1f;
              padding: 12px 0 10px 0;
              border-bottom: 1px solid #e8e8e8;
              > span {
                display: inline-block;
                margin-left: 8px;
                width: 22px;
                // height: 22px;
                border-radius: 4px;
                font-size: 12px;
                text-align: center;
                line-height: 20px;
              }
              .high {
                color: #f5222d;
                border: 1px solid #ffa39e;
                background: #fff1f0;
              }
              .low {
                color: #faad14;
                border: 1px solid #ffe58f;
                background: #fffbe6;
              }
              .no_risk {
                color: #52c41a;
                border: 1px solid #b7eb8f;
                background: #f6ffed;
              }
              .error {
                display: inline-block;
                white-space: nowrap;
                padding: 0 2px;
                width: auto;
                color: rgba(0, 0, 0, 0.85);
                background: #f5f5f5;
                border: 1px solid #d9d9d9;
              }
            }
            .ant-collapse {
              .ant-collapse-item {
                border-bottom: 1px solid #e8e8e8;
                .ant-collapse-header {
                  background: #fff;
                }
                .ant-collapse-content-box {
                  padding: 16px 24px;
                }
              }
            }

            // .rule-text {
            //   padding: 12px 0 0 0;
            //   > span {
            //     word-break: break-all;
            //   }
            //   .rich-editor-preview {
            //     margin: 12px 0;
            //     padding: 8px;
            //     background-color: #f5f5f5;
            //     border-radius: 8px;
            //     .hljs {
            //       border: none;
            //       margin: 0;
            //     }
            //     h6 {
            //       margin-bottom: 0;
            //     }
            //   }
            // }
          }
          .custom-empty {
            min-height: 300px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }
        .sql-plan {
          width: 70%;
          border-left: 1px solid #e8e8e8;
          display: block;
        }
      }
    }
  }
}
/deep/.CodeMirror {
  border-radius: 0;
  height: 100% !important;
  background: #e6f4ff;
  .CodeMirror-scroll {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    overflow: auto !important;
    .CodeMirror-gutters {
      background: #e6f4ff !important;
      border: none;
    }
    .CodeMirror-linenumber {
      color: #595959 !important;
    }
  }
}
</style>