import Vue from 'vue';
import store from '../store';

function permission(code, params = {}) {
  if (params.show) {
    return true;
  }
  const auth = store.state.auth;
  if (!auth || !auth.eleMap == null) {
    return true;
  }
  // console.log(code, auth.eleMap)
  return auth.eleMap[code] != null;
}

Vue.prototype.$permission = window.$permission = {
  project: key => {
    return permission(`sql_review_project_${key}`);
  },
  projectGroup: (key, params) => {
    return permission(`sql_review_projectGroup_${key}`, params);
  },
  rule: key => {
    return permission(`sql_review_rule_${key}`);
  },
  topSql: key => {
    return permission(`sql_review_topSql_${key}`);
  },
  realTimeSql: key => {
    return permission(`sql_review_realTimeSql_${key}`);
  },
  slowLogAnalyze: key => {
    return permission(`sql_review_slowLogAnalyze_${key}`);
  },
  role: key => {
    return permission(`sql_review_role_${key}`);
  },
  user: key => {
    return permission(`sql_review_user_${key}`);
  },
  whiteList: key => {
    return permission(`sql_review_whiteList_${key}`);
  }
};

// 批量处理
window.$permissionBatch = {
  // 任意一个存在即合法
  some: (batch = []) => {
    for (let i = 0; i < batch.length; i++) {
      const { module, values = [] } = batch[i] || {};
      if (module) {
        for (let j = 0; j < values.length; j++) {
          if ($permission[module] && $permission[module](values[j])) {
            return true;
          }
        }
      }
    }
    return false;
  }
};
