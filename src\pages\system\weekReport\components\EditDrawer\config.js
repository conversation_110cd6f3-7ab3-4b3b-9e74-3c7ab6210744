export default function(ctx) {
  const baseFields = [
    {
      type: 'RangePicker',
      label: '日期',
      key: 'date',
      props: {
        format: 'YYYY-MM-DD'
        // valueFormat: 'YYYY-MM-DD',
      }
    },
    // {
    //   type: 'DatePicker',
    //   label: '结束日期',
    //   key: 'end_date',
    //   props: {
    //     format: 'YYYY-MM-DD',
    //     valueFormat: 'YYYY-MM-DD',
    //   }
    // },
    {
      type: 'Coder',
      label: '报表SQL',
      key: 'report_sql',
      props: {
        mode: 'sql'
      }
    },
    {
      type: 'Textarea',
      label: '邮件主题',
      key: 'email_subject',
      props: {
      }
    },
    {
      type: 'Textarea',
      label: '邮件收件人',
      key: 'email_send_to',
      props: {
        placeholder: '多个以逗号分隔'
      }
    },
    {
      type: 'Textarea',
      label: '邮件抄送人',
      key: 'email_cc_to',
      props: {
        placeholder: '多个以逗号分隔'
      }
    }
  ];

  return {
    baseFields
  };
}
