<template>
  <div class="custom-btns">
    <template v-for="(item, index) in handleBtnList.slice(0, limit)">
      <a-button
        :key="index"
        v-if="item.type"
        :type="item.type"
        :icon="item.icon && item.icon"
        :disabled="item.disabled"
        @click="(e) => btnsFn(item.key, e, item)"
      >{{item.name}}</a-button>
      <a-button
        v-else
        :key="index"
        :disabled="item.disabled"
        :icon="item.icon && item.icon"
        class="highlight"
        @click="(e) => btnsFn(item.key, e, item)"
      >{{item.name}}</a-button>
    </template>
    <a-popover
      placement="bottom"
      overlayClassName="biz-custom-btns-ellipsis"
      v-if="handleBtnList.length > limit"
    >
      <template slot="content">
        <a-button
          v-for="(item, index) in handleBtnList.slice(limit)"
          :key="index"
          :disabled="item.disabled"
          :icon="item.icon && item.icon"
          class="highlight"
          @click="(e) => btnsFn(item.key, e, item)"
        >{{item.name}}</a-button>
      </template>
      <a>
        <custom-icon class="btns-ellipsis" type="ellipsis" />
      </a>
    </a-popover>
  </div>
</template>

<script>
export default {
  props: {
    limit: {
      type: Number,
      default: 2
    },
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  components: {},
  data() {
    return {
      visible: false
    };
  },
  computed: {
    handleBtnList() {
      return this.btnList.filter(item => item.visible != false);
    }
  },
  mounted() {},
  destroyed() {},
  methods: {
    btnsFn(key) {
      this.$emit('btnsFn', key);
    }
  }
};
</script>

<style lang="less" scoped>
.custom-btns {
  > .ant-btn {
    height: 36px;
    font-size: 14px;
    font-weight: 600;
    margin-right: 0;
    margin-left: 8px;
    &.highlight {
      color: #008adc;
      border-color: #7fc4ed;
      &:hover {
        color: @primary-5;
        border-color: #008adc;
      }
    }

    &.ant-btn-primary {
      background: #008adc;
      color: #ffffff;
      &:hover {
        background: #219be3;
        color: #ffffff;
      }
    }
  }
  .action-btn {
    margin: 0 6px;
  }
  .btns-ellipsis {
    margin: 0 6px;
    transform: rotate(90deg);
    font-size: 24px;
  }
}
</style>
<style lang="less">
.biz-custom-btns-ellipsis {
  .ant-popover-content {
    .ant-popover-inner {
      .ant-popover-inner-content {
        display: flex;
        flex-direction: column;
        .ant-btn {
          height: 36px;
          font-size: 14px;
          font-weight: 600;
          margin-right: 0;
          margin-top: 8px;
          border-radius: 8px;
          &.highlight {
            color: #008adc;
            border-color: #7fc4ed !important;
            &:hover {
              color: @primary-5;
              border-color: #008adc;
            }
          }
        }
      }
    }
  }
}
</style>