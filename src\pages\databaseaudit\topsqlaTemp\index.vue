<template>
  <div class="page-database-audit up-20">
    <Content ref="pageList" />
</div>
</template>

<script>
import InstanceItem from '@/components/Biz/InstanceItem';
import InstanceModal from '../components/Instance';
import Content from './Content';
// import { checkPermission } from '@/api/databaseaudit/topsql';
export default {
  components: { InstanceItem, Content, InstanceModal },
  data() {
    // 从缓存里获取
    // const panes = [
    //   { title: 'Tab 1', content: 'Content of Tab 1', key: '1' },
    //   { title: 'Tab 2', content: 'Content of Tab 2', key: '2' }
    // ];
    let panes = [];
    let activeKey;
    const user = this.$store.state.account.user;
    const userName = user.name;
    const databseInfo = localStorage.getItem('databaseaudit');
    const lastUserName = _.get(JSON.parse(databseInfo), 'name') || '';
    try {
      if (databseInfo && lastUserName == userName) {
        const info = JSON.parse(databseInfo) || {};
        panes = (info.panes || []).filter(item =>
          (item.key + '').startsWith('instanceId_')
        );
        if ((info.activeKey + '').startsWith('instanceId_')) {
          activeKey = info.activeKey;
        }
      }
    } catch (e) {}
    this.instanceList = [];
    return {
      instanceParams: this.getInstanceParams(),
      activeKey,
      panes,
      id: null,
      newTabIndex: 0,
      authInfo: null
    };
  },

  mounted() {
    // panes为空，直接跳出选择实例
    if (this.panes.length <= 0) {
      this.add();
    }
  },
  methods: {
    getInstanceKey() {
      return `instanceId_${Math.random()
        .toString(36)
        .substr(2)}`;
    },
    getInstanceParams() {
      return {
        url: `/sqlreview/after_audit/get_data_source`,
        reqParams: {},
        mode: 'default',
        placeholder: '请选择实例',
        backSearch: false,
        dropdownMatchSelectWidth: false,
        allowClear: false,
        getPopupContainer: el => {
          return document.body;
        },
        loaded: (data = []) => {
          this.instanceList = data;
        },
        beforeLoaded(data) {
          return data.map(item => {
            return {
              ...item,
              db_type: item.db_type,
              instance_usage: item.env,
              showText: item.label
            };
          });
        }
      };
    },
    onInstanceChange(val) {
      // 验证instance
      // checkPermission({ data_source_id: val })
      //   .then(res => {
      //     if (CommonUtil.isSuccessCode(res)) {
      //     } else {
      //       this.$message.error(_.get(res, 'data.message'));
      //     }
      //   })
      //   .catch(e => {
      //     this.$hideLoading({
      //       method: 'error',
      //       tips: _.get(e, 'response.data.message') || '请求失败'
      //     });
      //   });
      const instanceItem = this.instanceList.find(item => item.value == val);
      this.replacePanel(instanceItem);
    },
    onChange(val) {
      this.cache();
    },
    onEdit(targetKey, action) {
      this[action](targetKey);
    },
    add() {
      // this.$refs.InstanceModal.show();//
    },
    onAddPanel(data) {
      if (data) {
        const panes = this.panes;
        const activeKey = this.getInstanceKey();
        panes.push({ ...data, key: activeKey });
        this.panes = panes;
        this.activeKey = activeKey;
        this.cache();
      }
    },
    replacePanel(data) {
      const existPane = this.panes.find(pane => pane.key === this.activeKey);
      if (existPane && data) {
        const activeKey = this.getInstanceKey();
        Object.assign(existPane, data, {
          key: activeKey
        });
        this.activeKey = activeKey;
        this.panes = [...this.panes];
        this.cache();
      }
    },
    remove(targetKey) {
      let activeKey = this.activeKey;
      let lastIndex;
      this.panes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1;
        }
      });
      const panes = this.panes.filter(pane => pane.key !== targetKey);
      if (panes.length && activeKey === targetKey) {
        if (lastIndex >= 0) {
          activeKey = panes[lastIndex].key;
        } else {
          activeKey = panes[0].key;
        }
      }
      this.panes = panes;
      this.activeKey = activeKey;
      this.cache();
    },
    cache() {
      const user = this.$store.state.account.user;
      const userName = user.name;
      localStorage.setItem(
        'databaseaudit',
        JSON.stringify({
          activeKey: this.activeKey,
          panes: this.panes,
          name: userName
        })
      );
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.page-database-audit {
  display: flex;
  flex-grow: 1;
  // background: #ffffff;
  /deep/ .page-database-audit-instance {
    border-radius: 12px 12px 0 0;
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    width: 100%;
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    // tab-bar
    > .ant-tabs-bar {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-end;
      align-items: flex-end;
      margin: 0;
      background: #f2f2f2;
      border-bottom: 0;
      .ant-tabs-tab {
        background: #f2f2f2;
        border-left: 1px solid #fff;
        border-radius: 0;
        margin: 0;
        border-right: 0;
        border-top: 0;
        padding: 0;
        &:first-child {
          border: none;
        }
        > div {
          display: flex;
          align-items: center;
          > span {
            display: flex;
          }
        }

        .biz-instance-item {
          display: inline-block;
          .instance-item-tag {
            background: #f2f2f2;
            border: 0;

            &::after {
              display: none;
            }

            .database-image {
              margin-left: 0;

              > span > .custom-icon {
                margin-right: 0;
              }
              > span > .iconText {
                overflow: hidden;
                max-width: 100px;
                > pre {
                  font-size: 13px;
                  color: #71717a;
                  font-weight: 400;
                  white-space: nowrap;
                }
              }
            }
          }

          &:hover {
            .iconText {
              color: @primary-color;
              font-weight: 500;
            }
          }
        }

        .icon-select-arrow {
          transform: rotate(180deg);
          transition: all 0.3s;
          color: #27272a;
        }

        .biz-data-base-choose {
          position: absolute;
          left: 0;
          right: 24px;
          top: 0;
          bottom: 0;
          opacity: 0;
          width: auto;

          > .ant-select-selection {
            height: 40px;
          }
        }

        &.ant-tabs-tab-active {
          background: #ffffff;
          // border: 1px solid #f0f0f0;

          .biz-instance-item {
            .instance-item-tag {
              background: #ffffff;
              .database-image {
                .iconClass {
                  .iconText {
                    color: @primary-color;
                    font-weight: 500;

                    > pre {
                      color: #27272a;
                      font-weight: 600;
                    }
                  }
                }
              }
            }
          }
        }

        .ant-tabs-close-x {
          margin-right: 8px;
          height: 12px;
        }
      }

      .ant-tabs-extra-content {
        height: 40px;
        margin-bottom: -1px;

        .ant-tabs-new-tab {
          width: 40px;
          height: 40px;
          background: #f2f2f2;
          border: none;
          border-left: 1px solid #fff;
        }
      }
    }
    // tab-content
    > .ant-tabs-content {
      display: flex;
      flex-grow: 1;
      > .ant-tabs-tabpane {
        &.ant-tabs-tabpane-active {
          flex-grow: 1;
        }
        &.ant-tabs-tabpane-inactive {
          // width: 0;
          display: none;
        }
      }
    }
  }
  .ps-empty {
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 800px;
    > div {
      display: flex;
      justify-content: center;
      .left-block {
        display: flex;
        flex-direction: column;
        align-items: center;
        .img {
          margin-bottom: 24px;
        }
        .title {
          text-align: center;
          font-size: 24px;
          color: #27272a;
        }
      }
      .middle-block {
        width: 64px;
      }
      .right-block {
        /deep/.ant-steps {
          .ant-steps-item {
            .ant-steps-item-container {
              .ant-steps-item-icon {
                background: #4db5f2;
                .ant-steps-icon {
                  color: #fff;
                }
              }
              .ant-steps-item-content {
                min-height: 125px;
                display: flex;
                overflow: visible;
                .ant-steps-item-title {
                  font-size: 16px;
                  color: #27272a;
                }
                .ant-steps-item-description {
                  .ant-btn {
                    // height: 24px;
                    // line-height: 24px;
                    position: relative;
                    top: -4px;
                    > span {
                      padding: 0;
                      font-size: 14px;
                      color: #ffffff;
                      font-weight: 600;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>