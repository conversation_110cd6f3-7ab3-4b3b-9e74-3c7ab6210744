<template>
  <div class="page-list-content">
    <!-- 按钮和搜索 -->
    <div class="operate-part">
      <div class="btns" v-if="btns && btns.length > 0">
        <a-button
          v-for="item in btns"
          :key="item.label"
          :class="`btn-${item.color || 'blue'}`"
          @click="onBtnClick(item)"
        >{{item.label}}</a-button>
      </div>
      <div class="search">
        <a-form-model
          layout="inline"
          :model="searchData"
          v-if="searchArray && searchArray.length > 0"
        >
          <template v-for="item in searchArray">
            <a-form-model-item :key="item.key" v-if="item.compInfo && item.visible != false">
              <a-tooltip
                overlayClassName="tooltip-weak"
                placement="bottomRight"
                :mouseEnterDelay="0.5"
              >
                <template slot="title">{{item.placeholder}}</template>
                <component
                  v-model="searchData[item.key]"
                  v-bind="item.props || {}"
                  :is="item.compInfo.compName"
                  :placeholder="item.placeholder"
                  :style="{width:item.width ? item.width + 'px' : '190px'}"
                  @[getSearchQueryTrigger(item)]="queryList"
                ></component>
              </a-tooltip>
            </a-form-model-item>
          </template>
        </a-form-model>
      </div>
    </div>
    <!-- 表格 -->
    <Table ref="table" v-bind="tableParams || {}" :reqParams="reqParams">
      <template v-for="sItem in tableScopedSlots" v-slot:[sItem]="{ text, record, index, column }">
        <slot :name="sItem" v-bind="{text, record, index, column}"></slot>
      </template>
      <template v-for="item in tableSlots" v-slot:[item]>
        <slot :name="item"></slot>
      </template>
    </Table>
  </div>
</template>

<script>
import Table from '@/components/Table';
import Select from '@/components/Select';
import common from '@/utils/common';
import _ from 'lodash';

const searchMap = {
  Input: {
    compName: 'a-input-search',
    trigger: 'search'
  },
  Select: {
    compName: 'Select',
    trigger: 'change'
  }
};

export default {
  components: { Table, Select },
  props: {
    tableParams: {
      type: Object,
      default: function() {
        return {};
      }
    },
    btns: {
      type: Array,
      default: function() {
        return [];
      }
    },
    searches: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    const { columns = [], reqParams = {} } = this.tableParams;
    return {
      ...common.getTableSlots(columns),
      reqParams,
      searchArray: this.searches.map(item => {
        item.compInfo = searchMap[item.type];
        return item;
      }),
      searchData: {}
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    // 监听按钮点击
    onBtnClick(item = {}) {
      const { callback: cbk } = item;
      cbk && cbk(this);
    },
    // 带条件查询
    queryList(value) {
      console.log(value, JSON.stringify(this.searchData));
      // 1、通过属性触发table查询请求，必须深拷贝
      this.reqParams = _.merge({}, this.reqParams, this.searchData);
      // 2、直接ref获取table实例处理...
    },
    // 获取search组件查询事件名
    getSearchQueryTrigger(item) {
      return item.compInfo ? item.compInfo.trigger : '';
    }
  }
};
</script>

<style lang="less" scoped>
.page-list-content {
  .operate-part {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;

    .search {
      padding-top: 4px;
      /deep/ .ant-form-inline .ant-form-item {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
