#layout-root.top-bottom-new {
  // 外框间距变化，关联变化
  @layout-padding-top: 60px;
  @layout-padding: 24px;
  @btn-height: 32px;
  .frame-button-wrapper,
  .frame-button-wrapper-relative,
  .frame-button-wrapper-relative-blank {
    display: flex;
    > .ant-btn {
      height: @btn-height;
      font-size: 14px;
      font-weight: 600;
      margin-right: 0;
      margin-left: 8px;
      font-family: PingFangSC-Regular;
      &.highlight {
        color: #008adc;
        border-color: #7fc4ed;
        &:hover {
          color: @primary-5;
          border-color: #008adc;
        }
      }

      // &.ant-btn-primary {
      //   background: #008adc;
      //   color: #ffffff;
      //   &:hover {
      //     background: #219be3;
      //     color: #ffffff;
      //   }
      // }
    }
  }
  .frame-button-wrapper {
    position: absolute;
    right: @layout-padding;
    top: (60px - @btn-height) / 2;
    z-index: 10;
    background: transparent;
  }
  .frame-button-wrapper-relative {
    position: absolute;
    right: -24px;
    top: -(@btn-height + 14px) - 24px;
    z-index: 10;
    background: transparent;
  }
  .frame-button-wrapper-relative-blank {
    position: absolute;
    right: 0px;
    top: -(@btn-height + 14px);
    z-index: 10;
    background: transparent;
  }

  // 默认样式覆盖
  button.ant-btn {
    // border-radius: 8px;
  }
  // 高亮按钮(不需要type)
  // .frame-button-wrapper > .ant-btn {
  //   font-family: PingFangSC-Regular;
  //   font-size: 14px;
  //   font-weight: 400;
  //   &.highlight {
  //     color: #008adc;
  //     border-color: #7fc4ed !important;
  //     &:hover {
  //       color: @primary-5;
  //       border-color: #008adc;
  //     }
  //   }
  // }
}
