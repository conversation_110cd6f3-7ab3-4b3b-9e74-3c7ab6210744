import CodeMirror from 'codemirror';
import hintOptionsUtil from './hintOptionsUtil';
import { getDbData } from '@/api/sqlresolver';

const regEmpty = (str) => {
  return /^\s+$/.test(str)
}
const regSchema = (str) => {
  return str.replace(/\n/g, ' ').trim().match(/^[^.]+[\s]+from[\s]+([^.\s]+)\.$/i);
}
const regTable = (str) => {
  return str.replace(/\n/g, ' ').trim().match(/(FROM|UPDATE|INTO)$/i);
}
const regColumn = (str) => {
  return str.replace(/\n/g, ' ').trim().match(/^[^.]+[\s]+from[\s]+([^.\s]+)[\s]+where$/i);
}

// 获取表，字段等数据
const reqGetDbData = (ctx, params = {}, getCancel) => {
  return new Promise((resolve, reject) => {
    getDbData(params, getCancel)
      .then(res => {
        if (CommonUtil.isSuccessCode(res)) {
          resolve(_.get(res, 'data.data'));
        } else {
          // ctx.$hideLoading({
          //   method: 'error',
          //   tips: _.get(res, 'data.message'),
          //   response: res
          // });
          reject(new Error(''));
        }
      })
      .catch(e => {
        console.error(e);
        ctx.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        reject(new Error(''));
      });
  })
}

// 获取联想options
const getThinkOptions = (ctx, cm, cur, from) => {
  let res = null;
  const preToken = cm.getTokenAt(from);
  let preHintToken = preToken;
  if (!preToken.string) {
    if (cur.line > 0) {
      // 往上一行找
      const preLineTokens = cm.getLineTokens(cur.line - 1);
      preHintToken = _.last(preLineTokens.filter(item => !regEmpty(item.string)))
    }
  }
  // console.log(preHintToken, 'preHintToken');
  const preTokenString = preHintToken.string;
  if (preTokenString && !regEmpty(preTokenString)) {
    // 联想
    const options = hintOptionsUtil.getHintOptions(preTokenString);
    console.log(options, '联想');
    if (options) {
      res = options.map(item => {
        return {
          ...item,
          render: (Element, self, data) => {
            // console.log(Element, self, data);
            // console.log(dom, 'dfjdkjfkdjfkdjfk')
            LAYOUT_APP.getCustomDom(
              _.uniqueId(data.text === '*' ? 'anyone' : data.text),
              // Math.random().toString(36).substr(2)
              (h) => (
                <div class="sqlresolver-hint-item-wrapper">
                  <custom-icon type="lu-icon-idea" style="margin-right: 2px;" />
                  <span>{data.text}</span>
                </div>
              ),
              (dom) => {
                dom && Element.appendChild(dom);
              }
            );
          }
        }
      });
    }
  }
  res = [...(res || []), ...(ctx.asyncOptions || [])];
  // 最多显示40条
  if (res.length > 40) {
    res = res.slice(0, 40);
    res.push({
      text: '*最多显示40条',
      hint: (cm, self, data) => { },
      render: (Element, self, data) => {
        LAYOUT_APP.getCustomDom(
          data.text,
          (h) => (
            <div style="color:rgba(0,0,0,0.45)">
              <span>{data.text}</span>
            </div>
          ),
          (dom) => {
            dom && Element.appendChild(dom);
          }
        );
      }
    })
  }
  return _.isEmpty(res) ? null : res;
}

// 清空异步请求
const clearAsync = (ctx) => {
  if (ctx.hintCancelArr) {
    ctx.hintCancelArr.forEach(cancel => {
      cancel();
    });
    ctx.hintCancelArr = [];
  }
}

// 获取loading选项
const getLoadingOption = (ctx, from, to) => {
  return {
    list: [
      {
        text: 'LOADING',
        hint: (cm, self, data) => { },
        render: (Element, self, data) => {
          LAYOUT_APP.getCustomDom(
            data.text,
            (h) => (
              <div class="sqlresolver-hint-item-loading">
                <custom-icon type="loading" />
                <span>{data.text}</span>
              </div>
            ),
            (dom) => {
              dom && Element.appendChild(dom);
            }
          );
        }
      }
    ],
    from: to,
    to: to
  }
}

// 缓存hint值
const setHintCache = (ctx, matchKey, completion, datas = []) => {
  const instanceId = _.get(ctx.instanceItem, 'value');
  const schemaName = ctx.fixedDatabase.name;
  const cacheArr = _.get(ctx.asyncCache, `${instanceId}.${schemaName}`);
  const cacheValue = datas.find(item => item && item[matchKey] === completion);

  if (cacheValue) {
    if (cacheArr) {
      cacheArr.push(cacheValue)
    } else {
      _.set(ctx.asyncCache, `${instanceId}.${schemaName}`, [cacheValue]);
    }
  }
  // console.log(ctx.asyncCache, 'asyncCache')
}
// 获取缓存值
const getHintCache = (ctx, matchKey, completion) => {
  const instanceId = _.get(ctx.instanceItem, 'value');
  const schemaName = ctx.fixedDatabase.name;
  const cacheArr = _.get(ctx.asyncCache, `${instanceId}.${schemaName}`) || [];

  return cacheArr.find(item => item && item[matchKey] === completion);
}

const HintTool = function (ctx) {
  // 异步Options
  ctx.asyncOptions = null;
  // 异步缓存
  ctx.asyncCache = {};
  // 异步标志
  ctx.asyncFlag = false;
  // 请求取消集合
  ctx.hintCancelArr = [];
  return {
    async getHintList(cm, options) {
      let cur = cm.getCursor(),
        token = cm.getTokenAt(cur);
      let term = token.string,
        from = CodeMirror.Pos(cur.line, token.start),
        to = cur;
      let res = { list: [], from: to, to: to };
      const isDot = token.string === '.';
      // console.log(token, cm.getLineTokens(cur.line), 'token')

      if (!regEmpty(token.string) && !isDot) { // 输入非空格、非.
        const preToken = cm.getTokenAt(from);
        let thinkOptions = null;
        if (regEmpty(preToken.string) || /^\.[^.]+/.test(token.string)) {
          thinkOptions = getThinkOptions(ctx, cm, cur, CodeMirror.Pos(cur.line, preToken.start));
        }
        // 匹配
        const { list: defaultList } = CodeMirror.hint.sql(cm, options);
        let list = thinkOptions || defaultList;
        let words = list.map(item => item.text);

        let found = [];
        for (let i = 0; i < words.length; i++) {
          let word = words[i];
          if (
            // word.slice(0, term.length).toUpperCase() == term.toUpperCase()
            word.toUpperCase().includes(term.replace('.', '').toUpperCase())
          ) {
            found.push(list[i]);
          }
        }

        // console.log(term, cur, found, '匹配')

        res = { list: found, from: from, to: to };
      } else { // 输入空格
        let thinkOptions = getThinkOptions(ctx, cm, cur, from);
        if (thinkOptions) {
          res = { list: thinkOptions, from: to, to: to };
        }
        // 非异步联想时，清空异步数据
        if (!ctx.asyncFlag) {
          ctx.asyncOptions = null;
        }

        // 判断需要异步
        if (!ctx.asyncOptions) {
          // const preToken = cm.getTokenAt(from);
          const preString = cm.getRange(CodeMirror.Pos(0, 0), from) || '';
          const currString = cm.getRange(CodeMirror.Pos(0, 0), to) || '';
          // schema匹配
          let schemaMatch = regSchema(currString);
          // 表匹配
          let tableMatch = regTable(preString)
          // 字段匹配
          let columnMatch = regColumn(preString);
          const tableCache = getHintCache(ctx, 'name', columnMatch ? columnMatch[1] : '') || {};
          console.log(schemaMatch, tableMatch, columnMatch, tableCache, 'reg匹配值')

          if (schemaMatch) { // 请求schema
            // 清空请求
            clearAsync(ctx);

            // 设置loading
            res = getLoadingOption(ctx, from, to);

            // 请求
            reqGetDbData(ctx, {
              instance_id: _.get(ctx.instanceItem, 'value'),
              element_type: 'table',
              schema_name: schemaMatch[1]
            }, (cancel) => {
              ctx.hintCancelArr.push(cancel)
            }).then((data) => {
              cm.closeHint();
              ctx.asyncOptions = data.map(item => {
                return {
                  ...item,
                  hint: (cm, self, data) => {
                    const completion = '.' + item.name;
                    cm.replaceRange(completion, from, to, 'complete');
                    CodeMirror.signal(data, 'pick', completion);
                    cm.scrollIntoView();
                  },
                  text: item.name,
                  render: (Element, self, data) => {
                    LAYOUT_APP.getCustomDom(
                      data.text,
                      (h) => (
                        <div class="">
                          <custom-icon type="lu-icon-list" style="margin-right: 2px;" />
                          <span>{data.text}</span>
                        </div>
                      ),
                      (dom) => {
                        dom && Element.appendChild(dom);
                      }
                    );
                  }
                }
              });
              // 异步标识，延迟 50 设置为false，防止被直接清空
              ctx.asyncFlag = true;
              cm.showHint();
              setTimeout(() => {
                ctx.asyncFlag = false;
              }, 50)
            }).catch(e => {
              cm.closeHint();
              ctx.asyncOptions = null;
            })
          } else if (tableMatch) { // 请求表
            // 清空请求
            clearAsync(ctx);

            // 设置loading
            res = getLoadingOption(ctx, from, to);

            // 请求
            reqGetDbData(ctx, {
              instance_id: _.get(ctx.instanceItem, 'value'),
              element_type: 'table',
              schema_name: ctx.fixedDatabase.name
            }, (cancel) => {
              ctx.hintCancelArr.push(cancel)
            }).then((data) => {
              cm.closeHint();
              ctx.asyncOptions = data.map(item => {
                return {
                  ...item,
                  text: item.name,
                  render: (Element, self, data) => {
                    LAYOUT_APP.getCustomDom(
                      data.text,
                      (h) => (
                        <div class="">
                          <custom-icon type="lu-icon-list" style="margin-right: 2px;" />
                          <span>{data.text}</span>
                        </div>
                      ),
                      (dom) => {
                        dom && Element.appendChild(dom);
                      }
                    );
                  }
                }
              });
              // 异步标识，延迟 50 设置为false，防止被直接清空
              ctx.asyncFlag = true;
              cm.showHint();
              setTimeout(() => {
                ctx.asyncFlag = false;
              }, 50)
            }).catch(e => {
              cm.closeHint();
              ctx.asyncOptions = null;
            })
          } else if (columnMatch) { // 请求字段
            // 清空请求
            clearAsync(ctx);

            // 设置loading
            res = getLoadingOption(ctx, from, to);

            // 请求
            reqGetDbData(ctx, {
              instance_id: _.get(ctx.instanceItem, 'value'),
              element_type: 'column',
              schema_name: ctx.fixedDatabase.name,
              table_name: columnMatch[1],
              table_id: tableCache.element_type ? tableCache.id : undefined
            }, (cancel) => {
              ctx.hintCancelArr.push(cancel)
            }).then((data) => {
              cm.closeHint();
              ctx.asyncOptions = data.map(item => {
                return {
                  ...item,
                  text: item.name,
                  render: (Element, self, data) => {
                    LAYOUT_APP.getCustomDom(
                      data.text,
                      (h) => (
                        <div class="">
                          <custom-icon type="lu-icon-field" style="margin-right: 2px;" />
                          <span>{data.text}</span>
                        </div>
                      ),
                      (dom) => {
                        dom && Element.appendChild(dom);
                      }
                    );
                  }
                }
              });
              // 异步标识，延迟 50 设置为false，防止被直接清空
              ctx.asyncFlag = true;
              cm.showHint();
              setTimeout(() => {
                ctx.asyncFlag = false;
              }, 50)
            }).catch(e => {
              cm.closeHint();
              ctx.asyncOptions = null;
            })
          }
        }
      }
      CodeMirror.on(res, 'pick', completion => {
        // console.log(completion, 'pick hint');
        if (completion.text === 'LOADING' || completion.text.startsWith('*最多')) {
          return;
        }
        setHintCache(ctx, 'name', completion.name, [...(ctx.asyncOptions || [])]);
        ctx.asyncOptions = null;
        cm.replaceSelection(' ');
        cm.showHint();
      })

      // CodeMirror.on(res, 'shown', c => {
      // })
      CodeMirror.on(res, 'close', c => {
        LAYOUT_APP.clearCustomDom();
      })

      return res;
    }
  }
}
export default HintTool;