<template>
  <SplitPanel :class="className" v-bind="panelProps" v-on="panelListeners">
    <template v-slot:firstPane>
      <slot name="first"></slot>
    </template>
    <template v-slot:secondPane>
      <slot name="second"></slot>
    </template>
  </SplitPanel>
</template>

<script>
import SplitPanel from './Source/ResizeSplitPane';
/** {
  'allow-resize': { type: Boolean, default: false },//是否可以拖动
  'split-to': { type: String, default: 'columns' }, // columns || rows    分隔成列  ||  分隔成行
  'primary': { type: String, default: 'first' }, // first || second   用于指定两个窗格中哪一个的大小是固定的。
  'size': { type: Number, default: 25 }, // in pixels || percents   固定窗格的初始宽度或高度，是宽度或高度，这取决于窗格的分割方式。
  'units': { type: String, default: 'pixels' }, // pixels || percents  指定单位，默认是像素，也可以是百分比
  'min-size': { type: Number, default: 16 }, // in pixels || percents  固定窗格可拖动到的最小宽度或高度
  'max-size': { type: Number, default: 0 }, // in pixels || percents 固定窗格可拖动到的最大高度或宽度
  'step': { type: Number, default: 0 }, // in pixels only  每次拖动所移动的像素数
  'resizerThickness': { type: Number, default: 2 }, //in px - width of the resizer  分隔线的宽度  必须绑定的形式
  'resizerColor': { type: String, default: '#AAA' }, //  any css color - if you set transparency, it will afect the border too 分割线颜色
  'resizerBorderColor': { type: String, default: 'rgba(0,0,0, 0.15)' }, // any css color - #FFF, rgb(0,0,0), rgba(0,0,0,0)  分割线边框的颜色
  'resizerBorderThickness': { type: Number, default: 3 }, // in px - border that forms the shadow   分隔线边框的宽度或高度
  'resizeHandler': { type: Boolean, default: false } // 是否需要resize handler
} **/

const defaultProps = {
  'allow-resize': true,
  resizerThickness: 6,
  resizerBorderThickness: 0,
  resizerColor: '#F5F5F5'
};

export default {
  props: {},
  components: { SplitPanel },
  data() {
    return {};
  },
  computed: {
    panelProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    panelListeners() {
      return { ...this.$listeners };
    },
    className() {
      return ['custom-split-panel-new'];
    }
  },
  destroyed() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less">
.custom-split-panel-new {
}
</style>
