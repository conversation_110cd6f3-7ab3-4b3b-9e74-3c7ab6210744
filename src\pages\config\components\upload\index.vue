<template>
  <div class="upload-comp">
    <div class="download-box">
      <div>
        请先下载{{ confItem.name }}信息模板，填写{{
          confItem.name
        }}信息后，上传文件进行导入。
      </div>
      <div>
        <a @click="download">{{ confItem.name }}信息上传模板.xlsx</a>
        <a-button @click="download">下载</a-button>
      </div>
    </div>
    <div class="upload-box">
      <div>{{ confItem.name }}信息上传</div>
      <a-upload-dragger
        name="file"
        :before-upload="beforeUpload"
        :remove="() => (this.fileList = [])"
        :file-list="fileList"
      >
        <p class="ant-upload-drag-icon">
          <a-icon type="plus" />
        </p>
        <p class="ant-upload-text">
          将文件拖拽到此处，或
          <span class="tips">点击上传</span>
        </p>
        <p class="ant-upload-text">支持.xlsx文件格式，单个文件大小小于2M</p>
      </a-upload-dragger>
    </div>
    <div class="button-box">
      <!-- <a-button @click="hide">清空</a-button> -->
      <a-button @click="submit" type="primary" class="sql-btn">提交</a-button>
    </div>
  </div>
</template>
<script>
import { downloadProjectTemplate, uploadProject } from '@/api/config/project';
import { downloadProjectGroupTemplate, uploadProjectGroup } from '@/api/config/projectGroup';
import {
  downloadDataSourceTemplate,
  uploadDataSource
} from '@/api/config/dataSource';
import common from '@/utils/common';

export const ConfMap = {
  project: {
    name: '项目',
    downloadTempInterface: downloadProjectTemplate,
    uploadInterface: uploadProject,
    route: 'project-config'
  },
  projectGroup: {
    name: '项目组',
    downloadTempInterface: downloadProjectGroupTemplate,
    uploadInterface: uploadProjectGroup,
    route: 'project-group-config'
  },
  instance: {
    name: '数据源',
    downloadTempInterface: downloadDataSourceTemplate,
    uploadInterface: uploadDataSource,
    route: 'data-source'
  }
};
export default {
  components: {},
  props: {
    type: String
  },
  data() {
    return {
      confItem: ConfMap[this.type],
      fileList: []
    };
  },
  mounted() {},
  created() {},
  methods: {
    download() {
      this.$showLoading({
        tips: `下载中...`
      });
      const req = this.confItem.downloadTempInterface;
      req()
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 上传之前
    beforeUpload(file) {
      const maxSize = 2 * 1024 * 1024; // byte
      if (file.size > maxSize) {
        this.$message.error('文件大小错误，文件大小不超过2MB');
        return;
      }
      if (!/\.(xlsx)$/.test(file.name)) {
        this.$message.error('文件格式错误，文件类型支持.xlsx');
        return;
      }
      this.$set(this.fileList, 0, file);
      return false;
    },
    // 提交
    submit() {
      if (this.fileList.length === 0) {
        this.$message.warning('请上传文件');
        return;
      }
      this.$emit('submitProgress', 50);
      this.formData = new FormData();
      this.$emit('submitProgress');
      this.formData.append('file', this.fileList[0]);
      this.$emit('submitProgress', 75);

      this.$showLoading({
        tips: `正在导入，请稍后...`
      });
      const req = this.confItem.uploadInterface;
      req(this.formData)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data');
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.$emit('submitProgress', 100);
            this.$emit('onChange', 1, resData);
          } else {
            this.$emit('submitProgress', 0);
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          }
        })
        .catch(e => {
          this.$emit('submitProgress', 0);
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 清空
    hide() {
      this.fileList = [];
    }
  }
};
</script>

<style lang="less" scoped>
.upload-comp {
  // padding: 24px;
  .download-box {
    padding: 24px 24px 0 24px;
    margin: 24px;
    background: rgb(229, 246, 254);
    > div {
      padding-bottom: 24px;
      > a {
        margin-right: 12px;
      }
    }
  }
  .upload-box {
    margin: 24px;
    height: 320px;
    > div {
      padding-bottom: 12px;
    }
  }
  .button-box {
    margin: 72px 24px 0 0;
    text-align: right;
    .sql-btn {
      margin-left: 10px;
    }
  }
}
</style>
