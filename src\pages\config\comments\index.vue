<template>
  <div class="white-list-page page-list-single">
    <!-- <a-card style="width:100%" class="common-page-card" title="意见配置" :bordered="false"> -->
    <div class="frame-button-wrapper">
      <a-button slot="extra" class="highlight" icon="plus" @click="addComments">添加意见</a-button>
    </div>
    <!-- <SearchArea v-bind="searchParams" :needCache="true" @reset="reset" @search="search"></SearchArea> -->
    <Table ref="table" v-bind="tableParams || {}" class="new-view-table small-size">
      <LimitLabel slot="desc" slot-scope="{text}" :label="text" :limit="20"></LimitLabel>
      <!-- <template v-slot:action="{ text, record }"> -->
      <custom-btns-wrapper slot="action" slot-scope="{ text, record }">
        <a @click="edit(record)" actionBtn>编辑</a>
        <!-- <a-divider type="vertical" /> -->
        <a-popconfirm title="确定删除此意见配置?" @confirm="() => removeComments(record)" actionBtn>
          <a class="remove">删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
      <!-- </template> -->
    </Table>
    <!-- </a-card> -->
    <!-- 新建项目弹窗 -->
    <AddModal ref="addModal" :comments="currentComments" @save="saveComments"></AddModal>
  </div>
</template>

<script>
import Table from '@/components/Table';
import SearchArea from '@/components/SearchArea';
import AddModal from './components/AddModal';
import LimitLabel from '@/components/LimitLabel';
import config from './config';

import {
  addComments,
  deleteComments,
  editComments
} from '@/api/config/comments';
export default {
  components: {
    Table,
    AddModal,
    SearchArea,
    LimitLabel
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: '/sqlreview/comment-config',
        reqParams: {},
        isInitReq: false,
        needCache: true,
        needTools: true,
        needSearchArea: true,
        searchFields: this.config.searchFields,
        columns: this.config.columns,
        rowKey: 'id'
      },
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 3
      },
      currentComments: {},
      isEdit: false
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    removeComments(record) {
      deleteComments({
        id: record.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const { table } = this.$refs;
            this.$hideLoading({ tips: '删除成功' });
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    edit(record) {
      this.currentComments = { ...record };
      const { addModal } = this.$refs;
      addModal.show({ type: 'edit' });
      this.isEdit = true;
    },
    addComments() {
      const { addModal } = this.$refs;
      this.currentComments = {};
      this.isEdit = false;
      addModal.show();
    },
    saveComments(data) {
      const { addModal, table } = this.$refs;
      // 请求
      this.$showLoading();
      const requestApi = this.isEdit ? editComments : addComments;
      requestApi({
        ...data
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({
              tips: `${this.isEdit ? '编辑' : '新建'}评审意见成功`
            });
            addModal.hide();
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.refresh();
    }
  }
};
</script>

<style scoped lang="scss">
</style>
