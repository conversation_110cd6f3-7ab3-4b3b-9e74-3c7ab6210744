.pageInfo {
  opacity: 0.68;
  padding-left: 16px;
}
/deep/ .ant-dropdown-trigger[disabled] {
  opacity: 0.4;
}

.review-notice {
  h3 {
    i {
      margin-right: 8px;
    }
  }
  &.review-notice-fail {
    li,
    h3 {
      color: rgb(255 83 84);
    }
    background: rgba(255, 83, 84, 0.08) !important;
  }
  &.review-notice-pass {
    li,
    h3 {
      color: #23be6c;
      opacity: 0.8;
    }
    background: rgba(35, 190, 108, 0.08) !important;
    li {
      &:hover {
        /deep/ i {
          display: none;
        }
      }
    }
  }
  ul {
    list-style: circle;
    padding-left: 24px;
    li {
      line-height: 25px;
      opacity: 0.8;
      &:hover {
        opacity: 1;

        /deep/ i {
          display: inline-block;
        }
      }
      /deep/ i {
        display: none;
        margin-left: 32px;
        cursor: pointer;
        &:hover {
          color: #fb8283;
        }
      }
    }
  }
}
.home-review-wraper {
  color: rgba(86, 87, 89, 1);
  /deep/ .review-agree {
    border-color: rgba(35, 190, 108, 1);
    background: rgba(35, 190, 108, 1);
    color: #fff;
  }
  /deep/ .review-disagree {
    border-color: rgba(255, 83, 84, 1);
    background: rgba(255, 83, 84, 1);
    color: #fff;
  }
  /deep/ .review-filter {
    border-color: rgba(15, 120, 251, 1);
    background: rgba(15, 120, 251, 1);
    color: #fff;
  }
  /deep/ .anticon-robot,
  .anticon-read,
  .anticon-like,
  .anticon-exception {
    color: #1890ff;
  }
  h4 {
    color: #4e5054;
    font-size: 14px;
    line-height: 20px;
  }
  .review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .card-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      .title-project {
        margin-left: 40px;
      }
      .title-project-name {
        display: inline-block;
        padding: 0 24px;
        color: @primary-color;
        font-size: 12px;
        border: 1px solid @primary-color;
        border-radius: 24px;
        line-height: 24px;
        margin-left: 12px;
        &::before {
          content: '';
          display: inline-block;
          position: relative;
          left: -8px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: @primary-color;
        }
      }
      .file_path {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .review-header,
  .ant-spin-container > * {
    background: #fff;
    padding: 16px 24px;
    margin-bottom: 16px;

    &.review-detail-box {
      padding: 0;
      padding-bottom: 0;
      position: relative;

      .review-detail-rt-btns {
        position: absolute;
        right: 24px;
        top: 16px;
      }
    }
  }
  .ant-spin-container > .review-detail-box > .ant-tabs-top {
    padding-top: 8px;
    margin-bottom: 0;
    padding-bottom: 0;
  }
  /deep/ .ant-tabs-bar {
    padding-left: 24px;
    padding-right: 24px;
  }
  /deep/ .ant-tag {
    margin-left: 8px;
  }
  /deep/ .ant-card-head-title {
    padding-top: 0;
    padding-bottom: 8px;
  }
  /deep/ .ant-tabs-nav .ant-tabs-tab {
    padding: 12px 0;
  }
  /deep/ .ant-card-head {
    padding: 0;
    min-height: 32px;
  }
  /deep/ .ant-card-body {
    padding: 24px 0;
    padding-bottom: 0;
  }
  /deep/ textarea.review-advice {
    background-color: rgba(15, 120, 251, 0.06);
    border: 1px solid transparent;
  }

  pre {
    border-radius: 2px;
    padding: 16px;
    background-color: rgba(125, 125, 125, 0.1);
    color: #0f78fb;
    margin-bottom: 0;
  }
  .tab-content {
    background: #f5f5f5;
    margin-top: -16px;
    min-height: 100px;
    padding: 24px;
  }
  // 111111111111111111
  .review-wraper {
    background: #fff;
    border-radius: 3px;
    .tag-info {
      padding: 8px;
      background-color: rgba(15, 120, 251, 0.06);
      span {
        background: rgba(15, 120, 251, 0.14);
        line-height: 20px;
        padding: 4px 14px;
        border-radius: 12px;
        color: rgba(15, 120, 251, 1);
        display: inline-block;
        position: relative;
        padding-left: 20px;
        &:before {
          content: ' ';
          height: 8px;
          width: 8px;
          border-radius: 4px;
          position: absolute;
          display: block;
          top: 50%;
          margin-top: -4px;
          left: 8px;
          background: rgba(15, 120, 251, 1);
        }
      }
    }
    .review-diff-wraper {
      width: 100%;
      overflow: hidden;
      .tag-info {
        width: calc(50% - 20px);
        float: left;
        border-radius: 5px;
        &:last-child {
          margin-left: 40px;
          background: rgba(35, 190, 108, 0.06);
          > span {
            background: rgba(35, 190, 108, 0.39);
            color: #23be6c;
            &:before {
              background: #23be6c;
            }
          }
        }
      }
    }
    .redo {
      pre {
        padding: 16px;
        background: rgba(101, 105, 109, 0.05);
        line-height: 25px;
        color: rgba(101, 105, 109, 1);
        .hljs {
          background: transparent !important;
        }
      }
    }
  }
}
.sava-button {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.review-wraper2 {
  background: #fff;
  margin-bottom: 16px;
  margin-top: 28px;
  border-radius: 3px;
  position: relative;
  padding: 10px;
}
.sql-detail-item {
  .sql-detail-compare {
    /deep/ .custom-code-mirror-diff {
      display: flex;
      align-items: stretch;
      .ccm-diff-title {
        display: flex;
        flex-direction: column;
      }
    }
  }
}
.part-2 {
  // height: 48px;
  height: auto;
  overflow: hidden;
  margin-top: 12px;
  &.expanded {
    .icon-sql-expand {
      transform: rotate(270deg);
    }
  }
  > h4 {
    position: relative;
    cursor: pointer;

    &:hover {
      .icon-sql-expand {
        color: @primary-color;
      }
    }
    .icon-sql-expand {
      position: absolute;
      right: 8px;
      top: 0;
      transform: rotate(90deg);
    }
  }
}
.item-suggest-info {
  margin-bottom: 5px;
  .suggest-text {
    margin-bottom: 0;
    color: #0f78fb;
  }
}
.sqlmap-params {
  height: 56px;
  overflow: hidden;
  padding-bottom: 24px !important;

  /deep/ .ant-card-head {
    position: relative;
    min-height: 40px;
  }

  /deep/ .ant-card-extra {
    padding: 0 0 8px 0;

    > div {
      position: absolute;
      left: -24px;
      right: -24px;
      top: -16px;
      bottom: 0;
      text-align: right;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 24px;
      cursor: pointer;
    }

    .sqlmap-params-expand-icon {
      margin-right: 20px;
      transform: rotate(90deg);

      &:hover {
        color: @primary-color;
      }
      &.expanded {
        transform: rotate(270deg);
      }
    }
  }

  /deep/ .ant-form {
    .ant-form-item {
      display: flex !important;
      margin-bottom: 4px;

      .ant-form-item-label {
        width: 200px;
        flex-shrink: 0;
        padding-right: 8px;
      }
      .ant-form-item-control-wrapper {
        flex-grow: 1;

        /deep/ .table-edit,
        textarea {
          margin-top: 8px;
        }
      }
    }
  }
  .content {
    display: flex;
    justify-content: space-between;
    .dba-name {
      padding-top: 10px;
    }
  }
}
.sql-detail-item-info {
  color: #0f78fb;
  margin-left: 10px;
  &:nth-child(1) {
    margin-left: 0;
  }
}
.ai-comment-part {
  padding: 16px;
  background-color: #edf5ff;
  border-radius: 5px;
}
