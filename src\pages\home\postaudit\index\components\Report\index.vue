<template>
  <a-drawer
    title="问题总览"
    :width="'70%'"
    wrapClassName="sqlreview-report-drawer"
    :visible="visible"
    @close="onCancel"
  >
    <!-- <div
      :style="{ position: 'absolute', top: 0, right: 0, marginRight: '24px', height: '56px', display: 'flex', alignItems: 'center' }"
    >
      <a-button type="primary">下载PDF</a-button>
    </div>-->
    <a-spin style="position:relative;" :spinning="loading">
      <!-- 内容区域 -->
      <div class="srd-content-title">问题分类</div>
      <a-row>
        <a-col class="custom-bg" :span="13">
          <Chart :option="pieOption" />
        </a-col>
        <a-col style="padding:16px;" class="custom-bg" :span="10">
          <Table v-bind="tableParams" :dataSource="tableData"></Table>
        </a-col>
      </a-row>
      <div style="margin-top: 16px;padding-bottom: 32px;" class="srd-content-title">规则触发排行</div>
      <a-row>
        <div class="srd-rule-tab">
          <a :class="{ active: ruleType === 'SQL' }" @click="onChange('SQL')">SQL</a>
          <a-divider type="vertical" />
          <a :class="{ active: ruleType === 'RULE' }" @click="onChange('RULE')">规则</a>
        </div>
        <a-col class="custom-bg" style="height: 400px;" :span="13">
          <Chart :option="barOption" ref="bar" />
        </a-col>
        <a-col style="padding:16px;" class="custom-bg" :span="10">
          <div v-if="ruleType === 'SQL'">
            <div class="srd-rule-item-detail">
              <span>SQL ID：</span>
              <span>{{sqlDetail.name}}</span>
              <a-popover>
                <template slot="content">
                  <pre>{{sqlDetail.sql_text}}</pre>
                </template>
                <a-icon style="marginLeft:4px" type="question-circle" />
              </a-popover>
            </div>
            <div class="srd-rule-item-detail">
              <div>触发规则：</div>
              <ol>
                <li v-for="(item, index) in sqlDetail.detail" :key="index">{{item}}</li>
              </ol>
            </div>
            <div class="srd-rule-item-detail">
              <div>cost指标：{{sqlDetail.cost || 0}}</div>
            </div>
          </div>
          <div v-else>
            <div class="srd-rule-item-detail">
              <span>规则名：</span>
              <span>{{sqlDetail.name}}</span>
            </div>
            <div class="srd-rule-item-detail">
              <div>规则详情：</div>
              <ol>
                <li v-for="(item, index) in sqlDetail.detail" :key="index">{{item}}</li>
              </ol>
            </div>
            <div class="srd-rule-item-detail">
              <div>触发次数：{{sqlDetail.count || 0}}</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-spin>
  </a-drawer>
</template>

<script>
import config from './config';
import Chart from '@/components/Chart';
import Table from '@/components/Table';
import { getReviewDetailReport } from '@/api/home';

export default {
  components: { Chart, Table },
  props: {},
  data() {
    this.config = config(this);
    this.sqlCategoryData = [];
    this.ruleCategoryData = [];
    return {
      visible: false,
      loading: false,
      pieOption: null,
      tableParams: {
        columns: this.config.columns,
        pagination: false,
        rowKey: 'name'
      },
      tableData: [],
      ruleType: 'SQL',
      barOption: null,
      sqlDetail: {}
    };
  },
  mounted() {},
  created() {},
  computed: {},
  watch: {},
  methods: {
    show(record) {
      this.visible = true;
      this.ruleType = 'SQL';

      // 发起请求
      this.loading = true;
      ReqUtil.req({
        ctx: this,
        reqInstance: getReviewDetailReport,
        params: {
          review_id: record.id
        },
        needLoading: false,
        cbk: data => {
          this.loading = false;
          const {
            count_type: countType = [],
            sql_category: sqlCategory = [],
            rule_category: ruleCategory = []
          } = data || {};

          // 设置pie和table
          let countTypeData = countType.sort((a, b) => a.number - b.number);
          this.pieOption = this.config.pieOption(countTypeData);
          this.tableData = countTypeData;

          // 设置bar
          let sqlCategoryData = sqlCategory
            .filter(item => item.count > 0)
            .slice(0, 8);
          let ruleCategoryData = ruleCategory
            .filter(item => item.count > 0)
            .slice(0, 8);
          // 缓存
          this.sqlCategoryData = sqlCategoryData;
          this.ruleCategoryData = ruleCategoryData;
          // 设置
          this.setBarOption({ current: 0 });
          // 绑定事件
          this.bindEvent();
        },
        err: res => {
          this.sqlCategoryData = [];
          this.ruleCategoryData = [];
          this.tableData = [];
          this.pieOption = this.config.pieOption(this.tableData);
          this.setBarOption({ current: 0 });
        }
      })
        .then(() => {
          this.$hideLoading({ duration: 0 });
          this.loading = false;
        })
        .catch(e => {
          this.loading = false;
        });
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onChange(e) {
      this.ruleType = e;
      this.setBarOption({ current: 0 });
    },
    setBarOption(params = {}) {
      const { current } = params;
      const { ruleType } = this;
      const data =
        ruleType === 'SQL' ? this.sqlCategoryData : this.ruleCategoryData;
      this.barOption = this.config.barOption({
        current,
        data: data,
        ruleType
      });
      this.sqlDetail = data[current] || {};
    },
    bindEvent() {
      const { ruleType } = this;
      const data =
        ruleType === 'SQL' ? this.sqlCategoryData : this.ruleCategoryData;
      if (data.length <= 0) return;

      setTimeout(() => {
        const { bar } = this.$refs;
        bar &&
          bar.on('click', e => {
            this.setBarOption({ current: e.dataIndex });
          });
      }, 300);
    }
  }
};
</script>

<style lang="less">
.sqlreview-report-drawer {
  .ant-drawer-content-wrapper {
    min-width: 960px;
  }
  .srd-content-title {
    padding: 0 0 16px 0;
  }
  .ant-row {
    display: flex;
    align-items: stretch;
    justify-content: space-between;

    .ant-col {
      position: relative;
      min-height: 320px;
    }

    .srd-rule-tab {
      position: absolute;
      top: -24px;
      left: 16px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      a {
        color: #a7a7a7;
        &:hover {
          color: @primary-color;
        }
        &.active {
          font-weight: bold;
          color: @primary-color;
        }
      }
    }

    .srd-rule-item-detail {
      margin-bottom: 8px;

      > div:first-child,
      > span:first-child {
        font-weight: bold;
      }

      ol {
        padding: 0 16px 0 32px;
      }
      li {
        // list-style: none;
        margin-top: 8px;
      }
    }
  }
}
</style>
