<template>
  <div>
    <Prettier :value="sql" />
    <Prettier :value="xml" type="xml" />
    <Prettier :value="json" type="json" />
  </div>
</template>

<script>
import Prettier from '@/components/Prettier';

export default {
  components: { Prettier },
  props: {},
  data() {
    // this.config = config(this);
    return {
      sql: 'select * from tb1;',
      xml: `<div style='color:red;'><div>xxxx</div><div><div></div></div></div>`,
      json: JSON.stringify({
        bool: true,
        null: null,
        number: 123,
        arr: [1, 2, 3],
        obj: { a: 'sss' },
        str: 'kdjfkdjfkdjfdkfdf'
      })
    };
  },
  mounted() {},
  created() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
</style>