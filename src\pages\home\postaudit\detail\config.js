export default function (ctx) {
  const columns = [
    {
      title: '生成时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150
    },
    {
      title: '执行时间范围',
      key: 'time_range',
      dataIndex: 'time_range',
      width: 200
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      scopedSlots: { customRender: 'status' },
      width: 100
    },
    {
      title: 'SQL总数量',
      key: 'sql_count',
      dataIndex: 'sql_count',
      width: 100
    },
    {
      title: 'SQL通过率',
      key: 'passing_rate',
      dataIndex: 'passing_rate',
      width: 100
    },
    {
      title: '风险SQL统计',
      key: 'level_category',
      dataIndex: 'level_category',
      scopedSlots: { customRender: 'level_category' },
      width: 200
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 100
    }
  ];
  const searchFields = [
    {
      type: 'RangePicker',
      label: '时间范围选择',
      key: 'created_at',
      mainSearch: true,
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    }
    // {
    //   type: 'Select',
    //   label: '数据源选择',
    //   key: 'data_source',
    //   props: {
    //     url: '/sqlreview/project/data_source_choices',
    //     reqParams: {}
    //   }
    // },
    // {
    //   type: 'Select',
    //   label: '环境选择',
    //   key: 'env',
    //   props: {
    //     options: [
    //       {
    //         label: '生产',
    //         value: 'prod'
    //       },
    //       {
    //         label: '测试',
    //         value: 'test'
    //       }
    //     ]
    //   }
    // },
    // {
    //   type: 'Input',
    //   label: '创建人',
    //   key: 'created_by'
    // }
  ];
  return {
    columns,
    searchFields
  };
}
