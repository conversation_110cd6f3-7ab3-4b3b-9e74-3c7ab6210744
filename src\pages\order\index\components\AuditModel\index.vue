<template>
  <a-modal
    v-model="visible"
    title="审核"
    @ok="save"
    @cancel="onCancel"
    width="45%"
    :dialogStyle="{ 'minWidth': '600px', 'maxWidth': '800px' }"
    wrapClassName="order-list-audit-modal"
  >
    <div class="form-box">
      <Form v-bind="params" ref="form" :formData="formData"></Form>
    </div>
  </a-modal>
</template>
<script>
import Form from '@/components/Form';
import config from './config';

export default {
  props: {},
  components: { Form },
  data() {
    this.config = config(this);
    return {
      visible: false,
      formData: {},
      params: {
        fields: this.config.fields,
        fixedLabel: true,
        layout: 'vertical',
        multiCols: 1,
        colon: true,
        labelCol: { span: 16 },
        wrapperCol: { span: 24 }
      }
    };
  },
  methods: {
    show(data = {}) {
      this.visible = true;
    },
    onCancel() {
      this.visible = false;
    },
    save() {
      this.$showLoading();
      const { form } = this.$refs;
      const data = form.getData();
      form.validate(valid => {
        if (valid) {
        }
      });
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>