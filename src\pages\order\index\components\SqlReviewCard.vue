<template>
  <div class="card-table">
    <div class="card-table-main-info">
      <div class="left-block">
        <div class="info-box">
          <span class="project-name" @click="toDetail(cardData, $event)">
            <span v-if="cardData.project_name.length <= 16">{{
              cardData.project_name
            }}</span>
            <LimitLabel
              :label="cardData.project_name || ''"
              mode="ellipsis"
              :noWrap="true"
              v-else
            ></LimitLabel>
          </span>
          <div class="status-box">
            <span class="review-type">
              <span>Review方式</span>
              <a-tag
                color="rgba(91,147,255,0.15)"
                style="color: #214ac0; border-radius: 4px"
                >{{ cardData.mode == 0 ? '全量' : '增量' }}</a-tag
              >
            </span>
            <span class="dba-status">
              <a-tooltip>
                <template slot="title" v-if="cardData.operator_dba">
                  <span>{{ cardData.operator_dba }}</span>
                </template>
                <span v-if="cardData.dba_status == '待评审'">审核人</span>
                <span v-else>审核人({{ cardData.ch_dba || '--' }})</span>
              </a-tooltip>
              <a-tooltip v-if="cardData.dba_status == '待评审'">
                <template slot="title" v-if="cardData.operator_dba">
                  <span
                    >任务提交评审后可联系DBA负责人或者项目负责人进行审批。</span
                  >
                </template>
                <custom-icon type="question-circle"></custom-icon>
              </a-tooltip>

              <StatusTag type="dba" :status="cardData.dba_status" />
              <a-popover>
                <template slot="content">
                  <span>催办</span>
                </template>
                <a-icon
                  type="thunderbolt"
                  v-if="cardData.is_urge == 1"
                  style="font-size: 16px"
                ></a-icon>
              </a-popover>
            </span>
          </div>
        </div>

        <div class="tag-box">
          <span>ID：{{ cardData.id }}</span>
          <span>Tag：{{ cardData.review_point }}</span>
          <span
            class="project-group"
            v-if="cardData.project_group && cardData.project_group.length > 0"
          >
            <span>项目组：</span>
            <span v-if="cardData.project_group.length == 1">{{
              cardData.project_group[0]
            }}</span>
            <a-tooltip v-else>
              <template slot="title">
                <span>{{ cardData.project_group.toString() }}</span>
              </template>
              <span>{{ cardData.project_group[0] + '; ' + '...' }}</span>
            </a-tooltip>
          </span>
          <span
            class="project-group-leader"
            v-if="
              cardData.project_group_leader &&
              cardData.project_group_leader.length > 0
            "
          >
            <span>项目组负责人：</span>
            <span v-if="cardData.project_group_leader.length == 1">{{
              cardData.project_group[0]
            }}</span>
            <a-tooltip v-else>
              <template slot="title">
                <span>{{ cardData.project_group_leader.toString() }}</span>
              </template>
              <span>{{ cardData.project_group_leader[0] + '; ' + '...' }}</span>
            </a-tooltip>
          </span>
          <span
            class="dba-leader"
            v-if="cardData.dba_leader && cardData.dba_leader.length > 0"
          >
            <span>DBA负责人：</span>
            <span v-if="cardData.dba_leader.length == 1">{{
              cardData.dba_leader[0]
            }}</span>
            <a-tooltip v-else>
              <template slot="title">
                <span>{{ cardData.dba_leader.toString() }}</span>
              </template>
              <span>{{ cardData.dba_leader[0] + '; ' + '...' }}</span>
            </a-tooltip>
          </span>
          <span>SQL通过率：{{ cardData.passing_rate }}</span>
          <span v-if="cardData.history_baseline == 1">历史标准基线</span>
        </div>

        <div class="side-info-box">
          <span class="avatar-part">
            <a-avatar icon="user" />
          </span>
          <a-tooltip v-if="cardData.ch_creater">
            <template slot="title">
              <span>{{ cardData.created_by }}</span>
            </template>
            <span class="created-by">{{ cardData.ch_creater || '--' }}</span>
          </a-tooltip>
          <span class="created-by" v-else>{{ cardData.created_by }}</span>
          <span>于{{ cardData.created_at + ' ' }}</span>
          <span class="event">发起审核</span>
        </div>
      </div>
      <div class="right-block">
        <!-- <div class="right-block-content"> -->
        <div class="right-block-rules">
          <div>
            <span>处理状态</span>
            <span
              v-if="cardData.review_process"
              style="font-size: 24px; color: #f29339"
              >{{ cardData.review_process }}</span
            >
            <span v-else style="color: #4cbb3a; font-size: 24px">
              <custom-icon
                type="lu-icon-right1"
                style="color: #4cbb3a; font-size: 24px"
              />
            </span>
            <span
              style="font-size: 14px; color: #f29339"
              v-if="cardData.review_process"
              >待处理</span
            >
            <span v-else style="color: #4cbb3a; font-size: 14px">已完成</span>
          </div>
        </div>
        <a-divider type="vertical" />
        <div class="right-block-sql-text">
          <div>
            <span>SQL语句</span>
            <span>{{ cardData.sql_count }}</span>
            <span style="opacity: 0; font-size: 14px">{{ 0 }}</span>
          </div>
        </div>
        <a-divider class="right-block-divider" type="vertical" />
        <div class="right-block-botton">
          <slot name="action"></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';
import StatusTag from '@/components/Biz/Status/Tag';

export default {
  components: { LimitLabel, StatusTag },
  props: {
    cardData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  created() {},
  methods: {
    toDetail(data, e) {
      this.$emit('toDetail', data, e);
    }
  }
};
</script>

<style lang="less" scoped>
.card-table {
  .card-table-main-info {
    display: flex;
    justify-content: space-between;
    .left-block {
      display: flex;
      flex-direction: column;
      margin-right: 32px;
      .info-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .status-box > span,
        > span {
          margin-right: 28px;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #a1a1aa;
          font-weight: 400;
        }
        .status-box {
          .review-type {
            white-space: nowrap;
            .ant-tag {
              font-size: 12px;
              margin-right: 0;
              padding: 0 8px;
            }
          }
          .dba-status {
            .anticon-thunderbolt {
              color: rgb(232, 125, 0);
              vertical-align: middle;
            }
          }
        }
        .project-name {
          position: relative;
          white-space: nowrap;
          top: -2px;
          &:hover {
            cursor: pointer
          }
          > span {
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #4db5f2;
            font-weight: 600;
            max-width: 150px;
            display: block;
          }
          /deep/.limit-label {
            pre {
              font-family: PingFangSC-Semibold;
              font-size: 14px !important;
              color: #4db5f2 !important;
              font-weight: 600 !important;
            }
          }
        }
      }
      .tag-box {
        margin: 8px 0;
        display: flex;
        flex-wrap: wrap;
        > span {
          margin: 2px 16px 2px 0;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #27272a;
          letter-spacing: 0;
          font-weight: 400;
          text-align: center;
          border: 1px solid #e4e4e7;
          border-radius: 4px;
          padding: 3px 7px;
        }
      }
      .side-info-box {
        display: flex;
        align-items: center;
        > span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          letter-spacing: 0;

          font-weight: 400;
          &.avatar-part {
            width: 20px;
            .ant-avatar {
              width: 20px;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              margin-bottom: 2px;
              color: #fff;
              background: #4ec3f5;
            }
          }
        }
        .created-by {
          margin: 0 4px 0 8px;
          color: rgb(113, 113, 122);
        }
        .event {
          color: rgb(113, 113, 122);
        }
      }
    }
    .right-block {
      display: flex;
      align-items: center;
      .ant-divider-vertical {
        width: 1px;
        height: 32px;
        margin: 0 12px;
      }
      .right-block-rules,
      .right-block-sql-text {
        padding: 10px 18px 0 18px;
        display: flex;
        align-items: center;
        > div {
          display: flex;
          flex-direction: column;
          text-align: center;
          > span {
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: #27272a;
            font-weight: 400;
            &:first-child {
              font-size: 14px;
              color: #71717a;
              white-space: nowrap;
            }
            &:last-child {
              margin-top: -6px;
            }
          }
        }
      }
      .right-block-rules {
        .rules {
          color: #e71d36 !important;
        }
      }
      .right-block-botton {
        padding-left: 18px;
        width: 200px;
        display: flex;
        align-items: center;
        justify-content: space-between !important;
        flex-wrap: wrap;
        > a {
          height: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #4db5f2;
          letter-spacing: 0;
          line-height: 16px;
          font-weight: 400;
          display: flex;
          align-items: center;
          margin-right: 12px;
          margin-bottom: 4px;
          > .anticon {
            margin-right: 4px;
          }
          &.highlight {
            padding: 14px 6px 14px 8px;
            background: #4db5f2;
            border-radius: 4px;
            color: #fff;
            margin-right: 0;
            > .anticon {
              font-size: 12px;
              margin-right: 0;
              margin-left: 4px;
            }
          }
          &:hover {
            color: @primary-color;
            &.highlight {
              color: #fff;
              background: @primary-color;
            }
          }
        }
        > a[disabled] {
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .card-table {
    .card-table-main-info {
      .right-block {
        display: flex;
        justify-content: space-between;
        .right-block-divider {
          display: none;
        }
        .right-block-botton {
          width: 340px;
          display: none;
        }
      }
      .left-block {
        .info-box {
          .project-name {
            > span {
              max-width: 150px;
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1560px) {
  .card-table {
    .card-table-main-info {
      .left-block {
        .info-box {
          .project-name {
            > span {
              max-width: 270px;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less" scoped></style>
