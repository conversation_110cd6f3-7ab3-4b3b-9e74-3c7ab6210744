<template>
  <div class="psc-right-query-table-rows">
    <a-spin :spinning="loading">
      <Result :info="resultInfo" :resize="resize" v-if="!isEmpty"></Result>
      <custom-empty v-else />
    </a-spin>
  </div>
</template>

<script>
import { getFirstRows } from '@/api/sqlresolver';
import Result from '@/pages/sqlresolver/components/QueryDetail/Result';
import ResizeObserver from 'resize-observer-polyfill';
import config from './config';

export default {
  components: { Result },
  inject: ['instanceItem'],
  props: {
    node: Object
  },
  data() {
    this.config = config(this);
    return {
      loading: false,
      resize: 0,
      resultInfo: {}
    };
  },
  computed: {
    isEmpty() {
      return _.isEmpty(this.resultInfo);
    }
  },
  created() {
    const params = {
      instance_id: _.get(this.instanceItem, 'value'),
      schema_name: this.node.schema_name,
      table_name: this.node.name
    };
    this.loading = true;
    getFirstRows(params)
      .then(res => {
        this.loading = false;
        if (CommonUtil.isSuccessCode(res)) {
          // this.$hideLoading({ tips: '请求成功' });
          this.resultInfo = _.get(res, 'data.data') || {};
        } else {
          this.$hideLoading({
            method: 'error',
            tips: _.get(res, 'data.message'),
            response: res
          });
        }
      })
      .catch(e => {
        console.error(e);
        this.loading = false;
        this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
      });
  },
  beforeDestroy() {
    if (this.ro) {
      this.ro.unobserve(this.$el);
      this.ro = null;
    }
  },
  mounted() {
    this.ro = new ResizeObserver((entries, observer) => {
      // console.log(entries, 'entries');
      this.resize = this.resize + 1;
    });
    this.ro.observe(this.$el);
  },
  methods: {}
};
</script>

<style lang="less" scoped>
.psc-right-query-table-rows {
  padding: 12px;
  height: 100%;
  // overflow: auto;
  /deep/ .ant-spin-nested-loading {
    height: 100%;

    .ant-spin-container {
      height: 100%;

      .psc-right-query-detail-result {
        .sql-test {
          display: flex;
          width: 100%;
          .limit-label {
            max-width: none;
            display: inline-block;
          }
          .sql-total-count {
            flex-shrink: 0;
          }
        }
        .sql-limit-tips {
          display: none;
        }
      }
    }
  }
}
</style>
<style lang="less">
.psc-rqtr-limit-label-popover {
  .ant-popover-inner-content {
    max-height: 250px;
  }
}
</style>
