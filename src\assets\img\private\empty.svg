<?xml version="1.0" encoding="UTF-8"?>
<svg width="100px" height="85px" viewBox="0 0 100 85" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 158</title>
    <defs>
        <linearGradient x1="50%" y1="74.6517212%" x2="50%" y2="0%" id="linearGradient-1">
            <stop stop-color="#92B5FC" offset="0%"></stop>
            <stop stop-color="#597EF7" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#D6E5FF" offset="5.64822635%"></stop>
            <stop stop-color="#ADC6FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#E3EDFF" offset="0%"></stop>
            <stop stop-color="#F8FAFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="18.1112753%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#D6E5FF" offset="0%"></stop>
            <stop stop-color="#EAF0FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="175.906092%" id="linearGradient-5">
            <stop stop-color="#F0F5FF" offset="0%"></stop>
            <stop stop-color="#ADC6FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.6592061%" id="linearGradient-6">
            <stop stop-color="#E7F0FF" offset="0%"></stop>
            <stop stop-color="#E4ECFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-158" fill-rule="nonzero">
            <ellipse id="椭圆形" fill="#EBF2FF" cx="50" cy="71.2101064" rx="50" ry="13.7898936"></ellipse>
            <g id="编组" transform="translate(13.800905, 23.962766)">
                <path d="M59.8089027,0 C60.1220751,0 60.412988,0.161785715 60.5780233,0.427731615 L72.39819,19.4753292 L64.7058824,19.4752859 L64.7058824,44.6476064 C64.7058824,45.6464192 63.895538,46.456117 62.8959276,46.456117 L9.50226244,46.456117 C8.50265204,46.456117 7.69230769,45.6464192 7.69230769,44.6476064 L7.69230769,19.4752859 L0,19.4753292 L11.4039052,0.439814318 C11.5673461,0.166997138 11.8621781,0 12.1803935,0 L59.8089027,0 Z" id="路径" fill="url(#linearGradient-1)"></path>
                <path d="M72.39819,19.2154255 L72.39819,41.8218085 C72.39819,46.8158727 68.3464683,50.8643617 63.3484163,50.8643617 L9.04977376,50.8643617 C4.05172172,50.8643617 0,46.8158727 0,41.8218085 L0,19.2154255 L0,19.2154255 L19.1050779,19.2154255 C21.0487648,19.2154255 22.6244344,20.7898379 22.6244344,22.7319741 L22.6244344,24.4400118 C22.6244344,25.4388246 23.4347787,26.2485224 24.4343891,26.2485224 L47.9638009,26.2485224 C48.9634113,26.2485224 49.7737557,25.4388246 49.7737557,24.4400118 L49.7737557,22.7319741 C49.7737557,20.7898379 51.3494251,19.2154255 53.2931122,19.2154255 L72.39819,19.2154255 L72.39819,19.2154255 Z" id="路径" fill="url(#linearGradient-2)"></path>
            </g>
            <path d="M16.3796407,11.8676026 C17.3881463,12.1120438 18.1688323,12.2343748 18.7216983,12.2345954 C19.2339061,12.2347999 19.8837915,12.1303268 20.6713545,11.9211761 L20.6713547,11.9211773 C21.0308658,11.825703 21.399937,12.039809 21.4956982,12.3993964 C21.5279103,12.5203545 21.5257983,12.6478674 21.4895985,12.7676377 C21.264467,13.5125063 21.1520052,14.1449971 21.1522128,14.6651099 C21.152438,15.2295315 21.2854897,16.0184667 21.5513676,17.031915 L21.5513689,17.0319146 C21.6457992,17.3918543 21.4306221,17.759961 21.0707576,17.8541039 C20.9436895,17.8873456 20.8096448,17.882701 20.6851223,17.8407415 C19.9249892,17.5846043 19.2845114,17.4564317 18.7636887,17.4562238 C18.1885467,17.4559943 17.3842822,17.611664 16.3508955,17.9232329 L16.3508946,17.9232299 C15.9947663,18.0306037 15.6187931,17.8288785 15.5111352,17.4726644 C15.4743205,17.3508535 15.4727445,17.2211352 15.5065893,17.0985221 C15.8028199,16.0253383 15.9508227,15.2068751 15.9505977,14.6431326 C15.9503928,14.1295367 15.8270235,13.4915223 15.5804901,12.7290893 L15.5804867,12.7290905 C15.4659929,12.3750056 15.6601459,11.9953765 16.0141398,11.8811655 C16.132364,11.8430218 16.2588607,11.838328 16.3796407,11.8676026 Z" id="矩形备份-2" fill="url(#linearGradient-3)" transform="translate(18.527954, 14.900356) rotate(-45.000000) translate(-18.527954, -14.900356) "></path>
            <path d="M84.0266996,21.8144114 C85.0352045,22.0588527 85.8158919,22.1811836 86.3687572,22.1814043 C86.8809646,22.1816087 87.5308512,22.0771357 88.3184125,21.867985 L88.3184148,21.8679861 C88.6779254,21.7725119 89.0469971,21.9866179 89.1427562,22.3462053 C89.1749704,22.4671633 89.172857,22.5946763 89.1366568,22.7144466 C88.911526,23.4593152 88.7990632,24.091806 88.7992708,24.6119188 C88.7994961,25.1763403 88.9325475,25.9652755 89.1984275,26.9787239 L89.1984275,26.9787234 C89.292857,27.3386631 89.0776804,27.7067698 88.7178164,27.8009128 C88.5907492,27.8341544 88.4567035,27.8295098 88.3321822,27.7875504 C87.5720486,27.5314132 86.9315699,27.4032405 86.4107483,27.4030327 C85.8356045,27.4028031 85.0313407,27.5584728 83.9979547,27.8700417 L83.9979524,27.8700388 C83.6418252,27.9774125 83.2658518,27.7756873 83.1581937,27.4194732 C83.1213793,27.2976623 83.1198032,27.1679441 83.153648,27.045331 C83.4498795,25.9721472 83.5978805,25.1536839 83.5976556,24.5899415 C83.5974506,24.0763456 83.4740822,23.4383311 83.2275482,22.6758982 L83.2275459,22.6758993 C83.1130521,22.3218144 83.3072053,21.9421854 83.6611994,21.8279743 C83.779422,21.7898307 83.9059194,21.7851369 84.0266996,21.8144114 Z" id="矩形备份-4" fill="url(#linearGradient-3)" transform="translate(86.175013, 24.847164) rotate(-45.000000) translate(-86.175013, -24.847164) "></path>
            <g id="编组" transform="translate(28.959276, 0.000000)">
                <path d="M27.1493213,10.4712766 C28.9486199,10.4712766 30.4072398,11.9287326 30.4072398,13.7265957 C30.4072398,15.5244589 28.9486199,16.9819149 27.1493213,16.9819149 L27.1493213,16.9819149 C27.1948441,16.9819149 27.2401491,16.9828478 27.2852179,16.9846956 C25.5487624,17.0557572 24.1628959,18.4847815 24.1628959,20.237234 C24.1628959,21.9896866 25.5487624,23.418711 27.2851441,23.4897816 C27.2458106,23.491389 27.2062405,23.4923041 27.1664948,23.4925089 L35.1855204,23.4925532 C36.984819,23.4925532 38.4434389,24.9500093 38.4434389,26.7478723 C38.4434389,28.5457354 36.984819,30.0031915 35.1855204,30.0031915 L12.3800905,30.0031915 C10.5807918,30.0031915 9.12217195,28.5457354 9.12217195,26.7478723 C9.12217195,24.9558856 10.5712723,23.5020843 12.3624583,23.4925998 L11.9457014,23.4925532 C13.7450001,23.4925532 15.2036199,22.0350972 15.2036199,20.237234 C15.2036199,18.4393709 13.7450001,16.9819149 11.9457014,16.9819149 L3.25791855,16.9819149 C1.45861982,16.9819149 0,15.5244589 0,13.7265957 C0,11.9287326 1.45861982,10.4712766 3.25791855,10.4712766 L27.1493213,10.4712766 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                <rect id="矩形备份-3" fill="url(#linearGradient-5)" x="15.0950226" y="0" width="17.3755656" height="6.5106383" rx="2.80995475"></rect>
                <ellipse id="椭圆形" fill="url(#linearGradient-6)" cx="38.5520362" cy="13.6542553" rx="3.25791855" ry="3.25531915"></ellipse>
            </g>
        </g>
    </g>
</svg>