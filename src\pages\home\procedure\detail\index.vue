<template>
  <div class="procedure-detail">
    <a-card style="width:100%" class="common-page-card" title="存储过程游标详情" :bordered="false">
      <div slot="extra">
        <a-button type="primary" @click="exportData">导出</a-button>
      </div>
      <a-spin class="content-top" :spinning="loading">
        <a-row class="row-margin" :gutter="20">
          <a-col :span="8">
            <div class="label">项目：</div>
            <div>{{detail.project || '--'}}</div>
          </a-col>
          <a-col :span="8">
            <div class="label">版本：</div>
            <div>{{detail.review_point || '--'}}</div>
          </a-col>
        </a-row>
        <a-row :gutter="20">
          <a-col :span="8">
            <div class="label">时间：</div>
            <div>{{detail.created_at || '--'}}</div>
          </a-col>
          <a-col :span="8">
            <div class="label">发起人：</div>
            <div>{{detail.created_by || '--'}}</div>
          </a-col>
          <a-col :span="8">
            <div class="label">扫描内容：</div>
            <div>{{detail.review_content || '--'}}</div>
          </a-col>
        </a-row>
      </a-spin>
      <a-card style="width: 100%" class="common-box-card scan-list" :bordered="false">
        <span slot="title" class="common-box-card-title">
          <a-icon type="export" />扫描内容列表
        </span>
        <!-- <SearchArea v-bind="searchParams" @reset="reset" @search="search"></SearchArea> -->
        <Table ref="table" v-bind="tableParams || {}">
          <LimitLabel slot="function_title" slot-scope="{ text }" :limit="45" :label="text"></LimitLabel>
          <!-- <span slot="cursors" slot-scope="{ text }">
            <a-tag v-for="(item, i) in text" :key="i">{{item}}</a-tag>
          </span> -->
          <template v-slot:cursors="{ text, record }">
            <div class="tagStyle">
              <div :class="id === record.id && flag ? '' : 'tag-item'">
                <a-tag v-for="tag in text" :key="tag">{{ tag }}</a-tag>
              </div>
              <a-icon
                v-if="record.cursors && record.cursors.length > 2"
                :type="flag && id === record.id ? 'double-left' : 'double-right'"
                @click="toggleUpDown(record)"
              />
            </div>
          </template>
          <span slot="action" slot-scope="{ record }">
            <a @click="toDetail(record, $event)">详情</a>
          </span>
          <template slot="status" slot-scope="{ text, record }">
            <Status :status="text" :message="record.error_message" from="homedetail"></Status>
          </template>
        </Table>
      </a-card>
      <EditModal ref="editModal"></EditModal>
    </a-card>
  </div>
</template>

<script>
import { reviewProcedureDetail, exportProcedure } from '@/api/procedure';
import SearchArea from '@/components/SearchArea';
import Status from '@/components/Biz/Status';
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import EditModal from './components/editModal';
import common from '@/utils/common';
import config from './config';

export default {
  name: 'procedureDetail',
  components: {
    SearchArea,
    Table,
    LimitLabel,
    EditModal,
    Status
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      loading: false,
      detail: {},
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 3
      },
      tableParams: {
        url: '/sqlreview/review/review-procedure-detail/',
        reqParams: {
          review_procedure_id: this.$route.params.id
        },
        columns: this.config.columns,
        rowKey: 'id',
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true
      },
      searchData: {},
      id: null,
      flag: false
    };
  },
  computed: {},
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      // 请求
      this.loading = true;
      reviewProcedureDetail({
        id: this.$route.params.id
      })
        .then(res => {
          this.loading = false;
          if (_.get(res, 'data.code') == 0) {
            this.detail = _.get(res, 'data.data') || {};
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    toDetail(record) {
      const searchData = this.$refs.table.searchParams
      this.$router.push({
        name: 'home-procedure-review',
        params: { id: record.id, searchData: searchData }
      });
    },
    exportData() {
      // 请求
      this.$showLoading({ tips: '下载中' });
      exportProcedure({
        id: this.$route.params.id,
        ...(this.searchData || {})
      })
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      this.searchData = { ...data };
      if (this.searchData.status && this.searchData.status.length > 0) {
        this.searchData.status = this.searchData.status.join(',');
      }
      table.refresh(null, this.searchData);
    },
    reset() {
      const { table } = this.$refs;
      this.searchData = {};
      table.refresh();
    },
    // 上下折叠
    toggleUpDown(record) {
      if (this.flag && this.id !== record.id) {
        this.flag = true;
        this.id = record.id;
      } else {
        this.flag = !this.flag;
        this.id = record.id;
      }
    }
  }
};
</script>

<style scoped lang="less">
.procedure-detail {
  .content-top {
    width: 100%;
    background: rgba(24, 144, 255, 0.06);
    padding: 24px;
    margin-bottom: 24px;

    .ant-col {
      display: flex;
      .label {
        color: #8c8c8c;
        flex-shrink: 0;
      }
    }
    .row-margin {
      margin-bottom: 20px;
    }
    .content-top-button {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .top-left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }
  .scan-list {
    /deep/.ant-card-body {
      padding-top: 8px;
    }
  }
  .tagStyle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tag-item {
      height: 24px;
      overflow: hidden;
    }
    .ant-tag {
      margin-bottom: 5px;
    }
    .anticon {
      margin-right: 30px;
    }
    .anticon-double-right {
      transform: rotate(90deg);
    }
    .anticon-double-left {
      transform: rotate(90deg);
    }
  }
}
</style>
