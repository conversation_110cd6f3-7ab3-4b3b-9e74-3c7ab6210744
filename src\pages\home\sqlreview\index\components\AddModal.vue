<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="新建审核对象"
    okText="保存"
    width="640px"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="params" :formData="data" class="add-form">
        <div class="file-upload-area" slot="file-upload">
          <a-upload
            name="file"
            :multiple="true"
            :before-upload="beforeUpload"
            :fileList="fileList"
            :remove="handleRemove"
          >
            <a-button class="file-import-btn">点击上传</a-button>
          </a-upload>
        </div>
      </Form>
    </a-spin>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import { checkProject, sqlreviewProjectCheckGit } from '@/api/home';
// import common from '@/utils/common';
const formParams = (ctx, _t, bool, type) => {
  return {
    layout: 'vertical',
    fields: [
      {
        type: 'Select',
        label: '任务类型',
        key: 'review_type',
        props: {
          options:
            type == 'order'
              ? [{ label: '代码扫描', value: 0 }]
              : [
                  { label: '代码扫描', value: 0 },
                  { label: '存储过程扫描', value: 1 },
                  { label: '文件扫描', value: 2 }
                ]
        },
        listeners: {
          change: value => {
            ctx.$refs.form.saving({
              review_type: value,
              project_id: null,
              review_point: null,
              datasource_id: null,
              schema: null,
              procedure: null,
              db_type: null,
              data_source_id: null
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      },
      // 项目review
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'review对象',
          key: 'project_id',
          props: {
            url: '/sqlreview/review/all_project',
            reqParams: {
              _t
            },
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          listeners: {
            change: value => {
              if (value) {
                ctx.check(value);
              }
              ctx.$refs.form.saving({
                project_id: value,
                review_point: null
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项' }],
          visible: formData.review_type == 0 && bool == 0
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'review版本',
          key: 'review_point',
          props: {
            url: '/sqlreview/review/project-ls-git/',
            reqParams: {
              project_id: formData.project_id
            },
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
          visible: formData.review_type == 0 && bool == 0
        };
      },
      (formData = {}) => {
        return {
          type: 'TableEdit',
          label: 'review对象',
          key: 'review_list',
          width: '100%',
          getDataMethod: 'getData',
          resetFieldsMethod: 'resetFields',
          initialValue: [],
          rules: [{ required: false, message: '该项为必填项' }],
          props: {
            columns: [
              {
                title: '应用',
                dataIndex: 'project_id',
                key: 'project_id',
                width: 200,
                scopedSlots: { customRender: 'project_id' }
              },
              {
                title: '版本',
                dataIndex: 'review_type',
                key: 'review_type',
                width: 200,
                scopedSlots: { customRender: 'review_type' }
              },
              {
                title: '操作',
                key: 'action',
                width: 100,
                scopedSlots: { customRender: 'action' }
              }
            ],
            editConfig: {
              project_id: (row, record = {}) => {
                return {
                  type: 'Select',
                  props: {
                    url: '/sqlreview/review/all_project',
                    allowSearch: true,
                    backSearch: true,
                    limit: 50
                  },
                  rules: [{ required: true, message: '该项为必填项' }],
                  listeners: {
                    change: value => {
                      const { form } = ctx.$refs;
                      const table = form.$refs.review_list[0];

                      table.saving({
                        id: record.id,
                        project_id: value,
                        review_type: null
                      });
                    }
                  }
                };
              },
              review_type: (row, record = {}) => {
                return {
                  type: 'Select',
                  props: {
                    url: '/sqlreview/review/project-ls-git/',
                    reqParams: {
                      project_id: record.project_id
                    },
                    allowSearch: true,
                    backSearch: true,
                    limit: 50
                  },
                  rules: [{ required: true, message: '该项为必填项' }],
                  listeners: {
                    change: value => {}
                  }
                };
              }
            },
            // mode: 'list',
            rowKey: 'id',
            initEditStatus: true,
            pagination: false,
            leastNum: 1,
            actionBtns: ['add', 'remove'],
            actionBtnsIcons: {
              add: 'plus-circle',
              remove: 'close-circle'
            }
          },
          visible: formData.review_type == 0 && bool == 1
        };
      },
      // 存储过程
      (formData = {}) => {
        return {
          type: 'Select',
          label: '数据源选择',
          key: 'datasource_id',
          props: {
            url: '/sqlreview/project/data_source_choices',
            loaded(data) {
              ctx.dataSourceOption = data;
            },
            reqParams: {
              type: 'ORACLE'
            },
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          listeners: {
            change: value => {
              console.log(value);
              ctx.dataSourceOption.map(item => {
                if (item.value === value) {
                  ctx.changeValueArr = item.db_type;
                }
              });
              console.log(ctx.changeValueArr);
              ctx.$refs.form.saving({
                datasource_id: value,
                schema_id: null,
                procedure: null
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
          visible: formData.review_type == 1
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'schema',
          key: 'schema_id',
          props: {
            url: '/sqlreview/project/get_schema_list',
            reqParams: {
              datasource_id: formData.datasource_id
            },
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                schema_id: value || undefined,
                procedure: null
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
          visible: formData.review_type == 1
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '审核规则',
          key: 'rule_set',
          props: {
            url: '/sqlreview/project/rule_set_all',
            mode: 'multiple',
            reqParams: { db_type: ctx.changeValueArr }
          },
          listeners: {
            change: value => {}
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
          visible: formData.review_type == 1
        };
      },
      (formData = {}) => {
        return {
          type: 'TreeSelect',
          label: '存储过程选择',
          key: 'procedure',
          props: {
            url: '/sqlreview/review/procedure-list/',
            reqParams: {
              data_source_id: formData.datasource_id,
              schema_id: formData.schema_id
            },
            treeCheckable: true,
            maxTagCount: 4,
            beforeLoaded(res) {
              return [
                {
                  title: '存储过程',
                  value: 'PROCEDURE',
                  key: 'PROCEDURE',
                  disableCheckbox: (res.PROCEDURE || []).length <= 0,
                  children: (res.PROCEDURE || []).map(item => {
                    return {
                      ...item,
                      title: item.label,
                      key: item.value
                    };
                  })
                },
                {
                  title: '函数',
                  value: 'FUNCTION',
                  key: 'FUNCTION',
                  disableCheckbox: (res.FUNCTION || []).length <= 0,
                  children: (res.FUNCTION || []).map(item => {
                    return {
                      ...item,
                      title: item.label,
                      key: item.value
                    };
                  })
                },
                {
                  title: '包',
                  value: 'PACKAGE',
                  key: 'PACKAGE',
                  disableCheckbox: (res.PACKAGE || []).length <= 0,
                  children: (res.PACKAGE || []).map(item => {
                    return {
                      ...item,
                      title: item.label,
                      key: item.value
                    };
                  })
                }
              ];
            }
          },
          rules: [{ required: true, message: '该项为必填项' }],
          visible: formData.review_type == 1
        };
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: 'review方式',
          key: 'mode',
          props: {
            options: [
              {
                label: '增量',
                value: 1
              },
              {
                label: '全量',
                value: 0
              }
            ]
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                mode: value,
                history_baseline: null
              });
            }
          },
          visible: [0, 1].includes(formData.review_type),
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: '历史标准基线',
          key: 'baseline_switch',
          props: {
            options: [
              {
                label: '加入',
                value: 1
              },
              {
                label: '不加入',
                value: 0
              }
            ]
          },
          visible: ctx.type === 'order' && formData.mode == 0
        };
      },
      // 文件扫描
      (formData = {}) => {
        return {
          type: 'TableEdit',
          label: '指定数据源/schema',
          key: 'schema_list',
          width: '100%',
          getDataMethod: 'getData',
          resetFieldsMethod: 'resetFields',
          initialValue: [],
          rules: [{ required: false, message: '该项为必填项' }],
          props: {
            columns: [
              {
                title: '数据源',
                dataIndex: 'datasource_id',
                key: 'datasource_id',
                width: 200,
                scopedSlots: { customRender: 'datasource_id' }
              },
              {
                title: 'schema',
                dataIndex: 'schema',
                key: 'schema',
                width: 220,
                scopedSlots: { customRender: 'schema' }
              },
              {
                title: '操作',
                key: 'action',
                width: 80,
                scopedSlots: { customRender: 'action' }
              }
            ],
            editConfig: {
              datasource_id: (row, record = {}) => {
                return {
                  type: 'DataBaseChoose',
                  props: {
                    url: '/sqlreview/project/data_source_choices',
                    reqParams: {
                      restrict: 'restrict'
                    },
                    loaded(data) {
                      ctx.dataSourceOption = data;
                    },
                    beforeLoaded(data) {
                      return data.map(item => {
                        return {
                          ...item,
                          instance_usage: item.env
                        };
                      });
                    },
                    mode: 'default',
                    optionLabelProp: 'children',
                    // isClear: false,
                    // needConfirm: false,
                    allowSearch: true,
                    backSearch: true,
                    limit: 50
                  },
                  rules: [{ required: true, message: '该项为必填项' }],
                  listeners: {
                    change: value => {
                      const { form } = ctx.$refs;
                      const table = form.$refs.schema_list[0];
                      if (value) {
                        const env = ctx.dataSourceOption.filter(
                          item => item.value === value
                        )[0].env;
                        table.saving({
                          id: record.id,
                          datasource_id: value,
                          env: env,
                          schema: null
                        });
                      } else {
                        table.saving({
                          id: record.id,
                          datasource_id: null,
                          env: '--',
                          schema: null
                        });
                      }
                    }
                  }
                };
              },
              schema: (row, record = {}) => {
                return {
                  type: 'Select',
                  props: {
                    mode: 'multiple',
                    url: '/sqlreview/project/get_schema_list',
                    reqParams: { datasource_id: record.datasource_id || '' },
                    allowSearch: true,
                    backSearch: true,
                    limit: 50
                  },
                  rules: [{ required: true, message: '该项为必填项' }]
                };
              }
            },
            rowKey: 'id',
            initEditStatus: true,
            pagination: false,
            // draggable: true,
            leastNum: 1,
            actionBtns: ['add', 'remove'],
            actionBtnsIcons: {
              add: 'plus-circle',
              remove: 'close-circle'
            }
          },
          visible: formData.review_type == 2
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '绑定规则集',
          key: 'rule_set_id',
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ],
          props: {
            url: '/sqlreview/project/rule_set_all',
            mode: 'multiple'
          },
          visible: formData.review_type == 2
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '上传文件',
          key: 'files_list',
          hideComponent: true,
          slots: [{ key: 'file-upload' }],
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ],
          visible: formData.review_type == 2
        };
      }
    ]
  };
};

export default {
  components: { Form },
  props: {},
  data() {
    return {
      spinning: false,
      visible: false,
      data: {},
      params: formParams(this),
      dataSourceOption: [],
      changeValueArr: null,
      moreGit: -1,
      fileList: [],
      fileSize: 0
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(type = '') {
      this.type = type;
      this.data = {
        mode: 1,
        review_type: type == 'order' ? 0 : null
      };
      this.visible = true;
      this.spinning = true;
      sqlreviewProjectCheckGit()
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const moreGit = _.get(res, 'data.data');
            this.moreGit = moreGit;
            this.params = formParams(this, +new Date(), moreGit, type);
            this.spinning = false;
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    hide() {
      const { form } = this.$refs;
      this.data = {};
      form.resetFields();
      this.visible = false;
      this.fileList = [];
      this.fileSize = 0;
    },
    check(id) {
      checkProject({ project_id: id }).then(res => {
        if (res.data.code != 0) {
          this.data.project_id = undefined;
          this.$hideLoading({ method: 'error', tips: res.data.message });
        }
      });
    },
    // 上传之前
    beforeUpload(file) {
      const maxSize = 200 * 1024 * 1024; // byte
      this.fileSize = this.fileSize + file.size;
      if (this.fileSize > maxSize) {
        this.$message.error('文件大小错误，文件总大小不超过200MB');
        return;
      }
      if (!/\.(sql|cpp|xml|zip|tar|prc|pck|pkg)$/.test(file.name)) {
        this.$message.error(
          '文件格式错误，文件类型支持.sql/.cpp/.xml/.zip/.tar'
        );
        return;
      }
      if (this.fileList.length > 4) {
        this.$message.warning('最多可上传五个文件');
        return;
      }
      const fileNames = this.fileList.map(item => item.name);
      if (!fileNames.includes(file.name)) {
        this.fileList.push(file);
      } else {
        this.$message.warning('该文件已上传，无需重复上传');
        return;
      }
      const { form } = this.$refs;
      form.saving({ files_list: this.fileList });
      return false;
    },
    handleRemove(file) {
      this.fileList = this.fileList.filter(item => item.name !== file.name);
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData();
      let schemaList = data.schema_list || [];
      let schemaId = [];
      let datasourceId = [];
      schemaList.forEach(item => {
        !_.isEmpty(item.schema) && schemaId.push(item.schema);
        !!item.datasource_id && datasourceId.push(item.datasource_id);
      });
      // schemaList 有才校验
      if (
        schemaList.length > 0 &&
        (datasourceId.length <= 0 ||
          schemaId.length <= 0 ||
          datasourceId.length !== schemaId.length)
      ) {
        this.$message.warning('请选择数据源/schema');
        return;
      }

      form.validate(valid => {
        if (valid) {
          this.$emit('save', data, this.moreGit);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .add-form {
  .ant-row .ant-form-item-control-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        width: 150px;
      }
    }
  }
}
</style>
