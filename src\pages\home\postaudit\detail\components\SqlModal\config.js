/*
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-03 16:55:22
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/config.js
 */

export default function (ctx) {
  const columns = [
    {
      title: 'SQL语句',
      dataIndex: 'sql',
      key: 'sql',
      scopedSlots: { customRender: 'sql' },
      width: 200
    },
    {
      title: '预估提升值',
      dataIndex: 'ratio',
      key: 'ratio',
      width: 100,
      customRender: (text, record, index) => {
        const num = +text;
        return _.isNumber(num) ? (num / 10000).toFixed(2) : text;
      }
    }
  ];
  return {
    columns
  };
};
