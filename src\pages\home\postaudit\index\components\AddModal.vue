<template>
  <!-- 新建项目弹窗 -->
  <a-drawer
    :title="title"
    :width="'30%'"
    :visible="visible"
    :drawerStyle="{ paddingBottom: '80px' }"
    wrapClassName="post-audit-model"
    okText="保存"
    @close="onCancel"
  >
    <Form ref="form" v-bind="params" :formData="data">
      <JCronModal
        :data="frequency"
        ref="JCronModal"
        slot="frequency"
        @cronExpression="cronExpression"
      ></JCronModal>
      <HCronModal ref="HCronModal" slot="range_time" :pgData="pgData" v-bind="rangeTimeProps" type="hour"></HCronModal>
    </Form>
    <!-- 数据源选择mysql时 新增选项 -->
    <div v-if="dbType == 'MYSQL'">
      <div class="mysql-info">请填写以下服务器信息，并进行连接测试</div>
      <Form class="server-info" ref="mysqlForm" v-bind="mysqlParams" :formData="mysqlData"></Form>
    </div>
    <div
      :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
    >
      <a-button @click="test" type="primary" v-if="dbType == 'MYSQL'">连接测试</a-button>
      <a-button @click="hide">取消</a-button>
      <a-button type="primary" @click="onOk">保存</a-button>
    </div>
  </a-drawer>
</template>

<script>
import JCronModal from './JCronModal';
import HCronModal from './HCronModal';
import moment from 'moment';
import Form from '@/components/Form';
import config from './config';
import { checkProject, checkAuth } from '@/api/home';
import { Base64 } from 'js-base64';
export default {
  components: { Form, JCronModal, HCronModal },
  data() {
    this.config = config(this);
    return {
      form: this.$form.createForm(this, {}),
      visible: false,
      isDisabled: false,
      loading: false,
      data: {},
      mysqlData: {},
      params: {
        layout: 'vertical',
        fields: []
      },
      mysqlParams: {
        layout: 'vertical',
        fields: this.config.mysqlBaseInfo,
        multiCols: 2
      },
      methodType: 'post',
      frequency: '',
      title: '',
      dbType: '',
      pgData: {
        runningStatus: '',
        runStartTime: '',
        runEndTime: ''
      }
    };
  },
  computed: {
    rangeTimeProps() {
      return {
        type: ['DB2', 'SQLSERVER'].includes(this.dbType) ? 'default' : 'hour'
      };
    }
  },
  mounted() {},
  created() {},
  methods: {
    cronExpression(data) {
      if (data.split(' ').length > 0) {
        if (data.split(' ').includes('undefined')) {
          this.data = Object.assign({}, this.data, { frequency: '' });
        } else {
          this.data = Object.assign({}, this.data, { frequency: data });
        }
      }
      this.$nextTick(() => {
        this.$refs.form.validate();
      });
    },
    loadBaseInfo() {
      const fields = this.config[`${this.dbType}_FIELDS`];
      this.$set(this.params, 'fields', fields || this.config.baseInfo);
    },
    show(record, dbType) {
      // if (record) {
      //   this.dbType = record.db_type;
      //   if (this.dbType == 'POSTGRE') {
      //     this.data.type = 2;
      //     this.pgData = {
      //       runningStatus: record.running_status,
      //       runStartTime: record.run_start_time,
      //       runEndTime: record.run_end_time
      //     };
      //   }
      // }
      this.dbType = dbType;
      this.title = '新建审核对象';
      this.visible = true;
      this.data = {
        mode: '1'
      };
      if (!record) {
        this.mysqlData = {};
        this.mysqlData.mode = 0;
        this.$set(this.mysqlData, 'server_port', 22);
      }
      if (record) {
        this.dbType = record.db_type;
        if (this.dbType == 'POSTGRE' || this.dbType == 'SQLSERVER') {
          this.data.type = 2;
          this.pgData = {
            runningStatus: record.running_status,
            runStartTime: record.run_start_time,
            runEndTime: record.run_end_time
          };
        }
        this.title = '编辑审核对象';
        this.isDisabled = true;
        this.frequency = record.frequency;
        if (record.time_range) {
          record.time_range = [
            moment(record.time_range[0]),
            moment(record.time_range[1])
          ];
        }
        this.mysqlData = {
          mode: record.mode,
          server_ip: record.server_ip,
          server_port: record.server_port,
          server_username: record.server_username,
          server_password: record.server_password
        };
        this.data = {
          name: record.name,
          type: record.type,
          data_source: _.get(record, 'data_source.id') || '',
          schema: (record.schema && record.schema.id) || '',
          time_range: record.time_range,
          rule_set: record.rule_set,
          db_type: record.db_type,
          range_time: record.running_status,
          interval_frequency: record.interval_frequency
        };
        this.methodType = 'put';
      }
      this.loadBaseInfo();
    },
    hide() {
      this.visible = false;
      this.methodType = 'post';
      this.frequency = '';
      this.isDisabled = false;
      this.isPass = false;
      this.dbType = '';
      this.mysqlData = {};
      this.pgData = {
        runningStatus: '',
        runStartTime: '',
        runEndTime: ''
      };
      this.$refs.mysqlForm && this.$refs.mysqlForm.resetFields();
      this.$refs.Form && this.$refs.Form.resetFields();
    },
    check(id) {
      checkProject({ project_id: id }).then(res => {
        if (res.data.code != 0) {
          this.data.project_id = undefined;
          this.$hideLoading({ method: 'error', tips: res.data.message });
        }
      });
    },
    onCancel() {
      this.hide();
    },
    // 连接测试
    test() {
      const mysqlForm = this.$refs.mysqlForm;
      const form = this.$refs.form;
      const dataSource = form.getData().data_source;
      const params = { ...mysqlForm.getData(), data_source_id: dataSource };
      return new Promise((resolve, reject) => {
        mysqlForm.validate(valid => {
          if (valid) {
            this.$showLoading();
            checkAuth(params)
              .then(res => {
                if (_.get(res, 'data.code') == 0) {
                  this.$hideLoading({ tips: _.get(res, 'data.message') });
                  resolve();
                } else {
                  this.$hideLoading({
                    method: 'error',
                    tips: _.get(res, 'data.message')
                  });
                  reject(new Error());
                }
              })
              .catch(() => {
                this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
                reject(new Error());
              });
          }
        });
      });
    },
    // 保存 和 连接测试 没有相互关联性
    // 但是保存的时候需要调一次连接测试接口 成功后再调保存接口
    async onOk() {
      const mysqlForm = this.$refs.mysqlForm;
      if (mysqlForm) {
        await this.test();
        this.save();
      } else {
        this.save();
      }
    },
    save() {
      const { form, JCronModal, mysqlForm, HCronModal } = this.$refs;
      if (JCronModal) {
        JCronModal.handleSubmit();
      }
      if (HCronModal) {
        let data = HCronModal.getData();
        this.runningStatus = data.runningStatus;
        this.timeStart = data.timeStart;
        this.timeEnd = data.timeEnd;
        let timeStartArr = this.timeStart
          .split(':')
          .slice(0, 1)
          .join();

        let timeEndArr = this.timeEnd
          .split(':')
          .slice(0, 1)
          .join();

        let pgfrequency = form.getData().interval_frequency;
        if (this.runningStatus == 1) {
          form.getData().interval_frequency < 60
            ? (this.pgtimeCron =
                '0 ' +
                `0/${pgfrequency} ` +
                `${timeStartArr}-${timeEndArr}` +
                ' * * ?')
            : (this.pgtimeCron =
                '0 ' +
                '0 ' +
                `${timeStartArr}-${timeEndArr}/${pgfrequency / 60}` +
                ' * * ?');
        } else {
          form.getData().interval_frequency < 60
            ? (this.pgtimeCron = '0 ' + `0/${pgfrequency}` + ' * * * ?')
            : (this.pgtimeCron =
                '0 ' + '0 ' + `0/${pgfrequency / 60}` + ' * * ?');
        }
        this.data = Object.assign({}, this.data, {
          range_time: data.runningStatus
        });
      }
      this.$nextTick(() => {
        const validArr = [form.validate(), mysqlForm && mysqlForm.validate()];
        Promise.all(validArr).then(valid => {
          if (valid) {
            let getData = form.getData() || {};
            let getMysqlData = (mysqlForm && mysqlForm.getData()) || {};
            getMysqlData.server_password = Base64.encode(
              getMysqlData.server_password || ''
            );
            getData.rule_set = getData.rule_set.join(',');
            getData = {
              ...getData,
              ...getMysqlData,
              db_type: this.dbType,
              pgfrequency: this.pgtimeCron,
              run_start_time: this.timeStart,
              run_end_time: this.timeEnd,
              running_status: this.runningStatus
            };
            if (getData.time_range) {
              getData.time_range = getData.time_range.map(item => {
                return item.format('YYYY-MM-DD HH:mm:ss');
              });
            }
            this.$emit('save', this.methodType, getData, false, this.dbType);
            JCronModal && JCronModal.close();
          }
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.post-audit-model {
  /deep/ .ant-drawer-content-wrapper {
    min-width: 600px;
  }
}
/deep/ .server-info {
  width: 100%;
  background: rgba(230, 247, 255, 0.17);
  .multi-cols-item {
    // margin: 0;
    padding: 0;
  }

  &.ant-form {
    > .ant-row > .ant-col {
      padding: 0 24px !important;

      &:first-child {
        padding: 0 8px !important;
        .ant-form-item {
          > .ant-form-item-label {
            display: none;
          }
          > .ant-form-item-control-wrapper {
            padding: 12px;
            background: rgba(24, 144, 255, 0.06);
            border-radius: 4px;
          }
        }
      }
    }
  }
}
.mysql-info {
  color: rgba(175, 173, 173, 1);
  margin-bottom: 8px;
}
</style>
