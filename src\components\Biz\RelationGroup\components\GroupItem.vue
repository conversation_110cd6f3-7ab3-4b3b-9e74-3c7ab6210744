<template>
  <div :class="className">
    <!-- group -->
    <div
      class="group-box custom-bg"
      :style="{ paddingLeft: (data.children || []).length > 1 ? '116px' : '16px' }"
      v-if="data.group_type === 'group'"
    >
      <template v-for="(item, index) in (data.children || [])">
        <RelationGroupItem
          :key="item.key"
          :value="item"
          :fields="fields"
          :rowKey="rowKey"
          :index="index"
          :setInstance="setInstance"
          @remove="onRemove"
        ></RelationGroupItem>
      </template>

      <!-- operate -->
      <a-button icon="plus" class="group-box-add" @click="add"></a-button>
      <a-popconfirm title="确定删除该行?" @confirm="remove">
        <a-icon class="group-box-remove rg-remove" type="minus-circle" style="color:red;"></a-icon>
      </a-popconfirm>
    </div>
    <!-- single -->
    <div v-else>
      <FormItem
        :value="data"
        :fields="fields"
        :rowKey="rowKey"
        :index="index"
        :setInstance="setInstance"
        ref="form"
        @remove="remove"
      ></FormItem>
    </div>

    <!-- relation -->
    <a-form-item class="relation-group-comp" v-if="index > 0">
      <Select
        v-bind="relationParams"
        v-decorator="[data[rowKey], { initalValue: data.relation, rules: [{ required: true, message: '该项为必填项' }] }]"
      ></Select>
    </a-form-item>
  </div>
</template>

<script>
import FormItem from './FormItem';
import Select from '@/components/Select';
import common from '@/utils/common';
// import _ from 'lodash';

export default {
  name: 'RelationGroupItem',
  inheritAttrs: false,
  components: { FormItem, Select },
  props: {
    value: {
      type: Object,
      default: () => {}
    },
    fields: {
      type: Array,
      default: () => []
    },
    rowKey: {
      type: String,
      default: 'id'
    },
    index: Number,
    setInstance: Function
  },
  data() {
    const { value } = this;
    this.setInstance({
      id: value[this.rowKey],
      instance: this
    });
    // console.log(this, 7878)
    return {
      data: value,
      relationParams: {
        options: [
          { label: 'or', value: 'or' },
          { label: 'and', value: 'and' }
        ],
        style: { width: '80px' },
        allowClear: false,
        placeholder: '关系'
      }
    };
  },
  computed: {
    className() {
      // const { data } = this;
      return ['relation-group-item'].filter(item => item);
    }
  },
  created() {},
  mounted() {},
  destroyed() {
    this.setInstance({
      id: this.data[this.rowKey],
      type: 'delete'
    });
  },
  methods: {
    remove() {
      this.$emit('remove', this.data);
    },
    onRemove(record) {
      const { data, rowKey } = this;
      const { children = [] } = data;
      // this.$set(
      //   this.data,
      //   'children',
      //   children.filter(item => item[rowKey] != record[rowKey])
      // );
      this.data = Object.assign({}, data, {
        children: children.filter(item => item[rowKey] != record[rowKey])
      });
    },
    add() {
      const { data } = this;
      const { children = [] } = data;
      // this.$set(this.data, 'children', [
      //   ...children,
      //   {
      //     [this.rowKey]: common.uuid(),
      //     parent_id: data[this.rowKey],
      //     group_type: 'single'
      //   }
      // ]);
      this.data = Object.assign({}, this.data, {
        children: [
          ...children,
          {
            [this.rowKey]: common.uuid(),
            parent_id: data[this.rowKey],
            group_type: 'single'
          }
        ]
      });
    },
    getData() {
      let formData = {};
      if (this.$refs.form) {
        formData = this.$refs.form.getData();
      }
      return { ...this.data, ...formData, order_num: this.index, children: [] };
    },
    validate() {
      if (this.$refs.form) {
        return this.$refs.form.validate();
      }
      return Promise.resolve();
    }
  },
  watch: {
    value(newVal) {
      this.data = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
// @nodePadding: 12px;
// @childrenPadding: 12px;
.relation-group-item {
  position: relative;
  .group-box {
    padding: 32px 16px 0 116px;
    margin-bottom: 16px;
    position: relative;

    .group-box-add {
      position: absolute;
      left: 0;
      top: 0;
      width: 40px;
      height: 24px;
      border-radius: 0 0 2px 0;
      box-shadow: 2px 2px 2px #f5f5f5;
      border-color: #f5f5f5;
      font-size: 12px;
    }

    .group-box-remove {
      position: absolute;
      right: -8px;
      top: -8px;
      cursor: pointer;
      background: #ffffff;
      display: none;
    }

    &:hover {
      .group-box-remove {
        display: block;
      }
    }
  }

  .relation-group-comp {
    position: absolute;
    left: -100px;
    top: -26px;

    &::before {
      content: '';
      position: absolute;
      width: 24px;
      left: 78px;
      top: 4px;
      height: 2px;
      background: #e8e8e8;
      transform: rotate(-20deg);
      transform-origin: left;
    }
    &::after {
      content: '';
      position: absolute;
      width: 24px;
      left: 78px;
      top: 34px;
      height: 2px;
      background: #e8e8e8;
      transform: rotate(20deg);
      transform-origin: left;
    }
  }
}
</style>
