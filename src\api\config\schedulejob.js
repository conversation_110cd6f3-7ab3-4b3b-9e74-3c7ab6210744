import Http from '@/utils/request';

export function getOpenApiInfo(params) {
  return Http({
    url: `/sqlreview/after_audit/get_openApi_number`,
    method: 'get',
    params: params
  });
}

export function getJDBCInfo(params) {
  return Http({
    url: `/sqlreview/api/v1/schedules/count`,
    method: 'get',
    params: params
  });
}

export function getAgentMasterCount(params) {
  return Http({
    url: `/sqlreview/agent/agent_master_count`,
    method: 'get',
    params: params
  });
}

export default {};
