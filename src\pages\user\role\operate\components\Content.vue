<template>
  <div :class="`user-role-content ${isEdit ? 'urc-edit' : 'urc-add'}`">
    <Blocks :value="blocks">
      <!-- 基础信息 -->
      <Form ref="baseForm" slot="baseInfo" v-bind="baseInfoParams" :formData="baseInfoData"></Form>
      <!-- 权限授权 -->
      <a-row slot="authConfig" class="resource-edit-content" :gutter="48">
        <a-col :span="10">
          <a-spin :spinning="loading">
            <a-input-search style="margin-bottom: 8px" placeholder="Search" @change="onSearch" />
            <a-tree
              :treeData="treeData"
              :selectedKeys="selectedKeys"
              :autoExpandParent="autoExpandParent"
              :expandedKeys="expandedKeys"
              :show-line="true"
              show-icon
              checkable
              v-model="checkedKeys"
              @expand="onExpand"
              @select="selectNode"
              @check="onCheck"
            >
              <span slot="title" slot-scope="node">
                <!-- 文案 -->
                <span v-if="searchValue && node.source_name.indexOf(searchValue) > -1">
                  {{ node.source_name.substr(0, node.source_name.indexOf(searchValue)) }}
                  <span
                    style="color: #f50"
                  >{{ searchValue }}</span>
                  {{ node.source_name.substr(node.source_name.indexOf(searchValue) + searchValue.length) }}
                </span>
                <span v-else>{{node.source_name}}</span>
                <!-- 权限tag展示 -->
                <span class="permission-virtual-tag" v-if="node._showVirtualPermissions">权</span>
                <span class="permission-tag" v-else-if="node.permissions">权</span>
              </span>
              <!-- icons -->
              <template v-for="(icon, key) in iconMap">
                <a-icon
                  :key="key"
                  :type="icon.type"
                  :slot="key"
                  :style="`color: ${icon.color}`"
                  theme="filled"
                />
              </template>
            </a-tree>
          </a-spin>
        </a-col>
        <a-col class="part-right" :span="14">
          <a-alert
            style="margin-bottom:24px;"
            message="请先创建角色后，再设置元素节点属性"
            type="info"
            show-icon
            v-if="!isEdit && selectedNode.source_type === 'element'"
          />
          <div v-if="formParams.fields.length > 0">
            <AttrPanel ref="AttrPanel" :fields="formParams.fields" :data="formData" />
            <div class="part-right-btns" v-show="isEdit && selectedNode.source_type === 'element'">
              <a-button @click="onCancel">取消</a-button>
              <a-button @click="onBatchAuth">批量授权</a-button>
              <a-button type="primary" @click="onSave">保存属性</a-button>
            </div>
          </div>
          <custom-empty :description="false" style="margin-top:20px;" v-else />
        </a-col>
      </a-row>
    </Blocks>
    <BatchAuthModal ref="BatchAuthModal"></BatchAuthModal>
  </div>
</template>

<script>
// import _ from 'lodash';
import { getResourceData, roleUpdate } from '@/api/resource';
import Form from '@/components/Form';
import config from './config';
import Blocks from '@/components/Blocks';
import AttrPanel from './AttrPanel';
import BatchAuthModal from './BatchAuthModal';

export default {
  props: {
    type: String,
    data: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  components: { Form, Blocks, AttrPanel, BatchAuthModal },
  data() {
    this.config = config(this);
    this.baseInfoConfig = this.config.baseInfo(this.type);
    this.modifyNodes = {};
    this.systemNodes = [];
    return {
      loading: false,
      // 基础信息
      baseInfoParams: {
        layout: 'vertical',
        multiCols: 2,
        gutter: 32,
        fields: this.baseInfoConfig
      },
      baseInfoData: {},
      // 权限信息
      treeData: [
        {
          key: 'root_resource',
          source_name: '资源权限',
          isRoot: true,
          selectable: false,
          scopedSlots: { title: 'title' },
          source_type: 'root_resource',
          source_category: 'resource',
          // checkable: false,
          children: []
        }
        // {
        //   key: 'root_data',
        //   source_name: '数据权限',
        //   isRoot: true,
        //   selectable: false,
        //   scopedSlots: { title: 'title' },
        //   source_category: 'data',
        //   // checkable: false,
        //   children: []
        // }
      ],
      nodeMap: {},
      selectedNode: {},
      selectedKeys: [],
      checkedKeys: [],
      expandedKeys: ['root_resource', 'root_data'],
      searchValue: '',
      autoExpandParent: true,
      iconMap: this.config.iconMap,
      // 右侧表单
      formParams: {
        layout: 'vertical',
        fields: []
      },
      formData: {},
      // 块信息
      blocks: [
        {
          key: 'baseInfo',
          icon: 'edit',
          title: '基础信息'
        },
        {
          key: 'authConfig',
          icon: 'setting',
          title: '权限授权'
        }
      ]
    };
  },
  computed: {
    isEdit() {
      return this.type === 'edit';
    }
  },
  created() {
    this.initData();
  },
  mounted() {},
  methods: {
    initData(res = {}) {
      this.loading = true;
      getResourceData()
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.loading = false;
            // const { resource = [], data = [] } = _.get(res, 'data.data');
            const list = _.get(res, 'data.data.results') || [];
            const resource = list.filter(
              item => item.source_category === 'resource'
            );
            // const data = list.filter(item => item.source_category === 'data');
            let treeData = [...this.treeData];
            treeData[0].children = this.toTree(
              'resource',
              'root_resource',
              resource
            );
            // treeData[1].children = this.toTree('data', 'root_data', data);
            this.treeData = [...treeData];

            this.expandedKeys = [...this.expandedKeys];

            // this.$nextTick(() => {
            //   // 编辑时合并数据
            //   if (this.type === 'edit') {
            //     const { resource = [], data = [] } = this.data;
            //     const allData = [...resource, ...data];
            //     allData.forEach(node => {
            //       if (node.source_ext) {
            //         this.saveNode(node.id, node);
            //       }
            //     });
            //   }
            // });
            // 合并信息
            this.combineData();
          } else {
            this.loading = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    },
    toTree(category, rootKey, list = []) {
      let obj = {};
      let result = [];
      // 将数组中数据转为键值对结构 (这里的数组和obj会相互引用)
      list.map(el => {
        obj[el.id] = el;
        el.key = el.id;
        el.source_category = category;
        el.scopedSlots = { title: 'title' };
        el.slots = { icon: el.source_type };
        if (
          el.key.startsWith('$system') ||
          el.key.startsWith('systemAuth_') ||
          el.source_type === 'element'
        ) {
          el.checkable = false;
          this.systemNodes.push(el);
        }
        if (el.parent_id === 0) {
          el.parent_id = rootKey;
        }
        // 处理元素权限
        if (el.permissions) {
          el._showVirtualPermissions = false;
          el.permissions = el.permissions.map(item => {
            return {
              ...item,
              label: item.group_name,
              value: item.group_id
            }
          })
        }
      });
      for (let i = 0, len = list.length; i < len; i++) {
        let pid = list[i].parent_id;
        if ((pid + '').startsWith('root_')) {
          result.push(list[i]);
          result.sort((a, b) => a.sort_num - b.sort_num);
          continue;
        }
        if (obj[pid]) {
          // 给父节点添加个虚拟权限tag，目前只处理了一级
          if (list[i].permissions && !obj[pid].permissions) {
            obj[pid]._showVirtualPermissions = true;
          }
          if (obj[pid].children) {
            obj[pid].children.push(list[i]);
            if (list[i].source_type === 'element') {
              obj[pid].children.sort((a, b) =>
                (a.source_name || '').localeCompare(b.source_name || '')
              );
            } else {
              obj[pid].children.sort((a, b) => a.sort_num - b.sort_num);
            }
          } else {
            obj[pid].children = [list[i]];
          }
        }
      }
      return result;
    },
    getNodeMap() {
      if (!_.isEmpty(this.nodeMap)) {
        return this.nodeMap;
      }
      let map = {};
      const loop = (list = []) => {
        list.forEach(item => {
          map[item.key] = item;

          if (item.children && item.children.length > 0) {
            loop(item.children);
          }
        });
      };
      loop(this.treeData);
      this.nodeMap = map;
      return map;
    },
    switchForm(node) {
      const { form } = this.$refs;
      // 保存上一个节点
      // if (this.formData.id) {
      //   this.saveNode(this.formData.id);
      // }
      console.log(node, 'ssss');
      form && form.resetFields();
      // 设置结构
      this.formParams = Object.assign({}, this.formParams, {
        fields: this.config.getFields(node)
      });
      // 设置数据
      // let params = [];
      // _.forEach(node.source_ext || {}, (val, key) => {
      //   params.push({
      //     _key: key,
      //     _value: val
      //   });
      // });
      const { deal_type: dealType, _includes } = node.source_ext || {};
      let base = {};
      let extraAttrs = {};
      if (this.isEdit) {
        // 设置初始值
        _.forEach(node.source_ext || {}, (val, key) => {
          if (key.startsWith('_')) {
            if (key === '_show') {
              val = val == 1;
            }
            base[`source_ext${key}`] = val;
          } else if (
            !['data', 'init_value', 'match_key', 'deal_type'].includes(key)
          ) {
            extraAttrs[key] = val;
          }
        });
        // if (dealType === 'BACK') {
        //   base[`source_ext_backParams`] = JSON.stringify(extraAttrs);
        // }
        // dealType === 'ArrayMerge'
        if (['ArrayMerge', 'BACK'].includes(dealType)) {
          _includes == null &&
            this.$notification['error']({
              message: '数据有误，请检查',
              description: '_includes不存在',
              duration: 6
            });
          _includes === '' && console.warn('注意_includes为空，是否正常?');
        }
      }

      // this.formData = {
      //   id: node.id,
      //   source_name: node.source_name,
      //   source_type: node.source_type,
      //   source_ext: node.source_ext,
      //   ...base
      // };
      this.formData = node;
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    onCheck(checkedKeys, e) {
      // const arr = checkedKeys.checked || [];
      // const checked = e.checked;
      // const currKey = e.node.eventKey;
      // const nodeMap = this.getNodeMap();

      // // 级联父节点选中
      // const loopChooseParent = node => {
      //   if (!node) return;
      //   // if (node.key.startsWith('root_')) {
      //   //   return;
      //   // }
      //   arr.push(node.key);
      //   const pNode = nodeMap[node.parent_id];
      //   loopChooseParent(pNode);
      // };
      // // 级联子节点取消选中
      // const loopChildren = (type, node) => {
      //   if (!node) return;
      //   if (type === 'cancel') {
      //     _.remove(arr, itm => itm == node.key);
      //   } else {
      //     arr.push(node.key);
      //   }

      //   if (node.children && node.children.length > 0) {
      //     node.children.forEach(child => {
      //       loopChildren(type, child);
      //     });
      //   }
      // };
      // if (checked) {
      //   loopChooseParent(nodeMap[currKey]);
      //   if (currKey.startsWith('root_')) {
      //     loopChildren('choose', nodeMap[currKey]);
      //   }
      // } else {
      //   loopChildren('cancel', nodeMap[currKey]);
      // }
      // this.checkedKeys = [...new Set(arr)];
      const { halfCheckedKeys } = e;
      this.checkedKeys = {
        checked: checkedKeys,
        halfChecked: halfCheckedKeys
      };
    },
    selectNode(selectedKeys, e) {
      let node = e.node.dataRef;
      this.selectedKeys =
        selectedKeys.length <= 0 ? [...this.selectedKeys] : selectedKeys;
      this.selectedNode = node;

      this.switchForm(node);
    },
    saveNode(key, info) {
      // let node = this.getNodeMap()[key];
      // if (!node) return;
      // const { form } = this.$refs;
      // let ext = {};
      // if (info) {
      //   ext = info.source_ext || {};
      // } else {
      //   (form.getData().source_ext || []).forEach(item => {
      //     if (item._key !== undefined) {
      //       ext[item._key] = item._value;
      //     }
      //   });
      // }
      // // 值变更后保存
      // if (JSON.stringify(ext) !== JSON.stringify(node.source_ext)) {
      //   node.source_ext = ext;
      //   this.modifyNodes[key] = node;
      // }
    },
    onSearch(e) {
      const value = e.target.value;

      let expandedKeys = [];
      _.forEach(this.getNodeMap(), item => {
        if (item.source_name.includes(value)) {
          expandedKeys.push(item.key);
        }
      });
      this.expandedKeys = expandedKeys;
      this.searchValue = value;
      this.autoExpandParent = true;
    },
    getSubmitPermission() {
      let res = [];
      function loop(list = []) {
        list.forEach(node => {
          if (node && node.permissions) {
            res = [...res, ...node.permissions.map(item => {
              return {
                group_id: item.group_id,
                visible: (node.choose_permissions || []).includes(item.group_id)
              }
            })]
          }
          if (node.children) {
            loop(node.children);
          }
        })
      }
      loop(this.treeData);
      return res;
    },
    getData() {
      const { baseForm } = this.$refs;
      let res = {};
      // 基础信息
      res.baseInfo = baseForm.getData();
      // 授权信息
      const { checked = [], halfChecked = [] } = this.checkedKeys;
      let checkedNodes = [];
      [...checked, ...halfChecked].forEach(key => {
        checkedNodes.push(this.getNodeMap()[key]);
      });
      // console.log(this.checkedKeys, checked, halfChecked, checkedNodes);
      checkedNodes = [...new Set([...checkedNodes, ...this.systemNodes])];
      res.authInfo = {
        resource: checkedNodes
          .filter(
            node =>
              node.source_category === 'resource' &&
              !node.key.startsWith('root_') &&
              node.source_type !== 'element'
          )
          .map(item => ({
            id: item.id,
            source_ext: (this.modifyNodes[item.id] || {}).source_ext
          })),
        data: checkedNodes
          .filter(
            node =>
              node.source_category === 'data' && !node.key.startsWith('root_')
          )
          .map(item => ({
            id: item.id,
            source_ext: (this.modifyNodes[item.id] || {}).source_ext
          })),
        auth_resource_groups: this.getSubmitPermission()
      };
      return res;
    },
    validate(cbk) {
      this.$refs.baseForm.validate(valid => {
        if (valid) {
          // 先保存当前节点数据
          // if (this.formData.id) {
          //   this.saveNode(this.formData.id);
          // }
          cbk && cbk(this.getData());
        }
      });
    },
    combineData() {
      setTimeout(() => {
        const { resource = [], data = [] } = this.data;
        const allData = [...resource, ...data];
        const checkedKeys = [];
        const checked = [];
        const halfChecked = [];
        const map = {};
        const nodeMap = this.getNodeMap();
        allData.forEach(node => {
          checkedKeys.push(node.id);
          const sourceNode = nodeMap[node.id];
          if (sourceNode) {
            // Object.assign(sourceNode, node);
            sourceNode.source_ext = node.source_ext;
            if (sourceNode.parent_id != null) {
              if (map[sourceNode.parent_id]) {
                map[sourceNode.parent_id] += 1;
              } else {
                map[sourceNode.parent_id] = 1;
              }
            }
            // 处理元素权限
            if (node.choose_permissions) {
              sourceNode.choose_permissions = node.choose_permissions || [];
            }
          }
        });
        // console.log(map);
        checkedKeys.forEach(id => {
          const sourceNode = nodeMap[id];
          if (
            map[id] == null ||
            (sourceNode.children && sourceNode.children.length === map[id])
          ) {
            checked.push(id);
          } else {
            halfChecked.push(id);
          }
        });
        this.checkedKeys = {
          checked,
          halfChecked
        };
      }, 300);
    },
    onCancel() {
      this.formParams = Object.assign({}, this.formParams, {
        fields: []
      });
      this.formData = [];
    },
    onSave() {
      let ext = {
        ...this.selectedNode.source_ext,
        ...this.$refs.AttrPanel.getData()
      };
      // 请求
      this.$showLoading();
      roleUpdate({
        role_id: this.baseInfoData.id,
        menu_code: this.selectedNode.id,
        source_data: ext
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ useMessage: true, tips: '属性设置成功' });
            Object.assign(this.selectedNode.source_ext, ext);
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    },
    onBatchAuth() {
      this.$refs.BatchAuthModal.show({
        menuCode: this.selectedNode.id,
        node: this.selectedNode,
        formData: _.merge({}, this.formData)
      });
    }
  },
  watch: {
    data: {
      handler(newVal = {}, oldVal) {
        // 基础信息
        this.baseInfoData = {
          ...newVal
        };
        // 合并信息
        this.combineData();
      }
      // immediate: true
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.user-role-content {
  /deep/ .common-box-card {
    .ant-card-head {
      font-size: 14px;
    }
  }
  .resource-edit-content {
    display: flex;
    align-items: stretch;
    /deep/ .ant-tree {
      max-height: calc(60vh - 48px);
      overflow: auto;

      .permission-tag, .permission-virtual-tag {
        background: #ffc107;
        padding: 0px 4px;
        border-radius: 4px;
        font-size: 12px;
        color: #fff;
      }
      .permission-virtual-tag {
        opacity: 0.4;
      }
    }
    .part-right {
      border-left: 1px solid #eef2fb;
      max-height: 60vh;
      overflow: auto;
      > div {
        // padding: 20px 40px;
        /deep/ .ant-table {
          td {
            padding-left: 0;
          }
          tr {
            td:last-child {
              > span {
                position: relative;
                top: -4px;
                white-space: nowrap;
              }
            }
          }
        }
      }
      .part-right-btns {
        text-align: right;
        border-top: 1px solid #eef2fb;
        // padding-top: 12px;
        position: sticky;
        bottom: 0;
        background: #ffffff;
        padding: 12px;
        button {
          margin-left: 4px;
          font-size: 12px;
          padding: 0 12px;
        }
      }
    }
  }
}
</style>
