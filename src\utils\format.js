const SqlFormat = {
  getConfig(type) {
    const config = {
      ORACLE: {
        language: 'plsql'
      },
      MYSQL: {
        language: 'mysql'
      },
      TIDB: {
        language: 'mysql'
      },
      PGSQL: {
        language: 'postgresql'
      }
    }
    return config[type.toUpperCase()] || {};
  }
};

const FormatConfig = {
  SqlFormat
}

window.FORMAT_CONFIG = FormatConfig;
export default FormatConfig;
