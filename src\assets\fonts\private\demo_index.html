<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4203271" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6b9;</span>
                <div class="name">assess</div>
                <div class="code-name">&amp;#xe6b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6bb;</span>
                <div class="name">assess-db</div>
                <div class="code-name">&amp;#xe6bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6bc;</span>
                <div class="name">assess-app</div>
                <div class="code-name">&amp;#xe6bc;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6bd;</span>
                <div class="name">flow</div>
                <div class="code-name">&amp;#xe6bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6c1;</span>
                <div class="name">datamove2</div>
                <div class="code-name">&amp;#xe6c1;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6c3;</span>
                <div class="name">subscribe</div>
                <div class="code-name">&amp;#xe6c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6c5;</span>
                <div class="name">layer</div>
                <div class="code-name">&amp;#xe6c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6c6;</span>
                <div class="name">list</div>
                <div class="code-name">&amp;#xe6c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6c7;</span>
                <div class="name">datacheck</div>
                <div class="code-name">&amp;#xe6c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6c8;</span>
                <div class="name">integrate</div>
                <div class="code-name">&amp;#xe6c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6ca;</span>
                <div class="name">dev</div>
                <div class="code-name">&amp;#xe6ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6cb;</span>
                <div class="name">datamove</div>
                <div class="code-name">&amp;#xe6cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6ce;</span>
                <div class="name">scene</div>
                <div class="code-name">&amp;#xe6ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6cf;</span>
                <div class="name">backup</div>
                <div class="code-name">&amp;#xe6cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6d0;</span>
                <div class="name">sync</div>
                <div class="code-name">&amp;#xe6d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6d1;</span>
                <div class="name">taskcircle</div>
                <div class="code-name">&amp;#xe6d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6d3;</span>
                <div class="name">flow1</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6d4;</span>
                <div class="name">assess-app1</div>
                <div class="code-name">&amp;#xe6d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6d5;</span>
                <div class="name">meter1</div>
                <div class="code-name">&amp;#xe6d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6d6;</span>
                <div class="name">integrate1</div>
                <div class="code-name">&amp;#xe6d6;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6d7;</span>
                <div class="name">assess1</div>
                <div class="code-name">&amp;#xe6d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6d8;</span>
                <div class="name">datamove1</div>
                <div class="code-name">&amp;#xe6d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6d9;</span>
                <div class="name">dev1</div>
                <div class="code-name">&amp;#xe6d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6da;</span>
                <div class="name">assess-db1</div>
                <div class="code-name">&amp;#xe6da;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6db;</span>
                <div class="name">subscribe1</div>
                <div class="code-name">&amp;#xe6db;</div>
              </li>
          
            <li class="dib">
              <span class="icon luicon-ludb">&#xe6dc;</span>
                <div class="name">datacheck1</div>
                <div class="code-name">&amp;#xe6dc;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'luicon-ludb';
  src: url('iconfont.eot?t=1691549455238'); /* IE9 */
  src: url('iconfont.eot?t=1691549455238#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('iconfont.woff2?t=1691549455238') format('woff2'),
       url('iconfont.woff?t=1691549455238') format('woff'),
       url('iconfont.ttf?t=1691549455238') format('truetype'),
       url('iconfont.svg?t=1691549455238#luicon-ludb') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.luicon-ludb {
  font-family: "luicon-ludb" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="luicon-ludb"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"luicon-ludb" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-assess"></span>
            <div class="name">
              assess
            </div>
            <div class="code-name">.lu-icon-ludb-assess
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-assess-db"></span>
            <div class="name">
              assess-db
            </div>
            <div class="code-name">.lu-icon-ludb-assess-db
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-assess-app"></span>
            <div class="name">
              assess-app
            </div>
            <div class="code-name">.lu-icon-ludb-assess-app
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-flow"></span>
            <div class="name">
              flow
            </div>
            <div class="code-name">.lu-icon-ludb-flow
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-datamove2"></span>
            <div class="name">
              datamove2
            </div>
            <div class="code-name">.lu-icon-ludb-datamove2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-subscribe"></span>
            <div class="name">
              subscribe
            </div>
            <div class="code-name">.lu-icon-ludb-subscribe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-layer"></span>
            <div class="name">
              layer
            </div>
            <div class="code-name">.lu-icon-ludb-layer
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-list1"></span>
            <div class="name">
              list
            </div>
            <div class="code-name">.lu-icon-ludb-list1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-datacheck"></span>
            <div class="name">
              datacheck
            </div>
            <div class="code-name">.lu-icon-ludb-datacheck
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-integrate"></span>
            <div class="name">
              integrate
            </div>
            <div class="code-name">.lu-icon-ludb-integrate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-dev"></span>
            <div class="name">
              dev
            </div>
            <div class="code-name">.lu-icon-ludb-dev
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-datamove"></span>
            <div class="name">
              datamove
            </div>
            <div class="code-name">.lu-icon-ludb-datamove
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-scene"></span>
            <div class="name">
              scene
            </div>
            <div class="code-name">.lu-icon-ludb-scene
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-backup"></span>
            <div class="name">
              backup
            </div>
            <div class="code-name">.lu-icon-ludb-backup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-sync"></span>
            <div class="name">
              sync
            </div>
            <div class="code-name">.lu-icon-ludb-sync
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-taskcircle"></span>
            <div class="name">
              taskcircle
            </div>
            <div class="code-name">.lu-icon-ludb-taskcircle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-flow11"></span>
            <div class="name">
              flow1
            </div>
            <div class="code-name">.lu-icon-ludb-flow11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-assess-app11"></span>
            <div class="name">
              assess-app1
            </div>
            <div class="code-name">.lu-icon-ludb-assess-app11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-meter11"></span>
            <div class="name">
              meter1
            </div>
            <div class="code-name">.lu-icon-ludb-meter11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-integrate11"></span>
            <div class="name">
              integrate1
            </div>
            <div class="code-name">.lu-icon-ludb-integrate11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-assess11"></span>
            <div class="name">
              assess1
            </div>
            <div class="code-name">.lu-icon-ludb-assess11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-datamove11"></span>
            <div class="name">
              datamove1
            </div>
            <div class="code-name">.lu-icon-ludb-datamove11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-dev11"></span>
            <div class="name">
              dev1
            </div>
            <div class="code-name">.lu-icon-ludb-dev11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-assess-db11"></span>
            <div class="name">
              assess-db1
            </div>
            <div class="code-name">.lu-icon-ludb-assess-db11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-subscribe11"></span>
            <div class="name">
              subscribe1
            </div>
            <div class="code-name">.lu-icon-ludb-subscribe11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon luicon-ludb lu-icon-ludb-datacheck11"></span>
            <div class="name">
              datacheck1
            </div>
            <div class="code-name">.lu-icon-ludb-datacheck11
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="luicon-ludb lu-icon-ludb-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            luicon-ludb" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-assess"></use>
                </svg>
                <div class="name">assess</div>
                <div class="code-name">#lu-icon-ludb-assess</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-assess-db"></use>
                </svg>
                <div class="name">assess-db</div>
                <div class="code-name">#lu-icon-ludb-assess-db</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-assess-app"></use>
                </svg>
                <div class="name">assess-app</div>
                <div class="code-name">#lu-icon-ludb-assess-app</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-flow"></use>
                </svg>
                <div class="name">flow</div>
                <div class="code-name">#lu-icon-ludb-flow</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-datamove2"></use>
                </svg>
                <div class="name">datamove2</div>
                <div class="code-name">#lu-icon-ludb-datamove2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-subscribe"></use>
                </svg>
                <div class="name">subscribe</div>
                <div class="code-name">#lu-icon-ludb-subscribe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-layer"></use>
                </svg>
                <div class="name">layer</div>
                <div class="code-name">#lu-icon-ludb-layer</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-list1"></use>
                </svg>
                <div class="name">list</div>
                <div class="code-name">#lu-icon-ludb-list1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-datacheck"></use>
                </svg>
                <div class="name">datacheck</div>
                <div class="code-name">#lu-icon-ludb-datacheck</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-integrate"></use>
                </svg>
                <div class="name">integrate</div>
                <div class="code-name">#lu-icon-ludb-integrate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-dev"></use>
                </svg>
                <div class="name">dev</div>
                <div class="code-name">#lu-icon-ludb-dev</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-datamove"></use>
                </svg>
                <div class="name">datamove</div>
                <div class="code-name">#lu-icon-ludb-datamove</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-scene"></use>
                </svg>
                <div class="name">scene</div>
                <div class="code-name">#lu-icon-ludb-scene</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-backup"></use>
                </svg>
                <div class="name">backup</div>
                <div class="code-name">#lu-icon-ludb-backup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-sync"></use>
                </svg>
                <div class="name">sync</div>
                <div class="code-name">#lu-icon-ludb-sync</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-taskcircle"></use>
                </svg>
                <div class="name">taskcircle</div>
                <div class="code-name">#lu-icon-ludb-taskcircle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-flow11"></use>
                </svg>
                <div class="name">flow1</div>
                <div class="code-name">#lu-icon-ludb-flow11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-assess-app11"></use>
                </svg>
                <div class="name">assess-app1</div>
                <div class="code-name">#lu-icon-ludb-assess-app11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-meter11"></use>
                </svg>
                <div class="name">meter1</div>
                <div class="code-name">#lu-icon-ludb-meter11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-integrate11"></use>
                </svg>
                <div class="name">integrate1</div>
                <div class="code-name">#lu-icon-ludb-integrate11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-assess11"></use>
                </svg>
                <div class="name">assess1</div>
                <div class="code-name">#lu-icon-ludb-assess11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-datamove11"></use>
                </svg>
                <div class="name">datamove1</div>
                <div class="code-name">#lu-icon-ludb-datamove11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-dev11"></use>
                </svg>
                <div class="name">dev1</div>
                <div class="code-name">#lu-icon-ludb-dev11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-assess-db11"></use>
                </svg>
                <div class="name">assess-db1</div>
                <div class="code-name">#lu-icon-ludb-assess-db11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-subscribe11"></use>
                </svg>
                <div class="name">subscribe1</div>
                <div class="code-name">#lu-icon-ludb-subscribe11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#lu-icon-ludb-datacheck11"></use>
                </svg>
                <div class="name">datacheck1</div>
                <div class="code-name">#lu-icon-ludb-datacheck11</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
