// const DbTypeMap = {
//   0: 'OR<PERSON><PERSON>',
//   1: 'MYS<PERSON>',
//   2: 'TIDB',
//   3: 'KAFKA',
//   4: 'HBASE',
//   5: 'CLICKHOUSE',
//   6: 'POSTGRES',
//   7: 'ELASTICSEARCH',
//   8: 'STARROCKS',
//   9: 'UBISQL',
//   10: 'ROSESQL'
// }
// const DbNameMap = {
//   ORACLE: 'Oracle',
//   MYSQL: 'MySQL',
//   MONGODB: 'MongoDB',
//   TIDB: 'TiDB',
//   KAFKA: 'Kafka',
//   HBASE: 'HBase',
//   CLICKHOUSE: 'ClickHouse',
//   POSTGRES: 'PostgreSQL',
//   ELASTICSEARCH: 'Elasticsearch',
//   STARROCKS: 'StarRocks',
//   UBISQL: 'UbiSQL',
//   ROSESQL: 'RoseSQL'
// }
// const getDbShowName = (dbType) => {
//   return DbNameMap[(DbTypeMap[dbType] || '').toUpperCase()];
// }

export default {
  // DbTypeMap,
  // DbNameMap,
  // getDbShowName
}