<template>
  <a-modal
    :title="`确认发送 【记录ID: ${data.id || '--'}】`"
    :width="600"
    :visible="visible"
    wrapClassName="week-report-send"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="loading">
      <Form ref="baseForm" v-bind="baseInfoParams" :formData="baseData"> </Form>
    </a-spin>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import { weeklyReportSubmit } from '@/api/weekReport';
import config from './config';

export default {
  components: { Form },
  data() {
    this.config = config(this);
    return {
      visible: false,
      loading: false,
      data: {},
      baseInfoParams: {
        fields: this.config.baseFields,
        // fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        colon: true
      },
      baseData: {}
    };
  },
  mounted() {},
  created() {},
  computed: {},
  watch: {},
  provide() {},
  methods: {
    show(record = {}) {
      this.visible = true;
      this.data = record;
      this.baseData = {
        ...record
      };
    },
    hide() {
      this.visible = false;
      this.data = {};
      this.baseData = {};
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const formData = this.$refs.baseForm.getData();
      // console.log(params);
      // 请求
      this.$showLoading();
      weeklyReportSubmit({
        busi_type: 'send-email',
        record_id: this.data.id,
        ...formData
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              useMessage: true,
              tips: `发送成功`
            });
            this.onCancel();
            this.$emit('success');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less">
.week-report-send {
}
</style>
