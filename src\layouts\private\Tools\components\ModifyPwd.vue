<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="修改密码"
    okText="修改"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
// import common from '@/utils/common';

export default {
  components: { Form },
  props: {},
  data() {
    return {
      visible: false,
      data: {},
      params: {
        layout: 'vertical',
        fields: [
          {
            type: 'InputPassword',
            label: '原密码',
            key: 'userPassword',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' }
            ]
          },
          {
            type: 'InputPassword',
            label: '新密码',
            key: 'password',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' }
            ]
          },
          {
            type: 'InputPassword',
            label: '确认新密码',
            key: 'passwordConfirm',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' },
              { validator: (rules, value, callback) => { this.handleCfmPwd(rules, value, callback); } }
            ]
          }
        ]
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.data = {};
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { data } = this;
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    },
    handleCfmPwd(rules, value, callback) {
      const { form } = this.$refs;
      let pwd = form.getData().password;
      if (pwd && pwd !== value) {
        callback(new Error('两次密码输入不一致'));
      } else {
        callback();
      }
    }
  }
};
</script>

<style lang="less" scoped>
</style>
