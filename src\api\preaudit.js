import Http from '@/utils/request';

export function createReview(params = {}) {
  return Http({
    url: `/sqlreview/review/list`,
    method: 'post',
    data: params
  });
}

// 催办
export function pressToDo(params = {}) {
  return Http({
    url: `/sqlreview/review/send-email`,
    method: 'post',
    data: params
  });
}

// 删除行
export function removeRecord(params) {
  return Http({
    url: `/sqlreview/review/del_record`,
    method: 'post',
    data: params
  });
}
// 终止行
export function terminationRecord(params) {
  return Http({
    url: `/sqlreview/review/termination_record`,
    method: 'post',
    data: params
  });
}

export function reviewRetry(params = {}) {
  return Http({
    url: `/sqlreview/review/review-retry/`,
    method: 'post',
    data: params,
    timeout: 60000 * 3
  });
}

export function fileReview(params = {}) {
  return Http({
    url: `/sqlreview/review/file_review`,
    method: 'post',
    data: params
  });
}

// 下载excel模板
export function templateDownload(params = {}) {
  return Http({
    url: `/sqlreview/project/template/download`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 获取评审详情
export function getDetail(params) {
  return Http({
    url: `/sqlreview/review/review_detail_info`,
    method: 'get',
    params: params
  })
}

// dba建议
export function saveAdvice(params) {
  return Http({
    url: `/sqlreview/review/review_comment`,
    method: 'post',
    data: params
  })
}
export default {}