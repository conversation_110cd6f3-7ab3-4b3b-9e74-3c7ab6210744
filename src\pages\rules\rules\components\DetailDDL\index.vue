<template>
  <div class="config-rules-content config-rules-content-detail">
    <!-- 预加载括号 -->
    <Bracket color="#F29339" style="display: none" />
    <!-- 基础信息 -->
    <div class="rules-content-base-info">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <a-icon type="profile" />基础信息 </span>
        <div class="base-info-box">
          <div class="info-row">
            <div>
              <span>规则类型</span>
              <span>{{ baseInfoData.rule_type }}</span>
            </div>
            <div>
              <span>数据库类型</span>
              <DbImg
                :type="baseInfoData.db_type"
                :schemaName="baseInfoData.db_type"
                class="db-type-span"
              />
            </div>
          </div>
          <div class="info-row">
            <div>
              <span>规则集</span>
              <span>{{ baseInfoData.rule_set_names || '--' }}</span>
            </div>
          </div>
          <div class="info-row">
            <div>
              <span>规则名称</span>
              <span>{{ baseInfoData.name }}</span>
            </div>
            <div>
              <span>规则描述</span>
              <span>{{ baseInfoData.desc }}</span>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <div>
      <div class="rules-content-condition" :data-row="buttonNumberConditions">
        <a-card style="width: 100%" class="common-pure-card" :bordered="false">
          <span class="title"> <a-icon type="setting" />DDL规则属性设置 </span>
          <Form
            ref="baseInfoConditions"
            class="ddl-rule-set-form"
            v-bind="baseInfoParamsConditions"
            :formData="baseInfoDataConditions"
          />
        </a-card>
      </div>
      <!-- 条件区域-->
      <div class="rules-content-condition" :data-row="buttonNumberConditions">
        <a-card style="width: 100%" class="common-pure-card" :bordered="false">
          <span class="title"> <custom-icon type="lu-icon-if" />条件区域 </span>
          <TableEdit
            ref="tableEditConditions"
            v-bind="tableParamsConditions || {}"
            :dataSource="tableDataConditions"
          ></TableEdit>
        </a-card>
      </div>
      <!--目标属性的约束条件  -->
      <div class="rules-content-condition" :data-row="buttonNumberDDL">
        <a-card style="width: 100%" class="common-pure-card" :bordered="false">
          <span class="title">
            <custom-icon type="lu-icon-if" />目标属性的约束条件
          </span>
          <TableEdit
            ref="tableEditDDL"
            v-bind="tableParamsDDL || {}"
            :dataSource="tableDataDDL"
            class="table-edit-ddl"
          ></TableEdit>
        </a-card>
      </div>
    </div>
    <!--输出结果  -->
    <div class="rules-outport-results">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <custom-icon type="lu-icon-do" />输出结果 </span>
        <div class="rules-outport-results-box">
          <div class="info-row" style="border-bottom: 0">
            <div>
              <span>风险类型</span>
              <span v-if="outportResultsData.rule_result == 0">
                <custom-icon class="warning" type="lu-icon-alarm" />
                {{ '高风险' || '--' }}
              </span>
              <span v-if="outportResultsData.rule_result == 1">
                <custom-icon class="ring" type="lu-icon-alarm" />
                {{ '低风险' || '--' }}
              </span>
            </div>
            <div>
              <span>审核结果</span>
              <span v-if="outportResultsData.rule_result == 0">{{
                '不通过' || '--'
              }}</span>
              <span v-if="outportResultsData.rule_result == 1">{{
                '通过' || '--'
              }}</span>
            </div>
          </div>
          <div :class="['suggestion', !outportResultsData.suggest && 'empty']">
            <div>优化建议</div>
            <MarkdownViewer
              class="markdown-preview"
              v-model="outportResultsData.suggest"
            ></MarkdownViewer>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>
<script>
import Form from '@/components/Form';
import TableEdit from '@/components/TableEdit';
import Select from '@/components/Select';
import Bracket from '@/components/Biz/Bracket';
import MarkdownViewer from '@/components/Markdown/viewer';
import config from '../Content/config';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Form,
    TableEdit,
    Select,
    Bracket,
    MarkdownViewer
  },
  props: {
    type: {
      type: String,
      default: 'add'
    },
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    },
    rule_type: {
      type: String,
      default: ''
    }
  },
  data() {
    this.config = config(this);
    let dbType = window.localStorage.getItem('db_type') || '';
    return {
      baseInfoData: { rule_type: this.rule_type, db_type: dbType },
      baseInfoDataConditions: { category: '' },
      buttonNumberDDL: 0,
      buttonNumberConditions: 1,
      baseInfoParamsConditions: {
        fixedLabel: true,
        gutter: 32,
        multiCols: 3,
        // labelCol: { span: 8 },
        // wrapperCol: { span: 16 },
        layout: 'horizontal',
        fields: this.config.baseInfoConditions
      },
      tableDataConditions: [],
      tableDataDDL: [],
      tableParamsDDL: {
        initEditStatus: true,
        actionBtns: this.type !== 'detail' ? ['remove'] : [],
        editConfig: this.config.condition.editConfigDDL(),
        columns: this.config.condition.columnsDDL,
        pagination: false,
        rowKey: 'id'
      },
      tableParamsConditions: {
        initEditStatus: true,
        actionBtns: this.type !== 'detail' ? ['remove'] : [],
        editConfig: this.config.condition.editConfigConditions(),
        columns: this.config.condition.columnsConditions,
        pagination: false,
        leastNum: 1,
        rowKey: 'id'
      },
      outportResultsData: {},
      ruleSetData: {},
      value: false,
      rule_setList: [],
      db_type: '',
      rulesOptions: []
    };
  },
  created() {},
  mounted() {
    // document.body.style.minWidth = '1366px';
  },
  destroyed() {
    this.$bus.$off('input-modal');
    // document.body.style.minWidth = '1024px';
  },
  methods: {
    onChange(e) {
      this.value = e.target.checked;
    }
  },
  watch: {
    dataSource: {
      handler(newVal, oldVal) {
        // const { tableEdit } = this.$refs;
        this.baseInfoData = {
          name: newVal.name,
          desc: newVal.desc,
          db_type: newVal.db_type,
          level: Number(newVal.level),
          rule_type: newVal.rule_set_type,
          rule_set_uids: newVal.rule_set_uids,
          rule_set_names:
            newVal.rule_set_names && newVal.rule_set_names.toString()
        };
        this.outportResultsData = {
          rule_result: newVal.level,
          suggest: newVal.suggest
        };
        this.baseInfoDataConditions = {
          category: newVal.rule_type,
          property: newVal.target_property
        };
        this.tableDataConditions = newVal.condition.map(item => {
          this.$set(item, 'value', item.rule_value);
          delete item.rule_value;
          return item;
        });
        this.tableDataDDL = newVal.constraint;
        this.buttonNumberDDL = newVal.constraint.length;
        this.buttonNumberConditions = newVal.condition.length;
      }
      // immediate: true,
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.config-rules-content {
  .rules-content-base-info {
    /deep/.common-pure-card {
      .ant-card-body {
        .base-info-box {
          .info-row {
            display: flex;
            padding: 15px 0;
            border-bottom: 1px solid #f2f2f2;
            &:last-child {
              border-bottom: none;
            }
            > div {
              width: 50%;
              display: flex;
              align-items: center;
              > span {
                font-size: 14px;
                font-weight: 400;
                &:first-child {
                  width: 100px;
                  color: #27272a;
                  white-space: nowrap;
                }
                &:last-child {
                  color: #a1a1aa;
                }
              }
              .db-type-span {
                .anticon {
                  font-size: 16px;
                }
                .iconText {
                  pre {
                    font-size: 14px;
                    font-weight: 400;
                    color: #a1a1aa;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .rules-content-condition {
    &:first-child,
    &:nth-child(2) {
      .common-pure-card {
        margin-bottom: 0;
        border-radius: 16px 16px 0 0;
        border-bottom: 1px solid #ebebec;
      }
    }
    &:nth-child(2) {
      .common-pure-card {
        border-radius: 0;
      }
    }
    &:last-child {
      .common-pure-card {
        border-bottom: none;
        border-radius: 0 0 16px 16px;
      }
    }
    /deep/.common-pure-card {
      .ant-card-body {
        .table-edit {
          /** 修改条件区域样式 */
          &.table-edit-ddl {
            .ant-table-tbody > tr {
              > td {
                &:nth-child(3) {
                  padding-right: 0 !important;
                  .ant-input,
                  .ant-select-selection,
                  .ant-input-number,
                  .ant-input-number-input {
                    border-radius: 0;
                    border-right: none;
                  }
                }
                &:nth-child(4) {
                  padding-right: 12px !important;
                  .ant-input,
                  .ant-select-selection,
                  .ant-input-number,
                  .ant-input-number-input {
                    border-radius: 0 8px 8px 0;
                    // border-left: 0;
                  }
                }
              }
            }
          }
          colgroup {
            > col:last-child {
              width: 0 !important;
            }
          }
          .ant-table-tbody > tr {
            .ant-select-selection {
              transition: none;
            }
            > td {
              border-bottom: 0 !important;
              &:not(:first-child):not(:last-child) {
                padding: 12px 0;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  height: 50px;
                  box-shadow: 4px 4px 3px 0px #f5f5f5;
                  border-color: #ebebec;
                }
                .ant-select-selection {
                  .ant-select-selection__rendered {
                    line-height: 48px;
                    .ant-select-selection-selected-value {
                      height: 48px;
                    }
                  }
                }
              }
              &:nth-child(2) {
                padding-left: 12px !important;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 8px 0 0 8px;
                  border-right: 0;
                  box-shadow: 0px 0px 6px 2px #e8e8e8;
                }
              }
              &:nth-child(3) {
                padding-right: 12px !important;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 0 8px 8px 0;
                  // border-left: 0;
                }
              }
              &:last-child {
                padding: 0;
                > div {
                  display: none;
                }
              }
              .biz-rule-range {
                top: 0;
                > *:not(:last-child) {
                  margin-right: 0;
                }
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  box-shadow: 0;
                }
                .ant-select:nth-child(1) {
                  .ant-select-selection {
                    border-radius: 0;
                    border-right: 0;
                  }
                }
                .ant-select:nth-child(2) {
                  .ant-select-selection {
                    border-radius: 0;
                    border-top: 1px solid #e8e8e8;
                    border-bottom: 1px solid #e8e8e8;
                    border-left: 1px solid #f5f5f5;
                    border-right: 1px solid #f5f5f5;
                  }
                }
              }
            }
            &:first-child {
              td:first-child {
                .ant-form-item {
                  visibility: hidden;
                }
              }
            }
            td:first-child {
              padding-right: 35px;
              background-color: #fff !important;
              .ant-form-item {
                position: relative;
                .ant-form-item-control {
                  position: relative;
                  top: -34px;
                }
                .ant-select {
                  width: 75px;
                  .new-style(@color) {
                    .ant-select-selection {
                      border-color: @color;
                      color: @color;
                      box-shadow: none;
                      border-radius: 16px;
                      .ant-select-arrow {
                        color: @color;
                      }
                    }
                  }
                  &.relation-and {
                    .new-style(#4cbb3a);
                  }
                  &.relation-or {
                    .new-style(#F29339);
                  }
                }
                .biz-bracket {
                  position: absolute;
                  right: -36px;
                  top: -15px;
                }
              }
            }
          }
          thead {
            display: none;
          }
          .ant-table-tbody
            > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
            > td {
            background: transparent;
          }
          .ant-table-content td {
            padding: 10px 16px;
          }
          .ant-table-placeholder {
            display: none;
          }
        }
        .ddl-rule-set-form {
          padding: 0 28px 0 28px;
          > .ant-row {
            > .ant-col {
              padding: 0 !important;
              width: 50%;
              .ant-row {
                .ant-form-item-label {
                  > label {
                    justify-content: flex-start;
                  }
                }
                .ant-form-item-control-wrapper {
                  .ant-form-item-children {
                    .ant-select-selection {
                      height: 48px;
                      box-shadow: 4px 4px 3px 0px #f5f5f5;
                      border-color: #ebebec;
                      border-right: none;
                      border-radius: 8px 0 0 8px;
                      .ant-select-selection__rendered {
                        line-height: 46px;
                      }
                    }
                  }
                }
              }
              &:last-child {
                .ant-row {
                  .ant-form-item-control-wrapper {
                    .ant-form-item-children {
                      .ant-select-selection {
                        border-radius: 0 8px 8px 0;
                        border-right: 1px solid #d9d9d9;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    /deep/ .ant-card-body {
    }
    &[data-row='1'] {
      /deep/ .table-edit {
        colgroup {
          > col:first-child {
            width: 0 !important;
          }
        }
        .ant-table-tbody {
          > tr {
            &:first-child {
              td:first-child {
                padding: 0;
                > div {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }
}
.rules-outport-results {
  /deep/.common-pure-card {
    .ant-card-body {
      .rules-outport-results-box {
        .info-row {
          display: flex;
          padding: 16px 0;
          border-bottom: 1px solid #f2f2f2;
          > div {
            width: 50%;
            display: flex;
            align-items: center;
            > span {
              font-size: 14px;
              font-weight: 400;
              &:first-child {
                width: 100px;
                color: #27272a;
              }
              &:last-child {
                color: #a1a1aa;
              }
              .warning {
                color: #e71d36;
                margin-right: 4px;
              }
              .ring {
                color: #f29339;
                margin-right: 4px;
              }
            }
            .db-type-span {
              .anticon {
                font-size: 16px;
              }
              .iconText {
                pre {
                  font-size: 14px;
                  font-weight: 400;
                  color: #a1a1aa;
                }
              }
            }
          }
        }
        .suggestion {
          > div {
            font-size: 14px;
            font-weight: 400;
            color: #27272a;
            padding: 16px 0 8px 0;
          }
          .markdown-preview {
            padding: 32px 16px;
            height: 400px;
            overflow: auto;
            // background: #f4f5f7;
            border: 1px solid #f2f2f2;
          }

          &.empty {
            .markdown-preview {
              background: #f4f5f7;
            }
          }
        }
      }
    }
  }
}
.config-rules-content-detail {
  /deep/ .ant-form-item-control-wrapper {
    &::after {
      content: '';
      background: transparent;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      border-radius: 4px;
      z-index: 1;
    }
    .ant-input-suffix,
    .ant-select-arrow {
      visibility: hidden;
    }
  }
  .base-info-form {
    /deep/ .ant-form-item-control-wrapper {
      &::after {
        bottom: 3px;
        top: 3px;
      }
    }
  }
  /deep/ .ant-input-number {
    &:hover {
      border-color: transparent;
    }
    .ant-input-number-handler-wrap {
      display: none;
    }
  }
  /** 修改条件区域样式 */
  /deep/ .ant-table-tbody > tr {
    td:first-child {
      .ant-form-item {
        .ant-form-item-control-wrapper {
          &::after {
            top: -24px;
            bottom: 24px;
          }
        }
      }
    }
  }
}
</style>
