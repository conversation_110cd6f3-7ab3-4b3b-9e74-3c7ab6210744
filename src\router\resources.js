import { routes } from './lazy';
// import authElements from './authElements';
import common from '@/utils/common.js';
console.log('原数据', routes)
// const { systemAuth } = authElements;

// 生成资源树
let map = {};
let data = [];
let treeData = [];
try {
  const root = routes.find(item => item.name === 'root');
  root.children.filter(itm => itm.isResource !== false).forEach(item => {
    const { name: key, path, meta = {} } = item;
    const { navi = [], isMenuEntry } = meta;

    // 处理data
    const menuKey = (prefix = '$menu_') => prefix + key;
    const pageKey = (prefix = '$page_') => prefix + key;
    if (path === 'null') {
      // 菜单
      data.push({
        id: menuKey(),
        desc: meta.desc,
        source_name: meta.desc,
        source_category: 'resource',
        source_type: 'menu',
        source_ext: {},
        parent_id: 'root_resource'
      });
      map[_.last(data).id] = _.last(data);
    } else if (isMenuEntry) {
      // 添加菜单节点
      data.push({
        id: menuKey('$menuItem_'),
        desc: meta.desc,
        source_name: meta.desc,
        source_category: 'resource',
        source_type: 'menu',
        source_ext: {},
        parent_id: navi.length < 2 ? 'root_resource' : '$menu_' + navi[navi.length - 2]
      });
      map[_.last(data).id] = _.last(data);
      // 添加页面实体
      data.push({
        id: pageKey(),
        desc: meta.desc,
        source_name: meta.desc,
        source_category: 'resource',
        source_type: 'page',
        source_ext: {},
        parent_id: menuKey('$menuItem_')
      });
      map[_.last(data).id] = _.last(data);
      // 权限节点
      // auth.forEach(authItem => {
      //   data.push({
      //     id: authItem.value,
      //     desc: authItem.label,
      //     source_name: authItem.label,
      //     source_category: 'resource',
      //     source_type: 'element',
      //     source_ext: {},
      //     parent_id: pageKey()
      //   });
      // });
    } else {
      // 普通页面
      data.push({
        id: pageKey(),
        desc: meta.subDesc || meta.desc,
        source_name: meta.subDesc || meta.desc,
        source_category: 'resource',
        source_type: 'page',
        source_ext: {},
        parent_id: '$menuItem_' + (meta.parent || navi[navi.length - 2])
      });
      map[_.last(data).id] = _.last(data);
      // 权限节点
      // auth.forEach(authItem => {
      //   data.push({
      //     id: authItem.value,
      //     desc: authItem.label,
      //     source_name: authItem.label,
      //     source_category: 'resource',
      //     source_type: 'element',
      //     source_ext: {},
      //     parent_id: pageKey()
      //   });
      // });
    }
  });
  treeData = common.toTree(data, { rootId: 'root_resource' });

  // 全局权限节点添加
  // const systemNode = {
  //   id: '$system',
  //   desc: '系统节点',
  //   source_name: '系统节点',
  //   source_category: 'resource',
  //   source_type: 'system',
  //   source_ext: {},
  //   parent_id: 'root_resource',
  //   children:
  //     systemAuth.map(authItem => ({
  //       id: authItem.value,
  //       desc: authItem.label,
  //       source_name: authItem.label,
  //       source_category: 'resource',
  //       source_type: 'element',
  //       source_ext: {},
  //       parent_id: '$system'
  //     }))
  // };
  // map.$system = systemNode;
  // data.push(systemNode);
  // data = [...data, ...systemNode.children];
  data = [...data];
  data.forEach(el => {
    el.sort_num = 100;
    el.scopedSlots = { title: 'title' };
    el.slots = { icon: el.source_type };
  })
  // treeData.push(systemNode);
  console.log('数据map', map);
  console.log('资源节点', data);
  console.log('资源树', treeData);
} catch (e) {
  console.log(e)
}

export default {
  map,
  data,
  treeData
};