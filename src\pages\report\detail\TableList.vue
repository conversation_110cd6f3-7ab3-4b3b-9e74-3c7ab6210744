<template>
  <div>
    <PageList
      ref="PageList"
      :searchParams="searchParams"
      :tableParams="tableParams"
      :needSplitSearch="false"
    >
      <LimitLabel
        slot="project_info"
        slot-scope="{ record }"
        :label="record.project_info.project_id + '/' + record.project_info.name"
        :limit="16"
      ></LimitLabel>
      <!-- 审核结果 -->
      <span slot="ai_status" slot-scope="{ record }">
        <span v-if="record.ai_status === null">--</span>
        <a-badge :color="record.ai_status | color" :text="record.ai_status | status" />
      </span>
      <!-- 数据库类型 -->
      <span slot="data_source_type" slot-scope="{ record }">
        <a-tag v-for="(item, index) in record.data_source_type" :key="index">{{ item }}</a-tag>
      </span>
      <!-- 规则触发级别 -->
      <span slot="level_category" slot-scope="{ record }" class="level_category">
        <span>
          高风险
          <span style="color: #f73232">{{record.rule_info.level_category.high}}</span> 条
        </span>
        <a-divider type="vertical" />
        <span>
          中风险
          <span style="color: #ff9f28">{{ record.rule_info.level_category.middle }}</span> 条
        </span>
        <a-divider type="vertical" />
        <span>
          低风险
          <span style="color: #14c55f">{{ record.rule_info.level_category.low }}</span> 条
        </span>
      </span>
      <!-- 触发规则名称 -->
      <span slot="ai_comment" slot-scope="{ record }">
        <span v-if="record.rule_info.ai_comment.length === 0">--</span>
        <!-- <a-tag v-for="(item, index) in record.rule_info.ai_comment" :key="index">{{ item }}</a-tag> -->
        <LimitTags :tags="record.rule_info.ai_comment.map(item => ({ label: item }))" :limit="1"></LimitTags>
      </span>
    </PageList>
  </div>
</template>
<script>
import PageList from '@/components/PageListNew';
import LimitTags from '@/components/LimitTags';
import LimitLabel from '@/components/LimitLabel';
import config from './config';

export default {
  components: { PageList, LimitTags, LimitLabel },
  props: {
    reportTab: String
  },
  data() {
    this.config = config(this);
    this.searchData = {};
    return {
      searchParams: {
        fields: this.config.fields(),
        multiCols: 3,
        cacheKey: 'report-detail-' + this.reportTab
      },
      tableParams: {
        url:
          this.reportTab === 'beforeHand'
            ? '/sqlreview/review/report_list_review'
            : '/sqlreview/review/report_list_afterwards_review',
        reqParams: {},
        columns:
          this.reportTab === 'beforeHand'
            ? this.config.beforeHandColumns
            : this.config.afterWardsColumns,
        searchFields: this.config.fields(),
        needTools: true,
        needSearchArea: true,
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        scroll: { x: 'max-content' },
        cacheKey: 'report-detail-' + this.reportTab
      }
    };
  },
  created() {},
  mounted() {},
  methods: {
    // 查询
    search(data, params = {}) {
      const { table } = this.$refs;
      const { keep, type } = params;
      this.searchData = data || {};
      if (keep) {
        table.refreshKeep(type, data);
      } else {
        table.refresh(null, data);
      }
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      this.searchData = {};
      table.refresh();
    }
  },
  filters: {
    status(value) {
      let obj = {
        0: '未知',
        1: '通过',
        '-1': '未通过',
        2: '白名单通过',
        9: '错误'
      };
      return obj[value];
    },
    color(value) {
      let obj = {
        0: '#B0AEAE',
        1: '#52C41A',
        '-1': '#FF4D4F',
        2: '#52C41A',
        9: '#FF4D4F'
      };
      return obj[value];
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ant-table .level_category {
  > span {
    white-space: nowrap;
  }
  .ant-divider {
    margin: 0 2px;
  }
}
</style>
