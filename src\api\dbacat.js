import Http from '@/utils/request'

export function get_instance_list(params = {}) {
    return Http({
        url: `/sqlreview/dbcat/get_instance_list`,
        method: 'get',
        params: params
    });
}


export function alarm_record_list(params = {}) {
    return Http({
        url: `/sqlreview/dbcat/alarm_record_list`,
        method: 'post',
        data: params
    });
}


export function monitor_metric_list(params = {}) {
    return Http({
        url: `/sqlreview/dbcat/monitor_metric_list`,
        method: 'get',
        params: params
    });
}
// 获取参数的请求地址
export function get_vm_url(params = {}) {
    return Http({
        url: `/sqlreview/dbcat/get_vm_url`,
        method: 'get',
        params: params
    });
}
// 获取图表
// export function query_range(params = {}) {
//     return Http({
//         url: `/sqlreview/dbcat/prometheus/api/v1/query_range`,
//         method: 'post',
//         data: params
//     });
// }
// 获取参数
export function get_query_metric(params = {}) {
    return Http({
        url: `/sqlreview/dbcat/get_query_metric`,
        method: 'get',
        params: params
    });
}



export function instance_details(params = {}) {
    return Http({
        url: `/sqlreview/dbcat/instance_details`,
        method: 'post',
        data: params
    });
}


export function get_third_data_source_list(params = {}) {
    return Http({
        url: `/sqlreview/dbcat/get_third_data_source_list`,
        method: 'get',
        params: params
    });
}

