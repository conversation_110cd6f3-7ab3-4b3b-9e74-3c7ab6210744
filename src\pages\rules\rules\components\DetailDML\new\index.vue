<template>
  <div class="config-rules-content config-rules-content-detail">
    <!-- 预加载括号 -->
    <!-- <Bracket color="#F29339" style="display: none;" /> -->
    <!-- 基础信息 -->
    <div class="rules-content-base-info">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <a-icon type="profile" />基础信息 </span>
        <div class="base-info-box">
          <div class="info-row">
            <div>
              <span>规则类型</span>
              <span>{{ baseInfoData.rule_type }}</span>
            </div>
            <div>
              <span>数据库类型</span>
              <DbImg
                :type="baseInfoData.db_type"
                :schemaName="baseInfoData.db_type"
                class="db-type-span"
              />
            </div>
          </div>
          <div class="info-row">
            <div>
              <span>规则分类</span>
              <span>{{ baseInfoData.type_name }}</span>
            </div>
            <div>
              <span>规则集</span>
              <span>{{ baseInfoData.rule_set_name || '--' }}</span>
            </div>
          </div>
          <div class="info-row">
            <div>
              <span>规则名称</span>
              <span>{{ baseInfoData.name }}</span>
            </div>
            <div>
              <span>规则描述</span>
              <span>{{ baseInfoData.desc }}</span>
            </div>
          </div>
          <div class="info-row">
            <div>
              <span>风险类型</span>
              <span v-if="baseInfoData.rule_result == 1">
                <custom-icon class="warning" type="lu-icon-warning1" />警告
              </span>
              <span v-if="baseInfoData.rule_result == 0">
                <custom-icon class="ring" type="lu-icon-ring" />提示
              </span>
            </div>
            <div>
              <span>审核结果</span>
              <span v-if="baseInfoData.rule_result == 1">不通过</span>
              <span v-if="baseInfoData.rule_result == 0">通过</span>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 条件 -->
    <div class="rules-content-condition">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <custom-icon type="lu-icon-rule" />规则内容 </span>
        <div class="rules-content">
          <Rules :ruleData="ruleData" ref="rulesComp" :dbType="dbType"></Rules>
          <EditModal ref="EditModal" :dbType="dbType" />
        </div>
      </a-card>
    </div>

    <!--输出结果  -->
    <div class="rules-outport-results">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <custom-icon type="lu-icon-do" />输出结果 </span>
        <div class="rules-outport-results-box">
          <div :class="['suggestion', !outportResultsData.suggest && 'empty']">
            <div>优化建议</div>
            <MarkdownViewer
              class="markdown-preview"
              v-model="outportResultsData.suggest"
            ></MarkdownViewer>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>
<script>
import Form from '@/components/Form';
import Select from '@/components/Select';
import Rules from '@/components/Biz/RuleComps/Rules/index';
import EditModal from '../../EditModal';
import Bracket from '@/components/Biz/Bracket';
import MarkdownViewer from '@/components/Markdown/viewer';
import config from '../../ContentDML/new/config';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Form,
    Select,
    EditModal,
    Bracket,
    Rules,
    MarkdownViewer
  },
  props: {
    type: {
      type: String,
      default: 'add'
    },
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    },
    rule_type: {
      type: String,
      default: ''
    }
  },
  data() {
    this.config = config(this);
    let dbType = window.localStorage.getItem('db_type') || '';
    return {
      dbType,
      baseInfoData: { rule_type: 'DML', db_type: dbType },
      ruleSetParams: {
        fields: this.config.ruleSetFields,
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 0 },
        wrapperCol: { span: 24 }
      },
      ruleSetData: {},
      ruleData: {},
      value: false,
      rule_setList: [],
      outportResultsData: {},
      rulesOptions: []
    };
  },
  created() {},
  mounted() {
    const { EditModal } = this.$refs;
    this.$bus.$on('input-modal', data => {
      EditModal.show(this.type, data);
    });
  },
  destroyed() {
    this.$bus.$off('input-modal');
  },
  methods: {
    onChange(e) {
      this.value = e.target.checked;
    }
  },
  watch: {
    dataSource: {
      handler(newVal, oldVal) {
        this.baseInfoData = {
          name: newVal.name,
          desc: newVal.desc,
          type: newVal.type === 9 ? '待确定' : newVal.type,
          level: newVal.level,
          rule_type: newVal.rule_set_type,
          db_type: newVal.db_type,
          rule_set_uids: newVal.rule_set_uids,
          rule_set_name:
            newVal.rule_set_name && newVal.rule_set_name.toString(),
          type_name: newVal.type_name,
          rule_result: newVal.rule_result
        };
        this.outportResultsData = {
          suggest: newVal.suggest
        };
        // if (newVal.rule_set.length !== 0) {
        //   this.value = true;
        //   this.ruleSetData = {
        //     rule_set: newVal.rule_set,
        //     rule_set_name: newVal.rule_set_name
        //   };
        //   this.rule_setList = this.ruleSetData.rule_set_name || [];
        // }
        this.ruleData = newVal.details;
      },
      immediate: true
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.config-rules-content {
  .rules-content-base-info {
    /deep/ .common-pure-card {
      .ant-card-body {
        .base-info-box {
          .info-row {
            display: flex;
            padding: 15px 0;
            border-bottom: 1px solid #f2f2f2;
            &:last-child {
              border-bottom: none;
            }
            > div {
              width: 50%;
              display: flex;
              align-items: center;
              > span {
                font-size: 14px;
                font-weight: 400;
                &:first-child {
                  width: 100px;
                  color: #27272a;
                }
                &:last-child {
                  color: #a1a1aa;
                }
              }
              .db-type-span {
                .anticon {
                  font-size: 16px;
                }
                .iconText {
                  pre {
                    font-size: 14px;
                    font-weight: 400;
                    color: #a1a1aa;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .rules-content-condition {
    .rules-content {
      padding: 0 10% 0 0;
    }
  }
}
.rules-outport-results {
  /deep/ .common-pure-card {
    .ant-card-body {
      .rules-outport-results-box {
        .info-row {
          display: flex;
          padding: 16px 0;
          border-bottom: 1px solid #f2f2f2;
          > div {
            width: 50%;
            display: flex;
            align-items: center;
            > span {
              font-size: 14px;
              font-weight: 400;
              &:first-child {
                width: 100px;
                color: #27272a;
              }
              &:last-child {
                color: #a1a1aa;
              }
              .warning {
                color: #e71d36;
                margin-right: 4px;
              }
              .ring {
                color: #f29339;
                margin-right: 4px;
              }
            }
            .db-type-span {
              .anticon {
                font-size: 16px;
              }
              .iconText {
                pre {
                  font-size: 14px;
                  font-weight: 400;
                  color: #a1a1aa;
                }
              }
            }
          }
        }
        .suggestion {
          > div {
            font-size: 14px;
            font-weight: 400;
            color: #27272a;
            padding: 16px 0 8px 0;
          }
          .markdown-preview {
            padding: 32px 16px;
            height: 400px;
            overflow: auto;
            // background: #f4f5f7;
            border: 1px solid #f2f2f2;
          }

          &.empty {
            .markdown-preview {
              background: #f4f5f7;
            }
          }
        }
      }
    }
  }
}
.config-rules-content-detail {
  /deep/ .ant-form-item-control-wrapper {
    &::after {
      content: '';
      background: transparent;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      border-radius: 4px;
      z-index: 1;
    }
    .ant-input-suffix,
    .ant-select-arrow {
      visibility: hidden;
    }
  }
  .base-info-form {
    /deep/ .ant-form-item-control-wrapper {
      &::after {
        bottom: 3px;
        top: 3px;
      }
    }
  }
  /deep/ .ant-input-number {
    &:hover {
      border-color: transparent;
    }
    .ant-input-number-handler-wrap {
      display: none;
    }
  }
  /** 修改条件区域样式 */
  /deep/ .ant-table-tbody > tr {
    td:first-child {
      .ant-form-item {
        .ant-form-item-control-wrapper {
          &::after {
            top: -24px;
            bottom: 24px;
          }
        }
      }
    }
  }
}
</style>
