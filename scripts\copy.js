/*
 * @Descripttion: 
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2020-09-03 11:21:23
 */
const fs = require('fs');
const path = require('path');
const _ = require('lodash');
const config = require('./config.js');

const { copyPath } = config.frame;
const root = path.join(__dirname, '..');
const frameRoot = path.join(__dirname, '../../frame-web');
const args = process.argv.slice(2);
const action = args[0];
// console.log(root, frameRoot);
// console.log(process.argv);

// 启动拷贝
function startCopy() {
  // process.stdout.write('拷贝到frame-web...\n');
  fileList.forEach(item => {
    const _dist = action === 'toFrame' ? item.replace(root, frameRoot) : item.replace(frameRoot, root);
    remove(_dist);
    copy(item, _dist);
  });
}

// 删除
function remove(path) {
  if (fs.existsSync(path)) {
    if (fs.statSync(path).isDirectory()) {
      let files = fs.readdirSync(path);
      files.forEach((file, index) => {
        let curPath = path + '/' + file;
        remove(curPath)
      });
      fs.rmdirSync(path);
    } else {
      fs.unlinkSync(path); // 删除文件
    }
  }
}

// 拷贝
let copy = function (src, dist) {
  // 测试某个路径下文件是否存在
  if (fs.existsSync(dist)) {
    doCopy(src, dist);
  } else {
    if (fs.statSync(src).isDirectory()) {
      fs.mkdirSync(dist, {
        recursive: true
      });
      doCopy(src, dist);
    } else {
      fs.openSync(dist, 'w+');
      doCopy(src, dist);
    }
  }
}

// 执行具体拷贝
let doCopy = function (src, dist) {
  fs.stat(src, function (err, st) {
    if (err) {
      throw err;
    }
    if (st.isFile()) {
      readable = fs.createReadStream(src); // 创建读取流
      writable = fs.createWriteStream(dist); // 创建写入流
      readable.pipe(writable);
    } else if (st.isDirectory()) {
      // 读取目录
      fs.readdir(src, function (err, paths) {
        if (err) {
          throw err;
        }
        paths.forEach(function (path) {
          let _src = src + '/' + path;
          let _dist = dist + '/' + path;
          copy(_src, _dist)
        });
      });
    }
  });
}

// 解析需要拷贝的目录
let fileList = [];
const prefix = `${action === 'toFrame' ? root : frameRoot}`;
copyPath.forEach(item => {
  if (_.isPlainObject(item)) {
    // 读取目录
    const currPath = prefix + '/' + item.dir;
    const paths = fs.readdirSync(currPath);
    paths.forEach(function (path) {
      if (!item.ignore || !item.ignore.includes(path)) {
        fileList.push(currPath + '/' + path);
      }
    });
  } else {
    fileList.push(prefix + '/' + item);
  }
});
console.log('拷贝文件目录:');
console.log(fileList);
startCopy();

module.exports = {};