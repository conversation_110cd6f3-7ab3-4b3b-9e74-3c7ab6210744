<template>
  <a-spin tip="加载中" :spinning="loading">
    <div class="order-review-wraper">
      <span class="review-project-id">ID: {{ this.id }}</span>
      <div class="frame-button-wrapper">
        <div class="page-change-container">
          <span class="page-info">{{ current }}/{{ count }}</span>
          <a-button-group>
            <a-button @click="onPrev" :disabled="count < 0 || current === 1">
              <custom-icon type="left" />
            </a-button>
            <a-button
              @click="onNext"
              :disabled="count < 0 || current === count"
            >
              <custom-icon type="right" />
            </a-button>
          </a-button-group>
        </div>
        <div class="line"></div>
        <div
          :class="[
            labelAttribute || [2, 3, 4, 5, 8].includes(commentStatus)
              ? 'exist-audit-btn-box'
              : 'no-audit-btn-box'
          ]"
        >
          <div class="apply" v-if="labelAttribute">
            <custom-icon type="lu-icon-applyfor1" />
            <span>{{
              labelInfo.created_by ? labelInfo.created_by + '：' : ''
            }}</span>
            <span v-if="labelAttribute == 1">{{ '白名单' }}</span>
            <span v-else-if="labelAttribute == 2">{{ '整改中' }}</span>
          </div>
          <div class="audit" v-if="[2, 3, 4, 5, 8].includes(commentStatus)">
            <custom-icon
              v-if="[2, 4, 5, 8].includes(commentStatus)"
              type="lu-icon-right1"
            />
            <custom-icon v-if="commentStatus == 3" type="lu-icon-wrong" />
            <span>{{ labelInfo.operator_dba + '：' }}</span>
            <span v-if="[2, 4, 5, 8].includes(commentStatus)">{{
              '同意'
            }}</span>
            <span v-if="commentStatus == 3">{{ '不同意' }}</span>
          </div>
          <a-dropdown
            :disabled="!canDo && !isLeader && !isAdmin"
            v-if="(canDo || isLeader || isAdmin) && btnStatus == 'agree'"
            overlayClassName="review-agree-overlay"
            :getPopupContainer="getPopupContainer"
            class="review-agree"
          >
            <a-menu
              class="review-agree-menu"
              slot="overlay"
              @click="(event) => handleMenuClick('agree', event)"
            >
              <a-menu-item v-for="item in agreeList" v-bind:key="item.key">{{
                item.name
              }}</a-menu-item>
              <a-menu-item>
                <span
                  @click.stop="visible_show('agree')"
                  type="text"
                  key="自定义text"
                  >自定义评审</span
                >
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px" class="highlight">
              <a-icon type="file-done" />通过
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
          <a-dropdown
            :disabled="!canDo && !isLeader && !isAdmin"
            v-if="(canDo || isLeader || isAdmin) && btnStatus == 'agree'"
            overlayClassName="review-disagree-overlay"
            :getPopupContainer="getPopupContainer"
            class="review-disagree"
          >
            <a-menu
              slot="overlay"
              @click="(event) => handleMenuClick('disagree', event)"
            >
              <a-menu-item v-for="item in disagreeList" v-bind:key="item.key">{{
                item.name
              }}</a-menu-item>
              <a-menu-item>
                <span
                  @click.stop="visible_show('disagree')"
                  type="text"
                  key="自定义text"
                  >自定义评审</span
                >
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px" class="highlight">
              <a-icon type="close-circle" />不通过
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
          <a-button
            :disabled="!canDo && !isLeader && !isAdmin"
            v-if="
              (canDo || isLeader || isAdmin) &&
              btnStatus == 'pass' &&
              !(risk == null && labelAttribute == null)
            "
            style="margin-left: 8px"
            class="highlight"
            @click="onReviewPass"
            >豁免</a-button
          >
          <a-button
            :disabled="!canDo && !isLeader && !isAdmin"
            v-if="
              (canDo || isLeader || isAdmin) &&
              btnStatus == 'pass' &&
              !(risk == null && labelAttribute == null)
            "
            style="margin-left: 8px"
            class="highlight"
            @click="onReviewFail"
            >整改</a-button
          >
          <a-button
            v-if="
              btnStatus == 'again' && !(risk == null && labelAttribute == null)
            "
            style="margin-left: 8px"
            class="highlight"
            @click="onReviewAgain"
            >重新评审</a-button
          >
        </div>
      </div>

      <div>
        <!-- 头部信息 -->
        <HeaderInfo
          @addSqlTag="addSqlTag"
          @refresh="refresh"
          @saveAdvice="onSaveAdvice"
          :headerInfo="headerInfo"
          :isWhite="isWhite"
          :sqlLabelStatus="sqlLabelStatus"
          :dataSourceList="dataSourceList"
          :dataInfo="dataInfo"
          :pieOption="pieOption"
          :sqlPlanInfo="sqlPlanInfo"
          :sqlSuggest="sqlSuggest"
          :commentStatus="commentStatus"
          :dbaComment="dbaComment"
          :id="id"
          :isOrder="true"
        />
        <!-- SQL文本和执行计划 -->
        <SqlText
          @activeChange="activeChange"
          @saveNote="onSaveNote"
          :sqlMapParamsData="sqlMapParamsData"
          :tableExistFlag="tableExistFlag"
          :detaliData="detaliData"
          :activeKey="activeKey"
          :sqlList="sqlList"
          :sqlMap="sqlMap"
          :sqlHistoryCompare="sqlHistoryCompare"
          :auditType="auditType"
          :id="id"
          :isOrder="true"
          ref="sqlText"
        ></SqlText>

        <!-- 审核弹窗 -->
        <Audit ref="audit"></Audit>
        <!-- 打标弹窗 -->
        <TagModal ref="tag" @saveLabel="onSaveLabel"></TagModal>
      </div>
    </div>
    <a-modal v-model="visible" title="评审意见" @ok="handleOk">
      <a-input v-model="comment_content" placeholder="请输入" />
    </a-modal>
  </a-spin>
</template>

<script>
import HeaderInfo from '@/components/Biz/ReviewDetail/HeaderInfo';
import SqlText from '@/components/Biz/ReviewDetail/sqlText';
import aboutSqlTable from '@/pages/preaudit/review/components/AboutSqlTable';
import TagModal from '@/components/Biz/ReviewDetail/TagModal';
import NoteModal from '@/components/Biz/ReviewDetail/NoteModal';
import Audit from '@/components/Biz/AuditModel';
import common from '@/utils/common';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';

import {
  sqlAdviceInfo,
  saveSqlmapConfig,
  getDisOrAgree,
  reveiewPass,
  reveiewFail
} from '@/api/review';
import { saveLabel, saveSqlMap } from '@/api/home';
import { getDetail, saveAdvice } from '@/api/preaudit';
export default {
  components: {
    HeaderInfo,
    SqlText,
    aboutSqlTable,
    TagModal,
    NoteModal,
    Audit
  },
  mixins: [bodyMinWidth(1280)],
  data() {
    this.config = config(this);
    return {
      visible: false,
      comment_content: '', //新增备注字段
      eidt_type: '',
      // 顶部数据
      headerInfo: {},
      labelAttribute: '',
      // AI判定结果
      dataInfo: {
        ai_comment: [],
        rule_category: [] // 风险等级数据
      },
      sqlSuggest: [],
      sqlPlanInfo: [], // 索引建议
      dbaComment: [], // dba 建议
      labelInfo: {}, // dba标签
      dbaHandleComment: '', // dba手动建议
      // SQL文本和执行计划
      activeKey: 'sqlInfo',
      id: null,
      title: '',
      searchData: {}, // 上一页搜索参数
      detaliData: {}, // SQL文本和执行计划
      sqlList: [],
      sqlMap: {},
      sqlHistoryCompare: {}, // 历史版本比对
      operatorDba: null, // DBA负责人
      sqlMapParamsData: {}, // 申请白名单/申请通过数据
      pieOption: null, // 风险环形图数据
      reviewId: null,
      risk: null,
      loading: false,
      agreeList: [],
      disagreeList: [],
      filterList: [],
      isWhite: true,
      filterKey: '',
      filterName: '',
      reviewMessage: '', // 输入的建议数据
      canDo: false,
      hasBack: false,
      tableExistFlag: false,
      commentStatus: null,
      btnStatus: '',
      sqlLabelStatus: null,
      dataSourceList: [],
      current: 0,
      count: 0,
      prevId: null,
      nextId: null,
      auditType: null,
      timestamp: null,
      isCache: 0
    };
  },
  computed: {
    isLeader() {
      const user = this.$store.state.account.user || {};
      return user.role === 'leader';
    },
    isAdmin() {
      const user = this.$store.state.account.user || {};
      return user.role === 'admin';
    }
  },
  created() {},
  mounted() {
    this.searchData = this.$route.params.searchData;
    this.id = this.$route.params.id;
    getDisOrAgree()
      .then((info) => {
        this.agreeList = info.pass;
        this.disagreeList = info.fail;
        this.filterList = info.filter;
      })
      .catch((e) => {
        console.error(e);
      });
    this.refresh({ id: this.id });
  },
  destroyed() {},
  methods: {
    getPopupContainer() {
      return document.getElementById('rootContent');
    },
    // 获取详情数据
    refresh(params) {
      this.loading = true;
      params.params = this.searchData;
      params.is_cache = this.isCache;
      params.timestamp = this.timestamp;
      getDetail(params)
        .then((reuslts) => {
          if (_.get(reuslts, 'data.code') == 0) {
            const res = _.get(reuslts, 'data.data');
            this.loading = false;
            this.auditType = res.audit_type;
            // 上下条数据
            this.nextId = res.next_id;
            this.prevId = res.prev_id;
            this.current = res.curr_idx;
            this.count = res.count;
            this.timestamp = res.timestamp;
            this.isCache = 1;

            this.reviewId = res.review_id;
            this.plan = `${res.left}/${res.right}已评估`;
            // headerInfo 组件数据
            this.isWhite = res.is_white_list;
            this.sqlLabelStatus = res.sql_label_status;
            this.dataSourceList = res.datasource_list;
            this.headerInfo = {
              label_attribute: res.label_attribute,
              label_status: res.label_status,
              label_obj_id: res.label_obj_id,
              project_name: res.project_name,
              file_path: res.file_path,
              file_name: res.file_name,
              index: res.index,
              count: res.count,
              comment_status: res.comment_status,
              audit_status: res.audit_status,
              db_type: res.db_type,
              sql_frame: res.sql_frame,
              rule_set: res.rule_set
            };
            this.dbaComment = res.dba_comment;
            this.labelInfo = {
              ...res.label_info,
              label_attribute: res.label_attribute,
              ai_status: res.ai_status,
              operator_dba: res.operator_dba,
              comment_content: res.comment_content,
              dba_comment: res.dba_comment,
              updated_at: res.updated_at,
              comment_status: res.comment_status
            };
            this.pieOption = this.config.pieOption({
              data: res.rule_category || []
            });
            this.id = res.id;
            this.commentStatus = res.comment_status;
            this.sqlSuggest = _.get(res, 'sql_suggest') || [];
            this.dataInfo.ai_comment = _.get(res, 'ai_comment') || [];
            this.dataInfo.ai_status = res.ai_status;
            this.dataInfo.risk = res.risk;
            this.risk = res.risk;
            this.dataInfo.rule_category = res.rule_category;

            // sqlText组件数据
            this.detaliData = res;
            this.sqlList = res.sql_list || [];
            this.tableExistFlag = res.table_exist_flag;
            this.operatorDba = res.operator_dba;
            this.sqlMap = res.sql_map || {};
            this.sqlHistoryCompare = res.sql_history_compare || {};
            // sqlmap参数
            let sqlMapParamsData = {};
            // 申请白名单数据
            res.sql_map_config
              ? (sqlMapParamsData = res.sql_map_config)
              : (sqlMapParamsData.sqlmap_white_list = 1);
            if (
              !sqlMapParamsData.sqlmap_monthly_increase ||
              sqlMapParamsData.sqlmap_monthly_increase.length <= 0
            ) {
              sqlMapParamsData.sqlmap_monthly_increase =
                res.sql_map_table || [];
            }
            this.sqlMapParamsData = { ...sqlMapParamsData, id: this.id };

            const btnStatus = res.comment_status;
            if ([2, 3, 4, 5, 8].includes(btnStatus)) {
              this.btnStatus = 'again'; // 重新评审
            } else if ([6, 9].includes(btnStatus)) {
              this.btnStatus = 'agree'; // 同意不同意
            } else if ([0, 1, 7].includes(btnStatus)) {
              this.btnStatus = 'pass'; // 豁免整改
            }

            // 建议数据
            this.reviewMessage = res.dba_comment;
            this.getSqlAdviceInfoData(this.id);
            this.setNavi();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch((e) => {
          this.loading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 获取索引建议数据
    getSqlAdviceInfoData(id) {
      sqlAdviceInfo({ id })
        .then((res) => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            this.sqlPlanInfo = res.data.data || [];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch((e) => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    activeChange(data) {
      this.activeKey = data;
    },
    // 建议保存
    onSaveAdvice(data) {
      this.loading = true;
      saveAdvice({
        id: this.id,
        dba_comment: data
      })
        .then((e) => {
          this.loading = false;
          if (e.data.code === 0) {
            this.loading = false;
            this.$message.success(e.data.message || '成功');
            this.refresh({ id: this.id }, true);
          } else {
            this.loading = false;
            this.$message.warn(e.data.message || '系统错误');
          }
        })
        .catch((e) => {
          this.loading = false;
        });
    },
    // 上一条
    onPrev() {
      this.activeKey = 'sqlInfo';
      this.refresh({ id: this.prevId, is_cache: 1, timestamp: this.timestamp });
    },
    // 下一条
    onNext() {
      this.activeKey = 'sqlInfo';
      this.refresh({ id: this.nextId, is_cache: 1, timestamp: this.timestamp });
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'orderDetail') {
          path = sourcePath.replace(':id', this.reviewId);
        }
        return path;
      });

      if (this.id !== this.$route.params.id) {
        this.$router.push({
          name: 'orderReview',
          params: { id: this.id, params: this.searchData }
        });
      }
    },
    saveSqlMapParams(data) {
      saveSqlmapConfig({ ...data, id: this.id })
        .then((res) => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            setTimeout(() => {
              if (this.headerInfo.index < this.headerInfo.count) {
                this.onNext();
              }
            }, 2000);
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch((e) => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    visible_show(e) {
      this.eidt_type = e;
      this.visible = true;
      this.comment_content = '';
    },
    handleOk() {
      console.log(this.eidt_type);
      if (this.comment_content) {
        if (this.eidt_type == 'agree') {
          this.loading = true;
          reveiewPass({
            id: this.id,
            // dba_comment: this.reviewMessage,
            review_comment: '',
            comment_content: this.comment_content
          })
            .then((e) => {
              this.visible = false;
              this.loading = false;

              if (e !== true) {
                this.$message.warn(e.message || '系统错误');
              } else {
                this.refresh({ id: this.id }, true);
              }
            })
            .catch((e) => {
              console.error(e);
              this.loading = false;
            });
        } else {
          this.loading = true;
          reveiewFail({
            id: this.id,
            // dba_comment: this.reviewMessage,
            review_comment: '',
            comment_content: this.comment_content
          })
            .then((e) => {
              this.loading = false;
              this.visible = false;
              if (e !== true) {
                this.$message.warn(e.message || '系统错误');
              } else {
                this.refresh({ id: this.id }, true);
              }
            })
            .catch((e) => {
              console.error(e);
              this.loading = false;
            });
        }
      }
      // this.visible = false;
    },
    handleMenuClick(type, args) {
      console.log(type, args);
      // 过滤的时候需要调整
      if (type === 'filter') {
        let map = this.filterList.find((it) => {
          return it.key === args.key;
        });

        this.filterKey = map.key;
        this.filterName = map.name;
        this.refresh({
          id: this.id,
          query: this.filterKey
        });
      } else {
        if (type === 'agree') {
          this.loading = true;
          reveiewPass({
            id: this.id,
            // dba_comment: this.reviewMessage,
            review_comment: 1,
            review_comment: args.key
          })
            .then((e) => {
              this.loading = false;
              if (e !== true) {
                this.$message.warn(e.message || '系统错误');
              } else {
                this.refresh({ id: this.id }, true);
              }
            })
            .catch((e) => {
              console.error(e);
              this.loading = false;
            });
        } else {
          this.loading = true;
          reveiewFail({
            id: this.id,
            // dba_comment: this.reviewMessage,
            review_comment: 1,
            review_comment: args.key
          })
            .then((e) => {
              this.loading = false;
              if (e !== true) {
                this.$message.warn(e.message || '系统错误');
              } else {
                this.refresh({ id: this.id }, true);
              }
            })
            .catch((e) => {
              console.error(e);
              this.loading = false;
            });
        }
      }
    },
    onReviewPass() {
      this.loading = true;
      reveiewPass({
        id: this.id
      })
        .then((e) => {
          this.loading = false;
          if (e !== true) {
            this.$message.warn(e.message || '系统错误');
          } else {
            this.refresh({ id: this.id });
          }
        })
        .catch((e) => {
          console.error(e);
          this.loading = false;
        });
    },
    onReviewFail() {
      this.loading = true;
      reveiewFail({
        id: this.id
      })
        .then((e) => {
          this.loading = false;
          if (e !== true) {
            this.$message.warn(e.message || '系统错误');
          } else {
            this.refresh({ id: this.id });
          }
        })
        .catch((e) => {
          console.error(e);
          this.loading = false;
        });
    },
    // 重新评审
    onReviewAgain() {
      if (this.labelAttribute) {
        this.btnStatus = 'agree';
      } else {
        this.btnStatus = 'pass';
      }
    },
    // 点击表结构信息执行
    showInfoModal(name) {
      this.$refs.sqlText.showInfoModal(name);
    },
    // 点击申请表白名单执行
    showAddModal(data) {
      this.$refs.sqlText.clickApply(data);
    },
    // 打标
    addSqlTag() {
      this.$refs.tag.show();
    },
    // 打备注
    addNote() {
      this.$refs.note.show();
    },
    // 保存打标
    onSaveLabel(data) {
      this.$showLoading();
      const params = {
        operate_label_type: data.label_attribute,
        id: [this.id]
      };
      saveLabel(params)
        .then((res) => {
          if (_.get(res, 'data.code') == 0) {
            this.refresh({ id: this.id });
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch((e) => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 保存SQL备注
    onSaveNote(data = {}) {
      this.$showLoading({ useProgress: true });
      data.id = this.id;
      saveSqlMap(data)
        .then((res) => {
          if (_.get(res, 'data.code') == 0) {
            this.refresh({ id: this.id });
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch((e) => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 返回
    toBack() {
      this.$router.push({
        name: 'orderDetail',
        params: { id: this.reviewId }
      });
    }
  },
  watch: {
    '$store.state.account.user': {
      handler(newVal = {}) {
        let roleArr = ['dba', 'virtual_dev'];
        this.canDo = roleArr.includes(newVal.role);
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.order-review-wraper {
  color: rgba(86, 87, 89, 1);
  .review-project-id {
    .frame-fixed-top-left(-43px, 220px);
    background: #ffffff;
    border: 1px solid #e4e4e7;
    border-radius: 24px;
    display: block;
    padding: 2px 8px;
  }
  .frame-button-wrapper {
    display: flex;
    top: -45px !important;
    right: 0 !important;
    .page-change-container {
      .page-info {
        margin-right: 12px;
      }
      .ant-btn-group {
        .ant-btn {
          width: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          &:first-child {
            border-radius: 6px 0 0 6px !important;
          }
          &:last-child {
            border-radius: 0 6px 6px 0 !important;
          }
          .anticon {
            font-size: 12px;
          }
        }
      }
    }
    .line {
      width: 1px;
      height: 32px;
      margin: 0 8px 0 16px;
      background: #f0f0f0;
    }
    .exist-audit-btn-box {
      display: flex;
      align-items: center;
      padding: 4px 4px 4px 8px;
      background: #fef5ec;
      border: 1px solid #ffdcbb;
      border-radius: 6px;
      position: relative;
      top: -4px;
      .apply {
        margin-right: 16px;
        .anticon {
          font-size: 16px;
        }
      }
      .audit {
        margin-right: 8px;
        .anticon {
          font-size: 16px;
        }
      }
    }
    .no-audit-btn-box {
      display: flex;
      align-items: center;
      padding: 4px 4px 4px 8px;
      background: transparent;
      border: none;
      border-radius: 6px;
      position: relative;
      top: -2px;
    }
  }
}
.review-agree-overlay {
  .ant-dropdown-menu {
    background: #fff;
    .ant-dropdown-menu-item {
      // color: #fff;
      color: #008adc;
      &.ant-dropdown-menu-item-active {
        background: #fff;
      }
    }
  }
}
.review-disagree-overlay {
  .ant-dropdown-menu {
    background: #fff;
    .ant-dropdown-menu-item {
      // color: #fff;
      color: #008adc;
      &.ant-dropdown-menu-item-active {
        background: #fff;
      }
    }
  }
}
</style>