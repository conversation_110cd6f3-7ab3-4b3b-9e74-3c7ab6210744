<template>
  <div class="export-of-statistics-info">
    <a-spin :spinning="spinning" v-if="current == 1" class="export-content">
      <div class="form-area">
        <Form ref="form" class="export-form" v-bind="formParams" :formData="formData">
          <!-- 源数据库 -->
          <CheckItem
            slot="data_source_id"
            ref="source"
            type="dataSource"
            :tips="tips"
            :errorData="errorData"
            :id="dataSourceId"
            @checkAuth="checkAuth"
            @change="dataSourceChange"
            @copy="copy"
          />
          <!-- 目标数据库 -->
          <CheckItem
            slot="to_data_source_id"
            ref="target"
            type="toDataSource"
            :tips="targetTips"
            :errorData="targetErrorData"
            :id="toDataSourceId"
            @checkAuth="onCheckAuth"
            @change="toDataSourceChange"
            @copy="onCopy"
          />
          <TableEdit
            ref="table"
            v-bind="tableParams || {}"
            :dataSource="dataSource"
            slot="schema_list"
            @selectChange="selectChange"
          ></TableEdit>
        </Form>
      </div>
      <div class="btn-area">
        <a-button type="primary" @click="exportNow">导出</a-button>
      </div>
    </a-spin>
    <Progress ref="progress" v-if="current == 2" @carryOn="carryOn" type="导出" />
    <Result
      ref="result"
      v-if="current == 3"
      :taskId="taskId"
      :resultData="resultData"
      :type="exportType == 1 ? '导出到目标数据库' : '导出到文件'"
      @carryOn="carryOn"
    />
    <SubmitModal ref="submit" @reset="reset" />
  </div>
</template>
<script>
import config from './config';
import Form from '@/components/Form';
import TableEdit from '@/components/TableEdit';
import Progress from './components/Progress';
import Result from './components/Result';
import SubmitModal from './components/SubmitModal';
import CheckItem from './components/CheckItem.vue';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import {
  checkDatabase,
  exportOracleStatSql,
  getImportOrExportDetail
} from '@/api/config/dataSource';
export default {
  mixins: [bodyMinWidth(1280)],
  components: { Form, TableEdit, Progress, Result, CheckItem, SubmitModal },
  props: {},
  data() {
    this.config = config(this);
    this.reqCancelHandler = null; // 取消请求句柄
    return {
      current: 1, // 1导入页面， 2导入中， 3导入成功、失败
      activeKey: '',
      dataSource: [],
      spinning: false,
      tableParams: {
        url: '/sqlreview/project/oracle_schema',
        reqParams: {},
        initEditStatus: true,
        rowKey: 'id',
        // editConfig: this.config.exportEditConfig(),
        editConfig: {},
        columns: this.config.exportColumns(),
        rowSelection: {
          selectedRowKeys: [],
          getCheckboxProps: record => ({
            props: {
              disabled: !record.to_schema
            }
          })
        },
        pagination: {
          showSizeChanger: false
        }
      },
      formData: {},
      formParams: {
        layout: 'horizontal',
        colon: false,
        fields: this.config.exportFields()
      },
      tips: 2,
      errorData: [],
      targetTips: 2,
      targetErrorData: [],
      fields1: this.config.importFields(),
      fields2: this.config.fields(),
      dataSourceId: null,
      toDataSourceId: null,
      exportType: null,
      selectedRowKeys: [],
      selectedRows: [],
      status: null,
      resultData: {},
      taskId: null,
      isCheck: false,
      isTargetAuthCheck: false
    };
  },
  computed: {},
  created() {},
  mounted() {},
  beforeDestroy() {
    this.cancel();
  },
  methods: {
    // 源数据库权限检测
    checkAuth() {
      this.spinning = true;
      const { form } = this.$refs;
      const data = form.getData();
      checkDatabase({ data_source_id: data.data_source_id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.spinning = false;
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.tips = 1;
            this.isCheck = true;
          } else if (res.data.code == 4000) {
            this.tips = 0;
            this.spinning = false;
            this.errorData = _.get(res, 'data.data');
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 目标数据库权限检测
    onCheckAuth() {
      if (!this.isCheck) {
        this.$message.warning('请先检测源数据库权限');
        return;
      }
      this.spinning = true;
      const { form } = this.$refs;
      const data = form.getData();
      checkDatabase({ data_source_id: data.to_data_source_id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.spinning = false;
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.targetTips = 1;
            this.isTargetAuthCheck = true;
            const { form } = this.$refs;
            const data = form.getData();
            this.$set(this.tableParams, 'reqParams', {
              data_source_id: data.data_source_id || '',
              type: 'all',
              _t: +new Date()
            });
            this.$set(
              this.tableParams,
              'columns',
              this.config.exportColumns(1)
            );
            this.$set(
              this.tableParams,
              'editConfig',
              this.config.exportEditConfig(data.to_data_source_id)
            );
          } else if (res.data.code == 4000) {
            this.spinning = false;
            this.targetTips = 0;
            this.targetErrorData = _.get(res, 'data.data');
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    dataSourceChange(value) {
      this.$refs.form.saving({
        data_source_id: value,
        export_to: null,
        to_data_source_id: null
      });
      this.tips = 2;
      this.dataSourceId = value;
      this.errorData = [];
      this.dataSource = [];
      this.isCheck = false;
      this.$set(this.tableParams, 'reqParams', {});
    },
    toDataSourceChange(value) {
      const { form } = this.$refs;
      this.isTargetAuthCheck = false;
      form.saving({
        to_data_source_id: value
      });
      this.targetTips = 2;
      this.targetErrorData = [];
      this.toDataSourceId = value;
      this.$set(this.tableParams, 'reqParams', {});

      this.$set(this.tableParams.rowSelection, 'selectedRowKeys', []);
      this.selectedRowKeys = [];
    },
    // 源数据库复制错误信息
    copy() {
      CommonUtil.copy({
        value: this.errorData.toString(),
        callback: () => {
          this.$message.success('复制成功');
        }
      });
    },
    // 目标数据库复制错误信息
    onCopy() {
      CommonUtil.copy({
        value: this.targetErrorData.toString(),
        callback: () => {
          this.$message.success('复制成功');
        }
      });
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
      this.selectedRows = data.selectedRows;
      this.handleSelectChange();
    },
    // 表格勾选后，to_schema可以重选/或者清空不选，
    // exportEditConfig里面需要filter
    // this.selectedRowKeys和this.selectedRows
    // 以及重设this.$refs.form.saving
    handleSelectChange() {
      const arr = this.selectedRows.map(item => {
        return {
          schema: item.schema,
          to_schema: item.to_schema,
          export_file: item.export_file
        };
      });
      this.$refs.form.saving({
        schema_list: arr
      });
      this.$set(
        this.tableParams,
        'columns',
        this.config.exportColumns(this.exportType, this.selectedRowKeys)
      );
      this.$set(
        this.tableParams.rowSelection,
        'selectedRowKeys',
        this.selectedRowKeys
      );
    },
    // 导出
    exportNow() {
      const { form, table } = this.$refs;
      let data = form.getData();
      if (!this.isCheck) {
        this.$message.warning('请检测源数据库权限');
        return;
      }
      if (data.to_data_source_id && !this.isTargetAuthCheck) {
        this.$message.warning('请检测目标数据库权限');
        return;
      }
      let params = {};
      if (this.exportType == 1) {
        params = data;
      } else if (this.exportType == 2) {
        const arr = data.schema_list.map(item => {
          return {
            ...item,
            export_file: `slowlog_${item.schema}.sql`
          };
        });
        params = { ...data, schema_list: arr };
      }
      Promise.all([form.validate(), table.validate()]).then(valid => {
        if (valid) {
          if (_.isEmpty(this.selectedRowKeys)) {
            this.$message.warning('未选择表格数据');
            return;
          }
          this.$showLoading();
          exportOracleStatSql(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.current = 2; // 导出中
                this.$hideLoading({ duration: 0 });
                const id = _.get(res, 'data.data.task_id');
                this.taskId = id;
                this.onSetInterval({ task_id: id });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    // 手动用setTimeout 实现setInterval
    onSetInterval(params) {
      const loop = params => {
        getImportOrExportDetail(params, c => {
          this.reqCancelHandler = c;
        })
          .then(res => {
            if (CommonUtil.isSuccessCode(res)) {
              this.resultData = _.get(res, 'data.data') || {};
              this.status = _.get(res, 'data.data.status');
            } else {
              this.$message.error(_.get(res, 'data.message'));
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          })
          .finally(e => {
            clearTimeout(this.timer);
            // 取消接口请求
            if (this.reqCancelHandler) {
              this.reqCancelHandler();
            }
            if (this.status !== 0) {
              this.current = 3;
              this.cancel();
              return;
            }
            this.timer = setTimeout(() => {
              loop(params);
            }, 10000);
          });
      };
      loop(params);
    },
    // 停止计时器
    cancel() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      // 取消接口请求
      if (this.reqCancelHandler) {
        this.reqCancelHandler();
      }
    },
    // 清空数据
    clear() {
      this.current = 1;
      this.status = null;
      this.spinning = false;
      this.isCheck = false;
      this.isTargetAuthCheck = false;
      this.resultData = {};
      this.formData = {};
      this.dataSource = [];

      this.tips = 2;
      this.errorData = [];

      this.targetTips = 2;
      this.targetErrorData = [];

      this.dataSourceId = null;
      this.toDataSourceId = null;

      const { source, target } = this.$refs;
      source && source.clear();
      target && target.clear();

      this.$set(this.tableParams, 'reqParams', {});
    },
    // 继续导入
    carryOn() {
      this.cancel();
      this.clear();
    },
    // 置空选项
    reset() {
      this.$refs.form.saving({
        export_to: null
      });
    }
  }
};
</script>

<style lang="less" scoped>
.export-of-statistics-info {
  /deep/.export-content {
    padding: 30px 0 0 80px;
    .form-area {
      .ant-form {
        &.export-form {
          .ant-form-item {
            display: flex !important;
            margin-bottom: 24px;
            // align-items: center;
            .ant-form-item-label {
              width: 120px;
              height: 36px;
              line-height: 36px;
              .ant-form-item-no-colon {
                margin-left: 18px;
                justify-content: flex-start;
                > span {
                  font-size: 14px;
                  color: #27272a;
                  font-weight: 400;
                }
              }
            }
            .ant-form-item-control-wrapper {
              .table-edit {
                width: 600px;
                .ant-table-wrapper {
                  .ant-table-content {
                    border: 1px solid #ebebec;
                    .ant-table-body {
                      .ant-table-thead {
                        > tr {
                          > th {
                            border-right: 1px solid #ebebec;
                            &:last-child {
                              border-right: none;
                            }
                          }
                        }
                      }
                      .ant-table-tbody {
                        > tr {
                          > td {
                            border-bottom: 1px solid #ebebec;
                            border-right: 1px solid #ebebec;
                            padding: 8px;
                            &:last-child {
                              border-right: none;
                            }
                            .ant-row {
                              margin-bottom: 0;
                              .ant-form-item-control-wrapper {
                                .ant-form-item-control {
                                  .ant-form-item-children {
                                    .ant-select-search,
                                    .ant-select {
                                      .ant-select-selection {
                                        border: none;
                                        min-width: 200px;
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                  .ant-pagination.mini .ant-pagination-options {
                    margin-left: 0 !important;
                  }
                }
              }
            }
          }
        }
      }
    }
    .btn-area {
      padding-left: 120px;
      margin-top: 32px;
      display: flex;
      .ant-btn {
        display: flex;
        align-items: center;
        > span {
          padding: 0 24px;
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .export-of-statistics-info {
    /deep/.export-content {
      .form-area {
        .ant-form {
          &.export-form {
            .ant-form-item {
              .ant-form-item-control-wrapper {
                .table-edit {
                  width: 540px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
