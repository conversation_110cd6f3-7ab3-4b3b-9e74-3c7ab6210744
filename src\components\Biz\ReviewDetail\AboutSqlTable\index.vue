<template >
  <a-spin class="spin" :spinning="loading" v-if="tableData.length > 0">
    <a-card type="small" :bordered="false" class="small-card">
      <div class="title">
        <custom-icon type="lu-icon-list" />
        <span style="margin-left: 4px;">SQL相关表信息</span>
      </div>
      <Table v-bind="tableParams" :dataSource="tableData">
        <custom-btns-wrapper slot="action" slot-scope="{ record }" :limit="3">
          <a @click="showInfoModal(record.table_name)" actionBtn>表结构信息</a>
          <a @click="showAddModal(record)" actionBtn v-if="!isVirtualLogin">申请表白名单</a>
        </custom-btns-wrapper>
      </Table>
    </a-card>
  </a-spin>
</template>

<script>
import Table from '@/components/Table';
import config from './config';

export default {
  components: { Table },
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    this.config = config(this);
    return {
      loading: false,
      tableParams: {
        columns: this.config.tableColumns,
        pagination: false,
        rowKey: 'id',
        showHeader: true, // 是否展示表头
        // scroll: { y: 520 },
        size: 'small'
      }
      // scroll: { x: 1550 },
    };
  },
  computed: {
    isVirtualLogin() {
      let role = _.get(this.$store.state.account, 'user.role');
      return role === 'virtual_dev';
    }
  },
  mounted() {},
  methods: {
    showInfoModal(name) {
      this.$emit('showInfoModal', name);
    },
    showAddModal(data) {
      this.$emit('showAddModal', data);
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.small-card {
  /deep/ .ant-card-body {
    padding: 8px 0 0 0;
    .title {
      font-size: 14px;
      color: #27272a;
      font-weight: 400;
      padding: 6px 10px;
      background: #f4f5f7;
      .anticon {
        font-size: 16px;
        color: #000000;
      }
    }
    .custom-table {
      .ant-table-content {
        > .ant-table-header > .ant-table-thead > tr {
          > th {
            .ant-table-column-title {
              font-size: 14px;
              color: #a1a1aa;
              font-weight: 400;
            }
          }
        }
        > .ant-table-body > .ant-table-tbody > tr {
          background: #f7f7f7 !important;
          > td {
            border-bottom: 10px solid #fff;
            font-size: 14px;
            color: #27272a;
            font-weight: 400;
            .custom-btns-wrapper {
              > a {
                margin-right: 36px;
                &::after {
                  width: 0;
                  height: 0;
                }
              }
            }
          }
          &:last-child {
            > td {
              border-bottom: none;
            }
          }
          &.ant-table-row-hover {
            > td {
              background: #fbfcff;
            }
          }
          &:hover {
            > td {
              background: #fbfcff;
            }
          }
        }
      }
    }
  }
}
</style>