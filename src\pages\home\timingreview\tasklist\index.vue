<!--
 * @Author: your name
 * @Date: 2021-01-29 17:56:15
 * @LastEditTime: 2021-02-03 14:26:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/index.vue
-->
<template>
  <div class="home-timing-reivew">
    <div class="frame-button-wrapper">
      <a-button class="highlight" @click="back">返回</a-button>
    </div>
    <Table ref="table" v-bind="tableParams || {}" class="new-view-table">
      <!-- sql总数筛选 -->
      <template slot="tableTopRight">
        <div class="sql-count">
          <span class="sql-count-text">SQL总数不为0</span>
          <a-switch size="small" @change="onChange" :checked="sqlCount" />
        </div>
        <a-divider type="vertical" />
      </template>
      <!-- 列表模式 -->
      <template slot="project_name" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </template>
      <template slot="project_group" slot-scope="{ text }">
        <span class="project-group" v-if="text && text.length > 0">
          <span>{{ text[0] }}</span>
          <span v-if="text.length > 1">
            <a-tooltip>
              <template slot="title">
                <span>{{ text.toString() }}</span>
              </template>
              <span>...</span>
            </a-tooltip>
          </span>
        </span>
      </template>
      <template slot="review_point" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </template>
      <!-- DBA评审状态 -->
      <template slot="dba_status" slot-scope="{ text, record }">
        <StatusTag type="dba" :status="record.dba_status" />
      </template>
      <template slot="status" slot-scope="{ text, record }">
        <div class="status">
          <Status :status="text" :message="record.error_message"></Status>
        </div>
      </template>
      <template slot="created_by" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_creater" placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{ record.ch_creater }}</span>
          </div>
        </a-tooltip>
        <span v-else>{{ text || '--' }}</span>
      </template>
      <template slot="operator_dba" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_dba" placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{ record.ch_dba }}</span>
          </div>
        </a-tooltip>
        <span v-else>{{ text || '--' }}</span>
      </template>
      <custom-btns-wrapper
        slot="action"
        slot-scope="{ text, record }"
        :limit="3"
      >
        <a @click="toDetail(text, record, $event)" actionBtn>详情</a>
        <a @click="showReport(record)" actionBtn>报表</a>
        <a
          v-if="
            record.status === 1 ||
            record.status === 9 ||
            record.sql_count === 0 ||
            record.sql_count === null ||
            record.is_multi == 2
          "
          disabled
          actionBtn
          >提交评审</a
        >
        <a
          v-else-if="record.dba_status === '未提交' && record.is_multi !== 2"
          @click="authSubmit(record.id)"
          actionBtn
          >提交评审</a
        >
      </custom-btns-wrapper>
    </Table>
    <!-- 报表抽屉 -->
    <Report ref="Report" :routeName="routeName" :queryInfo="{ task_id: $route.params.id }"></Report>
    <!-- 提交评审弹窗 -->
    <Audit ref="audit" @refresh="refresh"></Audit>
  </div>
</template>

<script>
import Audit from '@/components/Biz/AuditModel';
import LimitTags from '@/components/LimitTags';
import Table from '@/components/Table';
import StatusTag from '@/components/Biz/Status/Tag';
import Status from '@/components/Biz/Status';
import Report from '@/pages/home/<USER>/Report';
// import common from '@/utils/common';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
export default {
  name: 'timing-review-tasklist',
  components: {
    Table,
    Status,
    Report,
    LimitTags,
    StatusTag,
    Audit
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    this.config = config(this);
    const taskId = this.$route.params.id;
    return {
      record: {},
      statusColor: this.config.statusColor,
      tableParams: {
        url: '/sqlreview/review/list1',
        reqParams: {
          sql_count: '0',
          task_id: taskId
        },
        method: 'post',
        columns: this.config.columns,
        rowKey: 'id',
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' }
      },
      starDesc: ['非常差', '差', '一般', '好', '非常好'],
      sqlCount: false,
      routeName: this.$route.name
    };
  },
  created() {
    this.setNavi();
  },
  mounted() {},
  computed: {
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  methods: {
    // 筛选sql是否为0
    onChange(data) {
      const { table } = this.$refs;
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        sql_count: data ? '1' : '0'
      });
      this.$set(table.searchParams, 'sql_count', data ? '1' : '0');
      this.sqlCount = data;
    },
    toDetail(record, e) {
      this.$router.push({
        name: 'timing-review-detail',
        params: { id: record.id },
        query: {task_id: this.$route.params.id}
      });
    },
    showReport(record) {
      this.record = record;
      this.$refs.Report.show(record);
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.refresh();
    },
    // 提交评审
    authSubmit(id) {
      this.$refs.audit.show(id);
    },
    refresh() {
      const { table } = this.$refs;
      table.refreshKeep();
    },
    setNavi() {
      // const id = this.$route.params.id;
      // common.setNavis(this, (key, sourcePath) => {
      //   let path = null;
      //   if (key === 'timing-review-tasklist') {
      //     path = sourcePath.replace(':id', id);
      //   }
      //   return path;
      // });
    },
    back() {
      this.$router.push({ name: 'timing-review' });
    }
  }
};
</script>

<style lang="less" scoped>
.home-timing-reivew {
  background: #fff;
  border-radius: 16px;
  /deep/.new-view-table {
    .search-area-wrapper {
      padding: 8px 24px 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f4f4f5;
      .custom-table-top-right {
        align-items: center;
      }
    }
    .sql-count {
      margin-right: 12px;
      display: flex;
      align-items: center;
      .sql-count-text {
        margin-right: 4px;
      }
    }
    .project-group {
      display: flex;
      > span {
        margin-right: 16px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #27272a;
        letter-spacing: 0;
        font-weight: 400;
        text-align: center;
        border: 1px solid #e4e4e7;
        border-radius: 4px;
        padding: 4px 7px;
        white-space: nowrap;
      }
    }
  }
}
</style>
