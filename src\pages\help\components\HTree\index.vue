<template>
  <div>
    <HTree :treeData="treeData" :expandedLevel="0"></HTree>
    <!-- 额外对比弹窗测试 -->
    <a-button @click="compareModalTest">对比弹窗测试</a-button>
    <CompareModal ref="CompareModal"></CompareModal>
  </div>
</template>

<script>
import HTree from '@/components/HTree';
import CompareModal from '@/components/Biz/CompareModal';
// import common from '@/utils/common';
import config from './config';
const treeData = [
  {
    label: 'ServiceA',
    value: 'ServiceA',
    color: 'orange',
    children: [
      {
        label: 'functionA',
        value: 'functionA',
        color: 'orange',
        children: [
          {
            label: 'TODO1',
            value: 'TODO1',
            color: 'red'
          },
          {
            label: 'TODO2',
            value: 'TODO2',
            color: 'red'
          },
          {
            label: 'TODO3',
            value: 'TODO3',
            color: 'green'
          }
        ]
      },
      {
        label: 'functionB',
        value: 'functionB',
        color: 'green'
      },
      {
        label: 'functionC',
        value: 'functionC',
        color: 'red',
        children: [
          {
            label: 'TODO4',
            value: 'TODO4',
            color: 'red'
          },
          {
            label: 'TODO5',
            value: 'TODO5',
            color: 'red'
          }
        ]
      }
    ]
  }
];

export default {
  components: { HTree, CompareModal },
  props: {},
  data() {
    this.config = config(this);
    return {
      treeData
    };
  },
  mounted() {},
  created() {},
  methods: {
    compareModalTest() {
      this.$refs.CompareModal.show({
        title: 'hahaahhaha',
        params: {
          value: '大幅度发的',
          valueTitle: 'abc',
          orig: 'bbbbbbbbbbbbbbbbbbb',
          origTitle: 'dddddd'
        }
      })
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>