<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal v-model="visible" title="索引建议" :footer="null" width="60%" @cancel="onCancel">
    <Table ref="table" v-bind="tableParams || {}">
      <!-- table插槽 -->
      <template slot="content" slot-scope="{ text }">
        <LimitLabel :label="text || ''" :limit="30"></LimitLabel>
      </template>
      <span slot="action" slot-scope="{ text, record }">
        <a @click="showSql(record)">查看所有sql</a>
      </span>
    </Table>
    <SqlModal ref="SqlModal"></SqlModal>
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import SqlModal from '../SqlModal';
import config from './config';
// import common from '@/utils/common'

export default {
  components: { Table, SqlModal, LimitLabel },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      tableParams: {
        url: '/sqlreview/review/index-suggest-list/',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id'
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.$set(this.tableParams, 'reqParams', {
        review_id: data.review_id
      });
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    showSql(record) {
      this.$refs.SqlModal.show(record);
    }
  }
};
</script>

<style lang="less" scoped>
</style>
