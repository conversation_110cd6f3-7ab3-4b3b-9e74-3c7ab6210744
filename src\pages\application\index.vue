<template>
  <div class="aplication-content">
    <div class="header">
      <div :class="['summary', isCollapse && 'collapse']" @click="onCollapse">
        <div class="title">
          <span>查询</span>
          <custom-icon :type="isCollapse ? 'down' : 'up'"></custom-icon>
        </div>
      </div>
      <!-- 搜索内容 -->
      <div
        :class="['search-content', isCollapse && 'collapse']"
        v-show="!isCollapse"
      >
        <SearchArea
          v-bind="searchParams || {}"
          @reset="reset"
          @search="search"
          ref="search"
          searchMode="switch"
          :iconCombine="false"
          :isShowExpandIcon="false"
          :searchData="searchData || {}"
        ></SearchArea>
      </div>
    </div>
    <div class="table-content">
      <Table
        ref="table"
        v-bind="tableParams"
        :dataSource="dataSource"
        class="new-view-table"
      >
        <template slot="tableTopLeft">
          <div>项目列表</div>
        </template>
        <template slot="tableTopRight">
          <div v-if="groupName">
            <span>项目组：</span>
            <span>{{ groupName }}</span>
          </div>
          <div v-if="dbaMgr.length > 0"  style="margin: 0 16px">
            <span>DBA负责人：</span>
            <LimitTags
              :tags="dbaMgr.map((item) => ({ label: item }))"
              :limit="1"
            ></LimitTags>
          </div>
          <div v-if="grMgr.length > 0">
            <span>项目组负责人：</span>
            <LimitTags
              :tags="grMgr.map((item) => ({ label: item }))"
              :limit="1"
            ></LimitTags>
          </div>
        </template>

        <template slot="data_source_list" slot-scope="{ text }">
          <div class="datasource-content">
            <span
              class="datasource-item"
              :key="index"
              v-for="(item, index) in text.slice(0, 1)"
            >
              <span :class="item.env == 'TEST' ? 'test' : 'prod'">{{
                item.env == 'TEST' ? '测试' : '生产'
              }}</span>
              <DbImg
                :value="item.db_type"
                :schemaName="item.name + '(' + item.db_url + ')'"
                mode="ellipsis"
              />
            </span>

            <a-popover
              overlayClassName="application-data-source-popover"
              v-if="text.length > 1"
            >
              <template slot="content">
                <span
                  class="datasource-item"
                  :key="index"
                  v-for="(item, index) in text.slice(1)"
                >
                  <span :class="item.env == 'TEST' ? 'test' : 'prod'">{{
                    item.env == 'TEST' ? '测试' : '生产'
                  }}</span>
                  <DbImg
                    :value="item.db_type"
                    :schemaName="item.name + '(' + item.db_url + ')'"
                    mode="ellipsis"
                  />
                </span>
              </template>
              <a class="more-text" style="display: inline-block">更多</a>
            </a-popover>
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>
<script>
import Table from '@/components/Table';
import SearchArea from '@/components/Biz/SearchArea/new';
import LimitTags from '@/components/LimitTags';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import { getInfo } from '@/api/application';
export default {
  mixins: [bodyMinWidth(1280)],
  components: { Table, SearchArea, LimitTags },
  props: {},
  data() {
    this.config = config(this);
    return {
      isCollapse: true,
      dataSource: [],
      tableParams: {
        url: `/sqlreview/subscribe/user_projects`,
        method: 'post',
        reqParams: {},
        isInitReq: false,
        columns: this.config.columns,
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      searchData: {},
      searchParams: {
        fields: this.config.fields
      },
      dbaMgr: [],
      grMgr: [],
      groupName: null,
      defaultGroupId: null
    };
  },
  created() {},
  mounted() {
    this.getData(true);
  },
  computed: {},
  methods: {
    getData(isFirst, params) {
      getInfo(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            this.dbaMgr = resData.dba_mgr || [];
            this.grMgr = resData.gr_mgr || [];
            this.groupName = resData.name;
            isFirst && (this.defaultGroupId = resData.id);
            this.$set(this.$data, 'searchData', {});
            this.$set(this.$data, 'searchData', {
              group_id: resData.id
            });
            this.$set(this.tableParams, 'reqParams', {
              ...params,
              group_id: resData.id
            });
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onCollapse() {
      this.isCollapse = !this.isCollapse;
    },
    // 查询
    search(data) {
      if (!data.group_id) {
        data.group_id = this.defaultGroupId;
      }
      const { table } = this.$refs;
      const { searchParams } = table;
      const params = {
        ...searchParams,
        ...data
      };
      this.getData(false, params);
    },
    // 重置
    reset() {
      this.$set(this.$data, 'searchData', {});
      this.$set(this.$data, 'searchData', { group_id: this.defaultGroupId });
      this.getData(true);
    }
  }
};
</script>

<style lang="less" scoped>
.application-content-title {
  font-size: 18px;
  font-weight: bold;
}
.content-info {
  margin: 8px 0 24px 0;
  border: none;
  width: 550px;
}
/deep/.search-area {
  .form {
    .ant-form-item-label {
      max-width: 130px !important;
    }
  }
}
.aplication-content {
  .header {
    border-radius: 16px;
    margin-bottom: 16px;
    background-color: #fff;
    .summary {
      padding: 16px 24px;
      border-radius: 16px;
      cursor: pointer;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        > span {
          font-weight: 600;
        }
        .anticon {
          font-size: 14px;
          color: #8c8c8c;
        }
      }
    }
    .search-content {
      border-top: 1px solid #f0f0f0;
      &.collapse {
        .title {
          border-bottom: none;
        }
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        > .anticon {
          font-size: 14px;
          color: #8c8c8c;
          margin-right: 0;
        }
      }
      /deep/.search-area {
        box-shadow: none;
        border: none;
      }
    }
  }
  .table-content {
    border-radius: 16px;
    background: #fff;
    /deep/.custom-table {
      .search-area-wrapper {
        display: flex;
        justify-content: space-between !important;
        align-items: center;
        padding: 12px 24px;
        border-bottom: solid 1px #fafafa;
        .custom-table-top-left {
          > div {
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #1f1f1f;
            font-weight: 600;
          }
        }
        .custom-table-top-right {
          .ant-tag {
            margin-bottom: 0;
          }
        }
      }
      .datasource-content {
        display: flex;
        align-items: center;
        .datasource-item {
          display: flex;
          align-items: center;
          > span {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            border-radius: 4px;
            margin: 0 4px 0 0;
            padding: 0 4px;
          }
          .test {
            background: #f6ffed;
            border: 1px solid rgba(183, 235, 143, 1);
            color: #52c41a;
          }
          .prod {
            background: #fff7e6;
            border: 1px solid rgba(255, 213, 145, 1);
            color: #fa8c16;
          }
          .limit-label {
            width: 120px;
          }
        }
        .more-text {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
<style lang="less" scoped>
.application-data-source-popover {
  .ant-popover-inner-content {
    .datasource-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      &.last-child {
        margin-bottom: 0;
      }
      > span {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        border-radius: 4px;
        margin: 0 4px 0 0;
        padding: 0 4px;
      }
      .test {
        background: #f6ffed;
        border: 1px solid rgba(183, 235, 143, 1);
        color: #52c41a;
      }
      .prod {
        background: #fff7e6;
        border: 1px solid rgba(255, 213, 145, 1);
        color: #fa8c16;
      }
      .limit-label {
        width: 120px;
      }
    }
    .more-text {
      margin-left: 8px;
    }
  }
}
</style>
