<template>
  <a-layout id="layout-root" :class="[mode, layout, frameClassName]">
    <!-- 左侧菜单 -->
    <a-layout-sider
      :class="collapsed ? 'menu-collapsed' : ''"
      :width="mode.startsWith('frame-inset') ? 130 : 200"
      v-model="collapsed"
    >
      <!-- <div class="logo" v-if="!collapsed">
        <img :src="logo" alt />
        <span class="project-name">{{ projectName }}</span>
      </div>-->
      <Logo :logo="logo" v-if="!collapsed" />
      <div class="logo" v-else>
        <img src="@/assets/img/private/blue.svg" alt />
      </div>
      <MenuView :collapsed="collapsed" :inlineIndent="12"></MenuView>
      <!-- <div class="custom-collapse" @click="() => onCollapse(!collapsed)"> -->
      <Collapse :collapsed="collapsed" @onCollapse="onCollapse" />
      <!-- </div> -->
    </a-layout-sider>
    <!-- 右侧 -->
    <a-layout :class="`layout-part-right ${collapsed ? 'menu-collapsed' : ''}`">
      <!-- 头部 -->
      <a-layout-header class="header">
        <div class="header-part-left">
          <!-- router信息 -->
          <a-breadcrumb
            class="router-info"
            separator=">"
            v-if="navi.length >= 2 || ignorePageTitle"
          >
            <a-breadcrumb-item v-for="(item, index) in navi" :key="item.path">
              <custom-icon :type="item.icon || 'home'" v-if="index === 0" />
              <router-link
                :to="item.path"
                v-if="item.path !== 'null' && index < navi.length - 1"
              >{{ item.name }}</router-link>
              <span style="margin-left: 0;" v-else>{{ item.name }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <!-- 顶部右侧tools -->
        <Tools v-if="!mode.startsWith('frame-inset')" @reload="reload" />
      </a-layout-header>
      <!-- 内容 -->
      <a-layout id="rootContent" :class="`content ${navi.length <= 0 && 'no-navi'}`">
        <div class="navi-wrapper" v-if="navi.length > 0">
          <!-- 刷新 -->
          <!-- <a-tooltip title="刷新" placement="bottom" v-if="navi.length > 0">
            <a-button class="icon-refresh" @click="reload" icon="sync"></a-button>
          </a-tooltip>-->
          <!-- <a class="icon-refresh" @click="reload">
            <a-icon type="sync" />刷新
          </a>-->
          <!-- 标题 -->
          <div class="page-title" v-if="!ignorePageTitle">{{ pageTitle }}</div>

          <!-- 公告 -->
          <template v-if="announce">
            <component :is="announce.name"></component>
          </template>
        </div>

        <!-- router-view -->
        <a-layout-content :class="contentType">
          <!-- <transition name="page-toggle">
            <router-view></router-view>
          </transition>-->
          <keep-alive :include="metaNavis">
            <router-view
              v-if="
                isRouterAlive &&
                  routerCache === true &&
                  $route.meta.keepAlive === true
              "
            ></router-view>
          </keep-alive>
          <router-view
            v-if="
              isRouterAlive &&
                !(routerCache === true && $route.meta.keepAlive === true)
            "
          ></router-view>
        </a-layout-content>

        <!-- footer -->
        <div class="footer">{{ currYear }}©LUFAX</div>
      </a-layout>
      <a-back-top style="right:24px" :target="getScrollTarget" />
    </a-layout>
    <!-- 额外块 -->
    <Extra />
  </a-layout>
</template>
<script>
// import { refreshToken } from '@/api/common';
import MenuView from '../../MenuView/index';
import Tools from '../../private/Tools/index';
import Logo from '../../private/Logo/index';
import Collapse from '../../private/Collapse.vue';
import Extra from '../../private/Extra/index.vue';
import { getYear } from 'date-fns';
// import { routesMap } from '@/router/lazy';
import Config from '@/utils/config';
// import Cookie from 'js-cookie';
// import _ from 'lodash';
import '../../private/index';
import mixins from '../mixins';
// import common from '@/utils/common';

export default {
  components: { MenuView, Tools, Logo, Collapse, Extra },
  props: {
    mode: String,
    layout: String,
    logo: String
  },
  mixins: [mixins],
  data() {
    return {
      collapsed: false,
      currYear: getYear(new Date()),
      contentType: '',
      // mode: mode ? 'frame-' + mode : '',
      // layout,
      projectName: Config.ProjectName,
      isRouterAlive: true,
      routerCache: Config.routerCache,
      // logo,
      announce: Config.announce
    };
  },
  computed: {
    pageTitle() {
      return _.get(this.$route, 'meta.desc');
    },
    frameType() {
      return _.get(this.$route, 'meta.frameType');
    },
    ignorePageTitle() {
      return ['ignorePageTopSpace', 'fullsize', 'pageNarrowMargins'].includes(this.frameType);
    },
    frameClassName() {
      const frameType = this.frameType;
      let res = '';
      switch (frameType) {
        case 'ignorePageTopSpace':
          res = 'ignore-page-top-space';
          break;
        case 'fullsize':
          res = 'show-header-menu-page';
          break;
        case 'pageNarrowMargins':
          res = 'page-narrow-margins';
          break;
        default:
          res = frameType;
          break;
      }
      return res;
    }
  },
  created() {},
  mounted() {
    this.$bus.$off('setPadding');
    this.$bus.$on('setPadding', data => {
      const el = document.querySelector('#layout-root >.ant-layout-sider');
      el.style.paddingBottom = 10 + data + 'px';
    });
  },
  destroyed() {},
  methods: {
    onCollapse() {
      this.collapsed = !this.collapsed;
    },
    getSelfScrollTarget() {
      return document.querySelector('#layout-root >.ant-layout');
    }
  },
  watch: {}
};
</script>
<style lang="less" scoped>
@import '../base.less';

#layout-root.left-right-new {
  // 左侧
  .ant-layout-sider {
    padding-top: 76px;
    background: linear-gradient(
      180deg,
      #003b72 51.04%,
      #2e76ab 100%
    ) !important;
    box-shadow: none;
    /deep/ .ant-layout-sider-children {
      .logo {
        display: flex;
        flex-direction: column;
        padding: 20px 12px 8px 26px !important;
        white-space: nowrap;
        text-overflow: ellipsis;
        // overflow: hidden;
        > img {
          width: 140px !important;
          margin: 8px 0 4px 0;
        }
        > span {
          display: inline;
          font-size: 12px;
          color: #3facff;
        }

        &.mode-back {
          width: 100%;
          padding: 20px !important;
          .btn-back {
            width: 100%;
            padding: 4px 0;
            height: auto;
            margin-bottom: 8px;
            border-radius: 4px;
          }
        }
      }
    }
    /deep/ .custom-menu {
      background-color: transparent;

      .ant-menu {
        background-color: transparent;
      }

      .ant-menu-sub {
        box-shadow: none;
      }

      .ant-menu-item {
        &.ant-menu-item-selected {
          background-color: #3ca3f2;
        }
        // 分割线
        &.has-divider {
          overflow: visible;
          margin-bottom: 12px;
          &::after {
            content: '';
            left: 0;
            bottom: -6px;
            top: auto;
            display: block;
            /* height: 10px; */
            /* background-color: red; */
            opacity: 1;
            transition: none;
            transform: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          }
        }
      }

      // item宽度
      .ant-menu-submenu,
      .ant-menu-item {
        width: calc(100% - 24px);
        margin: 8px 12px;
        border-radius: 4px;
      }
      // item字体颜色
      .ant-menu-item:not(.ant-menu-item-active):not(.ant-menu-item-selected) {
        color: rgba(255, 255, 255, 0.4);
      }
      .ant-menu-submenu:not(.ant-menu-submenu-active):not(.ant-menu-submenu-selected) {
        > .ant-menu-submenu-title {
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
    // 折叠
    &.menu-collapsed {
      padding: 64px 0 68px 0;
      /deep/ .ant-layout-sider-children {
        .logo {
          padding: 16px 12px 8px 24px !important;
          > img {
            transition: none;
            width: 32px !important;
          }
        }
      }
      /deep/ .custom-menu {
        // item宽度
        .ant-menu-submenu,
        .ant-menu-item {
          padding: 0 !important;
          text-align: center;
        }

        .ant-menu-submenu-title {
          padding: 0 !important;
        }
      }
    }
  }

  // 右侧
  @layout-padding-top: 70px;
  @layout-padding: 40px;
  .ant-layout.layout-part-right {
    background: #fafafa;
    padding-top: 0;
    .ant-layout-header {
      padding: 0 20px 0 40px;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      box-shadow: none;
      position: relative;

      .ant-breadcrumb a {
        color: #4db5f2;
        &:hover {
          color: @primary-color;
        }
      }
    }
    .ant-layout.content {
      padding: @layout-padding-top @layout-padding @layout-padding;
      overflow: visible;
      flex-shrink: 0;

      .navi-wrapper {
        position: absolute;
        left: @layout-padding;
        height: @layout-padding-top;
        top: 0;
      }

      .page-title {
        font-size: 28px;
        font-weight: 500;
        color: #000000;
      }
    }

    .footer {
      display: none;
    }

    // 折叠
    &.menu-collapsed {
      .ant-layout-header {
        // padding: 0 20px 0 100px;
      }
    }
  }

  // 忽略page上面高间距
  &.ignore-page-top-space {
    .ant-layout.layout-part-right {
      .ant-layout.content {
        padding: @layout-padding;
      }
    }
  }

  // 页面窄边距
  &.page-narrow-margins {
    .ant-layout.layout-part-right {
      .ant-layout.content {
        padding: 16px 24px;
      }
    }
  }

  // sql编辑器特殊处理
  &.show-header-menu-page {
    .ant-layout-sider {
      // display: none !important;
      // left: 0;
      // top: 60px;
      // bottom: 0;
    }
    // .layout-sider-mask {
    //   display: none !important;
    // }
    .layout-part-right {
      // padding-left: 0;
      // padding-left: 170px;
      #rootContent {
        // padding: 0;
        padding: 2px 0 0 8px;
      }
      .navi-wrapper {
        display: none !important;
      }

      &.menu-collapsed {
        padding-left: 80px;
      }
    }
    .footer {
      display: none;
    }
  }
  // 内嵌模式（不带菜单）
  &.frame-inset {
    .ant-layout.layout-part-right {
      .ant-layout-header {
        display: none !important;
      }
    }
  }
}
</style>
<style lang="less">
// 菜单折叠浮层
.ant-menu-dark.ant-menu-submenu-popup.layout-menu-popup-submenu {
  background: linear-gradient(180deg, #003b72 51.04%, #2e76ab 100%) !important;
  transition: none !important;
  .ant-menu-sub {
    background: transparent;
  }
  .ant-menu-item-selected {
    background-color: #3ca3f2 !important;
  }
}

#layout-root.left-right-new {
  // 外框间距变化，关联变化
  @layout-padding-top: 70px;
  @layout-padding: 40px;
  @btn-height: 36px;
  // .frame-button-wrapper,
  // .frame-button-wrapper-relative,
  // .frame-button-wrapper-relative-blank {
  //   display: flex;
  //   > .ant-btn {
  //     height: @btn-height;
  //     font-size: 14px;
  //     font-weight: 600;
  //     margin-right: 0;
  //     margin-left: 8px;
  //   }
  // }
  // .frame-button-wrapper {
  //   position: absolute;
  //   right: @layout-padding;
  //   top: 20px;
  //   z-index: 10;
  //   background: transparent;
  // }
  // .frame-button-wrapper-relative {
  //   position: absolute;
  //   right: -24px;
  //   top: -(@btn-height + 14px) - 24px;
  //   z-index: 10;
  //   background: transparent;
  // }
  // .frame-button-wrapper-relative-blank {
  //   position: absolute;
  //   right: 0px;
  //   top: -(@btn-height + 14px);
  //   z-index: 10;
  //   background: transparent;
  // }

  // // 默认样式覆盖
  // button.ant-btn {
  //   border-radius: 8px;
  // }

  // 分页新ui
  .ant-table-pagination {
    .hover-status {
      box-shadow: inset 0px 0px 0px 1px #25a7e8;
    }
    // 条数
    .ant-pagination-total-text {
      color: #a1a1aa;
    }
    // 跳至
    .ant-pagination-options-quick-jumper {
      display: none;
    }
    // pagesize
    .ant-pagination-options-size-changer {
      margin-right: 0;
    }
    // 边框
    > li {
      &.ant-pagination-item,
      &.ant-pagination-jump-prev,
      &.ant-pagination-jump-next {
        border: 1px solid #e4e4e7;
        border-right: none;
        border-radius: 0;
        margin-right: 0;
      }
    }
    // 正常页
    .ant-pagination-item {
      &:hover {
        .hover-status;
      }
      // 选中页
      &.ant-pagination-item-active {
        background: #008adc;
        border: none;
        > a {
          font-size: 14px;
          color: #ffffff;
          font-weight: 400;
        }
      }
    }
    // 向前五页, 向后五页
    .ant-pagination-jump-prev,
    .ant-pagination-jump-next {
      &:hover {
        .hover-status;
      }
    }
    // 上一页, 下一页
    .ant-pagination-prev,
    .ant-pagination-next {
      &:not(.ant-pagination-disabled) {
        > .ant-pagination-item-link {
          &:hover {
            .hover-status;
            border-color: #d9d9d9;
          }
        }
      }
    }
    .ant-pagination-prev {
      margin-right: 0;
      > .ant-pagination-item-link {
        border-radius: 4px 0 0 4px;
        border-right: none;
      }
    }
    .ant-pagination-next {
      margin-right: 0;
      > .ant-pagination-item-link {
        border-radius: 0 4px 4px 0;
      }
    }

    // size = default
    &:not(.mini) {
      @size: 38px;
      > li:not(.ant-pagination-total-text):not(.ant-pagination-options) {
        min-width: @size;
        height: @size;
        line-height: @size;
      }
      // 条数
      .ant-pagination-total-text {
        font-size: 16px;
        font-weight: 400;
        margin-right: 16px;
      }

      // 跳转内容
      .ant-pagination-options {
        .ant-pagination-options-size-changer {
          height: @size;
          .ant-select-selection--single {
            height: @size;
            .ant-select-selection__rendered,
            .ant-select-arrow {
              line-height: @size;
            }
          }
        }
      }
    }

    // size = small
    &.mini {
      @size: 32px;
      > li {
        &.ant-pagination-item,
        &.ant-pagination-jump-prev,
        &.ant-pagination-jump-next {
          border-color: #e4e4e7 !important;
        }
      }
      > li:not(.ant-pagination-total-text):not(.ant-pagination-options) {
        min-width: @size;
        height: @size;
        line-height: @size;
      }
      // 条数
      .ant-pagination-total-text {
        font-weight: 400;
        margin-right: 12px;
      }

      // 上一页, 下一页
      .ant-pagination-prev,
      .ant-pagination-next {
        > .ant-pagination-item-link {
          border-color: #d9d9d9;
        }
      }

      // 跳转内容
      .ant-pagination-options {
        margin-left: 12px;
        .ant-pagination-options-size-changer {
          height: @size;
          .ant-select-selection--single {
            height: @size;
            .ant-select-selection__rendered,
            .ant-select-arrow {
              line-height: @size;
            }
          }
        }
      }
    }
  }

  .custom-table {
    div.custom-table-tools {
      > .anticon {
        color: #27272a;
        &:hover {
          color: #fff;
          background: @primary-4;
        }
      }
    }
    .ant-table,
    .ant-table-fixed {
      .ant-table-thead {
        > tr {
          > th {
            background: #fff !important;
            font-family: PingFangSC-Regular;
            // font-size: 14px;
            color: #a1a1aa !important;
            font-weight: 400;
            border-color: #f4f4f5;
          }
        }
      }
      .ant-table-tbody > tr {
        background: #fff !important;
        > td {
          border-bottom: 1px solid #f4f5f7;
        }
        &.ant-table-row-hover {
          > td {
            background: #fbfcff;
          }
        }
        &:hover {
          > td {
            background: #fbfcff;
          }
        }
      }
    }
  }
  // 新table模式
  // .new-card-table {
  // }
}
@import './newViewTable.less';
@import './frameButtonWrapper.less';
</style>