<template>
  <!-- 数据库对象监控 -->
  <a-skeleton
    :loading="loading"
    active
    class="quater-block small-size monitor-block"
  >
    <div class="title">数据库对象监控</div>
    <div class="monitor-info" v-for="(item, index) in monitorInfo" :key="index">
      <div>
        <custom-icon :type="item.icon"></custom-icon>
        <span>{{ item.label }}</span>
      </div>
      <div>
        <div class="num">{{ item.value || 0 }}</div>
        <div class="chart-block">
          <Chart
            :option="
              item.chartType == 'line'
                ? monitorLineOption(item.chartData)
                : monitorBarOption(item.chartData)
            "
            ref="chart"
          />
        </div>
      </div>
    </div>
    <a-empty description="敬请期待" :image="image"></a-empty>
  </a-skeleton>
</template>

<script>
import Chart from '@/components/Chart';
import config from './config';
export default {
  components: { Chart },
  props: {
    monitorInfo: Array,
    loading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    this.config = config(this);
    const img = require('@/assets/img/private/table-empty.svg');
    return {
      image: img,
      monitorLineOption: data => this.config.monitorLineOption(data),
      monitorBarOption: data => this.config.monitorBarOption(data)
    };
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.monitor-block {
  height: 420px;
  width: 25%;
  margin-right: 16px;
  padding: 16px 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.05);
  .title {
    font-size: 16px;
    color: #1f1f1f;
    padding-bottom: 16px;
    font-weight: bold;
  }
  .monitor-info {
    padding: 4px 2px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    > div:first-child {
      .anticon {
        font-size: 16px;
        color: #1f1f1f;
        font-weight: 600;
      }
      span {
        font-size: 14px;
        color: #1f1f1f;
        margin-left: 8px;
      }
    }
    > div:last-child {
      display: flex;
      align-items: center;
      .chart-block {
        height: 48px;
        width: 60px;
      }
      .num {
        font-size: 20px;
        color: #1f1f1f;
      }
    }
  }
  .ant-empty {
    height: 80%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
</style>