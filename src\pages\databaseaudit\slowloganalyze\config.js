import moment from 'moment';
export default function (ctx) {
  const columns = [
    {
      title: '',
      dataIndex: 'checksum',
      key: 'checksum',
      scopedSlots: { customRender: 'checksum' },
      width: 180
    },
    {
      title: '',
      dataIndex: 'user',
      key: 'user',
      scopedSlots: { customRender: 'user' },
      width: 130
    },
    {
      title: '',
      key: 'db',
      dataIndex: 'db',
      scopedSlots: { customRender: 'db' },
      width: 120
    },
    {
      title: '',
      key: 'sql_fingerprint',
      dataIndex: 'sql_fingerprint',
      scopedSlots: { customRender: 'sql_fingerprint' },
      width: 120
    },
    {
      title: '',
      dataIndex: 'query_count',
      key: 'query_count',
      width: 200
    },
    {
      title: '',
      key: 'time_range',
      dataIndex: 'time_range',
      width: 300,
      scopedSlots: { customRender: 'time_range' }
    },
    {
      title: '',
      dataIndex: 'query_time_sum',
      key: 'query_time_sum',
      scopedSlots: { customRender: 'query_time_sum' },
      width: 120
    },
    {
      title: '',
      key: 'query_time_min',
      dataIndex: 'query_time_min',
      width: 300,
      scopedSlots: { customRender: 'query_time_min' }
    },
    {
      title: '',
      key: 'query_time_max',
      dataIndex: 'query_time_max',
      width: 300,
      scopedSlots: { customRender: 'query_time_max' }
    },
    {
      title: '',
      key: 'query_time_pct_95',
      dataIndex: 'query_time_pct_95',
      width: 300,
      scopedSlots: { customRender: 'query_time_pct_95' }
    },
    {
      title: '',
      dataIndex: 'query_time_stddev',
      key: 'query_time_stddev',
      scopedSlots: { customRender: 'query_time_stddev' },
      width: 120
    },
    {
      title: '',
      key: 'lock_time_sum',
      dataIndex: 'lock_time_sum',
      width: 300,
      scopedSlots: { customRender: 'lock_time_sum' }
    },
    {
      title: '',
      key: 'lock_time_min',
      dataIndex: 'lock_time_min',
      width: 300,
      scopedSlots: { customRender: 'lock_time_min' }
    },
    {
      title: '',
      key: 'lock_time_max',
      dataIndex: 'lock_time_max',
      width: 300,
      scopedSlots: { customRender: 'lock_time_max' }
    },
    {
      title: '',
      dataIndex: 'lock_time_pct_95',
      key: 'lock_time_pct_95',
      scopedSlots: { customRender: 'lock_time_pct_95' },
      width: 120
    },
    {
      title: '',
      key: 'lock_time_stddev',
      dataIndex: 'lock_time_stddev',
      width: 300,
      scopedSlots: { customRender: 'lock_time_stddev' }
    },
    {
      title: '',
      key: 'lock_time_median',
      dataIndex: 'lock_time_median',
      width: 300,
      scopedSlots: { customRender: 'lock_time_median' }
    },
    {
      title: '',
      dataIndex: 'rows_sent_sum',
      key: 'rows_sent_sum',
      scopedSlots: { customRender: 'rows_sent_sum' },
      width: 120
    },
    {
      title: '',
      dataIndex: 'rows_sent_min',
      key: 'rows_sent_min',
      scopedSlots: { customRender: 'rows_sent_min' },
      width: 120
    },
    {
      title: '',
      dataIndex: 'rows_sent_max',
      key: 'rows_sent_max',
      scopedSlots: { customRender: 'rows_sent_max' },
      width: 120
    },
    {
      title: '',
      dataIndex: 'rows_sent_pct_95',
      key: 'rows_sent_pct_95',
      scopedSlots: { customRender: 'rows_sent_pct_95' },
      width: 120
    },
    {
      title: '',
      dataIndex: 'rows_sent_stddev',
      key: 'rows_sent_stddev',
      scopedSlots: { customRender: 'rows_sent_stddev' },
      width: 120
    },
    {
      title: '',
      dataIndex: 'rows_sent_median',
      key: 'rows_sent_median',
      scopedSlots: { customRender: 'rows_sent_median' },
      width: 120
    },
    {
      title: '',
      dataIndex: 'rows_examined_sum',
      key: 'rows_examined_sum',
      scopedSlots: { customRender: 'rows_examined_sum' },
      width: 120
    },
    {
      title: '风险等级',
      key: 'risk',
      dataIndex: 'risk',
      width: 300,
      scopedSlots: { customRender: 'risk' }
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 170,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });

  const searchFields = (data = {}) => {
    const extraArr = [
      {
        type: 'Select',
        // label: '执行数据库',
        key: 'db',
        compIcon: 'lu-icon-database',
        props: {
          placeholder: 'DB',
          url: '/sqlreview/after_audit/select_data',
          reqParams: {
            data_source_id: data.data_source_id,
            select_type: 'schema'
          },
          allowSearch: true,
          backSearch: true,
          limit: 50
        }
      },
      {
        type: 'Select',
        // label: '执行用户',
        key: 'executive_user',
        compIcon: 'user',
        props: {
          placeholder: '执行用户',
          url: '/sqlreview/after_audit/select_data',
          reqParams: {
            data_source_id: data.data_source_id,
            select_type: 'user'
          },
          allowSearch: true,
          backSearch: true,
          limit: 50
        }
      }
    ];
    const defaultArr = [
      {
        type: 'RangePicker',
        // label: '时间范围',
        key: 'execution_time',
        compIcon: 'calendar',
        props: {
          showTime: {
            format: 'HH:mm'
          },
          format: 'YYYY-MM-DD HH:mm',
          disabledDate: (current) => {
            return (
              current <= moment().subtract(30, 'days') ||
              current > moment().endOf('day')
            );
          }
        }
      },
      {
        type: 'Input',
        // label: 'SQL_ID',
        compIcon: 'lu-icon-ID',
        key: 'checksum',
        props: {
          placeholder: 'SQL_ID'
        }
      },
      {
        type: 'Input',
        // label: 'SQL文本',
        compIcon: 'lu-icon-field',
        key: 'sql_text',
        props: {
          placeholder: 'SQL文本'
        }
      },
      {
        type: 'Select',
        // label: 'AI风险等级',
        compIcon: 'lu-icon-alarm',
        key: 'risk',
        props: {
          placeholder: 'AI风险等级',
          options: [
            { label: '待审核', value: 0 },
            { label: '高风险', value: -1 },
            { label: '无风险', value: 1 },
            { label: '低风险', value: 2 },
            { label: '异常', value: 9 },
            { label: '审核中', value: 3 }
          ]
        }
      }
    ];
    let res = [];
    if (data.data_source_id) {
      res = [...defaultArr, ...extraArr];
    } else {
      res = [...defaultArr];
    }
    return res;
  };
  return {
    columns,
    searchFields
  };
}
