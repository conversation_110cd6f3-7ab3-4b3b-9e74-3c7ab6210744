<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    @cancel="onCancel"
    @ok="onOk"
    width="600px"
    wrapClassName="biz-review-detail-tag-modal big-title"
  >
    <div slot="title" class="title">申请添加标签</div>
    <div class="des">
      为SQL添加的标签需要评审人员进行审核。审核通过则SQL标签添加成功。
      批量添加标签时，已审核通过的标签不受影响，待审核和未通过的SQL标签会被覆盖。
    </div>
    <div>
      <RadioGroup
        ref="radioGroup"
        mode="lineTips"
        :options="options"
        @change="change"
      />
      <Form ref="form" v-bind="formParams" :formData="formData"></Form>
    </div>
  </a-modal>
</template>

<script>
import RadioGroup from '@/components/RadioGroup';
import Form from '@/components/Form';

export default {
  components: { RadioGroup, Form },
  props: {},
  data() {
    return {
      visible: false,
      options: [
        {
          label: '白名单',
          value: 0,
          tips:
            'SQL 执行效率低，但由于调用频率，系统核心程度等原因不做优化调整时，可申请加入白名单。'
        },
        {
          label: '整改中',
          value: 1,
          tips:
            '整改周期较长的SQL，可先做标记，暂不影响发版流程。整改中的SQL需要尽快修改，后续版本会继续扫描。'
        }
      ],
      data: {},
      labelAttribute: null,
      id: null,
      formData: {},
      formParams: {
        gutter: 32,
        colon: true,
        multiCols: 2,
        fields: [
          (formData = {}) => {
            return {
              type: 'RadioGroup',
              label: '有效期(天)',
              key: 'permanent_day',
              props: {
                mode: 'tips',
                class: 'inline',
                options:
                  formData.label_attribute == 1
                    ? [
                        {
                          label: '自定义',
                          value: 1
                        }
                      ]
                    : [
                        {
                          label: '永久',
                          value: 0
                        },
                        {
                          label: '自定义',
                          value: 1
                        }
                      ]
              },
              listeners: {
                change: value => {
                  const { form } = this.$refs;
                  form.saving({
                    permanent_day: value,
                    custom_day: null
                  });
                }
              },
              rules: [
                { required: true, message: '该项为必填项', trigger: 'change' }
              ]
            };
          },
          (formData = {}) => {
            return {
              type: 'InputNumber',
              label: '',
              key: 'custom_day',
              props: {
                placeholder: '请输入',
                min: 1
              },
              visible: formData.permanent_day == 1,
              listeners: {
                change: value => {
                  const { form } = this.$refs;
                  form.saving({
                    custom_day: value
                  });
                }
              },
              rules: [
                { required: true, message: '该项为必填项', trigger: 'change' }
              ]
            };
          }
        ]
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      this.data = data;
      this.id = data && data.id;
      this.visible = true;
    },
    hide() {
      this.visible = false;
      this.labelAttribute = null;
      this.$refs.radioGroup.localValue = null;
      this.formData = {};
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      if (![0, 1].includes(this.labelAttribute)) {
        this.$message.warn('请选择标签！');
        return;
      }
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          const formData = form.getData();
          const data = {
            label_attribute: this.labelAttribute,
            id: this.id ? this.id : undefined,
            permanent_day: formData.custom_day || 0
          };
          this.$emit('saveLabel', data);
          this.hide();
        }
      });
    },
    change(data) {
      this.labelAttribute = data;
      const { form } = this.$refs;
      form.saving({
        label_attribute: data,
        permanent_day: data
      });
    }
  }
};
</script>

<style lang="less">
.biz-review-detail-tag-modal {
  // .ant-modal-header {
  //   background: #fff !important;
  //   .title {
  //     font-size: 20px;
  //     color: #27272a;
  //     font-weight: 600;
  //   }
  // }
  // .ant-modal-close {
  //   .ant-modal-close-x {
  //     color: #27272a;
  //   }
  // }
  .ant-modal-body {
    padding: 20px;
    .des {
      font-size: 12px;
      color: #a1a1aa;
      font-weight: 400;
      margin-bottom: 16px;
    }
    .custom-radio-group {
      &.crg-with-line-tips {
        .ant-radio-wrapper {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: start;
          border: none;
          border-radius: 6px;
          padding: 14px 20px;
          margin-left: 0;
          margin-right: 0;
          margin-bottom: 12px;
          background: #f4f5f7;
          > span {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: start;
            > span:first-child {
              margin-right: 12px;
              padding: 12px 32px;
              background: #ffffff;
              border-radius: 32px;
              border: 1px solid rgba(228, 228, 231, 1);
              font-size: 14px;
              color: #27272a;
              font-weight: 400;
            }
          }
          .crg-tips {
            word-break: break-all;
            white-space: pre-wrap;
            font-size: 12px;
            color: #71717a;
            font-weight: 400;
          }

          &.ant-radio-wrapper-checked {
            border-color: @checked-border-color;
            background: @checked-bg;
            > span {
              > span:first-child {
                border: 1px solid rgba(77, 181, 242, 1);
                color: #008adc;
                font-weight: 600;
              }
            }
          }
          &:not(.ant-radio-wrapper-disabled):hover {
            border-color: @checked-border-color;
            background: @checked-bg;
          }
          .ant-radio {
            .ant-radio-inner {
              width: 21px;
              height: 21px;
              &::after {
                width: 15px;
                height: 15px;
                top: 2px;
                left: 2px;
              }
            }
          }
        }
      }
    }
    .ant-form {
      margin-left: 16px;
      .ant-row {
        display: flex;
        align-items: center;
        .ant-col {
          width: auto;
          padding-right: 0 !important;
          padding-left: 0 !important;
          .ant-form-item {
            display: flex;
            .ant-form-item-control-wrapper {
              .ant-radio-group {
                display: flex;
                width: 210px;
              }
              .ant-input-number {
                width: 200px;
                height: 35px;
                .ant-input-number-input-wrap {
                  input {
                    height: 34px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .ant-modal-footer {
    // .ant-btn {
    //   width: 76px;
    //   height: 36px;
    //   background: #ffffff;
    //   border: 1px solid #008adc;
    //   font-size: 14px;
    //   color: #008adc;
    //   font-weight: 600;
    //   border-radius: 6px;
    // }
    // .ant-btn-primary {
    //   background: #008adc;
    //   color: #ffffff;
    //   font-weight: 600;
    // }
  }
}
</style>
