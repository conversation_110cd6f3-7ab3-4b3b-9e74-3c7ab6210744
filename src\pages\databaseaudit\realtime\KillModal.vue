<template>
  <div>
    <a-modal
      :title="title"
      :visible="visible"
      :footer="null"
      :mask="false"
      width="900px"
      @cancel="onCancel"
      ref="modal"
      wrapClassName="database-audit-realtime-kill-modal"
    >
      <a-spin :spinning="spinning" class="content-tabs">
        <a-tabs
          v-model="activeKey"
          type="editable-card"
          @edit="onEdit"
          @change="tabChange"
        >
          <a-tab-pane v-for="pane in panes" :key="pane.key">
            <div slot="tab" @dblclick="onEditTitle(pane)">
              <span v-if="!pane.isEdit">{{ pane.title }}</span>
              <a-input
                v-else
                class="edit-name-input"
                v-model="value"
                @blur="onBlur(pane)"
                @pressEnter="onBlur(pane)"
                ref="inputField"
              />
            </div>
            <div class="kill-coder-content">
              <Coder
                :ref="`coder${pane.key}`"
                v-model="pane.content"
                class="kill-coder"
                v-bind="getCoderParams(pane.isLock)"
                :tools="getTools(pane)"
                :onLoaded="onLoaded"
              ></Coder>
            </div>
            <div class="action-area">
              <a-button
                @click="execute(pane.content)"
                :class="isExecute && 'execute-button'"
                >立即执行</a-button
              >
              <a-button
                @click="generate(pane.content)"
                :class="isGenerate && 'generate-button'"
                >生成语句</a-button
              >
            </div>

            <div class="kill-result">
              <div class="kill-error-message" v-if="killErrorMessage">
                {{ killErrorMessage }}
              </div>

              <Coder
                ref="killCoder"
                class="kill-mode-coder"
                v-model="killSql"
                v-bind="killCoderParams"
                :tools="tools"
                :onLoaded="onLoaded"
                v-if="killSql"
              ></Coder>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </a-modal>
  </div>
</template>

<script>
import Coder from '@/components/Coder';
import Table from '@/components/Table';
import {
  getRealtimeKillTabs,
  realtimeKillExecute,
  realtimeKillGenerate,
  addRealtimeKillTab,
  editRealtimeKillTab,
  deleteRealtimeKillTab
} from '@/api/databaseaudit/realtime';
import Draggable from '@/utils/drag';

export default {
  name: '',
  components: { Coder, Table },
  props: {},
  inject: ['rootActiveKey'],
  data() {
    return {
      visible: false,
      spinning: false,
      title: '批量kill',
      value: null,
      panes: [],
      activeKey: null,
      label: '',
      coderParams: {
        type: 'sql',
        options: {
          gutters: [
            'CodeMirror-linenumbers',
            'CodeMirror-foldgutter',
            'CodeMirror-errTips'
          ]
        },
        placeholder: '请输入where条件',
        needFormat: true
      },
      killCoderParams: {
        type: 'js',
        options: {
          gutters: [
            'CodeMirror-linenumbers',
            'CodeMirror-foldgutter',
            'CodeMirror-errTips'
          ],
          readOnly: true
        },
        // placeholder: 'SQL语句需要以分号;结束',
        needFormat: true
      },
      tools: [
        { key: 'fullscreen' },
        {
          key: 'copy',
          icon: 'copy',
          callback(coder) {
            coder.onCopy();
          }
        }
      ],
      killSql: '',
      dbType: null,
      id: null,
      isExecute: false,
      isGenerate: false,
      killErrorMessage: null
    };
  },
  computed: {
    rootActiveKeyVal() {
      return this.rootActiveKey();
    }
  },
  watch: {
    rootActiveKeyVal(newVal) {
      this.onCancel();
    }
  },
  mounted() {
    this.setModalDraggable();
  },
  updated() {},
  methods: {
    show(id, dbType) {
      this.id = id;
      this.dbType = dbType;
      this.visible = true;
      this.init();
    },
    onCancel() {
      this.visible = false;
      this.clear();
    },
    clear() {
      this.killSql = '';
      this.isExecute = false;
      this.isGenerate = false;
      this.spinning = false;
      this.killErrorMessage = null;
    },
    init() {
      this.refresh();
    },
    refresh(isAdd) {
      this.spinning = true;
      getRealtimeKillTabs({ db_type: this.dbType.toUpperCase() })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.spinning = false;
            const resData = res.data.data;
            const oldPanes = [...this.panes];
            this.panes = resData.map((item, index) => {
              return {
                ...oldPanes.find(op => op.key == item.id),
                title: item.name,
                oldContent: item.content,
                content: item.content,
                key: item.id,
                isEdit: false,
                isLock: true
              };
            });
            if (isAdd) {
              this.activeKey = _.last(this.panes).key;
            } else if (this.activeKey == null) {
              this.activeKey = this.panes[0].key;
            }
            this.setCoderStatus();
          } else {
            this.spinning = false;
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 弹窗拖动
    setModalDraggable() {
      !this.modalDragInstance &&
        setTimeout(() => {
          const modalElement = this.$refs.modal.$el;
          if (modalElement && modalElement.querySelector) {
            this.modalDragInstance = new Draggable({
              el: modalElement.querySelector('.ant-modal-content'),
              handler: modalElement.querySelector('.ant-modal-header')
            });
          } else {
            this.setModalDraggable();
          }
        }, 800);
    },
    getCoderParams(isLock) {
      this.$set(this.coderParams.options, 'readOnly', isLock);
      return this.coderParams;
    },
    getTools(pane) {
      const tools = [
        { key: 'fullscreen' },
        {
          key: 'copy',
          icon: 'copy',
          callback(coder) {
            coder.onCopy();
          }
        },
        {
          key: pane.isLock ? 'lock' : 'unlock',
          icon: pane.isLock ? 'lock' : 'unlock',
          callback: (...args) => this.edit(pane, ...args)
        }
      ];
      return tools;
    },
    setCoderStatus() {
      this.$nextTick(() => {
        this.panes.forEach(pane => {
          const coderKey = `coder${pane.key}`;
          const _ref = this.$refs[coderKey];
          if (_ref) {
            const coderRef = _ref[0];
            coderRef.setOption('readOnly', pane.isLock);
            if (pane.onBlur) {
              coderRef.coder.off('blur', pane.onBlur);
            }
            coderRef.coder.on(
              'blur',
              (pane.onBlur = e => {
                if (pane.content === pane.oldContent) return;
                if (this.isLocal(pane.key)) {
                  this.addTab(pane);
                } else {
                  this.save(pane);
                }
              })
            );
          }
        });
      });
    },
    isLocal(tabId) {
      return tabId != null && (tabId + '').startsWith('add_');
    },
    // 切换 tab, 选项卡
    tabChange(activeKey) {
      this.activeKey = activeKey;
      this.clear();
      this.setCoderStatus();
    },
    onEdit(targetKey, action) {
      // console.log(action, 'hhhh')
      this[action](targetKey);
    },
    add(targetKey) {
      this.clear();
      const uid = _.uniqueId('add_');
      const pane = {
        title: `模板${this.panes.length + 1}`,
        content: '',
        key: uid,
        isEdit: false,
        isLock: false
      };
      this.panes.push(pane);
      this.activeKey = uid;
      this.setCoderStatus();
    },
    remove(targetKey) {
      if (this.panes.length == 1) return;
      this.$confirm({
        content: '确认是否删除此窗口?',
        onOk: () => {
          let activeKey = this.activeKey;
          let lastIndex;
          this.panes.forEach((pane, i) => {
            if (pane.key === targetKey) {
              lastIndex = i - 1;
            }
          });
          const panes = this.panes.filter(pane => pane.key !== targetKey);
          if (panes.length && activeKey === targetKey) {
            if (lastIndex >= 0) {
              activeKey = panes[lastIndex].key;
            } else {
              activeKey = panes[0].key;
            }
          }
          let tabId = null;
          this.panes.forEach(item => {
            if (item.key == targetKey) {
              tabId = item.key;
            }
          });
          this.panes = panes;
          this.activeKey = activeKey;
          !this.isLocal(tabId) && this.removeTab(tabId);
        },
        onCancel: () => {}
      });
    },
    edit(pane, coder, instance, event) {
      const bool = !pane.isLock;
      this.$set(pane, 'isLock', bool);
      coder.setOption('readOnly', bool);
    },
    addTab(pane) {
      addRealtimeKillTab({
        name: pane.title,
        content: pane.content,
        db_type: this.dbType.toUpperCase()
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.refresh(true);
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    removeTab(id) {
      deleteRealtimeKillTab(id)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$message.success('删除成功！');
            this.clear();
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    save(pane) {
      editRealtimeKillTab(pane.key, {
        name: pane.title,
        content: pane.content
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            // this.$set(pane, 'isLock', true);
            // coder.setOption('readOnly', true);
            this.refresh();
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 双击编辑窗口标题
    onEditTitle(pane) {
      this.value = pane.title;
      pane.isEdit = true;
      this.$nextTick(() => {
        const _ref = this.$refs.inputField;
        _ref && _ref[0].focus();
      });
    },
    // 双击编辑窗口标题,失焦后事件
    onBlur(pane) {
      pane.isEdit = false;
      pane.title = this.value;
      pane.name = this.value;
      const _ref = `coder${pane.key}`;
      const coder = this.$refs[_ref][0];
      this.save(pane, coder);
    },
    execute(sql) {
      this.clear();
      this.isExecute = true;
      this.spinning = true;
      realtimeKillExecute(this.id, this.dbType.toLowerCase(), { sql })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.spinning = false;
            const resData = res.data.data;
            resData
              ? resData.forEach(item => {
                  this.killSql +=
                    item.sql +
                    '\n' +
                    (item.success
                      ? '> ' + '执行成功。 '
                      : '> ' + '执行失败。  ' + item.message) +
                    '\n';
                })
              : (this.killSql = '暂无数据');
          } else {
            this.spinning = false;
            this.killErrorMessage = _.get(res, 'data.message');
            // this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    generate(sql) {
      this.clear();
      this.isGenerate = true;
      this.spinning = true;
      realtimeKillGenerate(this.id, this.dbType.toLowerCase(), { sql })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.spinning = false;
            const resData = res.data.data;
            !_.isEmpty(resData)
              ? resData.forEach(item => {
                  this.killSql += item + '\n';
                })
              : (this.killSql = '暂无数据');
          } else {
            this.spinning = false;
            this.killErrorMessage = _.get(res, 'data.message');
            // this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    onLoaded(coder) {
      coder.refreshDelay();
    }
  }
};
</script>

<style lang="less">
.database-audit-realtime-kill-modal {
  pointer-events: none;
  .ant-modal-content {
    .ant-modal-close {
      .ant-modal-close-x {
        color: #000;
      }
    }
    .ant-modal-header {
      background: #fff;
      border-bottom: 1px solid #e8e8e8;
      .ant-modal-title {
        color: #000;
      }
      border: none;
      cursor: move;
    }
    .ant-modal-body {
      padding-top: 12px;
      .content-tabs {
        width: 100%;
        // padding: 12px 0 0 0;
        background: #fff;
        .ant-tabs {
          height: 100%;
          .ant-tabs-bar {
            // padding: 0;
            // display: flex;
            // flex-direction: row-reverse;
            // justify-content: flex-end;
            // align-items: flex-end;
            .ant-tabs-nav-container {
              .ant-tabs-nav {
                > div {
                  .ant-tabs-tab {
                    // padding: 0 12px;
                    // margin-right: 4px;
                    // border-radius: 8px 8px 0 0;
                    // background: rgb(243, 244, 246);
                    // transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
                    // &:first-child {
                    //   margin-left: 16px;
                    // }
                    .edit-name-input {
                      width: 80px;
                      border: none;
                      border-radius: 0;
                      background: transparent;
                      &:focus {
                        box-shadow: 0 0 0 0;
                        border-bottom: 1px solid #25a7e8;
                      }
                    }
                    > div {
                      display: flex;
                      align-items: center;
                      .anticon {
                        font-size: 12px;
                        margin-left: 4px;
                        color: #a1a1aa;
                      }
                    }
                    &.ant-tabs-tab-active {
                      background: #fff;
                      > div {
                        .anticon {
                          color: #4db5f2;
                        }
                      }
                    }
                  }
                }
              }
            }
            // .ant-tabs-extra-content {
            //   height: 40px;
            //   margin-bottom: -1px;
            //   border-radius: 8px;
            //   .ant-tabs-new-tab {
            //     width: 40px;
            //     height: 40px;
            //     background: #f2f2f2;
            //     border: none;
            //     border-left: 1px solid #fff;
            //     border-radius: 8px 8px 0 0;
            //   }
            // }
          }
        }
      }
      .action-area {
        padding: 16px 0;
        .execute-button,
        .generate-button {
          border-radius: 8px;
          color: #fff;
          background-color: #1890ff;
          border-color: #1890ff;
          // text-shadow: 0 -1px 0 rgb(0 0 0 / 12%);
          // box-shadow: 0 2px 0 rgb(0 0 0 / 5%);
        }
      }
      .kill-result {
        .kill-error-message {
          color: rgb(245, 34, 45);
          background-color: rgb(255, 241, 240);
          padding: 16px;
          border-radius: 8px;
        }
      }
    }
  }
}
</style>
