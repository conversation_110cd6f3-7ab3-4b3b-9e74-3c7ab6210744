<template>
  <div class="custom-page-list page-list-single">
    <!-- <a-card style="width:100%" class="common-page-card" :title="title" :bordered="false">
    </a-card>-->
    <!-- 顶部按钮区域 -->
    <!-- <div slot="extra">
        <slot name="topBtns"></slot>
    </div>-->
    <div class="frame-button-wrapper" slot="extra">
      <slot name="topBtns"></slot>
    </div>
    <!-- 列表内容 -->
    <SearchArea
      v-bind="searchParams || {}"
      @reset="reset"
      @search="search"
      needCache
      v-if="needSplitSearch"
    ></SearchArea>
    <slot name="middle"></slot>
    <Table
      ref="table"
      v-bind="tableConfig || {}"
      v-on="tableListeners"
      needCache
      :isInitReq="false"
    >
      <template v-for="sItem in scopedSlots" v-slot:[sItem]="{text, record, index, column}">
        <slot :name="sItem" v-bind="{text, record, index, column}"></slot>
      </template>
      <template v-for="item in slots" v-slot:[item]>
        <slot :name="item"></slot>
      </template>
    </Table>
  </div>
</template>

<script>
// import _ from 'lodash';
import Table from '@/components/Table';
import SearchArea from '@/components/SearchArea';

export default {
  props: {
    title: String,
    searchParams: Object,
    tableParams: Object,
    needSplitSearch: {
      type: Boolean,
      default: true
    }
  },
  components: { Table, SearchArea },
  data() {
    this.searchData = {};
    return {};
  },
  computed: {
    scopedSlots() {
      return Object.keys(this.$scopedSlots);
    },
    slots() {
      return Object.keys(this.$slots);
    },
    tableConfig() {
      return {
        ...this.tableParams,
        ...(!this.needSplitSearch
          ? {
              searchFields: this.searchParams.fields,
              needTools: true,
              needSearchArea: true
            }
          : {})
      };
    },
    tableListeners() {
      return {
        ...this.$listeners
      };
    }
  },
  mounted() {},
  methods: {
    refresh(params = {}) {
      this.search(this.searchData, params);
    },
    // 查询
    search(data, params = {}) {
      const { table } = this.$refs;
      const { keep, type } = params;
      this.searchData = data || {};
      if (keep) {
        table.refreshKeep(type, data);
      } else {
        table.refresh(null, data);
      }
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      this.searchData = {};
      table.refresh();
    }
  }
};
</script>

<style lang="less" scoped>
</style>
