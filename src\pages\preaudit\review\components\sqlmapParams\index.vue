<template>
  <a-spin class="spin" :spinning="false">
    <a-card type="small" :bordered="false" class="sqlmap-params" v-show="(canDo || isLeader) && sqlMapParamsData">
      <div slot="title">
        <a-icon type="read" />
        <span style="margin-left: 4px;">SQLMAP运行状态评估:</span>
      </div>
      <div slot="extra" @click="onExpandSqlMapParams()">
        <a-icon
          :class="{
              'sqlmap-params-expand-icon': true,
              expanded: sqlMapParamsShowNew
            }"
          type="double-right"
        ></a-icon>
        <!-- <a-button type="primary" @click.stop="saveSqlMapParams">保存</a-button> -->
      </div>
      <Form ref="sqlMapParams" v-bind="sqlMapParams" :formData="sqlMapParamsData"></Form>
    </a-card>
  </a-spin>
</template>

<script>
import config from './config';
import Form from '@/components/Form';
export default {
  components: { Form },
  props: {
    sqlMapParamsShow: {
      type: Boolean,
      default: false
    },
    sqlMapParamsData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    sqlMapParamsShow(newValue) {
      if (newValue) {
        this.sqlMapParamsShowNew = newValue;
      }
    },
    watch: {
      $route(to, from) {
        // this.initData();
      },
      '$store.state.account.user'(newVal = {}) {
        this.canDo = newVal.role === 'dba';
      }
    }
  },
  data() {
    this.config = config(this);
    return {
      activeTab: '',
      sqlMapParams: {
        fields: this.config.fields,
        // fixedLabel: true,
        layout: 'horizontal',
        colon: true
      },
      sqlMapParamsShowNew: false,
      canDo: false
    };
  },
  computed: {
    isLeader() {
      const user = this.$store.state.account.user || {};
      return user.role === 'leader';
    }
  },
  mounted() {
    let user = this.$store.state.account.user || {};
    console.log(user);
    this.canDo = user.role === 'dba';
  },
  methods: {
    saveSqlMapParams() {
      const { sqlMapParams } = this.$refs;
      const data = sqlMapParams.getData();
      // 新写法
      ReqUtil.req({
        ctx: this,
        reqInstance: saveSqlmapConfig,
        params: {
          ...data,
          id: this.id
        },
        tips: '保存成功',
        cbk: () => {
          this.onNext();
        }
      });
    },
    onExpandSqlMapParams() {
      console.log(this.sqlMapParamsData);
      this.sqlMapParamsShowNew = !this.sqlMapParamsShowNew;
      let dom = this.$el.querySelector(`.sqlmap-params`);
      CommonUtil.toggleDom({
        element: dom,
        time: 0.3,
        startHeight: '56px',
        show: this.sqlMapParamsShowNew
      });
    }
  }
};
</script>

<style lang="less" scoped>
.sqlmap-params {
  height: 56px;
  overflow: hidden;
  margin-bottom: 24px !important;
}
.sqlmap-params-expand-icon {
  margin-right: 20px;
  transform: rotate(90deg);
  &:hover {
    color: @primary-color;
  }
  &.expanded {
    transform: rotate(270deg);
  }
}
/deep/ .ant-form {
  .ant-form-item {
    display: flex !important;
    margin-bottom: 4px;

    .ant-form-item-label {
      width: 200px;
      flex-shrink: 0;
      padding-right: 8px;
    }
    .ant-form-item-control-wrapper {
      flex-grow: 1;

      /deep/ .table-edit,
      textarea {
        margin-top: 8px;
      }
    }
  }
}
.small-card {
  margin-bottom: 24px;
  .ai-comment-part {
    padding: 16px;
    background-color: #edf5ff;
    border-radius: 5px;
  }
}
</style>