<template>
  <div>
    <div class="database-audit-real-time">
      <div class="header">
        <div class="header-left">
          <span class="setting-border">Release:</span>
          <span class="node-box">{{ release }}</span>
          <span class="setting-border" v-if="dbType == 'DB2'">Buffers:</span>
          <span class="node-box" v-if="dbType == 'DB2'">{{ buffers }}</span>
          <span>{{ time }} 更新</span>
          <span class="reload-box">
            <custom-icon type="sync" />
            <a-dropdown v-model="visible" class="tab-bar-btn">
              <a class="ant-dropdown-link" @click="(e) => e.preventDefault()">
                {{ value + '秒' }}
                <a-icon type="down" />
              </a>
              <a-menu slot="overlay" @click="handleMenuClick">
                <a-menu-item key="1000">1秒</a-menu-item>
                <a-menu-item key="5000">5秒</a-menu-item>
                <a-menu-item key="10000">10秒</a-menu-item>
                <a-menu-item key="30000">30秒</a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
        </div>
        <div
          :class="['header-right', isPause && 'pause']"
          @click="!isPause ? pause() : resume()"
        >
          <span>{{ isPause ? '暂停' : '进行中' }}</span>
          <custom-icon :type="isPause ? 'caret-right' : 'pause'" />
        </div>
      </div>

      <div class="echart-block">
        <div class="line-echart-block">
          <div
            class="custom-line-echart"
            v-for="item in chartData"
            :key="item.name"
          >
            <div class="title">{{ item.name }}</div>
            <div class="content">
              <span>{{ item.num }}</span>
              <Chart :option="item.arr || []" ref="line" />
            </div>
          </div>
        </div>
        <div class="bar-echart-block">
          <Chart :option="barOption" ref="bar" />
        </div>
      </div>
      <div class="table-block">
        <div class="search-area">
          <div class="title">SQL列表</div>
          <div class="search-content">
            <!-- <a-button type="primary" @click="kill" class="kill-btn">KILL</a-button> -->
            <a-tooltip>
              <template slot="title">表头设置</template>
              <custom-icon
                type="control"
                @click="customSetColumn"
              ></custom-icon>
            </a-tooltip>
            <a-tooltip>
              <template slot="title">模板设置</template>
              <custom-icon type="setting" @click="setting"></custom-icon>
            </a-tooltip>
            <a-tooltip>
              <template slot="title">重置</template>
              <custom-icon type="lu-icon-free" @click="reset"></custom-icon>
            </a-tooltip>
          </div>
        </div>
        <Table
          ref="table"
          v-bind="tableParams"
          :dataSource="dataSource || []"
          @filter-confirm="filterConfirm"
          @filter-reset="filterReset"
          @sorter="sorter"
          class="new-view-table small-size"
        >
          <!-- db2 -->
          <LimitLabel
            slot="STMT_TEXT"
            slot-scope="{ text }"
            :label="text"
            :contentStyle="{ width: '250px', height: 'auto', overflow: 'auto' }"
            :limit="16"
            :nowrap="true"
          ></LimitLabel>

          <!-- gaussdb -->
          <LimitLabel
            slot="QUERY"
            slot-scope="{ text }"
            :label="text"
            :contentStyle="{ width: '250px', height: 'auto', overflow: 'auto' }"
            :limit="16"
            :nowrap="true"
            format="sql"
          ></LimitLabel>
          <LimitLabel
            slot="CONNECTION_INFO"
            slot-scope="{ text }"
            :label="text"
            :contentStyle="{ width: '250px', height: 'auto', overflow: 'auto' }"
            :limit="16"
            :nowrap="true"
          ></LimitLabel>

          <custom-btns-wrapper slot="action" slot-scope="{ record }">
            <a
              actionBtn
              @click="onSqlException(record)"
              :disabled="!record.info"
              v-if="$permission.realTimeSql('explain')"
              >Explain</a
            >
            <a-popconfirm
              title="确定kill?"
              @confirm="() => singleKill(record)"
              v-if="$permission.realTimeSql('kill')"
              actionBtn
            >
              <a>Kill</a>
            </a-popconfirm>
          </custom-btns-wrapper>
        </Table>
      </div>
    </div>
    <CustomSetColumnModal
      ref="customSetColumn"
      @save="save"
    ></CustomSetColumnModal>
    <SettingDrawer ref="setting" :id="id" :dbType="dbType" />
  </div>
</template>

<script>
import Tag from '@/components/Biz/Tag';
import Status from '@/components/Biz/Status';
import config from './config';
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import DbImg from '@/components/CustomImg/DbImg';
import Chart from '@/components/Chart';
import Select from '@/components/Select';
import SqlExceptionDrawer from '../components/sqlExceptionDrawer';
import SettingDrawer from '../components/settingDrawer';
import CustomSetColumnModal from '../components/customSetColumnModal';
import {
  getDB2RealTimeInfo,
  realtimeKill,
  getGaussdbRealTimeInfo
} from '@/api/databaseaudit/realtime';

export default {
  components: {
    Tag,
    DbImg,
    Table,
    Chart,
    Select,
    Status,
    LimitLabel,
    SqlExceptionDrawer,
    SettingDrawer,
    CustomSetColumnModal
  },
  props: {
    pane: Object,
    id: Number | String,
    paneKey: String
  },
  computed: {
    // canDo() {
    //   const user = this.$store.state.account.user || {};
    //   return ['leader', 'admin', 'dba'].includes(user.role);
    // }
  },
  data() {
    this.config = config(this);
    this.reqCancelHandler = null; // 取消请求句柄
    const dbType = this.pane.db_type;
    return {
      inited: false,
      value: 1,
      barOption: this.config.barOption(),
      dataSource: [],
      columns: [],
      tableParams: {
        url: '',
        reqParams: {},
        columns: this.config.columns,
        searchFields: [],
        // rowKey: 'SID',
        // scroll: { x: 1400 },
        scroll: { x: 'max-content' }
      },
      visible: false,
      time: null,
      count: 0,
      timeout: 1000,
      params: {},
      isPause: false,
      wrongNum: 0,
      chartData: [],
      dbType,
      insId: null,
      sqlId: null,
      release: '',
      buffers: ''
    };
  },
  created() {},
  mounted() {
    window.localStorage.removeItem(`${this.dbType}RealTimeSqlId`);
    this.init();
  },
  beforeDestroy() {
    this.pageDestroyed = true;
    this.cancel();
  },
  methods: {
    init() {
      this.onSetInterval();
    },
    // 手动用setTimeout 实现setInterval
    onSetInterval() {
      const loop = (id, sqlId, insId, params = {}) => {
        if (this.pageDestroyed) return;
        let req = () => {};
        req =
          this.dbType == 'DB2' ? getDB2RealTimeInfo : getGaussdbRealTimeInfo;
        req(id, sqlId, insId, params, c => {
          this.reqCancelHandler = c;
        })
          .then(res => {
            this.count++;
            if (CommonUtil.isSuccessCode(res)) {
              let resData = _.get(res, 'data.data') || {};
              let variable = resData.variable;
              this.buffers = variable.buffers;
              this.release = variable.release;
              this.dataSource = resData.process_list;
              this.tableHeader = resData.real_time_process_header;
              const barData =
                this.dbType == 'DB2'
                  ? [
                      resData.read_dml,
                      resData.write_dml,
                      resData.ddl,
                      resData.other
                    ]
                  : [
                      resData.select,
                      resData.insert,
                      resData.update,
                      resData.delete
                    ];
              this.$set(
                this,
                'barOption',
                this.config.barOption(barData, 1, this.dbType)
              );
              // db2
              const activeSessions = variable.active_sessions || [];
              const lockWait = variable.lock_wait || [];

              // gaussdb
              const runningSql = variable.running_sql || [];
              const memoryUsed = variable.memory_used || [];

              //  公共的
              const hitRatio = variable.hit_ratio || [];
              const deadLock = variable.dead_lock || [];
              const connections = variable.connections || [];
              const tps = variable.tps || [];
              const qps = variable.qps || [];
              const tableLocks = variable.table_locks || [];
              this.chartData =
                this.dbType == 'DB2'
                  ? [
                      {
                        name: 'Active Sessions',
                        num: activeSessions[activeSessions.length - 1],
                        arr: this.config.lineOption(
                          '#A3A9EF',
                          '#E4E6FB',
                          activeSessions
                        )
                      },
                      {
                        name: 'Connections',
                        num: connections[connections.length - 1],
                        arr: this.config.lineOption(
                          '#90C6AC',
                          '#E5F3E8',
                          connections
                        )
                      },
                      {
                        name: 'TPS',
                        num: tps[tps.length - 1],
                        arr: this.config.lineOption('#90C6AC', '#E5F3E8', tps)
                      },
                      {
                        name: 'QPS',
                        num: qps[qps.length - 1],
                        arr: this.config.lineOption('#E4BF94', '#F8F2E4', qps)
                      },
                      {
                        name: 'Lock Waits',
                        num: lockWait[lockWait.length - 1],
                        arr: this.config.lineOption(
                          '#F1A1A1',
                          '#FBE6E0',
                          lockWait
                        )
                      },
                      {
                        name: 'Table Locks',
                        num: tableLocks[tableLocks.length - 1],
                        arr: this.config.lineOption(
                          '#EE9DBA',
                          '#FAE1E7',
                          tableLocks
                        )
                      },
                      {
                        name: 'Dead Locks',
                        num: deadLock[deadLock.length - 1],
                        arr: this.config.lineOption(
                          '#C0A1D8',
                          '#EDE1F2',
                          deadLock
                        )
                      },
                      {
                        name: 'Hit Ratio%',
                        num: hitRatio[hitRatio.length - 1],
                        arr: this.config.lineOption(
                          '#A9A7AB',
                          '#E4E4E7',
                          hitRatio
                        )
                      }
                    ]
                  : [
                      {
                        name: 'Running SQL',
                        num: runningSql[runningSql.length - 1],
                        arr: this.config.lineOption(
                          '#A3A9EF',
                          '#E4E6FB',
                          runningSql
                        )
                      },
                      {
                        name: 'Connections',
                        num: connections[connections.length - 1],
                        arr: this.config.lineOption(
                          '#90C6AC',
                          '#E5F3E8',
                          connections
                        )
                      },
                      {
                        name: 'Table Locks',
                        num: tableLocks[tableLocks.length - 1],
                        arr: this.config.lineOption(
                          '#EE9DBA',
                          '#FAE1E7',
                          tableLocks
                        )
                      },
                      {
                        name: 'TPS',
                        num: tps[tps.length - 1],
                        arr: this.config.lineOption('#90C6AC', '#E5F3E8', tps)
                      },
                      {
                        name: 'QPS',
                        num: qps[qps.length - 1],
                        arr: this.config.lineOption('#E4BF94', '#F8F2E4', qps)
                      },
                      {
                        name: 'Dead Locks',
                        num: deadLock[deadLock.length - 1],
                        arr: this.config.lineOption(
                          '#F1A1A1',
                          '#FBE6E0',
                          deadLock
                        )
                      },
                      {
                        name: 'Buffer Hit',
                        num: hitRatio[hitRatio.length - 1],
                        arr: this.config.lineOption(
                          '#C0A1D8',
                          '#EDE1F2',
                          hitRatio
                        )
                      },
                      {
                        name: 'Mem Used Pct',
                        num: memoryUsed[memoryUsed.length - 1],
                        arr: this.config.lineOption(
                          '#A9A7AB',
                          '#E4E4E7',
                          memoryUsed
                        )
                      }
                    ];

              this.dealTableHeaderInfo(this.tableHeader);
            } else {
              if (this.count > 1) return;
              this.$message.error(_.get(res, 'data.message'));
            }
          })
          .catch(e => {
            this.wrongNum++;
            if (this.wrongNum > 1) return;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          })
          .finally(e => {
            if (!this.inited) {
              this.inited = true;
            }
            if (this.wrongNum >= 5) {
              this.isPause = !this.isPause;
              this.cancel();
              return;
            }
            clearTimeout(this.timer);
            // 取消接口请求
            if (this.reqCancelHandler) {
              this.reqCancelHandler();
            }
            // 暂停之后 搜索会再调一次接口 这时不轮巡
            if (!this.isPause) {
              this.timer = setTimeout(() => {
                const sqlId = window.localStorage.getItem(
                  `${this.dbType}RealTimeSqlId`
                );
                this.sqlId = !_.isEmpty(sqlId) ? sqlId : this.sqlId;
                const params = this.params;
                loop(this.id, this.sqlId, this.insId, params);
              }, this.timeout);
            }
          });
      };
      loop(this.id, this.sqlId, this.insId, this.params);
    },
    // 处理表头数据
    dealTableHeaderInfo(data = []) {
      let columns = [];
      let searchFields = [];
      data.forEach(item => {
        columns.push({
          title: item.value,
          dataIndex: item.key,
          key: item.key,
          sorter: item.sorter,
          scopedSlots: { customRender: item.key }
        });
        item.type &&
          searchFields.push({
            type: item.type,
            label: item.value,
            key: item.key,
            props: {
              placeholder: '请输入'
            }
          });
      });
      // columns.push({
      //   title: '操作',
      //   key: 'action',
      //   slots: { title: 'customTitle' },
      //   scopedSlots: { customRender: 'action' },
      //   fixed: 'right'
      // });
      columns = columns.map(item => {
        return {
          ...item,
          width: undefined
        };
      });
      const localColumns = JSON.parse(
        window.localStorage.getItem(`${this.dbType}RealTimeColumns`)
      );

      const localSearchFields = JSON.parse(
        window.localStorage.getItem(`${this.dbType}RealTimeSearchFields`)
      );

      this.$set(
        this.tableParams,
        'columns',
        !_.isEmpty(localColumns) ? localColumns : columns
      );
      this.$set(
        this.tableParams,
        'searchFields',
        !_.isEmpty(localSearchFields) ? localSearchFields : searchFields
      );
    },
    // 设置更新秒数
    handleMenuClick(e) {
      this.value = e.key / 1000;
      this.timeout = e.key;
      this.visible = false;
    },
    // 停止计时器
    cancel() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      // 取消接口请求
      if (this.reqCancelHandler) {
        this.reqCancelHandler();
      }
      this.count = 0;
      this.wrongNum = 0;
      this.params = {};
    },
    // 暂停
    pause() {
      this.isPause = true;
      this.cancel();
    },
    // 恢复
    resume() {
      if (!this.inited) return;
      this.isPause = false;
      this.cancel();
      this.onSetInterval();
    },
    kill() {},
    setting() {
      this.$refs.setting.show();
    },
    // 设置表头弹窗
    customSetColumn() {
      this.$refs.customSetColumn.show(this.dbType, this.tableHeader);
    },
    save(data) {
      let columns = [];
      let searchFields = [];
      this.tableHeader.forEach(item => {
        data.forEach(el => {
          if (item.key == el) {
            columns.push({
              title: item.value,
              dataIndex: item.key,
              key: item.key,
              sorter: item.sorter,
              scopedSlots: { customRender: item.key }
            });
            item.type &&
              searchFields.push({
                type: item.type,
                label: item.value,
                key: item.key,
                props: {
                  placeholder: '请输入'
                }
              });
          }
        });
      });

      window.localStorage.setItem(
        `${this.dbType}RealTimeColumns`,
        JSON.stringify(columns)
      );
      window.localStorage.setItem(
        `${this.dbType}RealTimeSearchFields`,
        JSON.stringify(searchFields)
      );
    },
    // 执行计划
    onSqlException(record) {
      this.$refs.sqlException.show('realtime', record, this.pane);
    },
    // 列表单个kill
    singleKill(record) {
      const params = {
        data_source_id: this.id,
        id: record.id
      };
      realtimeKill(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: '操作成功'
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.msg')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 单个筛选
    filterConfirm(data, col) {
      this.params = {
        ...this.params,
        ...data
      };
      if (this.isPause) this.onSetInterval();
    },
    // 单个筛选重置
    filterReset(data, col) {
      this.params = {
        ...this.params,
        ...data
      };
      if (this.isPause) this.onSetInterval();
    },
    // 全部筛选重置
    reset() {
      this.params = {};
      this.$refs.table.onReset();
      if (this.isPause) this.onSetInterval();
    },
    // 表格过滤
    sorter(data) {
      this.params = {
        ...this.params,
        _sorter: data.order ? { [data.columnKey]: data.order } : undefined
      };
      if (this.isPause) this.onSetInterval();
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.custom-title {
  width: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .anticon {
    margin-right: 4px;
    font-size: 14px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    line-height: 24px;
    &:hover {
      background: @primary-3;
      color: #ffffff;
      cursor: pointer;
    }
  }
}
.database-audit-real-time {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    background: #ffffff;
    padding: 0 32px;
    border-bottom: 1px solid #ebebec;
    .header-left {
      font-size: 13px;
      color: #a1a1aa;
      font-weight: 400;
      .setting-border {
        padding: 0 8px 0 0;
        > span {
          color: #27272a;
        }
      }
      .node-box,
      .reload-box {
        margin: 0 8px;
        .anticon-sync {
          font-size: 12px;
          color: #27272a;
        }
        .ant-dropdown-link {
          font-size: 13px;
          color: #27272a;
          font-weight: 400;
        }
      }
      .node-box {
        margin: 0 8px 0 0;
        > span {
          font-size: 13px;
          color: #27272a;
        }
      }
    }
    .header-right {
      background: #ffffff;
      font-size: 14px;
      color: #008adc;
      text-align: center;
      font-weight: 400;
      border: 1px solid #a5d9f8;
      border-radius: 3px;
      > span {
        padding: 8px 12px;
        border-right: none;
      }
      .anticon {
        padding: 8px 6px;
        border-left: 1px solid #a5d9f8;
      }
      &:hover {
        cursor: pointer;
        background: #eff5ff;
      }
      &.pause {
        color: #ef6173;
        border: 1px solid #f7bbc2;
        .anticon {
          border-left: 1px solid #f7bbc2;
        }
        &:hover {
          cursor: pointer;
          background: #fef3f5;
        }
      }
    }
  }
  /deep/.echart-block {
    display: flex;
    align-items: center;
    background: #ffffff;
    border-radius: 0 0 12px 12px;
    margin-bottom: 16px;
    min-height: 180px;
    .line-echart-block {
      width: 75%;
      display: flex;
      flex-wrap: wrap;
      padding: 0 0 0 32px;
      .custom-line-echart {
        width: 25%;
        margin-bottom: 16px;
        .title {
          font-size: 14px;
          color: #27272a;
          font-weight: 400;
          margin-bottom: 8px;
        }
        .content {
          display: flex;
          align-items: flex-end;
          height: 42px;
          > span {
            margin-right: 16px;
            font-size: 28px;
            color: #27272a;
            font-weight: 600;
            vertical-align: bottom;
            display: inline-block;
          }
          .custom-chart {
            .chart-container {
              width: 60px !important;
              height: 42px !important;
            }
          }
        }
      }
    }
    .bar-echart-block {
      border-left: 1px solid #e0e0e0;
      width: 25%;
      height: 220px;
      padding: 24px 16px 0 32px;
    }
  }

  .search-area {
    background: #ffffff;
    padding: 16px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 10px;
    // border-bottom: 1px solid #e8e8e8;
    border-radius: 12px 12px 0 0;
    .title {
      font-size: 16px;
      color: #27272a;
      font-weight: 600;
      // width: 180px;
      margin-right: 32px;
    }
    .search-content {
      width: 88%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .anticon {
        font-size: 16px;
        margin-left: 16px;
        &:hover {
          color: #27272a;
          cursor: pointer;
        }
      }
      .kill-btn {
        background: #008adc;
        border-radius: 4px;
        > span {
          padding: 16px 24px;
          font-size: 14px;
          color: #ffffff;
          font-weight: 400;
        }
      }
    }
  }
  .table-block {
    padding: 0 24px;
    border-radius: 0 0 12px 12px;
    background: #ffffff;
    /deep/.new-view-table {
      .ant-table-wrapper {
        .ant-spin-container {
          .ant-table {
            top: -8px;
            .ant-table-thead tr > .ant-table-selection-column {
              padding: 16px 0 16px 32px !important;
            }
            .ant-table-tbody tr > .ant-table-selection-column {
              padding: 16px 0 16px 32px !important;
            }
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1500px) {
  .database-audit-real-time {
    /deep/.echart-block {
      .line-echart-block {
        // padding: 24px 0px 8px 32px;
        .custom-line-echart {
          width: 25%;
          .content {
            .custom-chart {
              .chart-container {
                width: 50% !important;
              }
            }
          }
        }
      }
      // .bar-echart-block {
      //   min-height: 290px !important;
      // }
    }
  }
}
</style>