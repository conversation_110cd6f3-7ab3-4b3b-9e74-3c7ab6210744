<template>
  <div class="page-dbacat-charts">
    <RadioGroup
      :value="timeVal"
      :options="options"
      mode="button"
      @change="onChange"
    />
    <div class="chart-wrapper">
      <template v-for="(item, index) in charts">
        <div class="chart-item" :key="item.key">
          <div>{{item.key}}</div>
          <Chart />
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import RadioGroup from '@/components/RadioGroup';
import Chart from '@/components/Chart';

export default {
  components: { RadioGroup, Chart },
  data() {
    return {
      timeVal: '15min',
      options: [
        { label: '15分钟', value: '15min' },
        { label: '30分钟', value: '30min' },
        { label: '1小时', value: '1hour' },
        { label: '6小时', value: '6hour' },
        { label: '12小时', value: '12hour' },
        { label: '1天', value: '1d' }
      ],
      charts: [
        {
          key: 'cpu_useage_percent'
        },
        {
          key: 'mem_useage_percent'
        },
        {
          key: 'disk_useage_percent'
        },
        {
          key: 'xx_useage_percent'
        },
        {
          key: 'yy_useage_percent'
        },
        {
          key: 'zz_useage_percent'
        }
      ]
    };
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    onChange(val) {
      this.timeVal = val;
    }
  }
};
</script>

<style lang="less" scoped>
.page-dbacat-charts {
  margin-top: 32px;
  .chart-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 10px;
    .chart-item {
      width: calc((100% - 32px) / 3);
      border: 1px solid #dfdfdf;
      border-radius: 4px;

      > div:first-child {
        // border-bottom: 1px solid #f5f5f5;
        background: #f5f5f5;
        padding: 4px
      }

      .custom-chart {
        height: 300px !important;
      }
    }
  }
}
</style>
