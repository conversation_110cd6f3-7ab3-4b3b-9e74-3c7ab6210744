export default function (ctx) {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '配置项目',
      dataIndex: 'item_desc',
      key: 'item_desc',
      width: 180
    },
    {
      title: '描述',
      dataIndex: 'config_desc',
      key: 'config_desc',
      width: 250,
      scopedSlots: { customRender: 'config_desc' }
    },
    {
      title: '设定参数',
      dataIndex: 'item_value',
      key: 'item_value',
      width: 150,
      scopedSlots: { customRender: 'item_value' }
    },
    {
      title: '修改人',
      dataIndex: 'updated_by',
      key: 'updated_by',
      width: 100
    },
    {
      title: '修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
      width: 100
    }
  ];
  const fields = [
    {
      type: 'Input',
      label: 'ID',
      key: 'id',
      props: {
        placeholder: '请输入ID'
      }
    },
    {
      type: 'Input',
      label: '配置项目',
      mainSearch: true,
      key: 'item_desc',
      props: {
        placeholder: '请输入配置项目'
      }
    },
    {
      type: 'Input',
      label: '描述',
      key: 'config_desc',
      props: {
        placeholder: '请输入描述'
      }
    }
  ];
  return {
    columns,
    searchFields: fields
  };
}
