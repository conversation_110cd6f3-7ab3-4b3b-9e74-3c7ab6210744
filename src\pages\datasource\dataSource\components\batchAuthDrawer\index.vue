<template>
  <a-drawer
    title="授权用户"
    :visible="visible"
    @close="hide"
    :width="'50%'"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="config-datasource-batch-auth-drawer"
  >
    <a-spin :spinning="showLoading">
      <div class="auth-instance-box">
        <span>{{activeKey == 'instance' ? '授权实例' : activeKey == 'database' ? '授权数据库' : '授权表'}}</span>
        <a-badge
          :count="count"
          :overflowCount="99"
          :number-style="{
          backgroundColor: '#eee',
          fontSize: '12px',
          color: 'rgba(0,0,0,0.65)'
          }"
        />
      </div>
      <div v-if="!['field', 'tableDetail'].includes(activeKey)">
        <template v-if="databaseList && databaseList.length > 0">
          <a-row class="check-box-content">
            <!-- <div class="check-all-box">
            <a-checkbox :checked="checkAll" @change="onCheckAllChange">当页全选</a-checkbox>
            </div>-->
            <div class="check-group-box">
              <a-row>
                <a-col
                  v-for="option in databaseList"
                  :key="option.id"
                  :span="12"
                  class="check-box-item-col"
                >
                  <a-checkbox
                    :value="option.id"
                    :checked="checkedList.indexOf(option.id) > -1"
                    @change="onCheckChange"
                    class="instance-item-check-box"
                  >
                    <InstanceItem
                      mode="ellipsis"
                      :tagText="option.env"
                      :src="option.db_type"
                      :text="option.db_url"
                      width="320"
                      percent="85%"
                    ></InstanceItem>
                  </a-checkbox>
                </a-col>
              </a-row>
            </div>
          </a-row>
          <!-- 分页 -->
          <a-row class="page-box">
            <a-pagination
              size="small"
              :showSizeChanger="true"
              v-model="current"
              :page-size.sync="pageSize"
              :total="total"
              @change="pageChange"
              @showSizeChange="pageSizeChange"
            />
          </a-row>
        </template>
        <custom-empty v-else></custom-empty>
      </div>
      <div v-if="['field', 'tableDetail'].includes(activeKey)" class="table-detail">
        <Table
          ref="table"
          v-bind="tableParams || {}"
          :dataSource="dataSource || []"
          @selectChange="selectChange"
        >
          <!-- table插槽 -->
          <template slot="name" slot-scope="{ text }">
            <div class="field-icon" v-if="activeKey=='field'">
              <custom-icon type="lu-icon-field" />
              <span>{{text}}</span>
            </div>
            <span v-else>
              <custom-icon type="lu-icon-list" />
              {{text}}
            </span>
          </template>
          <template slot="table_name" slot-scope="{ text }">
            <div class="field-icon">
              <span>表名：</span>
              <custom-icon type="lu-icon-list" />
              <span>{{text}}</span>
            </div>
          </template>
        </Table>
      </div>
    </a-spin>
    <div>
      <Form v-bind="userFormParams" :formData="userData" ref="user"></Form>
      <Form v-bind="authFormParams" :formData="authData" ref="auth"></Form>
    </div>
    <div
      :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
    >
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="handleOk" type="primary">保存</a-button>
    </div>
  </a-drawer>
</template>

<script>
import InstanceItem from '@/components/Biz/InstanceItem';
import Table from '@/components/Table';
import Form from '@/components/Form';
import config from './config';
import {
  saveDatabaseAuth,
  saveInstanceAuth,
  saveTableDetailAuth,
  getInstanceList,
  getDatabaseList,
  saveFeildAuth
} from '@/api/config/dataSource';
export default {
  components: {
    Form,
    Table,
    InstanceItem
  },
  data() {
    this.config = config(this);
    return {
      activeKey: '',
      count: 0,
      visible: false,
      showLoading: false,
      userData: {},
      authData: {
        auth_set: 0
      },
      data: {},
      showData: [],
      // 搜索
      userFormParams: {
        fields: this.config.userFields,
        multiCols: 2
      },
      authFormParams: {
        fields: this.config.authFields(),
        multiCols: 2
      },
      formParams: {
        layout: 'vertical',
        multiCols: 1,
        labelCol: { span: 24 },
        wrapperCol: { span: 16 },
        fields: this.config.fields
      },
      currentInstance: {},
      instanceOptions: [],
      // 选择
      nowCheckedNum: 0,
      checkAll: false,
      checkedList: [], // 被选中的实例
      databaseList: [], // 全部的实例
      // 分页
      total: 0,
      current: 1,
      pageSize: 10,
      // 已选择的数据库
      databaseMap: {},
      checkedDatabaseData: {},
      dataSource: [],
      tableParams: {
        url: '',
        method: 'post',
        reqParams: {},
        showHeader: false,
        columns: this.config.columns,
        size: 'small',
        rowKey: 'id',
        rowSelection: {
          type: 'checkbox', // 多选单选
          columnWidth: '20',
          selectedRowKeys: []
        },
        loaded: this.onTableLoaded,
        scroll: { x: 'max-content' }
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data, bool, type) {
      this.visible = true;
      this.activeKey = type;
      this.authData = {
        auth_set: 0
      };
      this.showData = _.cloneDeep(data);
      this.checkedList = _.cloneDeep(data);

      // dml_switch开关 1：开，0 ：关。 0 不展示，1 展示
      let dmlSwitch = _.get(this.$store.state, 'project.dmlSwitch');
      this.$set(this.authFormParams, 'fields', this.config.authFields(dmlSwitch));
      console.log(this.activeKey, 'show');

      if (type == 'instance' || type == 'database') {
        type == 'instance'
          ? this.getInstanceListFn()
          : this.getDatabaseListFn();
      } else if (type == 'tableDetail' || type == 'field') {
        let arr = [
          {
            key: 'tableDetail',
            url: '/sqlreview/project/authorization_table',
            req: 'table_id',
            columns: this.config.columns
          },
          {
            key: 'field',
            url: '/sqlreview/project/authorization_columns',
            req: 'columns_id',
            columns: this.config.fieldColumns
          }
        ];
        arr.forEach(item => {
          if (item.key == type) {
            this.$set(this.tableParams, 'url', item.url);
            this.$set(this.tableParams, 'columns', item.columns);
            this.$set(this.tableParams, 'reqParams', {
              [item.req]: data,
              _t: +new Date()
            });
          }
        });

        // 进来之后全选
        const selectedRowKeys = this.showData || [];
        this.$set(
          this.tableParams.rowSelection,
          'selectedRowKeys',
          selectedRowKeys
        );
      }
    },
    hide() {
      this.visible = false;
      this.userData = {};
      this.authData = {};
      this.data = {};
      this.total = 0;
      this.current = 1;
      this.pageSize = 10;
    },
    handleCancel() {
      this.hide();
    },
    // 保存
    handleOk() {
      if (this.activeKey == 'instance' && this.checkedList.length <= 0) {
        this.$message.warning('请选择实例');
        return;
      }
      if (this.activeKey == 'database' && this.checkedList.length <= 0) {
        this.$message.warning('请选择数据库');
        return;
      }
      const tableIds = _.get(this.tableParams, 'rowSelection.selectedRowKeys');
      if (this.activeKey == 'tableDetail' && tableIds.length <= 0) {
        this.$message.warning('请选择数据库');
        return;
      }
      if (this.activeKey == 'field' && tableIds.length <= 0) {
        this.$message.warning('请选择字段');
        return;
      }
      const { user, auth } = this.$refs;
      const userData = user.getData();
      const authData = auth.getData();
      let params = { ...userData, ...authData };
      if (this.activeKey == 'instance') {
        params.data_source_id = this.checkedList;
      } else if (this.activeKey == 'database') {
        params.schema_id = this.checkedList;
      } else if (this.activeKey == 'tableDetail') {
        params.table_id = tableIds;
      } else if (this.activeKey == 'field') {
        params.columns_id = tableIds;
      }

      // let saveAuth =
      //   this.activeKey == 'instance'
      //     ? saveInstanceAuth
      //     : this.activeKey == 'database'
      //     ? saveDatabaseAuth
      //     : saveTableDetailAuth;

      let saveAuth = saveInstanceAuth;
      switch (this.activeKey) {
        case 'instance':
          saveAuth = saveInstanceAuth;
          break;
        case 'database':
          saveAuth = saveDatabaseAuth;
          break;
        case 'field':
          saveAuth = saveFeildAuth;
          break;
        case 'tableDetail':
          saveAuth = saveTableDetailAuth;
          break;
        default:
          break;
      }

      Promise.all([user.validate(), auth.validate()]).then(valid => {
        if (valid) {
          this.$showLoading();
          saveAuth(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.hide();
                this.$hideLoading({ tips: '保存成功' });
                this.$emit('refresh');
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    getInstanceListFn() {
      this.showLoading = true;
      const params = {
        data_source_id: this.showData,
        page_number: this.current,
        page_size: this.pageSize
      };
      getInstanceList(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.showLoading = false;
            const resData = _.get(res, 'data.data');
            this.count = resData.count;
            this.total = resData.count;
            this.currentInstance = params;
            this.databaseList = resData.results;
            this.checkedDatabaseData = {};
            // this.getDatabaseMap();
          } else {
            this.showLoading = false;

            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.showLoading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getDatabaseListFn() {
      this.showLoading = true;
      const params = {
        schema_id: this.showData,
        page_number: this.current,
        page_size: this.pageSize
      };
      getDatabaseList(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.showLoading = false;
            const resData = _.get(res, 'data.data');
            this.count = resData.count;
            this.total = resData.count;
            this.currentInstance = params;
            this.databaseList = resData.results;
            this.checkedDatabaseData = {};
            // this.getDatabaseMap();
          } else {
            this.showLoading = false;

            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.showLoading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 表列表 数据
    onTableLoaded(req, res) {
      const resData = _.get(res, 'data.data');
      this.count = resData.count;
      this.total = resData.count;
      this.dataSource = resData.results;
    },
    // 表列表 表格勾选值
    selectChange(data = {}) {
      this.$set(
        this.tableParams.rowSelection,
        'selectedRowKeys',
        data.selectedRowKeys
      );
      // this.selectedRowKeys = data.selectedRowKeys;
    },
    // 分页
    pageChange(current, pageSize) {
      console.log(current, pageSize);
      this.current = current;
      this.pageSize = pageSize;
      if (this.activeKey == 'instance') {
        this.getInstanceListFn();
      } else if (this.activeKey == 'database') {
        this.getDatabaseListFn();
      }
      // this.databaseList = this.config['databaseList' + current];
      // this.getDatabaseMap();
      // 重新获取全选状态
      // this.getCheckAllStatus();
    },
    pageSizeChange(current, pageSize) {
      this.pageSize = pageSize;
      this.current = current;
      if (this.activeKey == 'instance') {
        this.getInstanceListFn();
      } else if (this.activeKey == 'database') {
        this.getDatabaseListFn();
      }
      // this.getDatabaseMap();
      // 重新获取全选状态
      // this.getCheckAllStatus();
    },
    // check单选中/不选中
    onCheckChange(e) {
      const { value, checked } = e.target;
      if (checked) {
        this.nowCheckedNum++;
        this.checkedList.push(value);
        // this.checkedDatabaseData[value] = { ...this.databaseMap[value] };
      } else {
        this.nowCheckedNum--;
        this.checkedList.splice(this.checkedList.indexOf(value), 1);
        // delete this.checkedDatabaseData[value];
      }
      // 去重
      this.checkedList = [...new Set(this.checkedList)];
      // 全选状态
      // this.checkAll = this.nowCheckedNum === this.databaseList.length;
    }
    // 全选/全不选
    // onCheckAllChange(e) {
    //   this.checkAll = e.target.checked;
    //   this.nowCheckedNum = 0;
    //   if (e.target.checked) {
    //     this.databaseList.map(item => {
    //       this.nowCheckedNum++;
    //       this.checkedList.push(item);
    //       this.checkedDatabaseData[item] = { ...this.databaseMap[item] };
    //     });
    //   } else {
    //     this.databaseList.map(item => {
    //       this.checkedList.splice(this.checkedList.indexOf(item), 1);
    //       delete this.checkedDatabaseData[item];
    //     });
    //   }
    //   // 去重
    //   this.checkedList = [...new Set(this.checkedList)];
    // },
    // 全选状态
    // getCheckAllStatus() {
    //   this.nowCheckedNum = 0;
    //   this.databaseList.map(item => {
    //     if (this.checkedList.indexOf(item) > -1) {
    //       this.nowCheckedNum++;
    //     }
    //   });
    //   this.checkAll = this.nowCheckedNum === this.databaseList.length;
    // }
    // 获取map数据
    // getDatabaseMap() {
    //   this.databaseList.forEach(item => {
    //     this.databaseMap[item] = { schema_name: item, ...this.currentInstance };
    //   });
    //   // 重新获取全选状态
    //   this.getCheckAllStatus();
    // }
  }
};
</script>

<style lang="less" scoped>
.config-datasource-batch-auth-drawer {
  /deep/ .ant-drawer-content-wrapper {
    min-width: 850px;
  }
}
.auth-instance-box::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}
.config-datasource-batch-auth-drawer {
  /deep/ .ant-form-item-label > label {
    justify-content: start !important;
  }
}
/deep/ .hidden-label {
  > .multi-cols-item {
    > .ant-form-item-label {
      display: none;
    }
  }
}
.check-box-content {
  .check-all-box {
    margin-bottom: 8px;
    padding-left: 16px;
  }
  .check-group-box {
    padding: 16px 16px 0;
    background: #f9fafa;
    .check-box-item-col {
      margin-bottom: 16px;
      padding-right: 24px;
      .instance-item-check-box {
        display: flex;
        align-items: center;
      }
    }
  }
}
.page-box {
  text-align: right;
  padding: 16px 0 24px;
}
.table-detail {
  padding-top: 24px;
  // width: 400px;
}
/deep/ .ant-table {
  line-height: 0.8;
}
.field-icon {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  .anticon {
    margin-right: 8px;
  }
}
</style>
