<template>
  <div class="padetail-report">
    <div>
      <div class="instance-box">
        <InstanceItem v-if="!!db_type" :tagText="env" :src="db_type" :text="name"></InstanceItem>
        <div class="check-report-default-time">
          <a-range-picker
            v-model="dateModel"
            :show-time="{
              hideDisabledOptions: true,
              defaultValue: [
                moment('00:00:00', 'HH:mm:ss'),
                moment('11:59:59', 'HH:mm:ss')
              ]
            }"
            :format="dateFormat"
            @ok="rangePickerChange"
          />
        </div>
      </div>
      <div class="report-wrapper">
        <Tabs
          v-model="activeKey"
          :tabsList="tabsList"
          mode="tag"
          @change="tabChange"
          class="report-wrapper-tabs"
          :animated="false"
        />
        <a-spin :spinning="spinning">
          <commonView
            ref="commonView"
            :chartList="chartList"
            :commonViewLoading="loading"
            @onClickLine="onClickLine"
            @onClickxAxisTwice="onClickxAxisTwice"
          />
        </a-spin>
      </div>
    </div>
    <div>
      <Table ref="table" v-bind="tableParams || {}" :dataSource="dataSource" @reset="reset">
        <template slot="query" slot-scope="{ text }">
          <LimitLabel :label="text || ''" :limit="16" format="sql"></LimitLabel>
        </template>
        <!-- 审核时间 -->
        <template slot="created_at" slot-scope="{ record }">
          <span>{{record.created_at && moment(record.created_at).format('YYYY-MM-DD HH:mm:ss')}}</span>
        </template>
        <span slot="ai_status" slot-scope="{ record }">
          <span v-if="record.ai_status === null">--</span>
          <a-badge :color="record.ai_status | color" :text="record.ai_status | status" />
        </span>
        <span slot="action" slot-scope="{ text, record }">
          <a @click="toDetail(text, record, $event)">详情</a>
        </span>
      </Table>
    </div>
  </div>
</template>

<script>
import { getChartData } from '@/api/home';
import Table from '@/components/Table';
import Chart from '@/components/Chart';
import commonView from './commonView/index';
import Tabs from '@/components/Tabs';
import LimitLabel from '@/components/LimitLabel';
import InstanceItem from '@/components/Biz/InstanceItem';
import moment from 'moment';
import common from '@/utils/common';

import config from './config';
export default {
  name: '',
  components: {
    Tabs,
    Table,
    Chart,
    commonView,
    InstanceItem,
    LimitLabel
  },
  props: {},
  data() {
    this.config = config(this);
    const timeData = this.getTime();
    const startTime = timeData[0];
    const endTime = timeData[1];
    let activeKey = this.$route.query.activeKey || 'total_exec_time';
    return {
      data: {},
      spinning: false,
      id: this.$route.query.task_id,
      // id: this.$route.query.id || 28, // 测试数据后面删掉
      db_type: this.$route.query.db_type || '',
      name: this.$route.query.name || '',
      env: (this.$route.query.env || '').toUpperCase(),
      query: this.$route.query.query || '',
      ai_status: this.$route.query.ai_status || '',
      tableParams: {
        url: '/sqlreview/after_audit/postgre_detail_list',
        method: 'post',
        reqParams: {
          task_id: this.$route.query.task_id,
          start_time: this.startTime,
          end_time: this.endTime
        },
        columns: this.config.columns,
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 'max-content' }
      },
      dataSource: [],
      activeKey,
      tabsList: [],
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 3
      },
      loading: false,
      chartList: null,
      dateModel: [
        moment(this.startTime, 'YYYY-MM-DD HH:mm:ss'),
        moment(this.endTime, 'YYYY-MM-DD HH:mm:ss')
      ],
      dateFormat: ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss'],
      startTime: startTime,
      endTime: endTime
      // rangePickerStartTime: '',
      // rangePickerEndTime: ''
    };
  },
  mounted() {
    this.setNavi();
    this.getCheckData(this.activeKey);
  },
  created() {},
  beforeDestroy() {},
  methods: {
    moment,
    onClickLine(data) {
      this.clickTime = data;
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        click_time: data,
        start_time: '',
        end_time: ''
      });
      table.refresh();
    },
    onClickxAxisTwice(data) {
      this.clickTime = '';
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        click_time: '',
        start_time: data[0],
        end_time: data[1]
      });
      table.refresh();
    },
    getTime() {
      const pageCache = this.$store.state.common.pageCache
        .homePostauditPgdetail;
      if (!_.isEmpty(pageCache)) {
        this.startTime = pageCache.startTime;
        this.endTime = pageCache.endTime;
      } else {
        this.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        this.startTime = moment()
          .subtract(12, 'hour')
          .format('YYYY-MM-DD HH:mm:ss');
      }
      return [this.startTime, this.endTime];
    },
    // 请求查看报告chart数据
    getCheckData(param) {
      this.spinning = true;
      getChartData({
        task_id: this.$route.query.task_id,
        start_time: this.startTime,
        end_time: this.endTime,
        index_type: param
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.spinning = false;
            if (_.isEmpty(this.tabsList)) {
              this.tabsList = res.data.data.header || [];
            }
            this.chartList = res.data.data.chart_list || [];
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.spinning = false;
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    // chart图表默认时间搜索
    rangePickerChange(values) {
      const start = moment(values[0]).format('YYYY-MM-DD HH:mm:ss');
      const end = moment(values[1]).format('YYYY-MM-DD HH:mm:ss');
      this.$set(this, 'startTime', start);
      this.$set(this, 'endTime', end);
      // this.rangePickerStartTime = start;
      // this.rangePickerEndTime = end;
      this.$store.commit('common/setPageCache', {
        homePostauditPgdetail: { startTime: start, endTime: end }
      });
      this.dateModel = values;
      this.getCheckData(this.activeKey);
      // this.$set(this.tableParams, 'reqParams', {
      //   start_time: start,
      //   end_time: end,
      //   task_id: this.id
      // });
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        start_time: this.startTime,
        end_time: this.endTime
      });
      table.refresh();
    },
    // 切换 tab 选项卡
    tabChange(activeKey) {
      this.activeKey = activeKey;
      this.getCheckData(activeKey);
      this.clickTime = '';
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        click_time: '',
        start_time: this.startTime,
        end_time: this.endTime
      });
      this.reset();
      table.refresh();
    },
    toDetail(text, record, e) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        click_time: '',
        start_time: this.startTime,
        end_time: this.endTime
      });
      this.$router.push({
        name: 'home-postaudit-pgsqldetail',
        query: {
          id: record.id,
          start_time: this.startTime,
          end_time: this.endTime,
          ai_status: searchParams.ai_status,
          activeKey: this.activeKey,
          click_time: this.clickTime,
          query: searchParams.query,
          username: searchParams.username,
          db_name: searchParams.db_name
        }
      });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      table.refresh(null);
    },
    // 重置
    reset(data) {
      const commonView = this.$refs.commonView;
      commonView.reset();
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'home-postaudit-pgdetail') {
          path =
            sourcePath +
            '?task_id=' +
            this.id +
            '&db_type=' +
            this.db_type +
            '&name=' +
            this.name +
            '&env=' +
            this.env;
        }
        return path;
      });
    }
  },
  watch: {},
  filters: {
    status(value) {
      let obj = {
        0: '未知',
        1: '通过',
        '-1': '未通过',
        2: '白名单通过',
        9: '错误'
      };
      return obj[value];
    },
    color(value) {
      let obj = {
        0: '#B0AEAE',
        1: '#52C41A',
        '-1': '#FF4D4F',
        2: '#52C41A',
        9: '#FF4D4F'
      };
      return obj[value];
    }
  }
};
</script>

<style lang="less" scoped>
.instance-box {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 0 16px 0;
  /deep/ .database-image .iconStyle {
    font-size: 24px;
    padding-top: 0;
  }
  /deep/.limit-label.ellipsis > pre {
    font-size: 16px;
    vertical-align: middle;
  }
}
.report-wrapper {
  .report-wrapper-tabs {
    /deep/.ant-tabs-tab {
      padding: 5px 12px;
      line-height: 24px;
      font-size: 14px;
      background: #f2f4f5 !important;
      border-radius: 8px;
      border: 2px solid #fff;
    }
    /deep/
      .ant-tabs-bar
      > .ant-tabs-nav-container
      > .ant-tabs-nav-wrap
      > .ant-tabs-nav-scroll
      > .ant-tabs-nav
      > div
      > .ant-tabs-tab-active {
      padding: 5px 12px;
      line-height: 24px;
      font-size: 14px;
      color: rgba(255, 255, 255) !important;
      background: #1890ff !important;
      border-radius: 8px;
      border: 2px solid #fff;
    }
  }
}
</style>
