export default function (ctx) {
  const baseInfo = [
    // 任务名称
    {
      type: 'Input',
      label: '任务名称',
      key: 'name',
      rules: [{ required: true, message: '该项为必填项' }]
    },
    // 任务类型
    {
      type: 'Select',
      label: '任务类型',
      key: 'type',
      props: {
        options: [
          { label: '单次', value: 1 },
          { label: '多次', value: 2 }
        ]
      },
      rules: [{ required: true, message: '该项为必填项' }],
      listeners: {
        change: (value) => {
          ctx.$refs.form.saving({
            type: value
          });
        }
      }
    },
    // 任务频率
    (formData = {}) => {
      return {
        type: 'Input',
        label: '任务频率',
        key: 'frequency',
        rules: [{ required: true, message: '', trigger: 'change' }],
        visible: formData.type === 2,
        hideComponent: true,
        slots: [{ key: 'frequency' }]
      };
    },
    // 数据源选择
    (formData = {}) => {
      return {
        type: 'Select',
        label: '数据源选择',
        key: 'data_source',
        props: {
          url: '/sqlreview/project/data_source_choices',
          reqParams: {
            type: 'oracle'
          },
          disabled: ctx.isDisabled
        },
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              data_source: value,
              datasource_id: null
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      };
    },
    // 审核用户
    (formData = {}) => {
      return {
        type: 'Select',
        label: '审核用户',
        key: 'schema',
        props: {
          url: '/sqlreview/project/get_schema_list',
          reqParams: {
            datasource_id: formData.data_source
          },
          allowSearch: true,
          backSearch: true,
          limit: 50,
          disabled: ctx.isDisabled
        },
        rules: [{ required: true, message: '该项为必填项' }]
      };
    },
    // 时间范围选择
    (formData = {}) => {
      return {
        type: 'RangePicker',
        label: '时间范围选择',
        key: 'time_range',
        props: {
          showTime: {
            format: 'HH:mm:ss'
          }
        },
        listeners: {
          change: (value) => {
            const timeArr = [];
            value.forEach((item) => {
              timeArr.push(item.format('YYYY-MM-DD HH:mm:ss'));
            });
            ctx.$refs.form.saving({
              time_range: timeArr
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项' }],
        visible: formData.type === 1
      };
    },
    // 规则集选择
    (formData = {}) => {
      return {
        type: 'Select',
        label: '规则集选择',
        key: 'rule_set',
        props: {
          url: '/sqlreview/project/rule_set_all',
          mode: 'multiple',
          reqParams: { db_type: 'oracle' }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      };
    }
  ];

  const cornInfo = [
    // 天 周 月
    {
      type: 'Select',
      label: '',
      key: 'type',
      width: '100%',
      props: {
        options: [
          {
            label: '每天',
            value: '每天'
          },
          {
            label: '每周',
            value: '每周'
          },
          {
            label: '每月',
            value: '每月'
          }
        ]
      },
      listeners: {
        change: (value) => {
          ctx.type = value;
          ctx.$refs.form.saving({
            type: value
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    },
    // 每周
    (formData = {}) => {
      return {
        type: 'Select',
        label: '',
        key: 'week',
        width: '100%',
        props: {
          options: ctx.weekOption
        },
        visible: formData.type === '每周',
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              week: value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      };
    },
    // 每月
    (formData = {}) => {
      return {
        type: 'Select',
        label: '',
        key: 'month',
        width: '100%',
        props: {
          options: ctx.monthOption
        },
        visible: formData.type === '每月',
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              month: value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      };
    },
    // 时间
    {
      type: 'TimePicker',
      label: '',
      key: 'time',
      width: '100%',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      },
      listeners: {
        change: (value) => {
          ctx.$refs.form.saving({
            time: value
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    }
  ];
  return {
    baseInfo,
    cornInfo
  };
}
