<template>
  <div class="config-wrapper page-list-single">
    <!-- <a-card style="width:100%" class="common-page-card" :title="title" :bordered="false">
    </a-card>
      <div slot="extra" class="search-tools">
        <slot name="actions" v-bind="{ addLine }"></slot>
        <a-button v-if="!$scopedSlots.actions" type="primary" @click="addLine">新增</a-button>
    </div>-->
    <div class="frame-button-wrapper">
      <slot name="actions" v-bind="{ addLine }"></slot>
      <a-button
        v-if="!$scopedSlots.actions"
        slot="extra"
        class="highlight"
        icon="plus"
        @click="addLine"
      >新增</a-button>
    </div>
    <!-- <SearchArea v-bind="searchParams" @reset="reset" @search="search"></SearchArea> -->
    <div class="table-content">
      <TableEdit ref="tableEdit" v-bind="tableParams || {}"></TableEdit>
    </div>
  </div>
</template>

<script>
import SearchArea from '@/components/SearchArea';
import TableEdit from '@/components/TableEdit';
const defaultTableParams = {
  type: 'lineEdit',
  actionBtns: ['edit', 'remove'],
  modalEdit: true,
  pagination: {
    showTotal: (total, range) => {
      return `共${total}条`;
    },
    showSizeChanger: true,
    showQuickJumper: true,
    size: 'small'
  },
  rowKey: 'id'
};
const defaultSerachParams = {
  multiCols: 3
};
export default {
  components: {
    SearchArea,
    TableEdit
  },
  props: {
    tableConfig: {
      type: Object,
      default: () => {
        return {};
      }
    },
    searchConfig: {
      type: Object,
      default: () => {
        return {};
      }
    },
    title: String
  },
  data() {
    return {
      searchParams: {
        ...defaultSerachParams,
        ...this.searchConfig
      },
      tableParams: {
        ...defaultTableParams,
        ...this.tableConfig
      }
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {
    console.log('---$slots----');
    console.log(this.$scopedSlots);
    console.log(this.searchConfig);
  },
  methods: {
    reset() {
      const { tableEdit } = this.$refs;
      tableEdit.refresh();
    },
    search(data) {
      console.log(data, 'gegegege');
      const { tableEdit } = this.$refs;
      tableEdit.refresh(null, data);
    },
    addLine() {
      this.$refs.tableEdit.addLine();
    }
  }
};
</script>

<style scoped lang="less">
.search-tools {
  // margin-bottom: 10px;
}
</style>
