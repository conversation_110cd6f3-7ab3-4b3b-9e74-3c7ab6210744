<template>
  <div>
    <Blocks :value="blocks" mode="card" :loading="loading" :bodyStyle="{paddingBottom: 0}">
      <Form slot="one" v-bind="formParams" />
      <a-switch style="margin-left: 8px;" slot="oneTitleSlot"></a-switch>
      <Form slot="two" v-bind="formParams" />
    </Blocks>
    <Blocks :value="blocks" :spinning="loading">
      <a-card slot="one">
        welcome
      </a-card>
      <a-card slot="two">
        welcome
      </a-card>
      <a-button slot="oneBtn">oneBtn</a-button>
      <a-space slot="twoBtn">
        <a-button size="small">actions</a-button>
        <a-button type="primary" size="small">primary</a-button>
      </a-space>
    </Blocks>
    <Blocks :value="slotBlocks">
      <a-switch style="margin-left: 8px;" slot="titleRight" default-checked></a-switch>
      <a-card slot="three">
        标题新增slot
      </a-card>
    </Blocks>
  </div>
</template>

<script>
import Blocks from '@/components/Blocks';
import Form from '@/components/Form';
export default {
  components: { Blocks, Form },
  props: {},
  data() {
    return {
      blocks: [
        {
          key: 'one',
          icon: 'edit',
          title: 'one',
          extra: 'oneBtn',
          style: { padding: '24px' },
          titleSlot: 'oneTitleSlot'
        },
        {
          key: 'two',
          title: 'two',
          titleTips: '@提示：扩展信息中不做配置的话系统将按照默认配置执行',
          extra: 'twoBtn'
        }
      ],
      slotBlocks: [
        {
          key: 'three',
          title: '标题',
          titleTips: '@提示：扩展信息中不做配置的话系统将按照默认配置执行',
          titleSlot: 'titleRight'
        }
      ],
      loading: true,
      formParams: {
        multiCols: 2,
        labelCol: { span: 24 },
        wrapperCol: { span: 20 },
        fields: [
          {
            type: 'Input',
            label: '资源编码',
            key: 'resource_code',
            props: {
              disabled: true
            },
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' }
            ]
          },
          {
            type: 'Input',
            label: '资源名称',
            key: 'resource_name',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' }
            ]
          }
        ]
      }
    };
  },
  computed: {},
  created() {},
  mounted() {
    setTimeout(() => { this.loading = false }, 3000)
  },
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
</style>