<template>
  <div class="config-rules-content">
    <div class="frame-button-wrapper">
      <a-button class="highlight" slot="extra" @click="backTo">返回</a-button>
      <a-button type="primary" slot="extra" @click="leadingOut">导出</a-button>
    </div>
    <div class="home-detail">
      <div class="common-view">
        <div>
          <span class="common-view-title">审核方式</span>
          <span class="common-view-des">{{ headerInfo.audit_type || '--' }}</span>
        </div>

        <div v-if="headerInfo.audit_type === '在线审核'">
          <span class="common-view-title">应用名称</span>
          <span class="common-view-tag">{{ headerInfo.project_name || '--' }}</span>
        </div>

        <div v-else>
          <span class="common-view-title">数据库类型</span>
          <span class="common-view-des">{{ headerInfo.db_type || '--' }}</span>
        </div>

        <div v-if="headerInfo.audit_type === '在线审核'">
          <span class="common-view-title">数据源</span>
          <span v-if="!headerInfo.data_Source_name" class="common-view-des">--</span>
          <span
            v-for="item in headerInfo.data_Source_name"
            :key="item"
            class="data-source-name-tag"
          >
            <Tag type="Env" :text="headerInfo.env.toUpperCase()" />
            <DbImg :value="headerInfo.db_type" :schemaName="item" :limit="16" />
          </span>
          <!-- <a-tag class="common-view-tag">{{item}}</a-tag> -->
        </div>

        <div v-if="headerInfo.file_root_name">
          <span class="common-view-title">上传文件名称</span>
          <span class="common-view-des">{{ headerInfo.file_root_name || '--' }}</span>
        </div>
        <!-- </div> -->

        <!-- <div class="common-view"> -->
        <div>
          <span class="common-view-title">任务类型</span>
          <span class="common-view-des">{{headerInfo.source_type ? '文件上传' : '文本粘贴'}}</span>
        </div>

        <div>
          <span class="common-view-title">发起人</span>
          <span class="common-view-des">{{ headerInfo.created_by || '--' }}</span>
        </div>

        <div>
          <span class="common-view-title">发起时间</span>
          <span
            class="common-view-des"
          >{{ headerInfo.created_at && headerInfo.created_at.replace("T"," ") || '--' }}</span>
        </div>
      </div>
      <a-divider />
      <Table
        ref="PageList"
        v-bind="tableParams"
        :needSplitSearch="true"
        :dataSource="dataSource"
        class="new-view-table"
      >
        <!-- table插槽 -->
        <span slot="source_type" slot-scope="{ record }">
          <span>{{record.source_type ? '文件上传' : '文本粘贴'}}</span>
        </span>
        <!-- 触发规则 -->
        <template slot="ai_message" slot-scope="{ text }">
          <LimitLabel :label="text || ''" :limit="16"></LimitLabel>
        </template>
        <!-- SQL文本 -->
        <template slot="sql_text" slot-scope="{ text }">
          <LimitLabel :label="text || ''" :limit="16" :nowrap="true"></LimitLabel>
        </template>
        <!-- review状态 -->
        <span slot="ai_status" slot-scope="{ record }">
          <span v-if="record.ai_status === null">--</span>
          <a-badge :color="record.ai_status | color" :text="record.ai_status | status" />
        </span>
        <!-- SQL质量 -->
        <template slot="risk" slot-scope="{ text }">
          <span :class="['risk-level',`risk-${text}`]">{{riskText[text]}}</span>
        </template>
        <span slot="action" slot-scope="{ record,index }">
          <a @click="toDetail(record,index)">详情</a>
        </span>
      </Table>
      <!-- 详情弹窗 -->
      <infoDrawer ref="infoDrawer"></infoDrawer>
    </div>
  </div>
</template>

<script>
import { quickReviewExport } from '@/api/quickaudit';
import Table from '@/components/Table';
import common from '@/utils/common';
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import infoDrawer from './components/infoDrawer';
import LimitLabel from '@/components/LimitLabel';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import config from './config';
export default {
  components: { Table, infoDrawer, LimitLabel, Tag, DbImg },
  mixins: [bodyMinWidth(1280)],
  data() {
    this.config = config(this);
    this.quick_audit_id = this.$route.query.quick_audit_id;
    return {
      tableParams: {
        url: '/sqlreview/quick_audit/quick_review1',
        // url: '/sqlreview/review/review-detail-list',
        method: 'post',
        reqParams: {
          quick_audit_id: this.quick_audit_id
          // review_id: 1004720,
          // is_status: 1
        },
        loaded: this.onTableLoaded,
        columns: this.config.columns,
        searchFields: this.config.searchFields,
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      headerInfo: {},
      pageNum: 0,
      dataSource: [],
      riskText: {
        high: '高风险',
        low: '低风险',
        null: '无风险',
        error: '异常'
      }
    };
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    onTableLoaded(req, res) {
      this.headerInfo = res.data.data.header;
      this.dataSource = res.data.data.results;
      // this.aiStatus = JSON.parse(res.config.data).ai_status || '';
      this.pageNum = JSON.parse(res.config.data).page_number;
    },
    // 查看详情
    toDetail(record, index) {
      const idx = this.pageNum - 1;
      const idxs = idx * 10;
      index = index + idxs;
      this.$refs.infoDrawer.show(record.id, index);
    },
    // 导出
    leadingOut() {
      this.$showLoading({
        tips: `下载中...`
      });
      quickReviewExport({ quick_audit_id: this.quick_audit_id })
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 返回
    backTo() {
      this.$router.push({ name: 'quickAudit' });
    }
  },
  filters: {
    status(value) {
      let obj = {
        0: '未知',
        1: '通过',
        '-1': '未通过',
        2: '白名单通过',
        9: '错误',
        '-2': '待审核'
      };
      return obj[value];
    },
    color(value) {
      let obj = {
        0: '#B0AEAE',
        1: '#52C41A',
        '-1': '#FF4D4F',
        2: '#52C41A',
        9: '#FF4D4F',
        '-2': '#FF4D4F'
      };
      return obj[value];
    }
    // sqlLevelStatus(value) {
    //   let obj = {
    //     0: '未知',
    //     1: '劣质',
    //     2: '一般',
    //     3: '良好',
    //     9: '优秀'
    //   };
    //   return obj[value];
    // }
  }
};
</script>

<style lang="less" scoped>
.config-rules-content {
  .home-detail {
    margin-top: 10px;
    .common-view {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      margin-left: 32px;
      > div {
        display: flex;
        flex-direction: column;
        align-items: left;
        margin-right: 64px;
        margin-right: 8%;
        white-space: nowrap;
        margin-bottom: 36px;
        .data-source-name-tag {
          display: flex;
          align-items: center;
          padding: 2px 16px;
          border: 1px solid rgba(228, 228, 231, 1);
          border-radius: 16px;
          margin-top: -6px;
          > .ant-tag {
            margin-right: 4px;
            padding: 0 6px;
            border: none;
            // font-family: PingFangSC-Medium;
            font-size: 12px;
            color: #fff;
            font-weight: 600;
            border-radius: 6px;
            line-height: 20px;
          }
          > .database-image {
            /deep/.iconClass {
              .limit-label {
                > pre {
                  // font-family: PingFangSC-Medium;
                  font-size: 14px;
                  color: #27272a;
                  font-weight: 500;
                }
              }
            }
          }
        }
        .common-view-title {
          font-family: PingFangSC-Semibold;
          font-size: 16px;
          color: #27272a;
          font-weight: 600;
          margin-bottom: 8px;
        }
        .common-view-des {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #71717a;
          font-weight: 400;
          // flex-shrink: 0;
        }
        .common-view-tag {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #71717a;
          font-weight: 400;
          padding: 2px 16px;
          border-radius: 16px;
          background: #ffffff;
          border: 1px solid #e4e4e7;
          margin-top: -6px;
          max-width: 200px;
        }
      }
    }
    .ant-divider-horizontal {
      background: #f2f2f2;
      margin: 0;
      margin-bottom: 12px;
    }
    /deep/ .ant-table .level_category {
      > span {
        white-space: nowrap;
      }
      .ant-divider {
        margin: 0 2px;
      }
    }
    .risk-level {
      width: 56px;
      height: 28px;
      font-size: 12px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .risk-high {
      color: #e71d36;
      background: #fdeff1;
    }
    .risk-low {
      color: #991b1b;
      background: #fef5ec;
    }
    .risk-null {
      color: #3a974c;
      background: #ecf5ee;
    }
    .risk-error {
      color: #71717a;
      background: rgba(0, 59, 114, 0.06);
    }
    // @media screen and (max-width: 1500px) {
    //   .common-view > div {
    //     width: 25%;
    //   }
    // }
  }
}
</style>
