<template>
  <div class="quick-audit-coder-comp">
    <a-tabs
      type="card"
      :active-key="activeKey"
      @change="tabsChange"
      class="xml-or-sql"
    >
      <a-tab-pane key="sql" tab="SQL" :disabled="isFinished">
        <div
          class="coder-content"
          v-if="
            !isFinished ||
            mode == 'history' ||
            (progress == 100 && auditResults.length <= 0)
          "
        >
          <!-- 编辑和新增 -->
          <Coder
            :key="`edit`"
            ref="sqlCoder"
            v-model="sqlContent"
            class="quick-aduit-coder"
            v-bind="sqlCoderParams"
            type="sql"
            theme="default"
            v-if="!isFinished"
          ></Coder>
          <!-- 查看 -->
          <!-- <Prettier
            :value="sqlContent"
            type="sql"
            v-if="
              mode == 'history' || (progress == 100 && auditResults.length <= 0)
            "
          /> -->
          <Coder
            :key="`view`"
            v-model="sqlContent"
            type="sql"
            class="quick-aduit-coder"
            :options="{ readOnly: true }"
            v-if="
              mode == 'history' || (progress == 100 && auditResults.length <= 0)
            "
          ></Coder>
        </div>
        <div v-else class="sql-result">
          <a-collapse
            :activeKey="sqlActiveKey"
            :bordered="false"
            @change="sqlChange"
            :accordion="true"
          >
            <a-collapse-panel
              :key="JSON.stringify(item)"
              v-for="item in auditResults"
            >
              <div slot="header" class="header">
                <span :class="[item.risk]">{{ riskText[item.risk] }}</span>
                {{ item.sql_text }}
              </div>
              <!-- <Prettier :value="item.sql_text" type="sql" /> -->
              <Coder
                v-model="item.sql_text"
                type="sql"
                class="quick-aduit-coder"
                :options="{ readOnly: true }"
              ></Coder>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </a-tab-pane>
      <a-tab-pane key="xml" tab="XML" :disabled="isFinished">
        <div
          class="coder-content"
          v-if="
            !isFinished ||
            mode == 'history' ||
            (progress == 100 && auditResults.length <= 0)
          "
        >
          <!-- 新增和编辑 -->
          <Coder
            ref="xmlCoder"
            v-model="xmlContent"
            class="quick-aduit-coder"
            v-bind="xmlCoderParams"
            type="xml"
            theme="default"
            v-if="!isFinished"
          ></Coder>
          <!-- 查看 -->
          <div
            :class="['audit-result', isAuditSwap && 'swap']"
            v-if="
              mode == 'history' || (progress == 100 && auditResults.length <= 0)
            "
          >
            <custom-icon
              type="swap"
              class="swap-icon"
              @click="onAuditSwap"
            ></custom-icon>
            <Prettier
              :value="xmlContent"
              :class="['audit-result-xml', !sqlText && 'no-sql']"
              type="xml"
            />
            <Prettier
              :value="sqlText"
              class="audit-result-sql"
              type="sql"
              v-if="sqlText"
            />
          </div>
        </div>
        <div v-else class="xml-result">
          <custom-icon
            type="swap"
            class="swap-icon"
            @click="onSwap"
          ></custom-icon>
          <div
            v-for="(item, index) in auditResults"
            :key="index"
            :class="isSwap && 'swap'"
          >
            <a-collapse
              class="xml-content"
              :activeKey="xmlActiveKey"
              :bordered="false"
              :accordion="true"
            >
              <a-collapse-panel :key="item.content">
                <div slot="header" class="header">
                  {{ item.content }}
                </div>
                <Prettier :value="item.content" type="xml" />
              </a-collapse-panel>
            </a-collapse>
            <a-collapse
              class="sql-content"
              :activeKey="xmlSqlActiveKey"
              :bordered="false"
              :accordion="true"
              @change="xmlChange"
            >
              <a-collapse-panel
                :key="JSON.stringify(itm)"
                v-for="itm in item.sql"
              >
                <div slot="header" class="header">
                  <span :class="[itm.risk]">{{ riskText[itm.risk] }}</span>
                  {{ itm.sql_text }}
                </div>
                <Prettier :value="itm.sql_text" type="sql" />
              </a-collapse-panel>
            </a-collapse>
          </div>
        </div>
      </a-tab-pane>
      <div slot="tabBarExtraContent">
        <a-progress
          :percent="progress"
          :strokeWidth="8"
          status="active"
          strokeColor="#52C41A"
          v-if="progress !== 0"
        />
        <a @click="onFormat" :disabled="isFinished">
          <custom-icon type="lu-icon-format" /> 格式化
        </a>
      </div>
    </a-tabs>
  </div>
</template>
<script>
import Coder from '@/components/Coder';
import Prettier from '@/components/Prettier';
export default {
  components: { Coder, Prettier },
  props: {
    isFinished: Boolean,
    coderData: {
      type: Object,
      default: () => {}
    },
    auditResults: {
      type: Array,
      default: () => []
    },
    progress: Number,
    mode: String
  },
  data() {
    return {
      list: [],
      activeKey: 'sql',
      xmlContent: '',
      xmlCoderParams: {
        type: 'xml',
        options: {
          gutters: [
            'CodeMirror-linenumbers',
            'CodeMirror-foldgutter',
            'CodeMirror-errTips'
          ],
          placeholder:
            '支持mybait和ibait语法，mybait文本中需要包含<mapper></mapper>标签, ibait文本中需要包含<sqlMap></sqlMap>标签'
        },
        needFormat: true
      },
      sqlContent: '',
      sqlCoderParams: {
        type: 'sql',
        options: {
          gutters: [
            'CodeMirror-linenumbers',
            'CodeMirror-foldgutter',
            'CodeMirror-errTips'
          ]
        },
        placeholder: 'SQL语句需要以分号;结束',
        needFormat: true
        // formatOptions: {
        //   language: 'plsql'
        // }
      },
      riskText: {
        high: '高',
        low: '低',
        no_risk: '无',
        error: '异'
      },
      sqlText: '',
      xmlSqlActiveKey: null,
      xmlActiveKey: null,
      sqlActiveKey: null,
      isSwap: false,
      isAuditSwap: false
    };
  },
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    // sql xml 切换
    tabsChange(activeKey) {
      this.activeKey = activeKey;
    },
    getData() {
      const _key = `${this.activeKey}Content`;
      return {
        content: this[_key],
        content_type: this.activeKey
      };
    },
    sqlChange(data) {
      if (data) {
        const res = JSON.parse(data);
        this.sqlContent = res.content;
        this.$emit('updateData', res);
      }
    },
    xmlChange(data) {
      if (data) {
        const res = JSON.parse(data);
        this.xmlContent = res.content;
        this.$emit('updateData', res);
      }
    },
    onFormat() {
      const key = `${this.activeKey}Coder`;
      this.$refs[key].init();
    },
    onSwap() {
      this.isSwap = !this.isSwap;
    },
    onAuditSwap() {
      this.isAuditSwap = !this.isAuditSwap;
    }
  },
  watch: {
    coderData: {
      handler(newVal) {
        if (newVal) {
          this.tabsChange(newVal.content_type || this.activeKey);
          const _key = `${this.activeKey}Content`;
          this[_key] = newVal.content;
          this.sqlText = newVal.sql_text || '';
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.quick-audit-coder-comp {
  height: 100%;
  /deep/.xml-or-sql {
    height: 100%;
    display: flex;
    flex-direction: column;
    &.ant-tabs {
      .ant-tabs-bar {
        padding: 10px 16px 0 0;
        margin: 0;
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
        align-items: flex-start;
        background: #e6f4ff;
        .ant-tabs-nav-container {
          height: 28px;
          .ant-tabs-nav {
            > div {
              .ant-tabs-tab {
                padding: 0 8px;
                margin-right: 4px;
                border-radius: 0;
                height: 22px;
                line-height: 22px;
                background: transparent;
                border: none;
                font-size: 14px;
                color: #1f1f1f;
                font-weight: 400;
                &:first-child {
                  margin-left: 16px;
                }
                &.ant-tabs-tab-active {
                  border: 1px solid #d9d9d9;
                  background: #fff;
                  border-radius: 4px;
                }
              }
            }
          }
        }
        .ant-tabs-extra-content {
          height: 22px;
          line-height: 22px;
          text-align: right;
          padding-right: 0;
          font-family: PingFangSC-Regular;
          color: #1f1f1f;
          > div {
            display: flex;
            align-items: center;
            .ant-progress {
              width: 120px;
              margin-right: 32px;
              top: -2px;
              .ant-progress-outer {
                .ant-progress-inner {
                  border-radius: 0;
                  .ant-progress-bg {
                    border-radius: 0 !important;
                  }
                }
              }
              &::after {
                content: '';
                position: absolute;
                left: 54px;
                top: 7px;
                z-index: 10;
                width: 8px;
                height: 10px;
                background: #e6f4ff;
              }
              &::before {
                content: '';
                position: absolute;
                left: 23px;
                top: 7px;
                z-index: 10;
                width: 8px;
                height: 10px;
                background: #e6f4ff;
              }
            }
            > a {
              color: #1f1f1f;
              &:hover {
                color: #008adc;
              }
            }
            a[disabled] {
              color: rgba(0, 0, 0, 0.25);
            }
          }
        }
      }
      .ant-tabs-content {
        height: calc(100% - 40px);
        background: #e6f4ff;
        .ant-tabs-tabpane-active {
          height: 100%;
          .coder-content {
            height: 100%;
            .custom-coder {
              .CodeMirror {
                border-radius: 0;
                background: #e6f4ff;
                .CodeMirror-scroll {
                  padding-bottom: 0;
                }
              }
              .custom-coder-tools {
                background: #fff;
              }
            }
            .audit-result {
              display: flex;
              height: 100%;
              position: relative;
              flex-direction: row;
              &.swap {
                flex-direction: row-reverse;
                .audit-result-xml {
                  width: 32%;
                  border-left: 1px solid #d9d9d9;
                }
                .audit-result-sql {
                  width: 68%;
                }
              }
              .swap-icon {
                display: none;
                position: absolute;
                top: 36%;
                left: calc(68% - 10px);
                font-size: 18px;
                z-index: 10;
                cursor: pointer;
                &:hover {
                  color: #008adc;
                }
              }
              .audit-result-xml {
                height: 100%;
                width: 68%;
                border-right: 1px solid #d9d9d9;
                &.no-sql {
                  width: 100%;
                  border-right: none;
                }
              }
              .audit-result-sql {
                width: 32%;
                height: 100%;
              }
            }
            .custom-prettier {
              height: 100%;
              background: transparent;
              pre {
                height: 80%;
              }
              .fullscreen-tools {
                right: 8px;
              }
            }
          }
          .sql-result,
          .xml-result {
            height: 81%;
            overflow-y: auto;
            .ant-collapse {
              background: transparent;
              .ant-collapse-item {
                margin: 8px;
                border-bottom: none;
                &.ant-collapse-item-active {
                  .ant-collapse-content-box {
                    padding: 0 0 0 16px;
                  }
                }
              }
            }
          }
          .sql-result {
            .ant-collapse {
              .header {
                width: 90%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #1f1f1f;
                padding: 8px;
                > span {
                  font-size: 12px;
                  padding: 2px 4px;
                  margin-right: 4px;
                }
                .high {
                  color: #f5222d;
                  border: 1px solid #ffa39e;
                  background: #fff1f0;
                }
                .low {
                  color: #faad14;
                  border: 1px solid #ffe58f;
                  background: #fffbe6;
                }
                .no_risk {
                  color: #52c41a;
                  border: 1px solid #b7eb8f;
                  background: #f6ffed;
                }
                .error {
                  color: rgba(0, 0, 0, 0.85);
                  background: #fafafa;
                  border: 1px solid #d9d9d9;
                }
              }
              .custom-prettier {
                background: rgba(0, 0, 0, 0.05);
                padding-bottom: 24px;
              }
              .custom-coder {
                .CodeMirror {
                  background: rgba(0, 0, 0, 0.05);
                }
                .custom-coder-tools {
                  background: #fff !important;
                }
              }
            }
          }
          .xml-result {
            display: flex;
            flex-direction: column;
            position: relative;
            .swap-icon {
              position: absolute;
              top: 50%;
              left: calc(68% - 10px);
              font-size: 18px;
              cursor: pointer;
              &:hover {
                color: #008adc;
              }
            }
            > div {
              display: flex;
              flex-direction: row;
              margin-bottom: 16px;
              &.swap {
                flex-direction: row-reverse;
                .ant-collapse {
                  &.xml-content {
                    width: 32%;
                  }
                  &.sql-content {
                    width: 68%;
                    border-right: 1px solid #d9d9d9;
                  }
                }
              }
              .ant-collapse {
                &.xml-content {
                  width: 68%;
                  .header {
                    width: 90%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color: #1f1f1f;
                  }
                }
                &.sql-content {
                  width: 32%;
                  border-left: 1px solid #d9d9d9;
                  .header {
                    width: 90%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color: #1f1f1f;
                    > span {
                      font-size: 12px;
                      padding: 2px 4px;
                      margin-right: 4px;
                    }
                    .high {
                      color: #f5222d;
                      border: 1px solid #ffa39e;
                      background: #fff1f0;
                    }
                    .low {
                      color: #faad14;
                      border: 1px solid #ffe58f;
                      background: #fffbe6;
                    }
                    .no_risk {
                      color: #52c41a;
                      border: 1px solid #b7eb8f;
                      background: #f6ffed;
                    }
                    .error {
                      color: rgba(0, 0, 0, 0.85);
                      background: #fafafa;
                      border: 1px solid #d9d9d9;
                    }
                  }
                }
                .custom-prettier {
                  // background: transparent;
                  background: rgba(0, 0, 0, 0.05);
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.quick-aduit-coder {
  height: calc(100% - 48px);
  border: none !important;
  .CodeMirror {
    border-radius: 0;
    height: 100% !important;
    background: #e6f4ff;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier,
      monospace;
    .CodeMirror-scroll {
      // position: absolute;
      // left: 0;
      // top: 0;
      // width: 100%;
      // overflow: auto !important;
      .CodeMirror-gutters {
        background: #e6f4ff !important;
        border: none;
      }
      .CodeMirror-linenumber {
        color: #595959 !important;
      }
    }
  }
  .custom-coder-tools {
    background: #fff !important;
  }
}
</style>