<template>
  <Form
    class="biz-rule-form"
    ref="form"
    v-bind="formParams"
    :formData="formData"
  >
    <InputModal
      slot="index_code"
      @change="onChange"
      :node="value"
      :value="value.index_code && value.index_code.label"
    />
  </Form>
</template>

<script>
import Select from '@/components/Select';
import Form from '@/components/Form';
import InputModal from '@/components/Biz/InputModal';
import config from './config';

export default {
  components: {
    Form,
    Select,
    InputModal
  },
  props: {
    value: {
      type: Object,
      default: () => {}
    },
    dbType: {
      type: String,
      default: 'ORACLE'
    }
  },
  data() {
    this.config = config(this);
    return {
      InputModal: 'InputModal',
      formData: this.value,
      formParams: {
        fields: this.config.fields(this.dbType)
      },
      visible: false
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    onChange() {
      this.$refs.form.saving({
        index_code: this.value.index_code,
        prev_operator: undefined,
        target_operator: undefined,
        target_value: undefined
      });
      this.refreshData();
      const params = this.getParams();
      this.$set(this.formParams.fields[1].props, 'reqParams', {
        value_list: params
      });
      this.$set(
        this.formParams.fields[1].props,
        'url',
        `/sqlreview/project/rule-new/get_target_list?db_type=${this.dbType}`
      );
    },
    refreshData() {
      // 父节点值改变后，所有子节点值要清空
      const loop = (item, level) => {
        item.level = level;
        item.id = _.uniqueId('init_');
        if (!item.children || item.children.length <= 0) {
          item.isLeaf = true;
        }
        if (item.children && item.children.length > 0) {
          item.children.forEach((child, index) => {
            if (
              child.role_type == 'item' &&
              child.children &&
              child.children.length > 0
            ) {
              child.hasChildren = true;
            }
            child._parent = item;
            child.item_order = index;
            child.source_code = '';
            child.index_code = { key: '', label: '' };
            child.prev_operator = { key: '', label: '' };
            child.target_operator = { key: '', label: '' };
            child.target_value = '';
            loop(child, ++level - index);
          });
        }
      };
      loop(this.value, this.value.level);
    },
    getParams() {
      let reqArr = [];
      const loop = item => {
        if (item.role_type == 'item') {
          const indexCode = item.index_code;
          const prevOperator = item.prev_operator;
          const targetOperator = item.target_operator;
          const targetValue = item.target_value;
          if (item.level >= 2) {
            reqArr.unshift([
              indexCode ? indexCode.label : '',
              prevOperator ? prevOperator.key : '',
              targetOperator ? targetOperator.key : '',
              targetValue || ''
            ]);
          } else {
            reqArr.unshift([
              indexCode ? indexCode.key : '',
              indexCode ? indexCode.label : '',
              prevOperator ? prevOperator.key : '',
              targetOperator ? targetOperator.key : '',
              targetValue || ''
            ]);
          }
        }
        if (item._parent) {
          loop(item._parent);
        }
      };
      loop(this.value);
      return reqArr;
    }
  },
  watch: {
    value(newVal) {
      this.formData = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
// .biz-rule-range {
//   display: flex;
//   align-items: center;
//   padding: 6px 0 6px 4px;
//   > .ant-input-number {
//     border-radius: 0 4px 4px 0;
//   }
// }
.biz-rule-form {
  display: flex;
  /deep/.ant-form-item {
    width: 20%;
    margin-bottom: 8px;
    .ant-form-item-control-wrapper {
      .ant-form-item-children {
        .ant-input {
          border: 1px solid #ebebec;
        }
        .ant-select {
          .ant-select-selection {
            border: 1px solid #ebebec;
            border-right: none;
            border-radius: 0;
            margin-right: 2px;
            .ant-select-selection__rendered {
              line-height: 32px;
            }
          }
        }
      }
    }
    &:first-child {
      width: 30%;
      .ant-form-item-control-wrapper {
        .ant-form-item-children {
          .input-modal {
            width: 100%;
            .ant-input {
              border: 1px solid #ebebec;
              border-right: none;
              border-radius: 4px 0 0 4px;
            }
          }
        }
      }
    }
    &:last-child {
      width: 30%;
      .ant-form-item-control-wrapper {
        .ant-form-item-children {
          .ant-input {
            border-radius: 0 4px 0 4px;
          }
        }
      }
    }
  }
}
</style>
