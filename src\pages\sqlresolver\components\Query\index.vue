<template>
  <div class="psc-right-query-content">
    <a-tabs
      class="psc-right-query-instance"
      v-model="activeKey"
      type="editable-card"
      @change="onChange"
      @edit="onEdit"
    >
      <!-- tabBarExtraContent -->
      <span class="psc-right-query-add" slot="tabBarExtraContent" @click.stop="add">
        <custom-icon type="lu-icon-plusnew" />
      </span>
      <!-- <a-tab-pane v-for="pane in panes" :key="pane.key" :closable="pane.closable"> -->
      <a-tab-pane v-for="(pane, index) in panes" :key="pane.key">
        <!-- tabSlot -->
        <div
          class="psc-right-query-instance-tab"
          slot="tab"
          @contextmenu.prevent.stop="(e) => onContextmenu(e, pane, index)"
        >
          <div v-if="pane.useSelf">
            <VNode :node="pane.sheet_name" />
          </div>
          <div v-html="pane.title" v-else></div>
          <custom-icon
            class="psc-right-query-instance-tab-close"
            type="close"
            @click.stop="remove(pane.key)"
          ></custom-icon>
        </div>
        <!-- <SplitPanel split-to="rows" primary="second" :min-size="100" :max-size="400" :size="200"> -->
        <SplitPanel
          :class="pane.needResult === false && 'hide-second'"
          split-to="rows"
          primary="second"
          units="percents"
          :min-size="pane.needResult === false ? 0 : 20"
          :size="pane.needResult === false ? 0 : 30"
        >
          <div class="psc-right-query-content-wrapper" slot="first">
            <template v-if="pane.useSelf">
              <!-- <NodeInfo :node="pane.node"></NodeInfo> -->
              <component :is="pane.compName" v-bind="pane.compProps || {}"></component>
            </template>
            <template v-else>
              <Coder
                :ref="`Coder_${pane.key}`"
                :database="database"
                :queryActiveTab="activeKey"
                :queryTab="pane.key"
                :queryItem="pane"
                :defaultSql="pane.key == activeKey ? defaultSql : ''"
                :schemaOptions="schemaOptions"
                @save-sheet="onSaveSheet"
                v-if="database"
              />
              <div class="psc-rq-empty" v-else>选择一个数据库连接或者选择一个查询开始运行</div>
            </template>
          </div>
          <div class="psc-right-query-detail-wrapper" slot="second">
            <QueryDetail
              :database="database"
              :queryActiveTab="activeKey"
              :queryTab="pane.key"
              v-if="pane.needResult !== false"
            />
          </div>
        </SplitPanel>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import Coder from './Coder';
import SplitPanel from '@/components/SplitPanel';
import QueryDetail from '../QueryDetail';
import NodeInfo from './NodeInfo';
import TableRows from './TableRows';

export default {
  components: { Coder, SplitPanel, QueryDetail, NodeInfo, TableRows },
  inject: ['instanceItem'],
  data() {
    // const panes = [
    //   { title: 'Tab 1', content: 'Content of Tab 1', key: '1' },
    //   { title: 'Tab 2', content: 'Content of Tab 2', key: '2' }
    // ];
    return {
      activeKey: 'query_0',
      database: null,
      queryCount: 0,
      panes: [
        {
          title: `<span style="color:red;margin-right:2px;">*</span>
              <span>新建查询1</span>`,
          key: 'query_0'
        }
      ],
      newTabIndex: 0,
      defaultSql: '',
      schemaOptions: []
    };
  },
  computed: {},
  mounted() {
    // schema变化
    this.$bus.$on('sqlresolver-database-change', data => {
      const { instanceId, node } = data;
      // console.log(node, 'database-change');
      if (instanceId == _.get(this.instanceItem, 'key')) {
        this.database = node;
      }
    });
    // schema加载完毕
    this.$bus.$on('sqlresolver-database-loaded', (params = {}) => {
      const { instanceId, data = [] } = params;
      if (instanceId == _.get(this.instanceItem, 'key')) {
        this.schemaOptions = data.map(item => {
          return {
            ...item,
            value: item.id,
            label: item.name
          };
        });
      }
    });
    // 新增query
    this.$bus.$on('sqlresolver-add-query', data => {
      // console.log(data, 'add-query');
      const { database = {}, defaultSql, sheetName = '', instanceId } =
        data || {};
      if (instanceId == _.get(this.instanceItem, 'key')) {
        this.database = database;
        this.add(defaultSql, { sheet_name: sheetName });
      }
    });
    // 打开query
    this.$bus.$on('sqlresolver-open-query', (params = {}) => {
      const { data, instanceId, type } = params;
      // console.log(data, 'open-query');
      if (instanceId != _.get(this.instanceItem, 'key')) {
        return;
      }
      const pane = this.panes.find(item => item.sheet_id == data.sheet_id);
      // 已经有tab，直接高亮
      if (type === 'query' && pane) {
        this.activeKey = pane.key;
        return;
      }
      let _database = data.schema_info || {};
      _database = {
        ..._database,
        name: _database.schema_name || _database.name
      };
      if (!this.database) {
        // 没选择database，直接设置为当前
        this.database = _database;
      } else {
        // 否则当前tab使用后，重置
        this._currDb = this.database;
        this.database = _database;
        this.$nextTick(() => {
          this.database = this._currDb;
        });
      }
      this.add(data.sql_text, data);
    });
    // 查看节点信息
    this.$bus.$on('sqlresolver-view-node', data => {
      // console.log(data, 'add-query');
      const { database = {}, item = {}, sheetName = '', instanceId } =
        data || {};
      if (instanceId == _.get(this.instanceItem, 'key')) {
        this.database = database;
        const pane = this.panes.find(pane => {
          const paneNodeInfo = _.get(pane, 'node') || {};
          return (
            paneNodeInfo.element_type === item.element_type &&
            paneNodeInfo.name == item.name &&
            paneNodeInfo.compName === 'NodeInfo'
          );
        });
        if (pane) {
          this.activeKey = pane.key;
          return;
        }
        this.add('', {
          sheet_name: sheetName,
          useSelf: true,
          compName: 'NodeInfo',
          compProps: {
            node: item
          },
          needResult: false,
          node: item
        });
      }
    });
    // 查看表前n-rows
    this.$bus.$on('sqlresolver-view-table-first-rows', data => {
      // console.log(data, 'table-first-rows');
      const { database = {}, item = {}, sheetName = '', instanceId } =
        data || {};
      if (instanceId == _.get(this.instanceItem, 'key')) {
        this.database = database;
        const pane = this.panes.find(pane => {
          const paneNodeInfo = _.get(pane, 'node') || {};
          return (
            paneNodeInfo.element_type === item.element_type &&
            paneNodeInfo.name == item.name &&
            paneNodeInfo.compName === 'TableRows'
          );
        });
        if (pane) {
          this.activeKey = pane.key;
          return;
        }
        this.add('', {
          sheet_name: sheetName,
          useSelf: true,
          compName: 'TableRows',
          compProps: {
            node: item
          },
          needResult: false,
          node: item
        });
      }
    });
    // 标记pos
    this.$bus.$on('sqlresolver-database-mark-pos', data => {
      const { instanceId, queryTab, markPos = [] } = data;
      if (instanceId != _.get(this.instanceItem, 'key')) {
        return;
      }
      if (queryTab !== this.activeKey) {
        return;
      }
      const Coder = _.get(this.$refs, `Coder_${this.activeKey}.0`);
      if (Coder) {
        Coder.markPos(markPos);
      }
    });
  },
  methods: {
    callback(key) {
      console.log(key);
    },
    onEdit(targetKey, action) {
      this[action](targetKey);
    },
    onChange() {},
    add(defaultSql, history = {}) {
      if (!this.database) {
        this.$message.warning('请选择一个数据库连接或者选择一个查询');
        return;
      }
      const panes = this.panes;
      this.queryCount = this.queryCount + 1;
      const activeKey = `query_${this.queryCount}`;
      panes.push(
        Object.assign(
          {
            title:
              history.sheet_name ||
              `<span style="color:red;margin-right:2px;">*</span>
              <span>新建查询${this.queryCount + 1}</span>`,
            key: activeKey
          },
          history
        )
      );
      if (defaultSql && _.isString(defaultSql)) {
        this.defaultSql = defaultSql;
      }
      this.panes = panes;
      this.activeKey = activeKey;
      // 默认sql赋值完毕后，清空
      this.$nextTick(() => {
        this.defaultSql = '';
      });
    },
    remove(targetKey) {
      let activeKey = this.activeKey;
      let lastIndex;
      this.panes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1;
        }
      });
      const panes = this.panes.filter(pane => pane.key !== targetKey);
      if (panes.length && activeKey === targetKey) {
        if (lastIndex >= 0) {
          activeKey = panes[lastIndex].key;
        } else {
          activeKey = panes[0].key;
        }
      }
      this.panes = panes;
      this.activeKey = activeKey;
    },
    removeOthers(targetKey) {
      this.panes = this.panes.filter(item => item.key == targetKey);
      this.activeKey = targetKey;
    },
    removeRight(targetKey, index) {
      this.panes = this.panes.slice(0, index + 1);
      this.activeKey = targetKey;
    },
    onSaveSheet(data) {
      const pane = this.panes.find(item => item.key === this.activeKey);
      pane.title = data.sheet_name;
      pane.sheet_name = data.sheet_name;
      pane.sheet_id = data.sheet_id;

      // console.log(pane, 'sssssssss')

      this.panes = [...this.panes];
    },
    onContextmenu(event, pane, index) {
      console.log(event, pane, index);
      const items = [
        {
          label: '关闭',
          key: 'close',
          onClick: () => {
            this.remove(pane.key);
          }
        },
        {
          label: '关闭其他',
          key: 'closeOthers',
          onClick: () => {
            this.removeOthers(pane.key);
          }
        },
        {
          label: '关闭右侧',
          key: 'closeRight',
          onClick: () => {
            this.removeRight(pane.key, index);
          }
        },
        {
          label: '关闭全部',
          key: 'closeAll',
          onClick: () => {
            this.panes = [];
            this.activeKey = '';
          }
        }
      ];
      if (items.length <= 0) return;

      this.$contextmenu({
        items,
        event,
        customClass: 'custom-class',
        zIndex: 1051
        // minWidth: 180
      });
      return false;
    }
  }
};
</script>

<style lang="less" scoped>
.psc-right-query-content {
  display: flex;
  flex-grow: 1;
  height: 100%;
  /deep/ .psc-right-query-instance {
    width: 100%;
    display: flex;
    flex-grow: 1;
    flex-direction: column;

    .psc-right-query-instance-tab {
      padding-right: 24px;
      position: relative;

      .psc-right-query-instance-tab-close {
        position: absolute;
        right: 0;
        top: 0;
        color: @font-color-weak;
        margin: 0;
        line-height: 40px;
        cursor: pointer;
        font-size: 12px;

        &:hover {
          color: @font-color-strong;
        }
      }
    }
    // tab-bar
    > .ant-tabs-bar {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-end;
      align-items: flex-end;
      margin: 0;

      .ant-tabs-tab {
        border: 1px solid #f0f0f0;
        border-top: 0;
        border-radius: 0;
        margin: 0;
        border-right: 0;
        background: #ffffff;
        font-size: 13px;
        color: @font-color-weak;

        &:hover {
          color: @primary-color;
        }

        &:first-child {
          border-left: 0;
        }

        &.ant-tabs-tab-active {
          background: #ffffff;
          color: @font-color-strong;
          font-size: 13px;
          .font-bold();
          // border: 1px solid #f0f0f0;
        }
      }

      .ant-tabs-extra-content {
        height: 40px;
        margin-bottom: -1px;
        position: relative;

        .ant-tabs-new-tab {
          width: 40px;
          height: 40px;
          background: #fafafa;
          border: 1px solid #f0f0f0;
          border-top: 0;
        }
        .psc-right-query-add {
          width: 40px;
          height: 40px;
          background: #ffffff;
          border: 1px solid #f0f0f0;
          border-top: 0;
          position: absolute;
          right: 0;
          top: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          &:hover {
            color: @primary-color;
          }
        }
      }

      .ant-tabs-tab-prev {
        border-right: 1px solid @border-color;
      }
      .ant-tabs-tab-next {
        border-left: 1px solid @border-color;
      }
    }
    // tab-content
    > .ant-tabs-content {
      display: flex;
      flex-grow: 1;
      > .ant-tabs-tabpane {
        position: relative;
        &.ant-tabs-tabpane-active {
          flex-grow: 1;
        }
        &.ant-tabs-tabpane-inactive {
          // width: 0;
          display: none;
        }

        .psc-right-query-content-wrapper {
          height: 100%;
        }
        .psc-right-query-detail-wrapper {
          height: 100%;
        }
      }
    }

    .hide-second {
      .Resizer {
        pointer-events: none;
      }
    }
  }
  .psc-rq-empty {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9fafa;
  }
}
</style>
