<template>
  <a-card type="small" class="a-card">
    <div slot="title">
      <a-icon type="robot" />
      <span style="margin-left: 4px;">AI判定结果</span>
      <a-tag
        :color="
              resData.status === 1 || resData.status === 2
                ? '#52c41a'
                : resData.status === -1 || resData.status === 9
                ? '#ff4d4f'
                : '#b0aeae'
            "
      >{{ resData.status | aiStatus }}</a-tag>
    </div>
    <div v-if="resData.ai_comment" class="ai-comment-part">
      <div v-for="(item, index) in resData.ai_comment" :key="index" class="ai-comment-div">
        <span
          :class="{
                level0: item.level === 0,
                level1: item.level === 1,
                level2: item.level === 2,
                level3: item.level === 3,
                level9: item.level === 9
              }"
        >【{{ item.level | levelStatus }}】</span>
        <span>{{ item.ai_comment }}</span>
      </div>
    </div>
    <div class="ai-comment-part" v-else>
      <span>暂无数据</span>
    </div>
  </a-card>
</template>

<script>
export default {
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    statusType() {
      if (this.dataInfo.ai_status === 1 || this.dataInfo.ai_status === 2) {
        return 'check-circle';
      } else if (
        this.dataInfo.ai_status === -1 ||
        this.dataInfo.ai_status === 9
      ) {
        return 'exclamation-circle';
      } else {
        return 'exclamation-circle';
      }
    }
  },
  data() {
    return {
      aiStatus: -1,
      statusColor: {
        1: '#52c41a',
        2: '#52c41a',
        '-1': '#ff4d4f',
        9: '#ff4d4f',
        0: '#b0aeae'
      },
      resData: {}
    };
  },
  methods: {},
  filters: {
    levelStatus(value) {
      let obj = {
        0: '未知',
        1: '高风险',
        2: '中风险',
        3: '低风险',
        9: '无风险'
      };
      return obj[value];
    },
    aiStatus(value) {
      let obj = {
        0: '未知',
        1: '通过',
        2: '白名单通过',
        9: '错误',
        '-1': '不通过'
      };
      return obj[value];
    }
  },
  watch: {
    dataInfo(newVal) {
      this.resData = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
// .small-card {
//   margin-bottom: 24px;
//   background: #ffffff;
//   box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
//   border-radius: 8px;
//   .title {
//     display: flex;
//     align-items: center;
//     .ai-result {
//       margin: 0 16px 0 12px;
//       font-weight: 600;
//       font-size: 15px;
//     }
//     .is-pass {
//       display: flex;
//       align-items: center;
//       .is-pass-icon {
//         font-size: 36px;
//         margin-right: 8px;
//       }
//       > span {
//         font-weight: 600;
//         font-size: 15px;
//       }
//     }
//   }
// }

.a-card {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
  border-radius: 8px;
  margin-bottom: 24px;
}
.ai-comment-part {
  padding: 16px;
  background-color: #edf5ff;
  border-radius: 5px;
  .ai-comment-div {
    padding: 0;
    margin: 2px 0;
    background-color: transparent;
    color: #0f78fb;
    line-height: 25px;
    .level0 {
      color: #b0aeae;
    }
    .level1 {
      color: #ff4d4f;
    }
    .level2 {
      color: #ff9358;
    }
    .level3 {
      color: #1edfa9;
    }
    .level9 {
      color: #52c41a;
    }
  }
}
</style>
