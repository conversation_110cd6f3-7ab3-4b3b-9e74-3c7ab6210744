<template>
  <div class="config-rules-edit">
    <div class="frame-button-wrapper">
      <a-button @click="onCancel" class="highlight">取消</a-button>
      <a-button type="primary" @click="onSave">保存</a-button>
    </div>

    <a-spin tip="加载中" :spinning="loading">
      <ContentDML
        :type="type"
        ref="contentDML"
        v-if="rule_category === 'DML'"
        :dataSource="dataSource"
      />
      <ContentDDL :type="type" ref="contentDDL" v-else :dataSource="dataSource" />
    </a-spin>
  </div>
</template>

<script>
import {
  ruleEdit,
  newRuleDetail,
  newRuleDMLDetail,
  newRuleEdit
} from '@/api/config/rule';
import ContentDDL from '../components/Content';
import ContentDML from '../components/ContentDML/new';

export default {
  components: { ContentDML, ContentDDL },
  props: {},
  data() {
    return {
      rule_category: this.$route.query.rule_category,
      showDDL: '',
      loading: false,
      dbType: '',
      type: 'edit',
      dataSource: {},
      oldRuleSetUids: []
    };
  },
  mounted() {},
  created() {
    // 请求
    this.loading = true;
    if (this.rule_category === 'DML') {
      newRuleDMLDetail({
        // dml
        id: this.$route.query.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.loading = false;
            let dataSource = _.get(res, 'data.data') || {};
            this.dataSource = dataSource;
            this.dbType =
              dataSource.db_type ||
              window.localStorage.getItem('db_type') ||
              '';
            // 滚动到顶部 (markdown组件设置值会导致scrollIntoView)
            this.$nextTick(() => {
              window.LAYOUT_ROOT.scrollTo(0);
            });
          } else {
            this.$message.error(_.get(res, 'data.message'));
            this.loading = false;
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$message.error('请求失败');
        });
    } else {
      newRuleDetail({
        // ddl
        rule_id: this.$route.query.id,
        rule_category: this.rule_category,
        rule_set_id: this.$route.query.rule_set_id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.loading = false;
            let dataSource = _.get(res, 'data.data') || {};
            this.dataSource = dataSource;
            this.oldRuleSetUids = dataSource.rule_set_uids || [];
          } else {
            this.$message.error(_.get(res, 'data.message'));
            this.loading = false;
          }
          // 滚动到顶部 (markdown组件设置值会导致scrollIntoView)
          this.$nextTick(() => {
            window.LAYOUT_ROOT.scrollTo(0);
          });
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$message.error('请求失败');
        });
    }
  },
  methods: {
    onCancel() {
      this.$router.push({ name: 'rules-config' });
    },
    onSave() {
      let content = null;
      if (this.rule_category === 'DML') {
        content = this.$refs.contentDML;
        content.getData().then(data => {
          // 请求
          this.$showLoading();
          ruleEdit({
            ...data,
            id: this.$route.query.id,
            db_type: data.ob_mode ? data.ob_mode : this.dbType
          })
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.$hideLoading({ tips: _.get(res, 'data.message') });
                // this.$router.go(-1);
                this.$router.push({ name: 'rules-config' });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        });
      } else {
        content = this.$refs.contentDDL;
        content.getData().then(data => {
          // 请求
          this.$showLoading();
          newRuleEdit({
            ...data,
            id: this.$route.query.id,
            old_rule_set_uids: this.oldRuleSetUids
          })
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.$hideLoading({ tips: _.get(res, 'data.message') });
                this.$router.push({ name: 'rules-config' });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
</style>
