<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 效率云/CQ批量上传弹窗 -->
  <a-modal
    v-model="visible"
    title="批量上传"
    okText="保存"
    width="580px"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="params" :formData="formData" class="add-form">
        <div slot="upload-wrap" class="upload-wrap">
          <a-upload-dragger
            name="file"
            :before-upload="beforeUpload"
            :remove="() => (this.fileList = [])"
            :file-list="fileList"
          >
            <p class="ant-upload-drag-icon">
              <a-icon type="plus" />
            </p>
            <p class="ant-upload-text">
              将文件拖拽到此处，或
              <span class="tips">点击上传</span>
            </p>
            <p class="ant-upload-text">支持.xlsx文件格式，单个文件大小小于2M</p>
          </a-upload-dragger>
          <a @click="downloadUploadTemplate">效率云/CQ单号上传模板.xlsx</a>
        </div>
      </Form>
    </a-spin>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import { downloadReviewTemplate } from '@/api/extraReview/cqxly';
import common from '@/utils/common';
const formParams = ctx => {
  return {
    layout: 'vertical',
    labelCol: { span: 24 },
    wrapperCol: { span: 24 },
    fields: [
      {
        type: '',
        label: '效率云/CQ单号文件上传：',
        key: 'review_type',
        slots: [{ key: 'upload-wrap' }],
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
    ]
  };
};

export default {
  components: { Form },
  props: {},
  data() {
    return {
      spinning: false,
      visible: false,
      fileList: [],
      formData: {},
      submitData: {},
      params: formParams(this)
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(type = '') {
      this.type = type;
      this.data = {};
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      this.formData = {};
      this.submitData = {};
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      if (this.fileList.length === 0) {
        this.$message.warning('请上传文件');
        return;
      }
      this.submitData = new FormData();
      this.submitData.append('file', this.fileList[0]);
      this.$emit('save', this.submitData);
    },
    // 上传之前
    beforeUpload(file) {
      const maxSize = 2 * 1024 * 1024; // byte
      if (file.size > maxSize) {
        this.$message.error('文件大小错误，文件大小不超过2MB');
        return;
      }
      if (!/\.(xlsx)$/.test(file.name)) {
        this.$message.error('文件格式错误，文件类型支持.xlsx');
        return;
      }
      this.$set(this.fileList, 0, file);
      return false;
    },
    // 下载模板
    downloadUploadTemplate() {
      this.$showLoading({
        tips: `下载中...`
      });
      downloadReviewTemplate()
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .add-form {
  .ant-row .ant-form-item-control-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        width: 150px;
      }
    }
  }
}
.upload-wrap {
  /deep/.ant-upload-drag {
    height: 300px;
    background: #f4f5f7;
    border: 1px dotted #a1a1aa;
    // margin-left: 120px;
    .ant-upload-text {
      font-size: 14px;
      .tips {
        color: #1890ff;
      }
    }
  }
  /deep/ .ant-upload-list {
    padding: 0 12px;
    margin-top: 16px;
    background: rgba(91, 147, 255, 0.1);
    > div {
      > span {
        .ant-upload-list-item {
          margin: 8px 0;
          .anticon {
            font-size: 16px;
          }
          .ant-upload-list-item-name {
            margin-top: 2px;
          }
          .ant-upload-list-item-card-actions {
            opacity: 1;
            margin-top: 2px;
            .anticon {
              font-size: 16px;
              color: #008adc;
            }
          }
        }
      }
    }
    .ant-upload-list-item:hover .ant-upload-list-item-info {
      background-color: rgba(91, 147, 255, 0);
    }
  }
}
</style>
