<template>
  <CustomIframe :url="url" />
</template>

<script>
import CustomIframe from '@/components/Iframe';
export default {
  components: { CustomIframe },
  props: {},
  data() {
    return {};
  },
  computed: {
    project() {
      return this.$store.state.project;
    },
    account() {
      return this.$store.state.account;
    },
    url() {
      const { metaUrl } = this.project;
      const { user } = this.account;
      const queryStr = this.$route.fullPath.split('?')[1] || '';
      return `${metaUrl}?umNo=${user.name}&platform=SQLReview&${queryStr}`;
    }
  },
  methods: {}
};
</script>

<style lang="less" scoped></style>
