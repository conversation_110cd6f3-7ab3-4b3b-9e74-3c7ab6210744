import moment from 'moment';
export default function (ctx) {
  const fields = () => {
    return [
      {
        type: 'Input',
        label: '任务名称',
        key: 'task_name',
        props: {},
        listeners: {},
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '指定子系统',
          key: 'project_id',
          props: {
            url: '/sqlreview/project/list-all',
            reqParams: {
              review_type: 'none'
            },
            allowSearch: true,
            backSearch: true,
            limit: 20,
            disabled: ctx.isEdit
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        }
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: '任务来源',
          key: 'source',
          props: {
            options: [
              {
                label: '效率云',
                value: 'XLY'
              },
              {
                label: 'CQ',
                value: 'CQ'
              }
            ],
            disabled: ctx.isEdit
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                source: value,
                state_value: null
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        }
      },
      (formData = {}) => {
        const source = formData.source;
        const filedName = source == 'XLY' ? '效率云单状态' : 'CQ单状态';
        const type = source == 'XLY' ? 'xly_form_status' : 'cq_form_status';
        return {
          type: 'Select',
          label: filedName,
          key: 'state_value',
          props: {
            url: '/sqlreview/review/get_select_enum',
            mode: 'multiple',
            reqParams: {
              enum_name: type
            }
          },
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ]
        };
      },
      (formData = {}) => {
        return {
          type: 'RangePicker',
          label: '定时审核时间范围',
          key: 'time_range',
          props: {
            // showTime: {
            //   format: 'HH:mm:ss'
            // },
            format: 'YYYY-MM-DD HH:mm:ss',
            disabledDate: (current) => {
              return (
                current < moment().startOf('day')
              );
            }
          },
          rules: []
        }
      },
      {
        type: 'Select',
        label: '定时策略',
        key: 'corn',
        props: {},
        hideComponent: true,
        slots: [{ key: 'timeStrategy' }],
        rules: []
      },
      {
        type: 'Switch',
        label: '任务开关',
        key: 'status',
        props: {
          size: 'default',
          'checked-children': '开启',
          'un-checked-children': '关闭'
        },
        width: 50,
        rules: []
      },
      {
        type: 'Textarea',
        label: '邮件通知',
        key: 'email',
        props: {
          row: 6,
          placeholder: '可填写多个邮件联系人，以分号“；”分隔'
        },
        rules: []
      }
    ]
  }
  return {
    fields
  };
}
