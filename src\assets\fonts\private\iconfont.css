@font-face {
  font-family: "luicon-ludb"; /* Project id 4203271 */
  src: url('iconfont.eot?t=1691549455238'); /* IE9 */
  src: url('iconfont.eot?t=1691549455238#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('iconfont.woff2?t=1691549455238') format('woff2'),
       url('iconfont.woff?t=1691549455238') format('woff'),
       url('iconfont.ttf?t=1691549455238') format('truetype'),
       url('iconfont.svg?t=1691549455238#luicon-ludb') format('svg');
}

.luicon-ludb {
  font-family: "luicon-ludb" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.lu-icon-ludb-assess:before {
  content: "\e6b9";
}

.lu-icon-ludb-assess-db:before {
  content: "\e6bb";
}

.lu-icon-ludb-assess-app:before {
  content: "\e6bc";
}

.lu-icon-ludb-flow:before {
  content: "\e6bd";
}

.lu-icon-ludb-datamove2:before {
  content: "\e6c1";
}

.lu-icon-ludb-subscribe:before {
  content: "\e6c3";
}

.lu-icon-ludb-layer:before {
  content: "\e6c5";
}

.lu-icon-ludb-list1:before {
  content: "\e6c6";
}

.lu-icon-ludb-datacheck:before {
  content: "\e6c7";
}

.lu-icon-ludb-integrate:before {
  content: "\e6c8";
}

.lu-icon-ludb-dev:before {
  content: "\e6ca";
}

.lu-icon-ludb-datamove:before {
  content: "\e6cb";
}

.lu-icon-ludb-scene:before {
  content: "\e6ce";
}

.lu-icon-ludb-backup:before {
  content: "\e6cf";
}

.lu-icon-ludb-sync:before {
  content: "\e6d0";
}

.lu-icon-ludb-taskcircle:before {
  content: "\e6d1";
}

.lu-icon-ludb-flow11:before {
  content: "\e6d3";
}

.lu-icon-ludb-assess-app11:before {
  content: "\e6d4";
}

.lu-icon-ludb-meter11:before {
  content: "\e6d5";
}

.lu-icon-ludb-integrate11:before {
  content: "\e6d6";
}

.lu-icon-ludb-assess11:before {
  content: "\e6d7";
}

.lu-icon-ludb-datamove11:before {
  content: "\e6d8";
}

.lu-icon-ludb-dev11:before {
  content: "\e6d9";
}

.lu-icon-ludb-assess-db11:before {
  content: "\e6da";
}

.lu-icon-ludb-subscribe11:before {
  content: "\e6db";
}

.lu-icon-ludb-datacheck11:before {
  content: "\e6dc";
}

