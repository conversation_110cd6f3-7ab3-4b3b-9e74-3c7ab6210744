<template>
  <div>
    <div class="database-audit-real-time">
      <div class="header">
        <div class="header-left">
          <span class="setting-border">集群节点:</span>
          <span class="node-box">
            <a-dropdown
              v-model="nodeVisible"
              class="tab-bar-btn"
              v-if="nodes && nodes.length > 0"
            >
              <a class="ant-dropdown-link" @click="(e) => e.preventDefault()">
                {{ nodeValue }}
                <a-icon type="down" />
              </a>
              <a-menu slot="overlay" @click="handleMenuNode">
                <a-menu-item v-for="item in nodes" :key="item.value">{{
                  item.label
                }}</a-menu-item>
              </a-menu>
            </a-dropdown>
            <span v-else>单节点</span>
          </span>
          <span>{{ time }} 更新</span>
          <span class="reload-box">
            <custom-icon type="sync" />
            <a-dropdown v-model="visible" class="tab-bar-btn">
              <a class="ant-dropdown-link" @click="(e) => e.preventDefault()">
                {{ value + '秒' }}
                <a-icon type="down" />
              </a>
              <a-menu slot="overlay" @click="handleMenuClick">
                <a-menu-item key="1000">1秒</a-menu-item>
                <a-menu-item key="5000">5秒</a-menu-item>
                <a-menu-item key="10000">10秒</a-menu-item>
                <a-menu-item key="30000">30秒</a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
        </div>
        <div
          :class="['header-right', isPause && 'pause']"
          @click="!isPause ? pause() : resume()"
        >
          <span>{{ isPause ? '暂停' : '进行中' }}</span>
          <custom-icon :type="isPause ? 'caret-right' : 'pause'" />
        </div>
      </div>
      <div class="node-info">
        <div>
          <span class="label">
            <custom-icon type="lu-icon-release-copy" />Release
          </span>
          <span class="value">
            <div v-for="(item, index) in releaseNum" :key="index">
              <span v-if="releaseNum.length > 1" class="node-num">{{
                index + 1
              }}</span>
              <span>{{ item }}</span>
            </div>
          </span>
        </div>
        <div>
          <span class="label">
            <custom-icon type="lu-icon-cpu" />CPU Sockets
          </span>
          <span class="value">
            <div v-for="(item, index) in cpuSocketsNum" :key="index">
              <span v-if="cpuSocketsNum.length > 1" class="node-num">{{
                index + 1
              }}</span>
              <span>{{ item }}</span>
            </div>
          </span>
        </div>
        <div>
          <span class="label">
            <custom-icon type="lu-icon-memo" />
            <span>Physical Memory</span>
          </span>
          <span class="value">
            <div v-for="(item, index) in physicalMemory" :key="index">
              <span v-if="cpuSocketsNum.length > 1" class="node-num">{{
                index + 1
              }}</span>
              <span>{{ item }}</span>
            </div>
          </span>
        </div>
        <div>
          <span class="label">
            <custom-icon type="lu-icon-cpu" />CPU Cores
          </span>
          <span class="value">
            <div v-for="(item, index) in cpuCoresNum" :key="index">
              <span v-if="cpuCoresNum.length > 1" class="node-num">{{
                index + 1
              }}</span>
              <span>{{ item }}</span>
            </div>
          </span>
        </div>
      </div>

      <div class="echart-block">
        <div class="line-echart-block" v-if="variables.length == 1">
          <div
            class="custom-line-echart"
            v-for="item in chartData"
            :key="item.name"
          >
            <div class="title">{{ item.name }}</div>
            <div class="content">
              <span>{{ item.num }}</span>
              <Chart :option="item.arr || []" ref="line" />
            </div>
          </div>
        </div>
        <div class="line-echart-table" v-if="variables.length > 1">
          <Table
            ref="chartTable"
            v-bind="chartParams"
            :dataSource="chartSource || []"
            class="new-view-table small-size"
          >
            <div slot="inst_id" slot-scope="{ text }" class="inst-id-box">
              {{ text }}
            </div>
            <div
              slot="chart_render"
              slot-scope="{ record, text }"
              class="chart-render-box"
            >
              <span>{{ text[text.length - 1] }}</span>
              <Chart :option="getLineOption(record, text)" ref="line" />
            </div>
          </Table>
        </div>
        <div
          class="bar-echart-block"
          :style="{
            height:
              variables.length <= 1
                ? `180px`
                : `${160 + (variables.length - 1) * 60}px`
          }"
        >
          <Chart :option="barOption" ref="bar" />
        </div>
      </div>
      <div class="table-block">
        <div class="search-area">
          <div class="title">SQL列表</div>
          <div class="search-content">
            <a-button
              type="primary"
              v-if="$permission.realTimeSql('batchKill')"
              @click="kill"
              class="kill-btn"
              >批量KILL</a-button
            >
            <a-tooltip>
              <template slot="title">表头设置</template>
              <custom-icon
                type="control"
                @click="customSetColumn"
              ></custom-icon>
            </a-tooltip>
            <a-tooltip>
              <template slot="title">模板设置</template>
              <custom-icon type="setting" @click="setting"></custom-icon>
            </a-tooltip>
            <a-tooltip>
              <template slot="title">重置</template>
              <custom-icon type="lu-icon-free" @click="reset"></custom-icon>
            </a-tooltip>
          </div>
        </div>
        <Table
          ref="table"
          v-bind="tableParams"
          :dataSource="dataSource || []"
          @filter-confirm="filterConfirm"
          @filter-reset="filterReset"
          @sorter="sorter"
          class="new-view-table small-size"
        >
          <LimitLabel
            slot="SQL_FULLTEXT"
            slot-scope="{ text }"
            :label="text"
            :contentStyle="{ width: '250px', height: 'auto', overflow: 'auto' }"
            :limit="16"
            :nowrap="true"
            format="sql"
          ></LimitLabel>
          <custom-btns-wrapper slot="action" slot-scope="{ record }">
            <a
              actionBtn
              @click="onSqlException(record)"
              :disabled="!record.info"
              v-if="$permission.realTimeSql('explain')"
              >Explain</a
            >
            <a-popconfirm
              title="确定kill?"
              @confirm="() => singleKill(record)"
              actionBtn
              v-if="$permission.realTimeSql('kill')"
            >
              <!-- <a :disabled="!canDo">Kill</a> -->
              <a>Kill</a>
            </a-popconfirm>
          </custom-btns-wrapper>
        </Table>
      </div>
    </div>
    <KillModal ref="kill"></KillModal>
    <CustomSetColumnModal
      ref="customSetColumn"
      @save="save"
    ></CustomSetColumnModal>
    <!-- <SqlExceptionDrawer ref="sqlException" :id="id" :dbType="dbType" /> -->
    <SettingDrawer ref="setting" :id="id" dbType="ORACLE" />
  </div>
</template>

<script>
import Tag from '@/components/Biz/Tag';
import Status from '@/components/Biz/Status';
import config from './config';
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import DbImg from '@/components/CustomImg/DbImg';
import Chart from '@/components/Chart';
import Select from '@/components/Select';
import SqlExceptionDrawer from '../components/sqlExceptionDrawer';
import SettingDrawer from '../components/settingDrawer';
import CustomSetColumnModal from '../components/customSetColumnModal';
import KillModal from './KillModal';
import {
  getOracleRealTimeInfo,
  getInstanceInfo,
  realtimeKill
} from '@/api/databaseaudit/realtime';

export default {
  components: {
    Tag,
    DbImg,
    Table,
    Chart,
    Select,
    Status,
    LimitLabel,
    SqlExceptionDrawer,
    SettingDrawer,
    CustomSetColumnModal,
    KillModal
  },
  props: {
    pane: Object,
    id: Number | String,
    paneKey: String
  },
  computed: {
    // canDo() {
    //   const user = this.$store.state.account.user || {};
    //   return ['leader', 'admin', 'dba'].includes(user.role);
    // }
  },
  data() {
    this.config = config(this);
    this.reqCancelHandler = null; // 取消请求句柄
    return {
      inited: false,
      value: 1,
      barOption: this.config.barOption(),
      dataSource: [],
      columns: [],
      tableParams: {
        url: '',
        reqParams: {},
        columns: this.config.columns,
        searchFields: [],
        // rowKey: 'SID',
        // scroll: { x: 1400 },
        scroll: { x: 'max-content' }
      },
      chartSource: [],
      chartParams: {
        url: '',
        reqParams: {},
        columns: this.config.chartColumns,
        rowKey: 'inst_id',
        pagination: false,
        // scroll: { x: 1400 }
        scroll: { x: 'max-content' }
      },
      visible: false,
      nodeVisible: false,
      time: null,
      count: 0,
      timeout: 1000,
      params: {},
      isPause: false,
      wrongNum: 0,
      variables: [],
      chartData: [],
      dbType: null,
      insId: null,
      sqlId: null,
      nodeValue: 'ALL',
      nodes: [],
      cpuCoresNum: [],
      cpuSocketsNum: [],
      releaseNum: [],
      physicalMemory: [],
      tableHeader: []
    };
  },
  created() {},
  mounted() {
    window.localStorage.removeItem('ORACLERealTimeSqlId');
    this.init();
  },
  beforeDestroy() {
    this.pageDestroyed = true;
    this.cancel();
  },
  methods: {
    init() {
      getInstanceInfo({ id: this.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.nodes = _.get(res, 'data.data') || [];
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
      // 开启轮询
      this.onSetInterval();
    },
    getTableSearchData() {
      this.$set(
        this.databaseParams,
        'url',
        '/sqlreview/after_audit/select_data'
      );
      this.$set(this.databaseParams, 'reqParams', {
        data_source_id: this.id,
        select_type: 'schema'
      });
    },
    // 手动用setTimeout 实现setInterval
    onSetInterval() {
      const loop = (id, sqlId, insId, params = {}) => {
        if (this.pageDestroyed) return;
        getOracleRealTimeInfo(id, sqlId, insId, params, c => {
          this.reqCancelHandler = c;
        })
          .then(res => {
            this.count++;
            if (CommonUtil.isSuccessCode(res)) {
              let resData = _.get(res, 'data.data') || {};
              let variables = resData.variables;
              let variable = variables[0];
              this.variables = variables;
              this.dataSource = resData.process_list;
              this.sqlId = resData.sql_id;
              this.cpuCoresNum = variables.map(item => item.num_cpu_cores);
              this.cpuSocketsNum = variables.map(item => item.num_cpu_sockets);
              this.releaseNum = variables.map(item => item.release);
              this.physicalMemory = variables.map(item => item.physical_memory);
              const barData = [
                resData.select,
                resData.insert,
                resData.update,
                resData.delete
              ];
              this.$set(
                this,
                'barOption',
                this.config.barOption(
                  barData,
                  this.chartSource.length,
                  'ORACLE'
                )
              );
              if (variables.length == 1) {
                const activeSessions = variable.active_sessions;
                const tableLocks = variable.table_locks;
                const runningSql = variable.running_sql;
                const ioWaitTime = variable.io_wait_time;
                const connections = variable.connections;
                const lockedSessions = variable.locked_sessions;
                const osLoad = variable.os_load;
                this.chartData = [
                  {
                    name: 'Active Sessions',
                    num: activeSessions[activeSessions.length - 1],
                    arr: this.config.lineOption(
                      '#A3A9EF',
                      '#E4E6FB',
                      activeSessions
                    )
                  },
                  {
                    name: 'Lock Tables',
                    num: tableLocks[tableLocks.length - 1],
                    arr: this.config.lineOption(
                      '#90C6AC',
                      '#E5F3E8',
                      tableLocks
                    )
                  },
                  {
                    name: 'Running SQL',
                    num: runningSql[runningSql.length - 1],
                    arr: this.config.lineOption(
                      '#90C6AC',
                      '#E5F3E8',
                      runningSql
                    )
                  },
                  {
                    name: 'IOwait Time(ms)',
                    num: ioWaitTime[ioWaitTime.length - 1],
                    arr: this.config.lineOption(
                      '#E4BF94',
                      '#F8F2E4',
                      ioWaitTime
                    )
                  },
                  {
                    name: 'Connections',
                    num: connections[connections.length - 1],
                    arr: this.config.lineOption(
                      '#F1A1A1',
                      '#FBE6E0',
                      connections
                    )
                  },
                  {
                    name: 'Lock Sessions',
                    num: lockedSessions[lockedSessions.length - 1],
                    arr: this.config.lineOption(
                      '#EE9DBA',
                      '#FAE1E7',
                      lockedSessions
                    )
                  },
                  {
                    name: 'OS Load',
                    num: osLoad[osLoad.length - 1],
                    arr: this.config.lineOption('#C0A1D8', '#EDE1F2', osLoad)
                  }
                ];
              } else if (variables.length > 1) {
                this.chartSource = variables;
                this.cpuCoresNum = this.chartSource.map(
                  item => item.num_cpu_cores
                );
                this.cpuSocketsNum = this.chartSource.map(
                  item => item.num_cpu_sockets
                );
                this.releaseNum = this.chartSource.map(item => item.release);
                this.physicalMemory = this.chartSource.map(
                  item => item.physical_memory
                );
              }
              this.tableHeader = resData.real_time_process_header;
              this.dealTableHeaderInfo(this.tableHeader);
            } else {
              if (this.count > 1) return;
              this.$message.error(_.get(res, 'data.message'));
            }
          })
          .catch(e => {
            this.wrongNum++;
            if (this.wrongNum > 1) return;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          })
          .finally(e => {
            if (!this.inited) {
              this.inited = true;
            }
            if (this.wrongNum >= 5) {
              this.isPause = !this.isPause;
              this.cancel();
              return;
            }
            clearTimeout(this.timer);
            // 取消接口请求
            if (this.reqCancelHandler) {
              this.reqCancelHandler();
            }
            // 暂停之后 搜索会再调一次接口 这时不轮巡
            if (!this.isPause) {
              this.timer = setTimeout(() => {
                const sqlId = window.localStorage.getItem(
                  'ORACLERealTimeSqlId'
                );
                this.sqlId = !_.isEmpty(sqlId) ? sqlId : this.sqlId;
                const params = this.params;
                loop(this.id, this.sqlId, this.insId, params);
              }, this.timeout);
            }
          });
      };
      loop(this.id, this.sqlId, this.insId, this.params);
    },
    // 处理表头数据
    dealTableHeaderInfo(data = []) {
      let columns = [];
      let searchFields = [];
      data.forEach(item => {
        columns.push({
          title: item.value,
          dataIndex: item.key,
          key: item.key,
          sorter: item.sorter,
          scopedSlots: { customRender: item.key }
        });
        item.type &&
          searchFields.push({
            type: item.type,
            label: item.value,
            key: item.key,
            props: {
              placeholder: '请输入'
            }
          });
      });
      // columns.push({
      //   title: '操作',
      //   key: 'action',
      //   slots: { title: 'customTitle' },
      //   scopedSlots: { customRender: 'action' },
      //   visible: $permissionBatch.some([
      //     { module: 'realTimeSql', values: ['kill', 'explain'] }
      //   ]),
      //   fixed: 'right'
      // });
      columns = columns.map(item => {
        return {
          ...item,
          width: undefined
        };
      });
      const localColumns = JSON.parse(
        window.localStorage.getItem('oracleRealTimeColumns')
      );
      const localSearchFields = JSON.parse(
        window.localStorage.getItem('oracleRealTimeSearchFields')
      );
      this.$set(
        this.tableParams,
        'columns',
        !_.isEmpty(localColumns) ? localColumns : columns
      );
      this.$set(
        this.tableParams,
        'searchFields',
        !_.isEmpty(localSearchFields) ? localSearchFields : searchFields
      );
    },
    // 设置更新秒数
    handleMenuClick(e) {
      this.value = e.key / 1000;
      this.timeout = e.key;
      this.visible = false;
    },
    handleMenuNode(e) {
      this.nodes.forEach(item => {
        if (item.value == e.key) {
          this.nodeValue = item.label;
        }
      });
      this.insId = e.key;

      this.nodeVisible = false;
      if (this.isPause) this.onSetInterval();
    },
    getLineOption(record, data) {
      let option = {};
      const colorObj = {
        active_sessions: ['#A3A9EF', '#E4E6FB'],
        table_locks: ['#90C6AC', '#E5F3E8'],
        // free_memory_bytes: ['#90C6AC', '#E5F3E8'],
        running_sql: ['#90C6AC', '#E5F3E8'],
        io_wait_time: ['#E4BF94', '#F8F2E4'],
        connections: ['#F1A1A1', '#FBE6E0'],
        locked_sessions: ['#EE9DBA', '#FAE1E7'],
        os_load: ['#C0A1D8', '#EDE1F2']
      };

      const keys = Object.keys(record);

      this.variables.forEach(item => {
        keys.forEach(elem => {
          if (item[elem] == data) {
            option = this.config.lineOption(
              colorObj[elem][0],
              colorObj[elem][1],
              data
            );
          }
        });
      });
      return option;
    },
    // 停止计时器
    cancel() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      // 取消接口请求
      if (this.reqCancelHandler) {
        this.reqCancelHandler();
      }
      this.count = 0;
      this.wrongNum = 0;
      this.params = {};
    },
    // 暂停
    pause() {
      this.isPause = true;
      this.cancel();
    },
    // 恢复
    resume() {
      if (!this.inited) return;
      this.isPause = false;
      this.cancel();
      this.onSetInterval();
    },
    // 全部kill
    kill() {
      this.$refs.kill.show(this.id, 'oracle');
    },
    setting() {
      this.$refs.setting.show();
    },
    // 设置表头弹窗
    customSetColumn() {
      this.$refs.customSetColumn.show('oracle', this.tableHeader);
    },
    save(data) {
      let columns = [];
      let searchFields = [];
      this.tableHeader.forEach(item => {
        data.forEach(el => {
          if (item.key == el) {
            columns.push({
              title: item.value,
              dataIndex: item.key,
              key: item.key,
              sorter: item.sorter,
              scopedSlots: { customRender: item.key }
            });
            item.type &&
              searchFields.push({
                type: item.type,
                label: item.value,
                key: item.key,
                props: {
                  placeholder: '请输入'
                }
              });
          }
        });
      });

      window.localStorage.setItem(
        'oracleRealTimeColumns',
        JSON.stringify(columns)
      );
      window.localStorage.setItem(
        'oracleRealTimeSearchFields',
        JSON.stringify(searchFields)
      );
    },
    // 执行计划
    onSqlException(record) {
      this.$refs.sqlException.show('realtime', record, this.pane);
    },
    // 列表单个kill
    singleKill(record) {
      const params = {
        data_source_id: this.id,
        id: record.id
      };
      realtimeKill(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: '操作成功'
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.msg')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 单个筛选
    filterConfirm(data, col) {
      this.params = {
        ...this.params,
        ...data
      };
      if (this.isPause) this.onSetInterval();
    },
    // 单个筛选重置
    filterReset(data, col) {
      this.params = {
        ...this.params,
        ...data
      };
      if (this.isPause) this.onSetInterval();
    },
    // 全部筛选重置
    reset() {
      this.params = {};
      this.$refs.table.onReset();
      if (this.isPause) this.onSetInterval();
    },
    // 表格过滤
    sorter(data) {
      this.params = {
        ...this.params,
        _sorter: data.order ? { [data.columnKey]: data.order } : undefined
      };
      if (this.isPause) this.onSetInterval();
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.custom-title {
  width: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .anticon {
    margin-right: 4px;
    font-size: 14px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    line-height: 24px;
    &:hover {
      background: @primary-3;
      color: #ffffff;
      cursor: pointer;
    }
  }
}
.database-audit-real-time {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    background: #ffffff;
    padding: 0 32px;
    .header-left {
      font-size: 13px;
      color: #a1a1aa;
      font-weight: 400;
      .setting-border {
        padding: 0 8px;
        margin-right: 8px;
        > span {
          color: #27272a;
        }
        &:first-child {
          padding: 0 8px 0 0;
          margin-right: 0;
        }
      }
      .node-box,
      .reload-box {
        margin: 0 8px;
        .anticon-sync {
          font-size: 12px;
          color: #27272a;
        }
        .ant-dropdown-link {
          font-size: 13px;
          color: #27272a;
          font-weight: 400;
        }
      }
      .node-box {
        margin: 0 8px 0 0;
        > span {
          font-size: 13px;
          color: #27272a;
        }
      }
    }
    .header-right {
      background: #ffffff;
      font-size: 14px;
      color: #008adc;
      text-align: center;
      font-weight: 400;
      border: 1px solid #a5d9f8;
      border-radius: 3px;
      > span {
        padding: 8px 12px;
        border-right: none;
      }
      .anticon {
        padding: 8px 6px;
        border-left: 1px solid #a5d9f8;
      }
      &:hover {
        cursor: pointer;
        background: #eff5ff;
      }
      &.pause {
        color: #ef6173;
        border: 1px solid #f7bbc2;
        .anticon {
          border-left: 1px solid #f7bbc2;
        }
        &:hover {
          cursor: pointer;
          background: #fef3f5;
        }
      }
    }
  }
  .node-info {
    display: flex;
    flex-wrap: wrap;
    background: #ffffff;
    padding: 0 32px;
    > div {
      width: 50%;
      display: flex;
      border: 1px solid #e8e8e8;
      &:last-child {
        border-top: none;
      }
      &:nth-child(3) {
        border-top: none;
      }
      &:nth-child(odd) {
        border-right: none;
      }
      .label {
        display: flex;
        align-items: center;
        min-height: 36px;
        width: 20%;
        min-width: 200px;
        background: #fafafa;
        font-size: 12px;
        color: #27272a;
        .anticon {
          font-size: 16px;
          margin: 0 16px;
        }
      }
      .value {
        width: 80%;
        min-height: 28px;
        border-left: 1px solid #e8e8e8;
        font-size: 13px;
        color: #27272a;
        display: flex;
        align-items: center;
        padding-left: 12px;
        flex-wrap: wrap;
        > div {
          margin-right: 24px;
          display: flex;
          align-items: center;
          padding: 4px 0;
          > .node-num {
            width: 18px;
            height: 18px;
            line-height: 16px;
            font-size: 12px;
            text-align: center;
            margin-right: 8px;
            color: #a1a1a1;
            border: 1px solid #e0e0e0;
          }
        }
      }
    }
  }
  /deep/.echart-block {
    display: flex;
    align-items: center;
    background: #ffffff;
    border-radius: 0 0 12px 12px;
    margin-bottom: 16px;
    min-height: 180px;
    .line-echart-block {
      width: 75%;
      display: flex;
      flex-wrap: wrap;
      padding: 16px 0 0 32px;
      border-right: 1px solid #e0e0e0;
      .custom-line-echart {
        width: 25%;
        margin-bottom: 16px;
        .title {
          font-size: 14px;
          color: #27272a;
          font-weight: 400;
          margin-bottom: 8px;
        }
        .content {
          display: flex;
          align-items: flex-end;
          height: 42px;
          > span {
            margin-right: 16px;
            font-size: 28px;
            color: #27272a;
            font-weight: 600;
            vertical-align: bottom;
            display: inline-block;
          }
          .custom-chart {
            .chart-container {
              width: 60px !important;
              height: 42px !important;
            }
          }
        }
      }
    }
    .line-echart-table {
      width: 75%;
      padding: 0 32px;
      border-right: 1px solid #e0e0e0;
      .new-view-table {
        .ant-table-wrapper {
          .ant-table {
            .ant-table-content {
              .ant-table-thead {
                tr > th {
                  padding: 6px 12px !important;
                }
              }
              .ant-table-tbody {
                tr > td {
                  padding: 6px 12px !important;
                }
              }
            }
          }
        }
        .inst-id-box {
          width: 18px;
          height: 18px;
          line-height: 16px;
          font-size: 12px;
          color: #a1a1a1;
          text-align: center;
          border: 1px solid #e8e8e8;
        }
        .chart-render-box {
          display: flex;
          align-items: center;
          > span {
            margin-right: 8px;
            font-size: 16px;
            color: #27272a;
            font-weight: 600;
            // vertical-align: bottom;
            display: inline-block;
            white-space: nowrap;
            height: 24px;
          }
          .custom-chart {
            .chart-container {
              height: 36px !important;
              width: 42px !important;
            }
          }
        }
      }
    }
    .bar-echart-block {
      // border-left: 1px solid #e0e0e0;
      width: 25%;
      padding: 0 32px;
    }
  }

  .search-area {
    background: #ffffff;
    padding: 16px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 10px;
    // border-bottom: 1px solid #e8e8e8;
    border-radius: 12px 12px 0 0;
    .title {
      font-size: 16px;
      color: #27272a;
      font-weight: 600;
      // width: 180px;
      margin-right: 32px;
    }
    .search-content {
      width: 88%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .anticon {
        font-size: 16px;
        margin-left: 16px;
        &:hover {
          color: #27272a;
          cursor: pointer;
        }
      }
      .kill-btn {
        background: #008adc;
        border-radius: 4px;
        > span {
          padding: 16px 24px;
          font-size: 14px;
          color: #ffffff;
          font-weight: 400;
        }
      }
    }
  }
  .table-block {
    padding: 0 24px;
    border-radius: 0 0 12px 12px;
    background: #ffffff;
    /deep/.new-view-table {
      .ant-table-wrapper {
        .ant-spin-container {
          .ant-table {
            top: -8px;
            .ant-table-thead tr > .ant-table-selection-column {
              padding: 16px 0 16px 32px !important;
            }
            .ant-table-tbody tr > .ant-table-selection-column {
              padding: 16px 0 16px 32px !important;
            }
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1500px) {
  .database-audit-real-time {
    /deep/.echart-block {
      .line-echart-block {
        padding: 24px 0px 8px 32px;
        .custom-line-echart {
          width: 33.33%;
          .content {
            .custom-chart {
              .chart-container {
                width: 50% !important;
              }
            }
          }
        }
      }
      .bar-echart-block {
        min-height: 290px !important;
      }
    }
  }
}
</style>