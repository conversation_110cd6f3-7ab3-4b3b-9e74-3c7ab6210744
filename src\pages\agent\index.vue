<template>
  <div class="agent-list-content">
    <!-- 页面概述 -->
    <div class="header">
      <div :class="['summary', isCollapse && 'collapse']" @click="onCollapse">
        <div class="title">
          <span>查询</span>
          <custom-icon :type="isCollapse ? 'down' : 'up'"></custom-icon>
        </div>
      </div>
      <!-- 搜索内容 -->
      <div
        :class="['search-content', isCollapse && 'collapse']"
        v-show="!isCollapse"
      >
        <SearchArea
          v-bind="searchParams || {}"
          @reset="reset"
          @search="search"
          ref="search"
          searchMode="switch"
          :iconCombine="false"
          :isShowExpandIcon="false"
          :searchData="searchData || {}"
        ></SearchArea>
      </div>
    </div>
    <div class="table-content">
      <Table
        ref="table"
        v-bind="tableParams"
        class="new-view-table"
        :dataSource="dataSource"
      >
        <template slot="tableTopLeft">
          <div>agent worker列表</div>
        </template>

        <template slot="tableTopRight">
          <!-- <a-tooltip>
            <template slot="title">
              首先需要关闭agent自动采集开关，然后重启对应服务器上所有被监控应用进程方可生效。
            </template>
            <custom-icon type="question-circle"></custom-icon>
          </a-tooltip>
          <span>如何关闭项目agent采集</span> -->
          <a-tooltip>
            <template slot="title">
              控制应用服务进程重启后agent是否继续采集，开启表示应用重启后agent会自动发现应用并继续采集，关闭表示应用重启后agent终止采集。
            </template>
            <custom-icon type="question-circle"></custom-icon>
          </a-tooltip>
          <span>agent自动发现</span>
        </template>

        <Shower
          slot="Shower"
          slot-scope="{ text, record, column }"
          v-bind="{
            showerValue: text,
            showerData: record,
            config: column.shower
          }"
        >
          <div slot="name" class="table-info">
            <div>
              <span>{{ record.name }}</span>
            </div>
            <div slot="group_name">
              <LimitTags
                :tags="record.group_name.map((item) => ({ label: item }))"
                :limit="1"
              ></LimitTags>
            </div>
          </div>
          <!-- agent自动发现 -->
          <div slot="agent_auto_find" class="permission-status-and-action">
            <a-tooltip>
              <template slot="title"> agent自动发现 </template>
              <a-switch
                checked-children="开"
                un-checked-children="关"
                :checked="record.agent_auto_find === 1"
                @change="onChange(record)"
              />
            </a-tooltip>
          </div>
        </Shower>

        <!-- 嵌套子表格 -->
        <div class="inner-table" slot="expandedRowRender" slot-scope="{ text }">
          <Table
            ref="innerTable"
            v-bind="innerTableParams || {}"
            :dataSource="text.project_master || []"
            class="new-view-table"
          >
            <template slot="worker_status" slot-scope="{ text }">
              <span :class="`status-${text}`">{{
                text == 1 ? '在线' : '离线'
              }}</span>
            </template>

            <template slot="agent_master" slot-scope="{ record, text }">
              <span
                :class="`master-name master-status-${record.master_status}`"
              ></span>
              <span>{{ text }}</span>
            </template>

            <div slot="auto_find" slot-scope="{ record, text }" class="permission-status-and-action">
              <a-switch
                checked-children="开"
                un-checked-children="关"
                :checked="text === 1"
                @change="onInnerChange(record)"
              />
            </div>
          </Table>
        </div>
      </Table>
    </div>
  </div>
</template>
<script>
import Shower from '@/components/Shower';
import LimitLabel from '@/components/LimitLabel';
import LimitTags from '@/components/LimitTags';
import Table from '@/components/Table';
import SearchArea from '@/components/Biz/SearchArea/new';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import { agentAutoFindSwitch, agentFindSwitch } from '@/api/agent';
export default {
  name: 'white-list',
  components: {
    Table,
    Shower,
    SearchArea,
    LimitLabel,
    LimitTags
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    this.config = config(this);
    return {
      isCollapse: true,
      searchData: {},
      searchParams: {
        fields: this.config.searchFields
      },
      dataSource: [],
      tableParams: {
        url: '/sqlreview/project/project_agent',
        reqParams: {},
        method: 'get',
        columns: this.config.columns.filter(item => item.visible !== false),
        rowKey: 'id',
        showHeader: false,
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' }
      },
      innerDataSource: [],
      innerTableParams: {
        url: ``,
        columns: this.config.agentColumns,
        rowKey: 'agent_worker',
        size: 'small',
        pagination: {
          simple: true
        },
        scroll: { x: 'max-content' }
      },
      labelTypeText: {
        0: 'SQL_ID',
        1: 'XML_TEXT',
        2: 'XML_ID',
        3: 'TABLE',
        4: 'JAVA',
        5: 'PROCEDURE'
      }
    };
  },
  created() {},
  mounted() {},
  destroyed() {},
  computed: {},
  methods: {
    onTableLoaded(req, res) {
      this.innerDataSource = res.data.data.worker_list;
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
      this.$store.commit('common/setSearchCache', {
        whiteList: data
      });
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.onReset();
    },
    refresh() {
      const { table } = this.$refs;
      table.refreshKeep();
    },
    onCollapse() {
      this.isCollapse = !this.isCollapse;
    },
    // agent自动查询
    onChange(data) {
      this.$showLoading();
      agentAutoFindSwitch({
        status: data.agent_auto_find === 0 ? 1 : 0,
        id: data.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const { table } = this.$refs;
            table.refreshKeep(null, { _clear: true });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // agent worker采集配置
    onInnerChange(data) {
      this.$showLoading();
      agentFindSwitch({
        auto_find: data.auto_find === 0 ? 1 : 0,
        id: data.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const { table } = this.$refs;
            table.refreshKeep(null, { _clear: true });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.agent-list-content {
  .header {
    border-radius: 16px;
    margin-bottom: 16px;
    background-color: #fff;
    .summary {
      padding: 16px 24px;
      border-radius: 16px;
      cursor: pointer;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        > span {
          font-weight: 600;
        }
        .anticon {
          font-size: 14px;
          color: #8c8c8c;
        }
      }
    }
    .search-content {
      border-top: 1px solid #f0f0f0;
      &.collapse {
        .title {
          border-bottom: none;
        }
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        > .anticon {
          font-size: 14px;
          color: #8c8c8c;
          margin-right: 0;
        }
      }
      /deep/.search-area {
        box-shadow: none;
        border: none;
      }
    }
  }
  .table-content {
    border-radius: 16px;
    background: #fff;
    /deep/.custom-table {
      .search-area-wrapper {
        display: flex;
        justify-content: space-between !important;
        align-items: center;
        padding: 16px 24px;
        border-bottom: solid 1px #fafafa;
        .custom-table-top-left {
          > div {
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #1f1f1f;
            font-weight: 600;
          }
        }
        .custom-table-top-right {
          .anticon {
            line-height: 14px;
            margin-right: 4px;
            &:hover {
              cursor: pointer;
            }
          }
          span {
            color: #595959;
            &:nth-of-type(1) {
              margin-right: 16px;
            }
          }
        }
      }
      .table-info {
        display: flex;
        > div:nth-child(1) {
          width: 220px;
          margin-right: 12px;
        }
        > div:nth-child(2) {
          width: 120px;
          margin-right: 12px;
        }
      }
      .permission-status-and-action {
        display: flex;
        justify-content: space-around;
        .permission-status {
          margin-right: 12px;
        }
      }
      .inner-table {
        .search-area-wrapper {
          display: none;
        }
        .status-0 {
          background: #fafafa;
          border: 1px solid rgba(217, 217, 217, 1);
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #1f1f1f;
          border-radius: 4px;
          padding: 2px 6px;
        }
        .status-1 {
          background: #e6f7ff;
          border: 1px solid rgba(145, 213, 255, 1);
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #1890ff;
          border-radius: 4px;
          padding: 2px 6px;
        }
        .master-name {
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          margin-right: 8px;
          &.master-status-0 {
            background: #d9d9d9;
          }
          &.master-status-1 {
            background: #52c41a;
          }
        }
      }
      .ant-table-expanded-row {
        > td {
          padding: 0 32px 16px 0 !important;
        }
      }
    }
  }
}
</style>
