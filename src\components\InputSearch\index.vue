<template>
  <div class="custom-input-search">
    <a-input
      class="ipt-search"
      v-bind="inputProps"
      v-on="inputListeners"
      v-model="searchVal"
      @pressEnter="search"
    >
      <template #suffix>
        <custom-icon class="search-icon" @click="search" type="lu-icon-search" />
      </template>
    </a-input>
  </div>
</template>

<script>
const defaultProps = {
  placeholder: '请输入'
};
export default {
  components: {},
  model: {
    prop: 'value',
    event: 'search'
  },
  props: {
    value: [String],
    trim: {
      type: <PERSON><PERSON>an,
      default: true
    }
  },
  data() {
    return {
      searchVal: this.value
    };
  },
  computed: {
    inputProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    inputListeners() {
      return { ...this.$listeners };
    }
  },
  created() {},
  mounted() {},
  methods: {
    search() {
      let val = this.searchVal;
      if (this.trim && _.isString(val) && _.isFunction(val.trim)) {
        val = val.trim();
      }
      this.$emit('search', val);
    }
  },
  watch: {
    value(newVal) {
      this.searchVal = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
.custom-input-search {
  /deep/ .ipt-search {
    .ant-input {
      // height: 32px;
      // border-radius: 8px;
    }
  }
  .search-icon {
    .font-size(16px);
    &:hover {
      cursor: pointer;
    }
  }
}
</style>
