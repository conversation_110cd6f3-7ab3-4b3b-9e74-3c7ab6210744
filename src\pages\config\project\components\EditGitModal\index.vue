<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    :title="title"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Form ref="form" class="base-info-form" v-bind="formParams" :formData="formData"></Form>
    <template slot="footer">
      <a-button @click="onCancel">取消</a-button>
      <a-button @click="onOk" type="primary">确定</a-button>
    </template>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import config from './config';
import { Base64 } from 'js-base64';
import { checkProjectUrl } from '@/api/config/project';
export default {
  components: { Form },
  props: {},
  data() {
    this.config = config(this);
    return {
      title: '',
      visible: false,
      formParams: {
        layout: 'vertical',
        labelCol: { span: 24 },
        wrapperCol: { span: 20 },
        fields: {}
      },
      formData: {},
      active: '',
      titles: {
        tag: 'git tag设置',
        branch: 'branch设置',
        svn: 'svn设置'
      },
      record: {},
      flag: '',
      isEdit: false
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(type, data = {}, active, isEdit) {
      this.record = data.editRecord;
      this.flag = data.flag;
      this.isEdit = isEdit;
      this.formData = {
        review_url: this.record.review_url,
        review_tag: this.record.review_tag,
        username: this.record.username,
        password: this.record.password,
        white_list: (this.record.white_list || []).map(item => {
          return {
            path: item
          };
        }),
        black_list: (this.record.black_list || []).map(item => {
          return {
            path: item
          };
        })
      };
      this.active = active;
      const fields = this.config.fields[this.active];
      this.$set(this.formParams, 'fields', fields);
      this.title = this.titles[active];
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData();
      data.password = Base64.encode(data.password || '');
      // 利用引用关系 将数据保存为行内数据editRecord;
      this.record.review_url = data.review_url;
      this.record.username = data.username;
      this.record.password = data.password;
      this.record.review_tag = data.review_tag;
      this.record.white_list = (data.white_list || []).map(item => {
        return item.path;
      });
      this.record.black_list = (data.black_list || []).map(item => {
        return item.path;
      });
      form.validate(valid => {
        if (valid) {
          this.$showLoading();
          checkProjectUrl(data)
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.$emit('save', data, this.record, this.flag);
                this.onCancel();
                this.$hideLoading({ useMessage: true, tips: '验证成功' });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(err => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(err, 'data.message')
              });
            });
        }
      });
    },
    checkBtn() {}
  }
};
</script>

<style lang="less" scoped>
</style>
