<template>
  <div class="config-datasource-instancce-tablelist">
    <Table
      ref="table"
      v-bind="tableParams || {}"
      @selectChange="selectChange"
      class="new-view-table small-size"
    >
      <!-- table插槽 -->
      <!-- <template slot="is_permission_control" slot-scope="{ text }">
        <custom-icon :type="text == 1 ? 'lu-icon-safe' : 'lu-icon-operation'"></custom-icon>
        <span style="margin-left: 8px">{{text == 0 ? '自由操作' : '安全协同'}}</span>
      </template>-->
      <!-- <template slot="after_audit_status" slot-scope="{ text, record }">
        <a-switch
          v-if="['ORACLE','MYSQL', 'OB_ORACLE'].includes(record.db_type.toUpperCase())"
          checked-children="开"
          un-checked-children="关"
          :checked="text === 1"
          @change="onChange(record)"
        />
        <span v-else>{{'--'}}</span>
      </template>-->
      <div slot="name" slot-scope="{ text, record }" class="instance-name">
        <Tag type="Env" :text="record.env.toUpperCase()" />
        <DbImg
          value="OCEANBASE"
          :schemaName="text"
          :limit="16"
          v-if="record.db_type == 'OB_MYSQL' || record.db_type == 'OB_ORACLE'"
        />
        <DbImg
          value="TDSQL"
          :schemaName="text"
          :limit="16"
          v-else-if="
            record.db_type == 'TD_MYSQL' || record.db_type == 'TD_PGSQL'
          "
        />
        <DbImg v-else :value="record.db_type" :schemaName="text" :limit="16" />
        <a-popover>
          <template slot="content">{{ '未连接测试' }}</template>
          <custom-icon
            v-if="record.connect_status == 0"
            type="lu-icon-warning"
          />
        </a-popover>
        <a-popover>
          <template slot="content">{{ '连接测试失败' }}</template>
          <a-icon
            v-if="record.connect_status == 2"
            type="minus-circle"
            theme="filled"
            style="color: #d9363e"
          />
        </a-popover>
      </div>
      <LimitLabel
        slot="db_url"
        slot-scope="{ text }"
        :label="text.replace(/\/\//g, '')"
        :limit="30"
      ></LimitLabel>
      <!-- <DbImg slot="db_type" slot-scope="{text}" :type="text" /> -->
      <!-- <Tag slot="env" slot-scope="{text}" type="Env" :text="text.toUpperCase()" /> -->
      <custom-btns-wrapper
        slot="action"
        slot-scope="{ text, record }"
        :limit="3"
      >
        <a @click="toDatabaseTableList(record)" actionBtn>数据库列表</a>
        <a @click="editProject(record)" actionBtn>编辑</a>
        <!-- <a
          v-if="!isTaiLong"
          @click="authManager(record)"
          actionBtn
          :disabled="record.is_permission_control == 0"
        >权限管理</a>-->
        <!-- <a
          @click="collect(record)"
          v-if="['ORACLE','MYSQL', 'OB_ORACLE'].includes(record.db_type.toUpperCase())"
          actionBtn
        >采集配置</a>-->
        <a-popconfirm
          title="确定删除?"
          @confirm="() => removeProject(record)"
          actionBtn
        >
          <a class="remove">删除</a>
        </a-popconfirm>
        <a
          v-if="record.db_type.toUpperCase() == 'MYSQL'"
          @click="getStatisticsInfoFn(record)"
          actionBtn
          >统计信息收集</a
        >
      </custom-btns-wrapper>
    </Table>
    <!-- 统计信息收集弹窗 -->
    <GetDataSourceStatistics
      ref="statistics"
      @refrsh="reset"
    ></GetDataSourceStatistics>
    <!-- 项目弹窗 -->
    <EditDrawer ref="editModal" @save="saveProject"></EditDrawer>
    <!-- 采集配置 -->
    <!-- <CollectConfigDrawer ref="collectConfig" @save="saveConfig"></CollectConfigDrawer> -->
    <AuthManager
      :DropdownSearchList="DropdownSearchList"
      :activeKey="activeKey"
      ref="authManager"
      @onAuth="onAuthShow"
    ></AuthManager>
    <BatchAuthDrawer
      :activeKey="activeKey"
      ref="batchAuth"
      @refresh="reset"
    ></BatchAuthDrawer>
    <!-- 拉取数据源弹窗 -->
    <PullDatasource ref="pullDatasource" @refresh="reset"></PullDatasource>
  </div>
</template>

<script>
import {
  addDataSource,
  editDataSource,
  deleteDataSource
} from '@/api/config/dataSource';
// import { saveCollectConfig } from '@/api/databaseaudit/topsql';
import Table from '@/components/Table';
import Status from '@/components/Biz/Status';
import SearchArea from '@/components/SearchArea';
import LimitLabel from '@/components/LimitLabel';
import EditDrawer from './components/EditDrawer';
// import CollectConfigDrawer from './components/CollectConfigDrawer';
import PullDatasource from './components/PullDatasourceModal';
import GetDataSourceStatistics from './components/GetDataSourceStatistics';
import AuthManager from './components/authManager';
import BatchAuthDrawer from './components/batchAuthDrawer';
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import config from './config';
import _ from 'lodash';

export default {
  name: 'HomePage',
  components: {
    Tag,
    DbImg,
    Table,
    Status,
    LimitLabel,
    SearchArea,
    EditDrawer,
    PullDatasource,
    GetDataSourceStatistics,
    AuthManager,
    BatchAuthDrawer
  },
  props: {
    activeKey: String
  },
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: '/sqlreview/project/data_source',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id',
        needTools: true,
        needSearchArea: true,
        searchFields: this.config.searchFields,
        scroll: { x: 'max-content' },
        rowSelection: {
          type: 'checkbox', // 多选单选
          columnWidth: '80',
          onSelectAll: this.onSelectAll,
          getCheckboxProps: record => ({
            // 选择框的默认属性配置
            props: {
              disabled: record.is_permission_control == 0 // 自由操作不需要选中
            }
          })
        }
      },
      isSelectAll: false,
      selectedRowKeys: [],
      DropdownSearchList: [
        {
          key: 'user_type',
          title: '用户类型',
          default: '全部',
          options: [
            { label: '全部', value: -1 },
            { label: '用户', value: 0 },
            {
              label: '用户组',
              value: 1
            }
          ]
        }
        // {
        //   key: 'permission_type',
        //   title: '权限类型',
        //   default: '全部',
        //   options: [
        //     { label: '全部', value: -1 },
        //     { label: '数据源登录', value: 0 },
        //     {
        //       label: '数据库登录',
        //       value: 1
        //     }
        //   ]
        // }
      ]
    };
  },
  computed: {
    // 泰隆银行特有
    isTaiLong() {
      const specificConfig = process.channel === 'TaiLongBank';
      return specificConfig;
    }
  },
  mounted() {},
  created() {},
  methods: {
    // 重置
    reset() {
      const { table } = this.$refs;
      table.refreshClear();
      this.selectedRowKeys = [];
    },
    // 权限管理
    authManager(record) {
      this.$refs.authManager.show(record, null, 'instance');
    },
    // 打开采集配置弹窗
    // collect(record) {
    //   this.$refs.collectConfig.show(record);
    // },
    batchAuthShow() {
      if (!this.selectedRowKeys || this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择数据源');
        return;
      }
      this.$refs.batchAuth.show(
        this.selectedRowKeys,
        this.isSelectAll,
        'instance'
      );
    },
    onAuthShow(id) {
      const data = [id];
      this.$refs.batchAuth.show(data, null, 'instance');
    },
    // 表格全选
    onSelectAll(e) {
      this.isSelectAll = e;
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    // 权限控制 启用禁用
    // onChange(data) {
    //   if (data.after_audit_status == 0) {
    //     this.$refs.collectConfig.show(data);
    //   } else {
    //     this.$showLoading();
    //     saveCollectConfig({
    //       collect_type: 0,
    //       data_source_id: data.id
    //     })
    //       .then(res => {
    //         if (_.get(res, 'data.code') == 0) {
    //           this.$hideLoading({ tips: _.get(res, 'data.message') });
    //           const { table } = this.$refs;
    //           table.refreshKeep(null, { _clear: true });
    //           this.selectedRowKeys = [];
    //         } else {
    //           this.$hideLoading({
    //             method: 'error',
    //             tips: _.get(res, 'data.message')
    //           });
    //         }
    //       })
    //       .catch(e => {
    //         this.$hideLoading({
    //           method: 'error',
    //           tips: _.get(e || {}, 'response.data.message') || '请求失败'
    //         });
    //       });
    //   }
    // },
    // 修改状态
    // onChange(record) {
    //   const { table } = this.$refs;
    //   this.$showLoading();
    //   editDataSource({
    //     id: record.id,
    //     download_status: record.download_status === 1 ? 0 : 1
    //   })
    //     .then(res => {
    //       if (_.get(res, 'data.code') == 0) {
    //         this.$hideLoading({ tips: _.get(res, 'data.message') });
    //         table.refresh();
    //       } else {
    //         this.$hideLoading({
    //           method: 'error',
    //           tips: _.get(res, 'data.message')
    //         });
    //       }
    //     })
    //     .catch(() => {
    //       this.$hideLoading({
    //         method: 'error',
    //         tips: _.get(e || {}, 'response.data.message') || '请求失败'
    //       });
    //     });
    // },
    // 新建项目
    addProject() {
      const { editModal } = this.$refs;
      editModal.show('add');
    },
    // 拉去数据源
    pullDatasource() {
      this.$refs.pullDatasource.show();
    },
    // 去数据库列表
    toDatabaseTableList(record) {
      this.$emit('onChangeTab', 'database', record.name);
    },
    // 编辑项目
    editProject(record) {
      const { editModal } = this.$refs;
      editModal.show(
        'edit',
        Object.assign({}, record, { password: undefined })
      );
    },
    // 统计信息获取
    getStatisticsInfoFn(record) {
      this.$refs.statistics.show(record);
    },
    // 删除项目
    removeProject(record) {
      const { table } = this.$refs;
      // 请求
      this.$showLoading();
      deleteDataSource({
        id: record.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 项目保存
    saveProject(params = {}) {
      const { editModal, table } = this.$refs;
      const { type, data } = params;
      const reqMethod = type === 'add' ? addDataSource : editDataSource;
      // 请求

      let submitData = new FormData();
      let reqParams = {};
      if (data.db_type == 'MYSQL') {
        data.ssl_ca &&
          data.ssl_ca.forEach(item => {
            submitData.append('ssl_ca', item);
          });
        data.ssl_cert &&
          data.ssl_cert.forEach(item => {
            submitData.append('ssl_cert', item);
          });
        data.ssl_key &&
          data.ssl_key.forEach(item => {
            submitData.append('ssl_key', item);
          });
        data.ssl_mode && submitData.append('ssl_mode', data.ssl_mode);
        data.ssl_key_password &&
          submitData.append(
            'ssl_key_password',
            Base64.encode(data.ssl_key_password || '')
          );
        submitData.append('db_url', data.db_url);
        submitData.append('decode_type', String(data.decode_type));
        submitData.append('name', data.name);
        submitData.append('env', data.env);
        submitData.append('user', data.user);
        submitData.append('password', Base64.encode(data.password || ''));

        submitData.append('url_type', String(data.url_type || 0));
        submitData.append('ssl', String(data.ssl || 0));

        submitData.append('type', String(data.url_type || 0));
        submitData.append('db_type', data.db_type);
        submitData.append('connect_status', String(data.connect_status || 2));
        data.id && submitData.append('id', String(data.id));
        submitData.append(
          'collect_config',
          JSON.stringify(data.collect_config)
        );
        reqParams = submitData;
      } else {
        reqParams = {
          url_type: data.type,
          ...data,
          password: Base64.encode(data.password || '')
        };
      }
      this.$showLoading();
      reqMethod(reqParams)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            editModal.hide();
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
    // // 采集配置保存后 刷新表格
    // saveConfig() {
    //   const { table } = this.$refs;
    //   table.refresh();
    // }
  }
};
</script>

<style lang="less" scoped>
.instance-name {
  display: flex;
  align-items: center;
  .ant-tag {
    border-radius: 6px;
    color: #fff !important;
  }
}
</style>
