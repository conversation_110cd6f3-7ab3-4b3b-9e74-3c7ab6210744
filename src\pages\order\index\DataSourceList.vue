<template>
  <div class="order page-list-single">
    <SwitchTable
      ref="switchTable"
      v-bind="tableParams"
      :cardColumns="cardColumns"
      :listColumns="listColumns"
    >
      <template slot="tableTopRight">
        <div class="switch-view">
          <a-tooltip>
            <template slot="title"> 切换视图 </template>
            <custom-icon
              type="lu-icon-viewlist"
              @click="switchView"
            ></custom-icon>
          </a-tooltip>
        </div>
      </template>
      <!-- 卡片模式 -->
      <DataSourceCard
        slot="cardTable"
        slot-scope="{ record }"
        v-bind="{ cardData: record }"
        @aKeyPass="aKeyPass(record.data_source_id)"
        @toDetail="toDetail(record)"
        @reject="reject(record.data_source_id)"
      >
      </DataSourceCard>
      <!-- 列表模式 -->
      <template slot="id" slot-scope="{ text, record }">
        <a @click="toDetail(record, $event)">{{ text }}</a>
      </template>
      <div slot="status" slot-scope="{ text }" class="dba-status">
        <a-tag :class="text == 1 && 'status-finish'">{{
          text == 0 ? '待审核' : '已完成'
        }}</a-tag>
      </div>
      <template slot="data_source_name" slot-scope="{ record }">
        <span class="datasource-content">
          <span :class="record.env == 'TEST' ? 'test' : 'prod'">{{
            record.env == 'TEST' ? '测试' : '生产'
          }}</span>
          <DbImg
            :value="record.db_type"
            :schemaName="record.data_source_name + '(' + record.db_url + ')'"
            mode="ellipsis"
          />
        </span>
      </template>
      <template slot="limit-tag" slot-scope="{ text }">
        <LimitTags
          :tags="text ? text.map((item) => ({ label: item })) : []"
          :limit="1"
          mode="numTag"
        ></LimitTags>
      </template>

      <custom-btns-wrapper slot="action" slot-scope="{ record }" :limit="3">
        <a @click="aKeyPass(record.data_source_id)" :disabled="record.wait_audit_count <= 0" actionBtn>一键通过</a>
        <a actionBtn @click="reject(record.data_source_id)" :disabled="record.wait_audit_count <= 0" class="highlight">
          一键驳回
        </a>
      </custom-btns-wrapper>
    </SwitchTable>
    <!-- 一键通过弹窗 -->
    <!-- <GetPassModal
      ref="getPass"
      @refresh="onRefresh"
      activeKey="datasource"
    ></GetPassModal> -->
    <!-- 一键驳回弹窗 -->
    <!-- <RejectModal
      ref="reject"
      @refresh="reset"
      activeKey="datasource"
    ></RejectModal> -->
  </div>
</template>

<script>
import LimitTags from '@/components/LimitTags';
import Status from '@/components/Biz/Status';
import SearchArea from '@/components/SearchArea';
import GetPassModal from './components/GetPassModal';
import RejectModal from './components/RejectModal';
// import moment from 'moment';
import config from './config';
import SwitchTable from '@/components/PageListNew/SwitchTable/table.vue';
import DataSourceCard from './components/DataSourceCard';
import StatusTag from '@/components/Biz/Status/Tag';
import { databaseReviewPassOrReject } from '@/api/order';
export default {
  name: 'order-list-data-source',
  components: {
    Status,
    SearchArea,
    GetPassModal,
    LimitTags,
    SwitchTable,
    DataSourceCard,
    StatusTag,
    RejectModal
  },
  props: {
    orderType: String
  },
  data() {
    this.config = config(this);
    // keep-alive是否激活
    this.activated = null;
    return {
      statusColor: this.config.statusColor,
      tableParams: {
        url: '/sqlreview/review/database_review/list',
        reqParams: {},
        columns: this.config.cardColumns,
        rowKey: 'data_source_id',
        showHeader: false,
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' },
        cacheKey: 'orderList' + this.orderType
      },
      cardColumns: this.config.cardColumns,
      listColumns: this.config.dateSourceColumns
    };
  },
  mounted() {},
  activated() {},
  deactivated() {},
  computed: {
    showAKeyPass() {
      const role = this.$store.state.account.user.role || '';
      return role == 'admin' || role.toLowerCase() == 'dba';
    },
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  created() {},
  methods: {
    // 切换视图
    switchView() {
      const { switchTable } = this.$refs;
      switchTable.switchView();
    },
    toDetail(record, e) {
      this.$router.push({
        name: 'orderDetail',
        params: { id: record.data_source_id },
        query: { status: record.dba_status, activeKey: 'datasource' }
      });
    },
    reject(id) {
      this.$showLoading();
      databaseReviewPassOrReject({
        data_source_id: id,
        audit_status: -1
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.onRefresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 查询
    // search(data) {
    //   const { switchTable } = this.$refs;
    //   const { table } = switchTable.$refs;
    //   table.refresh(null, data);
    // },
    // 重置
    reset() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refresh();
    },
    onRefresh() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refresh();
      this.$emit('getOrderTotal');
    },
    // 一键通过
    aKeyPass(id) {
      // this.$refs.getPass.show(id);
      this.$showLoading();
      databaseReviewPassOrReject({
        data_source_id: id,
        audit_status: 1 // 枚举 1通过；-1驳回
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.onRefresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.order {
  &.page-list-single {
    /deep/.custom-table {
      .search-area-wrapper {
        padding: 12px 24px;
        .custom-table-top-right {
          display: flex;
          align-items: center;
          .new-button {
            margin-right: 12px;
          }
          .switch-view {
            font-size: 16px;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            &:hover {
              cursor: pointer;
              background: #7adcff;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
.ant-dropdown-link {
  margin-top: 5px;
}
.status-finish {
  color: #52c41a !important;
  background: #f6ffed;
  border: 1px solid rgba(183, 235, 143, 1);
}
.datasource-content {
  display: flex;
  align-items: center;
  > span {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    border-radius: 4px;
    margin: 0 4px 0 0;
    padding: 0 4px;
  }
  .test {
    background: #f6ffed;
    border: 1px solid rgba(183, 235, 143, 1);
    color: #52c41a;
  }
  .prod {
    background: #fff7e6;
    border: 1px solid rgba(255, 213, 145, 1);
    color: #fa8c16;
  }
  .limit-label {
    width: 120px;
  }
}

@media screen and (max-width: 1440px) {
  .order {
    &.page-list-single {
      /deep/.custom-table {
        tr {
          .card-table {
            .right-block-botton {
              display: flex;
              animation: fadeIn 0.2s;
              justify-content: flex-end;
              > a {
                margin-left: 30px;
              }
            }
            .right-block-rules {
              display: none;
            }
            .right-block-sql-text {
              display: none;
            }
            .ant-divider-vertical {
              display: none;
            }
          }
        }
      }
    }
  }
}
</style>