<template>
  <div>
    <a-row type="flex" justify="space-around">
      <a-col
        :class="[
          'backgroundBlock',
          { active: active === item.code },
          { disabled: disabled === true }
        ]"
        :style="{width: itemWidth ? itemWidth + 'px' : 'auto'}"
        :key="index"
        v-for="(item, index) in dataSource"
      >
        <div :class="['topBlock']" @click="onChoose(item.code)">
          <custom-icon class="iconStyle" :type="item.icon" mode="svg" />
          <div class="text">{{ item.text }}</div>
        </div>
      </a-col>
    </a-row>
    <a-row type="flex" justify="space-around">
      <a-col
        class="backgroundBlock"
        :style="{width: itemWidth ? itemWidth + 'px' : 'auto'}"
        :key="index"
        v-for="(item, index) in dataSourceLine2"
      >
        <template v-if="item.icon">
          <div class="topBlock" @click="onChoose(item.code)">
            <custom-icon class="iconStyle" :type="item.icon" mode="svg" />
            <div class="text">{{ item.text }}</div>
          </div>
        </template>
      </a-col>
    </a-row>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    src: String,
    imgStyle: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dbType: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    itemWidth: {
      type: Number,
      default: 100
    },
    importDataSource: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      active: '',
      defaultDataSource: [
        { icon: 'lu-icon-oracle', code: 'ORACLE', text: 'ORACLE' },
        { icon: 'lu-icon-mysql', code: 'MYSQL', text: 'MYSQL' },
        { icon: 'lu-icon-pgsql', code: 'POSTGRE', text: 'POSTGRE' },
        { icon: 'lu-icon-tidb', code: 'TIDB', text: 'TIDB' },
        // { icon: 'lu-icon-oceanbase', code: 'OB_MYSQL', text: 'OCEANBASE' },
        // { icon: 'lu-icon-tdsql', code: 'TD_MYSQL', text: 'TDSQL' },
        { icon: 'lu-icon-oceanbase', code: 'OCEANBASE', text: 'OCEANBASE' },
        { icon: 'lu-icon-tdsql-1', code: 'TDSQL', text: 'TDSQL' },
        { icon: 'lu-icon-db2', code: 'DB2', text: 'DB2' },
        { icon: 'lu-icon-sql-server', code: 'SQLSERVER', text: 'SQLSERVER' },
        { icon: 'lu-icon-kingbase', code: 'KINGBASE', text: 'KINGBASE' },
        { icon: 'lu-icon-hive', code: 'HIVE', text: 'HIVE' },
        { icon: 'lu-icon-impala1', code: 'IMPALA', text: 'IMPALA' },
        { icon: 'lu-icon-presto', code: 'PRESTO', text: 'PRESTO' },
        { icon: 'lu-icon-goldendb', code: 'GOLDENDB', text: 'GOLDENDB' },
        { icon: 'lu-icon-mogdb', code: 'MOGDB', text: 'MOGDB' },
        { icon: 'lu-icon-gaussdb', code: 'GAUSSDB', text: 'GAUSSDB' },
        { icon: 'lu-icon-opengauss', code: 'OPENGAUSS', text: 'OPENGAUSS' },
        { icon: 'lu-icon-dameng', code: 'DM', text: 'DAMENG' }
      ],
      dataSourceLine2: []
    };
  },
  computed: {
    dataSource() {
      let res = this.defaultDataSource;
      if (!_.isEmpty(this.importDataSource)) res = this.importDataSource;
      return res;
    }
  },
  created() {},
  mounted() {},
  methods: {
    onChoose(key) {
      if (this.disabled) return;
      this.active = key;
      this.$emit('choose', key);
    }
  },
  watch: {
    dbType: {
      handler(val) {
        this.active = val;
      },
      immediate: true
    }
  }
};
</script>
<style lang="less" scoped>
.ant-row-flex-space-around {
  justify-content: flex-start;
  .backgroundBlock {
    margin-right: 12px;
    border: 1px solid #e4e4e7;
    border-radius: 4px;
    margin-bottom: 8px;
    padding: 2px;
    .topBlock {
      // background: #fff;
      border: none;
      padding: 12px 0;
      margin-bottom: 0;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      .text {
        margin: 8px 0 0 0;
      }
    }
    .iconStyle {
      width: 30px;
      height: 30px;
      font-size: 30px;
    }

    &.active,
    &:not(.disabled):hover {
      border: 1px solid @checked-border-color !important;
      background: @checked-bg !important;
    }

    &.disabled {
      .topBlock {
        cursor: not-allowed !important;
        opacity: 0.6;
      }
    }
  }
}
</style>
