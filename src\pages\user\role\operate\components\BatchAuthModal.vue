<template>
  <a-modal
    v-model="visible"
    title="批量授权"
    :width="600"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Select style="width:500px;" v-bind="roleParams" v-model="roleValue" />
    <a-divider />
    <AttrPanel ref="AttrPanel" :fields="fields" :data="formData" />
    <a-alert message="批量授权，会覆盖所有选中角色当前节点的控制属性，请谨慎" banner />
  </a-modal>
</template>

<script>
import { roleUpdateBatch } from '@/api/resource';
import Select from '@/components/Select';
import AttrPanel from './AttrPanel';
import config from './config';

export default {
  components: { Select, AttrPanel },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      roleParams: {
        url: '/bettle/base/role/list',
        // labelKey: 'role_name',
        // valueKey: 'id',
        placeholder: '请选择角色',
        mode: 'multiple'
      },
      roleValue: undefined,
      menuCode: null,
      node: null,
      fields: [],
      formData: {}
    };
  },
  mounted() {},
  created() {},
  methods: {
    show({ menuCode, node, formData }) {
      this.node = node;
      this.fields = this.config.getFields(node, true);
      this.formData = formData;
      this.menuCode = menuCode;
      this.visible = true;
    },
    hide() {
      this.roleValue = undefined;
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      let ext = {
        ...this.node.source_ext,
        ...this.$refs.AttrPanel.getData()
      };
      // 请求
      this.$showLoading();
      roleUpdateBatch({
        role_ids: this.roleValue,
        menu_code: this.menuCode,
        source_data: ext
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ useMessage: true, tips: '批量授权成功' });
            this.hide();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
