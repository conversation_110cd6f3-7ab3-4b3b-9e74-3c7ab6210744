<template>
  <a-form-model ref="form" v-bind="formProps" v-on="formListeners" :class="classes" :model="data">
    <!-- 空白 -->
    <custom-empty v-if="fieldArray.length <= 0 && needEmpty !== false"></custom-empty>
    <template v-else>
      <!-- 多列布局 -->
      <a-row :gutter="gutter" v-if="multiCols">
        <template v-for="item in fieldArray">
          <a-col
            :key="item.key"
            :span="item.span ? item.span : 24 / multiCols"
            :style="{visibility: item.hidden ? 'hidden' : 'visible', display: item.block !== false ? 'inline-block' : 'none'}"
            :class="item.className"
            v-if="item.visible != false"
          >
            <a-form-model-item
              :rules="item.rules"
              :prop="item.key"
              :labelCol="formProps.labelCol || {}"
              :wrapperCol="formProps.wrapperCol || {}"
              :class="`multi-cols-item`"
            >
              <span slot="label" v-if="item.label">
                <custom-icon :type="item.icon" v-if="item.icon" />
                {{item.label}}
                <Tips
                  :tips="item.tips"
                  v-if="item.tipsPlacement !== 'end'"
                  v-bind="item.tipsInfo || {}"
                ></Tips>
              </span>
              <custom-icon :type="item.compIcon" v-if="iconCombine" class="comp-icon" />
              <FormTriggerComp
                v-if="useTriggerComp"
                v-model="data[item.key]"
                :field="item || {}"
                :hideLabel="true"
              ></FormTriggerComp>
              <template v-else>
                <component
                  v-model="data[item.key]"
                  v-bind="item.props || {}"
                  v-on="item.listeners || {}"
                  :ref="item.key"
                  :is="item.realType"
                  :style="{ width: item.width ? (item.width + '').endsWith('%') ? item.width : item.width + 'px' : '100%' }"
                  v-if="item.hideComponent !== true"
                ></component>
              </template>
              <Tips
                style="margin-left: 12px;"
                :tips="item.tips"
                v-if="item.tipsPlacement === 'end'"
                v-bind="item.tipsInfo || {}"
              ></Tips>
              <template v-for="slotItem in (item.slots || [])">
                <div
                  :key="slotItem.key"
                  :style="slotItem.wrapperStyle || {}"
                  :class="slotItem.className"
                >
                  <slot
                    :name="slotItem.key"
                    :itemKey="item.key"
                    :data="data[item.key]"
                    :info="slotItem.info"
                  ></slot>
                </div>
              </template>
            </a-form-model-item>
          </a-col>
        </template>
      </a-row>
      <!-- 默认 -->
      <template v-for="item in fieldArray" v-else>
        <a-form-model-item
          :key="item.key"
          :prop="item.key"
          :rules="item.rules"
          :labelCol="formProps.labelCol || {}"
          :wrapperCol="formProps.wrapperCol || {}"
          :style="{visibility: item.hidden ? 'hidden' : 'visible', display: item.block !== false ? 'block' : 'none'}"
          :class="[item.className, item.ignoreToggle && 'ignore-toggle', item.expanded == true && 'expanded']"
          v-if="item.visible != false"
        >
          <span slot="label" v-if="item.label" @click="onToggle(item)">
            <custom-icon :type="item.icon" v-if="item.icon" />
            {{item.label}}
            <Tips
              :tips="item.tips"
              v-if="item.tipsPlacement !== 'end'"
              v-bind="item.tipsInfo || {}"
            ></Tips>
            <!-- 折叠按钮 -->
            <a-icon :class="{ 'icon-expand-arrow' : true }" type="double-right"></a-icon>
          </span>
          <template v-if="item.hideComponent !== true">
            <custom-icon :type="item.compIcon" v-if="iconCombine" class="comp-icon" />
            <FormTriggerComp
              v-if="useTriggerComp"
              v-model="data[item.key]"
              :field="{...item, width: item.width || '100%'} || {}"
              :hideLabel="true"
            ></FormTriggerComp>
            <component
              v-else
              v-model="data[item.key]"
              v-bind="item.props || {}"
              v-on="item.listeners || {}"
              :ref="item.key"
              :is="item.realType"
              :style="{ width: item.width ? (item.width + '').endsWith('%') ? item.width : item.width + 'px' : '100%' }"
            ></component>
          </template>
          <Tips
            style="margin-left: 12px;"
            :tips="item.tips"
            v-if="item.tipsPlacement === 'end'"
            v-bind="item.tipsInfo || {}"
          ></Tips>
          <template v-for="slotItem in (item.slots || [])">
            <div
              :key="slotItem.key"
              :style="slotItem.wrapperStyle || {}"
              :class="slotItem.className"
            >
              <slot
                :name="slotItem.key"
                :itemKey="item.key"
                :data="data[item.key]"
                :info="slotItem.info"
              ></slot>
            </div>
          </template>
        </a-form-model-item>
      </template>
    </template>
  </a-form-model>
</template>

<script>
import CustomFormComponents from './register';
import FormTriggerComp from './FormTriggerComp';
import config from './config';
import Tips from './Tips';
// import common from '@/utils/common';
// import _ from 'lodash';

const compMap = config().compMap;
export default {
  inheritAttrs: false,
  components: {
    Tips,
    FormTriggerComp,
    ...config().defineComp
  },
  props: {
    fields: {
      type: Array,
      default: function() {
        return [];
      }
    },
    // 是否使用FormTriggerComp组件
    useTriggerComp: Boolean,
    // 是否固定label宽度
    fixedLabel: Boolean,
    // 多列布局（只支持能整除24的数字）
    multiCols: Number,
    gutter: {
      type: Number,
      default: 16
    },
    formData: {
      type: Object,
      default: function() {
        return {};
      }
    },
    auth: {
      type: Object,
      default: function() {
        return {};
      }
    },
    labelOverflow: {
      type: Boolean,
      default: false
    },
    needToggle: {
      type: Boolean,
      default: false
    },
    needEmpty: {
      type: Boolean,
      default: true
    },
    // 紧凑型
    compact: {
      type: Boolean,
      default: false
    },
    // 自由型
    free: {
      type: Boolean,
      default: false
    },
    // 取消label 用前缀icon替换 且icon在输入框内
    iconCombine: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const { fields } = this;
    this.inited = false;
    // 外部form数据引用
    // this.outerValue = {};
    return {
      data: {},
      fieldArray: this.preDealFields(fields)
    };
  },
  computed: {
    formProps() {
      return {
        ...this.$attrs,
        ...(this.fixedLabel && this.$attrs.colon !== true
          ? { colon: false }
          : {})
      };
    },
    formListeners() {
      return { ...this.$listeners };
    },
    classes() {
      let arr = ['form'];

      if (this.useTriggerComp) {
        arr.push('use-form-trigger-comp');
      }

      if (this.fixedLabel) {
        arr.push('fixed-label');
      }

      if (this.$attrs.colon === true) {
        arr.push('with-colon');
      }

      if (this.labelOverflow === true) {
        arr.push('label-overflow');
      }

      if (
        this.needToggle &&
        this.multiCols == null &&
        this.$attrs.layout === 'vertical'
      ) {
        arr.push('need-toggle');
      }

      if (this.compact) {
        arr.push('compact');
      }

      if (this.free) {
        arr.push('free');
      }
      if (this.iconCombine) {
        arr.push('icon-combine');
      }
      return arr;
    }
  },
  created() {},
  mounted() {},
  methods: {
    resetFields() {
      this.$refs.form.resetFields();
    },
    validate(cbk) {
      let dataExtra = {};
      let validateArr = [this.$refs.form.validate()];
      this.fieldArray.forEach(matchItem => {
        if (matchItem != null && matchItem.visible != false && matchItem.key) {
          const key = matchItem.key;
          // const val = this.data[key];
          const comp = this.$refs[key];
          if (matchItem.validateMethod && comp && comp[0]) {
            comp[0][matchItem.validateMethod] &&
              validateArr.push(comp[0][matchItem.validateMethod]());
          }
          if (matchItem.getDataMethod && comp && comp[0]) {
            dataExtra[key] =
              comp[0][matchItem.getDataMethod] &&
              comp[0][matchItem.getDataMethod]();
          }
        }
      });
      // this.setData(dataExtra);
      // 兼容直接使用外部form数据情况
      // this.outerValue = Object.assign(this.outerValue, this.getData());
      if (!cbk) {
        return Promise.all(validateArr);
      }
      this.$refs.form.validate(cbk);
    },
    validateField(props, cbk) {
      return this.$refs.form.validateField(props, cbk);
    },
    preDealFields(fields = []) {
      return $Auth(
        this.auth.fields,
        fields.map(field => {
          let item = _.isFunction(field) ? field(this.data, this) : field;
          const matchItem =
            compMap[item.type] || CustomFormComponents.comp[item.type] || {};
          // type
          item.realType = matchItem.compName
            ? matchItem.compName
            : `a${item.type.replace(/([A-Z])/g, $0 => '-' + $0.toLowerCase())}`;
          // props
          item.props = { ...(matchItem.props || {}), ...(item.props || {}) };
          // console.log(item, 888)
          // 实时value
          if (item.realValue) {
            this.$nextTick(() => {
              this.setData({
                [item.key]: item.realValue
              });
            });
          }
          return item;
        })
      );
    },
    // 获取data，排除visible为false的数据
    getData(params = {}) {
      let res = {};
      this.fieldArray.forEach(matchItem => {
        // let matchItem = _.isFunction(field) ? field(this.data) : field;
        // const matchItem = this.fields.find(item => item.key == key);
        if (matchItem != null && matchItem.visible != false && matchItem.key) {
          const key = matchItem.key;
          const val = this.data[key];
          const comp = this.$refs[key];
          if (matchItem.getDataMethod && comp && comp[0]) {
            res[key] =
              comp[0][matchItem.getDataMethod] &&
              comp[0][matchItem.getDataMethod]();
          } else if (matchItem.type === 'Input') {
            res[key] = _.isString(val) && val ? val.trim() : val;
          } else {
            // if (val !== undefined) {
            // }
            res[key] = val;
          }
          if (params.nullString) {
            res[key] = res[key] === undefined ? '' : res[key];
          }
        }
      });
      // 加载默认值
      res = window.$AuthGetDefaultValue
        ? $AuthGetDefaultValue(this.auth.fields || {}, res)
        : res;
      return res;
    },
    saving(data = {}) {
      this.setData(data);
      this.refresh();
    },
    setData(value) {
      if (_.isPlainObject(value)) {
        _.forEach(value, (item, key) => {
          this.$set(this.data, key, item === null ? undefined : item);
        });
      }
    },
    refresh() {
      this.fieldArray = this.preDealFields(this.fields);
    },
    getInitValue() {
      const { fieldArray, fields, formData } = this;
      let res = {};
      (fieldArray || fields).forEach(item => {
        let formItem = _.isFunction(item) ? item({}, this) : item;
        if (
          formItem &&
          formItem.initialValue !== undefined &&
          formItem.initialValue !== ''
        ) {
          res[formItem.key] = formItem.initialValue;
        }
      });
      res = window.$AuthGetDefaultValue
        ? $AuthGetDefaultValue(this.auth.fields || {}, res)
        : res;
      return _.isEmpty(formData) ? res : formData;
    },
    onToggle(item) {
      const { needToggle, multiCols } = this;
      if (
        !needToggle ||
        multiCols > 0 ||
        this.$attrs.layout !== 'vertical' ||
        item.ignoreToggle
      ) {
        return;
      }
      this.$set(item, 'expanded', !item.expanded);
    },
    isArrayEmpty(list = []) {
      const arr = list.map(item => item != null);
      return _.isEmpty(arr);
    }
  },
  watch: {
    formData: {
      handler(newVal = {}, oldVal) {
        // console.log(newVal, 'sssssssssfffffffff');
        // this.data = _.merge({}, newVal);
        if (!this.inited && !this.isArrayEmpty(this.fields)) {
          this.data = Object.assign(newVal, this.getInitValue());
          this.inited = true;
        } else {
          this.data = newVal;
        }
        // 兼容直接使用外部form数据情况
        // this.outerValue = newVal;
        this.refresh();
      },
      immediate: true
      // deep: true
    },
    fields: {
      handler(newVal, oldVal) {
        this.fieldArray = this.preDealFields(newVal);
        if (!this.inited && !this.isArrayEmpty(this.fields)) {
          // this.data = Object.assign(this.data, this.getInitValue());
          this.setData(this.getInitValue());
          this.inited = true;
        }
      },
      immediate: true
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.form {
  .ant-col {
    display: inline-block;
    // vertical-align: top;
    float: none;
  }
  /deep/ .multi-cols-item {
    margin: 0 0 16px 0;
    display: block;

    .ant-form-explain {
      position: absolute;
    }
  }
  .icon-expand-arrow {
    display: none;
  }
  // 隐藏label
  /deep/ .ant-form-item.hidden-label {
    > .ant-form-item-label {
      visibility: hidden;
    }
  }
  .hidden-label {
    /deep/ .ant-form-item > .ant-form-item-label {
      visibility: hidden;
    }
  }

  &.fixed-label {
    /deep/ .ant-form-item-label {
      white-space: normal;
      word-break: break-all;
      line-height: 24px;
      padding-right: 8px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      min-height: 40px;

      label {
        display: flex;
        width: 100%;
        justify-content: flex-end;
        > span {
          display: inline-block;
          word-break: break-word;
        }

        &::before {
          line-height: inherit;
        }
        &::after {
          content: '';
          display: none;
        }
      }
    }
  }
  &.fixed-label-left {
    /deep/ .ant-form-item-label {
      display: flex;
      justify-content: flex-start;

      > label {
        justify-content: flex-start;
      }
    }
  }
  &.with-colon {
    /deep/ .ant-form-item-label {
      label {
        &::after {
          content: ':';
          display: inline-block;
          flex-shrink: 0;
        }
      }
    }
  }
  &.label-overflow {
    /deep/ .ant-form-item-label {
      label {
        > span {
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  &.horizontal-multiCols-label {
    padding: 12px 12px 0 12px;
    > .ant-row > .ant-col {
      vertical-align: top;
    }
    /deep/ .ant-form-item {
      // margin-bottom: 12px;

      .ant-form-item-label {
        line-height: 24px;
        min-height: 0;
      }

      .ant-form-item-control-wrapper {
        .ant-form-item-control {
          line-height: 24px;
          .custom-label {
            color: #888888;
          }
        }
      }
    }
  }
  &.horizontal-multiCols-normal {
    padding: 12px 12px 0 12px;
  }
  &.need-toggle {
    /deep/ .ant-form-item {
      &:not(.ignore-toggle) {
        .ant-form-item-label {
          background: #fafafa;
          padding: 0;
          > label {
            display: flex;
            align-items: center;
            padding: 0 8px;
            > span {
              display: block;
              padding: 8px 0;
              cursor: pointer;
              flex-grow: 1;
              position: relative;
              .icon-expand-arrow {
                position: absolute;
                right: 8px;
                top: 10px;
                display: inline-block;
                transform: rotate(90deg);
                color: rgba(0, 0, 0, 0.45);
              }
            }
          }
        }
      }

      .ant-form-item-control-wrapper {
        overflow: hidden;
        height: 0;
        min-height: 0;
      }

      &.ignore-toggle {
        .ant-form-item-control-wrapper {
          overflow: visible;
          height: auto;
        }
      }

      &.expanded {
        .ant-form-item-control-wrapper {
          overflow: visible;
          height: auto;
        }
        .icon-expand-arrow {
          transform: rotate(270deg) !important;
        }
      }
    }
  }
  &.compact {
    .ant-form-item {
      margin-bottom: 8px;
    }
  }
  &.free {
    /deep/ .ant-form-item {
      display: inline-block !important;
      vertical-align: top;
      padding: 0 12px;

      > .ant-form-item-control-wrapper {
        display: inline-block;
      }
    }
  }
  &.use-form-trigger-comp {
    .form-trigger-comp {
      margin-bottom: 0;
    }
  }
  &.icon-combine {
    // 输入框/选择框内前缀icon
    /deep/ .anticon.comp-icon {
      position: absolute;
      z-index: 1;
      top: 4px;
      left: 8px;
      font-size: 13px;
      color: rgba(0, 0, 0, 0.25);
    }
    /deep/ .ant-select-selection__rendered {
      margin-left: 28px;
      input {
        padding-left: 0;
      }
    }
    /deep/.ant-calendar-picker-input {
      padding-left: 28px;
      input {
        padding-left: 0;
      }
    }
    /deep/ input {
      padding-left: 28px;
    }
  }
}
</style>
