export default function (ctx) {
  const statusColor = {
    待评审: '#FC925E',
    评审中: '#4F96FB',
    已通过: '#6EC84A',
    未通过: '#F9676B',
    未提交: '#AFADAD'
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sorter: true,
      width: 100
    },
    {
      title: '任务名称',
      dataIndex: 'task_name',
      key: 'task_name',
      scopedSlots: { customRender: 'task_name' },
      width: 150
    },
    {
      title: '发起人',
      dataIndex: 'created_by',
      key: 'created_by',
      scopedSlots: { customRender: 'created_by' },
      width: 180
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '效率云/CQ单数量',
      dataIndex: 'record_count',
      key: 'record_count',
      width: 180
    },
    {
      title: '任务开关',
      dataIndex: 'status',
      key: 'status',
      width: 180,
      customRender: (text, record) => {
        return text == 1 ? '开启' : '关闭'
      }
    },
    // {
    //   title: '审核进展',
    //   dataIndex: 'review_progress',
    //   key: 'review_progress',
    //   width: 180
    // },
    {
      title: '上次审核时间',
      dataIndex: 'last_review_time',
      key: 'last_review_time',
      width: 180
    },
    {
      title: '下次审核时间',
      dataIndex: 'next_review_time',
      key: 'next_review_time',
      width: 180
    },
    {
      title: '任务来源',
      dataIndex: 'source_display',
      key: 'source_display',
      scopedSlots: { customRender: 'source_display' },
      width: 150
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 180,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = [
    {
      type: 'Input',
      label: '任务名称',
      key: 'task_name',
      mainSearch: true,
      props: {
        placeholder: '搜索任务名称'
      }
    },
    {
      type: 'Input',
      label: '任务来源',
      key: 'source'
    },
    {
      type: 'Input',
      label: '发起人',
      key: 'created_by'
    },
    {
      type: 'Select',
      label: '任务开关',
      key: 'status',
      props: {
        options: [
          {
            label: '开启',
            value: '1'
          },
          {
            label: '关闭',
            value: '0'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '审核进展',
      key: 'review_progress',
      props: {
        options: [
          {
            // label: '进行中',
            label: '队列中',
            value: '0'
          },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '未通过',
            value: '-1,2'
          },
          {
            label: 'Review失败',
            value: '9'
          },
          {
            label: '拉取代码中',
            value: '3'
          },
          {
            label: '代码解析中',
            value: '4'
          },
          {
            label: '代码审核中',
            value: '5'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'AIReview状态',
      key: 'status',
      props: {
        options: [
          {
            // label: '进行中',
            label: '队列中',
            value: '0'
          },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '未通过',
            value: '-1,2'
          },
          {
            label: 'Review失败',
            value: '9'
          },
          {
            label: '拉取代码中',
            value: '3'
          },
          {
            label: '代码解析中',
            value: '4'
          },
          {
            label: '代码审核中',
            value: '5'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'DBA评审状态',
      key: 'dba_status',
      props: {
        options: [
          {
            label: '未提交',
            value: '0'
          },
          {
            label: '待评审',
            value: '1'
          },
          {
            label: '评审中',
            value: '2'
          },
          {
            label: '已通过',
            value: '3'
          },
          {
            label: '未通过',
            value: '4'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'DBA负责人',
      key: 'dba',
      sourceKey: 'operator_dba',
      props: {
        url: '/sqlreview/review/select-value/',
        reqParams: {
          which: 'dba'
        },
        allowSearch: true,
        backSearch: true,
        limit: 50
      }
    }
  ];
  return {
    columns,
    statusColor,
    searchFields: fields
  };
}
