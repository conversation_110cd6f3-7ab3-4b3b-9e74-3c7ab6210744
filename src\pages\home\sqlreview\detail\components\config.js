export default function (ctx) {
  const columns = [
    {
      title: '排序',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      scopedSlots: { customRender: 'draggable' }
    },
    {
      title: '数据源',
      dataIndex: 'datasource_id',
      key: 'datasource_id',
      width: 200,
      scopedSlots: { customRender: 'datasource_id' }
    },
    {
      title: 'schema',
      dataIndex: 'schema',
      key: 'schema',
      width: 300,
      scopedSlots: { customRender: 'schema' }
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      scopedSlots: { customRender: 'action' }
    }
  ]

  const editConfig = (projectId) => {
    return {
      datasource_id: (row, record = {}) => {
        return {
          type: 'DataBaseChoose',
          props: {
            url: '/sqlreview/project/data_source_choices',
            reqParams: {
              project_id: projectId,
              restrict: 'restrict'
            },
            loaded(data) {
              ctx.dataSourceOption = data;
            },
            beforeLoaded(data) {
              return data.map((item) => {
                return {
                  ...item,
                  instance_usage: item.env,
                  showText: item.label + '(' + item.db_url + ')'
                };
              });
            },
            mode: 'default',
            optionLabelProp: 'children',
            // isClear: false,
            // needConfirm: false,
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          rules: [{ required: true, message: '该项为必填项' }],
          listeners: {
            change: (value) => {
              const { table } = ctx.$refs
              if (value) {
                const env = ctx.dataSourceOption.filter(
                  (item) => item.value === value
                )[0].env;
                table.saving({
                  id: record.id,
                  datasource_id: value,
                  env: env,
                  schema: null
                });
              } else {
                table.saving({
                  id: record.id,
                  datasource_id: null,
                  env: '--',
                  schema: null
                });
              }
            }
          }
        };
      },
      schema: (row, record = {}) => {
        return {
          type: 'Select',
          props: {
            mode: 'multiple',
            url: '/sqlreview/project/get_schema_list',
            reqParams: { datasource_id: record.datasource_id || '' },
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          rules: [{ required: true, message: '该项为必填项' }]
        };
      }
    }
  }

  return {
    columns,
    editConfig
  };
}