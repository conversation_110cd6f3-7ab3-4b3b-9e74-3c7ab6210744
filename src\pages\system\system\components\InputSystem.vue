<template>
  <div class="config-system-input">
    <div class="csi-input" v-for="item in inputValue" :key="item.id">
      <span class="element-label">{{ item.element_label }}</span>
      <span>
        <a-input-number
          v-if="compType == 'inputNumber'"
          v-model="item.element_name"
          :max="max"
          :min="1"
        />
        <a-input v-else v-model="item.element_name"></a-input>
      </span>
      <span class="unit" v-if="item.element_value">{{
        item.element_value
      }}</span>
      <span v-if="item.element_desc" class="desc">{{
        '(' + item.element_desc + ')'
      }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: Object
  },
  data() {
    return {
      inputValue: null,
      id: null,
      compType: null,
      max: null
    };
  },
  watch: {
    data: {
      handler(val) {
        this.compType = val.form_type;
        this.max = val.max;
        this.inputValue = val.sql_review_dynamic_form_element;
        this.id = val.sql_review_dynamic_form_element
          .map(item => item.id)
          .join();
      },
      immediate: true
    }
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    // getLabel(value) {
    //   return value === '3' ? '高档：' : value === '2' ? '中档：' : '低档：'
    // }
  }
};
</script>

<style lang="less" scoped>
.config-system-input {
  margin-top: 20px;
  margin-left: 20px;
  .csi-input {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .element-label {
      width: auto;
      width: 120px;
    }
    input {
      width: 300px
    }
    .unit {
      margin: 0 8px;
    }
    .desc {
      color: #a1a1aa;
    }
  }
}
</style>
