<template>
  <CustomIframe :url="url" />
</template>

<script>
import CustomIframe from '@/components/Iframe';
export default {
  components: { CustomIframe },
  props: {},
  data() {
    return {};
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  computed: {
    project() {
      return this.$store.state.project;
    },
    account() {
      return this.$store.state.account;
    },
    url() {
      const { topSqlUrl } = this.project;
      const { user } = this.account;
      const queryStr = this.$route.fullPath.split('?')[1] || '';
      // console.log(queryStr)
      return `${topSqlUrl}?umNo=${user.name}&platform=SQLReview&${queryStr}`;
    }
  },
  methods: {}
};
</script>

<style lang="less" scoped></style>
