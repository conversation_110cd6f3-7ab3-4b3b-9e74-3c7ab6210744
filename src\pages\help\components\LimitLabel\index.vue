<template>
  <div>
    <a-divider>优先级，由低到高</a-divider>
    <div>
      <h4 style="padding-bottom: 8px">:limit="30"</h4>
      <LimitLabel :label="text || ''" :limit="30" format="sql"></LimitLabel>
    </div>
    <a-divider />
    <div>
      <h4 style="padding-bottom: 8px">:limitLine="2"</h4>
      <LimitLabel :label="textFormat || ''" :limitLine="2" format="sql"></LimitLabel>
    </div>
    <a-divider />
    <div>
      <h4 style="padding-bottom: 8px">:limit="30" :nowrap="true"【会自动将换行替换为空格，可让宽度对齐】</h4>
      <LimitLabel :limit="30" :label="text || '--'" :nowrap="true" format="sql"></LimitLabel>
    </div>
    <a-divider />
    <div>
      <h4 style="padding-bottom: 8px">mode="ellipsis"</h4>
      <h5>1、外面能限制宽度，如flex盒子</h5>
      <div style="display: flex;">
        <LimitLabel
          mode="ellipsis"
          :label="text || '--'"
          format="sql"
          :popoverProps="{ placement:`topLeft`}"
        ></LimitLabel>
      </div>
      <div style="display: flex;">
        <LimitLabel
          mode="ellipsis"
          :label="text || '--'"
          format="sql"
          :popoverProps="{ placement:`topLeft`}"
        ></LimitLabel>
        <div style="flex-shrink: 0;background: #008adc33;">****站位****</div>
      </div>
      <div style="display: flex;">
        <div style="flex-grow: 1;display: flex;flex-direction: column;overflow: hidden;text-overflow: ellipsis;">
          <LimitLabel
            mode="ellipsis"
            :label="text || '--'"
            format="sql"
            :popoverProps="{ placement:`topLeft`}"
          ></LimitLabel>
          <div>哈哈哈</div>
        </div>
        <div style="flex-shrink: 0;background: #008adc33;">****站位****</div>
      </div>
      <h5 style="margin-top: 8px;">2、外部不固定，需要设置:block="true"</h5>
      <div>
        <LimitLabel
          mode="ellipsis"
          :block="true"
          :label="text || '--'"
          format="sql"
          :popoverProps="{ placement:`topLeft`}"
        ></LimitLabel>
      </div>
      <h5 style="margin-top: 8px;">3、表格列宽度不固定，表格列设置ellipsis，无需block</h5>
      <div>
        <pre style="background: #e8e8e8;padding: 8px;">{{code}}</pre>
        <!-- <LimitLabel
          mode="ellipsis"
          :label="text || '--'"
          format="sql"
          :popoverProps="{ placement:`topLeft`}"
        ></LimitLabel>-->
      </div>
    </div>
  </div>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';

export default {
  components: {
    LimitLabel
  },
  props: {},
  data() {
    return {
      text:
        'SELECT x."ID",x."DATE_",x."DATETIME_",x."INTEGER_",x."TINYINT_",x."SMALLINT_",x."MEDIUMINT_",x."BIGINT_",x."FLOAT_",x."DOUBLE_",x."DECIMAL_",x."NUMERIC_",x."CHAR_",x."VARCHAR_",x."BINARY_",x."VARBINARY_",x."BLOB_",x."TEXT_",x."JSON_"FROM"ZXSTEST"."T_TIDB_TYPES"x',
      textFormat:
        'SELECT x."ID"\nx."DATE_",x."DATETIME_"\nx."INTEGER_"\nx."TINYINT_",x."SMALLINT_"\nx."MEDIUMINT_",x."BIGINT_",x."FLOAT_",x."DOUBLE_"\nx."DECIMAL_",x."NUMERIC_",x."CHAR_",x."VARCHAR_"\nx."BINARY_",x."VARBINARY_"\nx."BLOB_",x."TEXT_",x."JSON_"FROM"ZXSTEST"."T_TIDB_TYPES"x',
      code: `<LimitLabel mode="ellipsis" :label="text || '--'" format="sql" :popoverProps="{ placement:'topLeft'}"></LimitLabel>`
    };
  },
  computed: {},
  mounted() {},
  beforeDestroy() {},
  methods: {}
};
</script>

<style lang="less" scoped>
</style>