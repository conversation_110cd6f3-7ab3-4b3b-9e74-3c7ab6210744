<template>
  <PageList :mode="'tabs'">
    <div class="quick-audit-history">
      <div class="content-tabs">
        <a-tabs default-active-key="initiatedByMe" :animated="false" @change="tabChange">
          <a-tab-pane key="initiatedByMe">
            <InitiatedByMe ref="initiatedByMe"></InitiatedByMe>
            <span slot="tab">
              <span class="head-example">我发起的</span>
            </span>
          </a-tab-pane>
          <a-tab-pane key="allRecords">
            <AllRecords ref="allRecords"></AllRecords>
            <span slot="tab">
              <span class="head-example">全部记录</span>
            </span>
          </a-tab-pane>
        </a-tabs>
        <div class="frame-button-wrapper">
          <a-button @click="back" class="highlight">返回</a-button>
        </div>
      </div>
    </div>
  </PageList>
</template>
<script>
import InitiatedByMe from './InitiatedByMe';
import AllRecords from './AllRecords';
import PageList from '@/components/PageListNew/SwitchTable/index.vue';
import config from './config';

export default {
  components: { InitiatedByMe, AllRecords, PageList },
  props: {},
  data() {
    this.config = config(this);
    return {
      activeKey: 'initiatedByMe' // 默认选中 tab
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    // 切换 tab 选项卡
    tabChange(activeKey) {
      this.activeKey = activeKey;
    },
    back() {
      this.$router.push({ name: 'quickAudit' });
    }
  }
};
</script>

<style lang="less" scoped>
.quick-audit-history {
  /deep/.ant-tabs-bar {
    padding-bottom: 8px;
    .ant-tabs-nav-container {
      .ant-tabs-nav-scroll {
        .ant-tabs-nav {
          > div {
            .ant-tabs-tab {
              height: 40px !important;
              line-height: 0 !important;
              > span {
                > span {
                  font-family: PingFangSC-Regular;
                  font-size: 16px;
                  color: #27272a;
                  font-weight: 400;
                  margin-bottom: 12px;
                }
              }
              &.ant-tabs-tab-active {
                > span {
                  > span {
                    color: #008adc;
                    font-weight: 600;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  /deep/.ant-tabs .custom-table .search-area-wrapper {
    top: -12px !important;
    &.no-search-area {
      .custom-table-tools {
        margin-top: 0px;
      }
    }
  }
}
</style>
