<template>
  <Form ref="form" v-bind="params" :formData="formData"></Form>
</template>
<script>
import Form from '@/components/Form';
import config from './config';

export default {
  components: { Form },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    this.config = config(this);
    return {
      params: {
        fields: this.config.authFields,
        multiCols: 2
      },
      formData: {}
    };
  },
  created() {},
  mounted() {},
  methods: {
    show() {},
    close() {},
    getData() {
      return this.$refs.form.getData();
    },
    validate() {
      this.$refs.form.validate();
    }
  },
  watch: {
    data: {
      handler(val) {
        this.formData = val;
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.form {
  display: flex;
  justify-content: space-between;
}
.footer {
  text-align: right;
  margin-top: 10px;
}
</style>