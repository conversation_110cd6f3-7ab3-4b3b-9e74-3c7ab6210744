<template>
  <div class="psc-right-query-node-info">
    <Tabs v-model="activeKey" :tabsList="tabsList" mode="tag" @change="onChange">
      <template v-for="item in tabsList">
        <Table :key="item.key" :slot="item.key" v-bind="item">
          <LimitLabel
            :label="text"
            :limitLine="3"
            :popoverProps="{ overlayClassName: 'psc-rqni-limit-label-popover' }"
            slot="trigger_body"
            slot-scope="{ text }"
          />
        </Table>
      </template>
    </Tabs>
  </div>
</template>

<script>
import {
  getTableColumnList,
  getTableTrigger,
  getTableIndex
} from '@/api/sqlresolver';
import Tabs from '@/components/Tabs';
import LimitLabel from '@/components/LimitLabel';
import Table from './Table';
import config from './config';

export default {
  components: { Tabs, Table, LimitLabel },
  inject: ['instanceItem'],
  props: {
    node: Object
  },
  data() {
    this.config = config(this);
    const node = this.node || {};
    return {
      activeKey: 'field',
      tabsList: [
        {
          tab: '字段',
          key: 'field',
          columns: this.config.fieldColumns,
          reqInstance: getTableColumnList,
          reqParams: {
            instance_id: _.get(this.instanceItem, 'value'),
            // element_type: 'column',
            schema_name: node.schema_name,
            // schema_id: node.key,
            table_id: node.table_id,
            table_name: node.table_name
          }
        },
        {
          tab: '索引',
          key: 'index',
          columns: this.config.indexColumns,
          reqInstance: getTableIndex,
          reqParams: {
            instance_id: _.get(this.instanceItem, 'value'),
            // element_type: 'column',
            schema_name: node.schema_name,
            // schema_id: node.key,
            // table_id: node.table_id
            table_name: node.table_name
          },
          visible: ['ORACLE', 'MYSQL'].includes(
            _.get(this.instanceItem, 'type')
          )
        },
        {
          tab: '触发器',
          key: 'trigger',
          columns: this.config.triggerColumns,
          reqInstance: getTableTrigger,
          reqParams: {
            instance_id: _.get(this.instanceItem, 'value'),
            // element_type: 'column',
            schema_name: node.schema_name,
            // schema_id: node.key,
            // table_id: node.table_id
            table_name: node.table_name
          },
          visible: _.get(this.instanceItem, 'type') === 'ORACLE'
        }
      ].filter(item => item.visible !== false)
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    onChange(val) {}
  }
};
</script>

<style lang="less" scoped>
.psc-right-query-node-info {
  padding: 12px;
  height: 100%;
  overflow: auto;
}
</style>
<style lang="less">
.psc-rqni-limit-label-popover {
  .ant-popover-inner-content {
    max-height: 250px;
  }
}
</style>
