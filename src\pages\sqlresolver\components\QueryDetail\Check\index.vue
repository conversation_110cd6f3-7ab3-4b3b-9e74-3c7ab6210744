<template>
  <div class="psc-right-query-detail-check">
    <div class="check-header">
      <div class="check-status">
        <custom-icon :type="getCheckStatus(info).icon" />
        <span>{{getCheckStatus(info).name}}</span>
      </div>
      <LimitLabel mode="ellipsis" :popoverProps="{ placement: 'topLeft' }" :label="info.sql_text"></LimitLabel>
    </div>
    <Table
      ref="table"
      class="table-new-mode-style"
      v-bind="tableParams"
      :dataSource="info.rule_list || []"
      :scroll="scroll"
      bordered
      v-if="getCheckStatus(info).viewStatus !== 'success'"
    >
      <template slot="name" slot-scope="{ text, record }">
        <a v-if="needMark(record)" @click="markCoder(record)">{{text}}</a>
        <span v-else>{{text}}</span>
      </template>
    </Table>
    <div class="sql-plan" v-if="sqlPlan">
      <div>执行计划:</div>
      <pre v-html="sqlPlan"></pre>
    </div>
  </div>
</template>

<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import config from './config';

export default {
  components: { Table, LimitLabel },
  props: {
    info: {
      type: Object,
      default: () => ({})
    },
    resultActiveTab: String,
    resultTab: String,
    resize: Number
  },
  data() {
    this.config = config(this);
    return {
      tableParams: this.getTableParams(),
      dataSource: [],
      scroll: {
        x: 'max-content'
        // y: 'max-content'
      },
      alive: true
    };
  },
  computed: {
    sqlPlan() {
      let { rule_list: ruleList, sql_plan: sqlPlan } = this.info;
      if (!sqlPlan) return;

      let rulePoints = [];
      ruleList.forEach(item => {
        const rp = _.get(item, 'rule_positioning.rule_point') || [];
        rulePoints = [...rulePoints, ...rp];
      });
      rulePoints = [...new Set(rulePoints)];
      // console.log(rulePoints, 'rulePoints');

      rulePoints.forEach(item => {
        sqlPlan = sqlPlan.replace(new RegExp(`[\\s|](${item})[\\s|]`), $0 =>
          $0.replace(
            item,
            `<span style="color:red;font-weight:bold;">${item}</span>`
          )
        );
      });
      // console.log(sqlPlan, 'sqlPlan');
      return sqlPlan;
    }
  },
  mounted() {},
  methods: {
    getTableParams(info) {
      const res = {
        size: 'small',
        columns: this.config.columns,
        pagination: false
      };
      return res;
    },
    getCheckStatus(item) {
      if (!item) return;
      let map = {
        0: '未知',
        1: '通过',
        2: '白名单通过',
        9: '错误',
        '-1': '不通过'
      };
      const status = item.ai_status;
      let icon;
      let viewStatus;
      if (status == 0) {
        icon = 'lu-icon-warning';
        viewStatus = 'warning';
      } else if ([1, 2].includes(status)) {
        icon = 'lu-icon-success';
        viewStatus = 'success';
      } else {
        icon = 'lu-icon-error';
        viewStatus = 'error';
      }
      return {
        icon,
        viewStatus,
        name: map[status]
      };
    },
    needMark(record = {}) {
      const markPos = _.get(record, 'rule_positioning.positioning_point') || [];
      return markPos.length > 0;
    },
    markCoder(record = {}) {
      const markPos = _.get(record, 'rule_positioning.positioning_point') || [];
      this.$emit('markCoder', markPos);
    }
  },
  watch: {
    info(newVal = {}) {}
  }
};
</script>

<style lang="less" scoped>
.psc-right-query-detail-check {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  padding: 12px 0;
  overflow: auto;

  .check-header {
    display: flex;
    align-items: center;
    .check-status {
      font-size: 15px;
      .font-bold();
      flex-shrink: 0;
      margin-right: 8px;
    }
  }

  /deep/ .table-new-mode-style {
    margin-top: 12px;
    th,
    td {
      font-size: 12px !important;
    }

    .ant-table-content {
      border-right: none !important;
    }
  }

  .sql-plan {
    font-size: 12px;
    margin-top: 12px;
  }
}
</style>
