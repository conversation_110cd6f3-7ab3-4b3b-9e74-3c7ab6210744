<template>
  <div class="container">
    <a-spin class="spin" :spinning="dbaReviewLoading">
      <a-card class="common-pure-card box-card" :bordered="false">
        <div class="title">DBA review统计</div>
        <div class="rule-data">
          <Chart :option="dbaReviewOption" />
          <Table
            ref="table"
            v-bind="tableParams || {}"
            :data-source="dbaDataSource"
            :rowKey="
                () => {
                  return Math.random();
                }
              "
          ></Table>
        </div>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import Chart from '@/components/Chart';
import Table from '@/components/Table';
import config from './config';
export default {
  components: { Chart, Table },
  props: {
    dbaReviewLoading: {
      type: Boolean,
      default: false
    },
    dbaReviewOption: {
      default: () => {}
    },
    dbaDataSource: {
      type: Array,
      default: () => []
    }
  },
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: '',
        columns: this.config.columns,
        isInitReq: true,
        needCache: true,
        needTools: false,
        needSearchArea: false,
        scroll: { x: 400, y: 220 },
        pagination: false
      }
    };
  }
};
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  .title {
    margin-bottom: 10px;
    font-size: 16px;
    letter-spacing: 0;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
  .rule-data {
    width: 100%;
    height: 280px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .custom-chart {
      // flex: 1;
      width: 50%;
    }
    .custom-table {
      // flex: 1;
      width: 50%;
      // height: 280px;
      // overflow: hidden;
      margin-left: 12px;
    }
  }
}
</style>
