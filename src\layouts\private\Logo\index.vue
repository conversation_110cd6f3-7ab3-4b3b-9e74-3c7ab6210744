<template>
  <div :class="['header-logo', 'logo', backUrl && 'mode-back']">
    <a-button class="btn-back" v-if="backUrl" @click="onBack"
      ><a-icon type="left" />返回 {{ parentFrame }}</a-button
    >
    <img :src="logo" alt v-else />
    <!-- 项目名称 -->
    <!-- <span class="project-name">{{ projectName }}</span> -->
  </div>
</template>

<script>
import Config from '@/utils/config';
export default {
  components: {},
  props: {
    logo: String
  },
  data() {
    return {
      projectName: Config.ProjectName,
      backUrl: CommonUtil.getQueryParams(location.search, 'backUrl'),
      parentFrame: window.CHANNEL_INFO.parentFrame
    };
  },
  mounted() {},
  created() {},
  methods: {
    onBack() {
      location.href = this.backUrl;
    }
  },
  computed: {}
};
</script>

<style lang="less" scoped>
.mode-back {
  .btn-back {
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    border: 0;
    &:hover {
      background: @primary-color;
    }
  }
  .project-name {
    top: 0 !important;
  }
}
</style>
