// import store from '@/store';
import { routesMap } from '@/router/lazy';

// 根据refreshToken接口返回的字段，判断菜单栏展示项 0是不展示 1是展示
const menuIgnore = function (key, store) {
  const stateData = store.state.project || {};
  const judgeMap = {
    // permissionManagement: ['application'],
    sqlToolSwitch: ['sqlresolver'],
    reviewProcedureSwitch: ['home-procedure']
  };

  const judgeMapKeys = Object.keys(judgeMap);

  let flag = false;
  _.forEach(judgeMapKeys, (item) => {
    if (stateData[item] == 0) {
      if (flag) return false;
      flag = (judgeMap[item] && judgeMap[item]).includes(key);
    }
  });
  return flag;
};
const toTree = (list = []) => {
  let obj = {};
  let result = [];
  // 将数组中数据转为键值对结构 (这里的数组和obj会相互引用)
  list.map((el) => {
    obj[el.id] = el;
    const key = el.id.split('_')[1] || '';
    const routeItem = routesMap[key] || {};
    const { icon, desc, jumpUrl, disabled, topGroup } = routeItem.meta || {};
    el.icon = icon;
    el.name = el.source_name || desc;
    el.key = key;
    el.jumpUrl = jumpUrl;
    el.disabled = disabled;
    el.topGroup = topGroup;
    // el.key = el.id;
    // el.source_category = category;
    // el.scopedSlots = { title: 'title' };
    // el.slots = { icon: el.source_type };
    // if (el.parent_id === 0) {
    //   el.parent_id = rootKey;
    // }
  });
  for (let i = 0, len = list.length; i < len; i++) {
    let pid = list[i].parent_id;
    if ((pid + '').startsWith('root_')) {
      result.push(list[i]);
      result.sort((a, b) => a.sort_num - b.sort_num);
      continue;
    }
    if (obj[pid]) {
      if (obj[pid].children) {
        obj[pid].children.push(list[i]);
        obj[pid].children.sort((a, b) => a.sort_num - b.sort_num);
      } else {
        obj[pid].children = [list[i]];
      }
    }
  }
  return result;
};
// 菜单配置
// const style = { fontWeight: 'bold' };
const config = (ctx) => {
  let menu = [];
  let resource = [];
  const auth = ctx.$store.state.auth;
  if (auth && auth.source != null) {
    const menus = _.merge([], auth.menus);
    resource = toTree(menus);
    // menu.forEach((item) => {
    //   const dividerArr = ['data-view', 'home-whitelist'];
    //   if (dividerArr.includes(item.key)) {
    //     item.className = 'has-divider';
    //   }
    // });
    resource.forEach((item, index) => {
      const routeItem = routesMap[item.key];
      const nextRouteItem = routesMap[(resource[index + 1] || {}).key];
      const currGroup = _.get(routeItem || {}, 'meta.group');
      const nextGroup = _.get(nextRouteItem || {}, 'meta.group');
      if (
        currGroup != null &&
        ((nextGroup != null && currGroup != nextGroup) ||
          (nextGroup == null && index < resource.length - 1))
      ) {
        item.className = 'has-divider';
      }
    });
    menu = resource.filter((item) => {
      return ![
        '$menu_system',
        '$menu_user',
        '$menu_rule-conf',
        '$menu_config',
        '$menu_data-source-config'
      ].includes(item.id);
    });
    const configArr = resource
      .filter((item) => {
        return [
          '$menu_system',
          '$menu_user',
          '$menu_rule-conf',
          '$menu_config',
          '$menu_data-source-config'
        ].includes(item.id);
      })
      .map((item) => {
        return item.children;
      });
    const configs = _.flatten(configArr);
    const sourceCode = _.get(ctx.$store.state.common, 'quickEntrance');
    const children = configs.filter((item) => sourceCode.includes(item.id));
    let quickEntrance = {};
    if (children && children.length > 0) {
      quickEntrance = {
        delete_mark: 0,
        icon: 'lu-icon-release-copy',
        id: '$menu_quick-entrance',
        isLeaf: false,
        jumpUrl: undefined,
        key: 'quick-entrance',
        name: '快捷入口',
        parentId: null,
        parent_id: 'root_resource',
        sort_num: 1000,
        source_category: 'resource',
        source_name: '快捷入口',
        source_type: 'menu',
        children: children || []
      };
      menu.push(quickEntrance);
    }
  } else {
    menu = [
      {
        name: 'SQL编辑器',
        key: 'sqlresolver',
        // style: style,
        channel: process.channel == 'TaiLongBank',
        icon: 'lu-icon-sqlresolver',
        visible: ['admin', 'leader', 'developer', 'dba']
      },
      {
        name: '工作台',
        key: 'data-view',
        // style: style,
        className: 'has-divider',
        icon: 'bar-chart',
        visible: ['admin', 'leader', 'developer', 'dba']
      },
      {
        name: '快速审核',
        key: 'quickAudit',
        icon: 'rise',
        // style: style,
        visible: ['admin', 'leader', 'dba', 'developer']
      },
      {
        name: '工单管理',
        key: 'orderList',
        // style: style,
        icon: 'file',
        visible: ['admin', 'dba', 'leader']
      },
      {
        name: '事前审核',
        key: 'home-sqlreview',
        // style: style,
        icon: 'bold',
        visible: ['developer', 'leader', 'admin', 'dba']
      },
      // {
      //   name: '实时审核',
      //   key: 'immediate',
      //   style: style,
      //   icon: 'clock-circle'
      // },
      {
        name: '事后审核',
        key: 'home-postaudit',
        icon: 'font-colors'
        // style: style
      },
      {
        name: '存储过程扫描',
        key: 'home-procedure',
        icon: 'sync'
        // style: style
      },
      {
        name: '申请白名单',
        key: 'home-whitelist',
        icon: 'form',
        // style: style,
        className: 'has-divider'
        // visible: ['leader', 'developer']
      },
      {
        name: '配置管理',
        key: 'config',
        icon: 'setting',
        visible: ['admin', 'dba'],
        children: [
          {
            name: '数据源配置',
            key: 'data-source-config'
          },
          {
            name: '项目配置',
            key: 'project-config'
          },
          // {
          //   name: '规则管理',
          //   key: 'rule-conf',
          //   children: [
          //     {
          //       name: '规则配置',
          //       key: 'rules-config'
          //     },
          //     {
          //       name: '规则集',
          //       key: 'ruleGather-config'
          //     }
          //   ]
          // },
          // {
          //   name: 'DDL规则管理',
          //   key: 'ddlRule-conf',
          //   children: [
          //     {
          //       name: '规则配置',
          //       key: 'ddlRules-config'
          //     },
          //     {
          //       name: '规则集',
          //       key: 'ddlRuleGather-config'
          //     }
          //   ]
          // },
          // {
          //   name: '白名单管理',
          //   key: 'white-list'
          // },
          {
            name: '评审意见配置',
            key: 'comments-config',
            visible: ['admin', 'dba']
          },
          {
            name: '系统配置',
            key: 'system-config',
            visible: ['admin']
          }
          // {
          //   name: 'LDAP认证',
          //   key: 'system-ldap',
          //   visible: ['admin', 'dba']
          // }
        ]
      },
      {
        name: '规则管理',
        key: 'rule-conf',
        icon: 'rule',
        visible: ['admin', 'dba'],
        children: [
          {
            name: '规则配置',
            key: 'rules-config'
          },
          {
            name: '规则集',
            key: 'ruleGather-config'
          }
        ]
      },
      {
        name: '用户管理',
        key: 'user',
        icon: 'user',
        visible: ['admin'],
        children: [
          {
            name: '用户权限管理',
            key: 'userList'
          },
          {
            name: '用户组管理',
            key: 'groupList'
          },
          {
            name: '角色管理',
            key: 'roleList'
          },
          {
            name: '资源配置',
            key: 'resourceEdit'
          }
        ]
      },
      {
        name: '报表统计',
        key: 'report',
        icon: 'pie-chart',
        visible: ['admin', 'dba']
      },
      {
        name: '审计日志',
        key: 'auditLog',
        icon: 'menu-fold',
        visible: ['admin', 'dba']
      },
      {
        name: '我的应用',
        key: 'application',
        icon: 'appstore',
        visible: ['admin', 'leader', 'developer', 'dba']
      }
    ];
  }

  // 初始化处理menu
  let menuMap = {};
  const init = function () {
    // 递归处理
    function deal(parentId, list) {
      list.forEach((item, index) => {
        item.parentId = parentId || null;
        if (item.channel === true) {
          item.visible = false;
          // list.splice(index, 1)
        }
        menuMap[item.key] = item;

        if (item.children && item.children.length > 0) {
          item.isLeaf = false;
          deal(item.key, item.children);
        } else {
          item.isLeaf = true;
        }
      });
    }
    deal(null, menu);
  };
  init();
  return {
    menu,
    menuMap,
    resource,
    menuIgnore,
    useFrontAuth: false
  };
};
export default config;
