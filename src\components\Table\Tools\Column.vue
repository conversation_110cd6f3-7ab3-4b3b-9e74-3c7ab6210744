<template>
  <a-modal
    v-model="visible"
    title="自定义列"
    :width="600"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-tag
      v-for="item in propColumns"
      :key="item.key"
      style="margin-bottom: 8px; cursor: pointer"
      :color="checkedKeys.includes(item.key) ? '#87d068' : '#bfbdbd'"
      @click="onChoose(item)"
      >{{ item.title || item.desc || '--' }}</a-tag
    >
    <div style="margin-top: 8px">
      <a
        style="text-decoration: underline"
        v-if="checkedKeys.length < propColumns.length"
        @click="onChooseAll"
        >还原配置</a
      >
    </div>
  </a-modal>
</template>

<script>
// import common from '@/utils/common'

export default {
  components: {},
  props: {},
  inject: ['columns', 'checkedColumnKeys'],
  data() {
    return {
      visible: false,
      checkedKeys: []
    };
  },
  computed: {
    propColumns() {
      return this.columns();
    },
    propCheckedColumnKeys() {
      return this.checkedColumnKeys();
    }
  },
  created() {},
  mounted() {},
  methods: {
    show(data) {
      this.checkedKeys =
        this.propCheckedColumnKeys ||
        this.propColumns
          .filter(item => item.defaultVisible !== false)
          .map(item => item.key);
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
    onOk() {
      this.$emit('save', this.checkedKeys);
      this.hide();
    },
    onCancel() {
      this.hide();
    },
    onChoose(item) {
      if (this.checkedKeys.includes(item.key)) {
        this.checkedKeys = this.checkedKeys.filter(itm => itm != item.key);
      } else {
        this.checkedKeys = [...this.checkedKeys, item.key];
      }
    },
    onChooseAll() {
      this.checkedKeys = this.propColumns
        .filter(item => item.defaultVisible !== false)
        .map(item => item.key);
    }
  }
};
</script>

<style lang="less" scoped>
</style>
