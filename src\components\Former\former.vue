<!--
 * @Descripttion: 表单工具
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2020-12-03 21:51:54
-->
<template>
  <a-form-model
    ref="form"
    :model="fieldsValue"
    :rules="getValidatorRules()"
    :layout="layout"
    :class="'former-colspan-' + column"
    v-bind="formItemLayout"
  >
    <a-form-model-item
      v-for="field in fields"
      v-bind:key="field.key"
      v-if="!hideControl[field.key]"
      :label="field.name"
      :prop="field.key"
      :class=" 'ant-form-item-colspan-' + (field.colspan || '1')"
    >
      <component
        :is="field.type || 'a-input'"
        v-bind="field.props"
        v-model="fieldsValue[field.key]"
      ></component>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import FSelect from './types/select.vue'
import FRadioGroup from './types/radio.vue'
import FCheckboxGroup from './types/checkbox.vue'

export default {
  components: { ASelect: FSelect, ARadioGroup: FRadioGroup, ACheckboxGroup: FCheckboxGroup },
  name: 'Former',
  props: {
    fields: {
      type: Array,
      required: true,
      default: () => []
    },
    layout: {
      type: String,
      required: false,
      default: () => 'horizontal'
    },
    defaultValue: {
      type: Object,
      required: false,
      default: () => {}
    },
    column: {// 表单列数
      type: Number,
      required: false,
      default: () => 1
    }
  },
  data (vm) {
    let defaultValue = vm.defaultValue || {}
    return {
      fieldsValue: defaultValue,
      // 影藏集合
      hideControl: vm.getHideControl(vm.fields, defaultValue)
    }
  },
  watch: {
    defaultValue: {
      handler (value) {
        this.fieldsValue = value || {}
      },
      deep: true
    },
    fieldsValue: {
      handler (value) {
        this.hideControl = this.getHideControl(this.fields, value)
      },
      deep: true
    }
  },
  computed: {
    formItemLayout () {
      return this.layout === 'horizontal'
        ? {
          labelCol: { span: 6 },
          wrapperCol: { span: 15 }
        } : {}
    }
  },
  methods: {
    getValidatorRules () {
      let valid = {}
      let fieldsValue = this.fieldsValue || {}

      this.fields.map((it) => {
        if (it.validator) {
          let validator = Array.isArray(it.validator) ? it.validator : [it.validator]
          validator = validator.filter(it => {
            // control
            // 通过control控制validator
            let match = true
            if (it.disabledControl) {
              for (let prop in it.control) {
                let value = it.control[prop]
                if (fieldsValue[prop] === value) {
                  return false
                }
              }
            }
            return match
          }).map((_it) => {
            let it = Object.assign({}, _it)
            if (!it.trigger) {
              it.trigger = 'change'
            }

            if (!it.message) {
              if (typeof it.required !== 'undefined') {
                it.message = '该字段必填'
              } else {
                it.message = '字段验证错误'
              }
            }
            if (it.valueControl) {
              for (let key in it.valueControl) {
                let control = it.valueControl[key]
                if (Array.isArray(control)) {
                  control.forEach(its => {
                    let ctr = its.control
                    for (let c in ctr) {
                      // 反向
                      if (its.reverse) {
                        
                        if (ctr[c] !== fieldsValue[c]) {
                          it[key] = its.value
                        }
                      } else {
                        if (ctr[c] === fieldsValue[c]) {
                          it[key] = its.value
                        }
                      }
                    }
                  })
                }
              }
            }
            return it
          })
          valid[it.key] = validator
        }
      })

      return valid
    },
    getHideControl (fields, value) {
      let hideControl = {}
      let cloneValue = Object.assign({}, value)
      fields.forEach((it) => {
        if (Array.isArray(it.when) && it.when.length === 2) {
          let where = this.makeArray(it.when[0])
          let control = this.makeArray(it.when[1])
          let defValue = cloneValue[it.key]
          // 值不匹配
          if (!this.matchValue(where, defValue)) {
            control.forEach((it) => {
              hideControl[it] = true
              delete cloneValue[it]
            })
          }
        }
      })
      return hideControl
    },
    matchValue (where, value) {
      if (Array.isArray(value)) {
        return value.some((it) => {
          return this.matchValue(where, it)
        })
      } else {
        return where.indexOf(value) > -1
      }
    },
    makeArray (obj) {
      if (!Array.isArray(obj)) {
        return obj ? [obj] : []
      }
      return obj
    },
    validation (callback) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          callback(this.fieldsValue)
        }
      })
    },
    resetFields () {
      this.$refs.form.resetFields()
    },
    getFieldsValue () {
      return this.fieldsValue
    }
  }
}
</script>
<style>
.former-colspan-2 .ant-form-item-colspan-1 {
    display:inline-block;
    box-sizing: border-box;
    width: 50%;
    padding-left:8px;
    padding-right: 8px;
}

.former-colspan-2 .ant-form-item-colspan-2 {
  width: 100%;
  padding-left:8px;
  padding-right: 8px;
}
</style>
