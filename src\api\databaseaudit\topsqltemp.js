import Http from '@/utils/request';
// 实例名称
export function get_datasource_list(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-datasource-list`,
    method: 'get',
    params: params
  });
}

// /sqlreview/after_audit/topsql-new/select-source-type
// 采集方式
export function select_source_type(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/select-source-type`,
    method: 'get',
    params: params
  });
}

// 执行数据
export function get_executive_data(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-executive-data`,
    method: 'post',
    data: params
  });
}

// 获取表头

export function get_header_columns(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-header-columns`,
    method: 'get',
    params: params
  });
}

// 获取汇总数据
export function get_summary_data_list(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-summary-data-list`,
    method: 'post',
    data: params
  });
}




// 获取echarts
export function get_overview_chart(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-overview-chart`,
    method: 'post',
    data: params
  });
}

// 申请添加标签
export function save_top_sql_label(params) {
  return Http({
    url: `/sqlreview/after_audit/save_top_sql_label`,
    method: 'post',
    data: params
  });
}

// 趋势

export function get_datasource(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-datasource`,
    method: 'get',
    params: params
  });
}

export function get_tendency_metrics_chart(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-tendency-metrics-chart`,
    method: 'post',
    data: params
  });
}


// /sqlreview/after_audit/topsql-new/get-detail-data-list
export function get_detail_data_list(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-detail-data-list`,
    method: 'post',
    data: params
  });
}
export function get_analysis_meta_info(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-analysis-meta-real`,
    method: 'post',
    data: params
  });
}
export function get_analysis_review_detail(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-analysis-review-detail`,
    method: 'get',
    params: params
  });
}
export function save_label(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/save-label`,
    method: 'post',
    data: params
  });
}



export function export_excel(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/export-excel`,
    method: 'post',
    responseType: 'blob',
    data: params
  });
}


export function analysis_explain(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/get-analysis-explain`,
    method: 'post',
    data: params
  });
}


export function add_header_columns(params) {
  return Http({
    url: `/sqlreview/after_audit/topsql-new/add-header-columns`,
    method: 'post',
    data: params
  });
}

export default {};
