<template>
  <div :class="['page-list-switch-wrapper', mode]">
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    mode: String // tabs | single
  },
  components: {},
  data() {
    return {};
  },
  computed: {},
  mounted() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.page-list-switch-wrapper {
  background: #fff;
  border-radius: 16px;
  &.tabs {
    padding: 32px 0 0 0;
    /deep/.ant-tabs {
      overflow: visible;
      .ant-badge-count {
        right: -15px;
        top: -6px;
      }
      > .ant-tabs-bar {
        margin: 0;
        > .ant-tabs-nav-container {
          height: 32px;
          overflow: visible;
          position: relative;
          > .ant-tabs-nav-wrap {
            overflow: visible;
            > .ant-tabs-nav-scroll {
              overflow: visible;
              > .ant-tabs-nav {
                > div {
                  > .ant-tabs-tab-active {
                    height: 32px;
                    line-height: 32px;
                  }
                  > .ant-tabs-tab {
                    height: 32px;
                    line-height: 32px;
                    margin-right: 16px;
                    font-family: PingFangSC-Regular;
                    color: #27272a;
                    text-align: center;
                    font-weight: 400;
                    .ant-badge {
                      .head-example {
                        font-size: 16px;
                      }
                    }
                    &.ant-tabs-tab-active {
                      color: #008adc;
                    }
                    &:first-child {
                      margin-left: 32px;
                    }
                    &:hover {
                      color: #008adc;
                    }
                  }
                }
              }
            }
          }
        }
      }
      .custom-table {
        position: static;
        &.need-search-area {
          margin-top: 0;
        }
        .search-area-wrapper {
          // width: 400px;
          padding: 0 32px;
          position: absolute;
          top: -20px;
          right: 0;
          justify-content: flex-end !important;
          align-items: center;
          .custom-table-top-left {
            margin-right: 8px;
            height: 54px;
            .search-area-simple {
              .ant-input-search {
                .ant-input {
                  height: 29px;
                  background: #ffffff;
                  border: 1px solid rgba(161, 161, 161, 0.5);
                }
              }
            }
          }
          .custom-table-top-right {
            .custom-table-tools {
              padding-bottom: 2px;
            }
          }
        }
      }
    }
  }

  &.single {
    /deep/.custom-table {
      // padding: 0 32px;
      .search-area-wrapper {
        padding: 24px 24px 0 24px;
        border-bottom: 1px solid #eee;
        border-color: #f4f4f5;
        align-items: center;
        .custom-table-top-right {
          display: flex;
          align-items: center;
          .ant-divider {
            width: 0.86px;
            height: 20px;
            background: #e0e0e0;
          }
        }
      }
    }
  }
}
</style>