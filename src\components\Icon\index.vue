<template>
  <i
    class="custom-icon anticon"
    v-on="iconListeners"
    v-if="showType.startsWith('lu-icon') && mode === 'svg'"
  >
    <svg class="custom-icon-svg" aria-hidden="true" :style="svgStyle">
      <use :xlink:href="`#${showType}`" />
    </svg>
  </i>
  <span
    :class="['custom-icon', 'luicon', showType]"
    v-on="iconListeners"
    v-else-if="showType.startsWith('lu-icon')"
  ></span>
  <a-icon
    :class="['custom-icon']"
    :type="showType"
    v-bind="iconAttrs"
    v-on="iconListeners"
    v-else-if="showType"
  />
</template>

<script>
export default {
  components: {},
  props: {
    mode: {
      type: String,
      default: 'svg' // class | svg
    },
    svgStyle: {
      type: Object,
      default: () => ({})
    },
    type: String
  },
  data() {
    return {};
  },
  computed: {
    showType() {
      const { type = '' } = this;
      return type;
    },
    iconAttrs() {
      return { ...this.$attrs };
    },
    iconListeners() {
      return { ...this.$listeners };
    }
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.custom-icon-svg {
  width: 1em;
  height: 1em;
  // vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
</style>
