<template>
  <!-- <a-form :form="form">
  <a-form-item>-->
  <j-cron
    ref="innerVueCron"
    v-decorator="['cronExpression', {rules: [{ required: true, message: '请输入cron表达式!' }]}]"
    @change="setCorn"
    :value="value"
  ></j-cron>
  <!-- </a-form-item>
  </a-form>-->
</template>

<script>
import JCron from './JCron.vue';
import { pick } from 'lodash';

export default {
  name: 'QuartzJobModal',
  props: {
    value: {
      required: false,
      type: String,
      default: () => {
        return '';
      }
    }
  },
  components: { JCron },
  data() {
    return {
      model: {},
      cron: {
        label: '',
        value: ''
      },
      form: this.$form.createForm(this),
      validatorRules: {
        cron: {
          rules: [
            {
              required: true,
              message: '请输入cron表达式!'
            }
          ]
        }
      }
    };
  },
  watch: {
    value(val) {
      if (val) {
        this.edit(val);
      }
    }
  },
  methods: {
    add() {
      this.edit({});
    },
    edit(record) {
      this.model = Object.assign({}, record);
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'jobClassName',
            'cronExpression',
            'parameter',
            'description',
            'status'
          )
        );
      });
    },
    setCorn(data) {
      // console.log(data);
      this.$nextTick(() => {
        this.model.cronExpression = data;
        this.$emit('cronExpression', data);
      });
    },
    validateCron(rule, value, callback) {
      if (!value) {
        callback();
      } else if (Object.keys(value).length == 0) {
        // callback('请输入cron表达式!');
        console.log('请输入cron表达式!');
      }
    }
  }
};
</script>
<style scoped>
</style>