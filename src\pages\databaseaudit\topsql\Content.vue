<template>
  <div class="database-audit-content">
    <!-- 搜索内容 -->
    <SearchArea
      v-bind="searchParams || {}"
      @reset="reset"
      @search="search"
      ref="search"
      :searchData="searchData || {}"
    ></SearchArea>
    <div class="table-content">
      <div class="top-info">
        <div class="left">
          <!-- <div v-if="dbType == 'MYSQL' && afterAuditStatus == 1"> -->
          <div>
            <span class="highligh">当前采集模式：</span>
            <span class="minor">{{ collectModel || '未开启' }}</span>
          </div>
          <div v-if="collectTime" class="next-collect">
            <span>上次采集： {{ collectTime }}</span>
            <a-popover overlayClassName="top-sql-collect-detail-info">
              <template slot="content">
                <div>
                  <span>采集频率：</span>
                  <span>{{ collectFrequency }}</span>
                </div>
                <div v-if="nextCollectTime">
                  <span>下次采集：</span>
                  <span>{{ nextCollectTime }}</span>
                </div>
              </template>
              <custom-icon type="question-circle"></custom-icon>
            </a-popover>
          </div>
          <a
            @click="onCollect"
            v-if="
              dbType == 'ORACLE' &&
              afterAuditStatus == 1 &&
              $permission.topSql('collect')
            "
            >立即采集</a
          >
        </div>

        <div class="right">
          <span class="unit-box">
            <a-radio-group v-model="unit" @change="changeUnit">
              <a-radio-button value="s">秒(s)</a-radio-button>
              <a-radio-button value="ms">毫秒(ms)</a-radio-button>
            </a-radio-group>
          </span>
          <a-select
            :value="displayType"
            style="width: 120px"
            @change="handleMenuChange"
          >
            <a-select-option :key="0">明细</a-select-option>
            <a-select-option :key="1">汇总</a-select-option>
            <a-select-option
              :key="2"
              v-if="
                ['ORACLE', 'MYSQL', 'GAUSSDB', 'DB2', 'GOLDENDB'].includes(
                  dbType
                )
              "
              >图表</a-select-option
            >
          </a-select>
          <a-button
            type="primary"
            @click="audit"
            class="right-btn"
            v-if="
              !['DB2', 'GAUSSDB'].includes(dbType) &&
              displayType == '明细' &&
              $permission.topSql('aiAudit')
            "
            >AI审核</a-button
          >
          <!-- <a-tooltip v-if="canDo">
            <template slot="title">数据库审核配置</template>
            <custom-icon
              type="setting"
              @click="databaseEdit"
              class="right-btn"
            />
          </a-tooltip> -->
          <a-tooltip
            v-if="displayType !== '图表' && $permission.topSql('export')"
          >
            <template slot="title">导出</template>
            <custom-icon
              type="cloud-download"
              @click="download"
              class="right-btn"
            />
          </a-tooltip>
          <a-tooltip>
            <template slot="title">表头设置</template>
            <custom-icon
              type="control"
              @click="customSetColumn"
              class="right-btn"
            ></custom-icon>
          </a-tooltip>
          <a-tooltip>
            <template slot="title"> 打标 </template>
            <custom-icon type="tags" class="right-btn" @click="batchAddTag" />
          </a-tooltip>
        </div>
      </div>
      <Table
        ref="table"
        v-bind="tableParams"
        :dataSource="dataSource || []"
        class="new-view-table small-size"
        @selectChange="selectChange"
        :rowSelection="rowSelection"
        v-if="model == 'table'"
      >
        <template slot="label_attribute" slot-scope="{ record, text }">
          <div v-if="record.label_obj_id">
            <LabelCard
              ref="labelCard"
              :id="record.label_obj_id"
              :text="text"
              :labelStatus="record.label_status"
              @refresh="reset"
            />
          </div>
          <span v-else>{{ '--' }}</span>
        </template>
        <LimitLabel
          slot="sql_id"
          slot-scope="{ text }"
          :label="text"
          :limit="24"
          :needCopy="true"
        ></LimitLabel>
        <LimitLabel
          slot="sql_text"
          slot-scope="{ text }"
          :label="text"
          :limit="24"
          :needCopy="true"
          :nowrap="true"
          format="sql"
        ></LimitLabel>
        <template slot="risk" slot-scope="{ record, text }">
          <RiskLevel :aiComment="record.ai_comment || {}" :value="text" />
        </template>
        <!-- <template slot="ai_status" slot-scope="{ record, text }">
          <RiskLevel :aiComment="record.ai_comment || {}" :value="text" />
        </template> -->
        <template slot="plan_hash_value" slot-scope="{ text, record }">
          <a @click="onSqlException(record, 'plan_hash_value')">{{ text }}</a>
        </template>

        <!-- gaussdb -->
        <div
          slot="query_plan"
          slot-scope="{ text, record }"
          class="query-plan-box"
        >
          <a @click="onSqlException(record, 'query_plan')"
            ><LimitLabel
              :label="text || ''"
              :limit="24"
              :nowrap="true"
            ></LimitLabel
          ></a>
        </div>
        <custom-btns-wrapper slot="action" slot-scope="{ record }">
          <a
            actionBtn
            @click="onSqlException(record)"
            v-if="dbType !== 'DB2' && $permission.topSql('explain')"
            >Explain</a
          >
        </custom-btns-wrapper>
      </Table>
      <Chart
        v-else
        ref="chart"
        :id="id"
        :unit="unit"
        :dbType="dbType"
        :searchData="commonSearchData"
      />
    </div>
    <SqlExceptionDrawer ref="sqlException" />
    <EditDrawer ref="editModal" @save="saveCollectConfig"></EditDrawer>
    <CustomSetColumnModal
      ref="customSetColumn"
      :id="id"
      @save="save"
    ></CustomSetColumnModal>
    <!-- 打标弹窗 -->
    <TagModal ref="tag" @saveLabel="onSaveLabel"></TagModal>
  </div>
</template>

<script>
import SearchArea from '@/components/Biz/SearchArea';
import CustomSetColumnModal from '../components/customSetColumnModal';
import EditDrawer from './components/EditDrawer';
import common from '@/utils/common';
import RiskLevel from '@/components/Biz/ReviewDetail/RiskLevel';
import TagModal from '@/components/Biz/ReviewDetail/TagModal';
import LabelCard from '@/components/Biz/LabelCard';
import Table from '@/components/Table';
import Chart from './components/Chart/index';
import LimitLabel from '@/components/LimitLabel';
import SqlExceptionDrawer from '../components/sqlExceptionDrawer';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import moment from 'moment';
import config from './config';
import tableSelectAll from '@/mixins/tableSelectAll';
import {
  saveTableHeaderInfo,
  download,
  afterAudit,
  getTableHeaderInfo,
  getCollectConfig,
  oracleImmediateCollection,
  saveLabel
} from '@/api/databaseaudit/topsql';
export default {
  mixins: [bodyMinWidth(1440), tableSelectAll({})],
  components: {
    CustomSetColumnModal,
    EditDrawer,
    SearchArea,
    RiskLevel,
    Table,
    LimitLabel,
    SqlExceptionDrawer,
    Chart,
    LabelCard,
    TagModal
  },
  props: {
    pane: Object,
    id: Number | String
  },
  data() {
    this.config = config(this);
    return {
      searchParams: {
        fields: this.config
          .searchFields(this.pane.db_type)
          .filter(item => item.visible !== false),
        multiCols: 3
      },
      visible: false,
      displayTypeVisible: false,
      displayType: '明细',
      value: 'openapi',
      searchData: {
        execution_time: [moment().subtract(48, 'hour'), moment()]
      },
      // dataSource: [],
      tableParams: {
        url: '/sqlreview/after_audit/list',
        isInitReq: false, // 图表模式和表格模式切换 需要带顶部搜索参数
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id',
        // rowSelection: {
        //   type: 'checkbox', // 多选单选
        //   columnWidth: '20',
        //   selectedRowKeys: []
        // },
        scroll: { x: 'max-content' },
        loaded: this.onTableLoaded
      },
      // selectedRowKeys: [],
      collectModel: null,
      collectFrequency: null,
      collectTime: null,
      nextCollectTime: null,
      afterAuditStatus: null,
      list: [],
      dbType: '',
      model: 'table',
      commonSearchData: {},
      columns: [],
      unit: 'ms',
      display: 0
    };
  },
  computed: {
    canDo() {
      const user = this.$store.state.account.user || {};
      return ['leader', 'admin', 'dba'].includes(user.role);
    }
  },
  created() {},
  mounted() {
    const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
    const startTime = moment()
      .subtract(48, 'hour')
      .format('YYYY-MM-DD HH:mm:ss');
    this.$set(this.tableParams, 'reqParams', {
      data_source_id: this.id,
      display_type: 0,
      execution_time: [startTime, endTime].toString(),
      unit_type: 'ms'
    });
    this.init();
    this.getCollectConfigFn();
  },
  methods: {
    getCollectConfigFn() {
      getCollectConfig({
        data_source_id: this.id
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            this.collectModel = _.get(resData, 'collect_model');
            this.collectTime = _.get(resData, 'collect_time');
            this.nextCollectTime = _.get(resData, 'next_collect_time');
            this.afterAuditStatus = _.get(resData, 'after_audit_status');
            const collectFrequency = _.get(resData, 'frequency');
            this.dbType == 'ORACLE'
              ? this.handleCron(collectFrequency)
              : (this.collectFrequency = collectFrequency);

            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    init() {
      getTableHeaderInfo({ data_source_id: this.id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data') || {};
            this.list = resData.display_list;
            this.dealTableHeaderInfo(this.list);
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 处理cron时间格式
    handleCron(data) {
      if (data) {
        let labelArr = data.split(' ') || [];
        let time = `${labelArr[2]}` + ':' + `${labelArr[1]}` + ':' + `00`;
        if (labelArr[2] === '*') {
          let time = `${labelArr[1]}` + ':' + `00`;
          this.collectFrequency = '每小时' + ' ' + time;
        } else if (labelArr[3] === '*') {
          this.collectFrequency = '每天' + ' ' + time;
        } else if (labelArr[3] === '?') {
          const weekOption = [
            {
              label: '星期一',
              value: '2',
              cron: 2
            },
            {
              label: '星期二',
              value: '3',
              cron: 3
            },
            {
              label: '星期三',
              value: '4',
              cron: 4
            },
            {
              label: '星期四',
              value: '5',
              cron: 5
            },
            {
              label: '星期五',
              value: '6',
              cron: 6
            },
            {
              label: '星期六',
              value: '7',
              cron: 7
            },
            {
              label: '星期日',
              value: '1',
              cron: 1
            }
          ];
          weekOption.find(val => {
            if (val.cron === Number(labelArr[5])) {
              this.collectFrequency = '每周' + val.label + time;
            }
          });
        }
      }
    },
    // 处理表头数据
    dealTableHeaderInfo(data = []) {
      let columns = [];
      if (this.dbType == 'POSTGRE') {
        data.forEach(item => {
          columns.push({
            title: item.value,
            dataIndex: item.key,
            key: item.key,
            sorter: item.sorter,
            scopedSlots: { customRender: item.key }
          });
        });
      } else {
        data.forEach(item => {
          this.columns.forEach(el => {
            if (item.key && item.key == el.key) {
              columns.push({
                ...el,
                title: item.value,
                sorter: item.sorter,
                width: undefined,
                sortDirections: ['descend', 'ascend'],
                scopedSlots: { customRender: item.key }
              });
            }
          });
        });
      }
      if (!['DB2', 'GAUSSDB'].includes(this.dbType)) {
        columns.push({
          title: '操作',
          key: 'action',
          width: undefined,
          slots: { title: 'customTitle' },
          scopedSlots: { customRender: 'action' },
          fixed: 'right',
          visible: $permissionBatch.some([
            { module: 'topSql', values: ['explain'] }
          ])
        });
      }
      this.$set(
        this.tableParams,
        'columns',
        columns.filter(item => item.visible !== false)
      );
    },
    // 表列表 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    customSetColumn() {
      this.$refs.customSetColumn.show();
    },
    // 执行计划
    onSqlException(record, text) {
      this.$refs.sqlException.show('topsql', record, this.pane, text);
    },
    // ai审核
    audit() {
      const { table } = this.$refs;
      const { searchParams } = table;
      const selectedRowKeys = table.selectedRowKeys;
      if (_.isEmpty(selectedRowKeys) && !this.isSelectAll) {
        this.$message.warn('请选择数据!');
        return;
      }
      const params = {
        data_source_id: this.id,
        examine_list: this.isSelectAll ? this.excludes : this.includes,
        is_select_all: this.isSelectAll ? 1 : 0,
        timestamp: this.timestamp,
        count: this.count,
        ...searchParams
      };
      const auditNum = this.isSelectAll
        ? this.count - this.excludes.length
        : this.includes.length;
      this.$showLoading();
      afterAudit(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            if (auditNum > 1000) {
              this.$notification['warning']({
                message: '提示信息',
                description:
                  _.get(res, 'data.data.message') ||
                  'SQL审核调起成功，数量超出限制，仅审核前1000条!',
                duration: 6
              });
            }
            this.resetAllCheckData();
            table.selectedRowKeys = [];
            table.refreshKeep(null, { _clear: true });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 导出
    download() {
      this.$showLoading({
        tips: `下载中...`
      });
      const { table } = this.$refs;
      const { searchParams } = table;
      const params = {
        data_source_id: this.id,
        execution_time: [
          moment()
            .subtract(48, 'hour')
            .format('YYYY-MM-DD HH:mm:ss'),
          moment().format('YYYY-MM-DD HH:mm:ss')
        ].toString(),
        display_type: this.display,
        unit_type: this.unit,
        ...searchParams
      };
      download(params)
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 明细 / 汇总
    handleMenuChange(e) {
      this.display = e;
      switch (e) {
        case 0:
          this.model = 'table';
          this.displayType = '明细';
          break;
        case 1:
          this.model = 'table';
          this.displayType = '汇总';
          break;
        case 2:
          this.model = 'chart';
          this.displayType = '图表';
          break;
        default:
          break;
      }
      let endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      let startTime = moment()
        .subtract(48, 'hour')
        .format('YYYY-MM-DD HH:mm:ss');
      let reqParams = {
        unit_type: 'ms',
        display_type: e,
        data_source_id: this.id,
        execution_time: [startTime, endTime].toString()
      };
      if (this.dbType == 'DB2' && this.displayType == '汇总') {
        this.$set(this.tableParams, 'url', `/sqlreview/api/v1/after/db2`);
        this.$set(this.tableParams, 'method', 'post');
        reqParams.template_id = this.pane.template_id;
      } else {
        this.$set(this.tableParams, 'url', '/sqlreview/after_audit/list');
        this.$set(this.tableParams, 'method', 'get');
        reqParams.template_id = undefined;
      }
      this.$nextTick(() => {
        let { table } = this.$refs;
        if (table) {
          const { searchParams } = table;
          Object.assign(searchParams, reqParams, {
            display_type: e,
            unit_type: this.unit,
            ...this.commonSearchData
          });
          table.refresh();
        }
      });

      // 汇总不需要风险等级字段
      if (e == 1) {
        const list = this.list.filter(item => item.key !== 'risk');
        this.dealTableHeaderInfo(list);
      } else if (e == 0) {
        this.dealTableHeaderInfo(this.list);
      }
      // 重设配置
      this.setConfig();
    },
    changeUnit(e) {
      this.unit = e.target.value;
      this.$nextTick(() => {
        let { table } = this.$refs;
        if (table) {
          const { searchParams } = table;
          Object.assign(searchParams, {
            unit_type: e.target.value,
            ...this.commonSearchData
          });
          table.refresh();
        }
      });
    },
    save(data = [], list = []) {
      this.$showLoading();
      const params = {
        db_type: this.pane.db_type,
        keys: [...data]
      };
      saveTableHeaderInfo(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const _list =
              this.displayType == '汇总'
                ? list.filter(item => item.key !== 'risk')
                : list;
            this.dealTableHeaderInfo(_list);
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    saveCollectConfig() {
      const { table, chart } = this.$refs;
      table && table.refresh();
      chart && chart.refresh();
      this.getCollectConfigFn();
    },
    // 查询
    search(data = {}, params = {}) {
      const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const startTime = moment()
        .subtract(48, 'hour')
        .format('YYYY-MM-DD HH:mm:ss');
      const { execution_time: executionTime } = data;
      !executionTime && (data.execution_time = [startTime, endTime].toString());
      this.commonSearchData = data;
      const { table, chart } = this.$refs;
      if (table) {
        const { searchParams } = table;
        Object.assign(searchParams, { ...data });
        const { keep, type } = params;
        if (keep) {
          table.refreshKeep(type, data);
        } else {
          table.refresh(null, data);
        }
      } else {
        chart.refresh(data);
      }
    },
    // 清空数据
    clearData() {
      this.isSelectAll = false;
      this.tableCheckAll = false;
      this.tableIndeterminate = false;
      this.selectedRowKeys = [];
      this.count = null;
      this.includes = [];
      this.excludes = [];
      this.pageKeys = [];
    },
    // 重置
    reset() {
      this.commonSearchData = {};
      const { table } = this.$refs;
      if (table) {
        table.searchParams = {
          execution_time: '',
          template_id: this.pane.template_id,
          display_type: this.display
        };
        table.refresh();
      }
    },
    databaseEdit() {
      this.$refs.editModal.show(this.id, this.pane);
    },
    // 立即采集
    onCollect() {
      oracleImmediateCollection({ data_source_id: this.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const { table } = this.$refs;
            table && table.refresh();
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    batchAddTag() {
      if (_.isEmpty(this.selectedRowKeys) && !this.isSelectAll) {
        this.$message.warning('未选择打标数据！');
        return;
      }
      this.$refs.tag.show();
    },
    onSaveLabel(data) {
      this.$showLoading();
      const { searchParams } = this.$refs.table;
      const params = {
        label_attribute: data.label_attribute,
        permanent_day: data.permanent_day,
        is_select_all: this.isSelectAll ? 1 : 0,
        examine_list: this.isSelectAll ? this.excludes : this.includes,
        count: this.count,
        timestamp: this.timestamp,
        data_source_id: this.id,
        ...searchParams
      };
      saveLabel(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            if (!_.isEmpty(this.selectedRowKeys)) {
              this.$refs.table.refreshClear();
            } else {
              this.$refs.table.refreshKeep();
            }
            this.clearData();
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    setConfig() {
      let dbType = this.dbType;
      if (['ORACLE', 'MYSQL', 'GOLDENDB', 'POSTGRE'].includes(dbType)) {
        this.columns = this.config.columns;
        this.$set(
          this.searchParams,
          'fields',
          this.config
            .searchFields(dbType)
            .filter(item => item.visible !== false)
        );
      } else if (['OB_ORACLE', 'OB_MYSQL'].includes(dbType)) {
        this.columns = this.config.obColumns;
        this.$set(
          this.searchParams,
          'fields',
          this.config
            .obSearchFields(dbType)
            .filter(item => item.visible !== false)
        );
      } else if (['DB2'].includes(dbType)) {
        this.columns = this.config.db2Columns;
        this.$set(
          this.searchParams,
          'fields',
          this.config
            .searchFields(dbType)
            .filter(item => item.visible !== false)
        );
      } else if (['GAUSSDB'].includes(dbType)) {
        this.columns = this.config.gaussdbColumns;
        this.$set(
          this.searchParams,
          'fields',
          this.config
            .searchFields(dbType)
            .filter(item => item.visible !== false)
        );
      }
    }
  },
  watch: {
    pane: {
      handler(newVal) {
        this.dbType = newVal.db_type;
        this.setConfig();
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.database-audit-content {
  /deep/.search-area {
    background: #fff;
    box-shadow: none;
    border: none;
    border-radius: 0 0 16px 16px;
    margin-bottom: 16px;
    .ant-form {
      > .ant-row {
        > .ant-col {
          .ant-form-item {
            .ant-form-item-label {
              width: auto;
              min-width: 88px;
            }
          }
        }
      }
    }
  }
  /deep/.table-content {
    // padding: 16px 0 0 0;
    background: #fff;
    border-radius: 16px;
    .top-info {
      padding: 12px 24px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #e8e8e8;
      .left {
        display: flex;
        align-items: center;
        > div {
          display: flex;
          align-items: center;
          margin-right: 24px;
          .highligh {
            font-size: 16px;
            color: #27272a;
            font-weight: 600;
          }
          .minor {
            font-size: 16px;
            color: #27272a;
            font-weight: 400;
          }
          span {
            color: #27272a;
          }
          .anticon {
            margin-right: 2px;
            color: #27272a;
          }
          .anticon-question-circle {
            margin: 0 8px;
          }
          &.next-collect {
            background: #eff5ff;
            padding: 4px 2px;
            border-radius: 5px;
            > span {
              font-size: 12px;
              color: #214ac0;
              font-weight: 400;
            }
            .anticon {
              color: #214ac0;
              &:hover {
                cursor: pointer;
              }
            }
            &::before {
              display: block;
              content: '';
              width: 0;
              height: 0;
              border-right: 8px solid #eff5ff;
              border-top: 6px solid transparent;
              border-bottom: 6px solid transparent;
              position: relative;
              left: -10px;
            }
          }
        }
      }
      .right {
        .unit-box {
          margin-right: 12px;
        }
        .right-btn {
          margin-left: 12px;
        }
      }
    }
    .custom-title {
      max-width: 100%;
      min-width: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .anticon {
        margin-right: 4px;
        font-size: 16px;
        border-radius: 50%;
        width: 26px;
        height: 26px;
        line-height: 30px;
        &:hover {
          background: #4ec3f5;
          color: #ffffff;
          cursor: pointer;
        }
      }
    }
    .query-plan-box {
      > a {
        .limit-label {
          pre {
            color: #008adc !important;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1640px) {
  .database-audit-content {
    /deep/.search-area {
      .ant-form {
        > .ant-row {
          > .ant-col {
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.top-sql-collect-detail-info {
  .ant-popover-inner-content {
    > div {
      span {
        font-size: 13px;
        font-weight: 400;
        &:first-child {
          color: #27272a;
        }
        &:last-child {
          color: #71717a;
        }
      }
    }
  }
}
</style>
