<template>
  <div class="page-sqlresolver">
    <a-tabs
      class="page-sqlresolver-instance"
      v-model="activeKey"
      type="editable-card"
      @edit="onEdit"
      @change="onChange"
      v-if="panes.length > 0"
    >
      <a-tab-pane v-for="pane in panes" :key="pane.key" :closable="pane.closable">
        <a-tooltip slot="tab" placement="bottom">
          <template slot="title">
            <div>{{ pane.label }}</div>
            <div>{{ pane.ip ? `${pane.ip}:${pane.port}` : '' }}</div>
          </template>
          <InstanceItem mode="limit" :tagText="pane.usage" :src="pane.type" :text="pane.label" />
          <div style="display:inline-block;" v-show="activeKey == pane.key">
            <custom-icon class="icon-select-arrow" type="lu-icon-up" />
            <DataBaseChoose :value="pane.value" v-bind="instanceParams" @change="onInstanceChange" />
          </div>
        </a-tooltip>
        <Content :instanceItem="pane" :key="pane.key" />
      </a-tab-pane>
    </a-tabs>
    <div class="ps-empty" v-else>
      <a-result title="SQL工作台必须选择数据库实例，方可使用">
        <template #extra>
          <a-button key="console" type="primary" @click="add">新增实例</a-button>
        </template>
      </a-result>
    </div>
    <InstanceModal ref="InstanceModal" @save="onAddPanel" />
  </div>
</template>

<script>
import InstanceItem from '@/components/Biz/InstanceItem';
import Content from './components/Content';
import InstanceModal from './modals/Instance';
import './private';
import authUtil from '@/pages/sqlresolver/auth';

export default {
  name: 'sqlresolver',
  components: { InstanceItem, Content, InstanceModal },
  provide() {
    return {
      authInfo: () => {
        return this.authInfo;
      }
    };
  },
  data() {
    // 从缓存里获取
    // const panes = [
    //   { title: 'Tab 1', content: 'Content of Tab 1', key: '1' },
    //   { title: 'Tab 2', content: 'Content of Tab 2', key: '2' }
    // ];
    let panes = [];
    let activeKey;
    const sqlresolverInfo = localStorage.getItem(this.getStoreKey());
    try {
      if (sqlresolverInfo) {
        const info = JSON.parse(sqlresolverInfo) || {};
        panes = (info.panes || []).filter(item =>
          (item.key + '').startsWith('instanceId_')
        );
        if ((info.activeKey + '').startsWith('instanceId_')) {
          activeKey = info.activeKey;
        }
      }
    } catch (e) {}
    this.instanceList = [];
    return {
      project: GLOBAL_CONFIG.ProjectCode,
      instanceParams: this.getInstanceParams(),
      activeKey,
      panes,
      newTabIndex: 0,
      authInfo: null
    };
  },
  computed: {
    layout() {
      return this.$store.state.common.layout;
    }
  },
  async created() {
    // 获取权限
    if (_.isFunction(authUtil[this.project])) {
      const authInfo = await authUtil[this.project]();
      this.authInfo = authInfo || {};
    }
  },
  destroyed() {
    this.toggleSpecialClassName(false);
  },
  mounted() {
    this.toggleSpecialClassName(true);

    // 收起菜单
    // if (this.layout === 'up-down') {
    //   window.LAYOUT_ROOT && LAYOUT_ROOT.onCollapse(true);
    // }
    // panes为空，直接跳出选择实例
    if (this.panes.length <= 0) {
      this.add();
    }
  },
  activated() {
    this.toggleSpecialClassName(true);
  },
  deactivated() {
    this.toggleSpecialClassName(false);
  },
  methods: {
    // 切换layout-root特殊样式，目前左右新布局已不需要（已有frameType），仅给老layout保留
    toggleSpecialClassName(flag) {
      // const className =
      //   this.layout === 'fresh'
      //     ? 'show-header-and-page'
      //     : 'show-header-menu-page';
      // const root = document.querySelector('#layout-root');
      // if (!root) return;
      // if (flag) {
      //   root.className += ` ${className}`;
      //   // 延迟再设置，防止刷新当前页没生效
      //   setTimeout(() => {
      //     root.classList.add(className);
      //   });
      // } else {
      //   root.classList.remove(className);
      // }
    },
    getStoreKey() {
      return `${GLOBAL_CONFIG.ProjectCode || ''}_sqlresolverInfo`;
    },
    getInstanceKey() {
      return `instanceId_${Math.random()
        .toString(36)
        .substr(2)}`;
    },
    getInstanceParams() {
      return {
        url: `${GLOBAL_CONFIG.ProjectApiPrefix}/sql_tool/get_instance_list`,
        reqParams: {
          // _t: +new Date()
        },
        mode: 'default',
        placeholder: '请选择实例',
        backSearch: false,
        dropdownMatchSelectWidth: false,
        allowClear: false,
        getPopupContainer: el => {
          return document.body;
        },
        loaded: (data = []) => {
          this.instanceList = data;
        },
        beforeLoaded(data) {
          return data.map(item => {
            return {
              ...item,
              db_type: item.type,
              instance_usage: item.usage,
              showText:
                item.label + (item.ip ? `(${item.ip}:${item.port})` : '')
            };
          });
        }
      };
    },
    onInstanceChange(val) {
      // console.log(val, 'dkjfdkjfkdjfk')
      const instanceItem = this.instanceList.find(item => item.value == val);
      this.replacePanel(instanceItem);
    },
    onChange(val) {
      this.cache();
      this.$bus.$emit('sqlresolver-instance-change', val);
    },
    onEdit(targetKey, action) {
      this[action](targetKey);
    },
    add() {
      this.$refs.InstanceModal.show();
    },
    onAddPanel(data) {
      if (data) {
        const panes = this.panes;
        // const existPane = panes.find(pane => pane.key === data.value);
        // if (existPane) {
        //   // 已经添加过该实例，直接显示即可
        //   this.activeKey = data.value;
        //   Object.assign(existPane, data);
        //   this.cache();
        //   return;
        // }
        const activeKey = this.getInstanceKey();
        panes.push({ ...data, key: activeKey });
        this.panes = panes;
        this.activeKey = activeKey;
        this.cache();
      }
    },
    replacePanel(data) {
      const existPane = this.panes.find(pane => pane.key === this.activeKey);
      if (existPane && data) {
        const activeKey = this.getInstanceKey();
        Object.assign(existPane, data, {
          key: activeKey
        });
        this.activeKey = activeKey;
        this.panes = [...this.panes];
        this.cache();
      }
    },
    remove(targetKey) {
      let activeKey = this.activeKey;
      let lastIndex;
      this.panes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1;
        }
      });
      const panes = this.panes.filter(pane => pane.key !== targetKey);
      if (panes.length && activeKey === targetKey) {
        if (lastIndex >= 0) {
          activeKey = panes[lastIndex].key;
        } else {
          activeKey = panes[0].key;
        }
      }
      this.panes = panes;
      this.activeKey = activeKey;
      this.cache();
    },
    cache() {
      localStorage.setItem(
        this.getStoreKey(),
        JSON.stringify({
          activeKey: this.activeKey,
          panes: this.panes
        })
      );
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.page-sqlresolver {
  display: flex;
  flex-grow: 1;
  background: #ffffff;
  /deep/ .page-sqlresolver-instance {
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    width: 100%;
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    // tab-bar
    > .ant-tabs-bar {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-end;
      align-items: flex-end;
      margin: 0;

      .ant-tabs-tab {
        background: #fafafa;
        border: 1px solid #f0f0f0;
        border-radius: 0;
        margin: 0;
        border-right: 0;
        border-top: 0;
        padding: 0;

        > div {
          display: flex;
          align-items: center;
          > span {
            display: flex;
          }
        }

        .biz-instance-item {
          display: inline-block;
          .instance-item-tag {
            background: #fafafa;
            border: 0;

            &::after {
              display: none;
            }

            .database-image {
              margin-left: 48px;

              > span > .custom-icon {
                margin-right: 0;
              }
            }
          }

          &:hover {
            .iconText {
              color: @primary-color;
              font-weight: 500;
            }
          }
        }

        .icon-select-arrow {
          transform: rotate(180deg);
          transition: all 0.3s;
        }

        .biz-data-base-choose {
          position: absolute;
          left: 0;
          right: 24px;
          top: 0;
          bottom: 0;
          opacity: 0;
          width: auto;

          > .ant-select-selection {
            height: 40px;
          }
        }

        &.ant-tabs-tab-active {
          background: #ffffff;
          // border: 1px solid #f0f0f0;

          .instance-item-tag {
            background: #ffffff;

            .iconText {
              color: @primary-color;
              font-weight: 500;
            }
          }
        }

        .ant-tabs-close-x {
          margin-right: 8px;
        }
      }

      .ant-tabs-extra-content {
        height: 40px;
        margin-bottom: -1px;

        .ant-tabs-new-tab {
          width: 40px;
          height: 40px;
          background: #fafafa;
          border: 1px solid #f0f0f0;
        }
      }
    }
    // tab-content
    > .ant-tabs-content {
      display: flex;
      flex-grow: 1;
      > .ant-tabs-tabpane {
        &.ant-tabs-tabpane-active {
          flex-grow: 1;
        }
        &.ant-tabs-tabpane-inactive {
          // width: 0;
          display: none;
        }
      }
    }
  }
  .ps-empty {
    display: flex;
    justify-content: center;
    width: 100%;
  }
}
</style>
