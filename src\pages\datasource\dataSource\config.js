export default function (ctx) {
  const columns = [
    {
      title: '数据源ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '数据源名称',
      dataIndex: 'name',
      key: 'name',
      scopedSlots: { customRender: 'name' },
      width: 180
    },
    // {
    //   title: '环境',
    //   dataIndex: 'env',
    //   key: 'env',
    //   scopedSlots: { customRender: 'env' },
    //   width: 120
    // },
    // {
    //   title: '数据库类型',
    //   key: 'db_type',
    //   dataIndex: 'db_type',
    //   width: 120,
    //   scopedSlots: { customRender: 'db_type' }
    // },
    {
      title: '连接串',
      dataIndex: 'db_url',
      key: 'db_url',
      scopedSlots: { customRender: 'db_url' },
      width: 200
    },
    {
      title: '账号',
      dataIndex: 'user',
      key: 'user',
      width: 120
    },
    // {
    //   title: '链接状态',
    //   dataIndex: 'connect_status',
    //   key: 'connect_status',
    //   scopedSlots: { customRender: 'connect_status' },
    //   width: 120
    // },
    // {
    //   title: '管控模式',
    //   dataIndex: 'is_permission_control',
    //   key: 'is_permission_control',
    //   scopedSlots: { customRender: 'is_permission_control' },
    //   width: 120
    // },
    // {
    //   title: '采集开关',
    //   dataIndex: 'after_audit_status',
    //   key: 'after_audit_status',
    //   scopedSlots: { customRender: 'after_audit_status' },
    //   width: 120
    // },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 150,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const searchFields = [
    // {
    //   type: 'Input',
    //   label: 'ID',
    //   key: 'id'
    // },
    {
      type: 'Input',
      label: '名称',
      key: 'name',
      mainSearch: true,
      props: {
        placeholder: '请输名称/ID'
      }
    },
    {
      type: 'Input',
      label: '连接串',
      key: 'db_url'
    },
    {
      type: 'Select',
      label: '环境',
      key: 'env',
      props: {
        options: [
          {
            label: '测试',
            value: 'TEST'
          },
          {
            label: '生产',
            value: 'PROD'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '数据库类型',
      key: 'db_type',
      props: {
        url: '/sqlreview/project/rule_support_db'
      }
    }
    // {
    //   type: 'Select',
    //   label: '权限控制',
    //   key: 'is_permission_control',
    //   props: {
    //     options: [
    //       {
    //         label: '关',
    //         value: '0'
    //       },
    //       {
    //         label: '开',
    //         value: '1'
    //       }
    //     ]
    //   }
    // },
    // {
    //   type: 'Select',
    //   label: '数据库类型',
    //   key: 'db_type',
    //   props: {
    //     options: [
    //       {
    //         label: 'ORACLE',
    //         value: 'ORACLE'
    //       },
    //       {
    //         label: 'MYSQL',
    //         value: 'MYSQL'
    //       },
    //       {
    //         label: 'POSTGRE',
    //         value: 'POSTGRE'
    //       },
    //       {
    //         label: 'TIDB',
    //         value: 'TIDB'
    //       },
    //       {
    //         label: 'OB_MYSQL',
    //         value: 'OB_MYSQL'
    //       },
    //       {
    //         label: 'OB_ORACLE',
    //         value: 'OB_ORACLE'
    //       },
    //       {
    //         label: 'TD_MYSQL',
    //         value: 'TD_MYSQL'
    //       },
    //       {
    //         label: 'DB2',
    //         value: 'DB2'
    //       },
    //       {
    //         label: 'SQLSERVER',
    //         value: 'SQLSERVER'
    //       },
    //       { label: 'IMPALA', value: 'IMPALA' },
    //       { label: 'HIVE', value: 'HIVE' },
    //       { label: 'PRESTO', value: 'PRESTO' },
    //       { label: 'GOLDENDB', value: 'GOLDENDB' },
    //       { label: 'MOGDB', value: 'MOGDB' }
    //     ]
    //   }
    // }
  ];

  const databaseColumns = [
    {
      title: '数据库名称',
      dataIndex: 'name',
      key: 'name',
      scopedSlots: { customRender: 'name' },
      width: 200
    },
    {
      title: '所属数据源',
      dataIndex: 'datasource_name',
      key: 'datasource_name',
      scopedSlots: { customRender: 'datasource_name' },
      width: 200
    },
    {
      title: '所属应用',
      key: 'project_name',
      dataIndex: 'project_name',
      width: 200,
      scopedSlots: { customRender: 'project_name' }
    },
    {
      title: '表数量',
      dataIndex: 'table_count',
      key: 'table_count',
      scopedSlots: { customRender: 'table_count' },
      width: 100
    },
    {
      title: '权限控制',
      dataIndex: 'permission_status',
      key: 'permission_status',
      scopedSlots: { customRender: 'permission_status' },
      width: 100
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 150,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const databaseSearchFields = [
    {
      type: 'Input',
      label: '数据库名称',
      key: 'name',
      mainSearch: true,
      props: {
        placeholder: '请输入数据库名称'
      }
    },
    {
      type: 'Input',
      label: '数据源名称',
      key: 'datasource_name',
      props: {
        placeholder: '请输入数据源名称'
      }
    },
    {
      type: 'Select',
      label: '环境',
      key: 'env',
      props: {
        options: [
          {
            label: '测试',
            value: 'TEST'
          },
          {
            label: '生产',
            value: 'PROD'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '权限控制',
      key: 'permission_status',
      props: {
        options: [
          {
            label: '开',
            value: '1'
          },
          {
            label: '关',
            value: '0'
          }
        ]
      }
    }
  ];
  return {
    columns,
    searchFields,
    databaseColumns,
    databaseSearchFields
  };
}
