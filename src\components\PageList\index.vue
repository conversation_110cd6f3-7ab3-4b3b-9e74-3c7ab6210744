<template>
  <div class="page-list">
    <!-- title -->
    <div class="title" v-if="title">
      <a-icon type="global" />
      <span class="text">{{title}}</span>
    </div>
    <!-- tabs -->
    <template v-if="tabs.length > 1">
      <a-tabs>
        <a-tab-pane v-for="(item, i) in tabs" :key="i" :tab="item.title">
          <slot :name="'p' + i"></slot>
        </a-tab-pane>
      </a-tabs>
    </template>
    <template v-else>
      <slot></slot>
    </template>
  </div>
</template>

<script>
import Table from '@/components/Table';
// import common from '@/utils/common';

export default {
  components: { Table },
  props: {
    title: String,
    tabs: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.title {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 4px;
  .text {
    font-size: 22px;
    margin-left: 8px;
    color: #000000;
  }
}
</style>
