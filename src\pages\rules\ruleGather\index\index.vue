<template>
  <div>
    <iframe
      v-if="url"
      style="width: 100%; height: 836px"
      frameborder="0"
      :src="url"
    ></iframe>

    <PageList :mode="'single'" v-else>
      <div class="rule-gather-list">
        <SwitchTable
          ref="switchTable"
          v-bind="tableParams"
          :cardColumns="cardColumns"
          :listColumns="listColumns"
        >
          <!-- <template slot="tableTopLeft">
          <div class="title">规则集列表</div>
        </template> -->
          <template slot="tableTopRight">
            <a-input
              class="table-search"
              v-model="searchVal"
              placeholder="请输入规则集名"
              @pressEnter="onSearch"
            >
              <template #suffix>
                <custom-icon
                  class="search-icon"
                  @click="onSearch"
                  type="lu-icon-search"
                />
              </template>
            </a-input>
          </template>
          <!-- 卡片模式 -->
          <CardTable
            slot="cardTable"
            slot-scope="{ record }"
            v-bind="{ cardData: record }"
          >
            <template slot="action">
              <a-popconfirm title="确定删除?" @confirm="() => del(record)">
                <a>
                  <custom-icon type="delete" style="margin-right: 4px" />删除
                </a>
              </a-popconfirm>
              <a @click="modify(record)" class="highlight">
                编辑
                <custom-icon type="lu-icon-right" />
              </a>
            </template>
          </CardTable>
          <!-- 列表模式 -->
          <template v-slot:name="{ record, text }">
            <span class="namedefault" v-if="record.is_default == 1">默认</span>
            <span>{{ text }}</span>
          </template>
          <span slot="ruleStatus" slot-scope="{ record }">
            <a-switch
              size="small"
              :checked="record.status === 1"
              @change="onChange(record)"
            />
            <span style="margin-left: 8px">{{
              record.status === 1 ? '启用' : '禁用'
            }}</span>
          </span>
          <template v-slot:db_type="{ text }">
            <DbImg
              :type="text"
              v-if="text != ''"
              :schemaName="text"
              class="db-type-span"
            />
            <span v-else></span>
          </template>
          <span slot="rule_set_type" slot-scope="{ text }">
            <span :class="`${text}-category`">{{ text }}</span>
          </span>
          <custom-btns-wrapper slot="action" slot-scope="{ record }">
            <a-popconfirm
              title="确定删除?"
              @confirm="() => del(record)"
              actionBtn
            >
              <a class="remove">删除</a>
            </a-popconfirm>
            <a @click="modify(record)" actionBtn>编辑</a>
          </custom-btns-wrapper>
        </SwitchTable>
      </div>
      <div class="frame-button-wrapper">
        <a-button type="primary" @click="add" icon="plus">
          新增规则集
        </a-button>
      </div>
    </PageList>
  </div>
</template>

<script>
import {
  ruleSetDelete,
  ruleSetStatus,
  // ruleSetDDLDelete,
  ruleSetDDLStatus
} from '@/api/config/rule';
import PageList from '@/components/PageListNew/SwitchTable/index.vue';
import DbImg from '@/components/CustomImg/DbImg';
import SwitchTable from '@/components/PageListNew/SwitchTable/table.vue';
import CardTable from './CardTable';
import config from './config';
import _ from 'lodash';
export default {
  name: 'ruleGather-config',
  components: { PageList, DbImg, SwitchTable, CardTable },
  props: {},
  data() {
    this.config = config(this);
    this.searchData = {};
    const url = _.get(this.$store.state.project, 'ruleSetUrl');
    return {
      url,
      tableParams: {
        url: '/sqlreview/project/dml_ddl_rule_set_list',
        reqParams: {},
        rowKey: 'item_id',
        columns: this.config.cardColumns,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' }
      },
      cardColumns: this.config.cardColumns,
      listColumns: this.config.columns,
      searchVal: ''
    };
  },
  created() {},
  mounted() {},
  methods: {
    // 新建
    add() {
      this.$router.push({ name: 'ruleGather-config-add' });
    },
    // 修改
    modify(data) {
      this.$router.push({
        name: 'ruleGather-config-edit',
        params: { id: data.rule_set_id }
      });
    },
    // 删除
    del(e) {
      ruleSetDelete({
        id: e.rule_set_id
      }).then(res => {
        if (_.get(res, 'data.code') == 0) {
          this.$hideLoading({ tips: _.get(res, 'data.message') });
          this.search();
        } else {
          this.$hideLoading({
            method: 'error',
            tips: _.get(res, 'data.message')
          });
        }
      });
      // if (e.rule_set_type == 'DML') {
      //   ruleSetDelete({
      //     id: e.rule_set_id
      //   }).then(res => {
      //     if (_.get(res, 'data.code') == 0) {
      //       this.$hideLoading({ tips: _.get(res, 'data.message') });
      //       this.search();
      //     } else {
      //       this.$hideLoading({
      //         method: 'error',
      //         tips: _.get(res, 'data.message')
      //       });
      //     }
      //   });
      // } else {
      //   ruleSetDDLDelete({
      //     rule_set_id: e.rule_set_id
      //   }).then(res => {
      //     if (_.get(res, 'data.code') == 0) {
      //       this.$hideLoading({ tips: _.get(res, 'data.message') });
      //       this.search();
      //     } else {
      //       this.$hideLoading({
      //         method: 'error',
      //         tips: _.get(res, 'data.message')
      //       });
      //     }
      //   });
      // }
    },
    // 改变状态
    onChange(data) {
      this.$showLoading();
      if (data.rule_set_type == 'DML') {
        ruleSetStatus({
          name: data.name,
          db_type: data.db_type,
          id: data.rule_set_id,
          status: data.status === 0 ? 1 : 0
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.search();
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else {
        ruleSetDDLStatus({
          rule_set_id: data.rule_set_id,
          status: data.status === 0 ? 1 : 0
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.search();
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      }
    },
    // 查询
    search(data, params = {}) {
      const { switchTable } = this.$refs;
      const _ref = switchTable.getInstance();
      const { keep, type } = params;
      this.searchData = data || {};
      if (keep) {
        _ref.refreshKeep(type, data);
      } else {
        _ref.refresh(null, data);
      }
    },
    onSearch() {
      const { switchTable } = this.$refs;
      const _ref = switchTable.getInstance();
      const { searchParams } = _ref;
      Object.assign(searchParams, {
        name: this.searchVal
      });
      this.search({ name: this.searchVal });
    }
  }
};
</script>

<style lang="less" scoped>
.rule-gather-list {
  .namedefault {
    display: inline-block;
    padding: 1px 6px;
    border-radius: 5px;
    margin-right: 6px;
    background: #4db5f2;
    font-size: 14px;
    color: #ffffff;
    font-weight: 400;
  }
  .db-type-span {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #27272a;
    font-weight: 400;
    text-align: center;
    border: 1px solid #e4e4e7;
    border-radius: 4px;
    padding: 2px 7px;
    max-width: 200px;
    &.database-image {
      /deep/.iconStyle {
        font-size: 18px;
      }
    }
  }
  /deep/.custom-table {
    .ant-table-wrapper .ant-spin-container .ant-table .ant-table-tbody tr > td {
      padding: 32px !important;
      height: auto;
    }
    .search-area-wrapper {
      width: 100%;
      padding: 24px !important;
      justify-content: space-between !important;
      .custom-table-top-left {
        .search-area-simple {
          padding: 0;
          .ant-radio-group {
            display: flex;
            justify-content: space-between;
            .ant-radio-wrapper {
              margin-right: 8px;
              &::before {
                width: 0;
              }
              .ant-radio {
                display: none;
              }
              > span {
                // font-family: PingFangSC-Semibold;
                font-size: 14px;
                color: #71717a;
                font-weight: 400;
                padding: 4px 0;
                width: 52px;
                border-radius: 16px;
                background: #f4f5f7;
                display: inline-block;
                text-align: center;
                border: 1px solid #f4f5f7;
              }
              &.ant-radio-wrapper-checked {
                > span {
                  color: #008adc;
                  font-weight: 600;
                  background: #fff;
                  border: 1px solid #8fcaf1;
                }
              }
              &:hover {
                > span {
                  color: #008adc;
                  background: #fff;
                }
              }
            }
          }
        }
      }
      .custom-table-top-right {
        .custom-table-tools {
          white-space: nowrap;
        }
      }
    }
  }
  .DDL-category {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #f29339;
    font-weight: 400;
  }
  .DML-category {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #4cbb3a;
    font-weight: 400;
  }
}
</style>
