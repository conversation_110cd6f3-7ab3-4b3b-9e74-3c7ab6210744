<template>
  <a-modal
    v-model="visible"
    @cancel="onCancel"
    :footer="null"
    width="950px"
    wrapClassName="home-sqlreview-review-splan-info-modal"
  >
    <a-card type="small" :bordered="false" class="small-card">
      <a-tabs
        :active-key="activeKey"
        @change="activeChange"
        :animated="false"
        :class="[tabsNum == 1 ? 'single' : 'couple']"
      >
        <a-tab-pane
          key="dbaSuggest"
          tab="DBA建议"
          v-if="labelInfo.comment_status !== 0 && isProject"
        >
          <main class="dba-suggest-box">
            <div class="apply" v-if="labelInfo.label_attribute">
              <div class="info">
                <custom-icon type="lu-icon-applyfor1" />
                <span>{{
                  labelInfo.created_by ? labelInfo.created_by + '：' : ''
                }}</span>
                <span v-if="labelInfo.label_attribute == 1">{{ '白名单' }}</span>
                <span v-else-if="labelInfo.label_attribute == 2">{{
                  '整改中'
                }}</span>
              </div>
              <span class="time">{{ labelInfo.created_at }}</span>
            </div>
            <div
              class="audit"
              v-if="[2, 3, 4, 5, 8].includes(labelInfo.comment_status)"
            >
              <div class="info">
                <custom-icon
                  v-if="[2, 4, 5, 8].includes(labelInfo.comment_status)"
                  type="lu-icon-right1"
                />
                <custom-icon
                  v-if="labelInfo.comment_status == 3"
                  type="lu-icon-wrong"
                />
                <span>{{ labelInfo.operator_dba + '：' }}</span>
                <span v-if="[2, 4, 5, 8].includes(labelInfo.comment_status)">{{
                  '同意'
                }}</span>
                <span v-if="labelInfo.ai_status == 3">{{ '不同意' }}</span>
              </div>
              <span class="time">{{ labelInfo.updated_at }}</span>
            </div>
            <div
              class="suggest"
              v-if="labelInfo.comment_content || labelInfo.dba_comment"
            >
              <div>{{ labelInfo.comment_content }}</div>
              <div>{{ labelInfo.dba_comment }}</div>
            </div>
            <div
              class="empty"
              v-if="
                !labelInfo.label_attribute &&
                ![2, 3, 4, 5, 8].includes(labelInfo.comment_status)
              "
            ></div>
          </main>
        </a-tab-pane>
        <a-tab-pane
          key="indexSuggest"
          tab="索引建议"
          v-if="sqlPlanInfo.length > 0"
        >
          <div v-for="(item, index) in sqlPlanInfo" :key="index">
            <div class="suggest">{{ item.message }}</div>
            <div class="suggest-sql">
              <span>{{ item.sql }}</span>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane
          key="optimzationSuggest"
          tab="优化建议"
          v-if="sqlSuggest.length > 0"
        >
          <div>
            <a-collapse :bordered="false" expandIconPosition="right">
              <a-collapse-panel
                v-for="(item, index) in sqlSuggest"
                :key="index"
                :header="item.ai_comment"
              >
                <MarkdownViewer
                  class="rich-editor-preview"
                  v-model="item.suggest"
                ></MarkdownViewer>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </a-modal>
</template>
<script>
import MarkdownViewer from '@/components/Markdown/viewer';
export default {
  components: { MarkdownViewer },
  props: {
    sqlPlanInfo: {
      type: Array,
      default: () => []
    },
    sqlSuggest: {
      type: Array,
      default: () => []
    },
    labelInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    isDba() {
      const user = this.$store.state.account.user || {};
      return user.role === 'dba';
    },
    tabsNum() {
      let num = 0;
      if (this.sqlPlanInfo.length > 0) {
        num += 1;
      }
      if (this.sqlSuggest.length > 0) {
        num += 1;
      }
      if (this.labelInfo.comment_status !== 0) {
        num += 1;
      }
      return num;
    },
    isProject() {
      const name = this.$route.name;
      return name == 'project-review-review';
    }
  },
  data() {
    return {
      visible: false,
      activeKey: '',
      value: '',
      tableParams: {
        url: '',
        reqParams: {},
        columns: [],
        rowKey: 'id'
      },
      isShow: false
    };
  },
  mounted() {},
  updated() {
    this.activeKey == '' && this.getActiveKey();
  },
  methods: {
    getActiveKey() {
      if (this.labelInfo.comment_status !== 0 && this.isProject) {
        this.activeKey = 'dbaSuggest';
      } else if (this.sqlPlanInfo.length > 0) {
        this.activeKey = 'indexSuggest';
      } else if (this.sqlSuggest.length > 0) {
        this.activeKey = 'optimzationSuggest';
      }
    },
    activeChange(activeKey) {
      this.activeKey = activeKey;
    },
    show() {
      this.visible = true;
    },
    onCancel() {
      this.visible = false;
    }
  },
  watch: {}
};
</script>

<style lang="less">
.home-sqlreview-review-splan-info-modal {
  .ant-modal-content {
    .ant-modal-close {
      .ant-modal-close-x {
        color: #27272a;
        .anticon {
          color: #27272a;
        }
      }
    }
    .ant-modal-body {
      padding: 32px 24px 48px 24px;
      .small-card {
        border-radius: 8px;
        .ant-card-body {
          // min-height: 400px;
          padding: 0;
          .ant-tabs {
            .ant-tabs-bar {
              .ant-tabs-nav-container {
                .ant-tabs-nav-wrap {
                  .ant-tabs-nav-scroll {
                    display: flex;
                    justify-content: space-around;
                    .ant-tabs-tab {
                      font-size: 16px;
                      color: #71717a;
                      font-weight: 400;
                      &.ant-tabs-tab-active {
                        font-size: 16px;
                        color: #27272a;
                        font-weight: 600;
                      }
                      &:last-child {
                        &::after {
                          display: none;
                        }
                      }
                    }
                    .ant-tabs-ink-bar {
                      height: 3px;
                    }
                  }
                }
              }
            }
            &.single {
              .ant-tabs-bar {
                .ant-tabs-nav-container {
                  .ant-tabs-nav-wrap {
                    .ant-tabs-nav-scroll {
                      justify-content: flex-start;
                    }
                  }
                }
              }
            }
          }
        }
        .suggest {
          font-size: 12px;
          color: #71717a;
          text-align: justify;
          font-weight: 400;
        }
        .suggest-sql {
          padding: 8px 16px;
          background: #f4f5f7;
          margin: 12px 0;
          border-radius: 6px;
          > span {
            font-size: 12px;
            color: #27272a;
            text-align: justify;
          }
        }
        .dba-suggest-box {
          .apply,
          .audit {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            .info {
              display: flex;
              align-items: center;
              .anticon {
                font-size: 18px;
                color: #000000;
                margin-right: 4px;
              }
              .span {
                font-size: 12px;
                color: #27272a;
                text-align: justify;
                font-weight: 400;
                margin-right: 4px;
              }
            }
          }
          .apply {
            border-bottom: 1px solid #e8e8e8;
          }
          .suggest {
            background: #f4f5f7;
            padding: 8px 16px;
            > div:first-child {
              margin-bottom: 8px;
            }
          }
          .empty {
            background: #f4f5f7;
            height: 200px;
          }
        }
        .action-box {
          display: flex;
          justify-content: flex-end;
          margin-top: 12px;
          > a {
            font-size: 12px;
            color: #008adc;
          }
        }
        .dba-suggest {
          background: #f4f5f7;
          height: 100px;
          &.ant-input:focus {
            border-color: #fff;
            border-right-width: 1px !important;
            outline: 0;
            box-shadow: 0 0 0 0;
          }
          &.ant-input:hover {
            border-color: #fff;
          }
        }
      }
    }
  }

  textarea.review-advice {
    background-color: rgba(15, 120, 251, 0.06);
    border: 1px solid transparent;
  }
  .ant-collapse-borderless {
    overflow: auto;
    max-height: 600px;
    background-color: #fff;
    .ant-collapse-item {
      &:nth-child(1) {
        .ant-collapse-header {
          padding: 0 0 10px 0;
        }
      }
      .ant-collapse-header {
        font-size: 12px;
        color: #27272a;
        font-weight: 600;
        padding: 10px 0;
        .anticon {
          svg {
            transform: rotate(-90deg);
          }
        }
      }
    }
  }
}
</style>