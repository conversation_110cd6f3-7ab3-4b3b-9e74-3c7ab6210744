<template>
  <!-- 概况 -->
  <div class="quater-block small-size overview-block">
    <a-skeleton :loading="loading" active>
      <div class="title">概况</div>
      <div class="overview-info" v-for="item in overviewInfo" :key="item.icon">
        <div>
          <custom-icon :type="item.icon"></custom-icon>
          <span>{{ item.label }}</span>
        </div>
        <div class="num">{{ item.value || '--' }}</div>
      </div>
      <a-list
        class="data-source"
        item-layout="horizontal"
        :data-source="handleDataSource"
        v-if="dataSource.length > 0"
      >
        <a-list-item slot="renderItem" slot-scope="item">
          <template v-if="item.db_type">
            <a-tooltip>
              <template slot="title">
                <!-- {{ item.db_type + ':' + item.db_type_count }} -->
                {{ item.db_type }}
              </template>
              <DbImg :value="item.db_type" :UseIconOnly="true" />
              <a-progress
                :percent="item.rate * 100"
                :show-info="false"
                :strokeColor="getColorFn()"
              />
              <span class="num">{{ item.db_type_count }}</span>
            </a-tooltip>
          </template>
          <a-progress
            :percent="0"
            :show-info="false"
            v-else
            class="only-progress"
          />
        </a-list-item>
      </a-list>
      <Empty v-else />
    </a-skeleton>
  </div>
</template>

<script>
import DbImg from '@/components/CustomImg/DbImg';
import Empty from './Empty';
export default {
  components: { DbImg, Empty },
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    overviewInfo: {
      type: Array,
      default: () => []
    },
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    // 数据小于四用空进度条站位
    handleDataSource() {
      const length = this.dataSource.length;
      const num = 4 - length;
      let arr = [];
      if (length < 4) {
        const obj = { db_type: null, db_type_count: 0, rate: 0 };
        for (let i = 0; i < num; i++) {
          arr.push(obj);
        }
      }
      return [...this.dataSource, ...arr];
    }
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    getColorFn() {
      // const colorObj = {
      //   0: '#4096FF',
      //   1: '#FF7875',
      //   2: '#52C41A',
      //   3: '#FAAD14',
      //   4: '#4096FF'
      // };
      // const randomNum = Math.floor(Math.random() * (4 - 0)) + 0;
      // return colorObj[randomNum];
      return 'rgb(138,185,216)';
    }
  }
};
</script>

<style lang="less" scoped>
.overview-block {
  height: 420px;
  width: 25%;
  margin-right: 16px;
  padding: 16px 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.05);
  .title {
    font-size: 16px;
    color: #1f1f1f;
    padding-bottom: 16px;
    font-weight: bold;
  }
  .overview-info {
    padding: 10px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    background-image: linear-gradient(90deg, #f2f9ff 0%, #e6f7ff 100%);
    &:nth-of-type(3) {
      background-image: linear-gradient(90deg, #fef9f5 0%, #fff3e6 100%);
    }
    &:nth-of-type(4) {
      background-image: linear-gradient(90deg, #fafff5 0%, #f6ffed 100%);
    }
    > div:first-child {
      .anticon {
        font-size: 16px;
        color: #1f1f1f;
        font-weight: 600;
      }
      span {
        font-size: 14px;
        color: #1f1f1f;
        margin-left: 8px;
      }
    }
    .num {
      font-size: 20px;
      color: #1f1f1f;
      font-weight: 600;
    }
  }
  .data-source {
    height: 180px;
    overflow-y: auto;
    .ant-spin-container {
      .ant-list-items {
        .ant-list-item {
          padding: 8px 10px;
          border-bottom: none;
          > span {
            display: flex;
            width: 100%;
            align-items: center;
            .custom-icon {
              font-size: 16px;
              margin-right: 12px;
            }
            .num {
              margin-left: 8px;
            }
          }
          .only-progress {
            margin-left: 28px;
          }
        }
      }
    }
  }
}
</style>