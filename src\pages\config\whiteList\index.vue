<template>
  <div class="content page-list-with-tabs">
    <div class="content-tabs">
      <a-tabs :active-key="activeTab" @change="tabChange">
        <a-tab-pane key="table" tab="表">
          <TableList :activeTab="activeTab" dbType="table" ref="table"></TableList>
        </a-tab-pane>
        <a-tab-pane key="mysql" tab="SQL">
          <TableList :activeTab="activeTab" dbType="mysql" ref="mysql"></TableList>
        </a-tab-pane>
      </a-tabs>
      <div class="frame-button-wrapper">
        <a-button type="danger" @click="batchDelete">批量删除</a-button>
        <a-button v-if="activeTab === 'table'" type="primary" @click="batchAdd">添加白名单</a-button>
      </div>
    </div>
  </div>
</template>
<script>
import TableList from './TableList';
import config from './config';

export default {
  components: { TableList },
  props: {},
  data() {
    this.config = config(this);
    return {
      activeTab: 'table' // 默认选中 tab
    };
  },
  created() {},
  mounted() {},
  methods: {
    addRules() {
      this.$router.push({ name: 'rules-config-add' });
    },
    // 切换 tab 选项卡
    tabChange(activeKey) {
      this.activeTab = activeKey;
      // 缓存状态
    },
    batchAdd() {
      this.$refs.table.show();
    },
    batchDelete() {
      if (this.activeTab === 'table') {
        this.$refs.table.del();
      } else {
        this.$refs.mysql.del();
      }
    }
  }
};
</script>

<style lang="less" scoped>
// .content-tabs {
// position: relative;

// .content-btns {
//   position: absolute;
//   right: 0;
//   top: 0;
// }
// }
/deep/.search-area {
  .form {
    .ant-form-item-label {
      max-width: 130px !important;
    }
  }
}
</style>
