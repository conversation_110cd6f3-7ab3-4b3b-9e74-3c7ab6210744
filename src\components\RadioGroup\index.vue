<template>
  <a-radio-group
    :class="className"
    v-bind="raidoGroupProps"
    v-on="raidoGroupListeners"
    :value="localValue"
    :button-style="buttonStyle"
  >
    <template v-for="item in options || []">
      <template v-if="mode=='icon'">
        <a-radio-button :key="item.value" v-bind="item || {}" v-if="item.visible!==false">
          <custom-icon :type="item.icon" :style="item.style"></custom-icon>
          <span class="crg-des">{{item.label}}</span>
          <span class="crg-sub-des" v-if="item.tips">{{item.tips}}</span>
        </a-radio-button>
      </template>
      <template v-else-if="mode.indexOf('button') > -1">
        <a-radio-button :key="item.value" v-bind="item || {}" v-if="item.visible!==false">
          <span>{{item.label}}</span>
        </a-radio-button>
      </template>
      <template v-else>
        <a-radio :key="item.value" v-bind="item || {}">
          <span>{{item.label}}</span>
          <div class="crg-tips" v-if="showTips && item.tips">{{item.tips}}</div>
        </a-radio>
      </template>
    </template>
  </a-radio-group>
</template>

<script>
const defaultProps = {};
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    options: {
      type: Array,
      default: () => []
    },
    value: String | Number,
    mode: {
      type: String,
      default: 'normal' // normal, tips, lineTips, button, buttonAlone, icon
    }
  },
  data() {
    return {
      localValue: this.value
    };
  },
  mounted() {},
  beforeDestroy() {},
  computed: {
    raidoGroupProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    raidoGroupListeners() {
      return { ...this.$listeners, ...{ change: this.handleChange } };
    },
    className() {
      let res = ['custom-radio-group'];
      if (this.mode === 'tips') {
        res.push('crg-with-tips');
      } else if (this.mode === 'lineTips') {
        res.push('crg-with-line-tips');
      } else if (this.mode === 'buttonAlone') {
        res.push('crg-with-button-alone');
      } else if (this.mode === 'icon') {
        res.push('crg-with-icon');
      }
      return res;
    },
    showTips() {
      const tipsMode = ['tips', 'lineTips'];
      return tipsMode.includes(this.mode);
    },
    buttonStyle() {
      return (
        this.raidoGroupProps['button-style'] ||
        this.raidoGroupProps['buttonStyle'] ||
        (this.mode.indexOf('button') > -1 ? 'solid' : 'outline')
      );
    }
  },
  methods: {
    handleChange(e) {
      // console.log(e);
      const _val = e.target.value;
      this.localValue = _val;
      this.$emit('change', _val);
    }
  },
  watch: {
    value(newVal, oldVal) {
      if (newVal != oldVal) {
        this.localValue = newVal;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.custom-radio-group {
  /deep/ .ant-radio-wrapper {
    .ant-radio {
      .ant-radio-inner {
        border-color: #A1A1AA;
      }
    }
    .ant-radio-checked {
      .ant-radio-inner {
        border-color: @primary-color;
      }
    }
  }

  &:not(.ant-radio-group-outline) {
    /deep/ .ant-radio-button-wrapper-checked {
      box-shadow: none;
    }
  }
  &.crg-with-tips {
    display: block;
    /deep/ .ant-radio-wrapper {
      display: flex;
      align-items: flex-start;
      background: #ffffff;
      border: 1px solid #E4E4E7;
      border-radius: 4px;
      padding: 12px 24px;
      margin-left: 0;
      margin-bottom: 12px;
      margin-right: 0;
      white-space: normal;

      .ant-radio {
        top: 2px;
      }

      .crg-tips {
        color: #A1A1AA;
        font-size: 12px;
        word-break: break-all;
        margin-top: 4px;
      }
      &.ant-radio-wrapper-disabled {
        cursor: not-allowed;
        // background: #f5f5f5;
        // border-color: #d9d9d9 !important;
      }
      &:not(.ant-radio-wrapper-disabled).ant-radio-wrapper-checked {
        border-color: @checked-border-color;
        background: @checked-bg;
      }
      &:not(.ant-radio-wrapper-disabled):hover {
        border-color: @checked-border-color;
        background: @checked-bg;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    &.inline {
      /deep/ .ant-radio-wrapper {
        display: inline-block;
        padding: 6px 14px;
        margin: 0 8px 0 0;

        .ant-radio {
          top: -1px;
        }
      }
    }
  }
  &.crg-with-line-tips {
    /deep/ .ant-radio-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: start;
      padding: 8px;
      border: 1px solid #E4E4E7;
      border-radius: 4px;
      padding: 12px 24px;
      margin-left: 0;
      margin-bottom: 12px;
      > span {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: start;
        > span:first-child {
          margin-right: 16px;
        }
      }
      .crg-tips {
        color: #A1A1AA;
        font-size: 12px;
        word-break: break-all;
      }

      &.ant-radio-wrapper-checked {
        border-color: @checked-border-color;
        background: @checked-bg;
      }
      &:not(.ant-radio-wrapper-disabled):hover {
        border-color: @checked-border-color;
        background: @checked-bg;
      }
    }
  }
  &.crg-with-button-alone {
    /deep/.ant-radio-button-wrapper {
      margin-right: 8px;
      border-radius: 4px;
      border: 1px solid #E4E4E7;
      &::before {
        width: 0;
        display: none;
      }
      &.ant-radio-button-wrapper-checked {
        border: 1px solid @primary-color;
        background: @primary-color;
      }
    }
  }
  &.crg-with-icon {
    display: flex;
    /deep/ .ant-radio-button-wrapper {
      display: flex;
      align-items: center;
      background: #ffffff;
      border: 1px solid #E4E4E7;
      border-radius: 4px;
      padding: 2px 20px 2px 16px;
      margin-left: 0;
      margin-right: 8px;
      white-space: normal;
      height: auto;
      &::before {
       display: none;
      }
      .ant-radio-button {
        display: none;
      }

      .crg-sub-des {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #A1A1AA;
        font-weight: 400;
      }
      .crg-des {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #27272a;
        font-weight: 400;
      }
      &.ant-radio-button-wrapper-checked {
        border: 1px solid @checked-border-color;
        background: @checked-bg;
        box-shadow: none;
      }
      &:not(.ant-radio-button-wrapper-checked):hover {
        border-color: @checked-border-color;
        background: @checked-bg;
      }
    }
  }
}
</style>