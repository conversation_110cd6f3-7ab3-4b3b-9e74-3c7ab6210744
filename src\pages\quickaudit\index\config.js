export default function (ctx) {
  const columns = [
    {
      title: 'SQL',
      dataIndex: 'sql_text',
      key: 'sql_text',
      scopedSlots: { customRender: 'sql_text' }
    },
    {
      title: '类型',
      dataIndex: 'audit_type',
      key: 'audit_type',
      width: 100,
      scopedSlots: { customRender: 'audit_type' }
    },
    {
      title: '审核时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 200
    }
  ]

  const searchFields = [
    {
      type: 'Select',
      key: 'risk',
      compIcon: 'lu-icon-alarm',
      props: {
        placeholder: '风险等级',
        options: [
          { label: '高风险', value: 'high' },
          { label: '低风险', value: 'low' },
          { label: '无风险', value: 'no_risk' },
          { label: '异常', value: 'error' }
        ],
        getPopupContainer: (el) => {
          return document.body;
        }
      }
    },
    {
      type: 'Select',
      key: 'audit_type',
      compIcon: 'lu-icon-database',
      props: {
        placeholder: '审核类型',
        options: [{ label: '在线', value: 'online' }, { label: '离线', value: 'offline' }],
        getPopupContainer: (el) => {
          return document.body;
        }
      }
    },
    {
      type: 'Input',
      key: 'sql_text',
      compIcon: 'lu-icon-sqledit',
      props: {
        placeholder: 'SQL搜索'
      }
    }
  ];

  return {
    columns,
    searchFields
  };
}
