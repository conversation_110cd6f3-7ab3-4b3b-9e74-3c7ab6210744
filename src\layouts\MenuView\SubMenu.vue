<template functional>
  <!-- 叶子节点 -->
  <a-menu-item
    :key="props.menuInfo.key"
    :style="props.menuInfo.style || {}"
    :class="props.menuInfo.className || ''"
    :disabled="props.menuInfo.disabled"
    v-if="props.menuInfo.isLeaf && props.isValidMenuItem(props.menuInfo) && props.menuInfo._visible !== false"
  >
    <VNode :node="() => props.menuInfo.title(props.menuInfo)" v-if="props.menuInfo.title" />
    <template v-else>
      <custom-icon :type="props.menuInfo.icon" v-if="props.menuInfo.icon" />
      <span :title="props.menuInfo.name">{{props.menuInfo.name}}</span>
      <custom-icon :type="'arrow-right'" class="icon-jump-url" v-if="props.menuInfo.jumpUrl && props.menuInfo.jumpUrl() && props.menuInfo.jumpIcon !== false" />
    </template>
  </a-menu-item>
  <!-- 非叶子节点 -->
  <a-sub-menu
    :key="props.menuInfo.key"
    :class="['layout-menu-submenu', props.menuInfo.className || '']"
    :disabled="props.menuInfo.disabled"
    popupClassName="layout-menu-popup-submenu"
    v-else-if="props.menuInfo._visible !== false"
  >
    <span :style="props.menuInfo.style || {}" slot="title">
      <custom-icon :type="props.menuInfo.icon" v-if="props.menuInfo.icon" />
      <span>{{props.menuInfo.name}}</span>
    </span>
    <template v-for="cItem in props.menuInfo.children">
      <my-sub-menu :key="cItem.key" :menu-info="cItem"></my-sub-menu>
    </template>
  </a-sub-menu>
</template>

<script>
import { Menu } from 'ant-design-vue';
export default {
  name: 'MySubMenu',
  isSubMenu: true,
  props: {
    ...Menu.SubMenu.props,
    menuInfo: {
      type: Object,
      default: function() {
        return {};
      }
    },
    isValidMenuItem: {
      type: Function,
      default: function(info = {}) {
        if (info.resourceCode && !(info.resourceCode + '').startsWith('$menuItem_')) {
          return false;
        }
        return true;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.icon-jump-url {
  position: absolute;
  top: 50%;
  right: 16px;
  margin-right: 0;
  transform: translateY(-50%);
  font-size: 12px;
  font-weight: 500;
}
.ant-menu-sub {
  .icon-jump-url {
    right: 4px;
  }
}
</style>
