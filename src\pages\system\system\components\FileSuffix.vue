<template>
  <div class="file-suffix-content">
    <div class="title">{{ data.from_desc }}</div>
    <Form ref="form" v-bind="params" :formData="formData"></Form>
    <div class="title">{{ radioData.from_desc + ':' }}</div>
    <a-alert :message="radioData.form_notes" type="info"></a-alert>
    <div class="radio-wrapper">
      <a-radio
        v-for="item in radioData.sql_review_dynamic_form_element"
        :key="item.id"
        defaultChecked
        disabled
        >{{ item.element_name }}</a-radio
      >
    </div>
  </div>
</template>

<script>
import Form from '@/components/Form';

export default {
  props: {
    value: String,
    data: Object
  },
  components: { Form },
  data() {
    let options = this.data.sql_review_dynamic_form_element.map(item => {
      return {
        ...item,
        label: item.element_name,
        value: item.element_value
      };
    });
    return {
      params: {
        fields: [
          {
            type: 'Select',
            label: '',
            key: 'file_suffix',
            props: {
              options: options,
              placeholder: '请选择',
              mode: 'tags'
              // maxTags: 1,
              // separator: ','
            },
            width: '300',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'change' }
            ]
          }
        ],
        fixedLabel: true,
        layout: 'vertical',
        colon: true
      },
      formData: {}
    };
  },
  computed: {
    radioData() {
      const res = _.get(this.data, 'extra_form_element');
      return res;
    }
  },
  created() {},
  mounted() {},
  methods: {
    getData() {
      let res = {};
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          res = this.$refs.form.getData();
        }
      });
      return res;
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.formData = { file_suffix: newVal.split(',') };
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.file-suffix-content {
  .title {
    padding: 24px 0 8px 0;
  }
  /deep/.ant-form {
    .ant-row {
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }
  .ant-alert {
    margin-bottom: 8px;
  }
  .radio-wrapper {
    .ant-radio-wrapper {
      width: 45%;
      padding: 4px 0;
    }
  }
}
</style>
