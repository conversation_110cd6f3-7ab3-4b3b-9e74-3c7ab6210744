import Http from '@/utils/request'

export function getLogin (params = {}) {
  return Http({
    url: `/sqlreview/login/`,
    method: 'post',
    data: params
  });
}

export function getLogout (params = {}) {
  return Http({
    url: `/sqlreview/logout/`,
    method: 'post',
    data: params
  });
}

export function verifyActivationCode (params = {}) {
  return Http({
    url: `/sqlreview/faced/verify_activationcode`,
    method: 'post',
    data: params
  });
}
export default {};