export default function (ctx) {
  const columns = [
    {
      title: '项目',
      dataIndex: 'name',
      key: 'name',
      shower: {
        useSlot: 'name'
      },
      scopedSlots: { customRender: 'Shower' }
    },
    {
      title: 'agent自动发现',
      dataIndex: 'agent_auto_find',
      key: 'agent_auto_find',
      shower: {
        useSlot: 'agent_auto_find'
      },
      scopedSlots: { customRender: 'Shower' }
    }
  ]
  const fields = [
    {
      type: 'Input',
      label: '项目',
      key: 'name'
    },
    {
      type: 'Input',
      label: 'agent worker',
      key: 'agent_worker'
    },
    {
      type: 'Select',
      label: '状态',
      key: 'worker_status',
      props: {
        options: [
          {
            label: '离线',
            value: 0
          },
          {
            label: '在线',
            value: 1
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'agent自动发现',
      key: 'auto_find',
      props: {
        options: [
          { label: '开', value: 1 },
          { label: '关', value: 0 }
        ]
      }
    },
    {
      type: 'Input',
      label: '',
      key: 'btns',
      hideComponent: true,
      slots: [{ key: 'btns' }],
      rules: []
    }
  ];

  const agentColumns = [
    {
      title: 'agent worker',
      dataIndex: 'agent_worker',
      key: 'agent_worker',
      scopedSlots: { customRender: 'agent_worker' }
    },
    {
      title: 'agent master',
      dataIndex: 'agent_master',
      key: 'agent_master',
      scopedSlots: { customRender: 'agent_master' }
    },
    {
      title: '状态',
      key: 'worker_status',
      dataIndex: 'worker_status',
      scopedSlots: { customRender: 'worker_status' }

    },
    {
      title: '创建时间',
      dataIndex: 'start_time',
      key: 'start_time'
    },
    {
      title: '最后采集时间',
      dataIndex: 'last_collect_time',
      key: 'last_collect_time'
    },
    {
      title: 'agent worker采集开关',
      dataIndex: 'auto_find',
      key: 'auto_find',
      scopedSlots: { customRender: 'auto_find' }
    }
  ];
  return {
    columns,
    searchFields: fields,
    agentColumns
  };
}
