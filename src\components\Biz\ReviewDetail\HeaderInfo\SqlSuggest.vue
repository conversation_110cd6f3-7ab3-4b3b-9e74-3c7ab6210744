<template>
  <a-card type="small" :bordered="false" class="small-card">
    <div class="enlarge-icon-box">
      <a-icon type="fullscreen" @click="onFullscreen" />
    </div>
    <a-tabs
      :active-key="activeKey"
      @change="activeChange"
      :animated="false"
      :class="[tabsNum == 1 ? 'single' : 'couple']"
    >
      <a-tab-pane
        key="dbaSuggest"
        tab="DBA建议"
        v-if="(isOrder && isDba) || (dbaComment.length > 0 && isProject)"
      >
        <main class="dba-suggest-box">
          <div class="apply" v-for="(item, index) in dbaComment" :key="index">
            <LimitLabel
              :label="item.operator_info"
              mode="ellipsis"
              :block="true"
              class="text"
            ></LimitLabel>
            <span class="time">{{ item.operator_time }}</span>
          </div>
          <div v-if="isDba" class="edit-box">
            <a-textarea
              placeholder="请输入建议"
              class="dba-suggest"
              :rows="6"
              v-model="value"
            />
            <a-button @click="save" type="primary">保存</a-button>
          </div>
        </main>
      </a-tab-pane>
      <a-tab-pane
        key="indexSuggest"
        tab="索引建议"
        v-if="sqlPlanInfo.length > 0"
      >
        <div v-for="(item, index) in sqlPlanInfo" :key="index">
          <div class="suggest">{{ item.message }}</div>
          <div class="suggest-sql" v-if="item.sql">
            <span>{{ item.sql }}</span>
          </div>
          <div class="index-suggest-btn" v-if="item.sql">
            <custom-icon type="copy" />
            <span @click="onCopy(item.sql)">复制</span>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane
        key="optimzationSuggest"
        tab="优化建议"
        v-if="sqlSuggest.length > 0"
      >
        <div>
          <a-collapse :bordered="false" expandIconPosition="right">
            <a-collapse-panel
              v-for="(item, index) in sqlSuggest"
              :key="index"
              :header="item.ai_comment"
            >
              <MarkdownViewer
                class="rich-editor-preview"
                v-model="item.suggest"
              ></MarkdownViewer>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </a-tab-pane>
    </a-tabs>
    <EnlargeModal
      ref="modal"
      :sqlPlanInfo="sqlPlanInfo"
      :sqlSuggest="sqlSuggest"
      :dbaComment="dbaComment"
    />
  </a-card>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';
import MarkdownViewer from '@/components/Markdown/viewer';
import EnlargeModal from './EnlargeModal';
export default {
  components: { MarkdownViewer, EnlargeModal, LimitLabel },
  props: {
    sqlPlanInfo: {
      type: Array,
      default: () => []
    },
    sqlSuggest: {
      type: Array,
      default: () => []
    },
    dbaComment: {
      type: Array,
      default: () => []
    },
    id: String | Number,
    isOrder: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isDba() {
      const user = this.$store.state.account.user || {};
      return user.role === 'dba';
    },
    tabsNum() {
      let num = 0;
      if (this.sqlPlanInfo.length > 0) {
        num += 1;
      }
      if (this.sqlSuggest.length > 0) {
        num += 1;
      }
      if (this.dbaComment.length > 0) {
        num += 1;
      }
      return num;
    },
    isProject() {
      const name = this.$route.name;
      return name == 'project-review-review';
    }
  },
  data() {
    return {
      activeKey: null,
      value: '',
      tableParams: {
        url: '',
        reqParams: {},
        columns: [],
        rowKey: 'id'
      }
    };
  },
  mounted() {},
  updated() {
    this.getActiveKey();
  },
  methods: {
    getActiveKey() {
      if (this.activeKey) {
        return;
      }
      if (
        (this.dbaComment.length > 0 && this.isProject) ||
        (this.isOrder && this.isDba)
      ) {
        this.activeKey = 'dbaSuggest';
      } else if (this.sqlPlanInfo.length > 0) {
        this.activeKey = 'indexSuggest';
      } else if (this.sqlSuggest.length > 0) {
        this.activeKey = 'optimzationSuggest';
      }
    },
    activeChange(activeKey) {
      this.activeKey = activeKey;
    },
    save() {
      if (!this.value) return;
      this.$emit('saveAdvice', this.value);
      this.value = '';
    },
    onCopy(sql) {
      CommonUtil.copy({
        value: sql,
        callback: () => {
          this.$message.success('成功复制sql语句');
        }
      });
    },
    onFullscreen() {
      this.$refs.modal.show();
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.small-card {
  border-radius: 8px;
  margin-left: 16px;
  width: 32%;
  /deep/.ant-card-body {
    .enlarge-icon-box {
      position: absolute;
      top: 6px;
      right: 12px;
      color: #e4e4e7;
      font-size: 20px;
    }
    .ant-tabs {
      .ant-tabs-bar {
        .ant-tabs-nav-container {
          .ant-tabs-nav-wrap {
            .ant-tabs-nav-scroll {
              display: flex;
              justify-content: space-around;
              .ant-tabs-tab {
                font-size: 16px;
                color: #71717a;
                font-weight: 400;
                &.ant-tabs-tab-active {
                  font-size: 16px;
                  color: #27272a;
                  font-weight: 600;
                }
                &:last-child {
                  &::after {
                    display: none;
                  }
                }
              }
              .ant-tabs-ink-bar {
                height: 3px;
                // height: 3px !important;
              }
            }
          }
        }
      }
      &.single {
        .ant-tabs-bar {
          .ant-tabs-nav-container {
            .ant-tabs-nav-wrap {
              .ant-tabs-nav-scroll {
                justify-content: flex-start;
              }
            }
          }
        }
      }
    }
  }
  .suggest {
    font-size: 12px;
    color: #71717a;
    text-align: justify;
    font-weight: 400;
  }
  .suggest-sql {
    padding: 8px 16px;
    background: #f4f5f7;
    margin: 12px 0;
    border-radius: 6px;
    > span {
      font-size: 12px;
      color: #27272a;
      text-align: justify;
    }
  }
  .index-suggest-btn {
    // position: absolute;
    // right: 16px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .anticon {
      font-size: 12px;
      color: #008adc;
      margin-right: 4px;
    }
    span {
      font-size: 13px;
      color: #008adc;
    }
    &:hover {
      cursor: pointer;
    }
  }
  .dba-suggest-box {
    min-height: 100px;
    .apply {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #e8e8e8;
      &:last-child {
        border: none;
      }
      // .text {
      //   overflow: hidden;
      //   text-overflow: ellipsis;
      //   word-break: normal;
      //   white-space: normal;
      // }
      .time {
        font-size: 12px;
        color: #bfbfbf;
        white-space: nowrap;
      }
    }
    .edit-box {
      display: flex;
      .dba-suggest {
        height: 32px;
        margin-right: 8px;
      }
    }
  }
}

/deep/ textarea.review-advice {
  background-color: rgba(15, 120, 251, 0.06);
  border: 1px solid transparent;
}
/deep/.ant-collapse-borderless {
  overflow: auto;
  max-height: 160px;
  background-color: #fff;
  .ant-collapse-item {
    &:nth-child(1) {
      .ant-collapse-header {
        padding: 0 0 10px 0;
      }
      .anticon {
        top: 10px;
      }
    }
    .ant-collapse-header {
      font-size: 12px;
      color: #27272a;
      font-weight: 600;
      padding: 10px 0;
      .anticon {
        svg {
          transform: rotate(-90deg);
        }
      }
    }
  }
}
</style>