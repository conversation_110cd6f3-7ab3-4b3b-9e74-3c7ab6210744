<template>
  <div class="trend_container">
    <!-- <div class="header" style="text-align: right;margin-bottom: 16px;">
            <a-range-picker disabled style="width: 360px" :showTime="{
                hideDisabledOptions: true,
                defaultValue: [
                    moment('00:00:00', 'HH:mm:ss'),
                    moment('23:59:59', 'HH:mm:ss')
                ]
            }" v-model:value="search_form.time" :format="['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']" />

        </div> -->
    <el-descriptions title="基础信息">
      <el-descriptions-item label="ID">{{
        base_data.sql_id
      }}</el-descriptions-item>
      <el-descriptions-item label="实例名称">
        <div style="display: flex; align-items: center">
          <custom-icon :type="base_data.iconType[base_data.db_type]" />
          <span> {{ base_data.title }}</span>
        </div>
      </el-descriptions-item>
      <el-descriptions-item :label="base_data.db_type | db_type_filter">{{
        base_data.executive_schema
      }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="1">
      <el-descriptions-item :span="24" label="SQL文本"
        ><span
          style="
            max-width: 800px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: blue;
            cursor: pointer;
          "
          @click="sql_text_btn"
        >
          {{ base_data.sql_text }}</span
        ></el-descriptions-item
      >
    </el-descriptions>
    <div v-show="sql_text_visable">
      <pre
        ref="container"
        style="
          width: 100%;
          white-space: pre-line;
          padding: 16px 24px;
          margin-bottom: 16px;
          background-color: antiquewhite;
        "
        v-html="sql_text_cpt"
      ></pre>
    </div>
    <el-descriptions title="语法&词法诊断"> </el-descriptions>

    <div>
      <div class="yf_box" style="min-height: 100px; font-size: 13px">
        <div class="end_box">
          <p style="margin: 0">诊断结果</p>
          <p
            style="margin: 0; font-size: 20px; color: #ff4d4f; font-weight: 600"
          >
            {{ base_req_data.risk | risk_filter }}
          </p>
        </div>
        <!-- <div style="margin: 8px 0;">
                    <span>SQL执行性能分析：
                    </span>
                    <el-tag type="danger" size="small">高风险</el-tag>
                </div> -->
        <!-- <div style="margin: 8px 0;">
                    <span class="el-icon-warning" style="color: red;"></span>
                    <span>SQL平均执行时长超过500ms</span>
                </div> -->
        <div style="margin: 8px 0; display: flex; align-items: center">
          <span>SQL语法/执行计划分析： </span>
          <el-tag
            v-if="base_req_data.review_status"
            type="danger"
            size="small"
            >{{ base_req_data.review_status | risk_filters }}</el-tag
          >
        </div>
        <p v-for="(item, index) in base_req_data.ai_comment" :key="index">
          {{ item.ai_comment }}
        </p>
      </div>
    </div>

    <el-descriptions title="执行计划"> </el-descriptions>
    <!-- <div>
            <pre style="width:100%;white-space: pre-line;padding: 16px 24px;background-color:antiquewhite;"
                v-html="sql_plan"></pre>
        </div> -->

    <div id="aceEditor" class="editor"></div>

    <el-descriptions style="margin-top: 16px" title="数据库对象">
    </el-descriptions>
    <el-select
      @change="select_change"
      size="small"
      v-model="search_form.table_name"
      placeholder="请选择"
    >
      <el-option
        v-for="(item, index) in table_data"
        :key="index"
        :label="item.table_name"
        :value="item.table_name"
      ></el-option>
    </el-select>
    <el-descriptions style="margin-top: 16px" title="">
      <el-descriptions-item label="表大小">{{
        first_data.table_rows
      }}</el-descriptions-item>
      <el-descriptions-item label="分区类型">{{
        first_data.partition_type
      }}</el-descriptions-item>
      <el-descriptions-item label="分区键">{{
        first_data.part_column
      }}</el-descriptions-item>
      <el-descriptions-item label="是否已上线">{{
        first_data.is_onlie
      }}</el-descriptions-item>
      <el-descriptions-item label="最后采集时间">{{
        first_data.last_collect_time
      }}</el-descriptions-item>
      <el-descriptions-item label="表注释">{{
        first_data.comment
      }}</el-descriptions-item>
    </el-descriptions>
    <div v-if="table_data.length > 0 && first_data.column_list">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="索引信息" name="first">
          <el-table
            :header-cell-style="{
              background: '#fafafa',
              height: '40px',
              padding: '5px 0',
              color: '#000000'
            }"
            :data="
              first_data.index_list.slice(
                (currentPage_index - 1) * pagesize_index,
                currentPage_index * pagesize_index
              )
            "
            style="width: 100%"
            :cell-style="{ padding: '5px 0', width: '200px', height: '40px' }"
          >
            <el-table-column prop="index_name" label="索引名">
            </el-table-column>
            <el-table-column prop="unique_name" label="索引类型">
            </el-table-column>
            <el-table-column prop="column_name" label="索引字段" width="180">
            </el-table-column>
            <el-table-column prop="data_type" label="字段类型">
            </el-table-column>
            <el-table-column prop="column_position" label="索引位置">
            </el-table-column>
            <el-table-column prop="cardinality" label="区分度" width="180">
            </el-table-column>
          </el-table>
          <div class="block">
            <el-pagination
              @size-change="handleSizeChange_index"
              @current-change="handleCurrentChange_index"
              :current-page="currentPage_index"
              :page-sizes="[10, 20]"
              :page-size="pagesize_index"
              layout="  prev, pager, next"
              :total="first_data.index_list.length"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="字段信息" name="second">
          <el-table
            :header-cell-style="{
              background: '#fafafa',
              height: '40px',
              padding: '5px 0',
              color: '#000000'
            }"
            :data="
              first_data.column_list.slice(
                (currentPage - 1) * pagesize,
                currentPage * pagesize
              )
            "
            style="width: 100%"
            :cell-style="{ padding: '5px 0', width: '200px', height: '40px' }"
          >
            <el-table-column prop="column_name" label="字段名">
            </el-table-column>
            <el-table-column prop="column_type" label="字段类型">
            </el-table-column>
            <el-table-column prop="is_nullable" label="可空"> </el-table-column>
            <el-table-column prop="auto_increment" label="自增" width="180">
            </el-table-column>
            <el-table-column prop="column_default" label="缺省值">
            </el-table-column>
            <el-table-column prop="column_comment" label="备注" width="180">
            </el-table-column>
          </el-table>
          <div class="block">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20]"
              :page-size="pagesize"
              layout=" prev, pager, next "
              :total="first_data.column_list.length"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div v-else class="model_box" style=""><span>暂无数据！</span></div>
  </div>
</template>

<script>
import ace from 'ace-builds';
import 'ace-builds/src-noconflict/mode-sql.js'; // SQL 模式
import 'ace-builds/src-noconflict/theme-dracula.js'; // 主题（可选）
import moment from 'moment';
import hljs from 'highlight.js';
import Prism from 'prismjs';
import 'prismjs/components/prism-sql';
import 'prismjs/themes/prism.css'; // 引入主题样式
import * as API from '@/api/databaseaudit/topsqltemp';
export default {
  data() {
    return {
      loading_list: false,
      currentPage: 1, // 初始页
      pagesize: 10, // 初始每页的数据
      currentPage_index: 1, // 初始页
      pagesize_index: 10, // 初始每页的数据
      activeName: 'first',
      sql_text_visable: false,
      table_data: [],
      first_data: { column_list: [], index_list: [] },
      search_form: {},
      chart1: null,
      base_req_data: {},
      sql_plan: '',
      sqlContent: 'null'
    };
  },
  props: ['search_time', 'base_data', 'search_form_to_child'],
  computed: {
    sql_text_cpt() {
      if (this.base_data.sql_text) {
        return hljs.highlight('sql', this.base_data.sql_text).value;
      }
    }
    // base_req_data_sql_plan(){
    //     return hljs.highlight('sql', this.base_req_data.sql_plan).value
    // }
  },
  filters: {
    db_type_filter(e) {
      console.log(e);
      if (e == 'MYSQL' || e == 'POSTGRE' || e == 'postgre' || e == 'TD_MYSQL') {
        return 'Database';
      }

      return 'Schema';
    },
    risk_filters(e) {
      if (e == 0) {
        return '待审核';
      }

      if (e == -1) {
        return '高风险';
      }

      if (e == 1) {
        return '通过';
      }

      return '';
    },
    risk_filter(e) {
      if (e == 'high') {
        return '高风险';
      }

      if (e == 'low') {
        return '低风险';
      }

      if (e == 'error') {
        return '异常';
      }

      if (e == 0) {
        return '待审核';
      }

      if (e == 1) {
        return '无风险';
      }

      return '未知';
    }
  },
  created() {
    this.search_form.time = this.search_time;
  },
  mounted() {
    this.$nextTick(() => {
      Prism.highlightAll();
    });
    this.get_analysis_meta_info();
    if (this.base_data.risk == 1) {
      this.base_req_data.risk = 1;
    }
    if (this.base_data.risk == 0) {
      this.base_req_data.risk = 0;
    }
    if (this.base_data.risk != 1 || this.base_data.risk != 0) {
      this.get_analysis_review_detail();
    }

    this.analysis_explain();
    this.initAceEditor();
    const span = document.createElement('span');
    span.textContent = '';
    span.className = 'el-icon-document-copy my-span-class';
    span.style.cursor = 'pointer';
    span.style.marginLeft = '10px';
    span.addEventListener('click', this.copySql);
    this.$refs.container.appendChild(span);
  },
  beforeDestroy() {
    if (this.chart1) {
      this.chart1.dispose();
    }
  },
  methods: {
    initAceEditor() {
      const editor = ace.edit('aceEditor');
      editor.setTheme('ace/theme/dracula'); // 设置主题
      editor.getSession().setMode('ace/mode/sql'); // 设置语言模式
      editor.setValue(this.sqlContent || 'null'); // 设置初始内容
      editor.setReadOnly(true); // 设置为只读模式

      // 设置初始选中范围为空
      editor.selection.setRange({
        start: { row: 0, column: 0 },
        end: { row: 0, column: 0 }
      });
      // 禁用自动格式化和智能缩进（可选）
      editor.setOptions({
        autoFormatOnWrite: false,
        smartIndent: false
      });
    },
    copySql(e) {
      console.log(e);
      e = this.base_data.sql_text;
      // 创建一个临时的 textarea 元素
      const textarea = document.createElement('textarea');
      textarea.value = e.trim();
      document.body.appendChild(textarea);

      // 执行复制操作
      textarea.select();
      document.execCommand('copy');

      // 移除临时元素
      document.body.removeChild(textarea);
      this.$message.success('复制成功');
    },
    analysis_explain() {
      console.log(this.base_data,"++++++++");
      this.loading_list = true;
      let params = {
        db_type: this.search_form_to_child.db_type,
        data_source_id: this.search_form_to_child.data_source_id,
        schema: this.base_data.executive_schema || '',
        sql_id: this.base_data.sql_id || '',
        sql_text: this.base_data.sql_text || '',
        // "db_type": "ORACLE",
        // "data_source_id": 12,
        // "schema": "LUDBGATESWITCH",
        // "sql_id": "7067b6p8dkhzn",
        // "sql_text": "select updated_at from LUDBGATESWITCH.LUDBGATE_TASK_HEARTBEAT_TABLE where taskid = 435",
        sql_bind_value: this.base_data.sql_bind_value || '',
        source_type: this.base_data.source_type || 0,
      };
      console.log(params);
      API.analysis_explain(params)
        .then((res) => {
          // this.sql_plan = hljs.highlight('sql', res.data.data.sql_plan).value
          this.sqlContent = res.data.data.sql_plan;
          this.initAceEditor();
        })
        .catch((e) => {})
        .finally(() => {
          this.loading_list = false;
        });
    },
    select_change(e) {
      this.currentPage = 1;
      this.pagesize = 10;
      this.currentPage_index = 1;
      this.pagesize_index = 10;
      let arr = this.table_data.filter((item) => {
        return item.table_name == e;
      });

      this.first_data = arr[0];
    },
    get_analysis_meta_info() {
      this.loading_list = true;
      console.log(this.base_data,"++++++++");
      let params = {
        db_type: this.search_form_to_child.db_type,
        data_source_id: this.search_form_to_child.data_source_id,
        schema: this.base_data.executive_schema || '',
        sql_id: this.base_data.sql_id || '',
        sql_text: this.base_data.sql_text || '',
        sql_bind_value: this.base_data.sql_bind_value || '',
        source_type: this.base_data.source_type || 0,

        // "db_type": "ORACLE",
        // "data_source_id": 12,
        // "schema": "LUDBGATESWITCH",
        // "sql_id": "7067b6p8dkhzn",
        // "sql_text": ""
      };
      console.log(params);
      API.get_analysis_meta_info(params)
        .then((res) => {
          if (res.data.data.length > 0) {
            let arr = res.data.data;
            let narr = [];
            for (let index = 0; index < 5; index++) {
              narr.push({ ...arr[0], id_index: index });
            }

            this.table_data = res.data.data;

            this.first_data = this.table_data[0];

            this.search_form.table_name = this.table_data[0].table_name;
          }
        })
        .catch((e) => {})
        .finally(() => {
          this.loading_list = false;
        });
    },
    get_analysis_review_detail() {
      console.log(this.base_data);
      let params = {
        detail_id: this.base_data.detail_id
        // detail_id: 636813
      };
      API.get_analysis_review_detail(params)
        .then((res) => {
          if (res.data.code == 0) {
            this.base_req_data = res.data.data || {};
          }
        })
        .catch((e) => {})
        .finally(() => {
          this.loading_list = false;
        });
    },
    handleClick(tab, event) {},
    moment,
    sql_text_btn() {
      this.sql_text_visable = !this.sql_text_visable;
    },
    handleSizeChange_index: function (size) {
      this.pagesize_index = size;
    },
    handleCurrentChange_index: function (currentPage) {
      this.currentPage_index = currentPage;
    },
    handleSizeChange: function (size) {
      this.pagesize = size;
    },
    handleCurrentChange: function (currentPage) {
      this.currentPage = currentPage;
    }
  }
};
</script>

<style lang="less" scoped>
.yf_box {
  margin-bottom: 32px;
  background: #ffffff;
  box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014,
    0 9px 28px 8px #0000000d;
  border-radius: 8px;
  padding: 16px;
  position: relative;
}

.block {
  margin: 26px 0;
  text-align: right;
}

.sql-viewer {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #333;
  text-align: center;
}

.code-container {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  overflow-x: auto;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

code {
  font-family: 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.end_box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 24px;
  width: 124px;
  height: 66px;
  text-align: center;
  background: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
}

.model_box {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0;
  height: 240px;
  align-items: center;
}

.editor {
  width: 100%;
  height: 400px;
  margin: 20px 0;
  border: 1px solid #ccc;
  border-radius: 4px;
}
</style>