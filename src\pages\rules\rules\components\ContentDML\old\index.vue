<template>
  <div class="config-rules-content">
    <!-- 预加载括号 -->
    <Bracket color="#F29339" style="display: none" />
    <!-- 基础信息 -->
    <div class="rules-content-base-info">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <a-icon type="profile" />基础信息 </span>
        <Form
          ref="baseInfo"
          class="base-info-form"
          v-bind="baseInfoParams"
          :formData="baseInfoData"
        >
          <DataSourceBlocks
            ref="dbType"
            slot="db_type"
            @choose="choose"
            :itemWidth="120"
            :dbType="baseInfoData.db_type"
            :disabled="type !== 'add'"
          />
        </Form>
      </a-card>
    </div>

    <!-- 条件 -->
    <div class="rules-content-condition" :data-row="buttonNumber">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <custom-icon type="lu-icon-if" />条件区域 </span>
        <TableEdit
          @change="onChangeNumber"
          ref="tableEdit"
          v-bind="tableParams || {}"
          :dataSource="tableData"
        ></TableEdit>
        <div
          :class="['btn-add-condition', tableData.length === 1 && 'only-one']"
        >
          <a-button
            icon="plus"
            @click="addCondition"
            v-if="type !== 'detail'"
          ></a-button>
        </div>
      </a-card>
    </div>
    <EditModal ref="EditModal" @save="onConditionSave" :dbType="dbType" />

    <!--输出结果  -->
    <div class="rules-outport-results">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <custom-icon type="lu-icon-do" />输出结果 </span>
        <Form
          ref="outportResults"
          class="rules-outport-results-form"
          v-bind="outportResultsParams"
          :formData="outportResultsData"
        />
      </a-card>
    </div>
  </div>
</template>
<script>
import Form from '@/components/Form';
import TableEdit from '@/components/TableEdit';
import Select from '@/components/Select';
import EditModal from '../../EditModal';
import Bracket from '@/components/Biz/Bracket';
import DataSourceBlocks from '@/components/Biz/DataSourceBlocks';
import MarkdownViewer from '@/components/Markdown/viewer';
import config from './config';
import { getDMLRuleSetList } from '@/api/config/rule';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Form,
    TableEdit,
    Select,
    EditModal,
    Bracket,
    DataSourceBlocks,
    MarkdownViewer
  },
  props: {
    type: {
      type: String,
      default: 'add'
    },
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    },
    rule_type: {
      type: String,
      default: ''
    }
  },
  data() {
    this.config = config(this);
    let dbType = window.localStorage.getItem('db_type') || '';
    return {
      dbType,
      baseInfoData: { rule_type: 'DML', db_type: dbType, ob_mode: null },
      buttonNumber: 1,
      baseInfoParams: {
        fixedLabel: true,
        // gutter: 32,
        // multiCols: 1,
        layout: 'horizontal',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        fields: this.config.baseInfo
      },
      tableData: [],
      tableParams: {
        initEditStatus: true,
        actionBtns: this.type !== 'detail' ? ['remove'] : [],
        editConfig: this.config.condition.editConfig(),
        columns: this.config.condition.columns,
        pagination: false,
        leastNum: 1,
        rowKey: 'id'
      },
      ruleSetParams: {
        fields: this.config.ruleSetFields,
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 0 },
        wrapperCol: { span: 24 }
      },
      ruleSetData: {},
      value: false,
      rule_setList: [],
      outportResultsParams: {
        fixedLabel: true,
        gutter: 32,
        multiCols: 1,
        layout: 'horizontal',
        fields: this.config.outportResults
      },
      outportResultsData: {},
      rulesOptions: [],
      ruleType: {
        1: 'SQL 语法规范',
        2: 'SQL 执行规范',
        3: 'SQL 执行计划'
      }
    };
  },
  created() {},
  mounted() {
    const { EditModal } = this.$refs;
    this.$bus.$on('input-modal', data => {
      EditModal.show(this.type, data);
    });
    this.getDMLRuleSetListFn();
  },
  destroyed() {
    this.$bus.$off('input-modal');
  },
  methods: {
    change(e) {
      this.$set(
        this.outportResultsData,
        'suggest',
        this.$el.querySelector('.source').value
      );
    },
    onChange(e) {
      this.value = e.target.checked;
    },
    onChangeNumber(data) {
      this.buttonNumber = data.length;
    },
    getRowNumber() {
      const { tableEdit } = this.$refs;
      console.log(tableEdit);
    },
    addCondition() {
      const { tableEdit } = this.$refs;
      tableEdit.addLine();
    },
    onConditionSave(params) {
      const { type, data = {} } = params;
      const { selectedRows, sourceData = {} } = data;
      const { editRecord, editRow } = sourceData;
      const { tableEdit, EditModal } = this.$refs;
      console.log(params, type, editRow, 666);

      tableEdit.saving({
        // ...editRecord,
        id: editRecord.id,
        target: selectedRows[0].code,
        desc: selectedRows[0].desc,
        value_operator: selectedRows[0].value_operator,
        value_type: selectedRows[0].value_type,
        value_props: selectedRows[0].value_props,
        condition: null,
        target_value: null
      });
      EditModal.hide();
    },
    getData() {
      const { tableEdit, baseInfo, outportResults } = this.$refs;
      return new Promise((resolve, reject) => {
        Promise.all([
          tableEdit.validate(),
          baseInfo.validate(),
          outportResults.validate()
          // output.validate()
        ])
          .then(values => {
            const outportResultsData = outportResults.getData();
            const conditions = tableEdit.getData();
            if (!conditions || conditions.length <= 0) {
              this.$message.error('条件不能为空！');
              reject(new Error('条件不能为空！'));
            }
            resolve({
              ...baseInfo.data,
              db_type: baseInfo.data.ob_mode
                ? baseInfo.data.ob_mode
                : baseInfo.data.db_type,
              ...outportResultsData,
              details: conditions.map(item => {
                if (this.type === 'add') {
                  delete item['id'];
                }
                return item;
              })
            });
          })
          .catch(e => {
            reject(e);
          });
      });
    },
    // 数据库选择
    choose(value) {
      this.dbType = value;
      const baseInfo = this.$refs.baseInfo;
      baseInfo.saving({
        db_type: value,
        ob_mode: null,
        rule_set_uids: null
      });
      // this.getDMLRuleSetListFn(value);
    },
    getDMLRuleSetListFn(data = '') {
      const params = {
        db_type: data || this.dbType
      };
      getDMLRuleSetList(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            const resData = _.get(res, 'data.data');
            this.rulesOptions = resData;
            if (this.type !== 'detail') {
              const { baseInfo } = this.$refs;
              baseInfo.refresh();
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  },
  watch: {
    dataSource: {
      handler(newVal, oldVal) {
        if (newVal.db_type == 'OB_MYSQL' || newVal.db_type == 'OB_ORACLE') {
          this.baseInfoData.db_type = 'OCEANBASE';
        } else if (
          newVal.db_type == 'TD_MYSQL' ||
          newVal.db_type == 'TD_PGSQL'
        ) {
          this.baseInfoData.db_type = 'TDSQL';
        } else {
          this.baseInfoData.db_type = newVal.db_type;
        }
        const { tableEdit } = this.$refs;
        this.baseInfoData = {
          ...this.baseInfoData,
          name: newVal.name,
          desc: newVal.desc,
          type: newVal.type === 9 ? '待确定' : newVal.type,
          level: newVal.level,
          rule_type: newVal.rule_set_type,
          // db_type: newVal.db_type,
          ob_mode: newVal.db_type,
          rule_set_uids: newVal.rule_set_uids
        };
        this.outportResultsData = {
          rule_result: newVal.rule_result,
          suggest: newVal.suggest
        };
        // this.outputData = {
        //   key: 0,
        //   value: newVal.rule_result
        // };
        if (newVal.rule_set.length !== 0) {
          this.value = true;
          this.ruleSetData = {
            rule_set: newVal.rule_set,
            rule_set_name: newVal.rule_set_name
          };
          this.rule_setList = this.ruleSetData.rule_set_name || [];
        }
        this.tableData = newVal.details;
        this.buttonNumber = newVal.details.length;
        tableEdit.editAll();
      }
      // immediate: true,
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.config-rules-content {
  .rules-content-base-info {
    /deep/ .common-pure-card {
      .ant-card-body {
        .base-info-form {
          .ant-form-item {
            display: flex;
            padding-right: 16px;
            margin: 0 0 12px 0;
            .ant-form-item-label {
              padding-right: 16px;
              align-items: start;
              width: 100px;
              flex-shrink: 0;
              top: 5px;
              > label {
                justify-content: flex-start;
                > span {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #27272a;
                  font-weight: 400;
                }
              }
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              .ant-form-item-children {
                .ant-row-flex-space-around {
                  .backgroundBlock {
                    .topBlock {
                      .text {
                        line-height: 20px;
                      }
                    }
                  }
                }
                .ant-input {
                  width: 700px !important;
                  height: 36px;
                  border: 1px solid #ebebec;
                  margin-bottom: 8px;
                }
                textarea.ant-input {
                  max-width: 700px !important;
                  height: 72px;
                }
                .ant-select {
                  width: 700px !important;
                  margin-bottom: 8px;
                  .ant-select-selection {
                    height: 36px;
                    border: 1px solid #ebebec;
                    .ant-select-selection__rendered {
                      > ul > li {
                        margin-top: 5px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .rules-content-condition {
    /deep/ .common-pure-card {
      .ant-card-body {
        .table-edit {
          /** 修改条件区域样式 */
          .ant-table-tbody > tr {
            .ant-select-selection {
              transition: none;
            }
            > td {
              border-bottom: 0 !important;
              &:not(:first-child):not(:last-child) {
                padding: 12px 0;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  height: 50px;
                  box-shadow: 4px 4px 3px 0px #f5f5f5;
                  border-color: #ebebec;
                }
                .ant-select-selection {
                  .ant-select-selection__rendered {
                    line-height: 48px;
                    .ant-select-selection-selected-value {
                      height: 48px;
                    }
                  }
                }
              }
              &:nth-child(2) {
                padding-left: 12px !important;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 8px 0 0 8px;
                  border-right: 0;
                  box-shadow: 0px 0px 6px 2px #e8e8e8;
                }
              }
              &:nth-child(3) {
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 0;
                }
              }
              &:nth-child(4) {
                padding-right: 12px !important;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 0 8px 8px 0;
                  border-left: 0;
                }
              }
              .biz-rule-range {
                top: 0;
                > *:not(:last-child) {
                  margin-right: 0;
                }
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  box-shadow: 0;
                }
                .ant-select:nth-child(1) {
                  .ant-select-selection {
                    border-radius: 0;
                    border-right: 0;
                  }
                }
                .ant-select:nth-child(2) {
                  .ant-select-selection {
                    border-radius: 0;
                    border-top: 1px solid #e8e8e8;
                    border-bottom: 1px solid #e8e8e8;
                    border-left: 1px solid #f5f5f5;
                    border-right: 1px solid #f5f5f5;
                  }
                }
              }
            }
            &:first-child {
              td:first-child {
                .ant-form-item {
                  visibility: hidden;
                  display: none;
                }
              }
            }
            td:first-child {
              padding-right: 35px;
              background-color: #fff !important;
              .ant-form-item {
                position: relative;
                .ant-form-item-control {
                  position: relative;
                  top: -34px;
                  // border-right: 1px dashed #eee;
                  // &:after {
                  //   content: ' ';
                  //   position: absolute;
                  //   width: 10px;
                  //   height: 0;
                  //   top: -3px;
                  //   right: -10px;
                  //   transform: rotate(-30deg);
                  //   border-bottom: 1px dashed #eee;
                  // }
                  // &:before {
                  //   content: ' ';
                  //   position: absolute;
                  //   width: 10px;
                  //   height: 0;
                  //   border-bottom: 1px dashed #eee;

                  //   bottom: -3px;
                  //   right: -10px;

                  //   transform: rotate(30deg);
                  // }
                }
                .ant-select {
                  width: 75px;
                  .new-style(@color) {
                    .ant-select-selection {
                      border-color: @color;
                      color: @color;
                      box-shadow: none;
                      border-radius: 16px;
                      .ant-select-arrow {
                        color: @color;
                      }
                    }
                  }
                  &.relation-and {
                    .new-style(#4cbb3a);
                  }
                  &.relation-or {
                    .new-style(#F29339);
                  }
                }
                .biz-bracket {
                  position: absolute;
                  right: -36px;
                  top: -15px;
                }
              }
            }
          }
          thead {
            display: none;
          }
          .ant-table-tbody
            > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
            > td {
            background: transparent;
          }
          .ant-table-content td {
            padding: 10px 16px;
          }
          .ant-table-placeholder {
            display: none;
          }
        }
      }
    }
    /deep/ .ant-card-body {
    }
    &[data-row='1'] {
      .btn-add-condition {
        position: absolute;
        bottom: 44px;
      }
      /deep/ .table-edit {
        colgroup {
          > col:first-child {
            width: 75px !important;
          }
        }
        .ant-table-tbody {
          > tr {
            &:first-child {
              td:first-child {
                padding: 0;
              }
            }
          }
        }
      }
    }
    /deep/.btn-add-condition {
      border-radius: 16px;
      margin: 10px 16px;
      font-size: 14px;
      .ant-btn {
        height: 28px;
        width: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #4db5f2;
        border-radius: 4px !important;
        transition: none;
        font-size: 14px;
        .anticon {
          line-height: 0;
          color: #4db5f2;
        }

        &:hover {
          background: #4db5f2;
          .anticon {
            color: #fff;
          }
        }
      }
    }
  }
}
.rules-outport-results {
  /deep/ .common-pure-card {
    .ant-card-body {
      .ant-form {
        display: flex;
        padding: 0 16px;
        .ant-col {
          padding: 0 !important;
          .ant-form-item {
            display: flex;
            padding-right: 16px;
            align-items: start;
            margin: 0;
            .ant-form-item-label {
              margin-right: 32px;
              padding-right: 8px;
              flex-shrink: 0;
            }
            .ant-form-item-control-wrapper {
              width: 100%;
              .ant-form-item-control {
                line-height: 1.5;
                .custom-markdown {
                  .toastui-editor-defaultUI {
                    max-width: 1250px;
                    border: none;
                    position: relative;
                    // .toastui-editor-toolbar {
                    //   border-radius: 8px 8px 0 0;
                    //   background: #e4e4e7;
                    // }
                    .toastui-editor-toolbar {
                      .toastui-editor-defaultUI-toolbar {
                        border: 1px solid #ebebec;
                        border-radius: 8px 8px 0 0;
                        border-bottom: none;
                      }
                    }
                    .toastui-editor-main {
                      border: 1px solid #ebebec;
                      border-radius: 0 0 8px 8px;
                      .toastui-editor {
                        .ProseMirror {
                          height: 100%;
                        }
                      }
                      .toastui-editor-md-preview {
                        .toastui-editor-contents {
                          padding-bottom: 24px;
                        }
                      }
                    }
                    .toastui-editor-mode-switch {
                      border-top: none;
                      position: absolute;
                      top: -32px;
                      right: 0;
                      // padding: 19px 0 0 0;
                      background: transparent;
                      .tab-item {
                        border: none;
                        border-radius: 8px;
                        margin-left: 8px;
                        &:hover {
                          background: #7fc4ed;
                          color: #fff;
                        }
                        &.active {
                          background: #7fc4ed;
                          color: #fff;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          &:last-child {
            .ant-form-item {
              .ant-form-item-label {
                .ant-form-item-no-colon {
                  margin-left: 12px;
                }
              }
            }
          }
          &.suggest {
            .ant-form-item {
              flex-direction: column;
              .ant-col .ant-form-item-no-colon {
                margin-left: 0;
                > span {
                  margin-right: 4px;
                  &::after {
                    content: '(支持MarkDown语法)';
                    color: #a1a1aa;
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
