<template>
  <a-date-picker
    v-bind="pickerProps"
    v-on="pickerListeners"
    format="YYYY"
    mode="year"
    :value="year"
    :open="isOpen"
    @openChange="onOpenChange"
    @panelChange="onPanelChange"
    @change="onChange"
  ></a-date-picker>
</template>

<script>
import moment from 'moment';
const defaultProps = {};
export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {},
  props: {
    value: String
  },
  data() {
    return {
      year: undefined,
      isOpen: false
    };
  },
  computed: {
    pickerProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    pickerListeners() {
      return { ...this.$listeners };
    }
  },
  created() {},
  mounted() {},
  methods: {
    onOpenChange(status) {
      this.isOpen = status;
    },
    onPanelChange(value) {
      this.year = value;
      this.isOpen = false;
      this.$emit('change', value.format('YYYY'));
    },
    onChange(e) {
      this.year = undefined;
      this.$emit('change', undefined);
    }
  },
  watch: {
    value: {
      handler(newVal, oldVal) {
        this.year = newVal ? moment(newVal) : undefined;
      },
      immediate: true
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
</style>
