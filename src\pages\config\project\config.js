export default function (ctx) {
  const isTaiLong = process.channel === 'TaiLongBank';
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '项目组名',
      dataIndex: 'group_name',
      key: 'group_name',
      width: 160,
      scopedSlots: { customRender: 'group_name' }
    },
    {
      title: '系统英文简称',
      dataIndex: 'en_name',
      key: 'en_name',
      visible: isTaiLong,
      width: 160
    },
    {
      title: isTaiLong ? '中文简称' : '项目名',
      dataIndex: 'name',
      key: 'name',
      width: 160
    },
    {
      title: '数据源',
      dataIndex: 'data_source_name',
      key: 'data_source_name',
      scopedSlots: { customRender: 'dataSource' },
      width: 280
    },
    {
      title: '测试源',
      dataIndex: 'test_data_source_name',
      key: 'test_data_source_name',
      scopedSlots: { customRender: 'testDataSource' },
      width: 200
    },
    {
      title: 'review方式',
      dataIndex: 'review_type',
      key: 'review_type',
      width: 160
    },
    // {
    //   title: 'review白名单',
    //   dataIndex: 'white_list',
    //   key: 'white_list',
    //   // scopedSlots: { customRender: 'white_list' },
    //   customRender: (text) => {
    //     return text.length ? text.toString() : '-';
    //   },
    //   width: 200
    // },
    // {
    //   title: 'review黑名单',
    //   dataIndex: 'black_list',
    //   key: 'black_list',
    //   // scopedSlots: { customRender: 'black_list' },
    //   customRender: (text) => {
    //     return text.length ? text.toString() : '-';
    //   },
    //   width: 200
    // },
    {
      title: 'git地址',
      dataIndex: 'review_url',
      key: 'review_url',
      scopedSlots: { customRender: 'review_url' },
      width: 200
    },
    {
      title: '账号',
      dataIndex: 'user',
      key: 'user',
      width: 200
    },
    {
      title: '最后一次review时间',
      dataIndex: 'last_review_time',
      key: 'updated_at',
      scopedSlots: { customRender: 'updated_at' },
      width: 200
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 110,
      fixed: 'right',
      visible: $permissionBatch.some([
        { module: 'project', values: ['edit', 'delete'] }
      ])
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const baseInfo = () => {
    return [
      {
        type: 'Select',
        label: '项目组名',
        key: 'group_name',
        width: '500',
        props: {
          url: '/sqlreview/project_config/select_project_group',
          reqParams: {},
          mode: 'multiple',
          allowClear: false,
          allowSearch: true,
          backSearch: true,
          backSearchOnlyOnSearch: true,
          limit: 20,
          getPopupContainer: (el) => {
            return el.parentNode;
          }
        }
      },
      // 项目名称
      {
        type: 'Input',
        label: '项目名',
        key: 'name',
        width: '500',
        className: 'group-end',
        rules: [{ required: true, message: '该项为必填项' }]
      },
      // 指定数据源/schema
      {
        type: 'TableEdit',
        label: '指定数据源/schema',
        key: 'data_sourceSchema_list',
        // width: '500',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        className: 'group-start',
        rules: [{ required: false, message: '该项为必填项' }],
        props: {
          mode: 'list',
          columns: [
            {
              title: 'id',
              dataIndex: 'id',
              key: 'id',
              scopedSlots: { customRender: 'draggable' }
            },
            {
              title: '数据源',
              dataIndex: 'datasource_id',
              key: 'datasource_id',
              scopedSlots: { customRender: 'datasource_id' }
            },
            {
              title: 'schema',
              dataIndex: 'schema',
              key: 'schema',
              scopedSlots: { customRender: 'schema' }
            },
            {
              title: '操作',
              key: 'action',
              scopedSlots: { customRender: 'action' }
            }
          ],
          editConfig: {
            datasource_id: (row, record = {}) => {
              return {
                type: 'DataBaseChoose',
                props: {
                  url: '/sqlreview/project/data_source_choices',
                  reqParams: {
                    restrict: 'restrict'
                  },
                  loaded(data) {
                    ctx.dataSourceOption = data;
                  },
                  beforeLoaded(data) {
                    return data.map((item) => {
                      return {
                        ...item,
                        instance_usage: item.env,
                        showText: item.label + '(' + item.db_url + ')'
                      };
                    });
                  },
                  mode: 'default',
                  optionLabelProp: 'children',
                  // isClear: false,
                  // needConfirm: false,
                  // backSearchOnlyOnSearch: true,
                  allowSearch: true,
                  backSearch: true,
                  limit: 30
                },
                rules: [{ required: true, message: '该项为必填项' }],
                listeners: {
                  change: (value) => {
                    const { baseInfo } = ctx.$refs;
                    const table = baseInfo.$refs.data_sourceSchema_list[0];
                    if (value) {
                      const env = ctx.dataSourceOption.filter(
                        (item) => item.value === value
                      )[0].env;
                      table.saving({
                        id: record.id,
                        datasource_id: value,
                        env: env,
                        schema: null
                      });
                    } else {
                      table.saving({
                        id: record.id,
                        datasource_id: null,
                        env: '--',
                        schema: null
                      });
                    }
                  }
                }
              };
            },
            schema: (row, record = {}) => {
              return {
                type: 'Select',
                props: {
                  mode: 'multiple',
                  url: '/sqlreview/project/get_schema_list',
                  reqParams: { datasource_id: record.datasource_id || '' },
                  backSearchOnlyOnSearch: true,
                  allowSearch: true,
                  backSearch: true,
                  limit: 30
                },
                rules: [{ required: true, message: '该项为必填项' }]
              };
            }
          },
          rowKey: 'id',
          initEditStatus: true,
          pagination: false,
          draggable: true,
          leastNum: 1,
          actionBtns: ['add', 'remove'],
          actionBtnsIcons: {
            add: 'plus-circle',
            remove: 'close-circle'
          }
        }
      },
      {
        type: 'RadioGroup',
        label: '数据源扫描策略',
        key: 'scan_strategy',
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }],
        props: {
          options: [
            { label: '顺序扫描', value: 0 },
            { label: '全部扫描', value: 1 }
          ]
        },
        tips:
          '顺序扫描： 按数据源顺序进行扫描。获取到执行计划后，不扫描后续数据源。<br/>全部扫描： 扫描数据源配置的全部环境。'
      },
      // 数据源权限检测报错信息
      {
        type: 'Input',
        label: ' ',
        key: 'check',
        clon: false,
        visible: ctx.checkStatus == true,
        hideComponent: true,
        className: 'check',
        slots: [{ key: 'check' }]
      },
      // 绑定规则集
      {
        type: 'Select',
        label: '绑定规则集',
        key: 'rule_set',
        className: 'group-end',
        width: '500',
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }],
        props: {
          url: '/sqlreview/project/rule_set_all',
          mode: 'multiple'
        }
      },
      {
        type: 'RadioGroup',
        label: '项目地址',
        key: 'review_type',
        className: 'group-start',
        width: '500',
        props: {
          mode: 'button',
          options: [
            {
              label: 'git',
              value: 'git'
            },
            {
              label: 'svn',
              value: 'svn'
            },
            {
              label: 'agent',
              value: 'agent'
            },
            {
              label: 'clearcase',
              value: 'clearcase'
            },
            {
              label: '无',
              value: 'none'
            }
          ]
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }],
        listeners: {
          change: (value) => {
            const { baseInfo } = ctx.$refs;
            baseInfo.saving({
              review_type: value,
              cc_ip: undefined,
              cc_port: undefined,
              cc_user: undefined,
              cc_pwd: undefined,
              ssh_cmd: undefined,
              attachment_path: undefined
            });
          }
        }
      },
      (formData = {}) => {
        return {
          type: 'TableEdit',
          label: 'java进程名称',
          key: 'project_master',
          getDataMethod: 'getData',
          resetFieldsMethod: 'resetFields',
          initialValue: [],
          className: 'group-end',
          visible: formData.review_type == 'agent',
          rules: [{ required: false, message: '该项为必填项' }],
          props: {
            mode: 'list',
            columns: [
              {
                dataIndex: 'id',
                key: 'id',
                scopedSlots: { customRender: 'draggable' }
              },
              {
                dataIndex: 'process_name',
                key: 'process_name',
                scopedSlots: { customRender: 'process_name' }
              },
              {
                dataIndex: 'master_id',
                key: 'master_id',
                scopedSlots: { customRender: 'master_id' }
              },
              {
                title: '操作',
                key: 'action',
                scopedSlots: { customRender: 'action' }
              }
            ],
            editConfig: {
              process_name: (row, record = {}) => {
                return {
                  type: 'Select',
                  props: {
                    url: '/sqlreview/agent/process',
                    reqParams: {},
                    mode: 'default',
                    allowSearch: true,
                    backSearch: true,
                    limit: 30
                  },
                  rules: [{ required: true, message: '该项为必填项' }],
                  listeners: {
                    change: (value) => {
                      const { baseInfo } = ctx.$refs;
                      const table = baseInfo.$refs.project_master[0];
                      if (value) {
                        table.saving({
                          id: record.id,
                          process_name: value,
                          master_id: null
                        });
                      } else {
                        table.saving({
                          id: record.id,
                          process_name: null,
                          master_id: null
                        });
                      }
                    }
                  }
                };
              },
              master_id: (row, record = {}) => {
                return {
                  type: 'Select',
                  props: {
                    mode: 'multiple',
                    url: '/sqlreview/agent/master',
                    reqParams: {
                      process_name: record.process_name || ''
                    },
                    backSearchOnlyOnSearch: true,
                    allowSearch: true,
                    backSearch: true,
                    limit: 30,
                    beforeLoaded(data) {
                      if (data.length <= 0) {
                        return [{
                          label: '没有找到选项时，请安装并启动agent master',
                          value: 'empty',
                          disabled: true,
                          class: 'custom-select-search-tips'
                        }]
                      }
                      return data
                    }
                  },
                  listeners: {
                    change: (value) => {
                      const { baseInfo } = ctx.$refs;
                      const table = baseInfo && baseInfo.$refs.project_master[0];
                      if (value && value.includes(0)) {
                        table.saving({
                          id: record.id,
                          master_id: [0]
                        });
                      } else if (value && !value.includes(0)) {
                        table.saving({
                          id: record.id,
                          master_id: value.filter(item => item !== 0)
                        });
                      } else {
                        table.saving({
                          id: record.id,
                          master_id: null
                        });
                      }
                    }
                  },
                  rules: [{ required: true, message: '该项为必填项' }]
                };
              }
            },
            rowKey: 'id',
            initEditStatus: true,
            pagination: false,
            draggable: true,
            leastNum: 1,
            actionBtns: ['add', 'remove'],
            actionBtnsIcons: {
              add: 'plus-circle',
              remove: 'close-circle'
            }
          }
        }
      },
      // cc 项目下字段 start
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'CC中转服务器IP',
          key: 'cc_ip',
          width: '500',
          visible: formData.review_type == 'clearcase',
          rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'CC中转服务器端口',
          key: 'cc_port',
          width: '500',
          visible: formData.review_type == 'clearcase',
          rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'CC中转服务器账号',
          key: 'cc_user',
          width: '500',
          visible: formData.review_type == 'clearcase',
          rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'InputPassword',
          label: 'CC中转服务器密码',
          key: 'cc_pwd',
          width: '500',
          visible: formData.review_type == 'clearcase',
          rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'SSH命令',
          key: 'ssh_cmd',
          width: '500',
          visible: formData.review_type == 'clearcase',
          rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'CC附件共用路径',
          key: 'attachment_path',
          width: '500',
          className: 'group-end',
          visible: formData.review_type == 'clearcase',
          rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
        };
      },
      // cc 项目下字段 end
      // git 项目下字段 start
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: 'git扫描方式',
          key: 'git_type',
          width: '500',
          visible: formData.review_type == 'git',
          props: {
            mode: 'button',
            options: [
              {
                label: 'tag',
                value: 'tag'
              },
              {
                label: 'branch',
                value: 'branch'
              }
            ]
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'change' }],
          listeners: {
            change: (value) => {
              const { baseInfo } = ctx.$refs;
              baseInfo.saving({
                git_type: value
              });
            }
          }
        }
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'git地址',
          key: 'review_url',
          width: '500',
          visible: formData.review_type == 'git',
          hideComponent: true,
          slots: [{ key: 'git_address' }],
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'svn地址',
          key: 'review_url',
          width: '500',
          visible: formData.review_type == 'svn',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      // (formData = {}) => {
      //   return {
      //     type: 'Textarea',
      //     label: 'id_rsa公钥',
      //     key: 'ssh_id_rsa',
      //     width: '500',
      //     visible: formData.review_type == 'git' && formData.pull_type == 'ssh',
      //     rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      //   };
      // },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'tag白名单',
          tips:
            '<div>请使用正则表达式，如与review点匹配成功则该次发起直接通过</div>',
          key: 'review_tag',
          width: '500',
          visible: formData.review_type == 'git' && formData.git_type == 'tag'
          // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '分支白名单',
          tips:
            '<div>请使用正则表达式，如与review点匹配成功则该次发起直接通过</div>',
          key: 'review_tag',
          width: '500',
          visible: formData.review_type == 'git' && formData.git_type == 'branch'
          // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '账号',
          key: 'user',
          width: '500',
          visible: (formData.review_type == 'git' && formData.pull_type !== 'ssh') || formData.review_type == 'svn',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'InputPassword',
          label: '密码',
          key: 'password',
          className: 'group-end',
          width: '500',
          visible: (formData.review_type == 'git' && formData.pull_type !== 'ssh') || formData.review_type == 'svn',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Switch',
          label: '拦截开关',
          key: 'intercept_switch',
          className: formData.review_type == 'none' || formData.review_type == null ? '' : 'group-start',
          props: {
            size: 'default',
            'checked-children': '开',
            'un-checked-children': '关'
          },
          width: 50
          // rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
        }
      },
      {
        type: 'TableEdit',
        label: 'review白名单',
        tips: `<div>配置文件或目录，仅操作该文件或目录下文件及子目录文件评审扫描；
      <div style='margin-top:8px;'>精确匹配：填写从项目根目录开始的完整路径（不包括项目根目录）: /module/script/sql_file.sql</div>
      <div style='margin-top:8px;'>模糊匹配：请输入'reg:'+正则表达式，例如，文件夹输入reg:.*dir/ 文件输入reg:.*sql_file\\.sql 若输入reg:.*name.* 则会将所有路径中含name的文件设置为白名单</div>
      </div>`,
        key: 'white_list',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        props: {
          columns: [
            {
              dataIndex: 'path',
              key: 'path',
              width: 250,
              scopedSlots: { customRender: 'path' }
            },
            {
              key: 'action',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ],
          editConfig: {
            path: (row, record = {}) => {
              return {
                type: 'Input',
                props: {
                  size: 'default',
                  placeholder: 'script/sql_file.sql 或者 script'
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          mode: 'list',
          initEditStatus: true,
          pagination: false,
          leastNum: 1,
          actionBtns: ['add', 'remove'],
          actionBtnsIcons: {
            add: 'plus-circle',
            remove: 'close-circle'
          }
        }
      },
      {
        type: 'TableEdit',
        label: 'review黑名单',
        tips: `<div>配置文件或目录，该文件或目录下文件及子目录文件不做评审扫描；
      <div style='margin-top:8px;'>精确匹配：填写从项目根目录开始的完整路径（不包括项目根目录）: /module/script/sql_file.sql</div>
      <div style='margin-top:8px;'>模糊匹配：请输入'reg:'+正则表达式，例如，文件夹输入reg:.*dir/ 文件输入reg:.*sql_file\\.sql 若输入reg:.*name.* 则会将所有路径中含name的文件设置为黑名单</div>
      </div>`,
        key: 'black_list',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        props: {
          columns: [
            {
              dataIndex: 'path',
              key: 'path',
              width: 250,
              scopedSlots: { customRender: 'path' }
            },
            {
              key: 'action',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ],
          editConfig: {
            path: (row, record = {}) => {
              return {
                type: 'Input',
                props: {
                  size: 'default',
                  placeholder: 'script/sql_file.sql 或者 script'
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          mode: 'list',
          initEditStatus: true,
          pagination: false,
          leastNum: 1,
          actionBtns: ['add', 'remove'],
          actionBtnsIcons: {
            add: 'plus-circle',
            remove: 'close-circle'
          }
        }
      }
    ];
  };

  const fields = [
    {
      type: 'Input',
      label: '项目名',
      key: 'name',
      mainSearch: true,
      props: {
        placeholder: '请输入项目名'
      }
    },
    {
      type: 'Select',
      label: '项目组名',
      key: 'group_name',
      props: {
        url: '/sqlreview/project_config/select_project_group'
      }
    },
    {
      type: 'Select',
      label: 'review方式',
      key: 'review_type',
      props: {
        options: [
          {
            label: 'git tag',
            value: 'tag'
          },
          {
            label: 'git branch',
            value: 'branch'
          },
          {
            label: 'svn',
            value: 'svn'
          },
          {
            label: 'agent',
            value: 'agent'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '数据源',
      key: 'data_source',
      sourceKey: 'data_source_name',
      props: {
        url: '/sqlreview/project/data_source_choices',
        allowSearch: true,
        backSearch: true,
        limit: 50
      }
    }
  ];
  const gitFormFields = () => {
    return [
      {
        type: 'Select',
        label: '',
        key: 'pull_type',
        width: '100',
        props: {
          options: [
            {
              label: 'http',
              value: 'http'
            },
            {
              label: 'https',
              value: 'https'
            },
            {
              label: 'ssh',
              value: 'ssh'
            }
          ]
        },
        listeners: {
          change: (value) => {
            const { baseInfo } = ctx.$refs;
            baseInfo.saving({
              pull_type: value
            });
          }
        }
      },
      {
        type: 'Input',
        label: '',
        key: 'review_url',
        width: '380',
        props: {
          placeholder: '请输入'
        },
        listeners: {
          blur: (e) => {
            const { baseInfo } = ctx.$refs;
            baseInfo.saving({
              review_url: e.target.value
            });
          }
        }
      }
    ];
  };
  return {
    columns,
    fields,
    baseInfo,
    gitFormFields
  };
}
