<template>
  <div :class="className">
    <!-- 当前节点 -->
    <div class="h-tree-item-node" @click="selectNode()">
      <span
        class="tree-item-node-label custom-bg"
        :style="{ background: data.color || 'transparent' }"
      >{{data.label}}</span>
      <span class="item-node-operate">
        <slot name="operate" v-bind="{ node: data }"></slot>
      </span>
      <a-icon
        class="tree-item-node-expand-icon"
        :type="!data.expanded ? 'plus-circle' : 'minus-circle'"
        @click.stop="expand()"
        v-if="!data.isLeaf"
      ></a-icon>
    </div>
    <!-- 子节点 -->
    <div class="h-tree-item-children" v-if="data.children && data.children.length > 0">
      <template v-for="item in data.children">
        <HTreeItem :key="item.value" :value="item" @select="onSelect"></HTreeItem>
      </template>
    </div>
  </div>
</template>

<script>
// import common from '@/utils/common';
// import _ from 'lodash';

export default {
  name: 'HTreeItem',
  inheritAttrs: false,
  components: {},
  props: {
    value: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const { value } = this;
    return {
      data: value
    };
  },
  computed: {
    className() {
      const { data } = this;
      return [
        'h-tree-item',
        data.isRoot && 'is-root-item',
        data.isLeaf && 'is-leaf-item',
        data.isFirst && 'is-first-item',
        data.isLast && 'is-last-item',
        data.expanded ? 'is-expand-item' : 'is-fold-item',
        data.selectable !== false && 'is-selectable-item'
      ].filter(item => item);
    }
  },
  created() {},
  mounted() {},
  methods: {
    expand() {
      this.data = Object.assign({}, this.data, {
        expanded: !this.data.expanded
      });
    },
    selectNode() {
      const { data } = this;
      if (data.selectable !== false) {
        this.$emit('select', data);
      }
    },
    onSelect(data) {
      this.$emit('select', data);
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
@nodePadding: 12px;
@childrenPadding: 12px;
.h-tree-item {
  display: flex;
  cursor: default;
  .h-tree-item-node {
    position: relative;
    display: flex;
    align-items: center;
    padding: @nodePadding;
    // 文案
    .tree-item-node-label {
      display: inline-block;
      padding: 8px;
      background: red;
      color: #ffffff;
      border-radius: 4px;
      z-index: 1;
      width: 200px;
      word-break: break-all;
      box-shadow: 4px 4px 4px #bbc2d6;
    }
    // 操作插槽
    .item-node-operate {
      position: absolute;
      right: @nodePadding + 20;
      display: inline-block;
      z-index: 1;
    }
    // 折叠图标
    .tree-item-node-expand-icon {
      background: #ffffff;
      cursor: pointer;
      z-index: 1;
    }
    &::before {
      content: '';
      position: absolute;
      left: -@childrenPadding / 2;
      top: 0;
      bottom: 0;
      width: 2px;
      background: @primary-color;
    }
    &::after {
      content: '';
      position: absolute;
      left: -@childrenPadding / 2;
      right: -@childrenPadding / 2;
      top: 50%;
      height: 2px;
      background: @primary-color;
    }
  }
  .h-tree-item-children {
    padding: @childrenPadding;
    display: none;
  }

  // 连接线
  &.is-root-item {
    > .h-tree-item-node::after {
      left: @nodePadding;
      right: -@childrenPadding / 2;
    }
  }
  &.is-leaf-item {
    > .h-tree-item-node::after {
      left: -@childrenPadding / 2;
      right: @nodePadding;
    }
  }
  &.is-first-item {
    > .h-tree-item-node::before {
      top: 50%;
    }
  }
  &.is-last-item {
    > .h-tree-item-node::before {
      bottom: 50%;
    }
  }

  &.is-root-item.is-leaf-item {
    > .h-tree-item-node::before {
      display: none;
    }
    > .h-tree-item-node::after {
      display: none;
    }
  }

  // 折叠
  &.is-expand-item {
    > .h-tree-item-children {
      display: block;
    }
  }
  &.is-fold-item {
    > .h-tree-item-node::after {
      right: @nodePadding;
    }
  }

  // 可选中
  &.is-selectable-item {
    > .h-tree-item-node {
      cursor: pointer;
    }
  }
}
</style>
