<template>
  <div class="home-postaudit page-list-single">
    <PageList
      ref="PageList"
      :searchParams="searchParams"
      :tableParams="tableParams"
      :needSplitSearch="false"
    >
      <!-- table插槽 -->
      <div slot="name" slot-scope="{ record, text }" class="instance-name">
        <Tag type="Env" :text="record.env.toUpperCase()" />
        <DbImg :value="record.db_type" :schemaName="text" :limit="16" />
      </div>
      <!-- <span slot="env" slot-scope="{ record }">
        <span>{{ record.env === 'prod' ? '生产' : '测试' }}</span>
      </span>-->
      <!-- 数据库图标展示 -->
      <!-- <template v-slot:db_type="{text}">
        <DbImg :type="text" v-if="text != ''" />
        <span v-else></span>
      </template>-->
      <template slot="created_by" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_creater">
          <template slot="title">
            <span>{{'用户名：' + text}}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{record.ch_creater}}</span>
          </div>
        </a-tooltip>
        <span v-else>{{text || '--'}}</span>
      </template>
      <template v-slot:type="{text}">
        <a-tag :color=" text == 1 ? 'blue' : 'purple' ">{{ text == 1 ? '单次' : '多次' }}</a-tag>
      </template>
      <!-- 数据源名称 -->
      <LimitLabel
        slot="data_source"
        slot-scope="{ record }"
        :label="record.data_source.name"
        :limit="20"
        :limitLine="2"
      ></LimitLabel>
      <!-- 审核规则 -->
      <template v-slot:rule_set_name="{ text, record }">
        <div class="tagStyle">
          <div :class="id === record.id && flag ? '' : 'tag-item'">
            <a-tag v-for="tag in text" :key="tag">{{ tag }}</a-tag>
          </div>
          <a-icon
            v-if="record.rule_set_name.length > 2"
            :type="flag && id === record.id ? 'double-left' : 'double-right'"
            @click="toggleUpDown(record)"
          />
        </div>
      </template>
      <span slot="scheduler_status" slot-scope="{ record }">
        <a-switch
          v-if="record.scheduler_status !== null"
          checked-children="运行"
          un-checked-children="停止"
          :checked="record.scheduler_status === 1"
          @change="onChange(record)"
        />
      </span>
      <custom-btns-wrapper slot="action" slot-scope="{ record }">
        <a @click="view(record)" actionBtn>查看报告</a>
        <a @click="modify(record)" actionBtn v-if="!isDev">编辑</a>
        <a-popconfirm
          v-if="!isDev"
          title="将按照已有规则立即发起一次审核，请确认是否发起?"
          @confirm="() => repeat(record)"
          actionBtn
        >
          <a>再次发起</a>
        </a-popconfirm>
        <a-popconfirm
          v-if="!isDev"
          title="确定删除?"
          @confirm="() => del('delete', record.id)"
          actionBtn
        >
          <a class="remove">删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </PageList>
    <!-- 新建项目弹窗 -->
    <AddModal ref="addModal" @save="save"></AddModal>
  </div>
</template>

<script>
import {
  // sendEmail,
  afterwardsReview,
  afterwardsPostgreReview,
  afterwardsRetry,
  pgafterwardsRetry
} from '@/api/home';
// import Table from '@/components/Table';
import Tag from '@/components/Biz/Tag';
import Status from '@/components/Biz/Status';
import SearchArea from '@/components/SearchArea';
import AddModal from './components/AddModal';
import config from './config';
import PageList from '@/components/PageListNew';
import QuartzJobModal from './components/QuartzJobModal';
import LimitLabel from '@/components/LimitLabel';
import DbImg from '@/components/CustomImg/DbImg';

export default {
  name: 'HomePage',
  components: {
    Tag,
    // Table,
    DbImg,
    Status,
    SearchArea,
    AddModal,
    PageList,
    LimitLabel,
    QuartzJobModal
  },
  props: {
    dbType: String
  },
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: 'sqlreview/review/afterwards_review',
        reqParams: {
          db_type: this.dbType
        },
        columns: this.config.columns,
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        searchFields: this.config.searchFields(this.dbType),
        needTools: true,
        needSearchArea: true,
        scroll: { x: 'max-content' }
      },
      searchParams: {
        fields: this.config.searchFields(this.dbType),
        multiCols: 3
      },
      id: null,
      flag: false
    };
  },
  mounted() {},
  computed: {
    // showAKeyPass() {
    //   const role = this.$store.state.account.user.role || '';
    //   return role == 'admin' || role.toLowerCase() == 'dba';
    // }
    isDev() {
      const user = this.$store.state.account.user || {};
      return user.role === 'developer';
    }
  },
  created() {},
  methods: {
    // 新建项目
    addProject() {
      this.$refs.addModal.show(null, this.dbType);
    },
    // 查看报告
    view(record) {
      if (record.db_type == 'POSTGRE') {
        this.$store.commit('common/setPageCache', {
          homePostaudit: { dbType: record.db_type }
        });
        this.$router.push({
          name: 'home-postaudit-pgdetail',
          query: {
            task_id: record.id,
            db_type: record.db_type,
            name: record.name,
            env: record.env
          }
        });
      } else {
        this.$router.push({
          name: 'home-postaudit-detail',
          query: { task_id: record.id }
        });
      }
    },
    // 编辑
    modify(record) {
      this.id = record.id;
      this.$refs.addModal.show(record);
    },
    // 再次发起
    repeat(record) {
      const { PageList } = this.$refs;
      this.$showLoading();
      if (record.db_type == 'POSTGRE') {
        pgafterwardsRetry({ task_id: record.id })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              PageList.refresh();
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(() => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else {
        afterwardsRetry({ id: record.id })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              PageList.refresh();
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(() => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      }
    },
    // 删除
    del(method, id) {
      this.save(method, { id }, true);
    },
    // 定时任务开关
    onChange(record) {
      this.save(
        'put',
        {
          id: record.id,
          scheduler_status: record.scheduler_status ? 0 : 1
        },
        true
      );
    },
    // 请求函数
    save(method, data, flag, dbType) {
      const { addModal, PageList } = this.$refs;
      // 请求
      this.$showLoading();
      if (this.id) {
        data.id = this.id;
      }
      if (dbType == 'POSTGRE') {
        afterwardsPostgreReview(method, data)
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              if (!flag) {
                addModal.hide();
              }
              this.id = null;
              PageList.refresh();
            } else {
              // addModal.hide();
              // this.id = null;
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(() => {
            // addModal.hide();
            // this.id = null;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else {
        afterwardsReview(method, data)
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              if (!flag) {
                addModal.hide();
              }
              this.id = null;
              PageList.refresh();
            } else {
              // addModal.hide();
              this.id = null;
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(() => {
            // addModal.hide();
            this.id = null;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      }
    },
    // 查询
    // search(data) {
    //   const { table } = this.$refs;
    //   table.refresh(null, data);
    // },
    // 重置
    // reset() {
    //   const { table } = this.$refs;
    //   table.refresh();
    // },
    // 提交评审
    // authSubmit(id) {
    //   const { table } = this.$refs;
    //   this.$confirm({
    //     title: '确认提交审批?',
    //     onOk: () => {
    //       this.$showLoading();
    //       sendEmail({
    //         record_id: id
    //       })
    //         .then(res => {
    //           if (_.get(res, 'data.code') == 0) {
    //             this.$hideLoading({ tips: _.get(res, 'data.message') });
    //             table.refresh();
    //           } else if (_.get(res, 'data.code') == 3000) {
    //             this.$hideLoading({ duration: 0 });
    //             this.$confirm({
    //               okText: '强制提交',
    //               cancelText: '取消提交',
    //               title:
    //                 '为了加快DBA评审该项目，请先填写不通过SQL的运行状态评估情况!',
    //               onOk: () => {
    //                 this.$showLoading();
    //                 sendEmail({
    //                   record_id: id,
    //                   confirm: 1
    //                 }).then(res => {
    //                   if (_.get(res, 'data.code') == 0) {
    //                     this.$hideLoading({ tips: _.get(res, 'data.message') });
    //                     table.refresh();
    //                   } else {
    //                     this.$hideLoading({
    //                       method: 'error',
    //                       tips: _.get(res, 'data.message')
    //                     });
    //                   }
    //                 });
    //               },
    //               onCancel() {}
    //             });
    //           } else {
    //             this.$hideLoading({
    //               method: 'error',
    //               tips: _.get(res, 'data.message')
    //             });
    //           }
    //         })
    //         .catch(e => {
    //           console.error(e);
    //           this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
    //         });
    //     },
    //     onCancel() {}
    //   });
    // },
    // 上下折叠
    toggleUpDown(record) {
      if (this.flag && this.id !== record.id) {
        this.flag = true;
        this.id = record.id;
      } else {
        this.flag = !this.flag;
        this.id = record.id;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.ant-dropdown-link {
  margin-top: 5px;
}
.tooltip-style {
  color: #ccc;
}
.tagStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .tag-item {
    height: 24px;
    overflow: hidden;
  }
  .ant-tag {
    margin-bottom: 5px;
  }
  .anticon {
    margin-right: 30px;
  }
  .anticon-double-right {
    transform: rotate(90deg);
  }
  .anticon-double-left {
    transform: rotate(90deg);
  }
}
.instance-name {
  display: flex;
  align-items: center;
  .ant-tag {
    border-radius: 6px;
  }
}
</style>
