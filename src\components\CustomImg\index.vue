<template>
  <div class="custom-image">
    <img :style="style" :src="value" />
  </div>
</template>

<script>
export default {
  inheritAttrs: false,
  components: {},
  props: {
    src: String,
    imgStyle: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  computed: {
    value() {
      const { src } = this;
      return src;
    },
    style() {
      const { imgStyle = {} } = this;
      return imgStyle;
    }
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>
<style lang="less" scoped>
</style>
