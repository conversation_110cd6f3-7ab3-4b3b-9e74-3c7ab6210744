<template>
  <div>
    <div class="frame-button-wrapper">
      <a-button slot="topBtns" class="highlight" @click="download">导出</a-button>
    </div>
    <Tabs
      v-model="activeKey"
      :tabsList="tabsList"
      :mode="'scale'"
      @change="onChange"
    >
      <!-- <div slot="sqlExecution" slot-scope="{ item }">
        <SQLExecution :ref="item.key" />
      </div> -->
      <div slot="tracking" slot-scope="{ item }">
        <Tracking :ref="item.key" />
      </div>
      <div slot="login" slot-scope="{ item }">
        <Login :ref="item.key" />
      </div>
    </Tabs>
  </div>
</template>

<script>
import Tabs from '@/components/Tabs';
import SQLExecution from './SQLExecution';
import Tracking from './Tracking';
import Login from './Login';
import common from '@/utils/common';
import {
  loginDownload,
  // SQLExecutionDownload,
  trackingListDownload
} from '@/api/auditLog';
export default {
  components: { Tabs, SQLExecution, Login, Tracking },
  props: {},
  data() {
    return {
      // activeKey: 'sqlExecution',
      activeKey: 'tracking',
      tabsList: [
        // {
        //   tab: 'SQL执行记录',
        //   key: 'sqlExecution'
        // },
        {
          tab: '用户操作记录',
          key: 'tracking'
        },
        {
          tab: '登录记录',
          key: 'login'
        }
      ]
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    onChange(e) {
      console.log(e, this.activeKey);
    },
    download() {
      // const req =
      //   this.activeKey == 'sqlExecution' ? SQLExecutionDownload : loginDownload;
      const req =
        this.activeKey == 'login' ? loginDownload : trackingListDownload;
      const params = this.$refs[this.activeKey].getSearchParamsFn() || {};
      this.$showLoading({
        tips: `下载中...`
      });
      req(params)
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>
