<!--
 * @Author: your name
 * @Date: 2021-01-20 14:09:05
 * @LastEditTime: 2021-01-29 17:32:56
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/components/CodeMirror.vue
-->
<template>
  <div :class="className">
    <div class="ccm-diff-title-wrapper">
      <div class="ccm-diff-title">
        <span>
          <a-icon type="exception" />
          <span v-html="origPos === 'Right' ? firstTitle : lastTitle"></span>
        </span>
        <slot name="head-left-extend"></slot>
      </div>
      <div class="ccm-diff-title">
        <span>
          <a-icon type="exception" />
          <span v-html="origPos === 'Right' ? lastTitle : firstTitle"></span>
        </span>
        <slot name="head-right-extend"></slot>
      </div>
      <div class="ccm-header-tools">
        <div class="ccm-h-tools-locate" v-if="useLocate">
          <custom-icon
            type="up-circle"
            v-tooltip="'上一处diff'"
            @click="goToDiff('Prev')"
          />
          <custom-icon
            type="down-circle"
            v-tooltip="'下一处diff'"
            @click="goToDiff('Next')"
          />
        </div>
      </div>
    </div>
    <pre class="ccm-diff-container"></pre>
  </div>
</template>

<script>
// import { format } from 'sql-formatter';
import { rewriteLanguages } from '@/utils/codemirror';
import CodeMirror from 'codemirror';
import 'codemirror/lib/codemirror.css';
import 'codemirror/addon/merge/merge.js';
import 'codemirror/mode/sql/sql.js';
import 'codemirror/mode/xml/xml.js';
import 'codemirror/addon/merge/merge.css';
import DiffMatchPatch from 'diff-match-patch';
window.diff_match_patch = DiffMatchPatch;
window.DIFF_DELETE = -1;
window.DIFF_INSERT = 1;
window.DIFF_EQUAL = 0;
rewriteLanguages();

export default {
  name: 'codeMirror',
  props: {
    dbType: String,
    theme: {
      type: String,
      default: 'default'
    },
    options: {
      type: Object,
      default: () => ({})
    },
    valueTitle: {
      type: String,
      required: false,
      default: () => '当前SQL信息'
    },
    value: {
      type: String,
      required: false,
      default: () => ''
    },
    orig: {
      type: String,
      required: false,
      default: () => ''
    },
    origTitle: {
      type: String,
      required: false,
      default: () => '最后一次通过SQL信息'
    },
    origPos: {
      type: String,
      default: 'Right'
    },
    format: {
      type: String,
      required: false,
      default: () => 'sql'
    },
    needFormat: {
      type: Boolean,
      required: false,
      default: true
    },
    useLocate: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    valueTitle: function(value) {
      this.firstTitle = value;
    },
    origTitle: function(value) {
      this.lastTitle = value;
    },
    value: function(value) {
      this.firstText = value;
      this.initUI();
    },
    orig: function(value) {
      this.lastText = value;
      this.initUI();
    }
  },
  computed: {
    className() {
      let arr = ['custom-code-mirror-diff'];
      const theme = this.theme;

      arr.push(`theme-${theme}`);
      if (!this.firstText && !this.lastText) {
        arr.push('ccm-content-empty');
      }

      return arr;
    }
  },
  data(vm) {
    return {
      firstTitle: vm.valueTitle,
      lastTitle: vm.origTitle,
      collapsed: false,
      firstText: vm.value,
      lastText: vm.orig
    };
  },
  mounted() {
    // console.log(this, 'codemirror');
    this.initUI();
  },
  methods: {
    initUI() {
      // if (value == null) return;
      let target = this.$el.querySelector('.ccm-diff-container');
      if (!target) return;

      target.innerHTML = '';
      // xml 的配置
      const opts = {
        value: this.firstText, // 上次内容
        // orig: this.lastText, // 本次内容
        mode: 'text/xml',
        smartIndent: true,
        [`orig${this.origPos}`]: this.lastText,
        readOnly: true, // 只读 不可修改
        lineNumbers: false, // 显示行号
        highlightDifferences: true,
        connect: null,
        collapseIdentical: false
      };

      if (this.format === 'sql') {
        opts.value = this.getSqlText(this.firstText); // 本次内容
        opts[`orig${this.origPos}`] = this.getSqlText(this.lastText); // 本次内容
        opts.mode = 'text/x-sql';
      }
      this.merger = CodeMirror.MergeView(
        target,
        Object.assign(opts, this.options)
      );
      // console.log(this.merger);

      // 添加行宽度（box-sizing导致的问题，目前先不删除）
      setTimeout(() => {
        const panelWidth = document.querySelector('.CodeMirror-scroll')
          .offsetWidth;
        document.querySelectorAll('.CodeMirror-sizer').forEach(item => {
          // console.log(item.offsetWidth, panelWidth);
          if (item.offsetWidth > panelWidth) {
            item.style.width = item.offsetWidth + 32 + 'px';
          }
        });
        document
          .querySelectorAll('.CodeMirror-hscrollbar >div')
          .forEach(item => {
            if (item.offsetWidth > panelWidth) {
              item.style.width = item.offsetWidth + 32 + 'px';
            }
          });
      }, 100);
    },
    getSqlText(sql) {
      if (!this.needFormat) {
        return sql;
      }
      let source = sql || '';
      let res = source;
      try {
        res = sqlFormatter.format(source, this.getSqlFormatOptions());
      } catch (e) {
        res = source;
        console.log(e);
      }
      return res;
    },
    getSqlFormatOptions() {
      let defaultOptions = {};
      if (this.dbType) {
        defaultOptions = FORMAT_CONFIG.SqlFormat.getConfig(this.dbType);
      }
      return defaultOptions;
    },
    goToDiff(dir) {
      const func = CodeMirror.commands[`go${dir}Diff`];
      if (func && this.merger && this.merger.edit) {
        func(this.merger.edit);
      }
    },
    refresh() {
      this.initUI();
    }
  }
};
</script>
<style lang="less" scoped>
.custom-code-mirror-diff {
  .ccm-diff-title-wrapper {
    overflow: hidden;
    padding-bottom: 8px;
    display: flex;
    justify-content: space-between;
    position: relative;
    .ccm-diff-title {
      width: calc(50% - 20px);
      display: inline-block;
      line-height: 25px;
      i {
        margin-right: 4px;
      }
    }
    .ccm-header-tools {
      position: absolute;
      right: 0;
      top: 10px;
      font-size: 16px;
      cursor: pointer;

      .anticon {
        &:hover {
          color: @primary-color;
        }
      }
    }
  }
  .ccm-diff-container {
    display: block;
    margin: 0 0 10px;
    font-size: 13px;
    line-height: 20px;
    word-break: break-all;
    word-wrap: break-word;
    color: #333;
    /deep/ .CodeMirror-cursors {
      display: none;
    }
    /deep/ .CodeMirror-merge-gap {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 40px;
      border: none;
      background: #fff;
    }
    /deep/ .CodeMirror {
      background: transparent;
    }
    /deep/ .CodeMirror-merge-pane {
      padding: 8px 0;
      width: calc(50% - 20px);
      background-color: #edf5ff;
      border-radius: 5px;
      font-size: 14px;
      pre {
        padding: 0 16px;
      }
    }
    /deep/ .CodeMirror-merge {
      border: none;
      min-height: 250px;
      height: auto;

      // source-left
      .CodeMirror-merge-l-chunk {
        & + .CodeMirror-gutter-wrapper {
          .CodeMirror-linenumber {
            background: #eef;
          }
        }
      }
      .CodeMirror-merge-l-chunk-start {
        border-color: rgba(136, 136, 238, 0.45);
        & + .CodeMirror-gutter-wrapper {
          .CodeMirror-linenumber {
            &::after {
              content: '';
              position: absolute;
              left: 0;
              right: 0;
              top: 0;
              bottom: 0;
              z-index: 0;
              border-top: 1px solid rgba(136, 136, 238, 0.45);
            }
          }
        }
      }
      .CodeMirror-merge-l-chunk-end {
        border-color: rgba(136, 136, 238, 0.45);
        & + .CodeMirror-gutter-wrapper {
          .CodeMirror-linenumber {
            &::after {
              content: '';
              position: absolute;
              left: 0;
              right: 0;
              top: 0;
              bottom: 0;
              z-index: 0;
              border-bottom: 1px solid rgba(136, 136, 238, 0.45);
            }
          }
        }
      }

      .CodeMirror-merge-l-connect {
        stroke: rgba(136, 136, 238, 0.45);
      }

      // source-right
      .CodeMirror-merge-r-chunk {
        & + .CodeMirror-gutter-wrapper {
          .CodeMirror-linenumber {
            background: #ffffe0;
          }
        }
      }
      .CodeMirror-merge-r-chunk-start {
        & + .CodeMirror-gutter-wrapper {
          .CodeMirror-linenumber {
            &::after {
              content: '';
              position: absolute;
              left: 0;
              right: 0;
              top: 0;
              bottom: 0;
              z-index: 0;
              border-top: 1px solid #ee8;
            }
          }
        }
      }
      .CodeMirror-merge-r-chunk-end {
        & + .CodeMirror-gutter-wrapper {
          .CodeMirror-linenumber {
            &::after {
              content: '';
              position: absolute;
              left: 0;
              right: 0;
              top: 0;
              bottom: 0;
              z-index: 0;
              border-bottom: 1px solid #ee8;
            }
          }
        }
      }
    }
    /deep/ .CodeMirror-merge-scrolllock,
    /deep/ .CodeMirror-merge-copy {
      display: none;
    }
  }
}

// default
.custom-code-mirror-diff.theme-default {
  .ccm-diff-title-wrapper {
    overflow: hidden;
    padding-bottom: 8px;
    .ccm-diff-title {
      width: calc(50% - 20px);
      display: inline-block;
      line-height: 25px;
      i {
        margin-right: 4px;
      }
    }
  }
  .ccm-diff-container {
    display: block;
    margin: 0 0 10px;
    font-size: 13px;
    line-height: 20px;
    word-break: break-all;
    word-wrap: break-word;
    color: #333;
    /deep/ .CodeMirror-cursors {
      display: none;
    }
    /deep/ .cm-s-default {
      color: #4e5054;
      .cm-keyword {
        color: #0f78fb;
      }
      .cm-operator {
        color: #ffb625;
      }
      .cm-punctuation {
        color: #ffb625;
      }
      .cm-variable-2 {
        color: #4e5054;
      }
      .cm-bracket {
        color: #ffb625;
      }
      .cm-string {
        color: #23be6c;
      }
      .cm-number {
        color: #23be6c;
      }
    }
    /deep/ .CodeMirror-merge-r-chunk {
      fill: #ffffe3;
    }
    /deep/ .CodeMirror-merge-r-chunk-start,
    /deep/ .CodeMirror-merge-r-chunk-end {
      border-color: #f8f8ae;
    }
    /deep/ .CodeMirror-merge-r-connect {
      fill: #ffffe3;
      stroke: #f8f8ae;
    }
    /deep/ .CodeMirror-merge-gap {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 40px;
      border: none;
      background: #fff;
    }
    /deep/ .CodeMirror {
      background: transparent;
    }
    /deep/ .CodeMirror-merge-pane {
      padding: 8px 0;
      width: calc(50% - 20px);
      background-color: #edf5ff;
      border-radius: 5px;
      font-size: 14px;
      pre {
        padding: 0 16px;
      }
    }
  }
}

// white
.custom-code-mirror-diff.theme-white {
  /deep/ .ccm-diff-title-wrapper {
    padding-bottom: 0;
    .ccm-diff-title {
      background: #ffffff;
      padding: 12px 12px 0 16px;
      border-radius: 4px 4px 0 0;
      color: @font-color-strong;
      font-size: 15px;
      .font-bold();
      width: calc(50% - 12px);
    }
  }

  /deep/ .ccm-diff-container {
    margin-bottom: 0;
    .CodeMirror-merge {
    }
    .CodeMirror-merge-pane {
      background-color: #ffffff;
      border-radius: 0 0 4px 4px;
      width: calc(50% - 12px);

      .CodeMirror-merge-r-inserted,
      .CodeMirror-merge-l-inserted {
        background: rgba(0, 255, 0, 0.25);
      }
      .CodeMirror-merge-r-deleted,
      .CodeMirror-merge-l-deleted {
        background: rgba(255, 0, 0, 0.2);
      }
    }
    .CodeMirror-gutters {
      background-color: #ffffff;
      border-right: none;
    }
    .CodeMirror-linenumber {
      color: @font-color-weak;
    }
    .CodeMirror-merge-gap {
      background: transparent;
      width: 24px;
    }
  }

  &.ccm-content-empty {
    /deep/ .ccm-diff-container {
      .CodeMirror-merge-pane {
        background-color: #fafafa;
      }
      .CodeMirror-gutters {
        background-color: #fafafa;
      }
    }
  }
}
</style>
