export default function (ctx) {
  const fields = (dbType) => {
    return [
      {
        type: 'Select',
        label: '采集开关',
        key: 'after_audit_status',
        props: {
          options: [
            { label: '开', value: 1 },
            { label: '关', value: 0 }
          ]
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '执行SQL采集',
          key: 'collect_type',
          className: 'collect-type',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: dbType == 'MYSQL' ? 'mysql_collect_status' : 'golden_collect_status'
            }
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                collect_type: value,
                jdbc_frequency: value == 'jdbc' ? 200 : null,
                statistical_interval: value == 'jdbc' ? 1 : null
              });
              ctx.$set(ctx, 'collectType', value);
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '接口数据库类型',
          key: 'open_api_type',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'openApi_chose'
            }
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                open_api_type: value,
                openapi: value == 0 ? 'rds.aliyuncs.com' : null,
                collect_day: 7,
                openapi_frequency: value == 0 ? 8 : 1,
                statistical_interval: value == 0 ? 1 : 1,
                access_key: null,
                access_secret: null,
                instance_id: null
              });
              ctx.$set(ctx, 'openApiType', value);
            }
          },
          visible: formData.collect_type == 'openapi',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'OpenAPI',
          key: 'openapi',
          props: {
            placeholder: 'rds.aliyuncs.com'
          },
          visible:
            formData.collect_type == 'openapi' && formData.open_api_type == 0,
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '资源区域',
          key: 'openapi',
          props: {
            url: '/sqlreview/project_config/openApi_tenxun_by_select',
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          visible:
            formData.collect_type == 'openapi' && formData.open_api_type == 1,
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '区域',
          key: 'openapi',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'polardb_region_chose'
            }
          },
          visible: formData.open_api_type == 2,
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'Access Key',
          key: 'access_key',
          visible: formData.collect_type == 'openapi',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'Access Key Secret',
          key: 'access_secret',
          visible: formData.collect_type == 'openapi',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '实例ID',
          key: 'instance_id',
          visible:
            formData.collect_type == 'openapi' && formData.open_api_type !== 2,
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '集群ID',
          key: 'instance_id',
          visible:
            formData.collect_type == 'openapi' && formData.open_api_type == 2,
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '首次采集天数',
          key: 'collect_day',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name:
                formData.open_api_type == 0
                  ? 'aliyun_collect_day'
                  : 'tenxunyun_collect_day'
            }
          },
          visible: formData.collect_type == 'openapi',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'openapi_frequency',
          tips:
            formData.open_api_type == 0
              ? '阿里云统计接口目前只支持天维度的慢日志'
              : '',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name:
                formData.open_api_type == 0
                  ? 'aliyun_frequency'
                  : 'tenxunyun_frequency'
            }
          },
          visible: formData.collect_type == 'openapi',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '统计间隔',
          key: 'statistical_interval',
          tips:
            formData.open_api_type == 0
              ? '阿里云慢日志统计接口目前只支持按照天维度进行数据统计'
              : '采集数据按照配置的时间间隔进行SQL执行情况统计',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name:
                formData.open_api_type == 0
                  ? 'aliyun_statistical_interval'
                  : 'tenxunyun_statistical_interval'
            }
          },
          visible: formData.collect_type == 'openapi',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'jdbc_frequency',
          tips: '执行消耗低于采集频率的SQL将不会采集',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'jdbc_frequency'
            }
          },
          visible: formData.collect_type == 'jdbc',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      // (formData = {}) => {
      //   return {
      //     type: 'InputNumber',
      //     label: '保留时间（天）',
      //     key: 'retention_time',
      //     props: {
      //       min: 1
      //     },
      //     visible: formData.collect_type == 'jdbc',
      //     rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      //   };
      // },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '统计间隔',
          key: 'statistical_interval',
          tips: '采集数据按照配置的时间间隔进行SQL执行情况统计',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: { enum_name: 'jdbc_statistical_interval' }
          },
          visible: formData.collect_type == 'jdbc',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: ' 登录方式',
          key: 'login_type',
          props: {
            mode: 'tips',
            class: 'inline',
            options: [
              {
                label: '密码',
                value: 0
              },
              {
                label: '密钥',
                value: 1
              }
            ]
          },
          visible: formData.collect_type == 'ssh',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: 'IP',
          key: 'ip',
          visible: formData.collect_type == 'ssh',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '端口',
          key: 'port',
          visible: formData.collect_type == 'ssh',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '用户名',
          key: 'ssh_user',
          visible: formData.collect_type == 'ssh',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        const password = {
          type: 'InputPassword',
          label: '密码',
          key: 'ssh_pwd',
          props: { placeholder: '请输入' },
          visible: formData.collect_type == 'ssh',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
        if (ctx.type === 'edit') {
          password.rules = [];
          password.props = {
            placeholder: '填写该项，会更新密码'
          };
        } else if (ctx.type === 'add') {
          password.rules = [
            { required: true, message: '该项为必填项', trigger: 'blur' }
          ];
          password.props = {
            placeholder: '请输入'
          };
        }
        return password;
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '慢日志路径',
          key: 'slow_log_url',
          visible: formData.collect_type == 'ssh',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'InputNumber',
          label: '采集频率（分钟）',
          key: 'ssh_frequency',
          props: {
            min: 1
          },
          visible: formData.collect_type == 'ssh',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      }
    ];
  };
  const oracleFields = () => {
    return [
      {
        type: 'Select',
        label: '采集开关',
        key: 'after_audit_status',
        props: {
          options: [
            { label: '开', value: 1 },
            { label: '关', value: 0 }
          ]
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '首次采集天数',
          key: 'collect_day',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'oracle_collect_day'
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'collect_frequency',
          props: {},
          hideComponent: true,
          slots: [{ key: 'frequency' }],
          rules: [{ required: true, message: '', trigger: 'change' }]
        };
      }
    ];
  };
  const db2Fields = () => {
    return [
      {
        type: 'Select',
        label: '采集开关',
        key: 'after_audit_status',
        props: {
          options: [
            { label: '开', value: 1 },
            { label: '关', value: 0 }
          ]
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'collect_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'db2_collect_frequency'
            }
          },
          rules: [{ required: true, message: '', trigger: 'change' }]
        };
      }
    ];
  };
  const obFields = () => {
    return [
      {
        type: 'Select',
        label: '采集开关',
        key: 'after_audit_status',
        props: {
          options: [
            { label: '开', value: 1 },
            { label: '关', value: 0 }
          ]
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Select',
        label: '采集天数',
        key: 'collect_day',
        props: {
          url: '/sqlreview/after_audit/get_sql_collect_enum',
          reqParams: {
            enum_name: 'ob_collect_day'
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Select',
        label: '采集频率',
        key: 'collect_frequency',
        props: {
          url: '/sqlreview/after_audit/get_sql_collect_enum',
          reqParams: {
            enum_name: 'ob_collect_frequency'
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'Select',
        label: '统计间隔',
        key: 'statistical_interval',
        props: {
          url: '/sqlreview/after_audit/get_sql_collect_enum',
          reqParams: {
            enum_name: 'ob_statistical_interval'
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
    ];
  };
  const collectObInfo = type => {
    return [
      {
        type: 'Input',
        label: '数据库审核账号',
        key: 'examine_user',
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'RadioGroup',
        label: ' ',
        className: 'hidden-label',
        key: 'examine_pwd_type',
        props: {
          mode: 'tips',
          class: 'inline',
          options: [
            {
              label: '密码',
              value: 0
            },
            {
              label: '密钥',
              value: 1
            }
          ]
        },
        listeners: {
          change: value => {
            ctx.$refs.form.saving({
              examine_pwd_type: value,
              examine_pwd: null
            });
            ctx.$refs.obForm.saving({
              examine_pwd_type: value,
              examine_pwd: null
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        const password = {
          type: 'InputPassword',
          label: '数据库审核密码',
          key: 'examine_pwd',
          className: 'password',
          props: {
            placeholder:
              formData.examine_pwd_type == 0 ? '请输入' : '密码为非必填'
          },
          rules:
            formData.examine_pwd_type == 0
              ? [{ required: true, message: '该项为必填项', trigger: 'blur' }]
              : []
        };
        if (type === 'edit') {
          password.rules = [];
          password.props = {
            placeholder:
              formData.examine_pwd_type == 0
                ? '填写该项，会更新密码'
                : '密码为非必填'
          };
        }
        return password;
      }
    ];
  };
  const pgFields = (id) => {
    return [
      {
        type: 'Select',
        label: '采集开关',
        key: 'after_audit_status',
        props: {
          options: [
            { label: '开', value: 1 },
            { label: '关', value: 0 }
          ]
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集模式',
          key: 'collect_type',
          className: 'collect-type',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'pg_collect_type'
            }
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                collect_type: value,
                jdbc_frequency: value == 'jdbc' ? 200 : null,
                statistical_interval: value == 'jdbc' ? 60000 : null
              });
              ctx.$set(ctx, 'collectType', value);
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '插件所在数据库',
          key: 'plugin_database',
          props: {
            url: '/sqlreview/after_audit/postgresql/databases',
            reqParams: {
              data_source_id: id
            },
            allowSearch: true,
            backSearch: true,
            limit: 20
          },
          visible: formData.collect_type == 'pg_stat_statements',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'jdbc_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'jdbc_frequency'
            }
          },
          visible: formData.collect_type == 'jdbc',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'pg_stat_statements_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'pg_stat_statements_frequency'
            }
          },
          visible: formData.collect_type == 'pg_stat_statements',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '统计间隔',
          key: 'statistical_interval',
          tips: '采集数据按照配置的时间间隔进行SQL执行情况统计',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: { enum_name: 'pg_jdbc_statistical_interval' }
          },
          visible: formData.collect_type == 'jdbc',
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      }
    ];
  };
  const columns = [
    {
      title: '字段',
      dataIndex: 'column',
      key: 'column',
      width: 100
    },
    {
      title: '字段类型',
      dataIndex: 'type',
      key: 'type',
      width: 200
    },
    {
      title: '采集方式',
      key: 'capture_type',
      dataIndex: 'capture_type',
      scopedSlots: { customRender: 'capture_type' },
      width: 200
    }
  ];
  const editConfig = (options = []) => {
    return {
      capture_type: (row, record = {}) => {
        return {
          type: 'Select',
          props: {
            options
          },
          rules: [{ required: true, message: '该项为必填项' }],
          listeners: {
            change: value => {
              ctx.$refs.tableEdit.saving({
                column: record.column,
                type: record.type,
                capture_type: value
              });
            }
          }
        };
      }
    };
  };
  const gaussdbFields = () => {
    return [
      {
        type: 'Select',
        label: '采集开关',
        key: 'after_audit_status',
        props: {
          options: [
            { label: '开', value: 1 },
            { label: '关', value: 0 }
          ]
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '采集频率',
          key: 'collect_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'gaussdb_collect_frequency'
            }
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
      }
      // (formData = {}) => {
      //   return {
      //     type: 'Select',
      //     label: '统计间隔',
      //     key: 'statistical_interval',
      //     props: {
      //       url: '/sqlreview/after_audit/get_sql_collect_enum',
      //       reqParams: {
      //         enum_name: 'gaussdb_statistical_interval'
      //       }
      //     },
      //     rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      //   };
      // }
    ];
  };
  const argus_realtime = ()=>{
    return [
      {
        type: 'Select',
        label: '采集开关',
        key: 'cat_collect_status',
        props: {
          options: [
            { label: '开', value: 1 },
            { label: '关', value: 0 }
          ]
        }
        // rules: [{ required: true, message: '该项为必填项' }]
      },
    ]
  }
  const autoReviewFields = (dbType) => {
    return [
      {
        type: 'Select',
        label: '自动审核开关',
        key: 'auto_review_status',
        props: {
          options: [
            { label: '开', value: 1 },
            { label: '关', value: 0 }
          ]
        }
        // rules: [{ required: true, message: '该项为必填项' }]
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: '审核频率',
          key: 'review_frequency',
          props: {
            url: '/sqlreview/after_audit/get_sql_collect_enum',
            reqParams: {
              enum_name: 'auto_review_frequency'
            }
          }
          // rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      // {
      //   type: 'Select',
      //   label: '采集开关',
      //   key: 'cat_collect_status',
      //   props: {
      //     options: [
      //       { label: '开', value: 1 },
      //       { label: '关', value: 0 }
      //     ]
      //   }
      //   // rules: [{ required: true, message: '该项为必填项' }]
      // },
      // (formData = {}) => {
      //   return {
      //     type: 'Select',
      //     label: 'TOP策略',
      //     key: 'top_strategy',
      //     props: {
      //       url: '/sqlreview/after_audit/get_sql_collect_enum',
      //       reqParams: {
      //         enum_name: dbType == 'POSTGRE' ? 'pg_top_strategy' : 'top_strategy'
      //       }
      //     },
      // rules: [{ required: true, message: '该项为必填项' }]
      // };
      // },
      // (formData = {}) => {
      //   return {
      //     type: 'InputNumber',
      //     label: 'TOP排名',
      //     key: 'top_sort',
      //     props: {
      //       max: 100,
      //       min: 0
      //     }
      // rules: [{ required: true, message: '该项为必填项' }]
      // };
      // },
      // (formData = {}) => {
      //   return {
      //     type: 'Select',
      //     label: '重复SQL过滤',
      //     key: 'filter_day',
      //     props: {
      //       url: '/sqlreview/after_audit/get_sql_collect_enum',
      //       reqParams: {
      //         enum_name: dbType == 'POSTGRE' ? 'pg_filter_time' : 'filter_day'
      //       }
      //     }
      // rules: [{ required: true, message: '该项为必填项' }]
      // };
      // }
    ];
  };
  return {
    columns,
    collectObInfo,
    obFields,
    db2Fields,
    editConfig,
    fields,
    pgFields,
    oracleFields,
    gaussdbFields,
    autoReviewFields,
    argus_realtime
  };
}
