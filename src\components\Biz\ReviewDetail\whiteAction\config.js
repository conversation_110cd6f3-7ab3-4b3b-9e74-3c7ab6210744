// const options = [
//   { label: '低档（QPS100以下）', value: 'low' },
//   { label: '中档（QPS100~300）', value: 'middle' },
//   { label: '高档（QPS300以上）', value: 'high' }
// ];
import { maxFrequencyDynamicForm } from '@/api/home';
let options = [];
maxFrequencyDynamicForm().then((res) => {
  if (_.get(res, 'data.code') == 0) {
    options = res.data.data.results.map((item) => {
      return {
        label: item.element_name,
        value: item.element_value
      };
    });
  }
});
const sqlOptions = [
  { label: '低档（低于1000条/天）', value: 'low' },
  { label: '中档（1000-10000条/天）', value: 'middle' },
  { label: '高档（大于10000条/天）', value: 'high' }
];
const passOptions = [
  { label: '申请加入SQL白名单', value: 1 },
  {
    label: '加入白名单后，如果这条SQL没有变更，以后将自动判定为评审通过',
    value: '-1',
    disabled: true
  },
  { label: '申请临时通过', value: 2 },
  {
    label:
      '申请本次DBA评审通过，如果SQL不做变更，下一次自动审核，仍然会被判定为不通过',
    value: '-2',
    disabled: true
  }
];
export default function (ctx) {
  const fields = () => {
    return [
      // {
      //   type: 'RadioGroup',
      //   label: '平均调用频率',
      //   key: 'sqlmap_average_frequency',
      //   props: {
      //     options: options
      //   },
      //   width: 'auto',
      //   slots: [
      //     { key: 'average_private', wrapperStyle: { display: 'inline-block' } }
      //   ]
      //   // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      // },
      {
        type: 'RadioGroup',
        label: '峰值调用频率',
        key: 'sqlmap_max_frequency',
        props: {
          options: options
        },
        width: 'auto',
        visible: true,
        slots: [
          { key: 'max_private', wrapperStyle: { display: 'inline-block' } }
        ]
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'TableEdit',
        label: '每月数据量增加',
        key: 'sqlmap_monthly_increase',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        props: {
          columns: [
            {
              title: '表名',
              dataIndex: 'table_name',
              key: 'table_name',
              width: 300
            },
            {
              title: 'SCHEMA名',
              dataIndex: 'schema',
              key: 'schema',
              width: 300
            },
            {
              title: '数据增加档数',
              dataIndex: 'frequency',
              key: 'frequency',
              width: 300,
              scopedSlots: { customRender: 'frequency' }
            }
          ],
          editConfig: {
            frequency: (row, record = {}) => {
              return {
                type: 'Select',
                props: {
                  options: sqlOptions,
                  placeholder: '请选择'
                  // mode: 'tags',
                  // maxTags: 1
                  // separator: ','
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          // mode: 'list',
          initEditStatus: true,
          pagination: false,
          size: 'small'
          // leastNum: 1,
          // actionBtns: ['add', 'remove'],
          // actionBtnsIcons: {
          //   add: 'plus-circle',
          //   remove: 'close-circle'
          // }
        },
        width: '80%'
      },
      // {
      //   type: 'RadioGroup',
      //   label: '白名单申请',
      //   key: 'sqlmap_white_list',
      //   props: {
      //     options: [
      //       { label: '加入白名单', value: 1 },
      //       { label: '不加入', value: 0 }
      //     ]
      //   }
      // },
      {
        type: 'Textarea',
        label: '备注',
        key: 'sqlmap_note',
        width: '80%',
        props: {
          // size: 'small'
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
    ];
  };
  const passType = [
    {
      type: 'RadioGroup',
      label: '通过类型',
      key: 'sqlmap_white_list',
      props: {
        options: passOptions,
        defaultValue: 1
      },
      width: 'auto'
      // slots: [
      //   { key: 'passType', wrapperStyle: { display: 'block' } }
      // ]
      // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    }
  ];
  const fieldsTop = [
    {
      type: 'Label',
      label: '峰值调用频率',
      key: 'max_count'
    },
    {
      type: 'Label',
      label: '平均执行时间',
      key: 'avg_time'
    },
    {
      type: 'Label',
      label: '每天调用次数',
      key: 'total_count'
    }
  ];
  return {
    fields,
    fieldsTop,
    passType
  };
}
