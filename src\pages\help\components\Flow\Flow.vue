<template>
  <div class="fdc-canvas-wrapper" @mousedown="onMousedown" @mouseup="onMouseup">
    <div id="fdcCanvas" ref="canvas" class="fdc-canvas">
      <template v-for="node in data.nodeList">
        <div
          :key="node.id"
          :id="node.id"
          :class="'fdc-node'"
          :style="getNodeStyle(node)"
          @click.stop="clickNode"
        >
          <!-- 最左侧的那条竖线 -->
          <div class="fdc-node-left"></div>
          <!-- 节点类型的图标 -->
          <div class="flow-node-drag"></div>
          <!-- 节点名称 -->
          <div class="fdc-node-text">{{node.name}}</div>
        </div>
      </template>
    </div>
    <!-- 工具栏 -->
    <div class="fdc-tools">
      <a-icon type="home" @click.stop="toHome" />
      <a-icon type="delete" @click.stop="removeElement" />
    </div>
  </div>
</template>

<script>
import { jsPlumb } from 'jsplumb';
import { flowMixin } from './mixins';
import panzoom from 'panzoom';
console.log(jsPlumb, 'jsPlumb');

export default {
  components: {},
  props: {},
  data() {
    return {
      data: {
        name: '流程B',
        nodeList: [
          {
            id: 'nodeA',
            name: '节点A-不可拖拽',
            type: 'task',
            left: 18,
            top: 223,
            ico: 'el-icon-user-solid',
            state: 'success'
            // viewOnly: true
          },
          {
            id: 'nodeB',
            type: 'task',
            name: '流程B-节点B',
            left: 351,
            top: 96,
            ico: 'el-icon-goods',
            state: 'error'
          },
          {
            id: 'nodeC',
            name: '流程B-节点C',
            type: 'task',
            left: 354,
            top: 751,
            ico: 'el-icon-present',
            state: 'warning'
          },
          {
            id: 'nodeD',
            name: '流程B-节点D',
            type: 'task',
            left: 723,
            top: 215,
            ico: 'el-icon-present',
            state: 'running'
          }
        ],
        lineList: [
          {
            from: 'nodeA',
            to: 'nodeB',
            label: '条件A'
          },
          {
            from: 'nodeA',
            to: 'nodeC',
            label: '条件B'
          },
          {
            from: 'nodeB',
            to: 'nodeD'
          },
          {
            from: 'nodeC',
            to: 'nodeD'
          }
        ]
      },
      // 是否加载完毕标志位
      flowLoaded: false,
      // 缩放比例
      zoom: 1,
      // panzoom实例
      pan: null,
      // activeObj
      activeObj: null
    };
  },
  // 一些基础配置移动该文件中
  mixins: [flowMixin],
  mounted() {
    // 画布添加拖动缩放
    this.pan = panzoom(this.getCanvas(), {
      // maxZoom: 1.5,
      // minZoom: 0.5
    });
    this.pan.on('zoom', e => {
      this.setZoom(e.getTransform().scale);
    });
    // 初始化 jsPlumb
    this.initJsPlumb();

    // body事件
    document.addEventListener(
      'click',
      (this.bodyClick = e => {
        this.clearActiveObj();
      })
    );
  },
  created() {},
  beforeDestroy() {},
  methods: {
    /**
     * 初始化流程图
     */
    initJsPlumb() {
      this.jsPlumb = jsPlumb.getInstance();
      console.log(this.jsPlumb, 'jsPlumb instance');
      this.jsPlumb.ready(() => {
        // 导入默认配置
        this.jsPlumb.importDefaults(this.jsplumbSetting);
        // 会使整个jsPlumb立即重绘。
        this.jsPlumb.setSuspendDrawing(false, true);
        // 初始化节点
        this.loadFlow();
        // 单点击了连接线,
        this.jsPlumb.bind('click', (conn, originalEvent) => {
          // this.activeElement.type = 'line';
          // this.activeElement.sourceId = conn.sourceId;
          // this.activeElement.targetId = conn.targetId;
          // this.$refs.nodeForm.lineInit({
          //   from: conn.sourceId,
          //   to: conn.targetId,
          //   label: conn.getLabel()
          // });
          console.log(conn, originalEvent, 898);

          conn.setPaintStyle({
            stroke: '#b0b2b5',
            strokeWidth: 2
          });
          this.setActiveObj('line', conn);
          originalEvent.stopPropagation();
        });
        // // 连线
        this.jsPlumb.bind('connection', evt => {
          let from = evt.source.id;
          let to = evt.target.id;
          this.addLine(from, to);
        });

        // 删除连线回调
        // this.jsPlumb.bind('connectionDetached', evt => {
        //   this.deleteLine(evt.sourceId, evt.targetId);
        // });

        // // 改变线的连接节点
        // this.jsPlumb.bind('connectionMoved', evt => {
        //   this.changeLine(evt.originalSourceId, evt.originalTargetId);
        // });

        // // 连线右击
        // this.jsPlumb.bind('contextmenu', evt => {
        //   console.log('contextmenu', evt);
        // });

        // // 连线
        // this.jsPlumb.bind('beforeDrop', evt => {
        //   let from = evt.sourceId;
        //   let to = evt.targetId;
        //   if (from === to) {
        //     this.$message.error('节点不支持连接自己');
        //     return false;
        //   }
        //   if (this.hasLine(from, to)) {
        //     this.$message.error('该关系已存在,不允许重复创建');
        //     return false;
        //   }
        //   if (this.hashOppositeLine(from, to)) {
        //     this.$message.error('不支持两个节点之间连线回环');
        //     return false;
        //   }
        //   this.$message.success('连接成功');
        //   return true;
        // });

        // // beforeDetach
        // this.jsPlumb.bind('beforeDetach', evt => {
        //   console.log('beforeDetach', evt);
        // });

        // this.jsPlumb.setContainer(this.$el.querySelector('.fdc-canvas'));

        // console.log(this.getCanvasStyle());
      });
    },
    /**
     * 加载流程图
     */
    loadFlow() {
      // 初始化节点
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        this.initNode(node);
      }
      // 初始化连线
      for (let i = 0; i < this.data.lineList.length; i++) {
        let line = this.data.lineList[i];
        let connParam = {
          source: line.from,
          target: line.to,
          label: line.label ? line.label : '',
          connector: line.connector ? line.connector : '',
          anchors: line.anchors ? line.anchors : undefined,
          paintStyle: line.paintStyle ? line.paintStyle : undefined
        };
        this.jsPlumb.connect(connParam, this.jsplumbConnectOptions);
      }
      // this.$nextTick(function() {
      //   this.flowLoaded = true;
      // });
    },
    /**
     * 初始化node
     */
    initNode(node) {
      // 设置源点，可以拖出线连接其他节点
      this.jsPlumb.makeSource(node.id, _.merge(this.jsplumbSourceOptions, {}));
      // 设置目标点，其他源点拖出的线可以连接该节点
      this.jsPlumb.makeTarget(node.id, this.jsplumbTargetOptions);
      if (!node.viewOnly) {
        this.jsPlumb.draggable(node.id, {
          // containment: 'parent',
          start: params => {
            params.el._isDrag = false;
          },
          drag: params => {
            params.el._isDrag = true;
          },
          stop: params => {
            console.log(params);
            // 拖拽节点结束后的对调
            this.updateNodePos(node, params.el);
          }
        });
      }
    },
    /**
     * 添加节点
     */
    addNode(node = {}, e) {
      // 添加id
      node.id = _.uniqueId('add_');
      // 添加pos
      const canvas = this.getCanvas();
      const {
        left: canvasLeft,
        top: canvasTop
      } = canvas.getBoundingClientRect();
      node.left = (e.pageX - canvasLeft) / this.zoom;
      node.top = (e.pageY - canvasTop) / this.zoom;

      this.data.nodeList.push(node);
      this.$nextTick(() => {
        this.initNode(node);
      });
    },
    /**
     * 更新节点
     */
    updateNode() {},
    /**
     * 更新节点位置
     */
    updateNodePos(node, el) {
      const canvas = this.getCanvas();
      const {
        left: canvasLeft,
        top: canvasTop
      } = canvas.getBoundingClientRect();
      const { left, top } = el.getBoundingClientRect();
      this.$set(node, 'left', (left - canvasLeft) / this.zoom);
      this.$set(node, 'top', (top - canvasTop) / this.zoom);
    },
    /**
     * 删除节点
     */
    removeNode(id) {
      this.jsPlumb.remove(id);
      let { nodeList, lineList } = this.data;
      this.$set(
        this.data,
        'nodeList',
        nodeList.filter(item => item.id != id)
      );
      this.$set(
        this.data,
        'lineList',
        lineList.filter(item => item.from != id && item.to != id)
      );
    },
    /**
     * 点击节点
     */
    clickNode(e) {
      let el = e.currentTarget;
      if (el._isDrag) {
        el._isDrag = false;
        return;
      }
      let classes = el.className.split(' ');
      classes.push('active');
      el.className = [...new Set(classes)].join(' ');
      this.setActiveObj('node', el);
    },
    /**
     * 添加line
     */
    addLine(from, to) {
      this.data.lineList.push({
        from,
        to
      });
    },
    /**
     * 删除line
     */
    removeLine(conn) {
      this.clearActiveObj();
      this.jsPlumb.deleteConnection(conn, () => {
        let { lineList } = this.data;
        this.$set(
          this.data,
          'lineList',
          lineList.filter(
            item => item.from != conn.sourceId || item.to != conn.targetId
          )
        );
        console.log(this.data, 11111111);
      });
    },
    /**
     * 删除元素
     */
    removeElement() {
      if (!this.activeObj) {
        return;
      }
      const { type, el } = this.activeObj;
      this.$confirm({
        title: '确认删除',
        onOk: () => {
          if (type === 'node') {
            this.removeNode(el.id);
          } else {
            this.removeLine(el);
          }
        }
      });
    },
    /**
     * 设置activeObj
     */
    setActiveObj(type, el) {
      if (this.activeObj && this.activeObj.el === el) {
        // 重复点击
        console.log('重复点击');
        return;
      }
      this.clearActiveObj();
      this.activeObj = {
        type,
        el
      };
    },
    /**
     * 清空activeObj
     */
    clearActiveObj() {
      if (this.activeObj) {
        const { type, el } = this.activeObj;
        if (type === 'node') {
          let classes = el.className.split(' ');
          el.className = classes.filter(item => item !== 'active').join(' ');
        } else {
          el.setPaintStyle({
            stroke: '#E0E3E7',
            strokeWidth: 1
          });
        }
        this.activeObj = null;
      }
    },
    /**
     * 回到home点
     */
    toHome() {
      const { x, y, rate } = this.getHomeInfo();
      this.pan.zoomTo(0, 0, rate / this.zoom);
      this.pan.moveTo(x, y);
    },
    /**
     * 设置缩放
     */
    setZoom(zoom = 1) {
      this.jsPlumb.setZoom(zoom);
      this.zoom = zoom;
    },
    /**
     * 监听mousedown
     */
    onMousedown(e) {
      if (e.target === this.$el) {
        e.target.style.cursor = 'grabbing';
      }
    },
    /**
     * 监听mouseup
     */
    onMouseup(e) {
      if (e.target === this.$el) {
        e.target.style.cursor = 'grab';
      }
    },
    /**
     * 获取中心点信息
     */
    getHomeInfo() {
      const canvasStyle = this.getCanvasStyle();
      const canvasWrapper = this.$el;
      const centerPos = {
        left: canvasWrapper.clientWidth / 2,
        top: canvasWrapper.clientHeight / 2
      };

      // rate
      const widthRate = canvasWrapper.clientWidth / canvasStyle.width;
      const heightRate = canvasWrapper.clientHeight / canvasStyle.height;
      let rate = widthRate < heightRate ? widthRate : heightRate;
      rate = rate > 1 ? 1 : rate * 0.9;
      console.log(rate, 'rate');

      // spans
      const spans = {
        left: centerPos.left - canvasStyle.centerLeft * rate,
        top: centerPos.top - canvasStyle.centerTop * rate
      };

      return {
        x: spans.left,
        y: spans.top,
        rate
      };
    },
    /**
     * 获取node样式
     */
    getNodeStyle(node = {}) {
      return {
        left: node.left + 'px',
        top: node.top + 'px'
      };
    },
    /**
     * 获取canvas样式
     */
    getCanvasStyle() {
      const { nodeList = [] } = this.data;
      console.log(nodeList, 89898);
      let leftItem = _.minBy(nodeList, 'left');
      let topItem = _.minBy(nodeList, 'top');
      let rightItem = _.maxBy(nodeList, 'left');
      let bottomItem = _.maxBy(nodeList, 'top');
      // 最右的宽、最下的高
      let rightItemWidth = document.getElementById(rightItem.id).clientWidth;
      let bottomItemHeight = document.getElementById(bottomItem.id)
        .clientHeight;
      // 内容区域宽高
      let width = rightItem.left + rightItemWidth - leftItem.left;
      let height = bottomItem.top + bottomItemHeight - topItem.top;

      return {
        left: leftItem.left,
        right: rightItem.left + rightItemWidth,
        top: topItem.top,
        bottom: bottomItem.top + bottomItemHeight,
        centerLeft: leftItem.left + width / 2,
        centerTop: topItem.top + height / 2,
        width,
        height
      };
    },
    /**
     * 获取wrapper
     */
    getContainer() {
      return this.$el;
    },
    /**
     * 获取canvas
     */
    getCanvas() {
      return this.$el.querySelector('.fdc-canvas');
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.fdc-canvas-wrapper {
  flex-grow: 1;
  border: 1px solid red;
  position: relative;
  overflow: hidden;
  cursor: grab;
  outline: none;

  .fdc-tools {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 100;
    background: #fafafa;
    padding: 4px 8px;
  }

  .fdc-canvas {
    position: absolute;
    left: 0;
    right: 0;
    width: 50px;
    height: 50px;

    .fdc-node {
      padding: 8px;
      text-align: center;
      cursor: move;
      background: #ffffff;
      width: 100px;
      border: 1px solid red;
      position: absolute;

      .flow-node-drag {
        width: 20px;
        height: 20px;
        background: blue;
        cursor: crosshair;
      }

      &.active {
        background: @primary-3;
      }

      &.jtk-drag-hover {
        box-shadow: 2px 2px 2px red;
      }
    }
  }
}
</style>