<template>
  <div class="review-header">
    <a-card type="small" :bordered="false">
      <div class="content">
        <div
          class="title"
          v-if="isProjectRview || isFileReview || isOrderRview"
        >
          <div class="title-flex" v-if="isProjectRview || isOrderRview">
            <div class="title-project">Review对象</div>
            <a-popover>
              <template slot="content">
                <span>{{ headerInfo.project_name }}</span>
              </template>
              <span class="text">{{ headerInfo.project_name || '--' }}</span>
            </a-popover>
          </div>
          <div class="title-flex" v-if="isFileReview">
            <div class="title-project">任务名称</div>
            <a-popover>
              <template slot="content">
                <span>{{ headerInfo.project_name }}</span>
              </template>
              <span class="text">{{ headerInfo.project_name || '--' }}</span>
            </a-popover>
          </div>
          <div class="title-flex">
            <div class="title-project">SQL来源</div>
            <a-popover>
              <template slot="content">
                <span>{{ headerInfo.file_path }}</span>
              </template>
              <span class="text file-path">{{
                headerInfo.file_name || '--'
              }}</span>
            </a-popover>
          </div>
          <div class="title-flex">
            <div class="title-project">数据库类型</div>
            <span class="text db-type">
              <template
                v-if="headerInfo.db_type && headerInfo.db_type.length >= 1"
              >
                <DbImg
                  v-for="item in headerInfo.db_type"
                  :key="item"
                  :value="item"
                  :UseIconOnly="true"
                />
              </template>
              <span v-else>--</span>
            </span>
          </div>
          <div class="title-flex" v-if="isProjectRview || isOrderRview">
            <div class="title-project">代码框架</div>
            <span class="text file-path">{{
              headerInfo.sql_frame || '--'
            }}</span>
          </div>
          <div class="title-flex" v-if="isProjectRview || isOrderRview">
            <div class="title-project">SQL标签</div>
            <div class="sql-label">
              <!-- <a
                v-if="headerInfo.label_attribute"
                :class="[
                  'text',
                  headerInfo.audit_status == -1 ? 'audit-status-red' : ''
                ]"
                :disabled="headerInfo.audit_status == 1"
              >
                <span>{{
                  headerInfo.label_attribute == 1 ? '白名单' : '整改中'
                }}</span>
                <custom-icon type="lu-icon-wrong" @click="deleteLabel" />
              </a> -->
              <LabelCard
                v-if="[0, 1].includes(headerInfo.label_attribute)"
                ref="labelCard"
                :labelStatus="headerInfo.label_status"
                :id="headerInfo.label_obj_id"
                :text="headerInfo.label_attribute"
                requstMode="everytime"
                @refresh="refresh"
              />
              <div v-else-if="sqlLabelStatus == 1 || sqlLabelStatus == 2">
                <div class="plus disabled" v-if="sqlLabelStatus == 1">
                  <custom-icon type="plus" />
                </div>
                <div v-else class="plus" @click="addSqlTag">
                  <custom-icon type="plus" />
                </div>
                <span class="color-text">特殊需求，可为SQL添加标签</span>
              </div>
              <div v-else>{{ '--' }}</div>
            </div>
          </div>
          <div class="title-flex" v-if="isFileReview">
            <div class="title-project">规则集</div>
            <span class="text file-path">{{
              headerInfo.rule_set && headerInfo.rule_set.length > 0
                ? headerInfo.rule_set.join()
                : '--'
            }}</span>
          </div>
        </div>
        <div class="title" v-else>
          <div class="title-flex">
            <div class="title-project">任务名称</div>
            <a-popover>
              <template slot="content">
                <span>{{ headerInfo.project_name }}</span>
              </template>
              <span class="text">{{ headerInfo.project_name || '--' }}</span>
            </a-popover>
          </div>
          <div class="title-flex">
            <div class="title-project">数据源</div>
            <span class="text db-type">
              <template v-if="dataSourceObj">
                <DbImg
                  :value="dataSourceObj.db_type"
                  :schemaName="
                    dataSourceObj.name + '(' + dataSourceObj.db_url + ')'
                  "
                  mode="ellipsis"
                />
              </template>
              <span v-else>--</span>
            </span>
          </div>
          <div class="title-flex">
            <div class="title-project">Schema</div>
            <span class="text file-path">{{
              dataSourceObj && dataSourceObj.schema_name.length > 0
                ? dataSourceObj.schema_name.join()
                : '--'
            }}</span>
          </div>
          <div class="title-flex">
            <div class="title-project">SQL来源</div>
            <a-popover>
              <template slot="content">
                <span>{{ headerInfo.file_path }}</span>
              </template>
              <span class="text file-path">{{
                headerInfo.file_name || '--'
              }}</span>
            </a-popover>
          </div>
        </div>
        <div class="btns">
          <a-divider type="vertical" class="btns-divider" />
          <a-button-group>
            <a-button
              :disabled="
                idList.length > 0 ? currentNum === 0 : headerInfo.index === 1
              "
              @click="reviewHeaderBtns(5)"
            >
              <custom-icon type="left" />上一条
            </a-button>
            <a-button
              :disabled="
                idList.length > 0
                  ? currentNum == idList.length - 1
                  : headerInfo.count === headerInfo.index
              "
              @click="reviewHeaderBtns(6)"
            >
              下一条
              <custom-icon type="right" />
            </a-button>
          </a-button-group>

          <span class="pageInfo" v-if="idList.length > 0"
            >{{ currentNum + 1 }}/{{ idList.length }}</span
          >
          <span class="pageInfo" v-else
            >{{ headerInfo.index }}/{{ headerInfo.count }}</span
          >
        </div>
      </div>
    </a-card>
  </div>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
import Status from '@/components/Biz/Status';
import DbImg from '@/components/CustomImg/DbImg';
import LabelCard from '@/components/Biz/LabelCard';
export default {
  components: { LimitLabel, Status, DbImg, LabelCard },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    idList: {
      type: Array,
      default: () => []
    },
    filterList: {
      type: Array,
      default: () => []
    },
    currentNum: {
      type: Number,
      default: 0
    },
    isWhite: {
      type: Boolean,
      default: true
    },
    dbaComment: {
      type: String,
      default: ''
    },
    dbaHandleComment: {
      type: String,
      default: ''
    },
    isOrder: {
      type: Boolean,
      default: false
    },
    jksuser: {
      type: String,
      default: ''
    },
    sqlLabelStatus: Number | String,
    dataSourceList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    isDba() {
      const user = this.$store.state.account.user || {};
      return user.role === 'dba';
    },
    isProjectRview() {
      const name = this.$route.name;
      return name == 'project-review-review';
    },
    isOrderRview() {
      const name = this.$route.name;
      return name == 'orderReview';
    },
    isFileReview() {
      const name = this.$route.name;
      return name == 'file-review-review';
    },
    dataSourceObj() {
      return this.dataSourceList.length > 0 ? this.dataSourceList[0] : null;
    }
  },
  data() {
    return {
      canDo: false,
      commentStatusList: [1, 2, 3, 4, 5, 6, 7],
      commentStatus: {
        1: '待处理',
        2: '已通过',
        3: '已拒绝',
        4: '加入白名单',
        5: '临时通过',
        6: '申请白名单通过',
        7: '申请临时通过'
      },
      commentColor: {
        1: ' #F29339',
        2: 'rgb(82, 196, 26)',
        3: 'rgba(231,29,54,1)',
        4: '#008ADC',
        5: 'rgb(82, 196, 26)',
        6: 'rgb(82, 196, 26)',
        7: 'rgb(82, 196, 26)'
      },
      value: ''
    };
  },
  mounted() {},
  methods: {
    reviewHeaderBtns(number) {
      this.$emit('reviewHeaderBtns', number);
    },
    onChangeMessage(event) {
      this.$emit('onChangeMessage', event.target.value);
    },
    onConfirm() {
      this.$emit('saveAdvice');
      this.value = '';
    },
    onCancel() {
      this.value = '';
    },
    // 打标
    addSqlTag() {
      this.$emit('addSqlTag');
    },
    deleteLabel() {
      this.$emit('deleteLabel');
    },
    refresh() {
      this.$emit('refresh', { id: this.headerInfo.label_obj_id }, true);
    }
  },
  watch: {}
};
</script>
<style lang="less">
.ant-popover {
  &.small-size {
    .ant-popover-content {
      .ant-popover-inner {
        .ant-popover-inner-content {
          max-width: 400px;
          max-height: 300px;
          > pre {
            font-size: 14px;
            color: #71717a;
            font-weight: 400;
          }
        }
      }
    }
  }
}
</style>
<style lang="less" scoped>
.review-header {
  margin-bottom: 24px;
  /deep/.ant-card-body {
    padding-left: 32px;
    padding-right: 32px;
  }
  .ant-card {
    border-radius: 8px;
    .content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        width: 75%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-flex {
          display: flex;
          flex-direction: column;
          justify-content: center;
          // margin-right: 24px;
          .title-project {
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #27272a;
            font-weight: 600;
            white-space: nowrap;
          }
          .text {
            display: inline-block;
            padding: 0 16px;
            border-radius: 24px;
            margin: 4px 0 0 0;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 14px;
            color: #71717a;
            font-weight: 400;
            background: #ffffff;
            // border: 1px solid #e4e4e7;
            padding-bottom: 2px;
            &.db-type {
              display: inline-block;
              max-width: 180px;
              .database-image {
                .limit-label {
                  width: 120px;
                }
              }
              &::before {
                display: none;
              }
              .anticon {
                margin-right: 4px;
              }
            }
          }
          .text::before {
            content: '';
            display: inline-block;
            position: relative;
            left: -5px;
            top: -1px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #71717a;
          }
          .sql-label {
            margin-top: 4px;
            .text {
              padding: 2px 12px;
              border-radius: 16px;
              border: 1px solid #e4e4e7;
              display: flex;
              align-items: center;
              &.audit-status-red {
                background: #e71d36;
                border: 1px solid #e71d36;
                > span {
                  color: #fff;
                }
              }
              &::before {
                content: '';
                display: none;
              }
              > span {
                font-size: 12px;
                color: #a1a1aa;
              }
              .anticon {
                display: none;
                color: #fff;
                width: 16px;
                height: 16px;
                line-height: 18px;
                font-size: 12px;
                border-radius: 50%;
                margin-left: 4px;
                border: none;
              }
              &:hover {
                cursor: pointer;
                .anticon {
                  background: #008adc;
                  display: inline-flex;
                  justify-content: center;
                  align-items: center;
                }
              }
            }
            a[disabled] {
              cursor: not-allowed;
              pointer-events: none;
              background: #f4f5f7;
              border: 1px solid #e4e4e7;
              > span {
                color: #a1a1aa;
              }
            }
            div {
              display: flex;
              align-items: center;
              .plus {
                width: 24px;
                height: 24px;
                min-width: 24px;
                min-height: 24px;
                border-radius: 12px;
                border: 1px solid #e4e4e7;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 6px;
                color: #71717a;
                &.disabled {
                  &:hover {
                    cursor: not-allowed;
                    border: 1px solid #e4e4e7;
                    color: #71717a;
                  }
                }
                > .anticon {
                  font-size: 12px;
                  font-weight: 600;
                }
                &:hover {
                  cursor: pointer;
                  border: 1px solid #008adc;
                  color: #008adc;
                }
              }
              .color-text {
                color: #f29339;
                white-space: nowrap;
              }
            }
          }
        }
      }
      .btns {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .btns-divider {
          margin: 0;
          margin-right: 32px;
          width: 1px;
          height: 20px;
          background: #e0e0e0;
        }
        .ant-btn-group {
          // position: relative;
          margin-right: 16px;
          .ant-btn {
            width: 84px;
            height: 30px;
            background: #ffffff;
            border: 1px solid #e4e4e7;
            border-radius: 6px;
            padding: 0;
            .anticon {
              // color: #71717a;
              margin-top: 4px;
              &.anitcon-left {
                margin-left: 2px;
              }
              &.anticon-right {
                margin-right: 2px;
              }
            }
            &:first-child {
              border-radius: 8px 0 0 8px !important;
            }
            &:last-child {
              border-radius: 0 8px 8px 0 !important;
            }
          }
        }
        .pageInfo {
          height: 32px;
          font-family: PingFangSC-Regular;
          font-size: 16px;
          color: #a1a1aa;
          text-align: right;
          line-height: 32px;
          font-weight: 400;
        }
      }
    }
  }
}
/deep/.ant-btn {
  > .anticon + span,
  > span + .anticon {
    margin-left: 2px !important;
  }
}
@media screen and (max-width: 1440px) {
  .review-header {
    .ant-card {
      .content {
        .title {
          flex-wrap: wrap;
          .title-flex {
            width: 20%;
            margin-bottom: 12px;
            margin-right: 32px;
            .text {
              max-width: 160px;
            }
            &:last-child {
              margin-right: 32px;
            }
            .title-project {
              font-family: PingFangSC-Semibold;
              font-size: 16px;
              color: #27272a;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}
</style>