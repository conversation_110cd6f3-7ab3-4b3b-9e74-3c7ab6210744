<template>
  <a-checkbox-group
    :class="className"
    v-bind="checkboxGroupProps"
    v-on="checkboxGroupListeners"
    :value="localValue"
  >
    <template v-for="item in options || []">
      <a-checkbox :key="item.value" v-bind="item || {}" @change="onClickItem">
        <span>{{item.label}}</span>
        <div class="ccg-tips" v-if="mode === 'tips' && item.tips">{{item.tips}}</div>
      </a-checkbox>
      <div :key="item.value + 'line'" v-if="item.lineFeed"></div>
    </template>
  </a-checkbox-group>
</template>

<script>
const defaultProps = {};
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    options: {
      type: Array,
      default: () => []
    },
    value: Array,
    mode: {
      type: String,
      default: 'normal' // normal, tips, button
    }
  },
  data() {
    return {
      localValue: this.value
    };
  },
  mounted() {},
  beforeDestroy() {},
  computed: {
    checkboxGroupProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    checkboxGroupListeners() {
      return { ...this.$listeners, ...{ change: this.handleChange } };
    },
    className() {
      let res = ['custom-checkbox-group'];
      if (this.mode === 'tips') {
        res.push('ccg-with-tips');
      }
      if (this.mode === 'button') {
        res.push('checkbox-group-button');
      }
      return res;
    }
  },
  methods: {
    handleChange(e) {
      // console.log(e);
      this.localValue = e;
      this.$emit('change', e);
    },
    onClickItem(e) {
      this.$emit('clickItem', e);
    }
  },
  watch: {
    value(newVal, oldVal) {
      if (newVal != oldVal) {
        this.localValue = newVal;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.custom-checkbox-group {
  &.ccg-with-tips {
    display: block;
    /deep/ .ant-checkbox-wrapper {
      display: flex;
      align-items: flex-start;
      background: #ffffff;
      border: 1px solid #E4E4E7;
      border-radius: 4px;
      padding: 12px 24px;
      margin-bottom: 12px;
      margin-left: 0;
      margin-right: 0;
      white-space: normal;

      .ant-checkbox {
        top: 2px;
      }

      .ccg-tips {
        color: rgba(0, 0, 0, 0.25);
        font-size: 12px;
        word-break: break-all;
        margin-top: 4px;
      }
      &.ant-checkbox-wrapper-disabled {
        cursor: not-allowed;
      }

      &:not(.ant-checkbox-wrapper-disabled).ant-checkbox-wrapper-checked {
        border-color: @checked-border-color;
        background: @checked-bg;
      }
      &:not(.ant-checkbox-wrapper-disabled):hover {
        border-color: @checked-border-color;
        background: @checked-bg;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
    &.inline {
      display: block;
      /deep/ .ant-checkbox-wrapper {
        display: inline-block;
        padding: 6px 12px 6px 14px;
        .ant-checkbox {
          top: 0;
        }
      }
    }
  }
  &.checkbox-group-button {
    /deep/ .ant-checkbox-wrapper {
      display: inline-block;
      height: 32px;
      line-height: 32px;
      margin: 0 8px 8px 0;
      padding: 0 15px;
      border-radius: 4px;
      .border(1px, 1px, 1px, 1px);
      .ant-checkbox {
        display: none;
      }
      &:hover {
        color: @primary-color;
      }
      &.ant-checkbox-wrapper-checked {
        color: #fff;
        background: @primary-color;
        border-color: @primary-color;
      }
    }
  }
}
</style>