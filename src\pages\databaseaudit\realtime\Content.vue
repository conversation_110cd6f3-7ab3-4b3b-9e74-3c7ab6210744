<template>
  <div>
    <div class="database-audit-real-time" v-if="flag">
      <div class="header">
        <div class="header-left">
          <span
            class="setting-border"
            v-if="['MYSQL', 'GOLDENDB'].includes(dbType)"
          >
            Innodb Buffer Pool Size:
            <span>{{ innodbBufferPoolSize }}</span>
          </span>
          <span class="setting-border">
            Version:
            <span>{{ version }}</span>
          </span>
          <div
            style="display: inline-block"
            v-if="['OB_ORACLE', 'OB_MYSQL'].includes(dbType)"
          >
            <span>OBServer:</span>
            <span class="server-box">
              <a-dropdown v-model="serverVisible" class="tab-bar-btn">
                <a class="ant-dropdown-link" @click="(e) => e.preventDefault()">
                  {{ server }}
                  <a-icon type="down" />
                </a>
                <a-menu slot="overlay" @click="changeServer">
                  <a-menu-item v-for="item in serverData" :key="item.value">{{
                    item.label
                  }}</a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
            <span>租户:</span>
            <span class="tenant-box">
              <a-dropdown v-model="tenantVisible" class="tab-bar-btn">
                <a class="ant-dropdown-link" @click="(e) => e.preventDefault()">
                  {{ tenant }}
                  <a-icon type="down" />
                </a>
                <a-menu slot="overlay" @click="changeTenant">
                  <a-menu-item v-for="item in tenantData" :key="item.value">{{
                    item.label
                  }}</a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </div>
          <span>{{ time }} 更新</span>
          <span class="reload-box">
            <custom-icon type="sync" />
            <a-dropdown v-model="visible" class="tab-bar-btn">
              <a class="ant-dropdown-link" @click="(e) => e.preventDefault()">
                {{ value + '秒' }}
                <a-icon type="down" />
              </a>
              <a-menu slot="overlay" @click="handleMenuClick">
                <a-menu-item key="1000">1秒</a-menu-item>
                <a-menu-item key="5000">5秒</a-menu-item>
                <a-menu-item key="10000">10秒</a-menu-item>
                <a-menu-item key="30000">30秒</a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
        </div>
        <div
          :class="['header-right', isPause && 'pause']"
          @click="!isPause ? pause() : resume()"
        >
          <span>{{ isPause ? '暂停' : '进行中' }}</span>
          <custom-icon :type="isPause ? 'caret-right' : 'pause'" />
        </div>
      </div>
      <div class="echart-block">
        <div class="line-echart-block">
          <div
            class="custom-line-echart"
            :style="{ width: `${(2 / chartData.length) * 100}%` }"
            v-for="item in chartData"
            :key="item.name"
          >
            <div class="title">{{ item.name }}</div>
            <div class="content">
              <span>{{ item.num }}</span>
              <Chart :option="item.arr || []" ref="line" />
            </div>
          </div>
        </div>
        <div class="bar-echart-block">
          <Chart :option="barOption" ref="bar" />
        </div>
      </div>
      <div class="search-area">
        <div class="title">SQL列表</div>
        <div class="search-content">
          <a-input-search placeholder="INFO" @search="searchInfo" />
          <a-input-search placeholder="HOST" @search="searchHost" />
          <Select
            v-bind="databaseParams"
            v-model="database"
            @change="handleMenuDatabase"
          />
          <Select
            v-bind="userParams"
            v-model="user"
            @change="handleMenuUser"
            style="margin-right: 32px"
            v-if="!['OB_ORACLE', 'OB_MYSQL'].includes(dbType)"
          />
          <a-input-search placeholder="User" @search="searchUser" v-else />
          <a-divider type="vertical" />
          <a-button
            type="primary"
            v-if="dbType == 'MYSQL' && $permission.realTimeSql('batchKill')"
            @click="kill"
            class="kill-btn"
            >批量KILL</a-button
          >
          <div class="switch">
            <span>过滤Sleep空闲连接</span>
            <a-switch default-unchecked @change="onChange" size="small" />
          </div>
        </div>
      </div>
      <div class="table-block">
        <Table
          ref="table"
          v-bind="tableParams"
          :dataSource="dataSource || []"
          @sorter="sorter"
          class="new-view-table small-size"
        >
          <LimitLabel
            slot="info"
            slot-scope="{ text }"
            :label="text"
            :limit="16"
          ></LimitLabel>
          <LimitLabel
            slot="sql_text"
            slot-scope="{ text }"
            :label="text"
            :limit="16"
          ></LimitLabel>
          <LimitLabel
            slot="sql_id"
            slot-scope="{ text }"
            :label="text"
            :limit="16"
          ></LimitLabel>
          <LimitLabel
            slot="trace_id"
            slot-scope="{ text }"
            :label="text"
            :limit="16"
          ></LimitLabel>
          <div slot="customTitle" class="custom-title" @click="customSetColumn">
            <span>操作</span>
            <a-tooltip>
              <template slot="title">表头设置</template>
              <a-icon type="control" />
            </a-tooltip>
          </div>
          <custom-btns-wrapper slot="action" slot-scope="{ record }">
            <a
              actionBtn
              @click="onSqlException(record)"
              :disabled="!record.info"
              v-if="$permission.realTimeSql('explain')"
              >Explain</a
            >
            <a-popconfirm
              title="确定kill?"
              @confirm="() => singleKill(record)"
              v-if="$permission.realTimeSql('kill')"
              actionBtn
            >
              <a>Kill</a>
            </a-popconfirm>
          </custom-btns-wrapper>
        </Table>
      </div>
    </div>

    <div class="ps-empty" v-else>
      <div class="img">
        <img src="~@/assets/img/private/info.svg" alt />
      </div>
      <div class="des">
        OceanBase数据库审核需要连接到sys系统租户进行数据库执行SQL采集，请配置sys系统租户账号密码。
      </div>
      <a-button type="primary" @click="add">数据库审核配置</a-button>
    </div>
    <KillModal ref="kill"></KillModal>
    <SqlExceptionDrawer ref="sqlException" :id="id" :dbType="dbType" />
    <CustomSetColumnModal
      ref="customSetColumn"
      :id="id"
      @save="save"
    ></CustomSetColumnModal>
    <DatabaseAuditModal
      ref="databaseAudit"
      :id="id"
      @save="audit"
    ></DatabaseAuditModal>
  </div>
</template>

<script>
import Tag from '@/components/Biz/Tag';
import Status from '@/components/Biz/Status';
import config from './config';
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import DbImg from '@/components/CustomImg/DbImg';
import Chart from '@/components/Chart';
import Select from '@/components/Select';
import DatabaseAuditModal from './DatabaseAuditModal';
import SqlExceptionDrawer from '../components/sqlExceptionDrawer';
import CustomSetColumnModal from '../components/customSetColumnModal';
import KillModal from './KillModal';
import {
  getRealTimeInfo,
  getObRealTimeInfo,
  getGoldendbRealTimeInfo,
  goldendbRealtimeKill,
  realtimeKill,
  checkObConfig,
  getOboracleServer,
  getOboracleTenant,
  oboracleRealtimeKill
} from '@/api/databaseaudit/realtime';
import {
  getTableHeaderInfo,
  saveTableHeaderInfo
} from '@/api/databaseaudit/topsql';
export default {
  components: {
    Tag,
    DbImg,
    Table,
    Chart,
    Select,
    Status,
    LimitLabel,
    SqlExceptionDrawer,
    DatabaseAuditModal,
    CustomSetColumnModal,
    KillModal
  },
  props: {
    pane: Object,
    id: Number | String,
    paneKey: String
  },
  computed: {
    // canDo() {
    //   const user = this.$store.state.account.user || {};
    //   return ['leader', 'admin', 'dba'].includes(user.role);
    // }
  },
  data() {
    this.config = config(this);
    this.reqCancelHandler = null; // 取消请求句柄
    return {
      inited: false,
      value: 1,
      barOption: this.config.barOption(),
      dataSource: [],
      columns: [],
      tableParams: {
        url: '',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id',
        // scroll: { x: 1400 },
        scroll: { x: 'max-content' }
      },
      databaseParams: {
        url: '',
        reqParams: {},
        placeholder: 'DB',
        // allowSearch: true,
        // backSearch: true,
        limit: 1000
      },
      userParams: {
        url: '/sqlreview/after_audit/select_data',
        reqParams: { data_source_id: this.id, select_type: 'user' },
        placeholder: 'USER',
        // allowSearch: true,
        // backSearch: true,
        limit: 1000
      },
      visible: false,
      innodbBufferPoolSize: null,
      version: null,
      time: null,
      count: 0,
      timeout: 1000,
      params: { id: this.id },
      database: undefined,
      user: undefined,
      databaseList: [],
      userList: [],
      isPause: false,
      wrongNum: 0,
      chartData: [],
      serverData: [],
      tenantData: [],
      server: 'ALL',
      tenant: 'ALL',
      tenantVisible: false,
      serverVisible: false,
      dbType: null,
      flag: true
    };
  },
  created() {},
  mounted() {
    if (['OB_ORACLE', 'OB_MYSQL'].includes(this.pane.db_type)) {
      this.checkFn();
    } else {
      this.init();
    }
  },
  beforeDestroy() {
    this.pageDestroyed = true;
    this.cancel();
  },
  methods: {
    add() {
      this.$refs.databaseAudit.show();
    },
    audit() {
      this.flag = true;
      this.init();
    },
    // ob数据库 首先查询实时是否存在
    checkFn() {
      // this.$showLoading();
      checkObConfig({ data_source_id: this.id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            const flag = _.get(res, 'data.data.flag');
            this.flag = flag;
            if (flag) {
              this.init();
              return;
            }
            this.$refs.databaseAudit.show();
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    init() {
      // 获取展示表头
      getTableHeaderInfo({ data_source_id: this.id, source_type: 3 })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data') || {};
            const list = resData.display_list;
            this.dealTableHeaderInfo(list);
            ['OB_ORACLE', 'OB_MYSQL'].includes(this.pane.db_type) &&
              this.getTenantAndSercer();
            this.getTableSearchData();
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
      // 开启轮询
      this.onSetInterval();
    },
    getTenantAndSercer() {
      getOboracleServer({ id: this.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.serverData = _.get(res, 'data.data') || {};
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
      getOboracleTenant({ id: this.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.tenantData = _.get(res, 'data.data') || {};
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    getTableSearchData() {
      switch (this.dbType) {
        case 'MYSQL':
        case 'GOLDENDB':
          this.$set(
            this.databaseParams,
            'url',
            this.dbType == 'GOLDENDB'
              ? `/sqlreview/api/v1/realtime/goldendb/${this.id}/databases`
              : `/sqlreview/api/v1/realtime/mysql/${this.id}/databases`
          );
          this.$set(
            this.userParams,
            'url',
            this.dbType == 'GOLDENDB'
              ? `/sqlreview/api/v1/realtime/goldendb/${this.id}/users`
              : `/sqlreview/api/v1/realtime/mysql/${this.id}/users`
          );
          this.$set(this.databaseParams, 'reqParams', {
            data_source_id: this.id,
            select_type: 'schema'
          });
          break;
        case 'OB_ORACLE':
        case 'OB_MYSQL':
          this.$set(
            this.databaseParams,
            'url',
            `/sqlreview/api/v1/realtime/oboracle/${this.id}/database`
          );
          this.$set(this.databaseParams, 'reqParams', {});
          break;
        default:
          break;
      }
    },
    // 手动用setTimeout 实现setInterval
    onSetInterval() {
      if (['MYSQL', 'GOLDENDB'].includes(this.dbType)) {
        this.mysqlOrGoldendbLoopFn();
      } else if (['OB_ORACLE', 'OB_MYSQL'].includes(this.dbType)) {
        this.obLoopFn();
      }
    },
    mysqlOrGoldendbLoopFn() {
      let req =
        this.dbType == 'MYSQL' ? getRealTimeInfo : getGoldendbRealTimeInfo;
      const loop = (params = {}) => {
        if (this.pageDestroyed) return;
        req(params, c => {
          this.reqCancelHandler = c;
        })
          .then(res => {
            this.count++;
            if (CommonUtil.isSuccessCode(res)) {
              const resData = _.get(res, 'data.data') || {};

              this.dataSource = resData.process_list;

              const abortedConnects = resData.aborted_connects;
              const tableLocks = resData.table_locks_waited;
              const connects = resData.threads_connected;
              const slowLaunch = resData.slow_launch_threads;
              const qps = resData.qps;
              const tps = resData.tps;
              const threadsRunning = resData.threads_running;
              const openTable = resData.open_table_definitions;

              const abortedConnectsList = resData.aborted_connects_list || [];
              const tableLocksList = resData.table_locks_waited_list || [];
              const connectsList = resData.threads_connected_list || [];
              const slowLaunchList = resData.slow_launch_threads_list || [];
              const qpsList = resData.qps_list || [];
              const tpsList = resData.tps_list || [];
              const threadsRunningList = resData.threads_running_list || [];
              const openTableList = resData.open_table_definitions_list || [];

              this.chartData = [
                {
                  name: ' Running Threads',
                  num: threadsRunning,
                  arr: this.config.lineOption(
                    '#A3A9EF',
                    '#E4E6FB',
                    threadsRunningList
                  )
                },
                {
                  name: 'Open Tables',
                  num: openTable,
                  arr: this.config.lineOption(
                    '#90C6AC',
                    '#E5F3E8',
                    openTableList
                  )
                },
                {
                  name: 'Slow Queries',
                  num: slowLaunch,
                  arr: this.config.lineOption(
                    '#90C6AC',
                    '#E5F3E8',
                    slowLaunchList
                  )
                },
                {
                  name: 'Connections',
                  num: connects,
                  arr: this.config.lineOption(
                    '#E4BF94',
                    '#F8F2E4',
                    connectsList
                  )
                },
                {
                  name: 'Aborted Connects',
                  num: abortedConnects,
                  arr: this.config.lineOption(
                    '#F1A1A1',
                    '#FBE6E0',
                    abortedConnectsList
                  )
                },
                {
                  name: ' Table Locks',
                  num: tableLocks,

                  arr: this.config.lineOption(
                    '#EE9DBA',
                    '#FAE1E7',
                    tableLocksList
                  )
                },
                {
                  name: 'QPS',
                  num: qps,
                  arr: this.config.lineOption('#C0A1D8', '#EDE1F2', qpsList)
                },
                {
                  name: 'TPS',
                  num: tps,
                  arr: this.config.lineOption('#A9A7AB', '#E4E4E7', tpsList)
                }
              ];

              if (this.count == 1) {
                this.version = resData.version;
                this.time = resData.time;
                this.innodbBufferPoolSize = resData.innodb_buffer_pool_size;
              }
              const barData = [
                resData.com_select,
                resData.com_insert,
                resData.com_update,
                resData.com_delete
              ];
              this.$set(this, 'barOption', this.config.barOption(barData));
            } else {
              if (this.count > 1) return;
              this.$message.error(_.get(res, 'data.message'));
            }
          })
          .catch(e => {
            this.wrongNum++;
            if (this.wrongNum > 0) return;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          })
          .finally(e => {
            if (!this.inited) {
              this.inited = true;
            }
            if (this.wrongNum >= 5) {
              this.isPause = !this.isPause;
              this.cancel();
              return;
            }
            clearTimeout(this.timer);
            // 取消接口请求
            if (this.reqCancelHandler) {
              this.reqCancelHandler();
            }
            // 暂停之后 搜索会再调一次接口 这时不轮巡
            if (!this.isPause) {
              this.timer = setTimeout(() => {
                const params = this.params;
                loop(params);
              }, this.timeout);
            }
          });
      };
      loop(this.params);
    },
    obLoopFn() {
      const loop = (params = {}) => {
        if (this.pageDestroyed) return;
        getObRealTimeInfo(params, c => {
          this.reqCancelHandler = c;
        })
          .then(res => {
            let count = this.count++;
            if (CommonUtil.isSuccessCode(res)) {
              const resData = _.get(res, 'data.data') || {};
              this.dataSource = resData.process_list;

              const chartList = resData.variables;

              const colorList = [
                { one: '#A3A9EF', two: '#E4E6FB' },
                { one: '#90C6AC', two: '#E5F3E8' },
                { one: '#90C6AC', two: '#E5F3E8' },
                { one: '#E4BF94', two: '#F8F2E4' },
                { one: '#F1A1A1', two: '#FBE6E0' },
                { one: '#EE9DBA', two: '#FAE1E7' }
              ];

              this.chartData = [];
              chartList.forEach((item, index) => {
                colorList.forEach((elem, idx) => {
                  if (index == idx) {
                    this.chartData.push({
                      ...item,
                      arr: this.config.lineOption(elem.one, elem.two, item.arr)
                    });
                  }
                });
              });

              if (count == 0) {
                this.version = resData.version;
                this.time = resData.time;
                this.innodbBufferPoolSize = resData.innodb_buffer_pool_size;
              }

              const barList = resData.extra;
              const barData = [
                barList.com_select,
                barList.com_insert,
                barList.com_update,
                barList.com_delete
              ];
              this.$set(this, 'barOption', this.config.barOption(barData));
            } else {
              if (this.count > 1) return;
              this.$message.error(_.get(res, 'data.message'));
            }
          })
          .catch(e => {
            this.wrongNum += 1;
            if (this.wrongNum > 0) return;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          })
          .finally(e => {
            if (!this.inited) {
              this.inited = true;
            }
            if (this.wrongNum >= 5) {
              this.isPause = !this.isPause;
              this.cancel();
              return;
            }
            clearTimeout(this.timer);
            // 取消接口请求
            if (this.reqCancelHandler) {
              this.reqCancelHandler();
            }
            // 暂停之后 搜索会再调一次接口 这时不轮巡
            if (!this.isPause) {
              this.timer = setTimeout(() => {
                const params = this.params;
                loop(params);
              }, this.timeout);
            }
          });
      };
      loop(this.params);
    },
    // 处理表头数据
    dealTableHeaderInfo(data = []) {
      let columns = [];
      data.forEach(item => {
        this.columns.forEach(el => {
          if (item.key && item.key == el.key) {
            columns.push({ ...el, title: item.value, sorter: item.sorter });
          }
        });
      });
      columns.push({
        // title: '操作',
        key: 'action',
        slots: { title: 'customTitle' },
        scopedSlots: { customRender: 'action' },
        visible: $permissionBatch.some([
          { module: 'realTimeSql', values: ['kill', 'explain'] }
        ]),
        fixed: 'right'
      });
      columns = columns
        .map(item => {
          return {
            ...item,
            width: undefined
          };
        })
        .filter(item => {
          return item.visible !== false;
        });
      this.$set(this.tableParams, 'columns', columns);
    },
    // 设置表头弹窗
    customSetColumn() {
      this.$refs.customSetColumn.show(3);
    },
    // 设置更新秒数
    handleMenuClick(e) {
      this.value = e.key / 1000;
      this.timeout = e.key;
      this.visible = false;
    },
    handleMenuDatabase(e) {
      this.database = e;
      this.params = {
        ...this.params,
        db: e
      };
      if (this.isPause) this.onSetInterval();
    },
    handleMenuUser(e) {
      this.user = e;
      this.params = {
        ...this.params,
        user: e
      };
      if (this.isPause) this.onSetInterval();
    },
    changeTenant(e) {
      this.tenant = e.key;
      this.params = {
        ...this.params,
        tenant: e.key
      };
    },
    changeServer(e) {
      this.server = e.key;
      this.params = {
        ...this.params,
        server: e.key
      };
    },
    searchInfo(value) {
      this.params = {
        ...this.params,
        info: value
      };
      if (this.isPause) this.onSetInterval();
    },
    searchHost(value) {
      this.params = {
        ...this.params,
        host: value
      };
      if (this.isPause) this.onSetInterval();
    },
    searchUser(value) {
      this.params = {
        ...this.params,
        user: value
      };
      if (this.isPause) this.onSetInterval();
    },
    // 过滤Sleep空闲连接切换
    onChange(e) {
      this.params = {
        ...this.params,
        no_sleep: e
      };
      if (this.isPause) this.onSetInterval();
    },
    save(data = [], list = []) {
      this.$showLoading();
      const params = {
        db_type: 'MYSQL',
        source_type: 3,
        keys: [...data]
      };
      saveTableHeaderInfo(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.dealTableHeaderInfo(list);
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 停止计时器
    cancel() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      // 取消接口请求
      if (this.reqCancelHandler) {
        this.reqCancelHandler();
      }
      this.count = 0;
      this.wrongNum = 0;
      // this.params = { id: this.id };
    },
    // 暂停
    pause() {
      this.isPause = true;
      this.cancel();
    },
    // 恢复
    resume() {
      if (!this.inited) return;
      this.isPause = false;
      this.cancel();
      this.onSetInterval();
    },
    // 全部kill
    kill() {
      this.$refs.kill.show(this.id, this.dbType);
    },
    // 下载
    download() {},
    // 执行计划
    onSqlException(record) {
      this.$refs.sqlException.show('realtime', record, this.pane);
    },
    // 列表单个kill
    singleKill(record) {
      const params = {
        data_source_id: this.id,
        id: record.id
      };
      let req;
      if (this.dbType == 'MYSQL') {
        req = realtimeKill;
      } else if (this.dbType == 'GOLDENDB') {
        req = goldendbRealtimeKill;
      } else if (['OB_ORACLE', 'OB_MYSQL'].includes(this.dbType)) {
        req = oboracleRealtimeKill;
      }
      req(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: '操作成功'
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.msg')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    sorter(data) {
      let key = data.columnKey;
      let order = data.order;
      if (key && order) {
        this.params = {
          ...this.params,
          _sorter: { [key]: order }
        };
      } else {
        this.params._sorter && delete this.params._sorter;
      }
      if (this.isPause) this.onSetInterval();
    }
  },
  watch: {
    pane: {
      handler(newVal) {
        this.dbType = newVal.db_type;
        switch (this.dbType) {
          case 'MYSQL':
          case 'GOLDENDB':
            this.columns = this.config.columns;
            break;
          case 'OB_ORACLE':
          case 'OB_MYSQL':
            this.columns = this.config.obColumns;
            break;
          default:
            break;
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.custom-title {
  width: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .anticon {
    margin-right: 4px;
    font-size: 14px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    line-height: 24px;
    // color: #27272a;
    &:hover {
      background: @primary-3;
      color: #ffffff;
      cursor: pointer;
    }
  }
}
.database-audit-real-time {
  // background: #fff;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    background: #ffffff;
    padding: 0 32px;
    border-bottom: 1px solid #ebebec;
    .header-left {
      font-size: 13px;
      color: #a1a1aa;
      font-weight: 400;
      .setting-border {
        border-right: 1px solid #ebebec;
        padding: 0 8px;
        margin-right: 8px;
        > span {
          color: #27272a;
        }
        &:first-child {
          padding: 0 8px 0 0;
          margin-right: 0;
        }
      }
      .server-box,
      .tenant-box,
      .reload-box {
        margin: 0 8px;
        .anticon-sync {
          font-size: 12px;
          color: #27272a;
        }
        .ant-dropdown-link {
          font-size: 13px;
          color: #27272a;
          font-weight: 400;
        }
      }
    }
    .header-right {
      background: #ffffff;
      font-size: 14px;
      color: #008adc;
      text-align: center;
      font-weight: 400;
      border: 1px solid #a5d9f8;
      border-radius: 3px;
      > span {
        padding: 8px 12px;
        border-right: none;
      }
      .anticon {
        padding: 8px 6px;
        border-left: 1px solid #a5d9f8;
      }
      &:hover {
        cursor: pointer;
        background: #eff5ff;
      }
      &.pause {
        color: #ef6173;
        border: 1px solid #f7bbc2;
        .anticon {
          border-left: 1px solid #f7bbc2;
        }
        &:hover {
          cursor: pointer;
          background: #fef3f5;
        }
      }
    }
  }
  /deep/.echart-block {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    background: #ffffff;
    border-radius: 0 0 12px 12px;
    margin-bottom: 16px;
    .line-echart-block {
      width: 75%;
      display: flex;
      flex-wrap: wrap;
      padding: 24px 0px 8px 32px;
      .custom-line-echart {
        margin-bottom: 16px;
        .title {
          font-size: 14px;
          color: #27272a;
          font-weight: 400;
          margin-bottom: 8px;
        }
        .content {
          display: flex;
          align-items: flex-end;
          height: 42px;
          > span {
            margin-right: 16px;
            font-size: 28px;
            color: #27272a;
            font-weight: 600;
            vertical-align: bottom;
            display: inline-block;
          }
          .custom-chart {
            .chart-container {
              width: 40% !important;
              height: 42px !important;
            }
          }
        }
      }
    }
    .bar-echart-block {
      border-left: 1px solid #e0e0e0;
      width: 25%;
      // min-width: 240px;
      height: 220px;
      padding: 24px 16px 0 32px;
    }
  }

  .search-area {
    background: #ffffff;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    // border-bottom: 1px solid #e8e8e8;
    border-radius: 12px 12px 0 0;
    .title {
      font-size: 16px;
      color: #27272a;
      font-weight: 600;
      // width: 180px;
      margin-right: 32px;
    }
    .search-content {
      width: 88%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .ant-input-search {
        width: 160px;
        margin-right: 16px;
        &:last-child {
          margin-right: 32px;
        }
      }
      .ant-select {
        width: 160px;
        margin-right: 16px;
      }
      .tab-bar-btn {
        margin-right: 32px;
        &.ant-dropdown-link {
          color: #27272a;
          .anticon {
            color: #8c8c8c;
          }
        }
        &.ant-btn {
          line-height: 24px;
          height: 24px;
          padding: 0 16px;
          font-size: 12px;
          border-radius: 5px !important;
        }
        &.anticon {
          font-size: 16px;
          border-radius: 50%;
          width: 26px;
          height: 26px;
          line-height: 30px;
          &:hover {
            background: #4ec3f5;
            color: #ffffff;
            cursor: pointer;
          }
        }
        &.anticon-setting {
          color: #27272a;
        }
        &.anticon-vertical-align-bottom {
          color: #000;
        }
      }
      .ant-divider {
        margin: 0;
        width: 1px;
        height: 20px;
        background: #e0e0e0;
      }
      .switch {
        white-space: nowrap;
        // margin: 0 32px;
        margin-left: 32px;
      }
      .kill-btn {
        background: #008adc;
        border-radius: 4px;
        margin-left: 32px;
        > span {
          padding: 16px 24px;
          font-size: 14px;
          color: #ffffff;
          font-weight: 400;
        }
      }
    }
  }
  .table-block {
    border-radius: 0 0 12px 12px;
    background: #ffffff;
    /deep/.new-view-table {
      .ant-table-wrapper {
        .ant-spin-container {
          .ant-table {
            top: -8px;
            .ant-table-thead tr > .ant-table-selection-column {
              padding: 16px 0 16px 32px !important;
            }
            .ant-table-tbody tr > .ant-table-selection-column {
              padding: 16px 0 16px 32px !important;
            }
          }
        }
      }
    }
  }
}
.ps-empty {
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 800px;
  .des {
    margin: 32px 0;
  }
}

@media screen and (max-width: 1440px) {
  .database-audit-real-time {
    .search-area {
      .search-content {
        .ant-select,
        .ant-input-search {
          width: 125px;
        }
      }
    }
  }
}
</style>
<style lang="less">
.real-time-tab-bar-dropdown {
  .ant-dropdown-menu {
    width: 180px;
    .ant-dropdown-menu-item {
      &.ant-dropdown-menu-item-active {
        color: #1890ff;
      }
    }
  }
}
</style>