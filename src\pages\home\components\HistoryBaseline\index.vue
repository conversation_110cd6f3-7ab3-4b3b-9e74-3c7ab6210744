<template>
  <a-modal
    v-model="visible"
    okText="确定"
    cancelText="取消"
    @ok="save"
    @cancel="onCancel"
    width="45%"
    :dialogStyle="{ minWidth: '300px', maxWidth: '400px' }"
    wrapClassName="home-sqlreview-index-history-baseline-modal"
  >
    <div class="content-box">
      <div class="title">
        <a-icon type="question-circle" />
        <span>设置为历史标准基线吗？</span>
      </div>
      <div class="subtitle">设置为历史标准基线后，该批次的所有SQL将被标记为历史SQL。历史SQL在增量扫描时将做忽略处理。</div>
    </div>
  </a-modal>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      visible: false,
      cardData: {}
    };
  },
  methods: {
    show(data = {}) {
      this.visible = true;
      this.cardData = data;
    },
    onCancel() {
      this.visible = false;
    },
    save() {
      this.onCancel();
      const id = this.cardData.id;
      this.$emit('onSave', id);
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.home-sqlreview-index-history-baseline-modal {
  .content-box {
    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      margin-bottom: 16px;
      color: @font-color-strong;
      vertical-align: middle;
      .anticon-question-circle {
        margin-right: 8px;
        // color: rgb(246, 176, 84);
        color: rgb(244, 153, 35);
        font-size: 32px;
      }
    }
    .subtitle {
      margin-bottom: 8px;
      margin-left: 40px;
    }
  }
}
/deep/ .ant-radio-wrapper {
  border: none !important;
  padding: 12px 40px !important;
}
</style>
