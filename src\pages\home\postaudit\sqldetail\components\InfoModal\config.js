export default function (ctx) {
  const tableColumns = [
    {
      title: '统计信息收集时间',
      dataIndex: 'last_analyzed',
      key: 'last_analyzed',
      width: 220
    },
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name',
      scopedSlots: { customRender: 'table_name' },
      width: 200
    },
    {
      title: '行数',
      dataIndex: 'num_rows',
      key: 'num_rows',
      width: 200
    },
    {
      title: '容量数',
      dataIndex: 'size_gb',
      key: 'size_gb',
      width: 200
    },
    {
      title: 'BLOCK数',
      key: 'blocks',
      dataIndex: 'blocks',
      width: 200
    },
    {
      title: '平均行长',
      key: 'avg_row_len',
      dataIndex: 'avg_row_len',
      width: 200
    },
    {
      title: '是否过期',
      key: 'stale_stats',
      dataIndex: 'stale_stats',
      width: 200
    }
  ];

  const indexColumns = (params = {}) => {
    const { combineInfo = {} } = params;
    const { index_name: indexName } = combineInfo;
    return [
      {
        title: '索引名称',
        dataIndex: 'index_name',
        key: 'index_name',
        width: 200,
        customRender: (value, row, index) => {
          let obj = {
            children: value,
            attrs: {}
          };
          if (indexName) {
            let uid = value + '_' + index;
            let matchItem = indexName[uid];
            if (matchItem) {
              obj.attrs.rowSpan = matchItem.rowSpan;
            }
          }
          return obj;
        }
      },
      {
        title: '索引类型',
        key: 'index_type',
        dataIndex: 'index_type',
        width: 200
      },
      {
        title: '索引高度',
        key: 'blevel',
        dataIndex: 'blevel',
        width: 200
      },
      {
        title: '叶子节点数量',
        dataIndex: 'leaf_blocks',
        key: 'leaf_blocks',
        width: 200
      },
      {
        key: 'clustering_factor',
        dataIndex: 'clustering_factor',
        slots: { title: 'titleFactor' },
        width: 200
      },
      {
        title: '索引状态',
        key: 'status',
        dataIndex: 'status',
        width: 200
      }
    ];
  };

  const fieldColumns = [
    {
      title: '字段名称',
      dataIndex: 'column_name',
      key: 'column_name',
      width: 200
    },
    {
      title: '字段类型',
      dataIndex: 'column_type',
      key: 'column_type',
      width: 200
    },
    {
      dataIndex: 'ndv',
      key: 'ndv',
      slots: { title: 'titleNdv' },
      width: 200
    },
    {
      title: '选择性',
      dataIndex: 'selectity',
      key: 'selectity',
      width: 200
    },
    {
      title: 'NULL值数量',
      key: 'num_nulls',
      dataIndex: 'num_nulls',
      width: 200
    },
    {
      title: '字段平均长度',
      dataIndex: 'avg_col_len',
      key: 'avg_col_len',
      width: 200
    }
  ];

  const baseInfo = [
    {
      type: 'Select',
      label: '审核用户',
      key: 'schema',
      props: {
        url: 'sqlreview/review/get_schema_from_table',
        reqParams: {
          id: ctx.$route.query.id
        },
        loaded(data) {
          if (data.length > 0) {
            ctx.getTableInfoData(data[0].value);
            const { baseInfo } = ctx.$refs;
            baseInfo.saving({
              schema: data[0].value
            });
          }
        }
      },
      listeners: {
        change: (value) => {
          if (value) {
            ctx.getTableInfoData(value);
          }
        }
      },
      width: 200
    }
  ];
  return {
    tableColumns,
    indexColumns,
    fieldColumns,
    baseInfo
  };
}
