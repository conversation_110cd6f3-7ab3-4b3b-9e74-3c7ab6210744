<template>
  <a-spin tip="加载中" :spinning="loading">
    <div class="home-review-wraper">
      <div class="frame-button-wrapper-relative-blank">
        <a-button class="back-btn highlight" @click="toBack" v-if="hasBack"
          >返回</a-button
        >
        <!-- <a-dropdown
          :disabled="!canDo && !isLeader && !isAdmin"
          v-if="(canDo || isLeader || isAdmin) && isOrder"
          overlayClassName="review-agree-overlay"
          :getPopupContainer="getPopupContainer"
          class="review-agree"
        >
          <a-menu
            class="review-agree-menu"
            slot="overlay"
            @click="(event) => handleMenuClick('agree', event)"
          >
            <a-menu-item v-for="item in agreeList" v-bind:key="item.key">
              {{
              item.name
              }}
            </a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px;">
            <a-icon type="file-done" />通过
            <a-icon type="down" />
          </a-button>
        </a-dropdown>
        <a-dropdown
          :disabled="!canDo && !isLeader && !isAdmin"
          v-if="(canDo || isLeader || isAdmin) && isOrder"
          overlayClassName="review-disagree-overlay"
          :getPopupContainer="getPopupContainer"
          class="review-disagree"
        >
          <a-menu slot="overlay" @click="(event) => handleMenuClick('disagree', event)">
            <a-menu-item v-for="item in disagreeList" v-bind:key="item.key">
              {{
              item.name
              }}
            </a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px;">
            <a-icon type="close-circle" />不通过
            <a-icon type="down" />
          </a-button>
        </a-dropdown>-->
        <!-- <a-button
          v-if="!isWhite && (canDo || isAdmin) && isOrder"
          :disabled="!canDo && !isAdmin"
          style="margin-left: 8px;"
          class="review-detail-btn"
          type="primary"
          @click="reviewHeaderBtns(1)"
        >
          <a-icon type="plus-circle" />加入白名单
        </a-button>-->

        <a-popover placement="top">
          <template slot="content">
            <span>{{ this.plan }}</span>
          </template>
          <a-button
            v-if="false"
            @click="reviewHeaderBtns(7)"
            class="highlight"
            >提交评审</a-button
          >
        </a-popover>

        <!-- <a-button
          v-if="!canDo && isShow"
          type="primary"
          class="review-detail-btn"
          @click="reviewHeaderBtns(2)"
        >申请通过</a-button>-->
      </div>

      <div class="review-project-id">ID: {{ id }}</div>
      <!-- 头部信息 -->
      <reviewHeader
        @reviewHeaderBtns="reviewHeaderBtns"
        @addSqlTag="addSqlTag"
        @deleteLabel="onDeleteLabel"
        :headerInfo="headerInfo"
        :isWhite="isWhite"
        :jksuser="jksuser"
        :plan="plan"
        :isOrder="false"
        :sqlLabelStatus="sqlLabelStatus"
      ></reviewHeader>
      <div
        :class="[
          'ai-result-and-splan-info',
          sqlPlanInfo.length > 0 || commentStatus !== 0 || sqlSuggest.length > 0
            ? 'has-splan-info'
            : ''
        ]"
      >
        <!-- AI判定结果 -->
        <aiResult
          :dataInfo="dataInfo"
          :pieOption="pieOption"
          :flag="flag"
          class="ai-result"
          :sqlPlanInfo="sqlPlanInfo"
          :sqlErrorMessage="sqlErrorMessage"
        ></aiResult>
        <!-- DBA、索引、优化建议 -->
        <splanInfo
          class="splan-info"
          :sqlSuggest="sqlSuggest"
          :labelInfo="labelInfo"
          :sqlPlanInfo="sqlPlanInfo"
          :id="id"
          @saveAdvice="saveAdvice"
        ></splanInfo>
      </div>
      <!-- SQL文本和执行计划 -->
      <sqlText
        @activeChange="activeChange"
        @saveNote="onSaveNote"
        :sqlMapParamsData="sqlMapParamsData"
        :tableExistFlag="tableExistFlag"
        :detaliData="detaliData"
        :activeKey="activeKey"
        :sql_list="sql_list"
        :id="id"
        ref="sqlText"
      ></sqlText>
      <!-- 申请白名单/申请通过按钮弹窗 -->
      <whiteAction
        ref="whiteAction"
        :operator_dba="operator_dba"
        :sqlMapParamsData="sqlMapParamsData"
        @saveSqlMapParams="saveSqlMapParams"
        :title="title"
        :promptMessage="promptMessage"
        :catShow="catShow"
        :catData="catData"
        :isSqlReview="isSqlReview"
      ></whiteAction>
      <!-- 审核弹窗 -->
      <Audit ref="audit"></Audit>
      <!-- 打标弹窗 -->
      <TagModal ref="tag" @saveLabel="onSaveLabel"></TagModal>
      <!-- SQL备注弹窗 -->
      <NoteModal ref="note" @saveNote="onSaveNote"></NoteModal>
    </div>
  </a-spin>
</template>

<script>
import reviewHeader from './components/reviewHeader';
import aiResult from './components/aiResult';
import sqlModify from '@/components/Biz/ReviewDetail/sqlModify';
import splanInfo from './components/splanInfo';
// import sqlText from '@/components/Biz/ReviewDetail/sqlText';
import sqlText from './components/sqlText';
import aboutSqlTable from './components/AboutSqlTable';
import whiteAction from '@/components/Biz/ReviewDetail/whiteAction';
import TagModal from '@/components/Biz/ReviewDetail/TagModal';
import NoteModal from '@/components/Biz/ReviewDetail/NoteModal';
import Audit from '@/components/Biz/AuditModel';
import common from '@/utils/common';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import {
  getDetail,
  sqlAdviceInfo,
  saveSqlmapConfig,
  getDisOrAgree,
  reveiewPass,
  reveiewFail,
  reviewWhiteListAction,
  getEveryDetailError,
  getCatData,
  sqlRewriteJudge,
  getSaveAdvice
} from '@/api/review';
import { saveLabel, saveSqlMap, deleteLabel } from '@/api/home';
export default {
  components: {
    reviewHeader,
    aiResult,
    sqlModify,
    splanInfo,
    sqlText,
    whiteAction,
    aboutSqlTable,
    Audit,
    TagModal,
    NoteModal
  },
  mixins: [bodyMinWidth(1366)],
  data() {
    this.config = config(this);
    const jksuser = this.$store.state.account.user.name;
    return {
      jksuser,
      isShow: true,
      hasBack: true,
      canDo: false,
      isOrder: false,
      // 顶部数据
      headerInfo: {},
      // AI判定结果
      dataInfo: {
        ai_comment: [],
        rule_category: [] // 风险等级数据
      },
      sqlSuggest: [],
      sqlPlanInfo: [], // 索引建议
      labelInfo: {}, // dba标签
      // SQL文本和执行计划
      activeKey: 'sqlInfo',
      id: null,
      searchData: {}, // 上一页搜索参数
      detaliData: {}, // SQL文本和执行计划
      sql_list: {},
      operator_dba: null, // DBA负责人
      sqlMapParamsData: {}, // 申请白名单/申请通过数据
      pieOption: null, // 风险环形图数据
      review_id: null,
      loading: false,
      agreeList: [],
      disagreeList: [],
      filterList: [],
      isWhite: true,
      filterKey: '',
      filterName: '',
      reviewMessage: '',
      isInfo: false,
      review_status: '',
      sqlMapParamsShow: false,
      title: '',
      promptMessage: '',
      catData: {}, // cat数据
      catShow: false,
      isShowSubmitButton: false, // 提交评审按钮是否显示
      plan: '', // 进度
      isSqlReview: true, // 评审详情显示,
      reviewId: '',
      flag: false,
      sqlErrorMessage: [],
      tableExistFlag: false,
      commentStatus: null,
      risk: '',
      routeName: 'code-review-review',
      sqlLabelStatus: null
    };
  },
  computed: {
    isLeader() {
      const user = this.$store.state.account.user || {};
      return user.role === 'leader';
    },
    isAdmin() {
      const user = this.$store.state.account.user || {};
      return user.role === 'admin';
    }
  },
  created() {
    // document.body.style.minWidth = '1366px';
  },
  mounted() {
    this.getEveryDetailErrorFn({ detail_id: this.$route.params.id });
    this.searchData = this.$route.params.searchData;
    this.id = this.$route.params.id;
    sqlRewriteJudge({ detail_id: this.id })
      .then(res => {
        if (CommonUtil.isSuccessCode(res)) {
          const _res = _.get(res, 'data.data');
          this.flag = _res.flag;
        } else {
          this.$hideLoading({
            method: 'error',
            tips: _.get(res, 'data.message')
          });
        }
      })
      .catch(e => {
        this.$hideLoading({
          method: 'error',
          tips: _.get(e || {}, 'response.data.message') || '请求失败'
        });
      });
    getDisOrAgree()
      .then(info => {
        this.agreeList = info.pass;
        this.disagreeList = info.fail;
        this.filterList = info.filter;
      })
      .catch(e => {
        console.error(e);
      });
    this.refresh(
      {
        id: this.id
      },
      true
    );
  },
  destroyed() {
    // document.body.style.minWidth = '1024px';
  },
  methods: {
    // 获取详情数据
    refresh(params, isInfo) {
      this.loading = true;
      this.isInfo = isInfo;
      params.params = this.searchData;
      getDetail(params, isInfo)
        .then(res => {
          this.loading = false;
          this.reviewId = res.review_id;
          this.isShowSubmitButton = res.dba_status === 0;
          this.plan = `${res.left}/${res.right}已评估`;
          this.isWhite = res.is_white_list;
          this.sqlLabelStatus = res.sql_label_status
          this.headerInfo = {
            project_name: res.project_name,
            file_path: res.file_path,
            file_name: res.file_name,
            index: res.index,
            count: res.count,
            comment_status: res.comment_status,
            label_type: res.label_type,
            audit_status: res.audit_status,
            db_type: res.db_type,
            sql_frame: res.sql_frame
          };
          this.tableExistFlag = res.table_exist_flag;
          this.sqlSuggest = _.get(res, 'sql_suggest') || [];
          this.dataInfo.ai_comment = _.get(res, 'ai_comment') || [];

          this.dataInfo.ai_status = res.ai_status;
          this.dataInfo.risk = res.risk;
          this.dataInfo.rule_category = res.rule_category;
          this.$set(this.dataInfo, 'sqlScore', res.sql_score);

          this.labelInfo = {
            ...res.label_info,
            label_type: res.label_type,
            ai_status: res.ai_status,
            operator_dba: res.operator_dba,
            comment_content: res.comment_content,
            dba_comment: res.dba_comment,
            updated_at: res.updated_at,
            comment_status: res.comment_status
          };
          this.detaliData = res;
          this.commentStatus = res.comment_status;
          this.risk = res.risk;
          this.sql_list = res.sql_list || {};
          this.id = res.id;
          this.operator_dba = res.operator_dba;
          // sqlmap参数
          let sqlMapParamsData = {};
          res.sql_map_config
            ? (sqlMapParamsData = res.sql_map_config)
            : (sqlMapParamsData.sqlmap_white_list = 1);
          if (
            !sqlMapParamsData.sqlmap_monthly_increase ||
            sqlMapParamsData.sqlmap_monthly_increase.length <= 0
          ) {
            sqlMapParamsData.sqlmap_monthly_increase = res.sql_map_table || [];
          }
          this.sqlMapParamsData = { ...sqlMapParamsData, id: this.id };
          this.review_id = res.review_id;
          this.pieOption = this.config.pieOption({
            data: res.rule_category || []
          });
          this.review_status = res.review_status;
          this.getSqlAdviceInfoData(this.id);
          this.setNavi();
        })
        .catch(e => {
          this.loading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 获取索引建议数据
    getSqlAdviceInfoData(id) {
      sqlAdviceInfo({ id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            this.sqlPlanInfo = res.data.data || [];
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    activeChange(data) {
      this.activeKey = data;
    },
    // 顶部按钮点击
    async reviewHeaderBtns(number) {
      if (number === 5) {
        this.refresh({
          id: this.id,
          query: this.filterKey,
          paging: 'prev'
        });
        this.activeKey = 'sqlInfo';
      } else if (number === 6) {
        this.refresh({
          id: this.id,
          query: this.filterKey,
          paging: 'next'
        });
        this.activeKey = 'sqlInfo';
      } else if (number === 0) {
        const res = await this.getData();
        this.title = '申请白名单';
        this.$refs.whiteAction.show(res);
        this.promptMessage =
          '提示：加入白名单后，如果这条SQL没有变更，以后将自动判定为评审通过';
      } else if (number === 2) {
        const res = await this.getData();
        this.title = '申请通过';
        this.$refs.whiteAction.show(res);
        // this.promptMessage =
        // '提示：申请本次DBA评审通过，如果SQL不做变更，下一次自动审核，仍然会被判定为不通过';
      } else if (number === 1) {
        reviewWhiteListAction({
          id: this.id,
          is_white_list: number,
          dba_comment: this.reviewMessage
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.reviewHeaderBtns(6);
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else if (number === 7) {
        this.$refs.audit.show(this.reviewId);
      }
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        // 处理多出的一层级
        if (key === 'timing-review-tasklist') {
          path = sourcePath.replace(':id', this.$route.query.task_id);
          return path;
        }
        const routeArr = [
          'code-review-detail',
          'itsm-review-detail',
          'efficiency-cq-review-detail',
          'cc-review-detail',
          'timing-review-detail'
        ];
        if (routeArr.includes(key)) {
          path = sourcePath.replace(':id', this.review_id);

          // 处理query
          const { query } = this.$route;
          if (query) {
            const _qstr = Object.keys(query)
              .map(key => `${key}=${query[key]}`)
              .join('&');
            _qstr && (path += `?${_qstr}`);
          }
        }
        return path;
      });
      if (this.id !== this.$route.params.id) {
        this.$router.push({
          name: this.routeName,
          params: { id: this.id, params: this.searchData },
          query: this.$route.query
        });
      }
    },
    saveSqlMapParams(data) {
      saveSqlmapConfig({
        ...data,
        id: this.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            setTimeout(() => {
              if (this.headerInfo.index < this.headerInfo.count) {
                this.reviewHeaderBtns(6);
              } else {
                this.refresh({ id: this.id }, true);
              }
            }, 2000);
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    handleMenuClick(type, args) {
      // 过滤的时候需要调整
      if (type === 'filter') {
        let map = this.filterList.find(it => {
          return it.key === args.key;
        });

        this.filterKey = map.key;
        this.filterName = map.name;
        this.refresh({
          id: this.id,
          query: this.filterKey
        });
      } else {
        if (type === 'agree') {
          this.loading = true;
          reveiewPass({
            id: this.id,
            dba_comment: this.reviewMessage,
            review_comment: args.key
          })
            .then(e => {
              this.loading = false;
              if (e !== true) {
                this.$message.warn(e.message || '系统错误');
              } else {
                this.reviewHeaderBtns(6);
              }
            })
            .catch(e => {
              console.error(e);
              this.loading = false;
            });
        } else {
          this.loading = true;
          reveiewFail({
            id: this.id,
            dba_comment: this.reviewMessage,
            review_comment: args.key
          })
            .then(e => {
              this.loading = false;
              if (e !== true) {
                this.$message.warn(e.message || '系统错误');
              } else {
                this.reviewHeaderBtns(6);
              }
            })
            .catch(e => {
              console.error(e);
              this.loading = false;
            });
        }
      }
    },
    // 返回
    toBack() {
      // this.$router.push({
      //   // name: 'home-sqlreview-detail',
      //   name: 'code-review-detail',
      //   params: { id: this.review_id }
      // });
      const { navi = [] } = this.$route.meta || {};
      const prevKey = navi[navi.length - 2];
      const prevItem = window.routesMap[prevKey];
      let path = '';
      if (prevItem) {
        path = prevItem.path.replace(':id', this.review_id);

        // 处理query
        const { query } = this.$route;
        if (query) {
          const _qstr = Object.keys(query)
            .map(key => `${key}=${query[key]}`)
            .join('&');
          _qstr && (path += `?${_qstr}`);
        }
      }
      if (path) {
        this.$router.push({
          path
        });
        return;
      }
      this.$router.back();
    },
    // 获取cat数据
    getData() {
      const obj = { sql_id: this.id };
      return getCatData(obj)
        .then(res => {
          if (_.get(res, 'data.code') === 0) {
            const catShow = true;
            const catData = {
              max_count: res.data.data.max_count / 10 + '次/分钟',
              avg_time: res.data.data.avg_time + '毫秒',
              total_count: res.data.data.total_count + '次'
            };
            return { catShow, catData };
          } else {
            const catShow = false;
            return { catShow, catData: {} };
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
          return Promise.reject(new Error());
        });
    },
    // 获取每一条错误详情
    getEveryDetailErrorFn(params = {}) {
      getEveryDetailError(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data');
            if (resData.sql_format == 'sql' && resData.all_wrong == 1) {
              this.$set(this, 'sqlErrorMessage', resData.error_message);
            } else {
              this.$set(this, 'sqlErrorMessage', []);
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 点击表结构信息执行
    showInfoModal(name) {
      this.$refs.sqlText.showInfoModal(name);
    },
    // 点击申请表白名单执行
    showAddModal(data) {
      this.$refs.sqlText.clickApply(data);
    },
    // 查看sql改写方案
    onViewModifyPlan() {
      this.$router.push({
        // name: 'home-sqlreview-plan',
        name: 'code-review-plan',
        params: { id: this.id, params: this.searchData }
      });
    },
    getPopupContainer() {
      return document.getElementById('rootContent');
    },
    // 建议保存
    saveAdvice(data) {
      this.loading = true;
      getSaveAdvice({
        id: this.id,
        dba_comment: data
      })
        .then(e => {
          this.loading = false;
          if (e.data.code === 0) {
            this.loading = false;
            this.$message.success(e.data.message || '成功');
            this.refresh({ id: this.id });
          } else {
            this.loading = false;
            this.$message.warn(e.data.message || '系统错误');
          }
        })
        .catch(e => {
          this.loading = false;
        });
    },
    // 打标
    addSqlTag() {
      this.$refs.tag.show();
    },
    // 删除打标
    onDeleteLabel() {
      this.$showLoading();
      deleteLabel({ id: this.id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.refresh({ id: this.id });
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 打备注
    addNote() {
      this.$refs.note.show();
    },
    // 保存打标
    onSaveLabel(data) {
      this.$showLoading();
      const params = {
        operate_label_type: data.label_type,
        id: [this.id]
      };
      saveLabel(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.refresh({ id: this.id }, true);
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 保存SQL备注
    onSaveNote(data = {}) {
      this.$showLoading({ useProgress: true });
      data.id = this.id;
      saveSqlMap(data)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.refresh({ id: this.id }, true);
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  },
  watch: {
    '$store.state.account.user': {
      handler(newVal = {}) {
        let roleArr = ['dba', 'virtual_dev'];
        this.canDo = roleArr.includes(newVal.role);
        // this.canDo = newVal.role === 'dba';
      },
      immediate: true
    },
    '$route.params.id': {
      handler(newVal) {
        this.getEveryDetailErrorFn({ detail_id: newVal });
      }
    },
    '$route.name': {
      handler(newVal) {
        this.routeName = newVal;
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.review-agree {
  border-color: rgba(35, 190, 108, 1);
  background: rgba(35, 190, 108, 1);
  color: #fff;
}
.review-agree-overlay {
  .ant-dropdown-menu {
    background: rgba(35, 190, 108, 1);
    .ant-dropdown-menu-item {
      color: #fff;
      &.ant-dropdown-menu-item-active {
        background: #52c287;
      }
    }
  }
}
/deep/.review-disagree {
  border-color: rgba(255, 83, 84, 1);
  background: rgba(255, 83, 84, 1);
  color: #fff;
}
.review-disagree-overlay {
  .ant-dropdown-menu {
    background: rgba(255, 83, 84, 1);
    .ant-dropdown-menu-item {
      color: #fff;
      &.ant-dropdown-menu-item-active {
        background: #fb8283;
      }
    }
  }
}
.home-review-wraper {
  color: rgba(86, 87, 89, 1);
  .frame-button-wrapper-relative-blank {
    display: flex;
    .ant-btn {
      border-radius: 8px;
      font-size: 14px;
      color: #008adc;
      font-weight: 600;
      margin-left: 8px;
    }
    .review-detail-btn {
      color: #ffffff;
    }
  }
  .review-project-id {
    background: #ffffff;
    border: 1px solid #e4e4e7;
    border-radius: 24px;
    width: auto;
    padding: 4px 12px;
    position: absolute;
    top: -48px;
    left: 124px;
    display: block;
    font-size: 14px;
    color: #000000;
    font-weight: 400;
  }
  .ai-result-and-splan-info {
    display: flex;
    justify-content: space-between;
    &.has-splan-info {
      .ai-result {
        width: 60%;
      }
      .splan-info {
        width: 38%;
        display: block;
      }
    }
    // .ai-result {
    //   width: 64%;
    // }
    // .splan-info {
    //   width: 34%;
    // }
    .ai-result {
      width: 100%;
    }
    .splan-info {
      display: none;
    }
  }
}

/deep/ .chart-name span {
  font-size: 16px;
}

/deep/ .chart-name span:nth-child(2) {
  color: rgba(0, 0, 0, 0.85);
}

// .frame-button-wrapper {
//   right: 240px;
//   top: 24px;
// }

.review-detail-toback-btn {
  position: absolute;
  top: 24px;
  right: 250px;
}
</style>