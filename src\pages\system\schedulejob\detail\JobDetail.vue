<template>
  <div class="schedule-job-detail">
    <div class="frame-button-wrapper">
      <a-button class="highlight" @click="toBack">返回</a-button>
    </div>
    <div class="project-name">{{jobName}}</div>
    <div class="table-content">
      <Table
        ref="table"
        v-bind="tableParams"
        :dataSource="dataSource || []"
        class="new-view-table small-size"
      >
        <LimitLabel slot="job_uuid" slot-scope="{ text }" :label="text.toString()" :limit="24"></LimitLabel>
        <LimitLabel slot="msg" slot-scope="{ text }" :label="text" :limit="24"></LimitLabel>
      </Table>
      <div class="tips" v-if="dataSource && dataSource.length > 1 && type !== 'OpenAPI'">默认展示最近1000条记录</div>
    </div>
  </div>
</template>

<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import config from './config';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Table,
    LimitLabel
  },
  props: {},
  data() {
    this.config = config(this);
    const jobName = this.$route.query.job_name;
    return {
      jobName,
      value: 'JDBC',
      dataSource: [],
      tableParams: {
        url: ``,
        reqParams: {},
        columns: [],
        rowKey: 'job_uuid',
        // needTools: true,
        loaded: this.onLoaded,
        needSearchArea: true,
        searchFields: {},
        scroll: { x: 'max-content' }
      },
      type: null
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    toBack() {
      this.$router.push({ name: 'schedule-job' });
    },
    onLoaded(data) {
      this.dataSource = data;
    }
  },
  watch: {
    '$route.query.type': {
      handler(newVal) {
        this.type = newVal
        if (newVal == 'JDBC' || newVal == 'ClearData') {
          this.$set(
            this.tableParams,
            'searchFields',
            this.config.searchFields('job_jdbc_detail_choose')
          );
          this.$set(
            this.tableParams,
            'columns',
            this.config.columns(newVal).filter(item => item.visible !== false)
          );

          // this.$set(this.tableParams, 'reqParams', {
          //   uuid: this.$route.query.id
          // });
          this.$set(
            this.tableParams,
            'url',
            `/sqlreview/api/v1/schedules/${this.$route.query.id}`
          );
        } else if (newVal == 'OpenAPI') {
          this.$set(
            this.tableParams,
            'searchFields',
            this.config.searchFields('job_openApi_detail_choose')
          );
          this.$set(
            this.tableParams,
            'columns',
            this.config.columns(newVal).filter(item => item.visible !== false)
          );
          this.$set(this.tableParams, 'reqParams', {
            data_source_id: this.$route.query.id
          });
          this.$set(
            this.tableParams,
            'url',
            '/sqlreview/after_audit/job_detail_list'
          );
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.schedule-job-detail {
  /deep/.table-content {
    position: relative;
    background: #fff;
    border-radius: 16px;
    padding: 24px;
    .tips {
      position: absolute;
      left: 24px;
      bottom: 45px;
      background: transparent;
      color: #a1a1aa;
    }
  }
  .project-name {
    .frame-fixed-top-left(16px, 248px);
    background: #ffffff;
    border: 1px solid #e4e4e7;
    border-radius: 24px;
    display: block;
    padding: 4px 8px;
    font-size: 13px;
    color: #27272a;
  }
}
</style>