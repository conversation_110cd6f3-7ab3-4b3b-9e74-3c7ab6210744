import store from '@/store';
import Cookie from 'js-cookie';
const ignorePages = ['login', 'help', 'exception'];
const setLoginTarget = (value) => {
  if (location.hash === '#/login') {
    return;
  }
  let res = '/';
  // 三方登录之后手动设置login_target
  // let loginTargetManual = sessionStorage.getItem('login_target_manual');
  if (value != null) {
    res = value;
  } else if (!ignorePages.find((item) => location.hash.includes(item))) {
    // if (loginTargetManual) {
    //   res = loginTargetManual;
    //   sessionStorage.removeItem('login_target_manual');
    // } else {
    //   res = location.hash.slice(1);
    // }
    res = location.hash.slice(1);
  }
  sessionStorage.setItem('login_target', res);
};

const config = {
  setLoginTarget,
  isGotoOwnLogin: (type, res) => {
    let flag = false;
    if (type === 'success') {
      let { code, data = {} } = _.get(res, 'data') || {};
      if (!data.url && ['401', '10001'].find((item) => item == code)) {
        flag = true;
      }
    } else if (type === 'error') {
      let { status, data = {} } = _.get(res, 'response') || {};
      if (!data.url && ['401'].find((item) => item == status)) {
        flag = true;
      }
    }
    return flag;
  },
  go: (data) => {
    // 三方登录
    if (data && data.url) {
      // 清空token，设置login_target等
      setLoginTarget();
      sessionStorage.setItem(
        'lastUserName',
        _.get(store.state.account, 'user.name')
      );
      Cookie.remove(GLOBAL_CONFIG.TokenKey);
      // lufax跳转登录 == 第三方跳转登录
      Cookie.remove(GLOBAL_CONFIG.LuTokenKey);
      // 第三方跳转登录
      // Cookie.remove(GLOBAL_CONFIG.ThirdTokenKey);

      Cookie.remove('but');
      data.domain && Cookie.remove('but', { domain: data.domain });
      // 拼接redirectUrl，去除_token参数
      // let searchStr = location.search
      //   .slice(1)
      //   .split('&')
      //   .filter((item) => item && !item.startsWith('_token='))
      //   .join('&');
      // searchStr = searchStr ? `?${searchStr}` : searchStr;
      // let redirectUrl =
      //   location.origin + location.pathname + searchStr + location.hash;
      // let LuLoginUrl = data.url || GLOBAL_CONFIG.LuLoginUrl;
      // location.href = LuLoginUrl + '?redirect=' + redirectUrl;
      location.href = data.url;
      return;
    }
    // const res = location.hash.slice(1);
    // sessionStorage.setItem('login_target_manual', res);
    setLoginTarget();
    // window.location.href = '/#/login';
    // search暂时不要， 后续如果需要一定的部分search 再说
    window.location.href = location.origin + location.pathname + '#/login';
  }
};
window.Login = config;
export { setLoginTarget };
export default {};
