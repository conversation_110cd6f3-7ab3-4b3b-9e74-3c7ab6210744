import Http from '@/utils/request'

export function getResourceData(params = {}) {
  return Http({
    url: `/sqlreview/user-group/`,
    method: 'get',
    params: params
  });
}

export function resourceSave(params = {}) {
  return Http({
    url: `/sqlreview/user-group/`,
    method: 'post',
    data: params
  });
}

export function resourceUpdate(params = {}) {
  return Http({
    url: `/sqlreview/user-group/${params.id}/`,
    method: 'put',
    data: params
  });
}

export function resourceRemove(params = {}) {
  return Http({
    url: `/sqlreview/user-group/${params.id}/`,
    method: 'delete',
    data: params
  });
}

export function getResourceInfo(params = {}) {
  return Http({
    url: `/sqlreview/user_group_info/`,
    method: 'get',
    params
  });
}
