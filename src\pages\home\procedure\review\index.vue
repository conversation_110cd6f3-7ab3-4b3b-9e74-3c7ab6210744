<!--
 * @Descripttion: sql review
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2021-01-25 09:19:04
-->
<template>
  <div class="home-review-wraper">
    <div class="review-header">
      <a-button @click="onPrev">
        <a-icon type="double-left" />prev
      </a-button>
      <a-dropdown
        :disabled="!canDo"
        overlayClassName="review-agree-overlay"
        :getPopupContainer="getPopupContainer"
        class="review-agree"
      >
        <a-menu slot="overlay" @click="(event) => handleMenuClick('agree', event)">
          <a-menu-item v-for="item in agreeList" v-bind:key="item.key">{{ item.name }}</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          <a-icon type="file-done" />通过的意见
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <a-dropdown
        :disabled="!canDo"
        overlayClassName="review-disagree-overlay"
        :getPopupContainer="getPopupContainer"
        class="review-disagree"
      >
        <a-menu slot="overlay" @click="(event) => handleMenuClick('disagree', event)">
          <a-menu-item v-for="item in disagreeList" v-bind:key="item.key">{{ item.name }}</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          <a-icon type="close-circle" />不通过的意见
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <a-button
        v-if="!isWhite"
        :disabled="!canDo"
        style="margin-left: 8px"
        type="primary"
        @click="whiteListAction(1)"
      >
        <a-icon type="plus-circle" />加入白名单
      </a-button>
      <a-button
        v-else
        :disabled="!canDo"
        style="margin-left: 8px"
        type="danger"
        @click="whiteListAction(0)"
      >
        <a-icon type="minus-circle" />移除白名单
      </a-button>
      <a-button style="margin-left: 8px" @click="onNext">
        next
        <a-icon type="double-right" />
      </a-button>
      <span class="pageInfo">{{index}}/{{count}}</span>
    </div>
    <a-spin tip="加载中" :spinning="loading">
      <div
        v-if="review_status !== 0"
        :class="
          'review-notice review-notice-' +
          (review_status != -1 ? 'pass' : 'fail')
        "
      >
        <h3 v-if="review_status == 1">
          <a-icon type="check-circle" />人工通过
        </h3>
        <h3 v-else-if="review_status == -1">
          <a-icon type="close-circle" />人工不通过
        </h3>
        <h3 v-else>
          <a-icon type="check-circle" />白名单通过
        </h3>
        <ul>
          <li v-for="item in review_comment" v-bind:key="item.item_key">
            {{ item.item_value }}
            <a-icon
              v-if="canDo"
              title="点击删除该意见"
              @click="() => onRemoveReview(item.item_key)"
              type="close-circle"
            />
          </li>
        </ul>
      </div>
      <a-card type="small" :bordered="false">
        <div slot="title">
          <a-icon type="robot" />AI判定结果:
          <a-tag
            :color="
              status === 1 || status === 2
                ? '#52c41a'
                : status === -1 || status === 9
                ? '#ff4d4f'
                : '#b0aeae'
            "
          >{{ status | aiStatus }}</a-tag>
        </div>
        <pre v-if="cursor_list">未关闭游标：{{ cursor_list }}</pre>
      </a-card>
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="存储过程内容">
          <div class="tab-content">
            <div class="redo">
              <pre v-html="function_content"></pre>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="3" :tab="'DBA建议' + (dba_comment ? '' : '(尚未Review)')">
          <div class="tab-content">
            <div class="review-wraper">
              <h4>Review建议</h4>
              <a-textarea
                placeholder="请输入Review 建议"
                :rows="6"
                :value="reviewMessage"
                @change="onChangeMessage"
              />
              <div class="sava-button">
                <a-button type="primary" :disabled="!canDo" @click="saveAdvice">保存</a-button>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </div>
</template>
<script>
import {
  getDisOrAgree,
  getDetail,
  reveiewPass,
  reveiewFail,
  removeReview,
  getSaveAdvice,
  reviewWhiteListAction
} from '@/api/procedure';

import SqlHighlight from '@/components/SqlHighlight';
import common from '@/utils/common';
import CodeMirror from '@/components/CodeMirror';

export default {
  components: {
    SqlHighlight,
    CodeMirror
  },
  data() {
    return {
      id: '',
      index: 1,
      count: 1,
      canDo: false,
      filterKey: '',
      filterName: '',
      loading: false,
      agreeReveiwMessage: '',
      reviewMessage: '',
      filterList: [],
      compare_point: '',
      review_point: '',
      status: '',
      cursor_list: '',
      function_content: '',
      dba_comment: '',
      review_status: '',
      review_comment: [],
      agreeList: [],
      disagreeList: [],
      isWhite: true,
      searchData: ''
    };
  },
  mounted() {
    this.searchData = this.$route.params.searchData;
    this.initData();
    let user = this.$store.state.account.user || {};
    this.canDo = user.role === 'dba';
  },
  watch: {
    $route(to, from) {
      // console.log(to, from, 8888);
      // this.dealNavi(to);
      this.initData();
    },
    '$store.state.account.user'(newVal = {}) {
      this.canDo = newVal.role === 'dba';
    }
  },
  methods: {
    getPopupContainer() {
      return document.getElementById('rootContent');
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'home-procedure-detail') {
          path = sourcePath.replace(':id', this.review_id);
        }
        return path;
      });

      if (this.id !== this.$route.params.id) {
        this.$router.push({
          name: 'home-procedure-review',
          params: { id: this.id, params: this.searchData }
        });
      }
    },
    highlightContent(data) {
      let content = data.function_content;
      let cursorList = (data.cursor_list || '').split(',');
      [...new Set(cursorList.map(item => item.trim()))].forEach(item => {
        content = content.replace(
          new RegExp(`EXEC SQL OPEN ${item}\\s*;`, 'gim'),
          `<span style="color:red">EXEC SQL OPEN ${item};</span>`
        );
      });
      return content;
    },
    initData() {
      this.id = this.$route.params.id;
      getDisOrAgree()
        .then(info => {
          this.agreeList = info.pass;
          this.disagreeList = info.fail;
          this.filterList = info.filter;
        })
        .catch(e => {
          console.error(e);
        });
      this.refresh(
        {
          id: this.id,
          query: this.filterKey
        },
        true
      );
    },
    refresh(params, isInfo) {
      this.loading = true;
      params.params = this.searchData;
      getDetail(params, isInfo)
        .then(info => {
          this.loading = false;
          if (info.message) {
            this.$message.warn(info.message || '当前ID不存在');
          } else {
            this.id = info.id;
            this.review_point = info.review_point;
            this.status = info.status;
            this.cursor_list = info.cursor_list;
            this.function_content = this.highlightContent(info);
            this.dba_comment = info.dba_comment;
            this.review_status = info.review_status;
            this.review_comment = info.review_comment;
            this.reviewMessage = info.dba_comment;
            this.compare_point = info.compare_point;
            this.review_id = info.review_id;
            this.index = info.index;
            this.count = info.count;
            this.isWhite = info.is_white_list;
            this.setNavi();
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
        });
    },
    onChangeMessage(event) {
      this.reviewMessage = event.target.value;
    },
    onRemoveReview(key) {
      this.loading = true;
      removeReview({
        id: this.id,
        review_comment: key
      })
        .then(e => {
          this.loading = false;
          if (e !== true) {
            this.$message.warn(e.message || '系统错误');
          } else {
            this.refresh({
              id: this.id,
              query: this.filterKey
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
        });
    },
    onNext() {
      this.refresh({
        id: this.id,
        query: this.filterKey,
        paging: 'next'
      });
    },
    onPrev() {
      this.refresh({
        id: this.id,
        query: this.filterKey,
        paging: 'prev'
      });
    },
    handleMenuClick(type, args) {
      // 过滤的时候需要调整
      if (type === 'filter') {
        let map = this.filterList.find(it => {
          return it.key === args.key;
        });

        this.filterKey = map.key;
        this.filterName = map.name;
        this.refresh({
          id: this.id,
          query: this.filterKey
        });
      } else {
        if (type === 'agree') {
          this.loading = true;
          reveiewPass({
            id: this.id,
            dba_comment: this.reviewMessage,
            review_comment: args.key
          })
            .then(e => {
              this.loading = false;
              if (e !== true) {
                this.$message.warn(e.message || '系统错误');
              } else {
                this.refresh(
                  {
                    id: this.id,
                    query: this.filterKey
                  },
                  true
                );
              }
            })
            .catch(e => {
              console.error(e);
              this.loading = false;
            });
        } else {
          this.loading = true;
          reveiewFail({
            id: this.id,
            dba_comment: this.reviewMessage,
            review_comment: args.key
          })
            .then(e => {
              this.loading = false;
              if (e !== true) {
                this.$message.warn(e.message || '系统错误');
              } else {
                this.refresh(
                  {
                    id: this.id,
                    query: this.filterKey
                  },
                  true
                );
              }
            })
            .catch(e => {
              console.error(e);
              this.loading = false;
            });
        }
      }
    },
    whiteListAction(action) {
      this.$showLoading();
      reviewWhiteListAction({
        id: this.id,
        is_white_list: action,
        dba_comment: this.reviewMessage
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.refresh(
              {
                id: this.id,
                query: this.filterKey
              },
              true
            );
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    saveAdvice() {
      this.loading = true;
      getSaveAdvice({
        id: this.id,
        dba_comment: this.reviewMessage
      })
        .then(e => {
          this.loading = false;
          if (e.data.code === 0) {
            this.loading = false;
            this.$message.success(e.data.message || '成功');
          } else {
            this.loading = false;
            this.$message.warn(e.data.message || '系统错误');
          }
        })
        .catch(e => {
          this.loading = false;
        });
    }
  },
  filters: {
    aiStatus(value) {
      let obj = {
        0: '未知',
        1: '通过',
        2: '白名单通过',
        9: '错误',
        '-1': '不通过'
      };
      return obj[value];
    }
  }
};
</script>
<style lang="less">
.ant-layout-content {
  > .home-review-wraper {
    padding: 0;
    background: transparent;
  }
}
.review-agree-overlay {
  .ant-dropdown-menu {
    background: rgba(35, 190, 108, 1);
    .ant-dropdown-menu-item {
      color: #fff;
      &.ant-dropdown-menu-item-active {
        background: #52c287;
      }
    }
  }
}
.review-disagree-overlay {
  .ant-dropdown-menu {
    background: rgba(255, 83, 84, 1);
    .ant-dropdown-menu-item {
      color: #fff;
      &.ant-dropdown-menu-item-active {
        background: #fb8283;
      }
    }
  }
}
.review-filter-overlay {
  .ant-dropdown-menu {
    background: rgba(15, 120, 251, 1);
    .ant-dropdown-menu-item {
      color: #fff;
      &.ant-dropdown-menu-item-active {
        background: #569bf1;
      }
    }
  }
}
</style>
<style lang="less" scoped>
.pageInfo {
  opacity: 0.68;
  padding-left: 16px;
}
/deep/ .ant-dropdown-trigger[disabled] {
  opacity: 0.4;
}
.review-diff-wraper {
  width: 100%;
  overflow: hidden;
}
.review-notice {
  h3 {
    i {
      margin-right: 8px;
    }
  }
  &.review-notice-fail {
    li,
    h3 {
      color: rgb(255 83 84);
    }
    background: rgba(255, 83, 84, 0.08) !important;
  }
  &.review-notice-pass {
    li,
    h3 {
      color: #23be6c;
      opacity: 0.8;
    }
    background: rgba(35, 190, 108, 0.08) !important;
    li {
      &:hover {
        /deep/ i {
          display: none;
        }
      }
    }
  }
  ul {
    list-style: circle;
    padding-left: 24px;
    li {
      line-height: 25px;
      opacity: 0.8;
      &:hover {
        opacity: 1;

        /deep/ i {
          display: inline-block;
        }
      }
      /deep/ i {
        display: none;
        margin-left: 32px;
        cursor: pointer;
        &:hover {
          color: #fb8283;
        }
      }
    }
  }
}
.home-review-wraper {
  color: rgba(86, 87, 89, 1);
  /deep/ .review-agree {
    border-color: rgba(35, 190, 108, 1);
    background: rgba(35, 190, 108, 1);
    color: #fff;
  }
  /deep/ .review-disagree {
    border-color: rgba(255, 83, 84, 1);
    background: rgba(255, 83, 84, 1);
    color: #fff;
  }
  /deep/ .review-filter {
    border-color: rgba(15, 120, 251, 1);
    background: rgba(15, 120, 251, 1);
    color: #fff;
  }
  /deep/ .anticon-robot,
  .anticon-exception {
    color: #1890ff;
  }
  h4 {
    color: #4e5054;
    font-size: 14px;
    line-height: 20px;
  }
  .review-header,
  .ant-spin-container > * {
    background: #fff;
    padding: 16px 24px;
    margin-bottom: 16px;
  }
  .ant-spin-container > .ant-tabs-top {
    padding-top: 8px;
    margin-bottom: 0;
    padding-bottom: 0;
  }
  /deep/ .ant-tabs-bar {
    margin-left: -24px;
    margin-right: -24px;
    padding-left: 24px;
    padding-right: 24px;
  }
  /deep/ .ant-tag {
    margin-left: 8px;
  }
  /deep/ .ant-card-head-title {
    padding-top: 0;
    padding-bottom: 8px;
  }
  /deep/ .ant-tabs-nav .ant-tabs-tab {
    padding: 12px 0;
  }
  /deep/ .ant-card-head {
    padding: 0;
    min-height: 32px;
  }
  /deep/ .ant-card-body {
    padding: 24px 0;
    padding-bottom: 0;
  }
  /deep/ textarea {
    background-color: rgba(15, 120, 251, 0.06);
    border: 1px solid transparent;
  }

  pre {
    border-radius: 2px;
    padding: 16px;
    background-color: rgba(15, 120, 251, 0.06);
    color: #0f78fb;
    margin-bottom: 0;
    white-space: pre-wrap;
  }
  .tab-content {
    background: #f5f5f5;
    margin-left: -24px;
    margin-right: -24px;
    margin-top: -16px;
    min-height: 100px;
    padding: 24px;
    height: 400px;
    overflow: auto;
  }
  .review-wraper {
    background: #fff;
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 3px;
    .redo {
      //margin-top: 16px;
      pre {
        padding: 16px;
        background: rgba(101, 105, 109, 0.05);
        line-height: 25px;
        color: rgba(101, 105, 109, 1);
      }
    }
  }
}
.sava-button {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
