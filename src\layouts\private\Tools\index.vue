<!--
 * @Author: your name
 * @Date: 2021-01-26 16:31:20
 * @LastEditTime: 2021-02-02 10:41:54
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/layouts/Tools/index.vue
-->
<template>
  <div class="header-tools">
    <div class="download-user-manual" @click="dowmLoadUser">操作手册</div>

    <div class="version">{{ version }}版本</div>

    <a-tooltip placement="bottom">
      <template slot="title">
        <span>刷新</span>
      </template>
      <a class="icon-refresh" @click="reload">
        <a-icon type="sync" style="font-size: 16px" />
      </a>
    </a-tooltip>

    <Avatar />
  </div>
</template>

<script>
import Avatar from './components/Avatar';
// import { downLoadUserManual } from '@/api/home';
export default {
  components: { Avatar },
  props: {},
  data() {
    let version = this.$store.state.project.version || '';
    return { version };
  },
  mounted() {},
  created() {},
  methods: {
    // 刷新
    reload() {
      this.$emit('reload');
    },
    // 用户手册下载
    dowmLoadUser() {
      const url = _.get(this.$store.state.project, 'operatingWiki');
      window.open(url);
      // this.$showLoading({ tips: '正在下载' });
      // downLoadUserManual()
      //   .then(res => {
      //     CommonUtil.downLoad(this, res);
      //   })
      //   .catch(e => {
      //     console.log(e);
      //     this.$hideLoading({
      //       method: 'error',
      //       tips: _.get(e || {}, 'response.data.message') || '请求失败'
      //     });
      //   });
    }
  }
};
</script>

<style lang="less" scoped>
.header-tools {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  align-items: center;
  color: #767676;
  font-size: 12px;
  display: flex;
  justify-content: flex-end;
  .download-user-manual,
  .version {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.85);
    margin-right: 16px;
  }
  .version {
    color: rgba(255, 255, 255, 0.65);
  }
  .download-user-manual {
    cursor: pointer;
    &:hover {
      color: #fff;
    }
  }
  .icon-refresh {
    display: flex;
    align-items: center;
    margin-right: 4px;
    // color: #fff;
    color: rgba(255, 255, 255, 0.85);
    &:hover {
      color: #fff;
    }
  }
}
</style>
