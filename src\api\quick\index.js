import Http from '@/utils/request'

// 获取分页数据
export function getAppPage(params = {}) {
  return Http({
    url: `/ludms/base-info/app/page`,
    method: 'get',
    params: params
  });
}
// 获取分页数据
export function getSchemaPage(params = {}) {
  return Http({
    url: `/ludms/base-info/schema/page`,
    method: 'get',
    params: params
  });
}
// 以上是改版前的接口

// 获取历史单条数据详情
export function getHistoryDetail(params = {}) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit_hst_detail`,
    method: 'get',
    params: params
  });
}

// 获取历史单条数据索引信息
export function getHistoryColumns(params = {}) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit_idx_columns`,
    method: 'get',
    params: params
  });
}

export function getHistoryTable(params = {}) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit_table_info`,
    method: 'get',
    params: params
  });
}

// 窗口查询
export function getTabs(params = {}) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit_tabs`,
    method: 'get',
    params: params
  });
}

// 审核接口
export function quickAudit(params = {}) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit`,
    method: 'post',
    data: params
  });
}

// 审核完成后 返回值接口
export function quickAuditBackList(params = {}) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit_list`,
    method: 'get',
    params: params
  });
}

// 新增窗口
export function addTabs(params = {}) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit_tabs`,
    method: 'post',
    data: params
  });
}

// 删除窗口
export function deleteTabs(params = {}) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit_tabs`,
    method: 'delete',
    data: params
  });
}

// 导出
export function historyDownload(params = {}) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit_hst_list_download`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}