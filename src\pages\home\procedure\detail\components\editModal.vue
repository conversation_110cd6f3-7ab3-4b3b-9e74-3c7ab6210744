<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <a-modal
    v-model="visible"
    title="存储过程代码块-详细信息"
    :maskClosable="false"
    :footer="null"
    :width="800"
    @cancel="onCancel"
  >
    <!-- <sql-highlight :sql="content"></sql-highlight> -->
    <pre style="background: #e8e8e8;padding: 24px;" v-html="content"></pre>
  </a-modal>
</template>

<script>
// import common from '@/utils/common';
import SqlHighlight from '@/components/SqlHighlight';

export default {
  components: { SqlHighlight },
  props: {},
  data() {
    return {
      visible: false,
      data: {},
      content: ''
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      let content = data.function_content;
      let cursorList = (data.cursor_list || '').split(',');
      [...new Set(cursorList.map(item => item.trim()))].forEach(item => {
        content = content.replace(
          new RegExp(`EXEC SQL OPEN ${item}\\s*;`, 'gim'),
          `<span style="color:red">EXEC SQL OPEN ${item};</span>`
        );
      });

      this.content = content;
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    }
  }
};
</script>

<style lang="less" scoped>
</style>
