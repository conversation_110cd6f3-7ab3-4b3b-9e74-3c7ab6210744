import Http from '@/utils/request';

export function loginDownload(params = {}) {
  return Http({
    url: `/sqlreview/audit_log/export_login_log`,
    method: 'post',
    data: params,
    responseType: 'blob'
  });
}

export function SQLExecutionDownload(params = {}) {
  return Http({
    url: `/sqlreview/audit_log/export_sqlresolver_log`,
    method: 'post',
    data: params,
    responseType: 'blob'
  });
}

export function trackingListDownload(params = {}) {
  return Http({
    url: `/sqlreview/audit_log/export_tracking_log`,
    method: 'post',
    data: params,
    responseType: 'blob'
  });
}

export default {};