export default function (props) {
  const { getCheckboxProps } = props
  const defaultCheckboxProps = record => ({
    props: {}
  });
  return {
    computed: {
      rowSelection() {
        return {
          type: 'checkbox',
          columnWidth: '40',
          columnTitle: (
            <a-checkbox
              indeterminate={this.tableIndeterminate}
              checked={this.tableCheckAll}
              onChange={this.onCheckAllChange}
            />
          ),
          getCheckboxProps: getCheckboxProps || defaultCheckboxProps,
          selectedRowKeys: this.selectedRowKeys,
          onSelect: this.onSelect
        };
      }
    },
    data() {
      return {
        count: null, // 表格总数
        selectedRowKeys: [],
        dataSource: [],
        includes: [], // 表格单选所有值
        excludes: [], // 表格全选后排除的值
        pageKeys: [], // 表格当前页所有key
        tableCheckAll: false, // 表格全选状态true/false
        tableIndeterminate: false, // 表格半选状态true/false
        isSelectAll: false, // 是否全选
        timestamp: null // 时间戳
      }
    },
    methods: {
      onTableLoaded(req, res) {
        const resData = _.get(res, 'data.data');
        const results = resData.results;
        const ids = results.map(item => item.id);
        this.pageKeys = ids;
        this.percent = resData.label_rate;
        this.dataSource = results;
        this.count = resData.count;
        this.timestamp = resData.timestamp;
        if (this.isSelectAll) {
          ids.forEach(item => {
            if (!this.excludes.includes(item)) {
              this.selectedRowKeys.push(item);
            }
          });
        }
      },
      // 用户手动选择/取消选择某列的回调
      onSelect(record, selected) {
        if (selected) {
          this.includes.push(record.id);
          this.excludes = this.excludes.filter(item => item !== record.id);

          this.selectedRowKeys.push(record.id);

          this.tableCheckAll = this.includes.length == this.count;
          this.tableIndeterminate = this.includes.length !== this.count;
        }
        if (!selected) {
          this.excludes.push(record.id);
          this.includes = this.includes.filter(item => item !== record.id);

          this.selectedRowKeys = this.selectedRowKeys.filter(
            item => item !== record.id
          );

          this.tableCheckAll = false;
          this.tableIndeterminate = this.excludes.length !== this.count;
        }
        if (this.isSelectAll && _.isEmpty(this.excludes)) {
          this.tableIndeterminate = false;
          this.tableCheckAll = true;
        }
      },
      // 表格全选
      onCheckAllChange(e) {
        const checked = _.get(e, 'target.checked');
        this.tableCheckAll = checked;
        this.isSelectAll = checked;
        this.tableIndeterminate = false;
        if (checked) {
          const arr = [...this.selectedRowKeys, ...this.pageKeys];
          this.selectedRowKeys = [...new Set(arr)];
          this.excludes = [];
        }
        if (!checked) {
          this.selectedRowKeys = [];
        }
      },
      // 表格勾选值
      selectChange(data = {}) {
        this.selectedRowKeys = data.selectedRowKeys;
      },
      // 清空全选数据
      resetAllCheckData() {
        this.count = null;
        this.isSelectAll = false;
        this.tableCheckAll = false;
        this.tableIndeterminate = false;
        this.selectedRowKeys = [];
        this.includes = [];
        this.excludes = [];
        this.pageKeys = [];
      }
    },
    watch: {}
  }
}