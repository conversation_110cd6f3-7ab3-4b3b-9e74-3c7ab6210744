/* eslint-disable */
import e from 'vue';
import { Button as t } from 'ant-design-vue';
import 'ant-design-vue/lib/button/style/css';
e.use(t);
function n(e, t, n, o, r, s, i, d, a, c) {
  'boolean' != typeof i && ((a = d), (d = i), (i = !1));
  const u = 'function' == typeof n ? n.options : n;
  let f;
  if (
    (e &&
      e.render &&
      ((u.render = e.render),
      (u.staticRenderFns = e.staticRenderFns),
      (u._compiled = !0),
      r && (u.functional = !0)),
    o && (u._scopeId = o),
    s
      ? ((f = function(e) {
          (e =
            e ||
            (this.$vnode && this.$vnode.ssrContext) ||
            (this.parent &&
              this.parent.$vnode &&
              this.parent.$vnode.ssrContext)) ||
            'undefined' == typeof __VUE_SSR_CONTEXT__ ||
            (e = __VUE_SSR_CONTEXT__),
            t && t.call(this, a(e)),
            e && e._registeredComponents && e._registeredComponents.add(s);
        }),
        (u._ssrRegister = f))
      : t &&
        (f = i
          ? function(e) {
              t.call(this, c(e, this.$root.$options.shadowRoot));
            }
          : function(e) {
              t.call(this, d(e));
            }),
    f)
  )
    if (u.functional) {
      const e = u.render;
      u.render = function(t, n) {
        return f.call(n), e(t, n);
      };
    } else {
      const e = u.beforeCreate;
      u.beforeCreate = e ? [].concat(e, f) : [f];
    }
  return n;
}
!(function(e, t) {
  void 0 === t && (t = {});
  var n = t.insertAt;
  if (e && 'undefined' != typeof document) {console.log(111111)
    var o = document.head || document.getElementsByTagName('head')[0],
      r = document.createElement('style');
    (r.type = 'text/css'),
      'top' === n && o.firstChild
        ? o.insertBefore(r, o.firstChild)
        : o.appendChild(r),
      r.styleSheet
        ? (r.styleSheet.cssText = e)
        : r.appendChild(document.createTextNode(e));
  }
})(
  '.lucard-vue-ant-btn{-webkit-transform:scale(.9);transform:scale(.9);-webkit-transform-origin:0 0;transform-origin:0 0}.lucard-vue-ant-btn span{color:red}'
);
var o = {
    props: {},
    components: {},
    data: function() {
      return {};
    },
    computed: {},
    mounted: function() {},
    destroyed: function() {},
    methods: {}
  },
  r = function() {
    var e = this,
      t = e.$createElement;
    return (e._self._c || t)(
      'a-button',
      { staticClass: 'lucard-vue-ant-btn' },
      [e._v('测试')]
    );
  };
r._withStripped = !0;
var s = n(
  { render: r, staticRenderFns: [] },
  undefined,
  o,
  undefined,
  false,
  undefined,
  !1,
  void 0,
  void 0,
  void 0
);
export { s as default };
