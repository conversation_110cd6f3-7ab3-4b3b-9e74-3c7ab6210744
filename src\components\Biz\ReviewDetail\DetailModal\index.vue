<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="详情"
    width="648px"
    :maskClosable="false"
    :footer="null"
  >
    <a-spin :spinning="spinning">
      <Table
        ref="table"
        v-bind="tableParams || {}"
        class="new-card-table"
        :dataSource="dataSource"
      >
        <template slot="error" slot-scope="{ text }">
          <LimitLabel
            :label="text || ''"
            :block="true"
            mode="ellipsis"
          ></LimitLabel>
        </template>
      </Table>
    </a-spin>
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import config from './config';
export default {
  components: { Table, LimitLabel },
  props: {},
  data() {
    this.config = config(this);
    return {
      spinning: false,
      visible: false,
      dataSource: [],
      tableParams: {
        url: '/sqlreview/review/review_file_error',
        method: 'get',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'file_name',
        scroll: { x: 600 },
        ignoreFastReqTips: true
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(id) {
      this.visible = true;
      this.$set(this.tableParams, 'reqParams', { review_id: id });
    },
    hide() {
      this.visible = false;
    },
    onOk() {}
  }
};
</script>

<style lang="less" scoped>
</style>