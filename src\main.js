// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import './utils/config';
import router from './router/lazy'
import 'ant-design-vue/dist/antd.less'
import 'highlight.js/styles/a11y-light.css';
import Antd from 'ant-design-vue'
import axios from 'axios'
// import '@/mock'
import store from './store'
// import PouchDB from 'pouchdb'
import './style/app.less'
import './style/theme.less'
import './style/private/index.less'
import * as echarts from 'echarts'
import globalMixins from './mixins/globalMixin'
import './filter/globalFilter'
import './components/index'
import './directives/index'
import './plugins/index'
import './utils/auth';
import '../src/assets/fonts/iconfont.js';
import '../src/assets/fonts/private/iconfont.js';
import channelInfo from '../src/channel/index.js';
import './utils/pattern';
import './utils/private';
import './utils/format';
import './utils/timer';
import './utils/highlight';
// import './utils/rem';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
Vue.use(ElementUI)

Vue.prototype.$echarts = echarts
Vue.prototype.$axios = axios
Vue.prototype.$channelInfo = channelInfo;
Vue.config.productionTip = false
Vue.use(Antd)

const bus = new Vue()
Vue.prototype.$bus = bus
Vue.mixin(globalMixins);

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>',
  mounted() { }
})
