<template>
  <div
    :class="className"
    v-if="groupData.length > 0"
    :style="{
      padding: needBorder ? '16px 16px 0 116px' : 0,
      paddingLeft: (groupData || []).length > 1 ? '116px' : '16px'
    }"
  >
    <a-form :form="form">
      <template v-for="(item, index) in groupData">
        <GroupItem
          :key="item[rowKey]"
          :value="item"
          :fields="fields"
          :rowKey="rowKey"
          :index="index"
          :setInstance="setInstance"
          @remove="remove"
        ></GroupItem>
      </template>
    </a-form>
  </div>
  <custom-empty v-else></custom-empty>
</template>

<script>
import GroupItem from './components/GroupItem';
import common from '@/utils/common';
// import _ from 'lodash';

export default {
  inheritAttrs: false,
  components: { GroupItem },
  props: {
    sourceData: {
      type: Array,
      default: () => []
    },
    value: {
      type: Array,
      default: () => []
    },
    fields: {
      type: Array,
      default: () => []
    },
    rowKey: {
      type: String,
      default: 'uid'
    },
    onlyEdit: {
      type: Boolean,
      default: false
    },
    needBorder: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const { value } = this;
    // console.log(value, this.fields)
    this.instances = {};
    return {
      groupData: value,
      form: this.$form.createForm(this, { name: 'relationForm' })
    };
  },
  computed: {
    className() {
      return [
        'relation-group',
        this.needBorder && 'custom-bg',
        this.onlyEdit && 'only-edit'
      ];
    }
  },
  created() {},
  mounted() {
    // console.log(this, 'uuuuu');
  },
  destroyed() {
    this.instances = null;
  },
  methods: {
    add(type = 'single') {
      let newItem = {
        [this.rowKey]: common.uuid(),
        parent_id: null,
        group_type: type
      };
      this.groupData = [...this.groupData, newItem];
    },
    remove(record = {}) {
      const { groupData, rowKey } = this;
      this.groupData = groupData.filter(item => item[rowKey] != record[rowKey]);
    },
    setInstance({ id, instance, type = 'add' }) {
      if (id == null) return;
      if (type === 'add') {
        this.instances[id] = instance;
      } else {
        delete this.instances[id];
      }
    },
    getData() {
      // console.log(this.groupData, this.instances, 888);
      const { instances, rowKey } = this;
      const relationData = this.form.getFieldsValue();
      let res = [];
      const loop = (list, pId) => {
        list.forEach((item, index) => {
          let resItem = { ...item };
          delete resItem['children'];
          let groupItem = instances[item[rowKey]];
          if (groupItem) {
            resItem = groupItem.getData() || {};
          }

          resItem.parent_id = pId;
          resItem.order_num = index;
          if (index === 0) {
            resItem.relation = null;
          } else {
            resItem.relation = relationData[item[rowKey]];
          }

          res.push(resItem);

          if (item.children && item.children.length > 0) {
            loop(item.children, item[rowKey]);
          }
        });
      };
      // console.log(
      //   instances,
      //   Object.values(instances).map(item => item.getData()),
      //   common.toTree(
      //     Object.values(instances).map(item => item.getData()),
      //     {
      //       rootId: null,
      //       rowKey: this.rowKey,
      //       sorter: children =>
      //         children.sort((a, b) => a.order_num - b.order_num)
      //     }
      //   ),
      //   44555
      // );
      loop(
        common.toTree(
          Object.values(instances).map(item => item.getData()),
          {
            rootId: null,
            rowKey: this.rowKey,
            sorter: children =>
              children.sort((a, b) => a.order_num - b.order_num)
          }
        )
      );

      return res;
    },
    validate() {
      return Promise.all([
        new Promise((resolve, reject) => {
          this.form.validateFields((err, value) => {
            if (err) {
              reject(new Error(err));
            }
            resolve();
          });
        }),
        ...Object.values(this.instances).map(item => item && item.validate())
      ]);
    }
  },
  watch: {
    value(newVal) {
      // console.log(newVal, 555)
      if (JSON.stringify(this.groupData) !== JSON.stringify(newVal)) {
        this.groupData = newVal;
      }
    },
    sourceData: {
      handler(newVal = []) {
        // console.log(newVal, 555);
        let initValue = {};
        newVal.forEach(item => {
          initValue[item[this.rowKey]] = item.relation;
        });

        setTimeout(() => {
          !_.isEmpty(initValue) && this.form.setFieldsValue(initValue);
        }, 200);
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.relation-group {
  padding: 16px 16px 0 116px;

  &.only-edit {
    /deep/ .group-box-add {
      display: none !important;
    }
    /deep/ .rg-remove {
      display: none !important;
    }
  }
}
</style>
