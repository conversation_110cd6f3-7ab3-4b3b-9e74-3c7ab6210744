<template>
  <div class="psc-left-source-query">
    <InputSearch @search="onSearch" />
    <a-spin class="psc-lsq-wrapper" :spinning="loading">
      <template v-if="list && list.length > 0">
        <div
          :class="['psc-lsq-item', !item.schema_info && 'psc-lsq-item-error']"
          v-for="item in list"
          :key="item.sheet_id"
          @click="onClickItem(item)"
        >
          <div style="width:100%">
            <div style="word-break:break-all">{{item.sheet_name}}</div>
            <LimitLabel class="psc-lsq-item-sql-text" mode="ellipsis" :label="item.sql_text"></LimitLabel>
          </div>
          <a-popconfirm title="确认删除" @confirm="onRemove(item)">
            <custom-icon type="lu-icon-minus" @click.stop="() => {}"></custom-icon>
          </a-popconfirm>
        </div>
      </template>
      <custom-empty style="margin-top: 12px;" image="simple" v-else></custom-empty>
    </a-spin>
  </div>
</template>

<script>
// import { getHistory } from '@/api/sqlresolver';
import { getSqlSheet, delSqlSheet } from '@/api/sqlresolver';
import InputSearch from '@/components/InputSearch';
import LimitLabel from '@/components/LimitLabel';

export default {
  components: { InputSearch, LimitLabel },
  inject: ['instanceItem'],
  data() {
    return {
      loading: false,
      list: []
    };
  },
  computed: {},
  created() {
    this.refresh();
  },
  mounted() {
    this.$bus.$on('sqlresolver-save-sheet', params => {
      const { instanceId } = params;
      if (instanceId == _.get(this.instanceItem, 'key')) {
        this.refresh();
      }
    });
  },
  methods: {
    refresh(params = {}) {
      // 请求
      this.loading = true;
      getSqlSheet({
        instance_id: _.get(this.instanceItem, 'value'),
        sheet_name: this.searchVal
      })
        .then(res => {
          this.loading = false;
          if (CommonUtil.isSuccessCode(res)) {
            const data = _.get(res, 'data.data') || [];
            this.list = data;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    onSearch(val) {
      // console.log(val)
      this.searchVal = val;
      this.refresh();
    },
    onRemove(item = {}) {
      // 请求
      this.$showLoading();
      delSqlSheet({
        sheet_id: item.sheet_id
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: '删除成功' });
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    onClickItem(item) {
      if (!item.schema_info) {
        this.$message.error(`schema信息已缺失`);
        return;
      }
      this.$bus.$emit('sqlresolver-open-query', {
        data: item,
        instanceId: _.get(this.instanceItem, 'key'),
        type: 'query'
      });
    }
  }
};
</script>

<style lang="less" scoped>
.psc-left-source-query {
  height: 100%;
  display: flex;
  flex-direction: column;

  /deep/ .psc-lsq-wrapper {
    flex-grow: 1;
    position: relative;
    overflow: auto;
    .ant-spin-container {
      position: absolute;
      width: 100%;
      top: 0;
      left: 0;
    }
  }
  .psc-lsq-item {
    padding: 12px;
    border-bottom: 1px solid @border-color;
    padding-right: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    position: relative;
    font-size: 13px;

    .psc-lsq-item-sql-text {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      margin-top: 4px;
      width: 100%;
      display: inline-block;
    }

    .custom-icon {
      display: none;
      cursor: pointer;
      position: absolute;
      right: 12px;
      top: 50%;
      margin-top: -7px;
    }

    &:hover {
      background: @primary-1;
      .custom-icon {
        display: inline-block;
      }
    }

    &.psc-lsq-item-error {
      background: rgba(255, 77, 79, 0.12);
    }
  }
}
</style>
