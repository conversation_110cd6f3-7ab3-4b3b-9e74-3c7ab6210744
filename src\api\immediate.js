import Http from '@/utils/request';
// 实时审核-设置
export function setSqlreview(params = {}) {
  return Http({
    url: `/sqlreview/real_time/real_time_list`,
    method: 'post',
    data: params
  });
}

// 实时审核-chart
export function getDb2Chart(params = {}) {
  return Http({
    url: `/sqlreview/real_time/db2_chart`,
    method: 'post',
    data: params
  });
}

// 实时审核-detail
export function getCheckDetail(params = {}) {
  return Http({
    url: `/sqlreview/real_time/db2_report_detail`,
    method: 'get',
    params
  });
}

// 获取列表数据
export function getTableList(params = {}) {
  return Http({
    url: `/sqlreview/real_time/real_time_list`,
    method: 'put',
    data: params
  });
}
export default {};
