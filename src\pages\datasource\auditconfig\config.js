export default function (ctx) {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '数据源',
      dataIndex: 'name',
      key: 'name',
      width: 220,
      scopedSlots: { customRender: 'name' }
    },
    {
      title: '采集开关',
      dataIndex: 'after_audit_status',
      key: 'after_audit_status',
      scopedSlots: { customRender: 'after_audit_status' },
      width: 100
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      scopedSlots: { customRender: 'status' }
    },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      scopedSlots: { customRender: 'error_message' },
      width: 200
    },
    {
      title: '采集模式',
      dataIndex: 'collect_type',
      key: 'collect_type',
      width: 150,
      scopedSlots: { customRender: 'collect_type' }
    },
    {
      title: '采集频率',
      key: 'collect_frequency',
      dataIndex: 'collect_frequency',
      width: 180,
      scopedSlots: { customRender: 'collect_frequency' }
    },
    {
      title: '统计间隔',
      dataIndex: 'statistical_interval',
      key: 'statistical_interval',
      width: 150,
      scopedSlots: { customRender: 'statistical_interval' }
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 150,
      fixed: 'right'
    }
  ].map(item => {
    return {
      ...item,
      width: undefined
    }
  });

  const searchFields = [
    {
      type: 'Select',
      key: 'db_type',
      compIcon: 'lu-icon-database',
      props: {
        placeholder: '数据库类型',
        url: '/sqlreview/review/select_db_type',
        getPopupContainer: (el) => {
          return document.body;
        }
      }
    },
    {
      type: 'Input',
      key: 'name',
      compIcon: 'highlight',
      props: {
        placeholder: 'ID/数据源名/连接串'
      }
    },
    {
      type: 'Select',
      key: 'status',
      compIcon: 'bg-colors',
      props: {
        placeholder: '任务状态',
        url: '/sqlreview/after_audit/get_sql_collect_enum',
        reqParams: {
          enum_name: 'collect_config_status'
        },
        getPopupContainer: (el) => {
          return document.body;
        }
      }
    }
  ];

  return {
    columns,
    searchFields
  };
}
