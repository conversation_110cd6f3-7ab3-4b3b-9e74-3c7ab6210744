import SparkMD5 from 'spark-md5';
import Vue from 'vue';

class BigFileUpload {
  options = {
    file: null,
    chunkSize: 1024 * 1024,
    reqInstance: null
  };
  chunks = [];
  fileId = null;
  cancels = [];
  async run(options = {}) {
    Object.assign(this.options, options);
    const { file, chunkSize } = this.options;
    if (!file) {
      console.error('file 不存在');
      return;
    }

    // 1.分片
    const chunks = this.file2Chunks(file, chunkSize);
    this.chunks = chunks;
    console.log('chunks:', chunks);
    // 2.获取文件hash
    const fileId = await this.getFileId(chunks);
    this.fileId = fileId;
    console.log('fileId:', fileId);
    // 3.调用接口，批量上传
    this.batchRequest();
  }
  pause() {
    this.cancels.forEach(cancel => {
      cancel();
    });
    console.log('已暂停');
  }
  resume() {
    this.batchRequest();
  }
  file2Chunks(file, chunkSize) {
    const result = [];
    for (let i = 0; i < file.size; i += chunkSize) {
      result.push(file.slice(i, i + chunkSize));
    }
    return result;
  }
  getFileId(chunks) {
    return new Promise((resolve, reject) => {
      const spark = new SparkMD5.ArrayBuffer();
      const read = i => {
        if (i >= chunks.length) {
          resolve(spark.end());
          return;
        }
        const blob = chunks[i];
        const reader = new FileReader();
        reader.onload = e => {
          const byte = e.target.result;
          spark.append(byte);
          read(i + 1);
        };
        reader.readAsArrayBuffer(blob);
      };
      read(0);
    });
  }
  batchRequest() {
    const { reqInstance } = this.options;
    const { chunks, fileId } = this;
    let cache = JSON.parse(localStorage.getItem(`bigFileUpload`) || '{}');
    cache[fileId] == null && (cache[fileId] = {});
    let loadedMap = cache[fileId];
    let index = 0;
    let limit = 3;
    let cancels = this.cancels;
    let results = Object.keys(loadedMap).length;

    if (results === chunks.length) {
      console.log('该文件已经上传！');
      Vue.prototype.$message.warning('该文件已经上传！');
      return;
    }

    const request = idx => {
      if (index >= chunks.length) {
        if (results === chunks.length) {
          console.log('上传完毕');
        }
        return;
      }
      if (!_.isEmpty(loadedMap) && loadedMap[idx]) {
        // next chunk
        setTimeout(() => {
          request(index);
          index++;
        })
        return;
      }

      const chunk = chunks[idx];
      const params = new FormData();
      params.append('file', chunk);
      params.append('fileId', fileId);
      params.append('chunkIndex', idx);
      reqInstance(params, cancel => {
        cancels.push({ index: idx, cancel });
      })
        .then(res => {
          console.log('已上传part:', idx);
          // todo 处理进度
          // 缓存
          loadedMap[idx] = 1;
          localStorage.setItem(`bigFileUpload`, JSON.stringify(cache));
          results++;
          // next chunk
          request(index);
          index++;
        })
        .catch(e => {
          // 重新提交当前chunk
          request(idx);
        })
        .finally(e => {
          cancels = cancels.filter(item => item.index !== idx);
        });
    };

    for (let i = 0; i < limit; i++) {
      request(i);
      index++;
    }
  }
}
export default BigFileUpload;
