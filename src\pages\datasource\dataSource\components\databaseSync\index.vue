<template>
  <!-- 新建项目抽屉 -->
  <a-drawer
    title="数据库同步"
    :visible="visible"
    @close="hide"
    :width="'40%'"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="config-datasource-edit-drawer"
  >
    <!-- 内容区域 -->
    <a-spin :spinning="spinning">
      <Form v-bind="formParams" :formData="formData" ref="form">
        <JCronModal
          :data="formData.sync_time"
          ref="JCronModal"
          slot="sync_interval_setting"
          @cronExpression="cronExpression"
        ></JCronModal>
        <HCronModal
          :data="formData.sync_time"
          ref="HCronModal"
          slot="sync_interval_once"
          @cronExpression="cronExpression"
        ></HCronModal>
      </Form>
    </a-spin>
    <!-- 按钮区域 -->
    <div
      :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 10,
        }"
    >
      <a-button @click="hide">取消</a-button>
      <a-button @click="save" type="primary">保存</a-button>
    </div>
  </a-drawer>
</template>

<script>
import config from './config';
import Form from '@/components/Form';
import JCronModal from './JCronModal';
import HCronModal from './HCronModal';
export default {
  components: { Form, JCronModal, HCronModal },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      spinning: false,
      formData: {},
      formParams: {
        multiCols: 1,
        // fixedLabel: true,
        gutter: 100,
        // layout: 'vertical',
        clone: true,
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
        fields: this.config.fields()
      },
      id: null
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.formData.mission_type = 0;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
      this.formData = {};
    },
    getData() {
      const resData = this.$refs.form.getData();
      this.formData = { ...this.formData, ...resData };
      return resData;
    },
    // 获取cron时间设置
    cronExpression(data) {
      if (data.split(' ').length > 0) {
        if (data.split(' ').includes('undefined')) {
          this.formData = Object.assign({}, this.formData, {
            sync_time: ''
          });
          this.$refs.form.validate();
        } else {
          this.formData = Object.assign({}, this.formData, {
            sync_time: data
          });
        }
      }
      this.$nextTick(() => {
        this.$refs.form.validate();
      });
    },
    // 保存
    save() {
      const { form } = this.$refs;
      form.validate((valid, error) => {
        if (valid) {
          // const paramsData = this.$refs.form.getData();
          // this.$emit('save', {});
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ant-form-item-label {
  text-align: left;
}
</style>
