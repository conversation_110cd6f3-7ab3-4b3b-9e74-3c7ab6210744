<template>
  <div class="check-auth-plugin">
    <div class="check-area">
      <DataBaseChoose
        :value="value"
        v-bind="instanceParams"
        @change="onChange"
      />
      <a-button @click="checkAuth" class="check-auth-btn" :disabled="id == null"
        >权限检测</a-button
      >
      <div :class="['tips', `${tipsIcon[tips]}`]">
        <custom-icon :type="tipsIcon[tips]" theme="filled" />
        <span>{{ databaseTips[tips] }}</span>
      </div>
    </div>
    <div class="error-info" v-if="errorData.length > 0">
      <div class="header">
        <div>
          <custom-icon type="exclamation-circle" theme="filled" />
          <span>请参照以下语句进行授权</span>
        </div>
        <custom-icon type="copy" @click="onCopy" />
      </div>
      <div class="content" v-for="(item, index) in errorData" :key="index">
        <span>{{ item }}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    id: Number,
    tips: {
      type: Number,
      default: 1
    },
    errorData: {
      type: Array,
      default: () => []
    },
    type: String
  },
  data() {
    return {
      value: undefined,
      databaseTips: {
        0: '权限检测失败',
        1: '权限检测成功',
        2: '待检测'
      },
      tipsIcon: {
        0: 'exclamation-circle',
        1: 'check-circle',
        2: 'info-circle'
      },
      instanceParams: {
        url:
          this.type == 'dataSource'
            ? '/sqlreview/project/get_datasource_list'
            : '/sqlreview/project/get_datasource_list',
        reqParams: {},
        placeholder:
          this.type == 'dataSource' ? '请选择源数据库' : '请选择目标数据库',
        mode: 'default',
        optionLabelProp: 'children',
        allowSearch: true,
        backSearch: true,
        limit: 30,
        dropdownMatchSelectWidth: false,
        allowClear: true,
        getPopupContainer: el => {
          return document.body;
        },
        loaded(data) {
          this.dataSourceOption = data;
        },
        beforeLoaded(data) {
          return data.map(item => {
            return {
              ...item,
              instance_usage: item.env,
              showText: item.label + '(' + item.db_url + ')'
            };
          });
        }
      }
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    onCopy() {
      this.$emit('copy');
    },
    checkAuth() {
      this.$emit('checkAuth');
    },
    onChange(value) {
      this.value = value;
      this.$emit('change', value);
    },
    clear() {
      this.value = undefined;
    }
  }
};
</script>

<style lang="less" scoped>
.check-auth-plugin {
  /deep/.check-area {
    display: flex;
    .biz-data-base-choose {
      &.ant-select {
        .ant-select-selection {
          width: 600px;
          min-height: 36px;
          .ant-select-selection__rendered {
            line-height: 36px;
            .ant-select-selection-selected-value {
              height: 36px;
              .biz-instance-item .instance-item-tag {
                height: 34px;
                padding: 0px 16px !important;
                border: none !important;
                margin-left: -10px;
                .ant-tag,
                .database-image {
                  transition: none;
                }
              }
            }
            .ant-select-arrow {
              color: #27272a;
            }
          }
        }
      }
    }
    .check-auth-btn {
      width: 80px;
      height: 36px;
      color: #008adc;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #008adc;
      margin-left: 12px;
    }
    .tips {
      margin-left: 12px;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      white-space: nowrap;
      > span {
        margin-left: 4px;
      }
      &.exclamation-circle {
        font-size: 12px;
        color: #ef6173;
      }
      &.check-circle {
        font-size: 12px;
        color: #4cbb3a;
      }
      &.info-circle {
        font-size: 12px;
        color: #f29339;
      }
    }
  }
  .error-info {
    padding-bottom: 8px;
    margin-top: 24px;
    width: 600px;
    border: 1px solid #d9d9d9;
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #eff5ff;
      padding: 4px 12px;
      > div {
        font-size: 13px;
        color: #a1a1a1;
      }
    }
    .content {
      span {
        padding: 8px 28px;
        font-size: 13px;
        color: #27272a;
        font-weight: 400;
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .check-auth-plugin {
    /deep/ .check-area {
      .biz-data-base-choose {
        &.ant-select {
          .ant-select-selection {
            width: 540px !important;
          }
        }
      }
    }

    .error-info {
      width: 540px;
    }
  }
}
</style>
