<template>
  <a-tag :class="`common-tags ${mode}`" :color="color">{{ label }}</a-tag>
</template>

<script>
export default {
  // inheritAttrs: false,
  components: {},
  props: {
    type: {
      type: String,
      default: 'normal'
    },
    mode: {
      type: String,
      default: ''
    },
    text: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  },
  computed: {
    color() {
      if (this.type != 'normal' && this.text) {
        return (
          _.get(GLOBAL_CONFIG_PRIVATE, `TAG.${this.type}.${this.text}.color`) ||
          ''
        );
      }
      return '';
    },
    label() {
      if (this.type != 'normal' && this.text) {
        return (
          _.get(GLOBAL_CONFIG_PRIVATE, `TAG.${this.type}.${this.text}.label`) ||
          ''
        );
      } else {
        return this.text;
      }
    }
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.common-tags {
  border-radius: 2px;
  padding: 0 8px;
  line-height: 22px;
  margin-bottom: 0!important;
  &.radius{
    border-radius: 11px;
    border: 1px solid rgba(55,57,78,0.15);
    background: #fff;
    color: @font-color-normal;
  }
}
</style>
