<template>
  <a-modal
    v-model="visible"
    @ok="save"
    @cancel="onCancel"
    width="45%"
    :dialogStyle="{ minWidth: '400px', maxWidth: '400px' }"
    wrapClassName="order-list-get-pass-modal"
  >
    <a-spin :spinning="spinning" class="content-box">
      <div class="title">
        <a-icon type="question-circle" />
        <span>确认一键通过当前任务？</span>
      </div>
      <div>
        <div class="num-box">待处理数量：{{ dealCount }}</div>
        <div class="num-box">申请整改中：{{ rectifingCount }}</div>
        <div class="num-box">申请白名单：{{ whiteCount }}</div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import { reviewDetailAllPass } from '@/api/home';
import { getPassInfo } from '@/api/order';

export default {
  props: {
    activeKey: String
  },
  components: {},
  data() {
    return {
      visible: false,
      spinning: false,
      id: '',
      dealCount: 0,
      rectifingCount: 0,
      whiteCount: 0
    };
  },
  methods: {
    show(id) {
      this.visible = true;
      this.spinning = true;
      this.id = id;
      getPassInfo({ record_id: id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data');
            this.dealCount = resData.deal_count;
            this.rectifingCount = resData.rectifing_count;
            this.whiteCount = resData.white_count;
            this.spinning = false;
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'data.message')
          });
        });
    },
    onCancel() {
      this.visible = false;
    },
    save() {
      this.$showLoading();
      // if (this.activeKey == 'datasource') {
      //   databaseReviewPassOrReject({
      //     data_source_id: this.id,
      //     audit_status: 1 // 枚举 1通过；-1驳回
      //   })
      //     .then(res => {
      //       if (CommonUtil.isSuccessCode(res)) {
      //         this.$hideLoading({ duration: 0 });
      //         this.onCancel();
      //         this.$emit('refresh');
      //       } else {
      //         this.$hideLoading({
      //           method: 'error',
      //           tips: _.get(res, 'data.message')
      //         });
      //       }
      //     })
      //     .catch(e => {
      //       this.$hideLoading({
      //         method: 'error',
      //         tips: _.get(e || {}, 'response.data.message') || '请求失败'
      //       });
      //     });
      // } else {
      reviewDetailAllPass({
        review_id: this.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.onCancel();
            this.$emit('refresh');
          } else if (_.get(res, 'data.code') == 4000) {
            this.$hideLoading({
              useMessage: true,
              method: 'warning',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'data.message')
          });
        });
      // }
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.order-list-get-pass-modal {
  .content-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      margin-bottom: 24px;
      color: @font-color-strong;
      vertical-align: middle;
      .anticon-question-circle {
        margin-right: 8px;
        // color: rgb(246, 176, 84);
        color: rgb(244, 153, 35);
        font-size: 32px;
      }
    }
    .num-box {
      margin-bottom: 16px;
      margin-left: 44px;
    }
  }
}
</style>
