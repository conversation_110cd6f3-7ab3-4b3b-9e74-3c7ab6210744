export default function (ctx) {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sorter: true,
      width: 80
    },
    {
      title: 'SQL文本',
      key: 'sql_text',
      dataIndex: 'sql_text',
      scopedSlots: { customRender: 'sql_text' },
      width: 160
    },
    {
      title: 'Review状态',
      key: 'ai_status',
      dataIndex: 'ai_status',
      scopedSlots: { customRender: 'ai_status' },
      width: 120
    },
    {
      title: '触发规则',
      key: 'ai_message',
      dataIndex: 'ai_message',
      scopedSlots: { customRender: 'ai_message' },
      width: 180
    },
    {
      title: '风险等级',
      key: 'risk',
      dataIndex: 'risk',
      scopedSlots: { customRender: 'risk' },
      width: 100
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 80
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const searchFields = [
    {
      type: 'Select',
      label: 'Review状态',
      key: 'ai_status',
      props: {
        options: [
          { label: '未知', value: '0' },
          { label: '通过', value: '1' },
          { label: '未通过', value: '-1' },
          { label: '白名单通过', value: '2' },
          { label: '错误', value: '9' },
          { label: '待审核', value: '-2' }
        ]
      }
    },
    {
      type: 'Select',
      label: '风险等级',
      key: 'risk',
      props: {
        options: [
          {
            label: '高风险',
            value: 'high'
          },
          {
            label: '低风险',
            value: 'low'
          },
          {
            label: '无风险',
            value: 'no_risk'
          },
          {
            label: '异常',
            value: 'error'
          }
        ]
      }
    }
  ];
  return {
    columns,
    searchFields
  };
}
