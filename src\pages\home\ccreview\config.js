export default function (ctx) {
  const statusColor = {
    待评审: '#FC925E',
    评审中: '#4F96FB',
    已通过: '#6EC84A',
    未通过: '#F9676B',
    未提交: '#AFADAD'
  };
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sorter: true,
      width: 100
    },
    {
      title: 'CC单号',
      dataIndex: 'ref_id',
      key: 'ref_id',
      scopedSlots: { customRender: 'ref_id' },
      width: 150
    },
    {
      title: '审核单类型',
      dataIndex: 'form_type',
      key: 'form_type',
      scopedSlots: { customRender: 'form_type' },
      width: 150
    },
    {
      title: '子系统',
      dataIndex: 'project_name',
      key: 'project_name',
      scopedSlots: { customRender: 'project_name' },
      width: 150
    },
    // {
    //   title: '项目组',
    //   dataIndex: 'project_group',
    //   key: 'project_group',
    //   scopedSlots: { customRender: 'project_group' },
    //   width: 150
    // },
    // {
    //   title: 'Tag',
    //   dataIndex: 'review_point',
    //   key: 'review_point',
    //   scopedSlots: { customRender: 'project_name' },
    //   width: 160
    // },
    {
      title: 'AIReview',
      key: 'status',
      dataIndex: 'status',
      scopedSlots: { customRender: 'status' },
      width: 150
    },
    {
      title: 'DBA负责人',
      key: 'operator_dba',
      dataIndex: 'operator_dba',
      scopedSlots: { customRender: 'operator_dba' },
      width: 120
    },
    {
      title: 'DBA评审',
      key: 'dba_status',
      dataIndex: 'dba_status',
      scopedSlots: { customRender: 'dba_status' },
      width: 150
    },
    {
      title: 'SQL总数',
      key: 'sql_count',
      dataIndex: 'sql_count',
      sorter: true,
      width: 120
    },
    {
      title: 'AI通过率',
      key: 'passing_rate',
      sorter: true,
      dataIndex: 'passing_rate',
      width: 130
    },
    // {
    //   title: 'review方式',
    //   key: 'mode',
    //   dataIndex: 'mode',
    //   customRender: (text, record, index) => {
    //     return text == '1' ? '增量' : '全量';
    //   },
    //   width: 120
    // },
    // {
    //   title: '历史标准基线',
    //   key: 'history_baseline',
    //   dataIndex: 'history_baseline',
    //   customRender: (text) => {
    //     return text == '1' ? '是' : '否';
    //   },
    //   width: 120
    // },
    {
      title: '发起人',
      dataIndex: 'created_by',
      key: 'created_by',
      scopedSlots: { customRender: 'created_by' },
      width: 180
    },
    {
      title: '发起时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 180,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = [
    {
      type: 'Input',
      label: 'CC单号',
      key: 'ref_id',
      mainSearch: true,
      props: {
        placeholder: '搜索CC单号'
      }
    },
    {
      type: 'Input',
      label: 'ID',
      key: 'id',
      props: {
        placeholder: '搜索ID'
      }
    },
    {
      type: 'RangePicker',
      label: '发起时间',
      key: 'created_at',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    },
    // {
    //   type: 'Input',
    //   label: '发起人',
    //   key: 'created_by'
    // },
    {
      type: 'Select',
      label: '子系统',
      key: 'project_id',
      sourceKey: 'project_name',
      props: {
        url: '/sqlreview/project/list-all',
        allowSearch: true,
        backSearch: true,
        limit: 50
      }
    },
    // {
    //   type: 'Input',
    //   label: 'Tag号',
    //   key: 'review_point'
    // },
    // {
    //   type: 'Select',
    //   label: 'Review方式',
    //   key: 'mode',
    //   props: {
    //     options: [
    //       {
    //         label: '增量',
    //         value: '1'
    //       },
    //       {
    //         label: '全量',
    //         value: '0'
    //       }
    //     ]
    //   }
    // },
    {
      type: 'Select',
      label: 'AIReview状态',
      key: 'status',
      props: {
        options: [
          {
            // label: '进行中',
            label: '队列中',
            value: '0'
          },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '未通过',
            value: '-1,2'
          },
          {
            label: 'Review失败',
            value: '9'
          },
          {
            label: '拉取代码中',
            value: '3'
          },
          {
            label: '代码解析中',
            value: '4'
          },
          {
            label: '代码审核中',
            value: '5'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'DBA评审状态',
      key: 'dba_status',
      props: {
        options: [
          {
            label: '未提交',
            value: '0'
          },
          {
            label: '待评审',
            value: '1'
          },
          {
            label: '评审中',
            value: '2'
          },
          {
            label: '已通过',
            value: '3'
          },
          {
            label: '未通过',
            value: '4'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'DBA负责人',
      key: 'dba',
      sourceKey: 'operator_dba',
      props: {
        url: '/sqlreview/review/select-value/',
        reqParams: {
          which: 'dba'
        },
        allowSearch: true,
        backSearch: true,
        limit: 50
      }
    }
    // {
    //   type: 'Select',
    //   label: '历史标准基线',
    //   key: 'history_baseline',
    //   props: {
    //     options: [
    //       { label: '是', value: 1 },
    //       { label: '否', value: 0 }
    //     ]
    //   }
    // }
  ];
  return {
    columns,
    statusColor,
    searchFields: fields
  };
}
