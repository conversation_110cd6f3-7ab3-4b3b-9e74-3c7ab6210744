// import moment from 'moment';
export default function (ctx) {
  const baseInfo = [
    // 白名单类型
    // {
    //   type: 'Input',
    //   label: '白名单类型',
    //   key: 'white_list_classify',
    //   width: '100%',
    //   rules: [{ required: true, message: '该项为必填项' }],
    //   props: {
    //     disabled: true
    //   }
    // },
    // 数据源名称
    (formData = {}) => {
      return {
        type: 'DataBaseChoose',
        label: '数据源',
        key: 'data_source_id',
        props: {
          url: '/sqlreview/project/data_source_choices',
          allowSearch: true,
          backSearch: true,
          limit: 30,
          loaded(data) {
            ctx.dataSourceOption = data;
          },
          beforeLoaded(data) {
            return data.map((item) => {
              return {
                ...item,
                instance_usage: item.env,
                showText: item.label + '(' + item.db_url + ')'
              };
            });
          },
          getPopupContainer: (el) => {
            return document.body;
          },
          mode: 'default',
          optionLabelProp: 'children'
        },
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              data_source_id: value,
              schema_id: null,
              table_name: []
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      };
    },
    // schema名称
    (formData = {}) => {
      return {
        type: 'Select',
        label: 'Schema',
        key: 'schema_id',
        props: {
          url: '/sqlreview/project/get_schema_list',
          reqParams: { datasource_id: formData.data_source_id },
          allowSearch: true,
          backSearch: true,
          limit: 50
          // disabled: ctx.isDisabled
        },
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              schema_id: value,
              table_name: []
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      };
    },
    // 表名称
    (formData = {}) => {
      return {
        type: 'Select',
        label: '表名',
        key: 'table_name',
        props: {
          url: '/sqlreview/review/sql-table-info/',
          mode: 'multiple',
          reqParams: { schema_id: formData.schema_id },
          allowSearch: true,
          backSearch: true,
          limit: 30
          // disabled: ctx.isDisabled
        },
        rules: [{ required: true, message: '该项为必填项' }]
      };
    }
    // 生效时间
    // (formData = {}) => {
    //   return {
    //     type: 'DatePicker',
    //     label: '生效时间',
    //     key: 'take_effect',
    //     props: {
    //       showTime: {
    //         format: 'HH:mm:ss'
    //       }
    //     },
    //     listeners: {
    //       change: (value) => {
    //       },
    //       ok: (value) => {
    //         ctx.$refs.form.saving({
    //           take_effect: value
    //         });
    //       }
    //     },
    //     rules: [{ required: true, message: '该项为必填项' }]
    //   };
    // },
    // 失效时间
    // (formData = {}) => {
    //   return {
    //     type: 'DatePicker',
    //     label: '失效时间',
    //     key: 'expire',
    //     props: {
    //       showTime: {
    //         format: 'HH:mm:ss'
    //       }
    //     },
    //     listeners: {
    //       change: (value) => {},
    //       ok: (value) => {
    //         ctx.$refs.form.saving({
    //           expire: value
    //         });
    //       }
    //     },
    //     rules: [{ required: true, message: '该项为必填项' }]
    //   };
    // }
  ];
  return {
    baseInfo
  };
}
