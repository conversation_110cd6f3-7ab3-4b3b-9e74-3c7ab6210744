export default function (ctx) {
  const fields = (dbType) => {
    return [
      {
        type: 'Select',
        label: '',
        key: 'index_code',
        props: {
          url: ``,
          reqParams: {},
          labelInValue: true,
          labelKey: 'option',
          valueKey: 'source',
          backSearch: true
        },
        hideComponent: true,
        slots: [{ key: 'index_code' }],
        rules: [{ required: true, message: '该项为必填项' }]
      },
      {

        type: 'Select',
        label: '',
        key: 'prev_operator',
        props: {
          url: ``,
          method: 'post',
          reqParams: {},
          labelInValue: true,
          labelKey: 'option',
          valueKey: 'source',
          backSearch: true
        },
        listeners: {
          change: (value) => {
            if (value) {
              ctx.$refs.form.saving({
                prev_operator: { key: value.key, label: value.label },
                target_operator: undefined,
                target_value: undefined
              });

              const params = ctx.getParams();
              ctx.$set(ctx.formParams.fields[2].props, 'reqParams', {
                value_list: params
              });
              ctx.$set(
                ctx.formParams.fields[2].props,
                'url',
                `/sqlreview/project/rule-new/get_target_list?db_type=${dbType}`
              );
              ctx.refreshData()
            }
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]

      },
      {
        type: 'Select',
        label: '',
        key: 'target_operator',
        props: {
          url: ``,
          method: 'post',
          reqParams: {},
          labelInValue: true,
          labelKey: 'option',
          valueKey: 'source',
          backSearch: true
        },
        listeners: {
          change: (value) => {
            if (value) {
              ctx.$refs.form.saving({
                target_operator: { key: value.key, label: value.label },
                target_value: undefined
              });
              ctx.refreshData()
            }
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]

      },
      {
        type: 'Input',
        label: '',
        key: 'target_value',
        props: {
          placeholder: '请输入'
        },
        listeners: {
          change: (value) => {
            ctx.refreshData()
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      }
    ];
  };
  return { fields };
}
