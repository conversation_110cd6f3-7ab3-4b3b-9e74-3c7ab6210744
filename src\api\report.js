import Http from '@/utils/request';

// BU标识：陆金所
export function getHeaderInfo(params) {
  return Http({
    url: `/sqlreview/review/review_report/get_header_info/`,
    method: 'get',
    params
  });
}

// 项目review结果一览
export function getReviewResult(params) {
  return Http({
    url: `/sqlreview/review/review_report/get_review_result/`,
    method: 'get',
    params
  });
}

// 应用通过率排行
export function getPassRead(params) {
  return Http({
    url: `/sqlreview/review/review_report/get_pass_read/`,
    method: 'get',
    params
  });
}
// 未知/错误占比图
export function getFailRead(params) {
  return Http({
    url: `/sqlreview/review/review_report/get_fail_read/`,
    method: 'get',
    params
  });
}

// DBA review统计
export function getDbaRead(params) {
  return Http({
    url: `/sqlreview/review/review_report/get_dba_read/`,
    method: 'get',
    params
  });
}

// 应用排行
export function getProjectRead(params) {
  return Http({
    url: `/sqlreview/review/review_report/get_project_read/`,
    method: 'get',
    params
  });
}

// 事前审核下载
export function reportDetailDownload(data = {}, reportTab) {
  return Http({
    url:
      reportTab === 'beforeHand'
        ? `/sqlreview/review/report_list_review?time_range=${data.time_range}`
        : `/sqlreview/review/report_list_afterwards_review?time_range=${data.time_range}`,
    method: 'post',
    responseType: 'blob'
  });
}

export default {};
