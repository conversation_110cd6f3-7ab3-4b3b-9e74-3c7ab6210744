export default {
  menuGroup: function({ menuMap }) {
    const group = [
      {
        key: 'mine',
        title: '我的',
        children: []
      },
      {
        key: 'preaudit',
        title: '事前审核',
        children: []
      },
      {
        key: 'databaseaudit',
        title: '数据库审核',
        children: []
      },
      {
        key: 'tools',
        title: '工具',
        children: []
      }
    ];
    _.forEach(menuMap, menu => {
      const matchItem = group.find(item => item.key === menu.topGroup);
      if (matchItem) {
        matchItem.children.push(menu);
      }
    });
    return group;
  }
};
