<template>
  <div :class="['custom-coder', currTheme, fullscreen && 'fullscreen']">
    <textarea class="custom-coder-textarea"></textarea>
    <div
      :class="[
        'custom-coder-tools',
        localTools.length > 1 && 'multi-items',
        toolsFixed && 'tools-fixed'
      ]"
      v-if="localTools.length > 0"
    >
      <!-- <a-icon :type="fullscreen ? 'fullscreen-exit' : 'fullscreen'" @click="toggleFullscreen"></a-icon> -->
      <template v-for="item in localTools">
        <a-icon
          :key="item.key"
          :type="fullscreen ? 'fullscreen-exit' : 'fullscreen'"
          style=""
          @click="toggleFullscreen"
          v-if="item.key === 'fullscreen'"
        ></a-icon>
        <a-icon
          :key="item.key"
          :type="item.icon"
          @click="item.callback"
          v-tooltip="item.tooltip"
          v-else
        ></a-icon>
      </template>
      <!-- <a-button v-else @click="toggleFullscreen">关闭</a-button> -->
    </div>
    <div class="CodeMirror-errTips-panel"></div>
  </div>
</template>

<script>
// import { format as sqlFormatter } from 'sql-formatter';
import { rewriteLanguages } from '@/utils/codemirror';
import xmlFormatter from 'xml-formatter';
import jsonFormatter from 'json-beautify';
import CodeMirror from 'codemirror';
import 'codemirror/lib/codemirror.css';
// mode
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/mode/sql/sql.js';
import 'codemirror/mode/xml/xml.js';
import 'codemirror/mode/clike/clike.js';

// theme
import 'codemirror/theme/darcula.css';

// hint
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/javascript-hint';
import 'codemirror/addon/hint/sql-hint';
import 'codemirror/addon/hint/xml-hint';
import 'codemirror/addon/hint/html-hint';

// fold
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/xml-fold.js';
import 'codemirror/addon/fold/indent-fold.js';
import 'codemirror/addon/fold/brace-fold';
import 'codemirror/addon/fold/markdown-fold.js';
import 'codemirror/addon/fold/comment-fold.js';
// import 'codemirror/addon/hint/anyword-hint';

// display
import 'codemirror/addon/display/placeholder';
import 'codemirror/addon/display/autorefresh';

// lint
// import 'codemirror/addon/lint/lint.js';
// import 'codemirror/addon/lint/lint.css';
// import 'codemirror/addon/lint/javascript-lint.js';
// import 'codemirror/addon/lint/json-lint';

// mrege
// import 'codemirror/addon/merge/merge.js';
// import 'codemirror/addon/merge/merge.css';
// import DiffMatchPatch from 'diff-match-patch';
// window.diff_match_patch = DiffMatchPatch;
// window.DIFF_DELETE = -1;
// window.DIFF_INSERT = 1;
// window.DIFF_EQUAL = 0;

// console.log(sqlFormatter.format, 89898);
import mixins from './utils/index.js';
rewriteLanguages();

const ModeMap = {
  sql: 'text/x-sql',
  js: 'javascript',
  xml: 'text/html',
  json: 'javascript',
  java: 'text/x-java'
};

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    theme: {
      type: String,
      default: 'darcula' // darcula, default
    },
    type: {
      type: String,
      default: 'sql'
    },
    dbType: String,
    placeholder: String,
    options: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: String | Number,
      default: 200
    },
    value: {
      type: String,
      default: ''
    },
    needFormat: {
      type: Boolean,
      default: false
    },
    formatOptions: {
      type: Object,
      default: () => ({})
    },
    hintDefault: {
      type: Boolean,
      default: true
    },
    tools: {
      type: Array,
      default: () => [{ key: 'fullscreen' }]
    },
    toolsFixed: {
      type: Boolean,
      default: false
    },
    onLoaded: {
      type: Function
    }
  },
  mixins: [mixins],
  data() {
    this.coder = null;
    this.errLines = [];
    return {
      data: '',
      currTheme: '',
      fullscreen: false
    };
  },
  computed: {
    localTools() {
      return this.tools.map(item => {
        return {
          ...item,
          callback: event => {
            item.callback(this, this.coder, event);
          }
        };
      });
    }
  },
  mounted() {
    this.init();
    this.parentNode = this.$el.parentNode;
    this.elIndex = _.findIndex(
      this.parentNode.childNodes,
      item => item === this.$el
    );
    this.$bus.$on('keyup', e => {
      if (e && (e.keyCode == 27 || /^esc/i.test(e.key + ''))) {
        // esc
        if (this.fullscreen) {
          this.toggleFullscreen();
        }
      }
    });
    this.onLoaded && this.onLoaded(this);
  },
  beforeDestroy() {
    console.log('coder destroy');
    // if (this.coder) {
    //   this.coder.toTextArea();
    //   this.coder = null;
    // }
  },
  methods: {
    init() {
      const myTextarea = this.$el.querySelector('.custom-coder-textarea');
      const FoldCombine = CodeMirror.fold.combine;
      // const codemirror = this.$el.querySelector('.CodeMirror');
      this.defaultOptions = {
        mode: ModeMap[this.type],
        // mode: 'text/html',
        // mode: 'javascript',
        theme: this.theme, // 主题
        // keyMap: 'sublime', // 快键键风格
        lineNumbers: true, // 显示行号
        smartIndent: true, // 智能缩进
        indentUnit: 2, // 智能缩进单位为4个空格长度
        indentWithTabs: true, // 使用制表符进行智能缩进
        lineWrapping: false, // 换行
        // 在行槽中添加行号显示器、折叠器、语法检测器`
        gutters: [
          'CodeMirror-linenumbers',
          'CodeMirror-foldgutter'
          // 'CodeMirror-lint-markers'
        ],
        foldGutter:
          this.type === 'sql'
            ? {
                rangeFinder: new FoldCombine(
                  CodeMirror.fold.indent,
                  CodeMirror.fold.comment
                )
              }
            : true, // 启用行槽中的代码折叠
        autofocus: false, // 自动聚焦
        matchBrackets: true, // 匹配结束符号，比如"]、}"
        autoCloseBrackets: true, // 自动闭合符号
        styleActiveLine: true, // 显示选中行的样式
        extraKeys: {
          Tab(cm) {
            if (cm.somethingSelected()) {
              cm.indentSelection('add');
            } else {
              var spaces = Array(cm.getOption('indentUnit') + 1).join(' ');
              cm.replaceSelection(spaces);
            }
          }
        },
        hintOptions: {
          // hint: async (cm, options) => {
          //   const cur = cm.getCursor();
          //   const end = cur.ch;
          //   const start = end;

          //   // console.log(cm, cur, start, end);
          //   await new Promise((resolve, reject) => {
          //     setTimeout(() => {
          //       resolve();
          //     }, 200);
          //   });

          //   const { list: modeList } = CodeMirror.hint.sql(cm, options);
          //   let list = [];

          //   return {
          //     list: [...modeList, ...list],
          //     from: CodeMirror.Pos(cur.line, start),
          //     to: CodeMirror.Pos(cur.line, end)
          //   };
          // },
          completeSingle: false
        },
        placeholder: this.placeholder,
        autoRefresh: true // 自动触发刷新
      };
      const realOptions = Object.assign({}, this.defaultOptions, this.options);
      this.coder = CodeMirror.fromTextArea(myTextarea, realOptions);
      this.currTheme = 'theme-' + realOptions.theme;
      console.log(this.coder);
      // 绑定事件
      this.bindListeners();
      // 设置高度
      if (this.height) {
        this.coder.setSize('auto', this.height);
      }
      // 赋值
      let _val = this.value;
      if (this.needFormat) {
        _val = this.format(_val);
      }
      // setTimeout(() => {
      this.coder.setValue(_val || '');
      // }, 300);
    },
    bindListeners() {
      // this.coder.on('cursorActivity', e => {
      // });
      this.hintDefault &&
        this.coder.on('inputRead', e => {
          const { line, ch } = e.getCursor() || {};
          const lineStr = e.getLine(line) || '';
          const char = lineStr.charAt(ch - 1);
          // console.log(line, ch, lineStr, char);
          if (/[a-zA-z]/.test(char.trim())) {
            this.coder.showHint();
          }
        });

      this.coder.on('change', e => {
        // console.log('coder change');
        this.data = this.getData();
        this.$emit('change', this.data);
      });

      this.coder.on('paste', (cm, e) => {
        // console.log('coder paste', this.value, cm.getDoc().getSelection());
        // let _val = e.clipboardData.getData('Text');
        let isAllSelected = this.value === cm.getDoc().getSelection();
        // 标识正在黏贴
        this.pasting = true;
        // let _val = this.value;
        if (this.needFormat) {
          if (!this.value || isAllSelected) {
            this.$nextTick(() => {
              cm.setValue(this.format(this.getData() || ''));
            });
          }
        }
        this.$nextTick(() => {
          // 异步关闭标识
          this.pasting = false;
        });
      });

      // selectionChange
      this.coder.on('cursorActivity', cm => {
        if (this.currSelection != cm.getSelection()) {
          this.currSelection = cm.getSelection();
          this.$emit('selectionChange', cm, this.currSelection);
        }
      });

      this.coder.on('gutterClick', (cm, line, gutter, clickEvent) => {
        // console.log('gutterClick', cm, line, gutter, clickEvent);
        if (gutter && gutter === 'CodeMirror-linenumbers') {
          // 选中当前行
          const lineStr = cm.getLine(line) || '';
          cm.setSelection(
            CodeMirror.Pos(line, 0),
            CodeMirror.Pos(line, lineStr.length)
          );
        }
      });
    },
    setOptions(options = {}) {
      if (this.coder) {
        // this.coder.setOption(Object.assign({}, this.defaultOptions, options));
      }
    },
    setOption(key, value) {
      if (this.coder) {
        this.coder.setOption(key, value);
        this.currTheme = 'theme-' + this.coder.getOption('theme');
      }
    },
    toggleFullscreen() {
      this.fullscreen = !this.fullscreen;
      this.$nextTick(() => {
        if (this.fullscreen) {
          document.body.appendChild(this.$el);
        } else {
          const childNodes = this.parentNode.childNodes;
          const index = this.elIndex;
          if (index === -1 || index == childNodes.length) {
            this.parentNode.appendChild(this.$el);
          } else {
            this.parentNode.insertBefore(this.$el, childNodes[index]);
          }
        }
        this.coder.refresh();
      });
    },
    format(value = '', options = {}) {
      let res = value;
      try {
        switch (this.type) {
          case 'sql':
            let defaultOptions = {};
            if (this.dbType) {
              defaultOptions = FORMAT_CONFIG.SqlFormat.getConfig(this.dbType);
            }
            let _options = Object.assign(
              {},
              defaultOptions,
              this.formatOptions,
              options
            );
            const ignore = [
              'CREATE EVENT',
              'CREATE PROCEDURE',
              'CREATE FUNCTION',
              'CREATE TRIGGER',
              'BEGIN'
            ].find(item => value.includes(item));
            if (!ignore) {
              res = sqlFormatter.format(value, _options);
            }
            break;
          case 'xml':
            res = value ? xmlFormatter(value) : '';
            break;
          case 'json':
            let jsonObj = value;
            if (_.isString(value) && value) {
              jsonObj = JSON.parse(value);
            }
            res = value ? jsonFormatter(jsonObj, null, 2, 20) : '';
            break;
          default:
            break;
        }
      } catch (e) {
        console.log(value, e);
        this.$message.warning('格式化错误，请检查');
      }
      return res;
    },
    getData() {
      return this.coder.getValue();
    },
    signError(list = []) {
      const cm = this.coder;
      const doc = cm.getDoc();
      const errTipsPanel = this.$el.querySelector('.CodeMirror-errTips-panel');
      // 先清空
      doc.clearGutter('CodeMirror-errTips');
      this.errLines.forEach(item => {
        doc.removeLineClass(item.line, 'err-line');
      });

      list.forEach(item => {
        const line = item.line;
        if (line != null) {
          doc.addLineClass(line, '', 'err-line');
          const err = document.createElement('div');
          err.className = 'CodeMirror-errTips-item';
          err.addEventListener('mouseenter', () => {
            // console.log('mouseenter');
            err._timeout = setTimeout(() => {
              let top =
                err.getBoundingClientRect().top -
                this.$el.getBoundingClientRect().top;
              let left = this.$el
                .querySelector('.CodeMirror-gutters')
                .getBoundingClientRect().width;
              errTipsPanel.style.top = top + 24 + 'px';
              errTipsPanel.style.left = left + 'px';
              errTipsPanel.style.display = 'block';
              errTipsPanel.innerHTML = item.tips || '';
            }, 50);
          });
          err.addEventListener('mouseleave', () => {
            // console.log('mouseleave');
            if (err._timeout) {
              clearTimeout(err._timeout);
              err._timeout = null;
              errTipsPanel.style.top = 0;
              errTipsPanel.style.left = 0;
              errTipsPanel.style.display = 'none';
              errTipsPanel.innerHTML = '';
            }
          });
          doc.setGutterMarker(line, 'CodeMirror-errTips', err);
        }
      });
      this.errLines = list;
    },
    refresh() {
      this.coder.refresh();
    },
    refreshDelay(delay = 300) {
      setTimeout(() => {
        this.coder.refresh();
      }, delay);
    },
    exec(method, ...args) {
      if (this.coder && _.isFunction(this.coder[method])) {
        this.coder[method](...args);
      }
    },
    getInstance() {
      return this.coder;
    },
    onCopy() {
      CommonUtil.copy({
        value: this.value,
        callback: () => {
          this.$message.success('复制成功');
        }
      });
    }
  },
  watch: {
    value(newVal, oldVal) {
      // console.log(newVal, this.data, 222);
      if (newVal !== this.data) {
        let _val = newVal;
        if (this.needFormat) {
          _val = this.format(_val);
        }
        this.coder.setValue(_val || '');
      }
    },
    formatOptions(newVal, oldVal) {
      if (this.needFormat) {
        _val = this.format(this.value, newVal);
        this.coder.setValue(_val || '');
      }
    }
  }
};
</script>
<style lang="less" scoped>
.custom-coder {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  position: relative;
  line-height: 24px;

  /deep/ .CodeMirror {
    min-height: 56px;
    .CodeMirror-line {
      line-height: 24px;
    }
    // height: 500px;

    // errtips
    .CodeMirror-code {
      .err-line {
        background: rgba(245, 34, 45, 0.58);
      }
    }
    .CodeMirror-errTips {
      width: 8px;
    }
    .CodeMirror-errTips-item {
      width: 8px;
      height: 24px;
      top: -1px;
      bottom: 0;
      background: #1890ff;
      position: absolute;
      cursor: default;
      &:hover {
        background: #18b8ff;
      }
    }

    .CodeMirror-linenumber.CodeMirror-gutter-elt {
      cursor: alias;
    }

    // placeholder
    &.CodeMirror-empty {
      color: #bfbfbf;
    }
  }

  .custom-coder-tools {
    position: absolute;
    top: 12px;
    right: 16px;
    // padding: 4px 8px;
    background: rgba(255, 255, 255, 0.5);
    z-index: 10;
    backdrop-filter: blur(2px);
    border-radius: 4px;
    display: none;
    &.tools-fixed {
      display: inline-block;
    }
    .anticon {
      cursor: pointer;
      padding: 2px;
      font-size: 20px;
      vertical-align: middle;
      transform: scale(0.85);

      &:hover {
        color: rgba(0, 0, 0, 0.45);
      }
      // line-height: 24px;
      // &:not(:last-child) {
      //   margin-right: 2px;
      // }
    }
    .ant-btn {
      border-radius: 4px;
      border-top: 0;
      border-right: 0;
      border: 0;
      // box-shadow: 2px 2px 2px #f5f5f5;
    }
  }

  .CodeMirror-errTips-panel {
    position: absolute;
    background: #ffd88b;
    padding: 0 12px;
    top: 0;
    left: 0;
    right: 0;
    display: none;
    z-index: 10;
  }

  &:hover {
    .custom-coder-tools {
      display: inline-block;
    }
  }

  // 暗黑皮肤
  &.theme-darcula {
    border: none;
    .custom-coder-tools {
      background: rgba(113, 113, 122, 0.5);
      color: #a1a1aa;

      .anticon {
        &:hover {
          color: #d9d9d9;
        }
      }
    }

    /deep/ .CodeMirror {
      border-radius: 8px;
      &.CodeMirror-empty {
        color: #71717a;
      }

      .CodeMirror-gutters {
        background: #494949;
        border-color: #494949;
      }

      .CodeMirror-linenumber {
        color: #fff;
      }
    }
  }

  // default-full-white
  &.default-full-white {
    border: none;
    /deep/ .CodeMirror {
      font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
        'Courier New', monospace;
      font-size: 13px;
      .CodeMirror-gutters {
        background: #ffffff;
        border-color: rgba(0, 0, 0, 0);
      }
      .CodeMirror-linenumber {
        color: @font-color-normal;
      }
    }
  }

  &.fullscreen {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100% !important;
    height: 100% !important;
    z-index: 1051;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;

    /deep/ .CodeMirror {
      height: 100% !important;
      border-radius: 0;
    }
  }
}
</style>
<style lang="less">
ul.CodeMirror-hints {
  z-index: 1051;
}
ul.CodeMirror-hints.darcula {
  background-color: #ffffff !important;
}
</style>
