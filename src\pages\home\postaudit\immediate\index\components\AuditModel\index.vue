<template>
  <a-modal
    v-model="visible"
    okText="确定"
    cancelText="取消"
    @ok="save"
    @cancel="onCancel"
    title="设置"
    width="45%"
    :dialogStyle="{ minWidth: '600px', maxWidth: '800px' }"
    wrapClassName="immediate-setting-list-audit-modal"
  >
    <a-spin :spinning="spinning" class="content-box">
      <div class="title">
        <span>运行时间</span>
      </div>
      <div style="margin-bottom: 15px">
        <!-- <Form ref="form" v-bind="params" :formData="formData"> -->
        <a-radio-group v-model="radioValue" @change="onChange">
          <a-radio :style="radioStyle" :value="0">
            一直运行
            <a-popover placement="right">
              <template slot="content">
                <span>运行时将间隔10分钟进行一次数据采集</span>
              </template>
              <a-icon type="question-circle" style="margin-left: 5px" />
            </a-popover>
          </a-radio>
          <a-radio :style="radioStyle" :value="1">
            每天
            <a-time-picker
              style="margin-left: 20px"
              placeholder="开始时间"
              v-model="timeStart"
              @change="
                (val, dateStrings) => changeTime(val, dateStrings, 'timeStart')
              "
              value-format="H:m:s"
              :disabled="radioValue == 0"
            />~
            <a-time-picker
              placeholder="结束时间"
              v-model="timeEnd"
              :disabledHours="getDisabledHours"
              :disabledMinutes="getDisabledMinutes"
              :disabledSeconds="getDisabledSeconds"
              @change="
                (val, dateStrings) => changeTime(val, dateStrings, 'timeEnd')
              "
              value-format="H:m:s"
              :disabled="radioValue == 0"
            />
            <a-popover placement="right">
              <template slot="content">
                <span>运行时将间隔10分钟进行一次数据采集</span>
              </template>
              <a-icon type="question-circle" style="margin-left: 5px" />
            </a-popover>
          </a-radio>
          <!-- <a-radio :style="radioStyle" :value="3">
            每周
            <a-select
              v-model="week"
              style="width: 200px; margin-left: 20px"
              placeholder="请选择"
              @change="weekChange"
            >
              <a-select-option
                v-for="item in weekOption"
                :key="item.value"
                :value="item.value"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </a-radio>-->
        </a-radio-group>
        <!-- </Form> -->
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
// import config from './config';
import Form from '@/components/Form';
import LimitLabel from '@/components/LimitLabel';
import { setSqlreview } from '@/api/immediate';

export default {
  props: {},
  components: { Form, LimitLabel },
  data() {
    // this.config = config(this);
    return {
      id: '',
      radioValue: 0,
      visible: false,
      spinning: false,
      message: '',
      formData: {},
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
        marginBottom: '15px'
      },
      week: [],
      weekOption: [
        { label: '周一', value: '1' },
        { label: '周二', value: '2' },
        { label: '周三', value: '3' },
        { label: '周四', value: '4' },
        { label: '周五', value: '5' }
      ],
      timeStart: '',
      timeEnd: ''
    };
  },
  methods: {
    show(record) {
      this.visible = true;
      if (record) {
        this.radioValue = record.running_time;
        if (record.running_time === 0) {
          this.timeStart = '';
          this.timeEnd = '';
        } else {
          this.timeStart = record.run_start_time;
          this.timeEnd = record.run_stop_time;
        }

        this.id = record.id;
      }
    },
    onCancel() {
      this.visible = false;
    },
    onChange(e) {
      this.radioValue = e.target.value;
      this.clear();
    },
    clear() {
      this.week = [];
      this.timeStart = '';
      this.timeEnd = '';
    },
    save() {
      this.$showLoading();
      setSqlreview({
        data_source: this.id,
        running_start: this.radioValue,
        run_start_time: this.timeStart,
        run_stop_time: this.timeEnd
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.$emit('refresh');
            this.onCancel();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    weekChange(value) {
      this.radioValue = value;
    },
    changeTime(val, dateStrings, type) {
      this.radioValue = 1;
      if (type === 'timeStart') {
        this.timeStart = dateStrings;
      } else {
        this.timeEnd = dateStrings;
      }
    },
    getDisabledHours() {
      let hours = [];
      let time = this.timeStart;
      let timeArr = time.split(':');
      for (var i = 0; i < parseInt(timeArr[0]); i++) {
        hours.push(i);
      }
      return hours;
    },
    getDisabledMinutes(selectedHour) {
      let time = this.timeStart;
      let timeArr = time.split(':');
      let minutes = [];
      if (selectedHour == parseInt(timeArr[0])) {
        for (var i = 0; i < parseInt(timeArr[1]); i++) {
          minutes.push(i);
        }
      }
      return minutes;
    },
    getDisabledSeconds(selectedHour, selectedMinute) {
      let time = this.timeStart;
      let timeArr = time.split(':');
      let second = [];
      if (
        selectedHour == parseInt(timeArr[0]) &&
        selectedMinute == parseInt(timeArr[1])
      ) {
        for (var i = 0; i <= parseInt(timeArr[2]); i++) {
          second.push(i);
        }
      }
      return second;
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.immediate-setting-list-audit-modal {
  .content-box {
    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: @font-color-strong;
      vertical-align: middle;
      .anticon-question-circle {
        margin-right: 8px;
        // color: rgb(246, 176, 84);
        color: rgb(244, 153, 35);
        font-size: 32px;
      }
    }
    .subtitle {
      margin-bottom: 8px;
      margin-left: 40px;
    }
  }
}
/deep/ .ant-radio-wrapper {
  border: none !important;
  padding: 12px 40px !important;
}
</style>
