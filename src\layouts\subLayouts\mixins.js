import { refreshToken } from '@/api/common';
import { routesMap } from '@/router/lazy';
import menuConfig from '@/layouts/MenuView/config';
import Config from '@/utils/config';
import Cookie from 'js-cookie';
import _ from 'lodash';

export default {
  computed: {
    navi() {
      const navis = this.$store.state.common.navi;
      const naviStatus = CommonUtil.getQueryParams(null, 'naviStatus');
      return navis.map(item => {
        if (naviStatus != null) {
          item.naviStatus = +naviStatus;
        }
        return item;
      });
    },
    metaNavis() {
      let navis = Config.routerCacheFixed || [];
      if (Config.routerCache === true) {
        navis = [
          ...navis,
          ...(this.$route.meta.navi || [])
            .map(item => {
              const routeItem = routesMap[item] || {};
              if (_.get(routeItem, 'meta.keepAlive') !== true) return;
              const trueCompName = _.get(routeItem, 'meta.trueCompName');
              if (trueCompName) {
                return trueCompName;
              }
              return item;
            })
            .filter(item => item)
        ];
        // 排重
        navis = [...new Set(navis)];
        // 点击局部刷新
        if (!this.isRouterAlive) {
          navis = navis.slice(0, -1);
        }
      }
      return navis;
    }
  },
  created() {
    this.dealNavi(this.$route);

    // 渠道初始化操作
    const channelConfig = window.CHANNEL_INFO || {};
    if (channelConfig.init) {
      channelConfig.init({
        ctx: this,
        Cookie
      });
    }

    // 刷新用户信息
    const useRefreshToken = _.isFunction(Config.useRefreshToken)
      ? Config.useRefreshToken(this)
      : Config.useRefreshToken;
    if (useRefreshToken !== false) {
      refreshToken().then(res => {
        if (CommonUtil.isSuccessCode(res)) {
          this.$store.commit('account/setUser', _.get(res, 'data.data.user'));
          Cookie.set(Config.TokenKey, _.get(res, 'data.data.token'));
        }
      });
    }
    window.LAYOUT_ROOT = this;
  },
  mounted() {
    window.addEventListener(
      'resize',
      (this.contentResize = () => {
        if (_.isFunction(this.resize)) {
          this.resize();
        }
        this.$bus.$emit('contentResize');
      })
    );
    document.addEventListener('keyup', this.keyup = e => {
      this.$bus.$emit('keyup', e);
    })
  },
  destroyed() {
    if (this.contentResize) {
      window.removeEventListener('resize', this.contentResize);
      this.contentResize = null;
    }
    if (this.keyup) {
      document.removeEventListener('keyup', this.keyup);
      this.keyup = null;
    }
  },
  methods: {
    onCollapse(d) {
      this.collapsed = d;
      this.$bus.$emit('contentResize');
    },
    // 只针对最外层父节点
    dealNavi(route) {
      const { menuMap = {} } = _.isFunction(menuConfig) ? menuConfig(this) : {};
      function getDynamicNavi(item) {
        if (item == null) return [];
        let arr = [item.key];
        if (item.parentId != null) {
          arr = [...getDynamicNavi(menuMap[item.parentId]), ...arr];
        }
        return arr;
      }
      let navis = [];
      if (route) {
        const { path, meta = {}, name } = route;
        const { navi: localNavi, desc: localDesc, boxType, autoSetNavis, icon = '' } = meta;
        let navi = localNavi;
        let desc = localDesc;
        // 根据menuMap动态计算navi
        const menuItem = menuMap[name];
        if (menuItem && Config.useDynamicNavi) {
          navi = getDynamicNavi(menuItem);
          desc = menuItem.name;
        }

        // 页面自行设置
        if (autoSetNavis === false) {
          if (this.navi.length <= 0) {
            // 刷新进入，默认设置第一项
            this.$store.commit('common/setNavi', [
              {
                name: desc,
                icon: icon,
                path
              }
            ]);
          }
        } else {
          if (navi === true) {
            navis.push({
              name: desc,
              icon: icon,
              path
            });
          } else if (_.isArray(navi)) {
            navi.forEach(key => {
              if (key === name) {
                // 当前页
                navis.push({
                  name: desc,
                  icon: icon,
                  path
                });
              } else {
                const item = routesMap[key];
                if (!item) {
                  console.error('后端返回菜单name不存在，请检查: ', key);
                  return;
                }
                const { meta = {} } = item;
                const menuItem = menuMap[key] || {};
                const desc = Config.useDynamicNavi && menuItem.name ? menuItem.name : meta.desc;
                navis.push({
                  name: desc,
                  icon: meta.icon,
                  path: item.path
                });
              }
            });
          }
          this.$store.commit('common/setNavi', navis);
        }
        // 处理boxType
        this.contentType = boxType || '';
      }
    },
    // 返回顶部按钮的target
    getScrollTarget() {
      if (_.isFunction(this.getSelfScrollTarget)) {
        return this.getSelfScrollTarget();
      }
      return window.document.getElementById('rootContent');
    },
    // 滚动位置
    scrollTo(pos = 0) {
      const rootContent = this.getScrollTarget();
      if (rootContent) {
        rootContent.scrollTop = pos;
      }
    },
    reload(total) {
      this.$bus.$emit('page-reload');
      if (total === true) {
        this.isAlive = false;
        this.$nextTick(() => (this.isAlive = true));
      } else {
        this.isRouterAlive = false;
        this.$nextTick(() => (this.isRouterAlive = true));
      }
    }
  },
  watch: {
    $route(to, from) {
      // console.log(to, from, 8888);
      this.dealNavi(to);
      // 跳转页面内容content scrollTop 为0
      if (Config.routerTransiton === true) {
        setTimeout(() => {
          this.scrollTo(0);
        }, 500);
      } else {
        this.scrollTo(0);
      }
    }
  }
}
