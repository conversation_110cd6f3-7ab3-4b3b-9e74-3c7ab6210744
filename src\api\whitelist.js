import Http from '@/utils/request'

// 删除行
export function removeRecord(params) {
  return Http({
    url: `/sqlreview/review/white_review/delete/${params.id}/`,
    method: 'delete',
    data: {}
  });
}

// 白名单管理列表 批量移除
export function batchDelete(data) {
  return Http({
    url: '/sqlreview/review/white_review/batch_delete',
    method: 'post',
    data
  })
}

// 白名单管理详情页面
export function getDetailPageData(params) {
  return Http({
    url: `/sqlreview/review/white_review/detail/${params.id}/`,
    method: 'get',
    params: params
  })
}

// 白名单管理详情页面 字段详情
export function getColumns(params) {
  return Http({
    url: `/sqlreview/review/white_review/audit_idx_columns`,
    method: 'get',
    params
  })
}

// 添加
export function addWhiteListTable(data = {}) {
  return Http({
    url: `/sqlreview/review/save_table_label/`,
    method: 'post',
    data
  });
}
export default {};
