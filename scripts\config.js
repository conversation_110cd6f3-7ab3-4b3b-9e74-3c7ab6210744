module.exports = {
  frame: {
    copyPath: [
      { dir: 'src/assets/fonts', ignore: ['private'] },
      { dir: 'src/assets/img', ignore: ['private'] },
      { dir: 'src/components', ignore: ['Biz', 'Former', 'EditPage', 'CustomImg'] },
      { dir: 'src/directives', ignore: ['private'] },
      { dir: 'src/plugins', ignore: ['private'] },
      // { dir: 'src/layouts', ignore: ['MenuView', 'private'] },
      'src/layouts/MenuView/index.vue',
      'src/layouts/MenuView/SubMenu.vue',
      // { dir: 'src/style', ignore: ['private'] },
      { dir: 'src/pages/help', ignore: ['private'] },
      'src/utils/common.js',
      'src/utils/drag.js',
      'src/utils/pattern.js',
      'src/utils/format.js',
      'src/utils/timer.js',
      'src/utils/highlight',
      'src/utils/codemirror',
      'src/store/modules/common.js',
      'src/App.vue',
      'src/main.js',
      'build/'
    ]
  }
}