/*
 * @Author: your name
 * @Date: 2021-01-28 11:43:49
 * @LastEditTime: 2021-01-29 13:50:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/mock/home/<USER>
 */
import Mock from '@/utils/mock'
import common from '@/utils/common';

import './review';

Mock.mock(/\/api\/home\/test(\?[\s\S]*)?$/, 'get', (options) => {
  return Mock.mock({
    'dataSource|5': [{
      'key|+1': 1,
      'mockTitle|1': ['哑巴', 'Butter-fly', '肆无忌惮', '摩天大楼', '初学者'],
      'mockContent|1': ['你翻译不了我的声响', '数码宝贝主题曲', '摩天大楼太稀有', '像海浪撞破了山丘'],
      'mockAction|1': ['下载', '试听', '喜欢']
    }]
  })
})

Mock.mock(/\/api\/home\/<USER>\/table(\?[\s\S]*)?$/, 'get', (options) => {
  const query = common.getQueryParams(options.url);
  const { page = 1, pageSize = 10 } = query;
  const data = Array(18)
    .fill()
    .map((item, i) => ({
      key: i,
      name: 'John Brown' + i,
      age: 32,
      address: i,
      addressName: 'New York No. 1 Lake Park' + i,
      tags: ['nice', 'developer']
    }));
  return {
    code: '0',
    message: 'ok',
    data: {
      results: _.merge([], data).slice(
        (page - 1) * pageSize,
        (page - 1) * pageSize + pageSize
      ),
      page,
      pageSize,
      count: data.length
    }
  };
})

Mock.mock(/\/api\/home\/<USER>\/select(\?[\s\S]*)?$/, 'get', (options) => {
  // const query = common.getQueryParams(options.url);
  // const {} = query;
  const data = Array(10)
    .fill()
    .map((item, i) => ({
      label: 'New York No. 1 Lake Park' + i,
      value: i
    }));
  return {
    code: '0',
    message: 'ok',
    data: data
  };
})

Mock.mock(/\/api\/home\/table(\?[\s\S]*)?$/, 'get', (options) => {
  const query = common.getQueryParams(options.url);
  const { page = 1, pageSize = 10 } = query;
  const data = Array(18)
    .fill()
    .map((item, i) => ({
      key: i,
      time: 'John Brown' + i,
      creater: 32,
      x: i,
      y: 'New York No. 1 Lake Park' + i,
      z: 3333,
      status: '1'
    }));
  return {
    code: '0',
    message: 'ok',
    data: {
      results: _.merge([], data).slice(
        (page - 1) * pageSize,
        (page - 1) * pageSize + pageSize
      ),
      page,
      pageSize,
      count: data.length
    }
  };
})

Mock.mock(/\/api\/home\/<USER>\/cascader(\?[\s\S]*)?$/, 'get', (options) => {
  // const query = common.getQueryParams(options.url);
  // const {} = query;
  const data = Array(10)
    .fill()
    .map((item, i) => ({
      label: 'New York No. 1 Lake Park' + i,
      value: i,
      isLeaf: false
    }));
  return {
    code: '0',
    message: 'ok',
    data: data
  };
})