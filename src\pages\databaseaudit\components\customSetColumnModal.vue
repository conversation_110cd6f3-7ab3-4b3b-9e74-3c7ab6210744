<template>
  <div>
    <a-modal
      title="表头设置"
      width="640px"
      :visible="visible"
      @ok="onSave"
      @cancel="onCancel"
      wrapClassName="database-audit-custom-set-column-modal"
    >
      <a-spin :spinning="spinning">
        <a-transfer
          class="tree-transfer"
          :titles="['全部', '已选']"
          :dataSource="tableData"
          :targetKeys="targetKeys"
          :filterOption="
            (inputValue, item) => item.title.indexOf(inputValue) !== -1
          "
          :showSelectAll="true"
          @change="onChange"
        >
          <template
            slot="children"
            slot-scope="{
              props: {
                direction,
                filteredItems,
                selectedKeys,
                disabled: listDisabled
              },
              on: { itemSelectAll, itemSelect }
            }"
          >
            <TableEdit
              ref="tableEdit"
              size="small"
              v-bind="tableParams || {}"
              :dataSource="filteredItems || []"
              :columns="direction === 'left' ? leftColumns : rightColumns"
              :rowSelection="
                getRowSelection({
                  disabled: listDisabled,
                  selectedKeys,
                  itemSelectAll,
                  itemSelect
                })
              "
            ></TableEdit>
          </template>
        </a-transfer>
      </a-spin>
      <div slot="footer">
        <a-button @click="onCancel" class="">取消</a-button>
        <a-button @click="onSave" type="primary">保存</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import TableEdit from '@/components/TableEdit';
import difference from 'lodash/difference';
import { getTableHeaderInfo } from '@/api/databaseaudit/topsql';

export default {
  name: '',
  components: { TableEdit },
  props: { id: String | Number },
  data() {
    const leftColumns = [
      {
        title: '全部',
        dataIndex: 'title',
        key: 'title'
      }
    ];
    const rightColumns = [
      {
        title: '已选',
        dataIndex: 'id',
        key: 'id',
        scopedSlots: { customRender: 'draggable' }
      },
      {
        title: ' ',
        dataIndex: 'title',
        key: 'title'
      }
    ];
    return {
      leftColumns,
      rightColumns,
      tableParams: {
        rowKey: 'key',
        showHeader: false,
        draggable: true,
        pagination: false
      },
      tableData: [],
      targetKeys: [],
      visible: false,
      spinning: false
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    show(type, data) {
      this.visible = true;
      if (type == 'oracle' || type == 'DB2' || type == 'GAUSSDB') {
        const localColumns =
          type == 'oracle'
            ? JSON.parse(window.localStorage.getItem('oracleRealTimeColumns'))
            : type == 'DB2'
            ? JSON.parse(window.localStorage.getItem('db2RealTimeColumns'))
            : JSON.parse(window.localStorage.getItem('gaussdbRealTimeColumns'));
        this.targetKeys = [];
        if (!_.isEmpty(localColumns)) {
          this.targetKeys = localColumns.map(item => item.key);
        }
        this.tableData = data.map(item => {
          return { ...item, title: item.value };
        });
        this.$set(this.tableParams, 'draggable', false);
        const rightColumns = [
          {
            title: ' ',
            dataIndex: 'title',
            key: 'title'
          }
        ];
        this.$set(this, 'rightColumns', rightColumns);
      } else {
        this.spinning = true;
        getTableHeaderInfo({
          data_source_id: this.id,
          source_type: type
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.spinning = false;
              const resData = _.get(res, 'data.data') || {};
              this.tableData = resData.all_list.map(item => {
                return { ...item, title: item.value };
              });

              this.targetKeys = resData.display_list.map(item => item.key);
            } else {
              this.spinning = false;

              this.$message.error(_.get(res, 'data.message'));
            }
          })
          .catch(e => {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          });
      }
    },
    getRowSelection({ disabled, selectedKeys, itemSelectAll, itemSelect }) {
      return {
        getCheckboxProps: item => ({
          props: { disabled: disabled || item.disabled }
        }),
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows
            .filter(item => !item.disabled)
            .map(({ key }) => key);
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys);
          itemSelectAll(diffKeys, selected);
        },
        onSelect({ key }, selected) {
          itemSelect(key, selected);
        },
        selectedRowKeys: selectedKeys
      };
    },
    // 选择在两栏之间转移时的回调函数
    onChange(targetKeys, direction, moveKeys) {
      if (direction == 'right') {
        this.targetKeys = [...this.targetKeys, ...moveKeys];
      } else {
        this.targetKeys = targetKeys;
      }
    },
    onSave() {
      const { tableEdit } = this.$refs;
      const list = _.get(tableEdit, 'data');
      const data = list.map(item => item.key);
      if (_.isEmpty(data)) {
        this.$message.warn('请选择数据!');
        return;
      }
      this.targetKeys = data;

      this.$emit('save', data, list);
      this.hide();
    },
    onCancel() {
      this.hide();
    },
    hide() {
      this.visible = false;
    }
  }
};
</script>

<style lang="less">
.database-audit-custom-set-column-modal {
  .ant-modal-content {
    .ant-modal-close {
      .ant-modal-close-x {
        color: #000;
      }
    }
    .ant-modal-header {
      background: #fff;
      border-bottom: 1px solid #e8e8e8;
      .ant-modal-title {
        color: #000;
      }
    }
    .ant-transfer-list {
      height: 400px;
      width: 46%;
      .ant-transfer-list-header {
        padding: 8px 34px;
      }
      .ant-transfer-list-body {
        .ant-transfer-list-body-customize-wrapper {
          .table-edit {
            height: 324px;
            overflow-y: auto;
            .ant-table-body {
              .ant-table-tbody {
                > tr {
                  background: #fff;
                  > td {
                    border: none;
                  }
                }
              }
            }
          }
        }
      }
    }
    .ant-modal-footer {
      > div {
        .ant-btn {
          // height: 32px;
          // font-size: 12px;
          margin-right: 0;
          margin-left: 8px;
          // border-radius: 6px;
          &.highlight {
            // color: #008adc;
            // border-color: #7fc4ed;
            // &:hover {
            //   color: @primary-5;
            //   border-color: #008adc;
            // }
          }

          &.ant-btn-primary {
            // background: #008adc;
            // color: #ffffff;
            // &:hover {
            //   background: #219be3;
            //   color: #ffffff;
            // }
          }
        }
      }
    }
  }
}
</style>