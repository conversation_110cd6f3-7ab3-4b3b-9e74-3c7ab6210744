<template>
  <a-tag
    :color="info.bgColor"
    :style="{ color: info.textColor, border: `1px solid ${info.borderColor}` }"
    :class="['biz-status-tag', shape]"
  >
    <span>{{ info.textVal }}</span>
    <slot></slot>
  </a-tag>
</template>

<script>
export default {
  components: {},
  props: {
    type: {
      type: String,
      default: 'review' // review, quick, sqlreview, label
    },
    status: {
      type: [Number, String]
    },
    fromPath: String,
    shape: {
      type: String,
      default: 'square' // border-radius是圆形还是方形 circle/square
    }
  },
  data() {
    return {
      statusColor: {
        待评审: 'rgba(242,147,57,0.17)',
        待审核: 'rgba(242,147,57,0.17)',
        评审中: '#E6F7FF',
        已通过: '#F6FFED',
        未通过: 'rgba(255,163,158,1)',
        未提交: 'rgba(228,228,231,0.50)'
      },
      textColor: {
        待评审: '#991B1B',
        待审核: '#991B1B',
        评审中: '#1890FF',
        已通过: '#52C41A',
        未通过: '#FFF1F0',
        未提交: '#27272A'
      },
      borderColor: {
        待评审: 'rgba(255,213,145)',
        待审核: 'rgba(255,213,145)',
        评审中: 'rgba(145,213,255,1)',
        已通过: 'rgba(183,235,143,1)',
        未通过: 'rgba(255,173,210)',
        未提交: 'rgba(217,217,217)'
      },
      reviewStatusColor: {
        '0': {
          // text: '进行中',
          text: this.fromPath == 'sqlreview' ? '队列中' : '待审核',
          color: 'rgba(91,147,255,0.15)',
          borderColor: 'rgba(173,198,255,1)'
        },
        '1': {
          text: '通过',
          color: '#F6FFED',
          borderColor: 'rgba(183,235,143,1)'
        },
        '-1': {
          text: '未通过',
          color: '#FFF1F0',
          borderColor: 'rgba(255,163,158,1)'
        },
        '9': {
          text: 'Review失败',
          color: '#FFF1F0',
          borderColor: 'rgba(255,163,158,1)'
        },
        '2': {
          text: '未通过',
          color: '#FFF1F0',
          borderColor: 'rgba(255,163,158,1)'
        },
        '3': {
          text: '拉取代码中',
          color: '#E6F7FF',
          borderColor: 'rgba(145,213,255,1)'
        },
        '4': {
          text: '代码解析中',
          color: '#E6F7FF',
          borderColor: 'rgba(145,213,255,1)'
        },
        '5': {
          text: '代码审核中',
          color: '#E6F7FF',
          borderColor: 'rgba(145,213,255,1)'
        }
      },
      reviewTextColor: {
        '-1': '#F5222D', // 未通过
        '0': '#214AC0', // 进行中
        '1': '#52C41A', // 通过
        '2': '#F5222D', // 未通过
        '3': '#1890FF', // 拉取代码中
        '4': '#1890FF', // 代码解析中
        '5': '#1890FF', // 代码审核中
        '9': '#F5222D' //  Review失败
      },
      quickStatusColor: {
        '-1': {
          text: '未通过',
          color: 'rgba(255,163,158,1)'
        },
        '-2': {
          text: '待审核',
          color: 'rgba(242,147,57,0.17)'
        },
        '0': {
          text: '未知',
          color: 'rgba(228,228,231,0.50)'
        },
        '1': {
          text: '通过',
          color: '#F6FFED'
        },
        '2': {
          text: '白名单通过',
          color: '#F6FFED'
        },
        '9': {
          text: '错误',
          color: 'rgba(255,163,158,1)'
        }
      },
      quickTextColor: {
        '-1': '#FFF1F0', // 未通过
        '-2': '#991B1B', // 待审核
        '0': '#27272A', // 未知
        '1': '#166534', // 通过
        '2': '#166534', // 白名单通过
        '9': '#FFF1F0' //  错误
      },
      labelStatusColor: {
        '-1': {
          text: '审核未通过',
          color: '#FFF1F0',
          borderColor: 'rgba(255,163,158,1)'
        },
        '0': {
          text: '待审核',
          color: 'rgba(91,147,255,0.15)',
          borderColor: 'rgba(173,198,255,1)'
        },
        '1': {
          text: '审核通过',
          color: '#F6FFED',
          borderColor: 'rgba(183,235,143,1)'
        },
        '2': {
          text: '已删除',
          color: '#FFF1F0',
          borderColor: 'rgba(255,163,158,1)'
        },
        '3': {
          text: '已失效',
          color: '#E6F7FF',
          borderColor: 'rgba(145,213,255,1)'
        }
      }
    };
  },
  computed: {
    info() {
      const { status } = this;
      if (status == null) return {};
      if (this.type === 'review') {
        let _status = status == '队列中' ? '待审核' : status;
        if (!/-?[0-9]+/.test(status)) {
          _.forEach(this.reviewStatusColor, (item, key) => {
            if (item.text === _status) {
              _status = key;
              return false;
            }
          });
        }
        return {
          bgColor: this.reviewStatusColor[_status].color,
          textColor: this.reviewTextColor[_status],
          borderColor: this.reviewStatusColor[_status].borderColor,
          textVal: this.reviewStatusColor[_status].text
        };
      }
      if (this.type === 'quick') {
        let _status = status;
        if (!/-?[0-9]+/.test(status)) {
          _.forEach(this.quickStatusColor, (item, key) => {
            if (item.text === status) {
              _status = key;
              return false;
            }
          });
        }
        return {
          bgColor: this.quickStatusColor[_status].color,
          textColor: this.quickTextColor[_status],
          textVal: this.quickStatusColor[_status].text
        };
      }
      if (this.type == 'label') {
        let _status = status;
        if (!/-?[0-9]+/.test(status)) {
          _.forEach(this.reviewStatusColor, (item, key) => {
            if (item.text === _status) {
              _status = key;
              return false;
            }
          });
        }
        return {
          bgColor: this.labelStatusColor[_status].color,
          textColor: this.reviewTextColor[_status],
          borderColor: this.labelStatusColor[_status].borderColor,
          textVal: this.labelStatusColor[_status].text
        };
      }
      return {
        borderColor: this.borderColor[status],
        bgColor: this.statusColor[status],
        textColor: this.textColor[status],
        textVal: status
      };
    }
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.biz-status-tag {
  font-size: 14px;
  line-height: 22px;
  margin-right: 0;
  padding: 0 8px;
  white-space: nowrap !important;
  &.circle {
    border-radius: 12px;
  }
  &.square {
    border-radius: 4px;
  }
}
</style>
