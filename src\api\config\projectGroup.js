import Http from '@/utils/request'

// 新增项目组
export function addProjectGroup (params = {}) {
  return Http({
    url: `/sqlreview/project_config/add_project_group`,
    method: 'post',
    data: params
  });
}
// 编辑项目组
export function saveProjectGroup (params = {}) {
  return Http({
    url: `/sqlreview/project_config/save_project_group`,
    method: 'post',
    data: params
  });
}
// 删除项目组
export function getDleProject (params = {}) {
  return Http({
    url: `/sqlreview/project_config/delete_project_group`,
    method: 'post',
    data: params
  });
}

export function downloadProjectGroupTemplate(params = {}) {
  return Http({
    url: `/sqlreview/project/download_project_group_template`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}
export function uploadProjectGroup(params = {}) {
  return Http({
    url: `/sqlreview/project/upload_project_group `,
    method: 'post',
    data: params
  });
}
export default {};