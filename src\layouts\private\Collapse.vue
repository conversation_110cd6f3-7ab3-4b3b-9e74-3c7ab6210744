<template>
  <div :class="['custom-collapse', collapsed && 'collapsed']">
    <div class="sys-action" v-if="menus && menus.length > 0">
      <div :class="[isActive && 'active', 'config-area']">
        <a-popover
          v-model="visible"
          placement="right"
          :overlayClassName="overlayClassName"
          @visibleChange="visibleChange"
        >
          <template slot="content">
            <div class="header">
              <div>
                <a-icon type="setting" />
                <span class="title">配置管理</span>
                <a-tooltip v-if="canDo">
                  <template slot="title">
                    一键备份将对配置管理中的配置数据应用服务的配置文件进行备份。备份文件恢复请参考备份zip包中的readme.txt
                  </template>
                  <a @click="onBackup">一键备份</a>
                </a-tooltip>
              </div>
              <a-icon type="close" @click="close" />
            </div>

            <div class="content">
              <div class="left">
                <div
                  v-for="item in menus"
                  :key="item.id"
                  :class="[
                    'config-item',
                    menus && menus.length > 2 && 'config-items'
                  ]"
                >
                  <span class="title">{{ item.name }}</span>
                  <div v-for="itm in item.children" :key="itm.id">
                    <a @click="itemClick(itm)">{{ itm.name }}</a>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <div class="config-button-area">
            <a-tooltip>
              <template slot="title"> 配置 </template>
              <custom-icon type="lu-icon-config1" />
            </a-tooltip>
          </div>
        </a-popover>
      </div>
    </div>
    <div class="sys-info" @click="onCollapse">
      <a-icon :type="collapsed ? 'right' : 'left'" class="arrow"></a-icon>
    </div>
  </div>
</template>

<script>
import { downLoadUserManual, backup } from '@/api/home';
import config from '../MenuView/config';
export default {
  components: {},
  props: {
    collapsed: Boolean
  },
  data() {
    this.config = config(this);
    let version = this.$store.state.project.version || '';
    return {
      version,
      menus: [],
      treeData: [],
      dataSource: [],
      visible: false,
      isActive: false,
      value: undefined
    };
  },
  computed: {
    overlayClassName() {
      let res = this.collapsed
        ? 'menu-view-config-popover  collapsed'
        : 'menu-view-config-popover';
      return res;
    },
    canDo() {
      const user = this.$store.state.account.user || {};
      return ['admin', 'dba'].includes(user.role);
    }
  },
  mounted() {
    this.getMenus();
    this.$nextTick(() => {
      const lastHeight = this.$el.offsetHeight;
      this.$bus.$emit('setPadding', lastHeight);
    });
  },
  created() {},
  methods: {
    show() {
      this.onConfig();
    },
    onConfig() {
      this.visible = true;
      this.isActive = true;
    },
    getMenus() {
      const sourceCode = _.get(this.$store.state.common, 'quickEntrance');
      const res = this.config.resource;
      const menu = res.filter(item => {
        return [
          '$menu_system',
          '$menu_user',
          '$menu_rule-conf',
          '$menu_config',
          '$menu_data-source-config'
        ].includes(item.id);
      });
      this.menus = menu;
      this.treeData = menu.map(item => {
        if (item.children) {
          item.children = item.children.map(itm => {
            return {
              ...itm,
              title: itm.name,
              value: itm.id
            };
          });
        }
        return {
          ...item,
          title: item.name,
          value: item.id
        };
      });
      const children = this.treeData.map(item => {
        return item.children;
      });
      const flatArr = _.flatten(children);
      this.dataSource = flatArr.filter(item => {
        return sourceCode.includes(item.id);
      });
      this.value = this.dataSource.map(item => {
        return item.id;
      });
    },
    itemClick(item) {
      const menuItem = this.getMenuItem(item.key) || {};
      if (menuItem.path) {
        this.$router.push({ path: menuItem.path });
      } else {
        this.$router.push({ name: item.key });
      }
      this.visible = false;
      this.isActive = false;
    },
    getMenuItem(key) {
      let res;
      const loop = list => {
        list.forEach(item => {
          if (res) return;
          if (item.key == key) {
            res = item;
            return false;
          }
          if (item.children && item.children.length > 0) {
            loop(item.children);
          }
        });
      };
      loop(this.menus);
      return res;
    },
    onChange(value) {
      const res = this.treeData.map(item => {
        return item.children;
      });
      const arr = _.flatten(res);
      this.dataSource = arr.filter(item => {
        return value.includes(item.id);
      });
    },
    remove(data) {
      this.dataSource = this.dataSource.filter(item => {
        return item.id != data.id;
      });
      this.value = this.dataSource.map(item => {
        return item.id;
      });
    },
    // 用户手册下载
    dowmLoadUser() {
      this.$showLoading({ tips: '正在下载' });
      downLoadUserManual()
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.log(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 一键备份
    onBackup() {
      this.$showLoading({ tips: '正在下载' });
      backup()
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.log(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onCollapse() {
      this.$emit('onCollapse');
    },
    visibleChange(visible) {
      if (!visible) {
        this.close();
      }
    },
    close() {
      this.visible = false;
      this.isActive = false;
    }
  }
};
</script>

<style lang="less" scoped>
.custom-collapse {
  z-index: 10;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 178px;
  background: #0c243e;
  transition: all 0.2s;
  background: transparent;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  > .sys-action {
    width: 100%;
    display: flex;
    flex-direction: column;
    color: #1f1f1f;
    font-size: 12px;
    padding-left: 12px;
    margin-bottom: 8px;
    > div {
      opacity: 0.4;
    }
    .config-area {
      display: flex;
      align-items: center;
      width: calc(100% - 12px);
      border-radius: 4px;
      opacity: 1;
      cursor: pointer;
      .anticon {
        font-size: 14px;
        padding: 0 12px;
      }
      span {
        font-size: 14px;
      }
      // &:hover {
      //   opacity: 1;
      // }
      &.active {
        opacity: 1;
        background: #3ca3f2;
        span {
          color: #1f1f1f;
        }
      }
      .config-button-area {
        width: 100%;
        padding: 4px 0;
        display: flex;
        justify-content: center;
      }
    }
  }
  > .sys-info {
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 12px;
    cursor: pointer;
    // .download-user-manual,
    // .version {
    //   font-size: 13px;
    //   color: rgba(255, 255, 255, 0.4);
    // }
    // .download-user-manual {
    //   cursor: pointer;
    //   &:hover {
    //     color: #fff;
    //   }
    // }
    .arrow {
      width: 15%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #1f1f1f;
      &:hover {
        opacity: 1;
        color: #1f1f1f;
      }
    }
  }
  &.collapsed {
    width: 80px;
    // height: 90px;
    > .sys-action {
      .config-button-area {
        display: flex;
        justify-content: center;
        > span {
          display: none;
        }
      }
    }
    > .sys-info {
      // height: 60px;
      // .download-user-manual,
      // .version {
      //   display: none;
      // }
      .arrow {
        color: #1f1f1f;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
<style lang="less">
.ant-popover {
  &.menu-view-config-popover {
    left: 170px !important;
    &.collapsed {
      left: 70px !important;
    }
    .ant-popover-content {
      .ant-popover-arrow {
        display: none;
      }
      .ant-popover-inner {
        .ant-popover-inner-content {
          padding: 24px;
          max-height: 600px;
          min-height: 360px;
          max-width: 400px;
          .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 32px;
            align-items: center;
            > div {
              font-size: 20px;
              color: #1f1f1f;
              font-weight: 600;
              .anticon {
                font-size: 18px;
                margin-right: 6px;
                color: #1f1f1f;
              }
              a {
                font-size: 14px;
                font-weight: 400;
                margin-left: 16px;
              }
              margin-right: 8px;
            }
            .anticon {
              font-size: 16px;
              color: #1f1f1f;
              font-weight: 600;
            }
          }
          .content {
            display: flex;
            justify-content: space-between;
            .left {
              display: flex;
              flex-wrap: wrap;
              padding-left: 32px;
              .config-item {
                width: 100%;
                display: flex;
                flex-direction: column;
                max-height: 200px;
                min-height: 80px;
                > span {
                  color: #27272a;
                  font-weight: 600;
                  margin-bottom: 22px;
                  white-space: nowrap;
                }
                > div {
                  margin-bottom: 20px;
                  white-space: nowrap;
                  a {
                    font-size: 13px;
                    color: #008adc;
                  }
                }
                &.config-items {
                  width: 50%;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>