<template>
  <div>
    <!-- 标题 branch name 区分是否有比对 -->
    <div class="review-wraper" v-if="isProject">
      <div class="tag-info">
        <span>{{ detaliData.review_point }} </span>
        <span>cost值从大到小排序</span>
      </div>
    </div>

    <!-- 第一部分：1、最差sql+执行计划 2、sqlmap -->
    <div class="sql-list-item">
      <a-collapse accordion defaultActiveKey="0">
        <template v-for="(item, index) in sqlList">
          <a-collapse-panel :key="index">
            <div slot="header" class="header">
              <span>
                <custom-icon
                  class="rule-icon error"
                  type="lu-icon-unusual"
                  v-if="item.risk == 'error'"
                />
                <custom-icon
                  class="rule-icon high"
                  type="lu-icon-alarm"
                  v-else-if="item.risk == 'high'"
                />
                <custom-icon
                  class="rule-icon low"
                  type="lu-icon-alarm"
                  v-else-if="item.risk == 'low'"
                />
              </span>
              <span class="text">{{ item.sql_text }}</span>
              <LabelCard
                v-if="item.label_obj_id"
                ref="labelCard"
                :labelStatus="item.label_status"
                :id="item.label_obj_id"
                :text="item.label_attribute"
                mode="icon"
              />
              <span @click.stop="openOptimizeAdvice(item)" class="advice-btn">
                <img src="~@/assets/img/private/dataview/advice.png" />
              </span>
            </div>
            <!-- 规则、报错区域 -->
            <div class="rule-and-error-message">
              <div class="rule-container" v-if="item.ai_comment.length > 0">
                <a class="expand-action" @click="onExpand"
                  >{{ !ruleExpand ? '查看全部' : '收起' }}({{
                    item.ai_comment.length
                  }})</a
                >
                <div class="shrink-content" v-if="!ruleExpand">
                  <span v-for="(el, index) in item.ai_comment" :key="index">
                    <custom-icon
                      class="rule-icon high"
                      type="lu-icon-alarm"
                      v-if="el.rule_result == 0"
                    />
                    <custom-icon
                      class="rule-icon low"
                      type="lu-icon-alarm"
                      v-if="el.rule_result == 1"
                    />
                    <span>{{ el.ai_comment }}</span>
                  </span>
                </div>
                <div class="expand-content rule" v-if="ruleExpand">
                  <div v-for="(el, index) in item.ai_comment" :key="index">
                    <custom-icon
                      class="rule-icon high"
                      type="lu-icon-alarm"
                      v-if="el.rule_result == 0"
                    />
                    <custom-icon
                      class="rule-icon low"
                      type="lu-icon-alarm"
                      v-if="el.rule_result == 1"
                    />
                    <span>{{ el.ai_comment }}</span>
                    <div
                      v-if="
                        el.data_source_list && el.data_source_list.length > 0
                      "
                      class="data-source-content"
                    >
                      <span
                        v-for="(db, index) in el.data_source_list"
                        :key="index"
                      >
                        <a-tag :class="db.env.toLowerCase()">{{
                          db.env.toLowerCase() == 'test' ? '测试' : '生产'
                        }}</a-tag>
                        <DbImg
                          :value="db.db_type"
                          :schemaName="db.name + '(' + db.db_url + ')'"
                          :limit="32"
                          mode="simple"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="error-message-container"
                v-if="item.error_message.length > 0"
              >
                <a class="expand-action" @click="onErrorExpand"
                  >{{ !errorExpand ? '查看全部' : '收起' }}({{
                    item.error_message.length
                  }})</a
                >
                <div class="shrink-content" v-if="!errorExpand">
                  <span v-for="(el, index) in item.error_message" :key="index">
                    <custom-icon
                      class="rule-icon error"
                      type="lu-icon-unusual"
                    />
                    {{ el.error_message }}
                  </span>
                </div>
                <div class="expand-content error-message" v-if="errorExpand">
                  <div v-for="(el, index) in item.error_message" :key="index">
                    <span>
                      <custom-icon
                        class="rule-icon error"
                        type="lu-icon-unusual"
                      />
                      {{ el.error_message }}</span
                    >
                    <div
                      v-if="
                        el.data_source_list && el.data_source_list.length > 0
                      "
                      class="data-source-content"
                    >
                      <span
                        v-for="(db, index) in el.data_source_list"
                        :key="index"
                      >
                        <a-tag :class="db.env.toLowerCase()">{{
                          db.env.toLowerCase() == 'test' ? '测试' : '生产'
                        }}</a-tag>
                        <DbImg
                          :value="db.db_type"
                          :schemaName="db.name + '(' + db.db_url + ')'"
                          :limit="32"
                          mode="simple"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- sql文本 -->
            <sql-highlight
              :sql="item.sql_text"
              :needCopy="true"
              :title="'[' + 'SQL_ID：' + `${item.sql_id}` + ']'"
            ></sql-highlight>

            <!-- 执行计划 -->
            <div class="sql-plan-content" v-if="item.sql_plan">
              <div class="title">
                <span>当前执行计划</span>
                <span class="data-source" v-if="item.data_source_info">
                  <a-tag :class="item.data_source_info.env.toLowerCase()">{{
                    item.data_source_info.env.toLowerCase() == 'test'
                      ? '测试'
                      : '生产'
                  }}</a-tag>
                  <DbImg
                    :value="item.data_source_info.db_type"
                    :schemaName="
                      item.data_source_info.name +
                      '(' +
                      item.data_source_info.db_url +
                      ')'
                    "
                    :limit="32"
                    mode="simple"
                  />
                </span>
              </div>
              <pre>{{ item.sql_plan }}</pre>
            </div>
          </a-collapse-panel>
        </template>
      </a-collapse>
    </div>

    <!-- 优化建议弹窗 -->
    <AdviceModal ref="AdviceModal" />
  </div>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
import CodeMirror from '@/components/CodeMirror';
import SqlHighlight from '@/components/SqlHighlight';
import LabelCard from '@/components/Biz/LabelCard';
import Tag from '@/components/Biz/Tag';
import AdviceModal from './adviceModal.vue';
import Draggable from '@/utils/drag';
export default {
  components: { CodeMirror, SqlHighlight, LabelCard, Tag, LimitLabel, AdviceModal },
  props: {
    detaliData: {
      type: Object,
      default: () => {}
    },
    sqlList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    isProject() {
      const name = this.$route.name;
      return name == 'project-review-review';
    }
  },
  data() {
    return {
      ruleExpand: false,
      errorExpand: false
    };
  },
  methods: {
    onExpand() {
      this.ruleExpand = !this.ruleExpand;
    },
    onErrorExpand() {
      this.errorExpand = !this.errorExpand;
    },
    openOptimizeAdvice(record) {
      this.$refs.AdviceModal.show(this.detaliData.id, record.id);
      this.setInfoModalDraggable();
    },
     // 弹窗拖动
     setInfoModalDraggable() {
      !this.infoModalDragInstance &&
        setTimeout(() => {
          const modalElement = this.$refs.AdviceModal.$el;
          if (modalElement && modalElement.querySelector) {
            this.infoModalDragInstance = new Draggable({
              el: modalElement.querySelector('.ant-modal-content'),
              handler: modalElement.querySelector('.ant-modal-header')
            });
          } else {
            this.setInfoModalDraggable();
          }
        }, 800);
    }
  }
};
</script>

<style lang="less" scoped>
@import './commonClass.less';

.review-wraper {
  padding-left: 0 !important;
  .tag-info {
    padding: 0 0 16px 0;
    display: flex;
    justify-content: space-between;
    span {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #27272a;
      &:last-child {
        color: #8c8c8c;
      }
    }
  }
}
.sql-list-item {
  /deep/.ant-collapse {
    .ant-collapse-item {
      .rule-icon {
        color: #e4e4e7;
        font-size: 12px;
        align-self: self-start;
        padding-top: 4px;
        &.high {
          color: #e71d36;
          margin-right: 4px;
        }
        &.low {
          color: #f29339;
          margin-right: 4px;
        }
        &.error {
          color: #71717a;
          margin-right: 4px;
          margin-top: 2px;
        }
      }
      .ant-collapse-header {
        .header {
          display: flex;
          align-items: center;
          .text {
            padding: 0 8px;
            width: 90%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
          }
        }
      }
      .ant-collapse-content {
        .rule-and-error-message {
          .error-message-container,
          .rule-container {
            background: #fffbe6;
            border: 1px solid rgba(255, 229, 143, 1);
            margin-bottom: 16px;
            position: relative;
            .expand-action {
              position: absolute;
              top: 12px;
              right: 16px;
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #1890ff;
            }
            .shrink-content {
              width: 80%;
              display: flex;
              justify-content: space-between;
              // overflow: hidden;
              // text-overflow: ellipsis;
              // word-break: normal;
              // white-space: normal;
              padding: 12px 16px;
              span {
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #1f1f1f;
                white-space: nowrap;
                margin-right: 16px;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
            .expand-content {
              display: flex;
              flex-direction: column;
              width: 90%;
              &.rule {
                > div {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #1f1f1f;
                  white-space: nowrap;
                  padding: 12px 16px;
                }
              }
              &.error-message {
                > div {
                  padding: 12px 16px 16px 16px;
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #1f1f1f;
                  &:last-child {
                    padding: 8px 16px;
                  }
                }
              }
              .data-source-content {
                > span {
                  display: flex;
                  padding: 8px 0;
                  .ant-tag {
                    margin-right: 8px;
                    &.test {
                      background: #f6ffed;
                      border: 1px solid rgba(183, 235, 143, 1);
                      font-family: PingFangSC-Regular;
                      font-size: 12px;
                      color: #52c41a;
                    }
                    &.prod {
                      background: #fff7e6;
                      border: 1px solid rgba(255, 213, 145, 1);
                      font-family: PingFangSC-Regular;
                      font-size: 12px;
                      color: #fa8c16;
                    }
                  }
                }
              }
            }
          }
        }
        .sql-format {
          background: #e6f4ff;
          border: 1px solid rgba(240, 240, 240, 1);
          border-radius: 4px;
        }
        .sql-plan-content {
          .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
            padding-bottom: 8px;
            > .data-source {
              display: flex;
              padding: 8px 0;
              .ant-tag {
                margin-right: 8px;
                &.test {
                  background: #f6ffed;
                  border: 1px solid rgba(183, 235, 143, 1);
                  font-family: PingFangSC-Regular;
                  font-size: 12px;
                  color: #52c41a;
                }
                &.prod {
                  background: #fff7e6;
                  border: 1px solid rgba(255, 213, 145, 1);
                  font-family: PingFangSC-Regular;
                  font-size: 12px;
                  color: #fa8c16;
                }
              }
            }
          }
          > pre {
            border-radius: 10px;
            background: #f4f5f7;
            padding: 16px;
          }
        }
      }
    }
  }
  .advice-btn {
    display: block;
    margin-top: -7px;
    img {
      width: 100px;
    }
  }
}

.part-1 {
  padding: 0 !important;
  /deep/ .review-wraper2 {
    margin-top: 16px;
    .pre-error-message > pre:last-child {
      padding-bottom: 16px !important;
    }
    .pre-error-message > pre:first-child {
      padding-top: 16px !important;
    }
    .pre-error-message {
      display: flex;
      flex-direction: column;
      overflow: auto;
      width: 100%;
      max-height: 200px;
      pre {
        overflow: visible;
      }
    }
    .pre-ai-comment {
      background: rgba(0, 59, 114, 0.13);
      border-radius: 10px 10px 0 0;
      overflow: auto;
      width: 100%;
      pre {
        padding: 8px 16px 0 16px;
        overflow: auto;
        background: rgba(0, 59, 114, 0.13);
        font-size: 14px;
        color: #3e60c1;
        font-weight: 500;
        display: flex;
        align-items: center;
        margin-bottom: 0;
        &:last-child {
          padding-bottom: 8px;
        }
        .rule-icon {
          margin-right: 8px;
          color: #e4e4e7;
          font-size: 12px;
          align-self: self-start;
          padding-top: 4px;
          &.high {
            color: #e71d36;
            margin-right: 4px;
          }
          &.low {
            color: #f29339;
            margin-right: 4px;
          }
          &.error {
            color: #71717a;
            margin-right: 4px;
          }
        }
        > span {
          padding-right: 16px;
        }
      }
    }
    .custom-code-mirror-diff {
      .ccm-diff-title-wrapper {
        .ccm-diff-title {
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          .anticon {
            font-size: 16px;
            color: #27272a;
          }
          > span {
            > span {
              font-size: 14px;
              color: #27272a;
            }
          }
        }
      }
      .ccm-diff-container {
        background: #f4f5f7;
        .CodeMirror-merge-pane {
          border-radius: 10px;
          background: #f4f5f7;
        }
      }
      &.sql-detail-compare {
        .ccm-diff-title-wrapper {
          .ccm-diff-title {
            display: flex;
            flex-direction: column;
            > div {
              // margin: 0 0 8px 0;
              > div {
                padding: 0 !important;
                border-radius: 10px 10px 0 0 !important;
                background: rgba(0, 59, 114, 0.13) !important;
                .pre-error-message {
                  overflow: auto;
                  .pre-rule {
                    padding: 8px 16px 0 16px;
                    > pre {
                      // font-family: PingFangSC-Medium;
                      font-size: 14px;
                      color: #3e60c1;
                      font-weight: 500;
                      margin-bottom: 8px;
                      padding: 0 !important;
                      display: flex;
                      align-items: center;
                      .rule-icon {
                        margin-right: 8px;
                        color: #e4e4e7;
                        font-size: 12px;
                        align-self: self-start;
                        padding-top: 5px;
                        &.high {
                          color: #e71d36;
                          margin-right: 4px;
                        }
                        &.low {
                          color: #f29339;
                          margin-right: 4px;
                        }
                        &.error {
                          color: #71717a;
                          margin-right: 4px;
                        }
                      }
                      > span {
                        padding-right: 16px;
                      }
                    }
                  }
                }
              }
            }
            .ant-divider {
              display: none;
            }
          }
        }
        .ccm-diff-container {
          // background: rgba(0, 59, 114, 0.06);
          .CodeMirror-merge-pane {
            border-radius: 0 0 10px 10px;
            background: rgba(0, 59, 114, 0.06) !important;
          }
        }
      }
    }
  }
  h4 {
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #27272a;
    font-weight: 600;
    .anticon {
      font-size: 16px;
      color: #27272a;
    }
    > span {
      > span {
        font-size: 14px;
        color: #27272a;
      }
    }
  }
}
</style>
