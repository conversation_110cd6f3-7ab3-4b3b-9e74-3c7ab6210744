<template>
  <div class="database-audit-slow-log-analyze-content">
    <div class="header-block">
      <div class="title">慢日志列表</div>
      <div class="btn-area">
        <a-button
          type="primary"
          @click="audit"
          v-if="dataSourceId && $permission.slowLogAnalyze('aiAudit')"
          >AI审核</a-button
        >
        <a-tooltip v-if="$permission.slowLogAnalyze('export')">
          <template slot="title">导出</template>
          <custom-icon type="cloud-download" @click="download" />
        </a-tooltip>
      </div>
    </div>
    <!-- 搜索内容 -->
    <SearchArea
      v-bind="searchParams || {}"
      @reset="reset"
      @search="search"
      ref="search"
      :searchData="searchData || {}"
    ></SearchArea>
    <div class="table-block">
      <Table
        ref="table"
        v-bind="tableParams"
        :dataSource="dataSource || []"
        class="new-view-table small-size"
        @selectChange="selectChange"
      >
        <LimitLabel
          slot="sql_fingerprint"
          slot-scope="{ text }"
          :label="text"
          :limit="24"
        ></LimitLabel>
        <template slot="risk" slot-scope="{ record, text }">
          <RiskLevel :aiComment="record.ai_comment || {}" :value="text" />
        </template>
        <div slot="customTitle" class="custom-title" @click="customSetColumn">
          <span>操作</span>
          <a-tooltip>
            <template slot="title">表头设置</template>
            <a-icon type="control" />
          </a-tooltip>
        </div>
        <custom-btns-wrapper slot="action" slot-scope="{ record }">
          <a
            actionBtn
            @click="onSqlException(record)"
            :disabled="!record.data_source_id"
            v-if="$permission.slowLogAnalyze('explain')"
            >Explain</a
          >
          <!-- <a actionBtn>Trace</a> -->
        </custom-btns-wrapper>
      </Table>
      <SqlExceptionDrawer
        ref="sqlException"
        :id="dataSourceId"
        :dbType="dbType"
      />
      <CustomSetColumnModal
        ref="customSetColumn"
        :id="dataSourceId"
        @save="save"
      ></CustomSetColumnModal>
    </div>
  </div>
</template>

<script>
import SearchArea from '@/components/Biz/SearchArea';
import CustomSetColumnModal from '../components/customSetColumnModal';
import common from '@/utils/common';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import SqlExceptionDrawer from '../components/sqlExceptionDrawer';
import RiskLevel from '@/components/Biz/ReviewDetail/RiskLevel';
import config from './config';
import { saveTableHeaderInfo } from '@/api/databaseaudit/topsql';
import {
  getTableHeaderInfo,
  download,
  slowLogExamineSql
} from '@/api/databaseaudit/slowlog';
export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    CustomSetColumnModal,
    SqlExceptionDrawer,
    LimitLabel,
    SearchArea,
    RiskLevel,
    Table
  },
  props: {
    pane: Object
  },
  data() {
    this.config = config(this);
    return {
      id: null,
      list: [],
      dbType: null,
      dataSourceId: null,
      searchParams: {
        fields: this.config.searchFields(this.dataSourceId),
        multiCols: 3
      },
      searchData: {},
      dataSource: [],
      tableParams: {
        url: '/sqlreview/after_audit/get_slow_log_list',
        reqParams: {
          record_id: this.pane.id
        },
        method: 'post',
        columns: this.config.columns,
        rowKey: 'id',
        rowSelection: !this.dataSourceId
          ? {}
          : {
              type: 'checkbox', // 多选单选
              columnWidth: '20'
            },
        scroll: { x: 'max-content' }
      },
      selectedRowKeys: []
    };
  },
  computed: {},
  created() {},
  mounted() {
    const params = { data_source_id: this.pane.data_source_id, source_type: 4 };
    this.init(params);
  },
  methods: {
    init(params) {
      getTableHeaderInfo(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data') || {};
            const list = resData.display_list;
            this.dealTableHeaderInfo(list);
          } else {
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 处理表头数据
    dealTableHeaderInfo(data = []) {
      let columns = [];
      data.forEach(item => {
        this.config.columns.forEach(el => {
          if (item.key && item.key == el.key) {
            columns.push({
              ...el,
              title: item.value,
              sorter: item.sorter,
              sortDirections: ['descend', 'ascend']
            });
          }
        });
      });
      columns.push({
        // title: '操作',
        key: 'action',
        slots: { title: 'customTitle' },
        scopedSlots: { customRender: 'action' },
        visible: $permissionBatch.some([
          { module: 'slowLogAnalyze', values: ['explain'] }
        ]),
        fixed: 'right'
      });
      columns = columns.map(item => {
        return {
          ...item,
          width: undefined
        };
      }).filter(item => item.visible !== false);
      this.$set(this.tableParams, 'columns', columns);
    },
    // 处理初始数据
    // dealTableHeaderInfo(list = []) {
    //   this.list = list;
    // },
    // 自定义表头
    customSetColumn() {
      this.$refs.customSetColumn.show(4);
    },
    // ai审核
    audit() {
      const { table } = this.$refs;
      const selectedRowKeys = table.selectedRowKeys;
      if (_.isEmpty(selectedRowKeys)) {
        this.$message.warn('请选择数据!');
        return;
      }
      const params = {
        slow_log_r_id: this.id,
        examine_list: selectedRowKeys,
        data_source_id: this.dataSourceId
      };
      this.$showLoading();
      slowLogExamineSql(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ method: 'success', tips: '审核完成' });
            this.selectedRowKeys = [];
            table.refreshKeep(null, { _clear: true });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 导出
    download() {
      this.$showLoading({
        tips: `下载中...`
      });
      const { table } = this.$refs;
      const { searchParams } = table;
      const params = {
        record_id: this.id,
        source_type: 4,
        ...searchParams
      };
      download(params)
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    save(data = [], list = []) {
      this.$showLoading();
      const params = {
        db_type: 'MYSQL',
        source_type: 4,
        keys: [...data]
      };
      saveTableHeaderInfo(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.dealTableHeaderInfo(list);
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    // 表列表 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    // 查询
    search(data, params = {}) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      const { keep, type } = params;
      if (keep) {
        table.refreshKeep(type, data);
      } else {
        table.refresh(null, data);
      }
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.searchParams = {};
      table.refresh();
    },
    // 执行计划
    onSqlException(record) {
      const prarams = {
        info: record.sql_sample,
        db: record.db,
        data_source_id: record.data_source_id
      };
      const data = {
        instance_usage: this.pane.db_env,
        db_type: this.pane.db_type,
        label: this.pane.db_name
      };
      this.$refs.sqlException.show('slowlog', prarams, data);
    }
  },
  watch: {
    pane: {
      handler(newVal) {
        this.id = newVal.id;
        this.dbType = newVal.db_type;
        this.dataSourceId = newVal.data_source_id;
        this.$set(this.tableParams, 'reqParams', { record_id: newVal.id });
        this.$set(
          this.searchParams,
          'fields',
          this.config.searchFields(newVal)
        );
        this.init({ data_source_id: this.pane.data_source_id, source_type: 4 });
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.database-audit-slow-log-analyze-content {
  border-left: 1px solid #ededec;
  background: #fff;
  .header-block {
    display: flex;
    justify-content: space-between;
    height: 56px;
    align-items: center;
    border-bottom: 1px solid #ebebec;
    padding: 0 24px;
    .title {
      font-size: 16px;
      color: #27272a;
      text-align: center;
      font-weight: 500;
    }
    .btn-area {
      .ant-btn {
        // border-radius: 4px !important;
        padding: 0 24px;
      }
      .anticon {
        font-size: 16px;
        color: #000000;
        margin-left: 16px;
        &:hover {
          color: #4ec3f5;
          cursor: pointer;
        }
      }
    }
  }
  .custom-title {
    max-width: 100%;
    min-width: 100px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .anticon {
      margin-right: 4px;
      font-size: 16px;
      border-radius: 50%;
      width: 26px;
      height: 26px;
      line-height: 30px;
      &:hover {
        background: #4ec3f5;
        color: #ffffff;
        cursor: pointer;
      }
    }
  }
  /deep/.search-area {
    background: #fff;
    box-shadow: none;
    border: none;
    border-radius: 0 0 16px 16px;
    padding: 20px 24px 12px 24px;
    .ant-form {
      > .ant-row {
        > .ant-col {
          .ant-form-item {
            .ant-form-item-label {
              width: auto;
              min-width: 88px;
            }
            .ant-form-item-control-wrapper {
              min-width: 240px !important;
            }
          }
        }
      }
    }
  }
  .table-block {
    border-top: 1px solid #ededec;
  }
}
@media screen and (max-width: 1640px) {
  .database-audit-slow-log-analyze-content {
    /deep/.search-area {
      .ant-form {
        > .ant-row {
          > .ant-col {
            width: 50%;
          }
        }
      }
    }
  }
}
</style>