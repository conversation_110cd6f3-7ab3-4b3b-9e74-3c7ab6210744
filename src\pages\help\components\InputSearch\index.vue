<template>
  <div>
    <InputSearch value="123123" @search="search"></InputSearch>
    <a-divider />
    <InputSearch @search="search"></InputSearch>
  </div>
</template>

<script>
import InputSearch from '@/components/InputSearch';
export default {
  components: { InputSearch },
  props: {},
  data() {
    return {
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    search(val) {
      console.log('搜索的数据', val)
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>