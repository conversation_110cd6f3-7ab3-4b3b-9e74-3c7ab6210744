import Http from '@/utils/request';

export function detailIdList(data = {}) {
  return Http({
    url: `/sqlreview/review/detail_id_list`,
    method: 'post',
    data
  });
}

export function detailSqlList(params = {}) {
  return Http({
    url: '/sqlreview/review/detail_sql_list',
    method: 'get',
    params
  })
}

export function getQuestionCommand(data = {}) {
  return Http({
    url: '/sqlreview/review/get_question_command',
    method: 'post',
    data
  })
}

export function sendQuestionCommand(data = {}) {
  return Http({
    url: '/sqlreview/review/send_question_command_stream',
    method: 'post',
    data
  })
}

export function getReviewAISuggestions(data = {}) {
  return Http({
    url: '/sqlreview/review/ai_suggestions_stream',
    method: 'post',
    data
  })
}

export function getModelList() {
  return Http({
    url: '/sqlreview/review/get_model_list',
    method: 'get'
  })
}

export default {}
