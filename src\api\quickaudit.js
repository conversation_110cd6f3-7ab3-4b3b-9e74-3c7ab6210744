import Http from '@/utils/request';

// 文本审核接口
export function quickRequest(params = {}, isUpload) {
  return Http({
    url: isUpload ? '/sqlreview/quick_audit/quick_audit_upload' : '/sqlreview/quick_audit/quick_review',
    method: 'post',
    data: params,
    headers: isUpload
      ? { 'Content-Type': 'multipart/form-data' }
      : { 'Content-Type': 'application/json' }
  });
}

// 数据详情
export function sqlDetailInfo(params) {
  return Http({
    url: `/sqlreview/quick_audit/quick_audit_get_detail`,
    method: 'get',
    params
  });
}

// 导出
export function quickReviewExport(params) {
  return Http({
    url: `/sqlreview/quick_audit/quick_review_export`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}

export default {};
