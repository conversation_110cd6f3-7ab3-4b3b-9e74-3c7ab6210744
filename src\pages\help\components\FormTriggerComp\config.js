export default function (ctx) {
  const baseFields = [
    {
      type: 'Input',
      label: '自动保存',
      key: 'name',
      mode: 'autoSave',
      rules: [
        { required: true, message: '该项为必填项', trigger: 'blur' }
      ]
    },
    {
      type: 'Input',
      label: '数据库名称',
      key: 'schema_name',
      rules: [
        { required: true, message: '该项为必填项', trigger: 'blur' }
      ]
    },
    (formData = {}) => {
      return {
        type: 'Select',
        label: '下拉多选',
        key: 'app_ids',
        props: {
          url: '/ludms/base-info/app/list-lv',
          mode: 'multiple'
        },
        // width: '80%',
        rules: [
          // { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      }
    },
    (formData = {}) => {
      return {
        type: 'Select',
        label: '下拉单选',
        key: 'app_id',
        props: {
          url: '/ludms/base-info/app/list-lv'
        },
        rules: [
          // { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      }
    },
    {
      type: 'Select',
      label: '下拉Tags',
      key: 'select_tags',
      props: {
        url: '/ludms/base-info/schema/db-type',
        // maxTags: 1,
        separator: '&',
        mode: 'tags'
        // allowClear: false,
        // allowSearch: true,
        // backSearch: true
      },
      rules: [{ required: true, message: '该项为必填项' }]
    },
    (formData = {}) => {
      return {
        type: 'DataBaseChoose',
        label: '实例选择',
        key: 'instance_id',
        props: {
          url: '/ludms/base-info/instance/list-lv',
          reqParams: {
            'db_type': 'MYSQL'
          },
          needConfirm: false,
          mode: 'default',
          backSearchOnlyOnSearch: true
        },
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      }
    }
  ];
  return {
    baseFields
  }
}