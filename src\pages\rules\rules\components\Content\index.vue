<template>
  <div class="config-rules-content">
    <!-- 预加载括号 -->
    <Bracket color="#F29339" style="display: none" />
    <!-- 基础信息 -->
    <div class="rules-content-base-info">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <a-icon type="profile" />基础信息 </span>
        <Form
          ref="baseInfo"
          class="base-info-form"
          v-bind="baseInfoParams"
          :formData="baseInfoData"
        >
          <DataSourceBlocks
            ref="dbType"
            slot="db_type"
            @choose="choose"
            :itemWidth="120"
            :dbType="baseInfoData.db_type"
            :importDataSource="importDataSource"
            :disabled="type !== 'add'"
          />
        </Form>
      </a-card>
    </div>

    <div>
      <div class="rules-content-condition" :data-row="buttonNumberConditions">
        <a-card style="width: 100%" class="common-pure-card" :bordered="false">
          <span class="title">
            <custom-icon type="setting" />DDL规则属性设置
          </span>
          <Form
            ref="baseInfoConditions"
            class="ddl-rule-set-form"
            v-bind="baseInfoParamsConditions"
            :formData="baseInfoDataConditions"
          />
        </a-card>
      </div>
      <!-- 条件区域-->
      <div class="rules-content-condition" :data-row="buttonNumberConditions">
        <a-card style="width: 100%" class="common-pure-card" :bordered="false">
          <span class="title"> <custom-icon type="lu-icon-if" />条件区域 </span>
          <TableEdit
            @change="onChangeNumberConditions"
            ref="tableEditConditions"
            v-bind="tableParamsConditions || {}"
            :dataSource="tableDataConditions"
          ></TableEdit>
          <div class="rules-btn-add-condition">
            <a-button
              @click="addConditionConditions"
              icon="plus"
              v-if="type !== 'detail'"
            ></a-button>
          </div>
        </a-card>
      </div>
      <!--目标属性的约束条件  -->
      <div class="rules-content-condition" :data-row="buttonNumberDDL">
        <a-card style="width: 100%" class="common-pure-card" :bordered="false">
          <span class="title">
            <custom-icon type="lu-icon-if" />目标属性的约束条件
          </span>
          <TableEdit
            @change="onChangeNumberDDL"
            ref="tableEditDDL"
            v-bind="tableParamsDDL || {}"
            :dataSource="tableDataDDL"
            class="table-edit-ddl"
          ></TableEdit>
          <div class="btn-add-condition">
            <a-button
              @click="addConditionDDL"
              icon="plus"
              v-if="type !== 'detail'"
            ></a-button>
          </div>
        </a-card>
      </div>
    </div>
    <!--输出结果  -->
    <div class="rules-outport-results">
      <a-card style="width: 100%" class="common-pure-card" :bordered="false">
        <span class="title"> <custom-icon type="lu-icon-do" />输出结果 </span>
        <Form
          ref="outportResults"
          class="rules-outport-results-form"
          v-bind="outportResultsParams"
          :formData="outportResultsData"
        />
      </a-card>
    </div>
  </div>
</template>
<script>
import Form from '@/components/Form';
import TableEdit from '@/components/TableEdit';
import Select from '@/components/Select';
import Bracket from '@/components/Biz/Bracket';
import DataSourceBlocks from '@/components/Biz/DataSourceBlocks';
import MarkdownViewer from '@/components/Markdown/viewer';
import config from './config';
import { getRuleSetList } from '@/api/config/rule';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Form,
    TableEdit,
    Select,
    DataSourceBlocks,
    Bracket,
    MarkdownViewer
  },
  props: {
    type: {
      type: String,
      default: 'add'
    },
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    },
    rule_type: {
      type: String,
      default: ''
    }
  },
  data() {
    this.config = config(this);
    let dbType = window.localStorage.getItem('db_type') || '';
    return {
      dbType,
      baseInfoData: {
        rule_type: this.rule_type,
        db_type: dbType,
        ob_mode: null
      },
      baseInfoDataConditions: {},
      buttonNumberDDL: 0,
      buttonNumberConditions: 1,
      baseInfoParams: {
        fixedLabel: true,
        // gutter: 32,
        // multiCols: 1,
        layout: 'horizontal',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        fields: this.config.baseInfo
      },
      baseInfoParamsConditions: {
        fixedLabel: true,
        gutter: 32,
        multiCols: 3,
        // labelCol: { span: 8 },
        // wrapperCol: { span: 16 },
        layout: 'horizontal',
        fields: this.config.baseInfoConditions
      },
      tableDataConditions: [],
      tableDataDDL: [],
      tableParamsDDL: {
        initEditStatus: true,
        actionBtns: this.type !== 'detail' ? ['remove'] : [],
        editConfig: this.config.condition.editConfigDDL(),
        columns: this.config.condition.columnsDDL,
        pagination: false,
        rowKey: 'id'
      },
      tableParamsConditions: {
        initEditStatus: true,
        actionBtns: this.type !== 'detail' ? ['remove'] : [],
        editConfig: this.config.condition.editConfigConditions(),
        columns: this.config.condition.columnsConditions,
        pagination: false,
        leastNum: 1,
        rowKey: 'id'
      },
      ruleSetParamsDDL: {
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 0 },
        wrapperCol: { span: 24 }
      },
      ruleSetData: {},
      value: false,
      rule_setList: [],
      db_type: '',
      outportResultsParams: {
        fixedLabel: true,
        gutter: 32,
        multiCols: 1,
        // labelCol: { span: 8 },
        // wrapperCol: { span: 16 },
        layout: 'horizontal',
        fields: this.config.outportResults
      },
      outportResultsData: {},
      rulesOptions: [],
      importDataSource: [
        { icon: 'lu-icon-oracle', code: 'ORACLE', text: 'ORACLE' },
        { icon: 'lu-icon-mysql', code: 'MYSQL', text: 'MYSQL' },
        { icon: 'lu-icon-pgsql', code: 'POSTGRE', text: 'POSTGRE' },
        { icon: 'lu-icon-tidb', code: 'TIDB', text: 'TIDB' },
        { icon: 'lu-icon-oceanbase', code: 'OCEANBASE', text: 'OCEANBASE' },
        { icon: 'lu-icon-tdsql-1', code: 'TDSQL', text: 'TDSQL' },
        { icon: 'lu-icon-db2', code: 'DB2', text: 'DB2' },
        { icon: 'lu-icon-sql-server', code: 'SQLSERVER', text: 'SQLSERVER' },
        { icon: 'lu-icon-kingbase', code: 'KINGBASE', text: 'KINGBASE' },
        { icon: 'lu-icon-goldendb', code: 'GOLDENDB', text: 'GOLDENDB' },
        { icon: 'lu-icon-gaussdb', code: 'GAUSSDB', text: 'GAUSSDB' }
      ]
    };
  },
  created() {},
  mounted() {
    this.getRuleSetListFn();
  },
  destroyed() {
    this.$bus.$off('input-modal');
  },
  methods: {
    change(e) {
      this.$set(
        this.outportResultsData,
        'suggest',
        this.$el.querySelector('.source').value
      );
    },
    onChange(e) {
      this.value = e.target.checked;
    },
    onChangeNumberDDL(data) {
      this.buttonNumberDDL = data.length;
    },
    onChangeNumberConditions(data) {
      console.log(data);
      this.buttonNumberConditions = data.length;
    },
    addConditionDDL() {
      const { tableEditDDL } = this.$refs;
      tableEditDDL.addLine();
    },
    addConditionConditions() {
      const { tableEditConditions } = this.$refs;
      tableEditConditions.addLine();
    },
    getData() {
      const {
        baseInfo,
        baseInfoConditions,
        tableEditConditions,
        tableEditDDL,
        outportResults
      } = this.$refs;
      return new Promise((resolve, reject) => {
        Promise.all([
          baseInfo.validate(),
          baseInfoConditions.validate(),
          tableEditConditions.validate(),
          tableEditDDL.validate(),
          outportResults.validate()
        ])
          .then(values => {
            const outportResultsData = outportResults.getData();
            const conditionsDDL = tableEditDDL.getData();
            const tableEditConditionsData = tableEditConditions.getData();
            const condition = baseInfoConditions.data;
            resolve({
              ...baseInfo.data,
              db_type: baseInfo.data.ob_mode
                ? baseInfo.data.ob_mode
                : baseInfo.data.db_type,
              ...outportResultsData,
              condition: {
                ...condition,
                conditions: tableEditConditionsData.map(item => {
                  if (this.type === 'add') {
                    delete item['id'];
                  }
                  return item;
                })
              },
              constraint: conditionsDDL.map(item => {
                if (this.type === 'add') {
                  delete item['id'];
                }
                return item;
              })
            });
          })
          .catch(e => {
            reject(e);
          });
      });
    },
    // 数据库选择
    choose(value) {
      this.dbType = value;
      const baseInfo = this.$refs.baseInfo;
      baseInfo.saving({
        db_type: value,
        ob_mode: null,
        rule_set_uids: null
      });
      // this.getRuleSetListFn(value);
    },
    getRuleSetListFn(data = '') {
      const params = {
        db_type: data || this.dbType
      };
      getRuleSetList(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            const resData = _.get(res, 'data.data');
            this.rulesOptions = resData;
            if (this.type !== 'detail') {
              const { baseInfo } = this.$refs;
              baseInfo.refresh();
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  },
  watch: {
    dataSource: {
      handler(newVal, oldVal) {
        if (newVal.db_type == 'OB_MYSQL' || newVal.db_type == 'OB_ORACLE') {
          this.baseInfoData.db_type = 'OCEANBASE';
        } else if (
          newVal.db_type == 'TD_MYSQL' ||
          newVal.db_type == 'TD_PGSQL'
        ) {
          this.baseInfoData.db_type = 'TDSQL';
        } else {
          this.baseInfoData.db_type = newVal.db_type;
        }
        // const { tableEdit } = this.$refs;
        this.baseInfoData = {
          ...this.baseInfoData,
          name: newVal.name,
          desc: newVal.desc,
          // db_type: newVal.db_type,
          ob_mode: newVal.db_type,
          level: Number(newVal.level),
          rule_type: newVal.rule_set_type,
          rule_set_uids: newVal.rule_set_uids
        };
        this.outportResultsData = {
          level: newVal.level,
          suggest: newVal.suggest
        };
        this.baseInfoDataConditions = {
          category: newVal.rule_type,
          property: newVal.target_property
        };
        this.tableDataConditions = newVal.condition.map(item => {
          this.$set(item, 'value', item.rule_value);
          delete item.rule_value;
          return item;
        });
        this.tableDataDDL = newVal.constraint;
        let ruleUid = newVal.rule_set_uids;
        if (ruleUid && ruleUid.length !== 0) {
          this.value = true;
          this.ruleSetData = {
            rule_set_uids: ruleUid
          };
          this.rule_setList = newVal.rule_set_names || [];
        }
        // this.tableData = newVal.details;
        this.buttonNumberDDL = newVal.constraint.length;
        this.buttonNumberConditions = newVal.condition.length;
        // tableEdit.editAll();
      }
      // immediate: true,
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.config-rules-content {
  .rules-content-base-info {
    /deep/.common-pure-card {
      .ant-card-body {
        .base-info-form {
          .ant-form-item {
            display: flex;
            padding-right: 16px;
            margin: 0 0 12px 0;
            .ant-form-item-label {
              padding-right: 16px;
              align-items: start;
              width: 100px;
              flex-shrink: 0;
              top: 5px;
              > label {
                justify-content: flex-start;
                > span {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #27272a;
                  font-weight: 400;
                }
              }
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              .ant-form-item-children {
                .ant-row-flex-space-around {
                  .backgroundBlock {
                    .topBlock {
                      .text {
                        line-height: 20px;
                      }
                    }
                  }
                }
                .ant-input {
                  width: 700px !important;
                  height: 36px;
                  border: 1px solid #ebebec;
                  margin-bottom: 8px;
                }
                textarea.ant-input {
                  max-width: 700px !important;
                  height: 72px;
                }
                .ant-select {
                  width: 700px !important;
                  margin-bottom: 8px;
                  .ant-select-selection {
                    height: 36px;
                    border: 1px solid #ebebec;
                    .ant-select-selection__rendered {
                      > ul > li {
                        margin-top: 5px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .rules-content-condition {
    &:first-child,
    &:nth-child(2) {
      .common-pure-card {
        margin-bottom: 0;
        border-radius: 16px 16px 0 0;
        border-bottom: 1px solid #ebebec;
      }
    }
    &:nth-child(2) {
      .common-pure-card {
        border-radius: 0;
      }
    }
    &:last-child {
      .common-pure-card {
        border-bottom: none;
        border-radius: 0 0 16px 16px;
      }
    }
    /deep/.common-pure-card {
      .ant-card-body {
        .table-edit {
          /** 修改条件区域样式 */
          &.table-edit-ddl {
            .ant-table-tbody > tr {
              > td {
                &:nth-child(3) {
                  padding-right: 0 !important;
                  .ant-input,
                  .ant-select-selection,
                  .ant-input-number,
                  .ant-input-number-input {
                    border-radius: 0;
                    border-right: none;
                  }
                }
                &:nth-child(4) {
                  padding-right: 12px !important;
                  .ant-input,
                  .ant-select-selection,
                  .ant-input-number,
                  .ant-input-number-input {
                    border-radius: 0 8px 8px 0;
                    // border-left: 0;
                  }
                }
              }
            }
          }
          .ant-table-tbody > tr {
            .ant-select-selection {
              transition: none;
            }
            > td {
              border-bottom: 0 !important;
              &:not(:first-child):not(:last-child) {
                padding: 12px 0;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  height: 50px;
                  box-shadow: 4px 4px 3px 0px #f5f5f5;
                  border-color: #ebebec;
                }
                .ant-select-selection {
                  .ant-select-selection__rendered {
                    line-height: 48px;
                    .ant-select-selection-selected-value {
                      height: 48px;
                    }
                  }
                }
              }
              &:nth-child(2) {
                padding-left: 12px !important;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 8px 0 0 8px;
                  border-right: 0;
                  box-shadow: 0px 0px 6px 2px #e8e8e8;
                }
              }
              // &:nth-child(3) {
              //   .ant-input,
              //   .ant-select-selection,
              //   .ant-input-number,
              //   .ant-input-number-input {
              //     border-radius: 0;
              //   }
              // }
              &:nth-child(3) {
                padding-right: 12px !important;
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  border-radius: 0 8px 8px 0;
                  // border-left: 0;
                }
              }
              .biz-rule-range {
                top: 0;
                > *:not(:last-child) {
                  margin-right: 0;
                }
                .ant-input,
                .ant-select-selection,
                .ant-input-number,
                .ant-input-number-input {
                  box-shadow: 0;
                }
                .ant-select:nth-child(1) {
                  .ant-select-selection {
                    border-radius: 0;
                    border-right: 0;
                  }
                }
                .ant-select:nth-child(2) {
                  .ant-select-selection {
                    border-radius: 0;
                    border-top: 1px solid #e8e8e8;
                    border-bottom: 1px solid #e8e8e8;
                    border-left: 1px solid #f5f5f5;
                    border-right: 1px solid #f5f5f5;
                  }
                }
              }
            }
            &:first-child {
              td:first-child {
                .ant-form-item {
                  visibility: hidden;
                  display: none;
                }
              }
            }
            td:first-child {
              padding-right: 35px;
              background-color: #fff !important;
              .ant-form-item {
                position: relative;
                .ant-form-item-control {
                  position: relative;
                  top: -34px;
                  // border-right: 1px dashed #eee;
                  // &:after {
                  //   content: ' ';
                  //   position: absolute;
                  //   width: 10px;
                  //   height: 0;
                  //   top: -3px;
                  //   right: -10px;
                  //   transform: rotate(-30deg);
                  //   border-bottom: 1px dashed #eee;
                  // }
                  // &:before {
                  //   content: ' ';
                  //   position: absolute;
                  //   width: 10px;
                  //   height: 0;
                  //   border-bottom: 1px dashed #eee;

                  //   bottom: -3px;
                  //   right: -10px;

                  //   transform: rotate(30deg);
                  // }
                }
                .ant-select {
                  width: 75px;
                  .new-style(@color) {
                    .ant-select-selection {
                      border-color: @color;
                      color: @color;
                      box-shadow: none;
                      border-radius: 16px;
                      .ant-select-arrow {
                        color: @color;
                      }
                    }
                  }
                  &.relation-and {
                    .new-style(#4cbb3a);
                  }
                  &.relation-or {
                    .new-style(#F29339);
                  }
                }
                .biz-bracket {
                  position: absolute;
                  right: -36px;
                  top: -15px;
                }
              }
            }
          }
          thead {
            display: none;
          }
          .ant-table-tbody
            > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
            > td {
            background: transparent;
          }
          .ant-table-content td {
            padding: 10px 16px;
          }
          .ant-table-placeholder {
            display: none;
          }
        }
        .ddl-rule-set-form {
          padding: 0 128px 0 32px;
          > .ant-row {
            > .ant-col {
              padding: 0 !important;
              width: 50%;
              .ant-row {
                .ant-form-item-label {
                  > label {
                    justify-content: flex-start;
                  }
                }
                .ant-form-item-control-wrapper {
                  .ant-form-item-children {
                    .ant-select-selection {
                      height: 48px;
                      box-shadow: 4px 4px 3px 0px #f5f5f5;
                      border-color: #ebebec;
                      border-right: none;
                      border-radius: 8px 0 0 8px;
                      .ant-select-selection__rendered {
                        line-height: 46px;
                        // .ant-select-selection__placeholder {
                        //   display: inline-block !important;
                        // }
                      }
                    }
                  }
                }
              }
              &:last-child {
                .ant-row {
                  .ant-form-item-control-wrapper {
                    .ant-form-item-children {
                      .ant-select-selection {
                        border-radius: 0 8px 8px 0;
                        border-right: 1px solid #d9d9d9;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    &[data-row='1'] {
      .btn-add-condition {
        position: absolute;
        bottom: 44px;
      }
      .rules-btn-add-condition {
        position: absolute;
        bottom: 44px;
      }
      /deep/ .table-edit {
        colgroup {
          > col:first-child {
            width: 75px !important;
          }
        }
        .ant-table-tbody {
          > tr {
            &:first-child {
              td:first-child {
                padding: 0;
              }
            }
          }
        }
      }
    }
    /deep/.btn-add-condition,
    /deep/.rules-btn-add-condition {
      border-radius: 16px;
      margin: 10px 16px;
      font-size: 14px;
      .ant-btn {
        height: 28px;
        width: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #4db5f2;
        border-radius: 4px !important;
        transition: none;
        font-size: 14px;
        .anticon {
          line-height: 0;
          color: #4db5f2;
        }

        &:hover {
          background: #4db5f2;
          .anticon {
            color: #fff;
          }
        }
      }
    }
  }
}
.rules-outport-results {
  /deep/.common-pure-card {
    .ant-card-body {
      .ant-form {
        display: flex;
        padding: 0 16px;
        .ant-col {
          padding: 0 !important;
          .ant-form-item {
            display: flex;
            padding-right: 16px;
            align-items: start;
            margin: 0;
            .ant-form-item-label {
              margin-right: 32px;
              flex-shrink: 0;
              .ant-col .ant-form-item-no-colon {
                > span {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #27272a;
                }
              }
            }
            .ant-form-item-control-wrapper {
              width: 100%;
              .ant-form-item-control {
                line-height: 1.5;
                .custom-markdown {
                  .toastui-editor-defaultUI {
                    max-width: 1250px;
                    border: none;
                    position: relative;
                    // .toastui-editor-toolbar {
                    //   border-radius: 8px 8px 0 0;
                    //   background: #e4e4e7;
                    // }
                    .toastui-editor-toolbar {
                      .toastui-editor-defaultUI-toolbar {
                        border: 1px solid #ebebec;
                        border-radius: 8px 8px 0 0;
                        border-bottom: none;
                      }
                    }
                    .toastui-editor-main {
                      border: 1px solid #ebebec;
                      border-radius: 0 0 8px 8px;
                      .toastui-editor {
                        .ProseMirror {
                          height: 100%;
                        }
                      }
                      .toastui-editor-md-preview {
                        .toastui-editor-contents {
                          padding-bottom: 24px;
                        }
                      }
                    }
                    .toastui-editor-mode-switch {
                      border-top: none;
                      position: absolute;
                      top: -32px;
                      right: 0;
                      // padding: 19px 0 0 0;
                      background: transparent;
                      .tab-item {
                        border: none;
                        border-radius: 8px;
                        margin-left: 8px;
                        &:hover {
                          background: #7fc4ed;
                          color: #fff;
                        }
                        &.active {
                          background: #7fc4ed;
                          color: #fff;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          &:last-child {
            .ant-form-item {
              .ant-form-item-label {
                .ant-form-item-no-colon {
                  margin-left: 12px;
                }
              }
            }
          }
          &.suggest {
            .ant-form-item {
              flex-direction: column;
              .ant-col .ant-form-item-no-colon {
                margin-left: 0;
                > span {
                  margin-right: 4px;
                  &::after {
                    content: '(支持MarkDown语法)';
                    color: #a1a1aa;
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
