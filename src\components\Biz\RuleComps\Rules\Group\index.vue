<template>
  <div :class="className">
    <!-- 关系节点 -->
    <div
      :class="['rule-tree-relation', value.children.length <= 1 && 'only-icon']"
    >
      <a-icon
        class="tree-relation-add-icon"
        type="plus-circle"
        @click="onAdd"
      ></a-icon>
      <a-select
        :value="data.relation"
        :style="{ background: '#fff' }"
        :class="[
          'tree-relation-label',
          data.relation == 'AND' ? 'relation-and' : 'relation-or'
        ]"
        @change="handleChange"
        v-if="data.children.length > 1"
      >
        <a-select-option value="AND">AND</a-select-option>
        <a-select-option value="OR">OR</a-select-option>
      </a-select>
    </div>
    <!-- 子节点 -->
    <div
      class="rule-tree-relation-children"
      v-if="data.children && data.children.length > 0"
    >
      <template v-for="(item, index) in data.children">
        <biz-rule-item
          :key="index"
          :value="item"
          v-if="item.role_type == 'item'"
        ></biz-rule-item>
        <Group
          :key="index"
          :value="item"
          v-if="item.role_type == 'group'"
        ></Group>
      </template>
      <!-- 连线 -->
      <SvgLine ref="SvgLine" :list="data.children" />
    </div>
  </div>
</template>

<script>
import Line from './Line';
import ResizeObserver from 'resize-observer-polyfill';

export default {
  name: 'Group',
  inheritAttrs: false,
  components: { SvgLine: Line },
  props: {
    value: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const { value } = this;
    return {
      data: value
    };
  },
  computed: {
    className() {
      const { data } = this;
      return [
        'rule-tree-group',
        data.isLeaf && 'is-leaf-item',
        data.isFirst && 'is-first-item',
        data.isLast && 'is-last-item',
        data.hasChildren && 'has-children',
        data.level && `rule-level-${data.level}`
      ].filter(item => item);
    }
  },
  created() {},
  mounted() {
    let lastHeight = this.$el.offsetHeight;
    this.ro = new ResizeObserver((entries, observer) => {
      const { contentRect } = entries[0];
      const { height } = contentRect || {};
      if (!height) return;
      if (height !== lastHeight) {
        this.$refs.SvgLine && this.$refs.SvgLine.draw();
        lastHeight = height;
      }
    });
    this.ro.observe(this.$el);
  },
  destroyed() {
    if (this.ro) {
      this.ro.unobserve(this.$el);
      this.ro = null;
    }
  },
  methods: {
    handleChange(e) {
      this.data.relation = e;
    },
    onAdd() {
      const arr = _.get(this.data, 'children');
      arr.push({
        role_type: 'item',
        source_code: '',
        index_code: { key: '', label: '' },
        prev_operator: { key: '', label: '' },
        target_operator: { key: '', label: '' },
        target_value: '',
        id: _.uniqueId('init_')
      });
      this.$set(this.data, 'children', arr);
      this.refreshData();
    },
    refreshData() {
      const loop = (item, level) => {
        // 处理新增节点
        item.level = level;
        item.id = _.uniqueId('init_');
        if (!item.children || item.children.length <= 0) {
          item.isLeaf = true;
        }
        if (item.children && item.children.length > 0) {
          item.children.forEach((child, index) => {
            if (
              child.role_type == 'item' &&
              child.children &&
              child.children.length > 0
            ) {
              child.hasChildren = true;
            }
            child._parent = item;
            child.item_order = index;
            loop(child, ++level - index);
          });
        }
      };
      loop(this.data, this.data.level);
    }
  },
  watch: {
    data: {
      handler(newVal) {}
    }
  }
};
</script>

<style lang="less" scoped>
</style>
