import { getGlobalSettings } from '@/api/sqlresolver';
const getAuth = () => {
  return new Promise((resolve, reject) => {
    getGlobalSettings()
      .then((res) => {
        if (CommonUtil.isSuccessCode(res)) {
          const auth = _.get(res, 'data.data') || {};
          resolve(auth);
        } else {
          reject(new Error())
        }
      })
      .catch((e) => {
        console.error(e);
        reject(new Error())
      });
  });
};

export default {
  sqlreview: async () => {
    let res = {};
    try {
      const { flag } = await getAuth();
      res = {
        /* 左侧数据库结构树 */
        database_contextmenu_table_drop: flag, // 右键菜单-删除表
        database_contextmenu_table_truncate: flag // 右键菜单-清空表
      };
    } catch (e) {
      console.log(e);
    }
    return res;
  }
};
