<template>
  <div class="test-rich-editor">
    <RichEditor ref="RichEditor" v-model="showHTML" />
    <!-- <a-button @click="preview">预览</a-button> -->
    <a-divider>预览</a-divider>
    <!-- <div class="rich-editor-preview" v-html="showHTML"></div> -->
    <RichEditorViewer class="rich-editor-preview" v-model="showHTML"></RichEditorViewer>
  </div>
</template>

<script>
import RichEditor from '@/components/RichEditor';
import RichEditorViewer from '@/components/RichEditor/viewer';

export default {
  components: { RichEditor, RichEditorViewer },
  props: {},
  data() {
    return {
      showHTML:
        '<p>测试一下</p><ol><li>hahaha</li><li>dfjdkfjdk</li><li>dfjdkfjkdf</li></ol><table border="0" width="100%" cellpadding="0" cellspacing="0"><tbody><tr><th>1</th><th>2</th><th>3</th><th>4</th><th>5</th></tr><tr><td>sss</td><td>dfdf</td><td>ggg</td><td>hhh</td><td>jjjjjjjjj</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></tbody></table><blockquote><p>引用块测试</p></blockquote><blockquote><p>的辅导辅导辅导费</p></blockquote><hr><p data-we-empty-p="" style="padding-left:2em;"><i><b><u><strike><font color="#46acc8">文字</font></strike></u></b></i></p><p data-we-empty-p="" style=""><i><b><u><strike style="background-color: rgb(249, 150, 59);">三生三世</strike></u></b></i></p><p data-we-empty-p="" style=""><i><b><u><strike style="background-color: rgb(249, 150, 59);"><br></strike></u></b></i></p><pre><code class="Bash"><span class="hljs-keyword">sh</span> <span class="hljs-keyword">ab</span>.<span class="hljs-keyword">sh</span></code><code class="Bash"><span class="hljs-keyword">dfdf</span></code><code class="Bash"><span class="hljs-keyword">fdfdf</span></code><code class="Bash"><span class="hljs-keyword">dfdfdf</span></code></pre>'
    };
  },
  mounted() {
    console.log(this.$refs.RichEditor)
  },
  created() {},
  methods: {
    // preview(e) {
    //   this.showHTML = this.$refs.RichEditor.getData();
    // }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.test-rich-editor {
  .rich-editor-preview {
    min-height: 300px;
    border: 1px solid #d9d9d9;
    padding: 12px;
  }
}
</style>