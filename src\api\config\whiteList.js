import Http from '@/utils/request'

export function removeWhiteList(data = {}) {
  return Http({
    url: `/sqlreview/review/white-list/${data.id}/`,
    method: 'delete',
    data: {}
  });
}

// 删除
export function removeWhiteListTable(data = {}) {
  return Http({
    url: `/sqlreview/review/white-list-table/?ids=${data.ids}`,
    method: 'delete',
    data: {}
  });
}

// 添加
export function addWhiteListTable(data = {}) {
  return Http({
    url: `/sqlreview/review/white-list-table/`,
    method: 'post',
    data
  });
}

export function batchRemove(data = {}) {
  return Http({
    url: `/sqlreview/review/batch_del_white_list/`,
    method: 'post',
    data
  });
}
// 审核
export function checkWhiteList(data = {}) {
  return Http({
    url: `/sqlreview/review/table-info`,
    method: 'post',
    data
  });
}
// 通过不通过按钮
export function pass(data = {}) {
  return Http({
    url: `/sqlreview/review/white-list-table/`,
    method: 'put',
    data
  });
}

// sql通过不通过按钮
export function sqlPass(data = {}) {
  return Http({
    url: `/sqlreview/review/white-list/`,
    method: 'put',
    data
  });
}

// 事前审核详情申请白名单
export function applyForWhite (data = {}) {
  return Http({
    url: `/sqlreview/review/table-detail-info/`,
    method: 'get',
    params: data
  });
}

// sql白名单审核信息
export function sqlWhiteListInfo (data = {}) {
  return Http({
    url: `/sqlreview/review/white_list_info/`,
    method: 'get',
    params: data
  });
}
