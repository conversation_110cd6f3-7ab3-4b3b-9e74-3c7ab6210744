<template>
  <a-drawer
    title="规则导入"
    :width="700"
    :visible="visible"
    wrapClassName="rules-import-drawer"
    @close="onCancel"
  >
    <!-- 内容区域 -->
    <a-spin :spinning="loading">
      <Form
        ref="form"
        class="custom-form"
        v-bind="formParams"
        :formData="formData"
      />
      <a-alert
        message="流程说明：填写规则信息 > 上传 > 导入&校验"
        type="info"
        show-icon
      />
      <div class="step-one">
        <div class="title">
          <span>1、上传规则文件</span>
          <span class="desc">仅支持SQLReview平台导出的json文件</span>
        </div>
        <div class="upload-btn">
          <a-upload
            name="file"
            :multiple="true"
            :before-upload="beforeUpload"
            :fileList="fileList"
            :remove="handleRemove"
          >
            <a-button class="file-import-btn" :disabled="timestamp !== null"
              >点击上传</a-button
            >
          </a-upload>
        </div>
      </div>
      <a-divider />
      <div class="step-two">
        <div class="title">2、导入规则文件</div>
        <div class="content">
          <div class="import-btn">
            <a-button type="primary" @click="onOk" :disabled="timestamp !== null"
              >导入</a-button
            >
          </div>
          <div class="import-info" v-if="timestamp !== null">
            <div class="info-item" v-if="importStatus">
              <div class="info">
                <custom-icon type="lu-icon-success" />
                <span>{{ '文件内容校验通过' }}</span>
              </div>
              <div class="info">
                <custom-icon type="lu-icon-success" />
                <span>{{
                  skipNumber >= 0
                    ? `规则导入完成，成功${succeedNumber}个，冲突跳过${skipNumber}个，失败${failedNumber}个（共${total}个）`
                    : `规则导入完成，成功${succeedNumber}个，冲突覆盖${coverNumber}个，失败${failedNumber}个（共${total}个）`
                }}</span>
              </div>
            </div>
            <div v-else class="info-item">
              <div v-if="importStatus == false">
                <custom-icon type="lu-icon-error" />
                <span>{{ '导入失败' }}</span>
              </div>
            </div>
            <div class="error-info">
              <a @click="downLoad" :disabled="timestamp == null"
                ><a-icon type="download" />下载导入报告</a
              >
            </div>
          </div>
          <Table
            v-bind="tableParams"
            :dataSource="dataSource"
            bordered
            v-if="timestamp !== null"
          >
            <div
              slot="status"
              slot-scope="{ text, record }"
              :class="['status-content', record.type]"
            >
              <span>{{ statusText[record.type] }}</span>
              <span v-if="record.type == 'failed'">{{ '(' + text + ')' }}</span>
            </div>
          </Table>
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script>
import Form from '@/components/Form';
import Table from '@/components/Table';
import config from './config';
import { importRule, downloadErrorReport } from '@/api/config/rule';
export default {
  components: { Form, Table },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      loading: false,
      isEdit: false,
      formParams: {
        layout: 'inline',
        fields: [
          {
            type: 'RadioGroup',
            label: '导入有冲突时',
            key: 'deal_clash',
            props: {
              options: [
                {
                  label: '跳过',
                  value: 'skip'
                },
                {
                  label: '覆盖',
                  value: 'cover'
                }
              ]
            },
            tips:
              '规则冲突通过规则uuid（规则属性、阈值、逻辑关系生成的规则唯一值）进行判断，该uuid不包括规则名称。'
          }
        ]
      },
      formData: {
        deal_clash: 'skip'
      },
      fileList: [],
      tableParams: {
        url: '',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      statusText: {
        succeed: '成功',
        failed: '失败',
        skip: '跳过',
        cover: '覆盖'
      },
      dataSource: [],
      submitData: {},
      timestamp: null,
      coverNumber: 0,
      skipNumber: 0,
      failedNumber: 0,
      succeedNumber: 0,
      total: 0,
      importStatus: undefined
    };
  },
  mounted() {},
  created() {},
  computed: {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.data = data;
    },
    // 上传之前
    beforeUpload(file) {
      const maxSize = 200 * 1024 * 1024; // byte
      this.fileSize = this.fileSize + file.size;
      if (this.fileSize > maxSize) {
        this.$message.error('文件大小错误，文件总大小不超过200MB');
        return;
      }
      if (!/\.(json)$/.test(file.name)) {
        this.$message.error('文件格式错误，文件类型支持.json');
        return;
      }
      if (this.fileList.length > 4) {
        this.$message.warning('最多可上传五个文件');
        return;
      }
      const fileNames = this.fileList.map(item => item.name);
      if (!fileNames.includes(file.name)) {
        this.fileList.push(file);
      } else {
        this.$message.warning('该文件已上传，无需重复上传');
        return;
      }
      return false;
    },
    handleRemove(file) {
      this.fileList = this.fileList.filter(item => item.name !== file.name);
    },
    onOk() {
      this.submitData = new FormData();
      this.fileList.forEach(item => {
        this.submitData.append('file', item);
      });
      const formData = this.$refs.form.getData();
      const timestamp = Date.now();
      this.submitData.append('deal_clash', formData.deal_clash);
      this.submitData.append('timestamp', timestamp);
      this.$showLoading();
      importRule(this.submitData)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            this.timestamp = resData.timestamp;
            this.coverNumber = resData.cover;
            this.skipNumber = resData.skip;
            this.failedNumber = resData.failed;
            this.succeedNumber = resData.succeed;
            this.total = resData.total;
            this.dataSource = resData.results;
            this.importStatus = true;
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.importStatus = false;
            const resData = _.get(res, 'data.data');
            this.timestamp = resData.timestamp;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    downLoad() {
      this.$showLoading();
      downloadErrorReport({ timestamp: this.timestamp })
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.log(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onCancel() {
      this.hide();
    },
    hide() {
      this.visible = false;
      this.importStatus = undefined;
      this.timestamp = null;
      this.fileList = [];
      this.formData = { deal_clash: 'skip' };
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.rules-import-drawer {
  .ant-spin-container {
    position: relative;
    .custom-form {
      /deep/ .ant-form-item {
        margin-bottom: 5.5px;
      }
    }
    .step-one {
      padding-top: 16px;
      .title {
        color: #1f1f1f;
        font-weight: bold;
        .desc {
          color: #8c8c8c;
          font-weight: 400;
        }
      }
      .upload-btn {
        margin: 8px 24px 0;
        /deep/ .ant-upload-list-text {
          .ant-upload-list-item:hover .ant-upload-list-item-info {
            background: #fafafa;
            color: #1677ff;
          }
        }
      }
    }
    .ant-divider-horizontal {
      margin: 16px 0;
    }
    .step-two {
      .title {
        color: #1f1f1f;
        font-weight: bold;
      }
      .content {
        margin: 0 24px;
        .import-btn {
          margin: 8px 0;
        }
        .import-info {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          .info-item {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .info {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              span {
                margin-left: 6px;
              }
            }
          }
          .error-info {
            margin-bottom: 8px;
          }
        }
        .status-content {
          color: #02a7f0;
          &.succeed {
            color: #70b603;
          }
          &.failed {
            color: #d9001b;
          }
        }
      }
    }
  }
}
</style>