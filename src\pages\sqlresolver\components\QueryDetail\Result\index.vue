<template>
  <div class="psc-right-query-detail-result">
    <div class="sql-info">
      <div class="sql-test">
        <LimitLabel mode="ellipsis" :label="info.sql_text || ''"></LimitLabel>
        <span
          class="sql-total-count"
          style="margin-left: 8px;"
          v-if="info.affect_rows != null"
        >【共{{info.affect_rows || 0}}条】</span>
      </div>
      <div
        class="sql-limit-tips"
        v-if="info.affect_rows != null && info.affect_rows > 200"
      >*最多查询200行数据，更多查询数据正在努力开放中</div>
    </div>
    <Table
      ref="table"
      class="table-new-mode-style"
      v-bind="tableParams"
      :scroll="scroll"
      bordered
      v-if="alive"
    >
      <template v-for="item in tableParams.columns || []">
        <div :slot="`title_${item.key}`" :key="item.key" class="title-slot">
          <span>{{item.key.split("_").slice(0, -1).join("_")}}</span>
          <a-tooltip>
            <template slot="title">敏感字段</template>
            <custom-icon
              style="margin-left: 4px"
              type="lu-icon-data-sensitive"
              v-if="item._isSensitive"
            />
          </a-tooltip>
        </div>
      </template>
    </Table>
    <div class="footer"></div>
  </div>
</template>

<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import config from './config';

export default {
  components: { Table, LimitLabel },
  props: {
    info: {
      type: Object,
      default: () => ({})
    },
    resultActiveTab: String,
    resultTab: String,
    resize: Number
  },
  data() {
    this.config = config(this);
    this.dataSource = [];
    return {
      tableParams: this.getTableParams(),
      scroll: {
        x: 'max-content',
        y: 'max-content'
      },
      alive: true,
      sensitiveFields: []
    };
  },
  computed: {},
  mounted() {
    this.setPagination();
    this.setScroll();
  },
  methods: {
    getTableParams(info) {
      const res = {
        rowKey: 'id',
        size: 'small',
        columns: [],
        dataSource: [],
        pagination: {
          size: 'small',
          hideOnSinglePage: true,
          showTotal: (total, range) => {
            return ``;
          },
          pageSize: 20,
          // showSizeChanger: false,
          showQuickJumper: false
        }
      };
      const _info = info || this.info;
      const {
        column_names: columnNames = [],
        data = [],
        is_sensitive: sensitiveFields = []
      } = _info;

      this.sensitiveFields = [...new Set(sensitiveFields)];
      // 赋值data
      const _dataSource = (data || []).map(item => {
        let dataItem = {};
        columnNames.map((columnName, index) => {
          const key = `${columnName}_${index}`;
          dataItem[key] = item[index];
        });
        return dataItem;
      });
      res.dataSource = _dataSource;
      this.dataSource = _dataSource;

      // 统计宽度
      const columnMaxStr = {};
      columnNames.map((columnName, index) => {
        columnMaxStr[columnName] = columnName;
        _dataSource.forEach(dataItem => {
          // 统计每一列最长str
          const key = `${columnName}_${index}`;
          columnMaxStr[columnName] = this.max([
            columnName,
            dataItem[key],
            columnMaxStr[columnName]
          ]);
        });
      });
      // console.log(columnMaxStr, 'columnMaxStr');

      // 赋值columns
      res.columns = [
        {
          title: '',
          dataIndex: '_index',
          key: '_index',
          width: 50,
          customRender: (text, record, index) => {
            const { table } = this.$refs;
            const { pageSize = 0, pageNum = 1 } = table || {};
            return index + 1 + (pageNum - 1) * pageSize;
          },
          fixed: true
        },
        ...columnNames.map((columnName, index) => {
          const isSensitive = this.sensitiveFields.includes(columnName);
          const extraWidth = isSensitive ? 24 : 0;
          const columnWidth =
            this.getTextWidth(columnMaxStr[columnName]) + 48 + extraWidth;
          const limitWidth = columnWidth > 300 ? 300 : columnWidth;
          const key = `${columnName}_${index}`;
          return {
            // title: columnName,
            // slots: { title: `title_${columnName}` },
            // key: columnName,
            // dataIndex: columnName,
            slots: { title: `title_${key}` },
            key: key,
            dataIndex: key,
            customRender: (text, record, index) => {
              return columnWidth > limitWidth ? (
                <LimitLabel
                  class="td-content"
                  style={{ width: limitWidth - 16 - 1 + 'px' }}
                  label={text + ''}
                  mode="ellipsis"
                ></LimitLabel>
              ) : (
                text
              );
            },
            width: limitWidth,
            onFilter: (value, record) =>
              CommonUtil.tableFilters.includes(value, record, key),
            scopedSlots: { filterDropdown: 'defaultFilterDropdown' },
            _isSensitive: isSensitive
          };
        })
      ];
      return res;
    },
    max(arr) {
      let max = '';
      arr
        .filter(item => item != null)
        .forEach(item => {
          if ((item + '').length > max.length) {
            max = item + '';
          }
        });
      return max;
    },
    getTextWidth(str) {
      let width = 0;
      var a = document.createElement('span');
      a.innerText = str;
      a.style.whiteSpace = 'nowrap';
      a.style.display = 'inline-block';
      a.style.wordBreak = 'normal';
      a.style.fontSize = '12px';
      document.body.appendChild(a);
      // console.log(getComputedStyle(a).width, a.offsetWidth, 'hahahah');
      width = a.offsetWidth;
      document.body.removeChild(a);
      return width;
    },
    setScroll() {
      const sqlInfo = this.$el.querySelector('.sql-info');
      const thead = this.$el.querySelector('.ant-table-thead');
      const pagination = this.$el.querySelector('.ant-pagination');
      const span =
        12 +
        ((sqlInfo ? sqlInfo.offsetHeight : 0) + 8) +
        (thead ? thead.offsetHeight : 0) +
        ((pagination ? pagination.offsetHeight : 0) + 16);
      // console.log(span, 'span');
      const y = this.$el.offsetHeight - span;
      // if (
      //   this.resultActiveTab === this.resultTab &&
      //   this.y !== this.tableHeight
      // ) {
      //   this.scroll = {
      //     x: 800,
      //     y: y < 0 ? 0 : y
      //   };
      // }
      // if (this.dataSource.length <= 0) {
      //   this.scroll = {};
      // }
      // this.tableHeight = y;
      const tableBody = this.$el.querySelector(
        '.ant-table-scroll .ant-table-body'
      );
      const tableFixedBody = this.$el.querySelector(
        '.ant-table-fixed-left .ant-table-body-inner'
      );
      tableBody && (tableBody.style.maxHeight = y + 'px');
      tableFixedBody && (tableFixedBody.style.maxHeight = y + 'px');
      if (pagination) {
        pagination.style.opacity = y <= 0 ? 0 : 1;
      }
    },
    setPagination() {
      const oldPagination = this.$el.querySelector('.footer .ant-pagination');
      const footer = this.$el.querySelector('.footer');
      if (oldPagination) {
        footer.removeChild(oldPagination);
      }
      const pagination = this.$el.querySelector(
        '.custom-table .ant-pagination'
      );
      if (pagination) {
        footer.appendChild(pagination);
      }
    }
  },
  watch: {
    info(newVal = {}) {
      this.alive = false;
      this.$nextTick(() => {
        // 重新加载table
        this.alive = true;
        this.tableParams = this.getTableParams(newVal);
        this.$nextTick(() => {
          // 重新设置pagination
          this.setPagination();
          this.setScroll();
        });
      });
    },
    resize() {
      this.setScroll();
    }
  }
};
</script>

<style lang="less" scoped>
.psc-right-query-detail-result {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  padding: 12px 0 0 0;
  overflow: hidden;
  .sql-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 0 8px;
    z-index: 1;
    background: #ffffff;
    .sql-test {
      display: flex;
    }

    .limit-label {
      max-width: 300px;
      display: inline-block;
    }

    .sql-limit-tips {
      font-size: 12px;
      color: @font-color-weak;
      // margin-top: 4px;
      // position: absolute;
      // right: 0;
      // bottom: 12px;
    }
  }

  /deep/ .table-new-mode-style {
    // display: inline-block;
    // max-width: 100%;
    z-index: 1;
    background: #ffffff;
    margin-bottom: 0;
    th,
    td {
      font-size: 12px !important;
      white-space: nowrap;
      // word-break: normal;
    }
    td {
      // max-width: 200px;
      // overflow: hidden;
      // text-overflow: ellipsis;
      .td-content {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    th {
      position: sticky;
      top: 0;
      background-color: #f5f5f5 !important;
    }
    .ant-table-scroll {
      display: inline-block;
      max-width: 100%;
      .ant-table-header.ant-table-hide-scrollbar {
        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
      }
      .ant-table-fixed-columns-in-body {
        border-left: none;
        border-right: none;
      }
    }

    .ant-table-bordered.ant-table-empty {
      .ant-table-placeholder {
        border: none !important;
        margin-top: 12px;
        background: #ffffff;
      }
      .ant-table-fixed-left {
        display: none;
      }
      .ant-table-body {
        height: 0;
        min-height: 0;
      }
    }
    .ant-table-content {
      border-right: none !important;
    }
  }

  /deep/ .footer {
    position: absolute;
    left: 0;
    bottom: 0;
    .ant-pagination {
      margin: 8px 0;
    }
  }
  /deep/ .title-slot {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
  }
}
</style>
