export default function (ctx) {
  const cardColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      scopedSlots: { customRender: 'cardTable' }
    }
  ];
  const columns = [
    {
      title: '规则集名称',
      dataIndex: 'name',
      scopedSlots: { customRender: 'name' },
      key: 'name',
      width: 150
    },
    {
      title: '数据库类型',
      dataIndex: 'db_type',
      key: 'db_type',
      scopedSlots: { customRender: 'db_type' },
      width: 100
    },
    // {
    //   title: '规则集类型',
    //   dataIndex: 'rule_set_type',
    //   key: 'rule_set_type',
    //   scopedSlots: { customRender: 'rule_set_type' },
    //   width: 100
    // },
    {
      title: '规则数量',
      dataIndex: 'count',
      key: 'count',
      scopedSlots: { customRender: 'dataSource' },
      width: 100
    },
    {
      title: '规则状态',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'ruleStatus' },
      width: 100
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      width: 100
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = [
    // {
    //   type: 'RadioGroup',
    //   label: '规则集类型',
    //   key: 'rule_set_type',
    //   mainSearch: true,
    //   props: {
    //     mode: 'buttonAlone',
    //     options: [
    //       {
    //         label: '全部',
    //         value: 'ALL'
    //       },
    //       {
    //         label: 'DML',
    //         value: 'DML'
    //       },
    //       {
    //         label: 'DDL',
    //         value: 'DDL'
    //       }
    //     ],
    //     defaultValue: 'ALL'
    //   }
    // },
    // {
    //   type: 'Select',
    //   label: '规则集类型',
    //   key: 'rule_set_type',
    //   mainSearch: true,
    //   props: {
    //     placeholder: '请选择规则集类型',
    //     options: [
    //       {
    //         label: 'DDL',
    //         value: 'DDL'
    //       },
    //       {
    //         label: 'DML',
    //         value: 'DML'
    //       }
    //     ]
    //   }
    // },
    {
      type: 'Input',
      label: '规则集名称',
      key: 'name'
    },
    {
      type: 'Select',
      label: '规则状态',
      key: 'status',
      props: {
        options: [
          {
            label: '启用',
            value: '1'
          },
          {
            label: '禁用',
            value: '0'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '数据库类型',
      key: 'db_type',
      props: {
        url: '/sqlreview/project/rule_support_db'
      }
    }
  ];
  return {
    columns,
    cardColumns,
    searchFields: fields
  };
}
