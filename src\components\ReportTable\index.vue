<!--
 * @Descripttion: 报表表格
 * @Version: 1.0.0
 * @Author: uoeye
 * @Date: 2020-12-16 11:05:58
-->

<template>
  <div class="report-table">
    <a-table
      rowKey="id"
      size="middle"
      :bordered="true"
      :columns="getColumns()"
      :loading="loading"
      :locale="{ emptyText: '暂无数据' }"
      :pagination="tablePagination"
      :scroll="getScroll()"
      :data-source="getDataSource()">
    >
      <template slot="default" slot-scope="text, row, rowNumber">
        <span v-if="isOnlyText(text)">{{getValue(text, text)}}</span>
        <span v-else-if="isObjectText(text)">{{getValue(text.value, text)}}</span>
        <span v-else-if="isActionView(text)" @click="showAction(text)" class="action-view">
          {{text.value}}
        </span>
        <span v-else><div>
          <component :is="getCellComponentName(text)" :type="text.type"  :columnKey="text.columnKey" :value="text.value" :search="text.data || {}" :action="showAction" @change="onChangeCell" :rowEdit="editRow && (editRowNumber === rowNumber)" :rowData="row"></component></div>
        </span>
      </template>
      <template slot="action" slot-scope="text, row, rowNumber">
        <span v-if="editRow && editRowNumber === rowNumber">
          <a @click="onSaveRow(row, rowNumber)" >保存</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定取消编辑?" @confirm="() => onCancelRow(row)">
            <a>取消</a>
          </a-popconfirm>
        </span>
        <span v-if="!editRow || editRowNumber !== rowNumber">
          <a @click="onClickRow(row, rowNumber)" :disabled="editRow && (editRowNumber !== rowNumber)">编辑</a>
        </span>
        <span v-if="rowDelete">
          <a-popconfirm title="确定删除该行?" @confirm="() => onRemoveRow(row)">
            <a-divider type="vertical" />
            <a style="color:#e26148">删除</a>
          </a-popconfirm>
        </span>
      </template>
    </a-table>
    <a-modal
      wrapClassName = "report-table-wraper"
      :title = "modalComponentProps.title || '详细信息'"
      :width = "modalComponentProps.width || 900"
      :visible = "modalVisible"
      @cancel = "() => this.modalVisible = false"
    >
      <component :is="modalComponentName" :reflush="modalReflush" v-bind="modalComponentProps"></component>
    </a-modal>
  </div>
</template>
<script>
import ReportCode from './plugins/code'
import ReportTruth from './plugins/truth'
import ReportCelledit from './plugins/celledit'

export default {
  name: 'report-table',
  components: { ReportCode, ReportTruth, ReportCelledit },
  props: {
    pageSize: {
      type: Number,
      required: false,
      default: () => 10
    },
    pagination: Object,
    rowEdit: {
      type: Boolean,
      required: false,
      defualt: () => false
    },
    rowDelete: {
      type: Boolean,
      required: false,
      defualt: () => false
    },
    // 表格是否支持滚动，这里传入表格的宽度就标识开启滚动
    scroll: {
      type: Number,
      required: false
    },
    // 默认的columns 
    columns: {
      type: Array,
      required: false,
      default: () => []
    },
    columnFilter: {
      type: Function,
      required: false
    },
    dataSource: {
      type: [Array, Function],
      requied: true,
      default: () => []
    },
    search: {
      type: Object,
      requied: false,
      default: function () {
        return {}
      }
    },
    reportDict: {
      type: Object,
      required: false,
      default: function () {
        return {}
      }
    },
    reportTypeDict: {
      type: Object,
      required: false,
      default: function () {
        return {}
      }
    },
    reportTitleKey: {
      type: String,
      requied: false,
      default: () => 'report_title'
    },
    reportValueKey: {
      type: String,
      required: false,
      default: () => 'report_value'
    },
    reportTotalKey: {
      type: String,
      required: false,
      default: () => 'report_total'
    },
    reportActionMap: {
      type: Object,
      required: false,
      default: function () {
        return {}
      }
    },
    reflush: {
      type: Number,
      required: false,
      default: () => 0
    }

  },
  watch: {
    scroll (newValue) {
      console.log(newValue, 333)
      this.scrollWidth = newValue
    },
    reflush () {
      this.editRowData = {}
      this.reflushDataSource()
    }
  },
  data (vm) {
    return {
      currentPage: 1,
      modalReflush: 0,
      total: 0,
      loading: false,
      editRowData: {},
      scrollWidth: vm.scroll,
      current: 1,
      reportTitle: vm.columns,
      reportValue: [],
      editRow: false,
      editRowNumber: 0,
      local: false,
      modalComponentName: '',
      modalComponentProps: {},
      modalVisible: false
    }
  },
  computed: {
    tablePagination () {
      return (
        this.total > this.pageSize &&
        Object.assign(
          {
            showSizeChanger: false,
            pageSize: this.pageSize,
            current: this.currentPage,
            total: this.total,
            onChange: (page, pageSize) => {
              this.onPageChange(page, pageSize)
            }
          },
          this.pagination || {}
        )
      )
    }
  },
  mounted () {
    // 初始化数据
    if (Array.isArray(this.dataSource)) {
      this.local = true
      this.reportValue = this.dataSource
    } else {
      this.reflushDataSource()
    }
  },
  methods: {
    getScroll () {
      console.log(this.scrollWidth)
      if (this.scrollWidth) {
        return {
          x: this.scrollWidth
        }
      }
    },
    onSaveRow (row) {
      this.$emit('change', {
        type: 'save',
        updateData: this.editRowData,
        updateRowNumber: this.editRowNumber
      })
      this.editRow = false
    },
    onCancelRow (row) {
      this.editRow = false
    },
    onRemoveRow (row) {
      this.$emit('change', {
        type: 'remove',
        updateData: row
      })
      this.editRow = false
    },
    onChangeCell (row) {
      if (row && row.columnKey) {
        this.editRowData[row.columnKey] = row.value
      }
    },
    onClickRow (row, rowNumber) {
      this.editRow = true
      this.editRowData = {}
      this.editRowNumber = rowNumber
    },
    isOnlyText (value) {
      return typeof value !== 'object'
    },
    isObjectText (value) {
      return typeof value.type === 'undefined'
    },
    isActionView (value) {
      /**
       * action:actioName_xxxx
       * component:xxxx
       */
      if (value.type && !value.type.match(/^component:/)) {
        return true
      }
    },
    showAction (value) {
      let { type } = value
      let typeList = type.split('_')
      let componentName = typeList.length > 1 ? typeList[0] : 'table'
      let componentType = typeList.length > 1 ? typeList[1] : type

      this.modalVisible = true
      this.modalComponentName = 'report-' + componentName.replace(/([A-Z])/, (_, $1) => '-' + $1.toLowerCase())
      this.modalComponentProps = Object.assign({
        reportActionMap: this.reportActionMap,
        search: value.data || {}
      }, this.reportActionMap[componentType])
      this.modalReflush = (new Date()).getTime()
    },
    getValue (text, coloumValue) {
      if (coloumValue) {
        let rowKey = coloumValue.key
        let dict = this.reportDict[rowKey]

        if (dict) {
          return dict[text]
        }
        return typeof text === 'undefined' ? '-' : text
      }
      return '-'
    },
    getCellComponentName (text) {
      let { type } = text
      // component:asset_code_dddd
      let trueType = type.split('_')
      let cellType = trueType.shift()
      return cellType.replace(/component:/, 'report-')
    },
    onPageChange (page, pageSize) {
      this.currentPage = page
      this.pageSize = pageSize

      if (!this.local) {
        this.reflushDataSource()
      }
    },
    reflushDataSource () {
      let dataSource = this.dataSource(this.currentPage, this.pageSize, this.search)
      if (dataSource) {
        if (dataSource.then) {
          dataSource.then((value) => {
            this.resetDataSource(value)
          })
        } else {
          this.resetDataSource(dataSource)
        }
      }
    },
    resetDataSource (value) {
      if (Array.isArray(value[this.reportTitleKey])) {
        this.reportTitle = (value[this.reportTitleKey] || []).filter(item => item.visible != false)
      }
      if (value[this.reportTotalKey]) {
        this.total = value[this.reportTotalKey]
      }
      if (Array.isArray(value[this.reportValueKey])) {
        this.reportValue = value[this.reportValueKey]
      }
    },
    getDataSource () {
      let data
      if (this.local) {
        let begin = (this.currentPage - 1) * this.pageSize
        data = this.reportValue.slice(begin, begin + this.pageSize)
      } else {
        data = this.reportValue
      }

      return data.map((row, index) => {
        for (let key in row) {
          let item = row[key]
          if (key !== 'id') {
            if (typeof item !== 'object') {
              row[key] = {
                value: item
              }
            }
            // 绑定key
            // row[key].key = key
            // 自定义key
            if (this.reportTypeDict[key]) {
              row[key].type = this.reportTypeDict[key]
            }
          }
        }
        if (!row.id) {
          row.id = index
        }
        return row
      })
    },
    getColumns (report) {
      let columns = report || this.reportTitle
      let rowspanCache = {}

      columns = columns.map((column, rowIndex) => {
        let rowspan = column.rowspan || column.rowSpan
        let key = column.dataIndex || column.key

        let columnItem = {
          dataIndex: key,
          key: column.key,
          title: column.title,
          fixed: column.fixed,
          children: column.children ? this.getColumns(column.children) : undefined,
          scopedSlots: {
            customRender: 'default'
          },
          customCell: (record, index) => {
            if (rowspan) {
              if (!rowspanCache[key]) {
                rowspanCache[key] = 0
              }
              if (++rowspanCache[key] % rowspan === 1) {
                return {
                  attrs: {
                    rowspan: rowspan
                  }
                }
              } else {
                return {
                  attrs: {
                    rowspan: 0
                  }
                }
              }
            }
          }
        }
        if (this.columnFilter) {
          this.columnFilter(columnItem, rowIndex, columns.length)
        }
        return columnItem
      })
      if (this.rowEdit) {
        return columns.concat(this.getActionColumns())
      }
      return columns
    },
    getActionColumns () {
      return [{
        key: 'action_edit',
        title: '操作',
        scopedSlots: {
          customRender: 'action'
        }
      }]
    }
  }
}
</script>
<style lang="less">
.report-table {
  .action-view {
    color: #1890ff;
    cursor:pointer;
  }
}
.report-table-wraper {
  .ant-modal-footer {
    display:none;
  }
  .ant-table-wrapper {
    padding: 0;
  }
}
td[rowspan='0'] {
  display:none;
}
</style>
