import Bracket from '@/components/Biz/Bracket';
export default function (ctx) {
  const dbType = window.localStorage.getItem('db_type') || '';
  const baseInfo = [
    {
      type: 'RadioGroup',
      label: '规则类型',
      key: 'rule_type',
      props: {
        mode: 'tips',
        class: 'inline',
        options: ['IMPALA', 'HIVE'].includes(dbType)
          ? [
            {
              label: 'DML规则',
              value: 'DML'
            }
          ]
          : [
            {
              label: 'DML规则',
              value: 'DML'
            },
            {
              label: 'DDL规则',
              value: 'DDL'
            }
          ],
        disabled: ctx.type !== 'add'
      },
      listeners: {
        change: (value) => {
          const baseInfo = ctx.$refs.baseInfo;
          baseInfo.saving({
            rule_type: value,
            db_type: null
          });
          if (value === 'DDL') {
            ctx.$emit('change', true);
            ctx.tableDataConditions = [{ category: '', property: '' }];
          } else {
            ctx.$emit('change', false);
            ctx.tableDataConditions = [];
          }
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    (formData) => {
      return {
        type: 'Select',
        label: '数据库类型',
        key: 'db_type',
        hideComponent: true,
        slots: [{ key: 'db_type' }]
      };
    },
    (formData = {}) => {
      return {
        type: 'Select',
        label: '模式类型',
        key: 'ob_mode',
        visible: formData.db_type == 'OCEANBASE' || formData.db_type == 'TDSQL',
        props: {
          options:
            formData.db_type == 'OCEANBASE'
              ? [
                {
                  label: 'OB_MYSQL',
                  value: 'OB_MYSQL'
                },
                {
                  label: 'OB_ORACLE',
                  value: 'OB_ORACLE'
                }
              ]
              : [
                {
                  label: 'TD_MYSQL',
                  value: 'TD_MYSQL'
                },
                {
                  label: 'TD_PGSQL',
                  value: 'TD_PGSQL'
                }
              ]
        },
        listeners: {
          change: (value) => {
            const { baseInfo } = ctx.$refs;
            baseInfo.saving({
              ob_mode: value,
              rule_set_uids: null
            });
            ctx.$set(ctx, 'dbType', value);
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      };
    },
    (formData = {}) => {
      return {
        type: 'Select',
        label: '加入规则集',
        key: 'rule_set_uids',
        props: {
          // mode: 'tips',
          // class: 'inline',
          // options: [...ctx.rulesOptions]
          mode: 'multiple',
          url: '/sqlreview/project/rule_set_list',
          reqParams: {
            db_type: formData.ob_mode || formData.db_type
          }
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      };
    },
    {
      type: 'Input',
      label: '规则名称',
      key: 'name',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Textarea',
      label: '规则描述',
      key: 'desc',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    }
  ];
  const outportResults = [
    {
      type: 'RadioGroup',
      label: '风险类型',
      key: 'level',
      props: {
        mode: 'icon',
        class: 'inline',
        options: [
          {
            label: '高风险',
            value: 0,
            icon: 'lu-icon-alarm',
            tips: '(审核-不通过)',
            style: { fontSize: '16px', color: '#E71D36' }
          },
          {
            label: '低风险',
            value: 1,
            icon: 'lu-icon-alarm',
            tips: '(审核-通过)',
            style: { fontSize: '16px', color: '#F29339' }
          }
        ]
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Markdown',
      label: '优化建议',
      key: 'suggest',
      className: 'suggest',
      props: {}
    }
  ];
  const baseInfoConditions = [
    {
      type: 'Select',
      label: '规则分类',
      key: 'category',
      props: {
        url: '/sqlreview/project/rule_category_list'
      },
      listeners: {
        change: (value) => {
          const baseInfoConditions = ctx.$refs.baseInfoConditions;
          baseInfoConditions.saving({
            category: value,
            property: null
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    (formData = {}) => {
      return {
        type: 'Select',
        label: '目标属性',
        key: 'property',
        props: {
          url: '/sqlreview/project/rule_target_property',
          reqParams: {
            category: formData && formData.category
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      };
    }
  ];
  const condition = {
    columnsDDL: [
      {
        dataIndex: 'relation',
        key: 'relation',
        width: 120,
        scopedSlots: { customRender: 'relation' }
      },
      {
        dataIndex: 'constraint_key',
        key: 'constraint_key',
        width: '30%',
        scopedSlots: { customRender: 'constraint_key' }
      },
      {
        dataIndex: 'method',
        key: 'method',
        width: '30%',
        scopedSlots: { customRender: 'method' }
      },
      {
        key: 'constraint_value',
        dataIndex: 'constraint_value',
        // width: 300,
        scopedSlots: { customRender: 'constraint_value' }
      },
      {
        key: 'action',
        width: 100,
        scopedSlots: { customRender: 'action' }
      }
    ],
    columnsConditions: [
      {
        dataIndex: 'relation',
        key: 'relation',
        width: 120,
        scopedSlots: { customRender: 'relation' }
      },
      {
        dataIndex: 'method_id',
        key: 'method_id',
        width: '40%',
        scopedSlots: { customRender: 'method_id' }
      },
      {
        key: 'value',
        dataIndex: 'value',
        // width: 300,
        scopedSlots: { customRender: 'value' }
      },
      {
        key: 'action',
        width: 100,
        scopedSlots: { customRender: 'action' }
      }
    ],
    editConfigDDL: (params = {}) => {
      return {
        relation: (row, record = {}, tableEdit, colVal) => {
          let options = [
            {
              label: '空置',
              value: ''
            },
            {
              label: 'AND',
              value: 'and'
            },
            {
              label: 'OR',
              value: 'or'
            }
          ];
          if (row > 0) {
            options = options.slice(1);
          }
          return {
            type: 'Select',
            props: {
              options,
              allowClear: false,
              disabled: row == 0,
              class: `relation-${colVal}`
            },
            initialValue: row == 0 ? '' : 'and',
            extra: (h) => {
              return (
                <Bracket color={colVal === 'and' ? '#d0eecc' : '#fce3cb'} />
              );
            }
            // rules: [
            //   { required: true, message: '该项为必填项' }
            // ]
          };
        },
        constraint_key: (row, record = {}) => {
          return {
            type: 'Select',
            props: {
              url: '/sqlreview/project/rule_constraint_list',
              reqParams: {
                category: ctx.baseInfoDataConditions.category
              }
            },
            rules: [{ required: true, message: '该项为必填项' }],
            listeners: {
              change: (value) => {
                ctx.$refs.tableEditDDL.saving({
                  id: record.id,
                  constraint_key: value,
                  method: null,
                  constraint_value: null
                });
              }
            }
          };
        },
        method: (row, record = {}) => {
          return {
            type: 'Select',
            props: {
              options: [
                { label: '相等', value: 'equal' },
                { label: '包含于', value: 'contains' },
                { label: '开始于', value: 'startswith' },
                { label: '结束于', value: 'endswith' }
              ]
            },
            rules: [{ required: true, message: '该项为必填项' }],
            listeners: {
              change: (value) => {
                ctx.$refs.tableEditDDL.saving({
                  id: record.id,
                  method: value,
                  constraint_value: null
                });
              }
            }
          };
        },
        constraint_value: (row, record = {}) => {
          let type = '';
          let props = {};
          const constraintKey = record.constraint_key;
          if (
            constraintKey == 'is_nullable' ||
            constraintKey == 'is_primary_col'
          ) {
            type = 'Select';
            props = {
              options: [
                {
                  label: '是',
                  value: 'true'
                },
                {
                  label: '否',
                  value: 'false'
                }
              ]
            };
          } else {
            type = 'Input';
          }
          return {
            type,
            props: {
              ...props
            },
            rules: [{ required: true, message: '该项为必填项' }]
          };
        }
      };
    },
    editConfigConditions: (params = {}) => {
      return {
        relation: (row, record = {}, tableEdit, colVal) => {
          let options = [
            {
              label: '空置',
              value: ''
            },
            {
              label: 'AND',
              value: 'and'
            },
            {
              label: 'OR',
              value: 'or'
            }
          ];
          if (row > 0) {
            options = options.slice(1);
          }
          return {
            type: 'Select',
            props: {
              options,
              allowClear: false,
              disabled: row == 0,
              class: `relation-${colVal}`
            },
            initialValue: row == 0 ? '' : 'and',
            extra: (h) => {
              return (
                <Bracket color={colVal === 'and' ? '#d0eecc' : '#fce3cb'} />
              );
            }
            // rules: [
            //   { required: true, message: '该项为必填项' }
            // ]
          };
        },
        method_id: (row, record = {}) => {
          return {
            type: 'Select',
            props: {
              url: '/sqlreview/project/rule_method_list',
              loaded(data) {
                ctx.dataSourceOption = data;
              }
            },
            listeners: {
              change: (value) => {
                let type = '';
                ctx.dataSourceOption.forEach((item) => {
                  if (item.value == value) {
                    type = item.input_type;
                  }
                });
                ctx.$refs.tableEditConditions.saving({
                  id: record.id,
                  method_id: value,
                  input_type: type,
                  value: null
                });
              }
            },
            rules: [{ required: true, message: '该项为必填项' }]
          };
        },
        value: (row, record = {}) => {
          let type = '';
          let props = {};
          let rules = [{ required: true, message: '该项为必填项' }];
          // const methodId = record.method_id;
          const inputType = record.input_type;
          if (inputType == 'select') {
            type = 'Select';
            props = {
              options: [
                {
                  label: '是',
                  value: 'true'
                },
                {
                  label: '否',
                  value: 'false'
                }
              ]
            };
          } else if (inputType == 'inputNumRange') {
            type = 'InputNumRange';
            rules = [
              {
                validator: function (rule, value, callback) {
                  if (value) {
                    if (value[0] == null || value[1] == null) {
                      callback(new Error('有输入项为空'));
                    }
                    if (value[0] >= value[1]) {
                      callback(new Error('最小值不能大于等于最大值'));
                    }
                    callback();
                  } else {
                    callback(new Error('有输入项为空'));
                  }
                }
              }
            ];
          } else if (inputType == 'input') {
            type = 'Input';
          }
          return {
            type,
            props: {
              ...props
            },
            rules: rules
          };
        }
      };
    }
  };
  return {
    baseInfo,
    outportResults,
    condition,
    baseInfoConditions
  };
}
