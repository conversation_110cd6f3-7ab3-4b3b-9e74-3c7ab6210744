export default function (ctx) {
  const columns = [
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 200
    },
    {
      title: '发起人',
      dataIndex: 'created_by',
      key: 'created_by',
      width: 200
    },
    {
      title: '项目',
      dataIndex: 'project',
      key: 'project',
      width: 200
    },
    {
      title: '版本',
      dataIndex: 'review_point',
      key: 'review_point',
      width: 200
    },
    {
      title: '扫描内容',
      dataIndex: 'review_content',
      key: 'review_content',
      width: 200
    },
    {
      title: '函数数量',
      dataIndex: 'review_count',
      key: 'review_count',
      scopedSlots: { customRender: 'review_count' },
      width: 150
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'status' },
      width: 150
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 80,
      fixed: 'right'
    }
  ]

  const searchFields = [
    {
      type: 'RangePicker',
      label: '发起时间',
      key: 'created_at',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    },
    {
      type: 'Input',
      label: '发起人',
      key: 'created_by'
    },
    {
      type: 'Input',
      label: '项目名称',
      key: 'project',
      mainSearch: true,
      props: {
        placeholder: '请输入项目名称'
      }
    },
    {
      type: 'Input',
      label: '版本',
      key: 'review_point'
    },
    {
      type: 'Input',
      label: '扫描内容',
      key: 'review_content'
    },
    {
      type: 'Select',
      label: '状态',
      key: 'status',
      props: {
        options: [
          {
            // label: '进行中',
            label: '队列中',
            value: '0'
          },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '未通过',
            value: '-1'
          },
          {
            label: '失败',
            value: '9'
          },
          {
            label: '完成',
            value: '2'
          },
          {
            label: '拉取代码中',
            value: '3'
          },
          {
            label: '代码解析中',
            value: '4'
          },
          {
            label: '代码审核中',
            value: '5'
          }
        ]
      }
    }
  ]
  return {
    columns, searchFields
  }
}