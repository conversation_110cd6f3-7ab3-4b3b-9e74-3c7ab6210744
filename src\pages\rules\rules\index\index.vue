<template>
  <div class="rules-content">
    <iframe
      v-if="url"
      style="width: 100%; height: 836px"
      frameborder="0"
      :src="url"
    ></iframe>
    <template v-else>
      <SearchArea
        v-bind="searchParams || {}"
        @reset="reset"
        @search="search"
        ref="search"
        :searchData="searchData || {}"
      ></SearchArea>
      <div class="table-header">
        <div class="title">规则列表</div>
        <div class="button-area">
          <a-button
            @click="bacthExport"
            class="highlight"
            v-if="$permission.rule('export')"
            >批量导出</a-button
          >
          <a-button
            @click="bacthImport"
            class="highlight"
            v-if="$permission.rule('import')"
            >批量导入</a-button
          >
          <a-button
            @click="addRules"
            type="primary"
            v-if="$permission.rule('add')"
            >新增</a-button
          >
        </div>
      </div>
      <!-- 表格 -->
      <Table
        ref="table"
        v-bind="tableParams"
        @reset="reset"
        :rowClassName="getRowClassName"
        class="new-view-table"
      >
        <DateFormat
          slot="updated_at"
          slot-scope="{ text }"
          :text="text"
          :limit="20"
        />
        <template v-slot:db_type="{ text }">
          <DbImg :value="text" :UseIconOnly="true" />
        </template>
        <LimitLabel
          slot="name"
          slot-scope="{ text }"
          :label="text"
          :limit="18"
        ></LimitLabel>
        <LimitLabel
          slot="desc"
          slot-scope="{ text }"
          :label="text"
          :limit="18"
        ></LimitLabel>
        <LimitTags
          v-if="record.rule_set_names"
          slot="rule_set_name"
          slot-scope="{ record }"
          :tags="
            (record.rule_set_names || []).map((item) => ({
              label: item
            }))
          "
          :limit="1"
        ></LimitTags>
        <span slot="rule_result" slot-scope="{ text }">
          <span v-if="text != null">
            <custom-icon
              type="lu-icon-alarm"
              :style="{
                color: text == 1 ? '#f6b475' : 'rgb(231, 29, 54)',
                'font-size': '16px'
              }"
            />
            <span>{{ text == 0 ? '高风险' : '低风险' }}</span>
          </span>
          <span v-else>--</span>
        </span>
        <span slot="rule_category" slot-scope="{ text }">
          <span :class="`${text}-category`">{{ text }}</span>
        </span>
        <template slot="updated_by" slot-scope="{ record, text }">
          <a-tooltip v-if="record.ch_update" placement="topLeft">
            <template slot="title">
              <span>{{ text }}</span>
            </template>
            <div class="des">
              <a-icon type="user" />
              <span>{{ record.ch_update }}</span>
            </div>
          </a-tooltip>
          <span v-else>{{ text || '--' }}</span>
        </template>
        <span slot="ruleStatus" slot-scope="{ record }">
          <a-switch
            size="small"
            v-if="$permission.rule('statusSwitch')"
            :checked="record.status === 1"
            style="margin-right: 8px"
            @change="onChange(record)"
          />
          <span>{{ record.status === 1 ? '启用' : '禁用' }}</span>
        </span>
        <span slot="type" slot-scope="{ record }">{{
          record.type | typeStatus
        }}</span>
        <custom-btns-wrapper slot="action" slot-scope="{ record }">
          <a @click="view(record)" actionBtn v-if="$permission.rule('view')"
            >查看</a
          >
          <a @click="modify(record)" actionBtn v-if="$permission.rule('edit')"
            >编辑</a
          >
          <a-popconfirm
            title="确定删除?"
            @confirm="() => deleteItem(record)"
            actionBtn
            v-if="$permission.rule('delete')"
          >
            <a class="remove">删除</a>
          </a-popconfirm>
        </custom-btns-wrapper>
      </Table>
      <ExportModal ref="ExportModal" @save="exportRuleAction" />
      <ImportDrawer ref="ImportDrawer" />
    </template>
  </div>
</template>
<script>
import {
  changeStatus,
  ruleDelete,
  newRuleDelete,
  exportRule
} from '@/api/config/rule';
import Table from '@/components/Table';
import SearchArea from '@/components/Biz/SearchArea';
import DbImg from '@/components/CustomImg/DbImg';
import DateFormat from '@/components/DateFormat';
import LimitLabel from '@/components/LimitLabel';
import LimitTags from '@/components/LimitTags';
import common from '@/utils/common';
import ExportModal from '../components/ExportModal';
import ImportDrawer from '../components/ImportDrawer';
import config from './config';
import _ from 'lodash';
import bodyMinWidth from '@/mixins/bodyMinWidth';
export default {
  name: 'rules-config',
  components: {
    Table,
    DateFormat,
    LimitLabel,
    LimitTags,
    DbImg,
    SearchArea,
    ExportModal,
    ImportDrawer
  },
  props: {},
  mixins: [bodyMinWidth(1280)],
  data() {
    this.config = config(this);
    const url = _.get(this.$store.state.project, 'ruleUrl');
    return {
      url,
      searchData: {},
      searchParams: {
        fields: this.config.searchFields()
      },
      tableParams: {
        url: '/sqlreview/project/all_rule_list',
        reqParams: {},
        columns: this.config.columns.filter(item => item.visible != false),
        rowKey: 'rule_id',
        scroll: { x: 'max-content' },
        cacheKey: 'rules-config-' + this.dbType,
        pagination: {
          size: ''
        }
        // rowSelection: {
        //   type: 'checkbox'
        // }
      },
      searchVal: ''
    };
  },
  created() {},
  mounted() {
    window.localStorage.setItem('db_type', 'ORACLE');
  },
  methods: {
    addRules() {
      this.$router.push({ name: 'rules-config-add' });
    },
    bacthExport() {
      const { ExportModal } = this.$refs;
      ExportModal.show();
    },
    exportRuleAction(type) {
      const { table, ExportModal } = this.$refs;
      this.$showLoading({
        tips: `下载中...`
      });
      exportRule({
        file_format: type,
        rule_ids: table.selectedRowKeys,
        ...this.searchData
      })
        .then(res => {
          ExportModal.hide();
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    bacthImport() {
      const { ImportDrawer } = this.$refs;
      ImportDrawer.show();
    },
    getRowClassName(record) {
      const { status } = record;
      if (status == 0) {
        return 'disabled';
      }
      return '';
    },
    deleteItem(data) {
      this.$showLoading();
      if (data.rule_category === 'DDL') {
        newRuleDelete({
          rule_category: data.rule_category,
          rule_id: data.rule_id,
          rule_set_id: data.rule_set_ids
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.search(this.searchData, { keep: true, type: 'remove' });
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else {
        ruleDelete({
          id: data.rule_id
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.search(this.searchData, { keep: true, type: 'remove' });
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      }
    },
    modify(data) {
      window.localStorage.setItem('db_type', data.db_type);
      this.$router.push({
        name: 'rules-config-edit',
        query: {
          id: data.rule_id || data.rule_uid,
          rule_category: data.rule_category
        }
      });
    },
    view(data) {
      this.$router.push({
        name: 'rules-config-detail',
        query: {
          id: data.rule_id || data.rule_uid,
          rule_category: data.rule_category,
          rule_set_id: data.rule_set_id
        }
      });
    },
    onChange(data) {
      this.$showLoading();
      changeStatus({
        id: data.rule_id,
        status: data.status === 0 ? 1 : 0
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.search(this.searchData, { keep: true });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 查询
    search(data, params = {}) {
      const { table } = this.$refs;
      const { keep, type } = params;
      this.searchData = data || {};
      if (keep) {
        table.refreshKeep(type, data);
      } else {
        table.refresh(null, data);
      }
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.searchParams = {};
      this.searchData = {};
      this.searchVal = undefined;
      table.refresh();
    }
  }
};
</script>

<style lang="less" scoped>
.rules-content {
  /deep/.search-area {
    background: #fff;
    box-shadow: none;
    border: none;
    border-radius: 16px;
    margin-bottom: 16px;
    .ant-form {
      > .ant-row {
        > .ant-col {
          .ant-form-item {
            .ant-form-item-label {
              width: auto;
              min-width: 88px;
            }
          }
        }
      }
    }
  }
  .table-search {
    margin-right: 4px;
  }
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    background: #fff;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #e8e8e8;
    .title {
      color: rgb(51, 51, 51);
      font-size: 18px;
      font-weight: 600;
    }
    .button-area {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }
  .new-view-table {
    background: #fff;
    border-radius: 8px;
  }
  .DDL-category {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #f29339;
    font-weight: 400;
  }
  .DML-category {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #4cbb3a;
    font-weight: 400;
  }
}
@media screen and (max-width: 1640px) {
  .rules-content {
    /deep/.search-area {
      .ant-form {
        > .ant-row {
          > .ant-col {
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
