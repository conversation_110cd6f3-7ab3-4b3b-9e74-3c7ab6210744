class Timer {
  constructor() {
    this.queue = [];
    this.limit = 3;
    this.uniq = 0;
    this.timers = {};
  }

  setInterval(callback, timeout) {
    let _timer = null;
    const uniqId = this.uniq++;
    const loop = () => {
      if (_timer) clearTimeout(_timer);
      _timer = setTimeout(() => {
        if (this.queue.length < this.limit) {
          const promise = callback().finally(() => {
            this.queue = this.queue.filter(item => item !== promise);
          });
          this.queue.push(promise);
        };
        loop();
      }, timeout)
      this.timers[uniqId] = _timer;
    }
    loop();
  }

  destroy() {
    _.forEach(this.timers, (timer, uniqId) => {
      if (timer) {
        clearTimeout(timer);
      }
    })
    this.queue = [];
    this.uniq = 0;
    this.timers = {};
  }
}

window.CustomTimer = new Timer();