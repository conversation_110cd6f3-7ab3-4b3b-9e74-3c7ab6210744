<!--
 * @Author: your name
 * @Date: 2021-01-26 16:31:20
 * @LastEditTime: 2021-02-02 10:41:54
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/layouts/Tools/index.vue
-->
<template>
  <div class="private-extra">
    <ActivationCode ref="activity"></ActivationCode>
  </div>
</template>

<script>
import ActivationCode from '@/components/Biz/ActivationCode';
export default {
  components: { ActivationCode },
  props: {},
  data() {
    return {};
  },
  created() {},
  mounted() {
    this.$bus.$on('openActivationCodeModal', data => {
      this.$refs.activity.show(data);
    });
  },
  destroyed() {
    this.$bus.$off('openActivationCodeModal');
  },
  methods: {}
};
</script>

<style lang="less" scoped>
.private-extra {
}
</style>
