<template>
  <div :class="['custom-fullscreen-wrapper', fullscreen && 'fullscreen']">
    <slot></slot>
    <div class="fullscreen-tools">
      <a-icon type="copy" @click="copy" style="marginRight: 4px" v-if="needCopy"></a-icon>
      <a-icon
        :type="fullscreen ? 'fullscreen-exit' : 'fullscreen'"
        @click="toggleFullscreen"
      ></a-icon>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    needCopy: {
      type: Boolean,
      default: false
    }
  },
  data(vm) {
    return {
      fullscreen: false
    };
  },
  mounted() {
    this.parentNode = this.$el.parentNode;
    this.elIndex = _.findIndex(
      this.parentNode.childNodes,
      item => item === this.$el
    );
  },
  methods: {
    toggleFullscreen() {
      this.fullscreen = !this.fullscreen;
      this.$nextTick(() => {
        if (this.fullscreen) {
          document.body.appendChild(this.$el);
        } else {
          const childNodes = this.parentNode.childNodes;
          const index = this.elIndex;
          if (index === -1 || index == childNodes.length) {
            this.parentNode.appendChild(this.$el);
          } else {
            this.parentNode.insertBefore(this.$el, childNodes[index]);
          }
        }
      });
    },
    copy() {
      this.$emit('copy');
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.custom-fullscreen-wrapper {
  position: relative;
  .fullscreen-tools {
    position: absolute;
    top: 0;
    right: 0;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.5);
    z-index: 10;
    display: none;
    .anticon {
      cursor: pointer;
      // line-height: 24px;
    }
  }

  &.fullscreen {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100% !important;
    height: 100% !important;
    z-index: 1051;
    margin: 0 !important;
    padding: 0 !important;
    background: #f4faff;

    .fullscreen-tools {
      display: inline-block;
    }
  }

  &:hover {
    .fullscreen-tools {
      display: inline-block;
    }
  }
}
</style>