
<template>
  <!-- 新建项目弹窗 -->
  <!-- okText="保存" -->
  <a-modal
    v-model="visible"
    title="评价建议"
    :maskClosable="false"
    :dialogStyle="{ 'minWidth': '600px', 'maxWidth': '800px' }"
    :footer="type === '1' ? null : undefined"
    @cancel="onCancel"
  >
    <template v-if="true" slot="footer">
      <a-button @click="onCancel">取消</a-button>
      <a-button @click="onOk" type="primary">保存</a-button>
    </template>
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </a-modal>
</template>
<script>
import Form from '@/components/Form';
// import common from '@/utils/common';
const formParams = type => {
  return {
    layout: 'vertical',
    fields: [
      {
        type: 'Textarea',
        label: '请告诉我们您的宝贵意见及建议',
        key: 'opinion',
        width: '100%',
        props: {
          // size: 'small',
          rows: '15',
          disabled: type == '1'
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
    ]
  };
};

export default {
  components: { Form },
  props: {
    // comments: {
    //   type: Object,
    //   default: () => {
    //     return {};
    //   }
    // }
  },
  data() {
    return {
      visible: false,
      data: {},
      params: formParams(),
      starObj: {},
      type: ''
    };
  },
  watch: {
    // comments(val) {
    //   this.data = { ...val };
    // }
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      // const { type } = data;
      if (data.type === '1') {
        this.type = '1';
        this.data = data;
      } else {
        this.type = '2';
        this.data = {};
        this.starObj = data;
      }
      this.params = formParams(this.type);
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
      if (this.type == '1') return;
      this.$emit('refresh');
    },
    onOk() {
      const { starObj } = this;
      const { form } = this.$refs;
      this.$emit('saveStar', { ...starObj, ...form.getData() });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ form textarea.ant-input {
  height: 200px;
}

/deep/ .ant-form-vertical .ant-form-item-label {
  padding: 0 0 12px;
}
/deep/ textarea.ant-input-disabled {
  color: #a9a9a9;
}
</style>
