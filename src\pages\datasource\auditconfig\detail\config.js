export default function (ctx) {
  const columns = (value) => {
    return [
      {
        title: 'UUID',
        dataIndex: 'job_uuid',
        key: 'job_uuid',
        scopedSlots: { customRender: 'job_uuid' }
      },
      {
        title: '任务状态',
        dataIndex: 'status',
        key: 'status',
        scopedSlots: { customRender: 'status' }
      },
      {
        title: '期望时间',
        dataIndex: 'expect_time',
        key: 'expect_time',
        visible: value !== 'OpenAPI',
        scopedSlots: { customRender: 'expect_time' }
      },
      {
        title: '开始时间',
        dataIndex: 'start_time',
        key: 'start_time',
        scopedSlots: { customRender: 'start_time' }
      },
      {
        title: '结束时间',
        dataIndex: 'end_time',
        key: 'end_time',
        scopedSlots: { customRender: 'end_time' }
      },
      {
        title: '采集时间范围',
        dataIndex: 'time_range',
        key: 'time_range',
        visible: value == 'OpenAPI',
        scopedSlots: { customRender: 'time_range' }
      },
      {
        title: '执行耗时',
        dataIndex: 'execution_time',
        key: 'execution_time'
      },
      {
        title: '延迟',
        key: 'delay',
        dataIndex: 'delay',
        visible: value !== 'OpenAPI'
      },
      {
        title: '信息',
        dataIndex: 'msg',
        key: 'msg',
        scopedSlots: { customRender: 'msg' }
      }
    ];
  };

  const searchFields = (type) => {
    return [
      {
        type: 'Select',
        key: 'status',
        label: '任务状态',
        mainSearch: true,
        props: {
          placeholder: '任务状态',
          url: '/sqlreview/after_audit/get_sql_collect_enum',
          reqParams: {
            enum_name: type
          },
          getPopupContainer: (el) => {
            return document.body;
          }
        }
      }
    ];
  };
  return {
    columns,
    searchFields
  };
}
