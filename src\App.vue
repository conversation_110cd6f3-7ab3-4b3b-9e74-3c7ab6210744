<template>
  <a-config-provider :locale="zhCN">
    <template #renderEmpty>
      <custom-empty />
    </template>
    <div id="app" :class="theme">
      <!-- 全局loading -->
      <!-- <a-spin class="full-page-loading" :spinning="showLoading"></a-spin> -->
      <div
        :class="['full-page-loading', 'full-page-loading-container', loadingWay]"
        v-show="showLoading"
      ></div>
      <!-- 自定义dom wrapper -->
      <div
        style="position:absolute;left:-10000px;top:-10000px;z-index: -100;"
        id="custom-dom-getter"
      >
        <div
          v-for="item in customDomFuncs"
          :key="item.key"
          :class="`custom-dom-getter-item`"
          :custom-dom-key="item.key"
        >
          <VNode :node="item.vnodes" />
        </div>
      </div>
      <!-- router-view -->
      <router-view />
    </div>
  </a-config-provider>
</template>

<script>
import Vue from 'vue';
// import enquireScreen from './utils/device';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import config from '@/utils/config';
import common from '@/utils/common';
import NProgress from 'nprogress';
import ClipboardJS from 'clipboard';
import 'nprogress/nprogress.css';

const loadingKey = config.GlobalLoadingKey;
const loadingWay = config.loadingWay;
const loadingErrWay = config.loadingErrWay;
// const loadingTimeout = config.Timeout;
export default {
  name: 'App',
  data() {
    // 设置mode和theme等
    let useQueryForce = common.getQueryParams(null, '_UQF');
    let _mode = common.getQueryParams(null, 'mode');
    let _theme = common.getQueryParams(null, 'theme');
    let _layout = common.getQueryParams(null, 'layout');
    let mode = useQueryForce
      ? _mode
      : (process.mode !== 'default' ? process.mode : '') || _mode;
    let theme = (process.theme !== 'default' ? process.theme : '') || _theme;
    let layout = useQueryForce
      ? _layout
      : (process.layout !== 'default' ? process.layout : '') || _layout;
    this.$store.commit('common/setMode', mode);
    this.$store.commit('common/setTheme', theme);
    this.$store.commit('common/setLayout', layout);

    // 全局message配置
    this.messageConfig = config.messageConfig || {};
    this.$message.config(this.messageConfig);

    return {
      zhCN: zhCN,
      showLoading: false,
      theme: theme ? 'theme-' + theme : '',
      loadingWay,
      loadingPaused: false,
      customDomFuncs: []
    };
  },
  mounted() {
    if (this.theme) {
      document.body.className += this.theme;
    }
    this.initClipboard();
    this.$bus.$on('showScreenLoading', (isShow, params = {}) => {
      if (this.loadingPaused) return;
      this.showLoading = isShow;
      const {
        response = {},
        tips,
        method,
        duration: _duration,
        useMessage,
        useProgress
      } = params;
      const resData = _.get(response, 'data') || {};
      const duration =
        _duration != null ? _duration : this.messageConfig.duration || 2;
      const _tips =
        _.isElement(tips) || _.isString(tips) ? (
          <pre
            style={{ maxHeight: '400px', whiteSpace: 'pre-wrap', display: 'inline' }}
            domPropsInnerHTML={_.isElement(tips) ? tips.outerHTML : tips}
          ></pre>
        ) : (
          JSON.stringify(tips)
        );

      if (this.showLoading) {
        if (
          useProgress ||
          (loadingWay === 'nprogress' && useMessage !== true)
        ) {
          NProgress.start();
        } else {
          this.$message.loading({
            content: _tips || '请求中...',
            key: loadingKey,
            // duration: timeout || loadingTimeout
            // 10min过期，知道调用hide才消失
            duration: 10 * 60 * 1000
          });
        }
      } else {
        const errMsgTitle = () => {
          return (
            <div>
              错误信息
              {resData.code != null && (
                <span
                  style={{
                    color: 'rgba(0,0,0,0.45)',
                    fontSize: '14px',
                    marginLeft: '4px'
                  }}
                >
                  [编码: {resData.code}]
                </span>
              )}
            </div>
          );
        };
        // 特殊tips处理
        if (_.isString(tips) && /^\[(\d+|cancel)\]$/.test(tips)) {
          NProgress.done();
          Vue.prototype.$message.destroy();
          return;
        }
        if (
          useProgress ||
          (loadingWay === 'nprogress' && useMessage !== true)
        ) {
          NProgress.done();
          if (method && method !== 'success') {
            if (loadingErrWay === 'notify') {
              this.$notification[method || 'error']({
                message: errMsgTitle,
                description: _tips || '请求失败！',
                duration: 6
              });
            } else {
              this.$message[method || 'success']({
                content: _tips || '请求完毕！',
                key: loadingKey,
                duration: method === 'error' ? duration + 1 : duration
              });
            }
          }
        } else {
          NProgress.done();
          if (
            loadingErrWay === 'notify' &&
            useMessage !== true &&
            method &&
            method !== 'success'
          ) {
            Vue.prototype.$message.destroy();
            this.$notification[method || 'error']({
              message: errMsgTitle,
              description: _tips || '请求失败！',
              duration: 6
            });
          } else if (duration === 0) {
            Vue.prototype.$message.destroy();
          } else {
            this.$message[method || 'success']({
              content: _tips || '请求完毕！',
              key: loadingKey,
              duration: method === 'error' ? duration + 1 : duration
            });
          }
        }
      }
    });
  },
  destroyed() {
    this.$bus.$off('showScreenLoading');
  },
  created() {
    // let _this = this;
    // enquireScreen(isMobile => {
    //   _this.$store.commit('setting/setDevice', isMobile);
    // });
    window.LAYOUT_APP = this;
  },
  methods: {
    // 请确保key不重复，如需重复调用，key需要添加_.uniqueId
    getCustomDom(key, vnodes, cbk) {
      this.customDomFuncs = _.uniqBy(
        [
          ...this.customDomFuncs,
          {
            key,
            vnodes: () => vnodes(this.$createElement)
          }
        ],
        'key'
      );
      // console.log(this.customDomFuncs, 'dfjdkjfdkjf')

      this.$nextTick(() => {
        const domGetter = document.getElementById('custom-dom-getter');
        const domGetterItem = domGetter.querySelector(
          `.custom-dom-getter-item[custom-dom-key="${key}"]`
        );
        cbk && cbk(_.get(domGetterItem, 'children.0'));
        // domGetter.removeChild(domGetterItem);
        // this.customDomFuncs = this.customDomFuncs.filter(item => item.key !== key);
      });
    },
    clearCustomDom() {
      this.customDomFuncs = [];
    },
    initClipboard() {
      const clipboard = new ClipboardJS('.custom-clip-board');
      clipboard.on('success', e => {
        console.log(e, '复制成功')
        this.$hideLoading({ useMessage: true, tips: '复制成功！' });
      });
    }
  }
};
</script>

<style lang="less">
</style>
