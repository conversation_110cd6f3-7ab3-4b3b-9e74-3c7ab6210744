<template>
  <div class="optimize-directive-container">
    <div class="input">
      <a-select
        show-search
        :show-arrow="false"
        :filter-option="false"
        v-model="detailId"
        :value="detailIdSearchword"
        @search="handleDetailIdSearch"
        @change="handleDetailIdChange"
        :options="detailIdOption"
        placeholder="可输入审核详情ID搜索"
      >
        <a-spin v-if="fetchDetailLoading" slot="notFoundContent" size="small" />
      </a-select>
      <a-select
        showSearch
        :show-arrow="false"
        :filter-option="false"
        v-model="sql"
        :value="sqlSearchword"
        @search="handleSqlSearch"
        :dropdownMatchSelectWidth="false"
        :dropdownStyle="{ maxWidth: '50vw' }"
        :options="sqlOption"
        :disabled="!detailId"
        optionFilterProp="label"
        placeholder="请选择sql"
      >
        <a-spin v-if="fetchDetailLoading" slot="notFoundContent" size="small" />
      </a-select>
      <a-select
        v-model="model"
        :options="modelOption"
        placeholder="请选择模型"
      />
      <a-button type="primary" :disabled="!sql" @click="createCommand" :loading="generateLoading">生成指令</a-button>
      <a-button type="primary" :disabled="!directive || !model" @click="sendChatStream" :loading="sendLoading">会话发送</a-button>
    </div>
    <div class="directive">
      <a-textarea :autoSize="{ minRows: 10, maxRows: 25 }" v-model="directive"/>
    </div>
    <div class="suggestion">
      <custom-empty v-if="!suggestion" />
      <MarkdownViewer v-model="suggestion" v-else />
    </div>
  </div>
</template>

<script>
import Cookie from 'js-cookie';
import Config from '@/utils/config.js'
import MarkdownViewer from '@/components/Markdown/viewer';
import { detailIdList, detailSqlList, getQuestionCommand, sendQuestionCommand, getModelList } from '@/api/optimizeDirective';

export default {
  components: { MarkdownViewer },
  data() {
    return {
      detailIdSearchword: '',
      detailId: undefined,
      fetchDetailLoading: false,
      detailIdOption: [],
      sqlSearchword: '',
      sql: undefined,
      fetchSqlLoading: false,
      sqlOption: [],
      model: undefined,
      modelOption: [],
      generateLoading: false,
      sendLoading: false,
      directive: '',
      suggestion: '',
      abortController: null
    }
  },
  mounted() {
    // this.getDetailIdList('')
    this.getModelList()
  },
  watch: {
    sql() {
      this.directive = ''
      this.suggestion = ''
      if (this.abortController) {
        this.abortController.abort()
        this.abortController = null
      }
    }
  },
  methods: {
    async getDetailIdList(value) {
      if (!value) return
      const param = {
        is_all: false,
        detail_id: value
      }
      this.fetchDetailLoading = true
      const { data: { code, data } } = await detailIdList(param).finally(() => { this.fetchDetailLoading = false })
      if (code === 0) {
        this.detailIdOption = data.map(i => ({ label: i, value: i }))
      }
    },
    handleDetailIdSearch: _.debounce(async function(value) {
      this.getDetailIdList(value)
    }, 600),
    async handleDetailIdChange(id) {
      if (!id) return
      this.sql = undefined
      this.sqlOption = []
      this.getSqlList('')
    },

    async getSqlList(value) {
      const param = {
        detail_id: this.detailId
      }
      if (value) param['sql_text'] = value
      this.fetchSqlLoading = true
      const { data: { code, data } } = await detailSqlList(param).finally(() => { this.fetchSqlLoading = false })
      if (code === 0) {
        this.sqlOption = data.map(i => ({ label: i.sql_text, value: i.id, ...i }))
      }
    },
    handleSqlSearch: _.debounce(async function(value) {
      this.getSqlList(value)
    }, 600),

    async getModelList() {
      const { data: { code, data, message } } = await getModelList()
      if (code === 0) {
        this.modelOption = data.map(i => ({
          value: i.code,
          label: i.desc,
          ...i
        }))
        const defaultItem = data.find(i => i.defaultModel)
        if (defaultItem) {
          this.model = defaultItem.code
        }
      } else {
        this.$hideLoading({
          method: 'error',
          tips: message
        })
      }
    },
     async createCommand() {
      const sqlItem = this.sqlOption.find(i => i.id === this.sql)
      const { id, is_detail_ext: ext } = sqlItem
      const param = {
        detail_id: id,
        is_detail_ext: ext
      }
      this.generateLoading = true
      const { data: { code, data, message } } = await getQuestionCommand(param).finally(() => { this.generateLoading = false })
      if (code === 0) {
        this.$hideLoading({
          tips: '指令生成成功'
        })
        this.directive = data.question_command
      } else {
        this.$hideLoading({
          method: 'error',
          tips: message
        })
      }
    },

    async sendChat() {
      const param = {
        question_command: this.directive,
        is_stream: false
      }
      this.sendLoading = true
      const { data: { code, data, message } } = await sendQuestionCommand(param).finally(() => { this.sendLoading = false })
      if (code === 0) {
        this.$hideLoading({
          tips: '发送成功'
        })
        this.suggestion = data.answer
      } else {
        this.$hideLoading({
          method: 'error',
          tips: message
        })
      }
    },
    async sendChatStream() {
      this.sendLoading = true
      this.suggestion = ''
      const { supportStream } = this.modelOption.find(i => i.value === this.model) || {}
      const param = {
        question_command: this.directive,
        is_stream: supportStream === 1,
        ai_model: this.model
      }
      if (this.abortController) {
        this.abortController.abort()
        this.abortController = null
      }
      this.abortController = new AbortController()
      const res = await fetch('/sqlreview/review/send_question_command_stream', {
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          'Custom-Header': 'value', // 添加自定义头以触发预检请求
          'Authorization': Cookie.get(Config.TokenKey) || Cookie.get(Config.LuTokenKey)
        },
        body: JSON.stringify(param),
        cache: 'no-cache',
        method: 'post',
        signal: this.abortController.signal
      }).finally(() => { this.sendLoading = false })
      const reader = res.body.getReader();
      const textDecoder = new TextDecoder('utf-8');
      let buffer = ''
      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          this.abortController = null
          break
        }
        buffer += textDecoder.decode(value, { stream: true })
        console.log(buffer)
        while (buffer.includes('\n')) {
          const lineEnd = buffer.indexOf('\n')
          const line = buffer.slice(0, lineEnd).trim()
          buffer = buffer.slice(lineEnd + 1)
          if (line.length > 1) {
            try {
              const res = JSON.parse(line)
              this.suggestion += res.answer || ''
            } catch (error) {
              console.error(error)
            }
          }
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.optimize-directive-container {
  display: flex;
  flex-direction: column;
  background-color: #FFF;
  margin-top: -40px;
  padding: 10px 15px;
  .input {
    display: flex;
    & > :not(:last-child) {
      margin-right: 10px;
    }
    & > .ant-input-search {
      width: 200px;
    }
    & > .ant-select {
      width: 200px;
    }
  }
  .directive {
    margin: 10px 0;
  }
}
</style>
