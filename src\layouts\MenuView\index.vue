<template>
  <a-menu
    :mode="menuMode"
    :theme="
      ['up-down', 'up-down-new', 'fresh'].includes(layout) ? 'light' : 'dark'
    "
    class="custom-menu"
    :style="{ height: '100%', borderRight: 0 }"
    :selectedKeys="selectedKeys"
    :openKeys.sync="openKeys"
    :inlineIndent="mode.startsWith('frame-inset') ? 16 : inlineIndent"
    @click="menuClick"
  >
    <template v-for="item in menus">
      <!-- <a-sub-menu :key="item.key" v-if="item.isLeaf">
        <span slot="title">
          <a-icon :type="item.icon" v-if="item.icon" />
          <span>{{item.name}}</span>
        </span>
        <MySubMenu :key="item.key" :menu-info="item"></MySubMenu>
      </a-sub-menu>-->
      <MySubMenu :key="item.key" :menu-info="item"></MySubMenu>
    </template>
  </a-menu>
</template>

<script>
import MySubMenu from './SubMenu';
import config from './config';
// import config from './config copy';

export default {
  components: { MySubMenu },
  props: {
    menuMode: {
      type: String,
      default: 'inline'
    },
    collapsed: {
      type: Boolean,
      default: true
    },
    inlineIndent: {
      type: Number,
      default: 24
    },
    menusConfig: Array,
    openKeysConfig: Array
  },
  data() {
    let mode = this.$store.state.common.mode;
    // console.log(this.$route, '6666');
    // const { name, meta = {} } = this.$route;
    this.config = _.isFunction(config) ? config(this) : config;
    const menus = this.getMenus();
    this.setVisible(menus);
    return {
      menus,
      selectedKeys: this.getSelectedKeysByRoute(),
      openKeys: this.getOpenKeys(),
      mode: mode ? 'frame-' + mode : ''
    };
  },
  computed: {
    layout() {
      return this.$store.state.common.layout;
    }
  },
  mounted() {
    this.$bus.$off('updateMenu');
    this.$bus.$on('updateMenu', data => {
      this.config = _.isFunction(config) ? config(this) : config;
      const menus = this.getMenus();
      this.setVisible(menus);
      this.menus = [...menus];
    });
  },
  updated() {},
  methods: {
    getMenus() {
      return this.menusConfig || this.config.menu;
    },
    setVisible(menus) {
      const deal = (list = []) => {
        list.forEach(item => {
          let trueVisible = item.visible != null ? item.visible : true;
          if (_.isArray(item.visible) && this.config.useFrontAuth !== false) {
            // 是数组，需要根据角色判断
            const user = this.$store.state.account.user;
            if (!_.isEmpty(user)) {
              // if (
              //   this.config.menuIgnore &&
              //   this.config.menuIgnore(item.key, this.$store)
              // ) {
              //   trueVisible = false;
              // } else {
              trueVisible = item.visible.includes(user.role);
              // }
            } else {
              trueVisible = false;
            }
          }
          // 权限改版由后端接口返回控制后，不再需要判断menuIgnore
          // if (
          //   this.config.menuIgnore &&
          //   this.config.menuIgnore(item.key, this.$store)
          // ) {
          //   trueVisible = false;
          // }
          if (item.relyTaskId && this.config.relyTaskId) {
            // 依赖taskId
            const taskId = this.$store.state.task.id;
            trueVisible = taskId != null;
          }
          item._visible = trueVisible;

          if (item.children && item.children.length > 0) {
            deal(item.children);
          }
        });
      };
      deal(menus);
    },
    getOpenKeys() {
      if (this.collapsed || this.menuMode === 'vertical') return [];
      if (this.openKeysConfig) {
        return this.openKeysConfig;
      }
      const { menuMap } = this.config;
      const { name, meta = {} } = this.$route;
      const { parent } = meta;
      let keys = [];

      function getKeys(key) {
        const menuItem = menuMap[key];
        if (menuItem) {
          const parentItem = menuMap[menuItem.parentId];
          if (parentItem) {
            keys.push(parentItem.key);
            getKeys(parentItem.key);
          }
        }
      }
      getKeys(parent || name);
      return keys;
    },
    menuClick({ item, key, keyPath }) {
      // console.log(item, key, keyPath);
      const menuItem = this.getMenuItem(key) || {};
      if (_.isFunction(menuItem.jumpUrl) && menuItem.jumpUrl()) {
        window.open(menuItem.jumpUrl(), '_blank');
        return;
      }
      if (menuItem.path) {
        this.$router.push({ path: menuItem.path });
      } else {
        this.$router.push({ name: key });
      }
      this.selectedKeys = [key];
    },
    getMenuItem(key) {
      let res;
      const loop = list => {
        list.forEach(item => {
          if (res) return;
          if (item.key == key) {
            res = item;
            return false;
          }

          if (item.children && item.children.length > 0) {
            loop(item.children);
          }
        });
      };
      loop(this.menus);

      return res;
    },
    getSelectedKeysByRoute() {
      const { name, meta = {} } = this.$route;
      const newkey = meta.parent || name;
      // const oldKey = this.selectedKeys[0] || '';
      // if (newkey != oldKey && oldKey.split('_')[0] !== newkey) {
      //   this.selectedKeys = [newkey];
      // }
      let res = [];
      if (_.isFunction(meta.getMenuKey)) {
        res = [meta.getMenuKey(this.$route)];
      } else {
        res = [newkey];
      }

      return res;
    }
  },
  watch: {
    collapsed: {
      handler(newVal = {}, oldVal) {
        if (newVal) {
          this.openKeys = [];
        } else {
          this.openKeys = this.getOpenKeys();
        }
      }
      // immediate: true
      // deep: true
    },
    $route(to, from) {
      this.selectedKeys = this.getSelectedKeysByRoute();
      if (!this.collapsed) {
        this.openKeys = this.getOpenKeys();
      }
    },
    '$store.state.account.user'(newVal = {}) {
      const menus = this.getMenus();
      this.setVisible(menus);
      this.menus = [...menus];
    },
    '$store.state.task.id': {
      handler(newVal, oldVal) {
        const menus = this.getMenus();
        this.setVisible(menus);
        this.menus = [...menus];
      },
      immediate: true
    },
    menusConfig(newVal, oldVal) {
      this.setVisible(newVal);
      this.menus = newVal;
    },
    openKeysConfig(newVal, oldVal) {
      this.openKeys = this.getOpenKeys();
    }
  }
};
</script>
<style lang="less" scoped>
.custom-menu {
  // /deep/ * {
  //   transition: none !important;
  //   animation: none !important;
  // }
}
</style>
