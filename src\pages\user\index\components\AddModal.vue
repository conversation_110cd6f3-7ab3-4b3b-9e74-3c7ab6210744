<template>
  <!-- 新增用户权限弹窗 -->
  <a-modal
    v-model="visible"
    title="新增用户权限"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
// import common from '@/utils/common';

export default {
  components: { Form },
  props: {},
  data() {
    return {
      visible: false,
      data: {},
      params: {
        layout: 'vertical',
        fields: [
          {
            type: 'Input',
            label: '姓名',
            key: 'ch_name'
          },
          {
            type: 'Input',
            label: '用户名',
            key: 'name',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' },
              {
                pattern: /^[a-zA-Z0-9_-]{4,16}$/,
                message: '用户名要求4到16位（字母，数字，下划线，减号）'
              }
            ]
          },
          {
            type: 'InputPassword',
            label: '用户密码',
            key: 'pwd',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' },
              {
                pattern: /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!_%*#?&])[A-Za-z\d$@$!_%*#?&]{8,20}$/,
                message:
                  '密码至少8个字符，必须包含字母、数字和@$!_%*#?&等特殊字符'
              }
            ]
          },
          {
            type: 'InputPassword',
            label: '确认密码',
            key: 'confirm',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' },
              {
                validator: (rules, value, callback) => {
                  this.handleCfmPwd(rules, value, callback);
                }
              }
            ]
          },
          // {
          //   type: 'TreeSelect',
          //   label: '选择组',
          //   key: 'groups',
          //   props: {
          //     url: '/sqlreview/get-all-user-group/',
          //     multiple: true,
          //     showSearch: true,
          //     treeNodeFilterProp: 'label'
          //     // mode: 'multiple'
          //   }
          // },
          {
            type: 'Select',
            label: '项目组',
            key: 'groups',
            props: {
              url: '/sqlreview/project_config/select_project_group',
              reqParams: {},
              mode: 'multiple',
              allowSearch: true,
              backSearch: true,
              limit: 20
            }
          },
          {
            type: 'Input',
            label: '邮箱',
            key: 'email',
            rules: [
              // { required: true, message: '该项为必填项', trigger: 'blur' },
              {
                pattern: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
                message: '必须符合邮箱格式'
              }
            ]
          },
          {
            type: 'Select',
            key: 'userRole',
            label: '用户角色',
            props: {
              url: '/sqlreview/project_config/role_info_by_select'
            }
          }
        ]
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.data = {};
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData() || {};
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    },
    handleCfmPwd(rules, value, callback) {
      const { form } = this.$refs;
      let pwd = form.getData().pwd;
      if (pwd && pwd !== value) {
        callback(new Error('两次密码输入不一致'));
      } else {
        callback();
      }
    }
  }
};
</script>

<style lang="less" scoped>
</style>
