<template>
  <span class="dataSource-content" v-if="!isEmpty">
    <span :class="dataSource.env == 'TEST' ? 'test' : 'prod'">{{
      dataSource.env == 'TEST' ? '测试' : '生产'
    }}</span>
    <DbImg
      :style="{ width: width }"
      :value="dataSource.db_type"
      :schemaName="dataSource.name + '(' + dataSource.db_url + ')'"
      mode="ellipsis"
    />
  </span>
</template>
<script>
export default {
  components: {},
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    },
    width: {
      type: String,
      default: '120px'
    }
  },
  computed: {
    isEmpty() {
      return _.isEmpty(this.dataSource)
    }
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.dataSource-content {
  display: flex;
  align-items: center;
  > span {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    border-radius: 4px;
    margin: 0 4px 0 0;
    padding: 0 4px;
    height: 20px;
    line-height: 20px;
    white-space: nowrap;
  }
  .test {
    background: #f6ffed;
    border: 1px solid rgba(183, 235, 143, 1);
    color: #52c41a;
  }
  .prod {
    background: #fff7e6;
    border: 1px solid rgba(255, 213, 145, 1);
    color: #fa8c16;
  }
}
</style>
