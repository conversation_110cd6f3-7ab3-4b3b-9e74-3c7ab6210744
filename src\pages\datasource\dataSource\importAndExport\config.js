export default function (ctx) {
  // 统计信息导入
  const importFields = () => {
    return [
      {
        type: 'DataBaseChoose',
        label: '目标数据库',
        key: 'data_source_id',
        props: {
          url: '/sqlreview/project/get_datasource_list',
          reqParams: {},
          loaded(data) {
            ctx.dataSourceOption = data;
          },
          beforeLoaded(data) {
            return data.map((item) => {
              return {
                ...item,
                instance_usage: item.env,
                showText: item.label + '(' + item.db_url + ')'
              };
            });
          },
          mode: 'default',
          optionLabelProp: 'children',
          allowSearch: true,
          backSearch: true,
          limit: 30
        },
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              data_source_id: value,
              export_to: null,
              to_data_source_id: null
            });
            ctx.tips = 2;
            ctx.dataSourceId = value;
            ctx.errorData = [];
            ctx.dataSource = [];
            ctx.isCheck = false;
            ctx.$set(ctx.tableParams, 'reqParams', {});
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      }
    ];
  };
  const columns = [
    {
      title: '文件',
      dataIndex: 'file',
      key: 'file',
      width: 200
    },
    {
      title: '目标Schema',
      dataIndex: 'schema',
      key: 'schema',
      width: 200,
      scopedSlots: { customRender: 'schema' }
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      scopedSlots: { customRender: 'action' }
    }
  ];
  const editConfig = {
    schema: (row, record = {}) => {
      return {
        type: 'Select',
        props: {
          url: '/sqlreview/project/oracle_schema',
          reqParams: { data_source_id: record.data_source_id || '' },
          allowSearch: true,
          backSearch: true,
          limit: 50
        },
        listeners: {
          change: (value) => {
            const { tableEdit } = ctx.$refs;
            if (value) {
              tableEdit.saving({
                id: record.id,
                schema: value
              });
            } else {
              tableEdit.saving({
                id: record.id,
                schema: null
              });
            }
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      };
    }
  };

  // 统计信息导出
  const exportFields = () => {
    return [
      {
        type: 'Input',
        label: '源数据库',
        key: 'data_source_id',
        hideComponent: true,
        slots: [{ key: 'data_source_id' }],
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: '导出类型',
          key: 'export_to',
          props: {
            class: 'inline',
            mode: 'tips',
            options: [
              {
                label: '导出到目标数据库',
                value: 1
              },
              {
                label: '导出到文件',
                value: 2
              }
            ]
          },
          listeners: {
            change: (value) => {
              ctx.$refs.form.saving({
                export_to: value,
                to_data_source_id: null
              });
              if (value == 1 && formData.data_source_id) {
                ctx.$refs.submit.show();
                ctx.$set(ctx.tableParams, 'reqParams', {});
              }
              if (value == 2 && formData.data_source_id) {
                ctx.$set(ctx.tableParams, 'reqParams', {
                  data_source_id: formData.data_source_id || '',
                  type: 'all'
                });
                ctx.$set(ctx.tableParams, 'columns', exportColumns(2));
                ctx.$set(ctx.tableParams.rowSelection, 'selectedRowKeys', []);
                ctx.selectedRowKeys = [];
                ctx.toDataSourceId = null;
                ctx.isTargetAuthCheck = false;
                ctx.targetTips = 2;
              }
              ctx.exportType = value;
            }
          },
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      (formData = {}) => {
        return {
          type: 'Input',
          label: '目标数据库',
          key: 'to_data_source_id',
          hideComponent: true,
          slots: [{ key: 'to_data_source_id' }],
          visible: formData.export_to == 1,
          rules: [{ required: true, message: '该项为必填项' }]
        };
      },
      {
        type: 'Input',
        label: 'Schema',
        key: 'schema_list',
        hideComponent: true,
        slots: [{ key: 'schema_list' }]
        // rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      }
    ];
  };
  const exportColumns = (exportType, selectedKeys = []) => {
    return exportType == 1
      ? [
          {
            title: '源schema',
            dataIndex: 'schema',
            key: 'schema',
            width: 300
          },
          {
            title: '目标schema',
            dataIndex: 'to_schema',
            key: 'to_schema',
            scopedSlots: { customRender: 'to_schema' },
            width: 300
          }
        ]
      : [
          {
            title: '源schema',
            dataIndex: 'schema',
            key: 'schema',
            width: 300
          },
          {
            title: '导出文件名',
            dataIndex: 'export_file',
            key: 'export_file',
            customRender: (text, record) => {
              return selectedKeys.includes(record.id)
                ? `slowlog_${record.schema}.sql`
                : '--';
            },
            width: 300
          }
        ];
  };

  const exportEditConfig = (id) => {
    return {
      to_schema: (row, record = {}) => {
        return {
          type: 'Select',
          props: {
            url: '/sqlreview/project/oracle_schema',
            reqParams: {
              data_source_id: id || '',
              type: 'select'
            },
            allowSearch: true,
            backSearch: true,
            limit: 30,
            loaded: (data) => {
              const { table } = ctx.$refs;
              if (data.length == 0) {
                table.saving({
                  id: record.id,
                  to_schema: null
                });
              }
            }
          },
          listeners: {
            change: (value) => {
              const { table } = ctx.$refs;
              if (value) {
                table.saving({
                  id: record.id,
                  to_schema: value
                });
              } else {
                table.saving({
                  id: record.id,
                  to_schema: null
                });
                ctx.selectedRowKeys = ctx.selectedRowKeys.filter(
                  (item) => item !== record.id
                );
                ctx.selectedRows = ctx.selectedRows.filter(
                  (item) => item.id !== record.id
                );
              }
              ctx.handleSelectChange();
            }
          },
          rules: []
        };
      }
    };
  };
  const fields = () => {
    return [
      (formData = {}) => {
        return {
          type: 'DataBaseChoose',
          label: ' ',
          key: 'to_data_source_id',
          props: {
            url: '/sqlreview/project/get_datasource_list',
            reqParams: {},
            loaded(data) {
              ctx.dataBaseOption = data;
            },
            beforeLoaded(data) {
              return data.map((item) => {
                return {
                  ...item,
                  instance_usage: item.env
                };
              });
            },
            mode: 'default',
            optionLabelProp: 'children',
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          listeners: {
            change: (value) => {
              const { form } = ctx.$refs;
              // const data = form.getData();
              ctx.isTargetAuthCheck = false;
              form.saving({
                to_data_source_id: value
              });
              ctx.targetTips = 2;
              ctx.targetErrorData = [];
              ctx.toDataSourceId = value;
              ctx.$set(ctx.tableParams, 'reqParams', {});
              // ctx.$set(ctx.tableParams, 'columns', exportColumns(1));
              // ctx.$set(ctx.tableParams, 'editConfig', exportEditConfig(value));
              ctx.$set(ctx.tableParams.rowSelection, 'selectedRowKeys', []);
              ctx.selectedRowKeys = [];
            }
          },
          rules: [{ required: true, message: '该项为必填项' }]
        };
      }
    ];
  };

  // 历史记录
  const historyColumns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      customRender: (text) => {
        return text == 1 ? '导入' : '导出';
      }
    },
    {
      title: '源',
      dataIndex: 'source_type',
      key: 'source_type',
      scopedSlots: { customRender: 'source_type' }
    },
    {
      title: '目标',
      key: 'target_type',
      dataIndex: 'target_type',
      scopedSlots: { customRender: 'target_type' }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'status' }
    },
    {
      title: '创始人',
      dataIndex: 'created_by',
      key: 'created_by'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at'
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      fixed: 'right'
    }
  ];
  const historySearchFields = () => {
    return [
      {
        type: 'Select',
        // label: '执行数据库',
        key: 'task_type',
        compIcon: 'lu-icon-task',
        props: {
          placeholder: '任务类型',
          options: [
            { label: '导入', value: 1 },
            { label: '导出', value: 2 }
          ],
          getPopupContainer: (el) => {
            return document.body;
          }
        }
      },
      {
        type: 'Input',
        // label: '执行用户',
        key: 'source',
        compIcon: 'lu-icon-database',
        props: {
          placeholder: '源数据库名称/ip'
        }
      },
      {
        type: 'Input',
        // label: 'SQL_ID',
        compIcon: 'lu-icon-schema',
        key: 'target',
        props: {
          placeholder: '目标数据库名称/ip'
        }
      },
      {
        type: 'Input',
        // label: 'SQL文本',
        compIcon: 'user',
        key: 'created_by',
        props: {
          placeholder: '创始人'
        }
      }
    ];
  };
  return {
    fields,
    columns,
    editConfig,
    historySearchFields,
    historyColumns,
    importFields,
    exportFields,
    exportColumns,
    exportEditConfig
  };
}
