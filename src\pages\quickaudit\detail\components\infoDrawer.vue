<template>
  <a-drawer
    placement="right"
    :maskClosable="true"
    :closable="true"
    :visible="visible"
    wrapClassName="quick-audit-info-drawer"
    @close="onClose"
    width="60%"
  >
    <a-spin tip="加载中" :spinning="loading">
      <SqlDetail ref="SqlDetail" :dataInfo="dataInfo" @onPrev="onPrev" @onNext="onNext"></SqlDetail>
    </a-spin>
    <div
      :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
    >
      <a-button @click="onClose">关闭</a-button>
    </div>
  </a-drawer>
</template>
<script>
import { sqlDetailInfo } from '@/api/quickaudit';
import SqlDetail from '@/components/Biz/SqlDetail';

export default {
  components: {
    SqlDetail
  },
  data() {
    return {
      index: 1,
      visible: false,
      loading: false,
      audit_id: null,
      dataInfo: {
        system_info: {},
        plan: null,
        ai_comment: null
      }
    };
  },
  mounted() {},
  methods: {
    show(id, idx) {
      this.index = idx + 1;
      this.visible = true;
      this.audit_id = id;
      this.getSqlDetailInfoData({
        audit_id: this.audit_id
        // index: this.index
      });
    },
    onClose() {
      if (!_.isEmpty(this.dataInfo.dynamic_list)) {
        this.$refs.SqlDetail.$refs.SqlException.onExpand();
      }
      this.visible = false;
    },
    // 数据详情
    getSqlDetailInfoData(payload) {
      this.loading = true;
      sqlDetailInfo(payload)
        .then(res => {
          this.loading = false;
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ duration: 0 });
            this.dataInfo = res.data.data || {};
            this.dataInfo = { ...this.dataInfo };
            this.audit_id = this.dataInfo.audit_id;
            this.index = res.data.data.index;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(() => {
          this.loading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // prev
    onPrev() {
      this.idx = this.idx - 1;
      this.getSqlDetailInfoData({
        audit_id: this.audit_id,
        paging: 'prev',
        index: this.index
      });
    },
    // next
    onNext() {
      this.idx = this.idx + 1;
      this.getSqlDetailInfoData({
        audit_id: this.audit_id,
        paging: 'next',
        index: this.index
      });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.ant-drawer-header-no-title {
  .ant-drawer-close {
    .anticon {
      font-size: 16px;
      color: #27272a;
    }
  }
}
</style>
<style lang="less">
.quick-audit-info-drawer {
  .ant-drawer-body {
    padding-bottom: 52px;
  }
}
</style>