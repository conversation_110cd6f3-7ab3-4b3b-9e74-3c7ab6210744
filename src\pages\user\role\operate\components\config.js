export default function (ctx) {
  const iconMap = {
    system: {
      type: 'home',
      color: '#f5793b'
    },
    menu: {
      type: 'folder',
      color: '#716dda'
    },
    page: {
      type: 'file-text',
      color: '#58b372'
    },
    element: {
      type: 'code-sandbox-square',
      color: '#efb176'
    },
    schema: {
      type: 'profile',
      color: '#95b75a'
    }
  };
  const baseInfo = (type) => {
    return [
      {
        type: 'Input',
        label: '角色code',
        key: 'role_code',
        props: {
          disabled: type === 'edit'
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur', whitespace: true }]
      },
      {
        type: 'Input',
        label: '角色名称',
        key: 'role_name',
        props: {
          disabled: type === 'edit'
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur', whitespace: true }]
      },
      {
        type: 'Textarea',
        label: '角色描述',
        key: 'comments',
        props: {
          placeholder: '请输入'
        },
        span: 24,
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
      // {
      //   type: 'Switch',
      //   label: '是否默认角色',
      //   key: 'default_role',
      //   visible: !!type,
      //   props: {
      //     disabled: true,
      //     size: 'default',
      //     'checked-children': '是',
      //     'un-checked-children': '否'
      //   },
      //   width: 50,
      //   rules: [
      //     // { required: true, message: '该项为必填项', trigger: 'blur' }
      //   ]
      // }
    ]
  };
  const getFields = (node = {}, batch) => {
    const category = node.source_category;
    let fields = [
      {
        type: 'Input',
        label: '资源code',
        key: 'id',
        props: {
          disabled: true
        }
      },
      {
        type: 'Input',
        label: '资源name',
        key: 'source_name',
        props: {
          disabled: true
        }
      }
    ];
    if (category === 'resource') {
      fields.push({
        type: 'Select',
        label: '类型',
        key: 'source_type',
        props: {
          options: [
            { label: '系统', value: 'system' },
            { label: '菜单', value: 'menu' },
            { label: '页面', value: 'page' },
            { label: '元素', value: 'element' }
          ],
          disabled: true
        }
      })
    } else {
      fields.push({
        type: 'Select',
        label: '类型',
        key: 'source_type',
        props: {
          options: [
            { label: 'schema', value: 'schema' }
          ],
          disabled: true
        }
      })
    }

    if (node.permissions) {
      fields.push({
        type: 'CheckboxGroup',
        label: '权限',
        key: 'choose_permissions',
        props: {
          options: node.permissions
        }
      })
    }

    if (batch) { fields = []; }
    if (ctx.isEdit || batch) {
      fields.push(
        (data = {}) => {
          return {
            type: 'Switch',
            label: '是否显示',
            key: 'source_ext_show',
            props: {
              checkedChildren: '是',
              unCheckedChildren: '否'
            },
            visible: ['DefaultShow', 'DefaultHide'].includes(_.get(data, 'source_ext.deal_type')),
            width: 48
          }
        }
      )

      fields.push(
        (data = {}) => {
          return {
            type: 'Input',
            label: '选取项',
            key: 'source_ext_includes',
            props: {
            },
            visible: ['ArrayMerge', 'BACK'].includes(_.get(data, 'source_ext.deal_type')),
            hideComponent: true,
            slots: [
              { key: 'source_ext_includes' }
            ]
          }
        }
      )

      // fields.push(
      //   (data = {}) => {
      //     return {
      //       type: 'Coder',
      //       label: '配置值',
      //       key: 'source_ext_backParams',
      //       tips: '本节点为后端权限注入配置，如有疑问，请咨询平台方',
      //       props: {
      //         options: {
      //           theme: 'default'
      //         },
      //         needFormat: true,
      //         type: 'json'
      //       },
      //       visible: ['BACK'].includes(_.get(data, 'source_ext.deal_type'))
      //     }
      //   }
      // )
    }

    // fields.push({
    //   type: 'TableEdit',
    //   label: '资源参数',
    //   // tips: '<div>用于设置文件/文件夹，从而不进行sql review扫描</div>',
    //   key: 'source_ext',
    //   getDataMethod: 'getData',
    //   resetFieldsMethod: 'resetFields',
    //   initialValue: [],
    //   props: {
    //     columns: [
    //       {
    //         dataIndex: '_key',
    //         key: '_key',
    //         width: 300,
    //         scopedSlots: { customRender: '_key' }
    //       },
    //       {
    //         dataIndex: '_value',
    //         key: '_value',
    //         width: 300,
    //         scopedSlots: { customRender: '_value' }
    //       },
    //       {
    //         key: 'action',
    //         width: 80,
    //         scopedSlots: { customRender: 'action' }
    //       }
    //     ],
    //     editConfig: {
    //       _key: (row, record = {}) => {
    //         return {
    //           type: 'Input',
    //           props: {
    //             size: 'default',
    //             placeholder: 'key'
    //           },
    //           rules: [
    //             // { required: true, message: '该项为必填项' }
    //           ]
    //         }
    //       },
    //       _value: (row, record = {}) => {
    //         return {
    //           type: 'Input',
    //           props: {
    //             size: 'default',
    //             placeholder: 'value'
    //           },
    //           rules: [
    //             // { required: true, message: '该项为必填项' }
    //           ]
    //         }
    //       }
    //     },
    //     mode: 'list',
    //     initEditStatus: true,
    //     pagination: false,
    //     leastNum: 1,
    //     actionBtns: ['add', 'remove'],
    //     actionBtnsIcons: {
    //       add: 'plus-circle',
    //       remove: 'close-circle'
    //     }
    //   }
    // })
    return fields;
  }
  return {
    baseInfo,
    iconMap,
    getFields
  };
};
