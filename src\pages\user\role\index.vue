<template>
  <div class="content page-list-single">
    <!-- <a-card style="width:100%" class="common-page-card" title="角色管理" :bordered="false"></a-card>
    <a-button slot="extra" type="primary" icon="plus" @click="add">新增角色</a-button>-->
    <div class="frame-button-wrapper">
      <a-button
        slot="extra"
        class="highlight"
        icon="plus"
        @click="add"
        v-if="$permission.role('add')"
        >新增角色</a-button
      >
    </div>
    <!-- <SearchArea v-bind="searchParams" @reset="reset" @search="search" needCache></SearchArea> -->
    <Table
      ref="table"
      v-bind="tableParams || {}"
      class="new-view-table small-size"
    >
      <span slot="role_name" slot-scope="{ text }">
        <a-tag :color="roleNameColor[text]">{{ text }}</a-tag>
      </span>
      <custom-btns-wrapper slot="action" slot-scope="{ text, record }">
        <span authKey="edit" actionBtn v-if="$permission.role('edit')">
          <a @click="edit(record)">编辑</a>
          <a-divider type="vertical" />
        </span>
        <a-popconfirm
          authKey="delete"
          v-if="!record.default_role && $permission.role('delete')"
          actionBtn
          title="确定删除用户组?"
          @confirm="() => remove(record)"
        >
          <a class="remove">删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </Table>
  </div>
</template>

<script>
import { roleRemove } from '@/api/user';
import Table from '@/components/Table';
import SearchArea from '@/components/SearchArea';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
export default {
  name: 'userList',
  props: {},
  mixins: [bodyMinWidth(1280)],
  components: { Table, SearchArea },
  data() {
    this.config = config(this);
    return {
      roleNameColor: {
        admin: '',
        dev: 'blue',
        dba: 'red',
        tester: 'green'
      },
      tableParams: {
        url: '/sqlreview/project_config/role/page',
        // method: 'post',
        rowKey: 'id',
        columns: this.config.columns,
        needCache: true,
        isInitReq: false,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true
      },
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 3
      }
    };
  },
  computed: {},
  mounted() {
    // 请求、全局loading测试
    // this.$showLoading();
  },
  methods: {
    // 新建
    add() {
      this.$router.push({ name: 'roleAdd', params: {} });
    },
    // 修改
    edit(data = {}) {
      this.$router.push({ name: 'roleEdit', params: { id: data.id } });
    },
    // 删除
    remove(data = {}) {
      // 请求
      this.$showLoading();
      roleRemove({
        id: data.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: '删除成功' });
            this.search(this.searchData, { keep: true, type: 'remove' });
          } else if (_.get(res, 'data.code') == 4000) {
            this.$hideLoading({
              useMessage: true,
              method: 'warning',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: '请求失败' });
        });
    },
    // 查询
    search(data, params = {}) {
      const { table } = this.$refs;
      const { keep, type } = params;
      this.searchData = data || {};
      if (keep) {
        table.refreshKeep(type, data);
      } else {
        table.refresh(null, data);
      }
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      this.searchData = {};
      table.refresh();
    }
  }
};
</script>

<style lang="less" scoped>
</style>
