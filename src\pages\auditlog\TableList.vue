<template>
  <div>
    <Table
      ref="table"
      v-bind="tableParams || {}"
      class="new-view-table small-size"
    >
      <template slot="status" slot-scope="{ text }">
        <span v-if="text == 1" style="color: green">成功</span>
        <span v-else style="color: red">失败</span>
      </template>
      <template slot="schema" slot-scope="{ record }">
        <InstanceItem
          mode="ellipsis"
          width="300"
          percent="85%"
          size="small"
          :tagText="record.env"
          :src="record.db_type"
          :text="record.schema + '@' + record.ds_name"
        ></InstanceItem>
      </template>
      <template slot="sql_text" slot-scope="{ text }">
        <LimitLabel
          :label="text"
          :limit="36"
          :nowrap="true"
          format="sql"
        ></LimitLabel>
      </template>
      <custom-btns-wrapper slot="action" slot-scope="{ record }" :limit="3">
        <a @click="showDetail(record)" actionBtn :disabled="checkEmpty(record)"
          >详情</a
        >
      </custom-btns-wrapper>
    </Table>
    <DetailModal ref="trackingDetail" />
  </div>
</template>
<script>
import DetailModal from './DetailModal';
import InstanceItem from '@/components/Biz/InstanceItem';
import LimitLabel from '@/components/LimitLabel';
import Table from '@/components/Table';
import config from './config';
import moment from 'moment';

export default {
  components: { Table, InstanceItem, LimitLabel, DetailModal },
  props: {
    tabKey: String,
    rangeTime: {
      type: Object,
      default: () => {}
    },
    type: String
  },
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url:
          this.type == 'login'
            ? '/sqlreview/audit_log/login_list'
            : '/sqlreview/audit_log/tracking_list',
        // this.type == 'login'
        //   ? '/sqlreview/audit_log/login_list'
        //   : '/sqlreview/audit_log/sqlresolver_list',
        method: 'post',
        reqParams: {
          time_range: [
            moment()
              .subtract(7, 'day')
              .format('YYYY-MM-DD HH:mm:ss'),
            moment().format('YYYY-MM-DD HH:mm:ss')
          ]
        },
        rowKey: 'id',
        needTools: true,
        needSearchArea: true,
        columns:
          // this.type == 'login' ? this.config.columnsLogin : this.config.columns,
          this.type == 'login'
            ? this.config.columnsLogin
            : this.config.trackingColumns,
        searchFields:
          // this.type == 'login' ? this.config.fieldsLogin : this.config.fields,
          this.type == 'login'
            ? this.config.fieldsLogin
            : this.config.trackingFields,
        scroll: { x: 'max-content' }
      }
    };
  },
  created() {},
  mounted() {},
  methods: {
    getSearchParamsFn() {
      const { table } = this.$refs;
      const data = table.searchParams;
      return data;
    },
    showDetail(record) {
      this.$refs.trackingDetail.show(record);
    },
    checkEmpty(record) {
      const bool = _.isEmpty(record.request_params);
      return bool;
    }
  },
  watch: {
    rangeTime: {
      handler(newVal) {
        if (newVal.activeKey == this.tabKey) {
          this.$set(this.tableParams, 'reqParams', {
            time_range: newVal.value
          });
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
</style>
