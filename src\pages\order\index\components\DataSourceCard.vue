<template>
  <div class="card-table">
    <div class="card-table-main-info">
      <div class="left-block">
        <div class="project-name">
          <a @click="toDetail">
            {{ cardData.data_source_id + ' ' + cardData.data_source_name }}
          </a>
        </div>
        <div class="info-box">
          <div class="status-box">
            <span class="datasource-content">
              <span>数据源</span>
              <div v-if="cardData.data_source_name">
                <span :class="cardData.env == 'TEST' ? 'test' : 'prod'">{{
                  cardData.env == 'TEST' ? '测试' : '生产'
                }}</span>
                <DbImg
                  :value="cardData.db_type"
                  :schemaName="
                    cardData.data_source_name + '(' + cardData.db_url + ')'
                  "
                  mode="ellipsis"
                />
              </div>
              <div v-else>{{ '--' }}</div>
            </span>
            <span class="ai-review">
              <span>审核</span>
              <a-tag :class="cardData.status == 1 && 'status-finish'">{{
                cardData.status == 0 ? '待审核' : '已完成'
              }}</a-tag>
            </span>
          </div>
        </div>

        <div class="tag-box">
          <a-tag class="tooltip">
            <span>关联项目：</span>
            <span v-if="cardData.projects.length == 1">{{
              cardData.projects[0]
            }}</span>
            <a-tooltip v-if="cardData.projects.length > 1">
              <template slot="title">
                <span>{{ cardData.projects.toString() }}</span>
              </template>
              <span>{{ cardData.projects[0] + '; ' + '...' }}</span>
            </a-tooltip>
          </a-tag>
          <a-tag
            class="tooltip"
            v-if="
              cardData.project_group_leader &&
              cardData.project_group_leader.length > 0
            "
          >
            <span>项目组负责人：</span>
            <span v-if="cardData.project_group_leader.length > 0">
              <span>
                <a-tooltip>
                  <template slot="title">
                    <span>
                      {{ cardData.project_group_leader | handleTitle }}</span
                    >
                  </template>
                  <span>{{
                    cardData.project_group_leader | handleContent
                  }}</span>
                </a-tooltip>
              </span>
            </span>
          </a-tag>
          <a-tag
            class="tooltip"
            v-if="cardData.dba_leader && cardData.dba_leader.length > 0"
          >
            <span>DBA负责人：</span>
            <span v-if="cardData.dba_leader.length > 0">
              <span>
                <a-tooltip>
                  <template slot="title">
                    <span>{{ cardData.dba_leader | handleTitle }}</span>
                  </template>
                  <span>{{ cardData.dba_leader | handleContent }}</span>
                </a-tooltip>
              </span>
            </span>
          </a-tag>
        </div>

        <div class="side-info-box">
          <span class="avatar-part">
            <a-avatar icon="user" />
          </span>
          <a-tooltip v-if="cardData.ch_creater">
            <template slot="title">
              <span>{{ cardData.created_by }}</span>
            </template>
            <span class="created-by">{{ cardData.ch_creater || '--' }}</span>
          </a-tooltip>
          <span class="created-by" v-else>{{ cardData.created_by }}</span>
          <span>于{{ cardData.latest_created_at + ' ' }}</span>
          <span class="event">发起审核</span>
        </div>
      </div>
      <div class="right-block">
        <div class="right-block-count">
          <span>待审核</span>
          <span class="wait-audit">{{ cardData.wait_audit_count }}</span>
        </div>
        <a-divider type="vertical" />
        <div class="right-block-count">
          <span>白名单</span>
          <span class="white-list">{{ cardData.white_count }}</span>
        </div>
        <a-divider type="vertical" />
        <div class="right-block-count">
          <span>整改中</span>
          <span class="rectifying">{{ cardData.rectifying_count }}</span>
        </div>
        <!-- <a-divider type="vertical" />
        <div class="right-block-count">
          <span>已失效</span>
          <span>{{ cardData.expired_count }}</span>
        </div> -->
        <a-divider class="right-block-divider" type="vertical" />
        <div class="right-block-botton">
          <div class="btns-wrapper-nomal">
            <a @click="aKeyPass" :disabled="cardData.wait_audit_count <= 0">一键通过</a>
            <a @click="reject" :disabled="cardData.wait_audit_count <= 0"> 一键驳回 </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';
import StatusTag from '@/components/Biz/Status/Tag';
export default {
  components: { LimitLabel, StatusTag },
  props: {
    cardData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  computed: {
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  created() {},
  methods: {
    // 详情
    toDetail() {
      this.$emit('toDetail');
    },
    aKeyPass() {
      this.$emit('aKeyPass');
    },
    reject() {
      this.$emit('reject');
    }
  },
  filters: {
    handleTitle(data) {
      return data.join();
    },
    handleContent(data) {
      const res = data && data.length > 1 ? data[0] + '...' : data[0];
      return res;
    }
  }
};
</script>

<style lang="less" scoped>
.card-table {
  .card-table-main-info {
    display: flex;
    justify-content: space-between;
    .left-block {
      display: flex;
      flex-direction: column;
      margin-right: 32px;
      .project-name {
        margin-bottom: 8px;
        &:hover {
          cursor: pointer;
        }
        a {
          font-family: PingFangSC-Semibold;
          font-size: 14px;
          color: #4db5f2;
          font-weight: 600;
        }
      }
      .info-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .status-box > span,
        > span {
          margin-right: 28px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          font-weight: 400;
        }
        /deep/.status-box {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          .datasource-content {
            display: flex;
            align-items: center;
            > div {
              display: flex;
              align-items: center;
              margin-left: 8px;
              > span {
                font-family: PingFangSC-Regular;
                font-size: 12px;
                border-radius: 4px;
                margin: 0 4px 0 0;
                padding: 0 4px;
              }
              .test {
                background: #f6ffed;
                border: 1px solid rgba(183, 235, 143, 1);
                color: #52c41a;
              }
              .prod {
                background: #fff7e6;
                border: 1px solid rgba(255, 213, 145, 1);
                color: #fa8c16;
              }
              .limit-label {
                width: 120px;
              }
            }
          }
          .ai-review {
            .status-finish {
              color: #52c41a !important;
              background: #f6ffed;
              border: 1px solid rgba(183, 235, 143, 1);
            }
          }
        }
      }
      .tag-box {
        margin: 12px 0;
        display: flex;
        flex-wrap: wrap;
        .ant-tag {
          margin-bottom: 4px;
        }
        .tooltip:hover {
          cursor: pointer;
        }
      }
      .side-info-box {
        display: flex;
        align-items: center;
        > span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          letter-spacing: 0;
          font-weight: 400;
          &.avatar-part {
            width: 20px;
            .ant-avatar {
              width: 20px;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              margin-bottom: 2px;
              color: #fff;
              background: #4ec3f5;
            }
          }
        }
        .created-by {
          margin: 0 4px 0 8px;
          color: rgb(113, 113, 122);
        }
        .event {
          color: rgb(113, 113, 122);
        }
        .audit {
          color: #71717a;
          margin-left: 12px;
          white-space: nowrap;
        }
      }
    }
    .right-block {
      display: flex;
      align-items: center;
      .ant-divider-vertical {
        width: 1px;
        height: 32px;
        margin: 0 12px;
      }
      .right-block-count {
        padding: 0 18px;
        display: flex;
        flex-direction: column;
        text-align: center;
        span:first-child {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #8c8c8c;
          letter-spacing: 0;
          font-weight: 400;
          margin-bottom: 8px;
          white-space: nowrap;
        }
        span:last-child {
          font-family: PingFangSC-Regular;
          font-size: 24px;
          color: #bfbfbf;
          letter-spacing: 0;
          font-weight: 500;
          &.wait-audit {
            color: #1f1f1f;
          }
          &.white-list {
            color: #52c41a;
          }
          &.rectifying {
            color: #1677ff;
          }
        }
      }
      .right-block-botton {
        padding-left: 18px;
        width: 160px;
        display: flex;
        align-items: flex-start;
        .btns-wrapper-nomal {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          a {
            margin: 6px 12px 8px 0;
          }
        }
        a {
          height: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #4db5f2;
          letter-spacing: 0;
          line-height: 16px;
          font-weight: 400;
          display: flex;
          align-items: center;
          > .anticon {
            margin-right: 4px;
          }
        }
        a[disabled] {
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .card-table {
    .card-table-main-info {
      .right-block {
        display: flex;
        justify-content: space-between;
        .right-block-divider {
          display: none;
        }
        .right-block-botton {
          width: 160px;
        }
      }
      .left-block {
        .info-box {
          .project-name {
            width: 100%;
            > span {
              max-width: 300px;
            }
          }
        }
      }
    }
  }
}
</style>