<template>
  <div class="content page-list-with-tabs">
    <div class="content-tabs">
      <a-tabs :active-key="reportTab" @change="tabChange">
        <a-tab-pane key="beforeHand" tab="事前审核">
          <TableList :reportTab="reportTab" ref="beforeHand"></TableList>
        </a-tab-pane>
        <a-tab-pane key="afterWards" tab="事后审核">
          <TableList :reportTab="reportTab" ref="afterWards"></TableList>
        </a-tab-pane>
      </a-tabs>
      <div class="frame-button-wrapper">
        <a-button type="primary" @click="download">导出</a-button>
      </div>
    </div>
  </div>
</template>
<script>
import TableList from './TableList';
import config from './config';
import common from '@/utils/common';
import { reportDetailDownload } from '@/api/report';

export default {
  components: { TableList },
  props: {},
  data() {
    this.config = config(this);
    // （管理在 localstorage 中  db_type）
    let reportTab = window.localStorage.getItem('reportTab') || '';
    if (!reportTab) {
      window.localStorage.setItem('reportTab', 'beforeHand');
      reportTab = 'beforeHand';
    }
    return {
      time_range: '',
      reportTab: 'beforeHand' // 默认选中 tab
    };
  },
  created() {},
  mounted() {},
  methods: {
    download() {
      let searchData = {};
      if (this.reportTab === 'beforeHand') {
        searchData = this.$refs.beforeHand.$refs.PageList.$refs.table.getFinalSearchParams();
      } else {
        searchData = this.$refs.afterWards.$refs.PageList.$refs.table.getFinalSearchParams();
      }
      this.$showLoading({
        tips: `下载中...`
      });
      reportDetailDownload(
        { time_range: searchData.time_range || '' },
        this.reportTab
      )
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    // 切换 tab 选项卡
    tabChange(activeKey) {
      this.reportTab = activeKey;
      // 缓存状态
      window.localStorage.setItem('reportTab', activeKey);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.search-area {
  .form {
    .ant-form-item-label {
      max-width: 130px !important;
    }
  }
}
</style>
