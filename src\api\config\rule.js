import Http from '@/utils/request';

export function changeStatus(data = {}) {
  return Http({
    url: `/sqlreview/project/rule`,
    method: 'post',
    data: data
  });
}

export function ruleDelete(data = {}) {
  return Http({
    // url: `/sqlreview/project/rule-new`,
    url: `/sqlreview/project/rule`,
    method: 'delete',
    data: data
  });
}

export function ruleAdd(data = {}) {
  return Http({
    // url: `/sqlreview/project/rule-new/add`,
    url: `/sqlreview/project/rule/add`,
    method: 'post',
    data: data
  });
}

export function ruleDMLAdd(data = {}) {
  return Http({
    url: `/sqlreview/project/rule/${data.id}/`,
    method: 'post',
    data: data
  });
}

export function ruleDDLAdd(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_ddl_update`,
    method: 'post',
    data: data
  });
}

export function ruleDetail(data = {}) {
  return Http({
    url: `/sqlreview/project/rule/${data.id}/`,
    method: 'get',
    params: {}
  });
}

// dml详情新接口
export function newRuleDMLDetail(data = {}) {
  return Http({
    url: `/sqlreview/project/rule-new/${data.id}/`,
    method: 'get',
    params: {}
  });
}

export function ruleDDLDetail(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_ddl_rule_detail`,
    method: 'get',
    params: data
  });
}

export function ruleEdit(data = {}) {
  return Http({
    // url: `/sqlreview/project/rule-new/${data.id}/`,
    url: `/sqlreview/project/rule/${data.id}/`,
    method: 'post',
    data: data
  });
}

/**
 * 规则集相关接口
 */
// 新增规则集
export function ruleSetAdd(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_set`,
    method: 'post',
    data: data
  });
}

// 新增DDL规则集
export function ruleSetDDLAdd(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_add_ddl_set`,
    method: 'post',
    data: data
  });
}

// 删除规则集
export function ruleSetDelete(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_set?id=${data.id}`,
    method: 'delete',
    params: {}
  });
}

// DDL删除规则集
export function ruleSetDDLDelete(data = {}) {
  return Http({
    url: `/sqlreview/project/ddl_rule_set_delete`,
    method: 'get',
    params: data
  });
}

// 规则集状态改变
export function ruleSetStatus(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_set`,
    method: 'put',
    data: data
  });
}

// DDL规则集状态改变
export function ruleSetDDLStatus(data = {}) {
  return Http({
    url: `/sqlreview/project/ddl_rule_set_button`,
    method: 'get',
    params: data
  });
}

// 规则集查看
export function ruleSetDetail(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_set?id=${data.id}`,
    method: 'get',
    params: {}
  });
}

// DDL规则集查看
export function ruleSetDDLDetail(data = {}) {
  return Http({
    url: `/sqlreview/project/ddl_rule_set_detail`,
    method: 'get',
    params: data
  });
}
// 规则集修改
export function ruleSetEdit(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_set`,
    method: 'put',
    data: data
  });
}

// DDL规则集修改
export function ruleSetDDLEdit(data = {}) {
  return Http({
    url: `/sqlreview/project/ddl_rule_set_update`,
    method: 'post',
    data: data
  });
}

// 规则集简易数据接口
export function ruleSetAll(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_set_all`,
    method: 'get',
    params: {}
  });
}

// DDL规则保存
export function addDdlRule(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_add_ddl_rule`,
    method: 'post',
    data: data
  });
}

// 新的规则详情接口
export function newRuleDetail(data = {}) {
  return Http({
    url: `/sqlreview/project/ddl_dml_rule_detail`,
    method: 'get',
    params: data
  });
}
// 新的规则修改接口
export function newRuleEdit(data = {}) {
  return Http({
    url: `/sqlreview/project/ddl_dml_rule_update`,
    method: 'post',
    data: data
  });
}

// 新的规则删除接口
export function newRuleDelete(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_delete`,
    method: 'post',
    data: data
  });
}

// 获取DDL规则集
export function getRuleSetList(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_set_list`,
    method: 'get',
    params: data
  });
}

// 获取DML规则集
export function getDMLRuleSetList(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_set_all`,
    method: 'get',
    params: data
  });
}

// 获取DML 快捷指令
export function getTargetList(data = {}) {
  return Http({
    url: `/sqlreview/project/rule-new/get_target_list`,
    method: 'post',
    data: data
  });
}

// 规则导出
export function exportRule(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_export`,
    method: 'post',
    data,
    timeout: 60000 * 3,
    responseType: 'blob'
  });
}

// 规则导入
export function importRule(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_import`,
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'multipart/form-data' }
  });
}

// 下载错误报告
export function downloadErrorReport(data = {}) {
  return Http({
    url: `/sqlreview/project/rule_import`,
    method: 'get',
    params: data,
    timeout: 60000 * 3,
    responseType: 'blob'
  });
}
export default {};
