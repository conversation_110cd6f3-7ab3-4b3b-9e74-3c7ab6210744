<template>
  <Form ref="form" v-bind="formParams" :fields="fields" :formData="formData">
    <div slot="source_ext_includes">
      <a-button size="small" @click="onChooseAll(true)">全选</a-button>
      <a-button size="small" @click="onChooseAll(false)">取消全选</a-button>
      <div class="ext-includes-wrapper">
        <div
          :class="`ext-includes-item ${includes.includes(item.value) && 'checked'}`"
          v-for="item in formData.source_ext.data || []"
          :key="item.key"
          @click="onChooseItem(item)"
        >
          <custom-icon
            type="check-circle"
            :theme="includes.includes(item.value) ? 'filled' : 'outlined'"
          />
          <span>{{item.label}}</span>
        </div>
      </div>
    </div>
  </Form>
</template>

<script>
import Form from '@/components/Form';
import config from './config';

export default {
  props: {
    type: String,
    fields: Array,
    data: {
      type: Object,
      default: () => {
        return {
          source_ext: {}
        };
      }
    }
  },
  components: { Form },
  data() {
    this.config = config(this);
    return {
      // 右侧表单
      formParams: {
        layout: 'vertical'
      },
      formData: this.data,
      // arrmerge类型包含keys
      includes: []
    };
  },
  computed: {},
  created() {
    this.initData(this.data);
  },
  mounted() {},
  methods: {
    initData(formData = {}) {
      this.formData = formData;
      const { deal_type: dealType, _includes } = formData.source_ext || {};
      if (['ArrayMerge', 'BACK'].includes(dealType) && _includes) {
        const arr = _includes.split(',').map(item => item);
        this.includes = arr;
      }
    },
    onChooseItem(item) {
      if (this.includes.find(itm => itm == item.value)) {
        this.includes = this.includes.filter(itm => itm != item.value);
      } else {
        this.includes = [...this.includes, item.value];
      }
    },
    onChooseAll(flag) {
      const arr = _.get(this.formData, 'source_ext.data') || [];
      this.includes = flag ? arr.map(item => item.value) : [];
    },
    getData() {
      const ext = {};
      const { deal_type: dealType } = this.formData.source_ext || {};
      switch (dealType) {
        case 'DefaultShow':
        case 'DefaultHide':
          ext._show = this.formData.source_ext_show ? 1 : 0;
          break;
        case 'ArrayMerge':
        case 'BACK':
          const includes = this.includes;
          ext._includes = includes.length > 0 ? includes.join(',') : '';
          break;
        // case 'BACK':
        //   try {
        //     Object.assign(
        //       ext,
        //       JSON.parse(this.formData.source_ext_backParams || '{}')
        //     );
        //   } catch (e) {
        //     console.log(e);
        //   }
        //   break;
        default:
          break;
      }
      return ext;
    }
  },
  watch: {
    data: {
      handler(newVal = {}, oldVal) {
        this.initData(newVal);
      }
      // immediate: true
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.ext-includes-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #eef2fb;
  .ext-includes-item {
    padding: 8px 8px 8px 16px;
    margin-bottom: 8px;
    box-shadow: 1px 1px 6px 0px #d9d9d9;
    width: 49%;
    cursor: pointer;
    color: @font-color-weak;
    font-size: 12px;
    border-radius: 2px;
    border: 1px solid transparent;
    &:hover {
      // box-shadow: 1px 1px 6px 0px @primary-color;
      color: @font-color-normal;
    }
    &.checked {
      border-color: #b8d8a8;
      color: @font-color-normal;
      > .anticon {
        color: #52c41a;
      }
    }

    > .anticon {
      padding-right: 4px;
    }
  }
}
</style>
