export default function (ctx) {
  const beforeHandColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '应用ID/应用名称',
      dataIndex: 'project_info',
      key: 'project_info',
      scopedSlots: { customRender: 'project_info' }
      // customRender: (text, record, index) => {
      //   return record.project_info.project_id + '/' + record.project_info.name;
      // },
      // width: 150
    },
    {
      title: '版本名称',
      dataIndex: 'point',
      key: 'point'
      // customRender: (text, record, index) => {
      //   return record.project_info.point;
      // }
      // width: 120
    },
    {
      title: '数据库类型',
      dataIndex: 'data_source_type',
      key: 'data_source_type',
      scopedSlots: { customRender: 'data_source_type' },
      width: 180
    },
    {
      title: '审核结果',
      dataIndex: 'ai_status',
      key: 'ai_status',
      width: 100,
      scopedSlots: { customRender: 'ai_status' }
    },
    {
      title: '规则触发级别',
      dataIndex: 'level_category',
      key: 'level_category',
      // width: 200,
      scopedSlots: { customRender: 'level_category' }
    },
    {
      title: '触发规则名称',
      dataIndex: 'ai_comment',
      key: 'ai_comment',
      // width: 120,
      scopedSlots: { customRender: 'ai_comment' }
    }
  ];
  const afterWardsColumns = [
    {
      title: 'SQLID',
      dataIndex: 'sql_id',
      key: 'sql_id'
      // width: 100
    },
    {
      title: '应用ID/应用名称',
      dataIndex: 'project_info',
      key: 'project_info',
      customRender: (text, record, index) => {
        return record.project_info.project_id + '/' + record.project_info.name;
      }
      // width: 100
    },
    {
      title: '数据库类型/名称',
      dataIndex: 'data_source_info',
      key: 'data_source_info',
      customRender: (text, record, index) => {
        return (
          record.data_source_info.type + '/' + record.data_source_info.name
        );
      }
      // width: 150
    },
    {
      title: '规则触发级别',
      dataIndex: 'level_category',
      key: 'level_category',
      // width: 200,
      scopedSlots: { customRender: 'level_category' }
    },
    {
      title: '触发规则名称',
      dataIndex: 'ai_comment',
      key: 'ai_comment',
      // width: 200,
      scopedSlots: { customRender: 'ai_comment' }
    },
    {
      title: '首次捕获时间',
      dataIndex: 'begin_time',
      key: 'begin_time',
      // width: 100,
      customRender: (text, record, index) => {
        return record.time_info.begin_time;
      }
    },
    {
      title: '最后捕获时间',
      dataIndex: 'end_time',
      key: 'end_time',
      // width: 100,
      customRender: (text, record, index) => {
        return record.time_info.end_time;
      }
    },
    {
      title: '执行用户',
      dataIndex: 'schema',
      key: 'schema'
      // width: 120
    }
  ];
  const fields = () => {
    return [
      {
        type: 'RangePicker',
        label: '时间范围选择',
        key: 'time_range',
        mainSearch: true,
        props: {
          showTime: {
            format: 'HH:mm:ss'
          }
        },
        listeners: {
          ok: (value) => {}
        }
      }
    ];
  };
  return {
    beforeHandColumns,
    afterWardsColumns,
    fields
  };
}
