<template>
  <div :class="className" :style="{ height: option.visibleHeight + 'px' }">
    <a-input-search
      placeholder="请搜索"
      @change="onSearchChange"
      v-if="allowSearch"
      v-model="localSearchValue"
    ></a-input-search>
    <div class="virtual-tree" @scroll="handleScroll" v-show="data && data.length > 0">
      <div class="vt-phantom" :style="{ height: contentHeight + 'px' }"></div>
      <div ref="content" class="vt-content" :style="{ transform: `translateY(${offset}px)` }">
        <div
          v-for="(item, index) in visibleData"
          :key="item.key"
          :level="item.level"
          :class="{
            'vt-item': true,
            'item-selected': item.selected,
            'item-disabled': item.disabled,
            ...(item.className || {}),
            ...(itemProps(item).class || {})
          }"
          :style="{
            lineHeight: option.itemHeight + 'px',
            height: option.itemHeight + 'px',
            paddingLeft: indent * (item.level - 1) + (isSingleLevel ? indentOffset - 12 : indentOffset) + 'px',
            ...(itemProps(item).style || {})
          }"
          v-bind="itemProps(item)"
          @click="nodeClick(item)"
        >
          <div
            :class="['virtual-tree-icon', !item.loading && item.expand && 'expand']"
            @click="toggleExpand($event, item, index)"
            :style="{ left: indent * (item.level - 1) + (indentOffset - 20) + 'px' }"
            v-show="!isSingleLevel"
          >
            <a-icon :type="item.loading ? 'loading' : expandIcon" v-if="item.is_leaf !== true"></a-icon>
          </div>
          <a-checkbox
            v-if="checkable && item.checkable !== false"
            style="margin-right:8px;"
            :checked="item.checked"
            :indeterminate="!item.checked && item.half_checked"
            :disabled="item.disableCheckbox"
            @change="(e) => onCheck(item, e)"
            @click.stop="() => {}"
          ></a-checkbox>
          <slot name="title" :item="item" :index="index"></slot>
        </div>
      </div>
    </div>
    <custom-empty style="padding: 12px;" v-if="!data || data.length <= 0" />
  </div>
</template>

<script>
export default {
  inheritAttrs: false,
  name: 'VirtualTree',
  model: {
    prop: 'tree',
    event: 'update'
  },
  components: {},
  props: {
    tree: {
      // 树的数据源（树形结构）
      type: Array,
      default: () => []
    },
    defaultExpand: {
      // 是否默认展开
      type: Boolean,
      required: false,
      default: false
    },
    expandIcon: {
      type: String,
      default: 'caret-right'
    },
    option: {
      // 配置对象
      type: Object,
      required: true
      // default: {
      //   visibleHeight: 0,     // 可视区域的高度
      //   itemHeight: 25,       // 单个item的高度
      //   // 若滚动元素不是自身，需要提供下面两个属性
      //   flexHeight: 0,        // 顶部浮动的高度
      //   scrollDom: null       // 滚动元素的节点
      // }
    },
    itemProps: {
      // item属性扩展
      type: Function,
      required: false,
      default: () => ({})
    },
    // 异步加载回调
    onLoad: Function,
    // 是否可勾选
    checkable: {
      type: Boolean,
      default: false
    },
    // 勾选是否受控
    checkStrictly: {
      type: Boolean,
      default: true
    },
    // 允许搜索
    allowSearch: {
      type: Boolean,
      default: true
    },
    searchValue: String
    // autoHeight: {
    //   type: Boolean,
    //   default: false
    // }
  },
  data() {
    this.search = _.debounce(() => {
      this.handleSearch();
    }, 300);
    const { option = {} } = this;
    return {
      data: this.tree,
      flexCount: 20, // 修正个数
      indent: option.indent || 18, // 缩进
      indentOffset: option.indentOffset || 32, // 缩进偏移值
      offset: 0, // translateY偏移量
      visibleData: [], // 可视数据列表
      contentHeight: 0, // 容器高度
      selectNode: null, // 选中节点
      checkedKeys: [], // 勾选keys
      localSearchValue: this.searchValue || '' // 搜索值
    };
  },
  computed: {
    className() {
      const { option } = this;
      let res = ['virtual-tree-wrapper'];

      if (!option.visibleHeight) {
        res.push('autoHeight');
      }

      return res;
    },
    flattenTree() {
      const flatten = (
        tree,
        list = [],
        level = 0,
        parent = { children: tree }
      ) => {
        if (tree && tree instanceof Array) {
          ++level;
          tree.forEach(item => {
            if (item.expand === undefined) {
              item.expand = this.defaultExpand;
            }
            if (item.visible === undefined) {
              if (this.defaultExpand || level === 1) {
                item.visible = true;
              } else {
                item.visible = false;
              }
            }
            item.level = level;
            item.parent = parent;
            list.push(item);
            if (item.children) {
              flatten(item.children, list, level, item);
            }
          });
        }
        return list;
      };
      return flatten(this.data);
    },
    isSingleLevel() {
      const { flattenTree } = this;
      if (this.onLoad) {
        return false;
      }
      let flag = true;
      flattenTree.forEach(item => {
        if (item.level > 1) {
          flag = false;
          return false;
        }
      });
      return flag;
    }
  },
  mounted() {
    this.scrollContainer = this.$el.querySelector('.virtual-tree');
    this.updateView();
    window.addEventListener('resize', this.resize);
  },
  methods: {
    // 处理滚动
    handleScroll() {
      // 如果当前滚动元素不是当前组件，则需要传入scrollDom
      if (this.option.scrollDom) {
        const scrollTop = this.option.scrollDom.scrollTop;
        const diff =
          scrollTop - (this.scrollContainer.offsetTop - this.option.flexHeight);
        this.updateVisibleData(diff > 0 ? diff : 0);
      } else {
        const scrollTop =
          this.scrollContainer.scrollTop <= this.contentHeight
            ? this.scrollContainer.scrollTop
            : this.contentHeight;
        this.updateVisibleData(scrollTop);
      }
    },
    // 更新可视数据
    updateVisibleData(scrollTop = 0) {
      // console.log(scrollTop);
      const that = this;
      const visibleHeight = this.option.visibleHeight
        ? this.option.visibleHeight
        : this.scrollContainer.clientHeight;
      const flexCount = this.option.flexCount
        ? this.option.flexCount
        : this.flexCount;
      let start = Math.floor(scrollTop / this.option.itemHeight);
      let end =
        start + Math.ceil(visibleHeight / this.option.itemHeight) + flexCount;
      const allVisibleData = (this.flattenTree || []).filter(
        item => item.visible
      );
      // let above = start - flexCount < 0 ? 0 : start - flexCount;
      // let below =
      //   end + flexCount > allVisibleData.length
      //     ? allVisibleData.length
      //     : end + flexCount;
      // console.log(visibleHeight, start, end);
      that.visibleData = allVisibleData.slice(start, end);
      that.offset = start * that.option.itemHeight;
      this.$nextTick(() => {
        // 避免动画渲染问题
        that.$emit('update-visible-data', {
          visibleData: that.visibleData,
          offset: that.offset
        });
      });
    },
    // 更新容器高度
    updateContentHeight() {
      this.contentHeight =
        (this.flattenTree || []).filter(item => item.visible).length *
        this.option.itemHeight;
    },
    // 窗口变化，需要做的处理
    resize(duration) {
      if (!this.timer) {
        // 避免频繁触发
        this.timer = true;
        const that = this;
        setTimeout(function() {
          that.$emit('resize');
          that.handleScroll();
          that.timer = false;
        }, duration);
      }
    },
    // 强制刷新
    updateView() {
      const that = this;
      this.updateContentHeight();
      this.$emit('update', this.data);
      this.$nextTick(() => {
        that.handleScroll();
      });
    },
    // 点击节点
    nodeClick(item) {
      // const recursionSelect = function(children, value) {
      //   children &&
      //     children.forEach(node => {
      //       node.select = value;
      //       recursionSelect(node.children, value);
      //     });
      // };
      // recursionSelect(this.flattenTree, false);
      // recursionSelect([item], true);
      if (!item || item.selectable === false) {
        return;
      }
      if (this.selectNode) {
        this.selectNode.selected = false;
      }
      item.selected = true;
      this.selectNode = item;
      this.updateView();
      this.$emit('node-click', item);
    },
    // 展开全部
    toggleExpandAll(state, level = 1) {
      const that = this;
      let expandNodes = []; // 待展开/折叠的节点
      let rootNodes = []; // 父级节点（直到根节点）
      // 1. 找到对应节点
      this.flattenTree.forEach(item => {
        if (item.level === level) {
          expandNodes.push(item);
        }
        if (item.level < level) {
          rootNodes.push(item);
        }
      });
      // 2. 展开/折叠节点
      expandNodes.forEach(item => {
        if (state) {
          that.expand(item, false);
        } else {
          that.collapse(item, false);
        }
      });
      // 3.展开父级节点
      rootNodes.forEach(item => {
        that.expand(item, true);
      });
      this.updateView();
    },
    // 切换节点展开/折叠
    async toggleExpand(e, item, index) {
      if (item.is_leaf) return;
      e && e.stopPropagation();
      const isExpand = item.expand;
      if (isExpand) {
        this.collapse(item, true); // 折叠
      } else {
        if (!(item.children && item.children.length > 0) && this.onLoad) {
          item.loading = true;
          this.updateView();
          // 加载子节点
          let children = [];
          try {
            children = await this.onLoad(item);
          } catch (error) {
            error.message && this.$message.error(error.message);
            console.log(error);
          }
          item.loading = false;
          this.$set(item, 'children', children || []);
        }
        this.expand(item, true); // 展开
      }
      this.updateView();
      !isExpand && this.$emit('node-expand', e, item, index);
    },
    // 展开节点
    expand(item, isKeep = true) {
      const recursionVisible = function(children) {
        children.forEach(node => {
          if (!isKeep) {
            node.expand = true;
          }
          node.visible = true;
          if (node.expand && node.children) {
            recursionVisible(node.children);
          }
        });
      };
      item.expand = true;
      item.children &&
        item.children.forEach(node => {
          if (!isKeep) {
            node.expand = true;
          }
          node.visible = true;
          node.expand && node.children && recursionVisible(node.children);
        });

      if (this.localSearchValue) {
        this.handleSearch();
      }
    },
    // 折叠节点
    collapse(item, isKeep = true) {
      const recursionVisible = function(children) {
        children.forEach(node => {
          if (!isKeep) {
            node.expand = false;
          }
          node.visible = false;
          if (node.children) {
            recursionVisible(node.children);
          }
        });
      };
      item.expand = false;
      item.children && recursionVisible(item.children);
    },
    // 勾选节点
    onCheck(item, e, params = {}) {
      const { checkedValue, force, loopChildren, loopParent } = params;
      e && e.stopPropagation();
      let checked = checkedValue != null ? checkedValue : e.target.checked;
      let { checkStrictly, checkedKeys: arr } = this;

      const recursionCheckChildren = function(children, value) {
        children &&
          children.forEach(node => {
            if (node.disableCheckbox && force !== true) return;
            node.checked = value;
            node.half_checked = checked;
            // 回调
            loopChildren && loopChildren(node);
            // 统计
            dealCheckedKeys(node, value);
            recursionCheckChildren(node.children, value);
          });
      };
      const recursionCheckParent = parent => {
        if (parent) {
          if (parent.disableCheckbox && force !== true) return;
          let childrenLen = parent.children.filter(
            item =>
              item.disableCheckbox !== true || item.checked || item.half_checked
          ).length;
          let checkedChildren = parent.children.filter(item => item.checked);
          let halfCheckedChildren = parent.children.filter(
            item => item.half_checked
          );
          parent.checked =
            checkedChildren.length > 0 &&
            checkedChildren.length === childrenLen;
          parent.half_checked =
            checkedChildren.length + halfCheckedChildren.length > 0 &&
            (checkedChildren.length === 0 ||
              checkedChildren.length < childrenLen);

          // 回调
          loopParent && loopParent(parent);
          // 统计
          dealCheckedKeys(parent, checkedChildren.length === childrenLen);
          recursionCheckParent(parent.parent);
        }
      };

      const dealCheckedKeys = (node, checked) => {
        if (checked) {
          arr.push(node.key);
        } else {
          arr = arr.filter(item => item != node.key);
        }
      };

      // 处理自己
      item.checked = checked;
      item.half_checked = checked;
      dealCheckedKeys(item, checked);
      if (checkStrictly) {
        // 子节点
        recursionCheckChildren(item.children, checked);
        // 父节点
        recursionCheckParent(item.parent);
      }
      this.updateView();
      this.checkedKeys = [...new Set(arr)];
      this.$emit('node-check', this.checkedKeys);
      // console.log(arr, this.checkedKeys, 8899);
    },
    /** 对树节点的操作 **/
    // 添加子节点
    append(item, parent) {
      if (!parent.children) {
        this.$set(parent, 'children', []);
      }
      parent.children.push(item);
      this.updateView();
    },
    // 添加兄弟节点
    insertAfter(item, node, isUpdate = true) {
      const index = node.parent.children.indexOf(node);
      node.parent.children.splice(index + 1, 0, item);
      isUpdate && this.updateView();
    },
    // 删除节点
    remove(item) {
      if (item.parent) {
        const index = item.parent.children.indexOf(item);
        item.parent.children.splice(index, 1);
        this.updateView();
      }
    },
    // 搜索
    onSearchChange(e) {
      this.localSearchValue = e.target.value
        ? e.target.value.trim()
        : e.target.value;
      this.search();
    },
    handleSearch() {
      this.$emit('search', this.localSearchValue);
    },
    // 自动展开父节点
    autoExpandParent(items = []) {
      this.flattenTree.forEach(item => (item.visible = false));
      items.forEach(item => (item.visible = true));

      const recursionVisible = function(parent) {
        if (parent) {
          parent.expand = true;
          parent.visible = true;

          recursionVisible(parent.parent);
        }
      };
      items.forEach(item => {
        item && recursionVisible(item.parent);
      });
      this.updateView();
    },
    getTreeData() {
      return this.data;
    },
    getFlattenTree() {
      return this.flattenTree;
    },
    getCheckedNodes(type = 'ALL') {
      return this.flattenTree.filter(item => item.checked || item.half_checked);
    },
    refresh(list = []) {
      this.data = list;
      this.$nextTick(() => {
        const selectedNode = this.flattenTree.find(item => item.selected);
        if (selectedNode) {
          this.nodeClick(selectedNode);
        }
        this.updateView();
      });
    }
  },
  watch: {
    tree(newVal = []) {
      this.data = newVal;
    },
    searchValue(newVal) {
      this.localSearchValue = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
.virtual-tree-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e8e8e8;
  border-radius: 2px;

  /deep/ .ant-input-search,
  /deep/ .custom-input-search {
    input {
      border-radius: 2px;
      border: 0;
      border-bottom: 1px solid #e8e8e8;
    }
  }

  // 自适应高度
  &.autoHeight {
    height: 100% !important;
    display: flex;
    flex-direction: column;

    .virtual-tree {
      flex-grow: 1;
    }
  }
}
.virtual-tree {
  flex-grow: 1;
  overflow: auto;
  position: relative;
  -webkit-overflow-scrolling: touch;
}
.vt-phantom {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  z-index: -1;
}
.vt-content {
  left: 0;
  // right: 0;
  top: 8px;
  position: absolute;
  min-height: 100px;
  min-width: 100%;
}
.vt-item {
  padding: 5px 12px;
  box-sizing: border-box;
  display: flex;
  // justify-content: space-between;
  position: relative;
  align-items: center;
  cursor: pointer;
  white-space: nowrap;
}
.vt-item {
  &:hover {
    background-color: @tree-node-hover-bg;
  }

  &.item-selected {
    background-color: @tree-node-selected-bg !important;
  }
  &.item-disabled {
    .virtual-tree-icon {
      opacity: 0.3;
      pointer-events: none;
    }
  }
}
.virtual-tree-icon {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  color: #000000;
  font-size: 12px;
  z-index: 10;
  width: 18px;
  display: flex;
  align-items: center;
  > .anticon {
    transform-origin: 50%, 50%;
    transition: transform 0.3s;

    &.anticon-right {
      transform: scale(0.8);
    }
  }

  &.expand {
    > .anticon {
      transform: rotate(90deg);
      &.anticon-right {
        transform: scale(0.8) rotate(90deg);
      }
    }
  }
}
</style>