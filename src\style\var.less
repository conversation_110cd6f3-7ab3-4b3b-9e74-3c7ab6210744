@import '~ant-design-vue/lib/style/themes/default.less';

// 主色渐变
@primary-background-image: linear-gradient(90deg, tint(@primary-5, 20%) 0%, @primary-6 100%);
@primary-background-image-reverse: linear-gradient(90deg, @primary-6 0%, tint(@primary-5, 20%) 100%);

// 背景颜色
@bg-color: #F9FAFA;

// 字体颜色old
@font-color: #414d6a;
// 字体颜色
@font-color-normal: rgba(0,0,0,0.65);
@font-color-strong: rgba(0,0,0,0.85);
@font-color-weak: rgba(0,0,0,0.45);
// 字体加粗
.font-bold() {
  font-weight: 500;
}
// 字号
.font-size(@size) {
  font-size: @size + (@font-size-base - 14px);
}

// 颜色old
@primary-green: #23be6c;
@primary-red: #e26148;

// 状态
@status-success: #389E0D;
@status-error: #D9363E;
@status-warning: #FAAD14;
@status-pending: #5E1BE4;
@status-end: #D9D9D9;

// border
@border-color: rgba(0, 0, 0, 0.06);
.border(@top, @right, @bottom, @left) {
  border-top: @top solid @border-color;
  border-right: @right solid @border-color;
  border-bottom: @bottom solid @border-color;
  border-left: @left solid @border-color;
}

// form横向布局文本宽度范围
.formRangeLabel(@min: auto, @max: auto) {
  .ant-form-item {
    display: flex !important;
    align-items: flex-start;
  }
  .ant-form-item-label {
    max-width: @max;
    min-width: @min;
  }
  .ant-form-item-control-wrapper {
    flex-grow: 1;
    // flex: auto;
    // width: auto;
  }
}

// 抽屉内容宽度范围
.drawer-content-width(@min: auto, @max: auto) {
  .ant-drawer-content-wrapper {
    max-width: @max;
    min-width: @min;
  }
}

// 框架顶部左侧固定
.frame-fixed-top-left(@top, @left) {
  position: absolute;
  left: @left;
  top: @top;
  z-index: 10;
  background: transparent;
}

// radio,checkbox等hover,选中配色
@checked-border-color: @primary-5;
@checked-bg: @primary-1;

@import '~@/style/private/var.less';