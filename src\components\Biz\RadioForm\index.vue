<template>
  <div :class="['radio-form', !active && 'is-empty']">
    <div class="radio-wrapper">
      <a-radio-group :value="active" :options="options" @change="onChange"></a-radio-group>
    </div>
    <div class="form-wrapper">
      <template v-for="item in options">
        <keep-alive :key="item.value">
          <Form
            ref="form"
            :fields="realConfig.find(itm => itm.key === active).form"
            :formData="data"
            layout="vertical"
            v-if="item.value === active"
          ></Form>
        </keep-alive>
      </template>
    </div>
  </div>
</template>

<script>
import Form from '@/components/Form';
// import common from '@/utils/common';
// import _ from 'lodash';

export default {
  // inheritAttrs: false,
  // model: {
  //   prop: 'value',
  //   event: 'change'
  // },
  components: { Form },
  props: {
    value: {
      type: Object,
      default: () => {
        return {};
      }
    },
    config: {
      type: Array,
      default: () => {
        return [];
      }
    },
    defaultChoose: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const { config = [] } = this;
    const realConfig = _.merge([], config);
    const { options, active } = this.dealConfig(realConfig);

    return {
      options,
      active,
      data: {},
      realConfig: realConfig
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    dealConfig(config) {
      // radio的options
      let options = [];
      // 默认选择
      let active = null;

      // 遍历获取
      config.forEach(item => {
        options.push({
          label: item.label,
          value: item.key,
          disabled: item.disabled
        });
        active = item.active ? item.key : active;

        // 设置formItem key值
        (item.form || []).forEach(formItem => {
          const prefix = item.key + '_';
          if (!formItem.key.startsWith(prefix)) {
            formItem.key = prefix + formItem.key;
          }
        });
      });
      active = active || this.defaultChoose ? config[0].key : null;

      return {
        active,
        options
      };
    },
    onChange(e) {
      this.active = e.target.value;
    },
    // 获取数据
    getData() {
      const type = this.active;
      const { form } = this.$refs;
      const { $refs: formRefs } = form[0];
      // 过滤数据
      let filterData = {};
      this.realConfig
        .find(itm => itm.key === type)
        .form.forEach(item => {
          const _key = item.key.replace(type + '_', '');
          filterData[_key] = item.getDataMethod
            ? formRefs[item.key][0][item.getDataMethod]()
            : this.data[item.key];
        });

      return {
        type,
        formData: filterData
      };
    },
    resetFields() {
      const { form } = this.$refs;
      let resetValue = {};
      form[0].resetFields();
      // 遍历将类似tableEdit组件resetFields和设初始值
      this.realConfig.forEach(itm => {
        (itm.form || []).forEach(item => {
          const formItem = _.get(form[0].$refs, `${item.key}.0`);
          if (formItem && item.resetFieldsMethod) {
            formItem[item.resetFieldsMethod]();
          }
          if (item.initialValue) {
            resetValue[item.key] = item.initialValue;
          }
        });
      });
      this.data = { ...resetValue };
    },
    validate(cbk) {
      const { form } = this.$refs;
      return form[0].validate(cbk);
    }
  },
  watch: {
    config: {
      handler(newVal = [], oldVal) {
        const realConfig = _.merge([], newVal);
        const { options, active } = this.dealConfig(realConfig);
        this.realConfig = realConfig;
        this.options = options;
        this.active = active;
      }
      // deep: true
    },
    value: {
      handler(newVal = {}, oldVal) {
        const { type, formData = {} } = newVal;
        if (type == null) {
          return;
        }
        const matchItem = this.realConfig.find(item => item.key === type);
        if (!matchItem) {
          console.error('RadioForm: 未匹配到正确type，请检查！！！');
          return;
        }
        let realData = {};
        _.forEach(formData, (val, key) => {
          realData[type + '_' + key] = val;
        });
        this.active = type;
        this.data = Object.assign({}, oldVal, realData);
      },
      immediate: true
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.radio-form {
  padding: 12px;
  background: rgba(24, 144, 255, 0.06);
  border-radius: 4px;

  .radio-wrapper {
    padding: 8px 16px;
    background: #ffffff;
    border-radius: 4px;
    margin: 10px;

    /deep/ .ant-radio-wrapper {
      margin-bottom: 4px;
    }
  }
  .form-wrapper {
    padding: 16px;

    /deep/ .ant-form-item {
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
      }
    }
  }

  &.is-empty {
    .form-wrapper {
      padding: 0;
    }
  }
}
</style>
