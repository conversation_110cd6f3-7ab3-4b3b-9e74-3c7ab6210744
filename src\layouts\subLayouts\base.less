
#layout-root {
  height: 100%;
  min-width: 1024px;

  .ant-layout-sider {
    overflow: hidden;
    background: #001529;
    box-shadow: rgba(0, 21, 41, 0.35) 2px 0px 3px;
    // position: relative;
    // z-index: 1;
    padding: 60px 0 48px 0;
    position: fixed;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 12;
    /deep/ .ant-layout-sider-children {
      &::-webkit-scrollbar {
        width: 17px;
      }
      overflow-y: scroll;
      overflow-x: hidden;
      margin-right: -17px;
    }
    /deep/ .ant-layout-sider-trigger {
      position: absolute;
    }
    .logo {
      position: absolute;
      display: flex;
      align-items: center;
      color: #ffffff;
      padding: 8px 12px 8px 32px;
      z-index: 3;
      top: 0;
      line-height: 16px;
      height: 60px;
      > img {
        width: 16px;
        margin-right: 8px;
      }
      > span {
        font-weight: bold;
      }
    }

    &.menu-collapsed {
      .logo {
        > span {
          display: none;
        }
      }
      /deep/ .icon-jump-url {
        display: none;
      }
    }
  }

  .layout-part-right {
    // overflow: visible;
    padding: 64px 0 0 200px;
    transition: all 0.2s;

    &.menu-collapsed {
      padding: 64px 0 0 80px;
    }
  }

  header.ant-layout-header {
    padding: 0 20px;
    background: #fff;
    box-shadow: 0px 1px 4px rgba(0, 21, 41, 0.12);
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 11;
  }

  .ant-layout.content {
    // padding: 0 24px 24px;
    min-height: auto;
    display: flex;
    position: relative;
    padding: 0 24px;
    overflow: auto;
    width: 100%;

    .router-info {
      display: inline-block;
      opacity: 0.5;
      i {
        margin-top: -1px;
      }
      margin: 16px 0;
    }

    .icon-refresh {
      // position: absolute;
      // right: 24px;
      // top: 16px;
      // font-size: 16px;
      color: @primary-color;
      // border-color: #e8e8e8;
      margin-left: 16px;
      cursor: pointer;

      .anticon {
        margin-right: 8px;
      }
    }
    .navi-wrapper {
      display: flex;
      flex-shrink: 0;
      align-items: center;
    }

    .ant-layout-content {
      // min-height: calc(100vh - 166px);
      // display: inline-block;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      width: 100%;
      background: #fff;
      padding: 24px;

      border-radius: 2px;

      &.blank {
        background: transparent;
        padding: 0;
      }
    }

    .footer {
      height: 48px;
      line-height: 48px;
      text-align: center;
      // background: #f6f6f6;
    }

    // &.no-navi {
    //   .ant-layout-content {
    //     min-height: calc(100vh - 112px);
    //   }
    // }
  }

  // 内嵌模式
  &.frame-inset {
    min-width: 0;
    .ant-layout.layout-part-right {
      padding: 0;
    }
    header.ant-layout-header {
      display: none;
    }
    .ant-layout-sider {
      display: none;
    }
    .layout-sider-mask {
      display: none;
    }
    .footer {
      display: none;
    }

    &.no-navi {
      display: none;
    }

    .ant-layout.content {
      // position: fixed;
      // top: 0;
      // left: 0;
      // right: 0;
      // bottom: 0;
      // z-index: 10;
      // overflow: auto;
      // .ant-layout-content {
      // }
    }
  }

  // 内嵌模式（带菜单）
  &.frame-inset-with-menu {
    min-width: 0;

    .layout-part-right {
      padding: 0 0 0 130px;

      &.menu-collapsed {
        padding: 0 0 0 80px;
      }
    }

    .ant-layout-header {
      display: none;
    }
    .ant-layout-sider {
      padding-top: 53px;
      .logo {
        padding-left: 20px !important;
        height: 53px;
        width: 100%;
        border-bottom: 1px solid @primary-3;

        > img {
          width: 80px !important;
        }
      }
      // fk gd ic
      /deep/ .ant-menu-submenu-title {
        > span {
          > .anticon:first-child {
            display: none;
          }
        }
      }
      /deep/ .ant-menu-item {
        > .anticon:first-child {
          display: none;
        }
      }

      /deep/ .custom-menu {
        > .ant-menu-submenu {
          > .ant-menu-submenu-title {
            padding-left: 24px !important;
          }
        }
        > .ant-menu-item {
          padding-left: 24px !important;
        }
      }

      &.menu-collapsed {
        .logo {
          padding-left: 16px !important;
          > img {
            width: 50px !important;
          }
        }
        /deep/ .ant-menu-submenu-title {
          > span {
            > .anticon:first-child {
              display: inline-block !important;
            }
          }
        }
        /deep/ .ant-menu-item {
          > .anticon:first-child {
            display: inline-block !important;
          }
        }

        /deep/ .custom-menu {
          > .ant-menu-submenu {
            > .ant-menu-submenu-title {
              padding-left: 32px !important;
            }
          }
          > .ant-menu-item {
            padding-left: 32px !important;
          }
        }
      }
    }
  }
}