export default function(ctx) {
  const columns = [
    {
      title: '记录ID',
      dataIndex: 'id',
      key: 'id',
      width: 120
    },
    {
      title: '开始日期',
      dataIndex: 'start_date',
      key: 'start_date',
      width: 150
    },
    {
      title: '结束日期',
      dataIndex: 'end_date',
      key: 'end_date',
      width: 150
    },
    {
      title: '模板路径',
      dataIndex: 'template_path',
      key: 'template_path',
      width: 250,
      scopedSlots: { customRender: 'limit' }
    },
    {
      title: '报表文件名称',
      dataIndex: 'report_file_name',
      key: 'report_file_name',
      width: 250,
      scopedSlots: { customRender: 'fileName' }
    },
    {
      title: '邮件主题',
      dataIndex: 'email_subject',
      key: 'email_subject',
      width: 300
    },
    {
      title: '邮件收件人',
      dataIndex: 'email_send_to',
      key: 'email_send_to',
      width: 300
    },
    {
      title: '邮件抄送人',
      dataIndex: 'email_cc_to',
      key: 'email_cc_to',
      width: 300
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
      width: 140
    }
  ];
  const fields = [
    {
      type: 'RangePicker',
      label: '时间区间',
      mainSearch: true,
      key: 'start_date',
      props: {
        format: 'YYYY-MM-DD'
      }
    }
  ];
  return {
    columns,
    searchFields: fields
  };
}
