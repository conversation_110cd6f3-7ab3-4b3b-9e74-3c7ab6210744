<template>
  <div class="tab-router-view">
    <a-tabs :activeKey="activeKey" :animated="false" @change="onChange">
      <a-tab-pane :key="item.path" v-for="item in routeList">
        <span slot="tab">
          <span>{{ item.meta.desc }}</span>
        </span>
        <keep-alive>
          <router-view  v-if="activeKey === item.path"></router-view>
        </keep-alive>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      activeKey: '',
      routeList: []
    };
  },
  computed: {},
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    onChange(val) {
      this.activeKey = val;
      this.$router.push({ path: val });
    }
  },
  watch: {
    $route: {
      handler(to, from) {
        console.log(to, from, 8888);
        this.activeKey = to.path;
        if (!this.routeList.find(item => item.path === to.path)) {
          this.routeList.push(to);
        }
      },
      immediate: true
    }
  }
};
</script>
<style lang="less" scoped></style>
