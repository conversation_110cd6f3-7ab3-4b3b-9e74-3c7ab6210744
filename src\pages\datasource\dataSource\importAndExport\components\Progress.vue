<template>
  <div class="progress-page">
    <div class="text-content">
      <custom-icon type="loading" />
      <div>
        <div class="bold">{{type +'中'}}</div>
        <div class="dark">
          <span>任务进行中，稍后请在“</span>
          <a>历史记录</a>
          <span>”中查看{{type}}结果。</span>
        </div>
      </div>
    </div>
    <div class="btn-area">
      <a-button class="highlight" @click="importAgain">{{ '继续' + type}}</a-button>
    </div>
  </div>
</template>
<script>
// import config from './config';

export default {
  name: 'import-and-export',
  components: {},
  props: {
    type: String
  },
  data() {
    return {};
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    importAgain() {
      this.$emit('carryOn');
    }
  }
};
</script>

<style lang="less" scoped>
.progress-page {
  padding: 50px 80px;
  .text-content {
    display: flex;
    padding: 20px;
    align-items: center;
    background: #eff5ff;
    .anticon {
      font-size: 48px;
      color: #008adc;
      margin-right: 20px;
    }
    > div {
      .bold {
        font-size: 13px;
        color: #27272a;
        font-weight: 600;
        margin-bottom: 8px;
      }
      .dark {
        span {
          font-size: 13px;
          color: #a1a1a1;
          font-weight: 400;
        }
      }
    }
  }
  /deep/.btn-area {
    margin-top: 32px;
    display: flex;
    .ant-btn {
      display: flex;
      align-items: center;
      border: 1px solid #008adc;
      > span {
        padding: 0 24px;
        font-size: 14px;
        color: #008adc;
        font-weight: 600;
      }
    }
  }
}
</style>
