<template>
  <a-modal
    v-model="visible"
    width="50%"
    centered
    :mask="false"
    :footer="null"
    wrapClassName="review-total-info-modal"
    :maskStyle="{ pointerEvents: 'none' }"
    :dialogStyle="{ minWidth: '1100px', maxWidth: '1100px' }"
    @cancel="onCancel"
  >
    <template slot="title">
      <span>表结构信息</span>
      <a-icon type="drag" />
    </template>
    <a-spin
      :spinning="loading"
      class="table-info-content"
      v-if="treeData && treeData.length > 0"
    >
      <div class="table-list">
        <div v-for="item in treeData" :key="item.key">
          <div class="data-source">
            <custom-icon
              :type="item.expand ? 'down' : 'right'"
              @click="onExpand(item.key)"
            />
            <div class="instance-name">
              <a-tooltip slot="tab" placement="bottom">
                <template slot="title">
                  <div>{{ item.title }}</div>
                  <div>{{ item.db_url }}</div>
                </template>
                <DbImg
                  :value="item.db_type"
                  :schemaName="item.title"
                  :limit="16"
                />
                <span
                  :class="['text', item.env.toUpperCase() == 'TEST' && 'test']"
                  >{{
                    item.env.toUpperCase() == 'TEST' ? '测试' : '生产'
                  }}</span
                >
              </a-tooltip>
            </div>
            <a-tooltip>
              <template slot="title">数据源连接失败</template>
              <custom-icon
                type="lu-icon-notice"
                v-if="item.status == -1"
                class="lu-icon-notice"
              />
            </a-tooltip>
          </div>
          <div
            v-if="item.expand && item.children && item.children.length > 0"
            class="table-and-schema-container"
          >
            <div
              v-for="itm in item.children"
              :key="itm.key"
              @click="onSelect(item, itm)"
              :class="[
                'table-and-schema-content',
                itm.key == selectedKey && 'active'
              ]"
            >
              <custom-icon type="lu-icon-list" />
              <span class="schema-name">{{
                itm.schema + `.` + itm.title
              }}</span>
            </div>
          </div>
          <div
            class="table-and-schema-container empty"
            v-if="(item.expand && !item.children) || item.children.length <= 0"
          >
            未查询到关联表
          </div>
        </div>
      </div>
      <a-tabs default-active-key="table">
        <a-tab-pane key="table" tab="表信息">
          <Table v-bind="tableParams" :dataSource="tableData" bordered>
            <template slot="is_onlie" slot-scope="{ record }">
              <span>{{ record.is_onlie }}</span>
              <a-popover v-if="record.is_onlie === '未知'">
                <template slot="content">
                  <span>没有可用的生产数据库链接，无法获取到表上线状态。</span>
                </template>
                <a-icon type="question-circle" />
              </a-popover>
              <!-- :color="statusColor[record.dba_status]" -->
              <a-tooltip placement="bottom">
                <template slot="title"
                  >生效时间:{{ record.take_effect }} 失效时间:{{
                    record.expire
                  }}</template
                >
                <a-tag
                  v-if="record.is_white === 1"
                  color="rgba(35, 190, 108, 1)"
                  >已加入白名单</a-tag
                >
              </a-tooltip>
            </template>
            <template slot="comment" slot-scope="{ text }">
              <LimitLabel :label="text || ''" :limit="20"></LimitLabel>
            </template>
            <custom-btns-wrapper
              slot="action"
              slot-scope="{ text, record }"
              :limit="3"
            >
              <a
                @click="showAddModal(record)"
                :disabled="isVirtualLogin"
                actionBtn
                >申请表白名单</a
              >
            </custom-btns-wrapper>
          </Table>
        </a-tab-pane>
        <a-tab-pane key="index" tab="索引信息">
          <div
            class="table-name-wrapper"
            v-for="item in tableData"
            :key="item.table_name"
          >
            <!-- <div class="table-name">
              <span>表名：{{item.table_name}}</span>
            </div>-->
            <Table
              v-bind="indexParams"
              :columns="getColumns(item.table_name)"
              :dataSource="
                indexData.filter((itm) => itm.table_name === item.table_name)
              "
              bordered
            ></Table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="field" tab="字段信息">
          <Table v-bind="fieldParams" :dataSource="fieldData" bordered></Table>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
    <div v-else>
      <custom-empty />
    </div>
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import { getNewTableInfo, getReviewTableList } from '@/api/review';
import config from './config';

export default {
  components: { Table, LimitLabel, Tag, DbImg },
  props: {},
  data() {
    this.config = config(this);
    return {
      id: null,
      visible: false,
      loading: false,
      tableParams: {
        columns: this.config.tableColumns,
        // pagination: false,
        rowKey: 'id',
        scroll: { x: 'max-content' }
        // size: 'small'
      },
      tableData: [],
      indexParams: {
        columns: this.config.indexColumns(),
        // pagination: false,
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      indexData: [],
      table_name: null,
      fieldParams: {
        columns: this.config.fieldColumns,
        // pagination: false,
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      fieldData: [],
      treeData: [],
      selectedKey: null
    };
  },
  computed: {
    isVirtualLogin() {
      let role = _.get(this.$store.state.account, 'user.role');
      return role === 'virtual_dev';
    }
  },
  mounted() {},
  created() {},
  methods: {
    show(id) {
      this.visible = true;
      this.loading = true;
      getReviewTableList({ detail_id: id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.loading = false;
            const data = _.get(res, 'data.data') || [];
            let count = 0;
            this.treeData = data.map(item => {
              // 默认选中第一个
              if (item.children && item.children.length > 0) {
                count++;
                if (count == 1) {
                  this.onSelect(item, item.children[0]);
                }
              }
              return {
                ...item,
                expand: true
              };
            });
            this.$hideLoading({ duration: 0 });
          } else {
            this.loading = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    hide() {
      this.treeData = [];
      this.tableData = [];
      this.indexData = [];
      this.fieldData = [];
      this.table_name = null;
      this.selectedKey = null;
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    getColumns(tableName) {
      const data = this.indexData.filter(itm => itm.table_name === tableName);
      return this.config.indexColumns({
        combineInfo: this.combineIndexTable(data)
      });
    },
    combineIndexTable(data = []) {
      const combineColumns = ['table_name', 'index_name'];
      let map = { table_name: {}, index_name: {} };
      data.forEach((item, index) => {
        combineColumns.forEach(key => {
          let colVal = item[key];
          let colMap = map[key];
          if (colVal) {
            let uid = colVal + '_' + index;
            colMap[uid] = {
              index,
              rowSpan: 1
            };
            // 和前面值相同
            if (index > 0 && data[index - 1][key] == colVal) {
              colMap[uid].rowSpan = 0;
              let pid = colVal + '_' + (index - 1);
              if (!colMap[pid].parent) {
                colMap[pid].rowSpan += 1;
                colMap[uid].parent = colMap[pid];
              } else {
                colMap[pid].parent.rowSpan += 1;
                colMap[uid].parent = colMap[pid].parent;
              }
            }
          }
        });
      });
      return map;
    },
    showAddModal(data) {
      this.$emit('clickApply', data);
    },
    onExpand(key) {
      this.treeData = this.treeData.map(item => {
        if (item.key == key) {
          item.expand = !item.expand;
        }
        return item;
      });
    },
    onSelect(data, child) {
      if (child.key == this.selectedKey) {
        return;
      }
      this.selectedKey = child.key;
      // 发起请求;
      this.loading = true;
      ReqUtil.req({
        ctx: this,
        reqInstance: getNewTableInfo,
        params: {
          data_source_id: data.data_source_id,
          schema: child.schema,
          table: child.title
        },
        needLoading: false,
        cbk: data => {
          data = data || {};
          this.tableData = (data.table_list || []).map((item, index) => ({
            ...item,
            id: index
          }));
          this.table_name = _.get(this.tableData, '0.table_name');
          this.indexData = (data.index_list || []).map((item, index) => ({
            ...item,
            id: index
          }));
          // 处理索引合并
          this.fieldData = (data.column_info || []).map((item, index) => ({
            ...item,
            id: index
          }));
        },
        err: res => {
          this.tableData = [];
          this.indexData = [];
          this.fieldData = [];
        }
      })
        .then(() => {
          this.$hideLoading({ duration: 0 });
          this.loading = false;
        })
        .catch(e => {
          this.loading = false;
        });
    }
  }
};
</script>

<style lang="less">
.review-total-info-modal {
  pointer-events: none;
  .ant-modal-content {
    .ant-modal-close {
      .ant-modal-close-x {
        color: #27272a;
        font-size: 20px;
      }
    }
    .ant-modal-header {
      padding: 18px 24px;
      cursor: move;
      background: #ffff;
      .ant-modal-title {
        font-size: 20px;
        color: #27272a;
        font-weight: 600;
        margin-right: 36px;
        display: flex;
        justify-content: space-between;
      }
    }
    .table-name-wrapper {
      padding: 8px 0;
      &:first-child {
        padding-top: 0;
      }
      .table-name {
        color: #1890ff;
        font-size: 14px;
        font-weight: 700;
        margin: 8px 0 16px 0;
        span {
          padding: 4px 8px;
          border: 1px solid #1890ff;
          border-radius: 4px;
        }
      }
    }
    .ant-modal-body {
      padding: 0px 24px 24px 24px;
      .table-info-content {
        > .ant-spin-container {
          display: flex;
          .table-list {
            padding: 0 20px 0 0;
            min-width: 270px;
            min-height: 300px;
            .data-source {
              padding: 8px 0 0 0;
              display: flex;
              align-items: center;
              .instance-name {
                display: flex;
                align-items: center;
                margin: 0 4px 0 12px;
                > span {
                  display: flex;
                  align-items: center;
                }
                .limit-label {
                  pre {
                    font-size: 13px;
                    color: #27272a;
                    font-weight: 600;
                  }
                }
                .text {
                  margin-left: 6px;
                  font-size: 12px;
                  color: #f29339;
                  font-weight: 600;
                  white-space: nowrap;
                  &.test {
                    color: #4cbb3a;
                  }
                }
              }
              .lu-icon-notice {
                color: #e71d36;
              }
            }
            .table-and-schema-container {
              padding: 8px 0;
              &.empty {
                font-size: 13px;
                color: #71717a;
                font-weight: 400;
                margin-left: 26px;
              }
              .table-and-schema-content {
                display: flex;
                align-items: center;
                padding: 4px 4px;
                margin-left: 22px;
                border-radius: 6px;
                cursor: pointer;
                .anticon {
                  color: #f29339;
                }
                .schema-name {
                  margin: 0 8px;
                  font-size: 12px;
                  color: #71717a;
                  font-weight: 400;
                  word-break: break-all;
                }
                &.active {
                  background: #ebf2ff;
                  > span {
                    color: #008adc;
                  }
                }
              }
            }
          }
          .ant-tabs {
            width: 100%;
            padding: 0 0 0 20px;
            border-left: 1px solid #e8e8e8;
            .ant-tabs-bar {
              padding: 0;
              margin: 0 0 8px 0;
              .ant-tabs-nav-container {
                .ant-tabs-nav {
                  > div {
                    .ant-tabs-tab {
                      font-size: 14px;
                      color: #27272a;
                      font-weight: 600;
                      padding: 16px 0;
                      &.ant-tabs-tab-active {
                        color: #008adc;
                      }
                    }
                  }
                }
              }
            }
            .ant-tabs-content {
              .table-name-wrapper {
                padding: 0;
              }
              .custom-table {
                min-height: 240px;
                .ant-table,
                .ant-table-fixed {
                  .ant-table-thead {
                    > tr {
                      > th {
                        background: #f4f5f7 !important;
                        border-bottom: none;
                        border-right: none;
                        font-size: 13px;
                        color: #a1a1a1 !important;
                        font-weight: 400;
                        &:last-child {
                          border-right: 1px solid #e8e8e8;
                        }
                      }
                    }
                  }
                  .ant-table-tbody > tr {
                    background: #fff !important;
                    > td {
                      border-right: none;
                      &:last-child {
                        border-right: 1px solid #e8e8e8;
                      }
                    }
                    &.ant-table-row-hover {
                      > td {
                        background: #fbfcff;
                      }
                    }
                    &:hover {
                      > td {
                        background: #fbfcff;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      .ant-empty {
        // min-height: 400px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .ant-empty-image {
          height: 64px;
          margin-top: 24px;
        }
      }
    }
  }
}
</style>
