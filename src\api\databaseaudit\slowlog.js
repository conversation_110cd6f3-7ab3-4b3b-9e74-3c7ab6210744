import Http from '@/utils/request';
// 获取表
export function getSlowRecord(params) {
  return Http({
    url: `/sqlreview/after_audit/get_slow_record`,
    method: 'get',
    params: params
  });
}

// 表头设置保存
export function getTableHeaderInfo(params) {
  return Http({
    url: ` /sqlreview/after_audit/get_header_columns`,
    method: 'get',
    params: params
  });
}

// topsql 导出功能
export function download(params) {
  return Http({
    url: ` /sqlreview/after_audit/export_slow_log`,
    method: 'post',
    data: params,
    responseType: 'blob'
  });
}

// ai 审核
export function slowLogExamineSql(params) {
  return Http({
    url: `/sqlreview/after_audit/slow_log_examine_sql`,
    method: 'post',
    data: params
  });
}

// 执行计划
export function afterExplain(params) {
  return Http({
    url: `/sqlreview/after_audit/after_explain`,
    method: 'post',
    data: params
  });
}

// 删除
export function deleteSlowLog(params) {
  return Http({
    url: `/sqlreview/after_audit/delete_slow_log`,
    method: 'get',
    params: params
  });
}

// 慢日志上传
export function uploadSlowLog(params) {
  return Http({
    url: `/sqlreview/after_audit/upload_slow_log`,
    method: 'post',
    data: params
  });
}

// 慢日志上传 更新
export function updateSlowLog(params) {
  return Http({
    url: `/sqlreview/after_audit/update_slow_log`,
    method: 'post',
    data: params
  });
}

export default {};