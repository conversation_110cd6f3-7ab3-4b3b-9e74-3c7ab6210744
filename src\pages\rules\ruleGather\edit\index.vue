<template>
  <div class="config-rules-edit">
    <ContentNew
      :type="type"
      ref="ruleGather"
      :dataSource="dataSource"
      @change="change"
    />
    <div class="frame-button-wrapper">
      <a-button @click="onCancel" class="highlight">取消</a-button>
      <a-button @click="onSave" type="primary">保存并启用</a-button>
    </div>
  </div>
</template>

<script>
import { ruleSetEdit, ruleSetDetail } from '@/api/config/rule';

import ContentNew from '../components/ContentNew/index';
export default {
  components: { ContentNew },
  props: {},
  data() {
    return {
      loading: false,
      title: '规则集修改',
      dbType: '',
      type: 'edit',
      dataSource: null,
      id: null
    };
  },
  computed: {},
  mounted() {},
  created() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      // 请求
      this.loading = true;
      ruleSetDetail({
        id: this.$route.params.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.loading = false;
            let dataSource = _.get(res, 'data.data') || {};
            const { details = [] } = dataSource;
            dataSource.details = details.map(item => {
              return {
                ...item,
                ...(item.target_info || {})
              };
            });
            this.dataSource = dataSource;
            this.id = dataSource.id;
            this.dbType =
              dataSource.db_type ||
              window.localStorage.getItem('db_type') ||
              '';
            if (this.dbType) {
              this.title += `（${this.dbType}）`;
            }
          } else {
            this.$message.error(_.get(res, 'data.message'));
            this.loading = false;
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$message.error('请求失败');
        });
    },
    onCancel() {
      this.$router.push({ name: 'ruleGather-config' });
    },
    onSave() {
      let _ref = this.$refs.ruleGather;
      let { tableEdit } = _ref.$refs;
      let selectedRowKeys = tableEdit.selectedRowKeys || [];
      let formData = _ref.baseInfoData;
      let params = {
        ...formData,
        rule: selectedRowKeys.join(',') || '',
        id: this.dataSource.id
      };

      if (selectedRowKeys.length <= 0) {
        this.$hideLoading({
          method: 'warn',
          tips: '请选择规则'
        });
        return;
      }
      this.$showLoading();
      ruleSetEdit(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.$router.push({ name: 'ruleGather-config' });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    change(data) {
      this.showDDL = data;
    }
  }
};
</script>

<style lang="less" scoped>
</style>
