<template>
  <div class="project-review-content">
    <!-- 页面概述 -->
    <div class="header">
      <div :class="['summary', isCollapse && 'collapse']" @click="onCollapse">
        <div class="title">
          <span><custom-icon type="project"></custom-icon>项目审核</span>
          <div>
            <a-input-search
              placeholder="搜索项目名称/ID"
              @click="(e) => e.stopPropagation()"
              @search="onSearch"
              v-if="isCollapse"
            ></a-input-search>
            <span class="search" v-if="isCollapse">高级查询</span>
            <custom-icon :type="isCollapse ? 'down' : 'up'"></custom-icon>
          </div>
        </div>
        <div class="des" v-if="!isCollapse">
          该项目审核菜单提供了对已配置项目的代码解析和SQL审核功能。用户可以基于项目进行全量代码审核和增量代码审核，审核前确保项目配置的代码库能正常访问，配置的账号具备代码读取权限；同时数据源能够正常访问，并且配置的数据源账号具备SQL审核所需的数据库权限。
        </div>
        <div class="supports" v-if="!isCollapse">
          <span>功能支持</span>
          <span>代码库类型：git、svn、cc</span>
          <span
            >代码框架：Mybatis(99%)...<a-tooltip>
              <template slot="title"
                >Mybatis(99%)、Mybatis-JAVA 注解(99%)、Ibatis(99%)、 证券新一代
                xml 写法(99%)、Mybatis Plus(80%)、 C++
                嵌入式SQL(.sqx)((80%))、.cpp 文件(c/c++源代码)(3%)、
                JDBC(3%)</template
              >
              <a-icon type="exclamation-circle" /> </a-tooltip
          ></span>
          <span>SQL类型：DML、DDL、DQL</span>
          <span>离线/在线支持：在线</span>
        </div>
      </div>
      <!-- 搜索内容 -->
      <div
        :class="['search-content', isCollapse && 'collapse']"
        v-show="!isCollapse"
      >
        <SearchArea
          v-bind="searchParams || {}"
          @reset="reset"
          @search="search"
          ref="search"
          searchMode="switch"
          :iconCombine="false"
          :isShowExpandIcon="false"
          :searchData="searchData || {}"
        ></SearchArea>
      </div>
    </div>
    <PageList :mode="'single'">
      <div class="page-list-single">
        <SwitchTable
          ref="switchTable"
          v-bind="tableParams"
          :cardColumns="cardColumns"
          :listColumns="listColumns"
        >
          <template slot="tableTopLeft">
            <div>任务列表</div>
          </template>
          <template slot="tableTopRight">
            <a-button
              slot="extra"
              class="new-button"
              icon="plus"
              @click="addProject"
              type="primary"
              >新建</a-button
            >
            <div class="sql-count">
              <a-tooltip>
                <template slot="title"> SQL总数不为0 </template>
                <a-switch
                  size="small"
                  @change="onChange"
                  :checked="sql_count"
                />
              </a-tooltip>
            </div>
            <div class="switch-view">
              <a-tooltip>
                <template slot="title"> 切换视图 </template>
                <custom-icon
                  type="lu-icon-viewlist"
                  @click="switchView"
                ></custom-icon>
              </a-tooltip>
            </div>
          </template>
          <!-- 卡片模式 -->
          <CardTable
            slot="cardTable"
            slot-scope="{ record }"
            v-bind="{ cardData: record }"
            @authSubmit="authSubmit(record.id)"
            @showReport="showReport(record)"
            @toDetail="toDetail(record)"
            @remove="remove(record)"
            @terminate="terminate(record)"
            @reReview="reReview(record)"
            @urge="urge(record.id)"
            @refresh="refresh"
          ></CardTable>
          <!-- 列表模式 -->
          <template slot="project_name" slot-scope="{ text, record }">
            <a @click="toDetail(record, $event)">{{ text }}</a>
          </template>
          <template slot="project_group" slot-scope="{ text }">
            <span class="project-group" v-if="text && text.length > 0">
              <span v-if="text.length == 1">{{ text[0] }}</span>
              <span v-if="text.length > 1">
                <a-tooltip>
                  <template slot="title">
                    <span>{{ text.toString() }}</span>
                  </template>
                  <span>{{ text[0] + '...' }}</span>
                </a-tooltip>
              </span>
            </span>
          </template>
          <!-- DBA评审状态 -->
          <div
            slot="dba_status"
            slot-scope="{ text, record }"
            class="dba-status"
          >
            <a-tooltip v-if="text == '未通过'">
              <template slot="title">
                <span>驳回原因：{{ record.comment_content }}</span>
              </template>
              <StatusTag type="dba" :status="text">
                <a-icon style="marginleft: 4px" type="question-circle" />
              </StatusTag>
            </a-tooltip>
            <StatusTag type="dba" :status="text" v-else />
          </div>
          <template slot="status" slot-scope="{ text, record }">
            <div class="status">
              <a-tooltip v-if="record.status == '9' && record.error_message">
                <template slot="title">
                  <span>{{ record.error_message }}</span>
                </template>
                <StatusTag
                  type="review"
                  :status="record.status"
                  fromPath="sqlreview"
                >
                  <a-icon style="marginleft: 4px" type="question-circle" />
                </StatusTag>
              </a-tooltip>
              <StatusTag
                type="review"
                :status="record.status"
                fromPath="sqlreview"
                v-else
              />
              <a-progress
                v-if="[0, 3, 4, 5].includes(record.status)"
                :strokeWidth="8"
                :percent="Number(record.progress)"
                size="small"
              />
            </div>
          </template>
          <template slot="created_by" slot-scope="{ record, text }">
            <a-tooltip v-if="record.ch_creater" placement="topLeft">
              <template slot="title">
                <span>{{ text }}</span>
              </template>
              <div class="des">
                <a-icon type="user" />
                <span>{{ record.ch_creater }}</span>
              </div>
            </a-tooltip>
            <span v-else>{{ text || '--' }}</span>
          </template>
          <!-- DBA负责人 -->
          <template slot="dba_leader" slot-scope="{ text }">
            <a-tooltip v-if="text && text.length > 0" placement="topLeft">
              <template slot="title">
                <span>{{ text | handleTitle }}</span>
              </template>
              <div class="dba-leader">
                <span>{{ text | handleContent }}</span>
              </div>
            </a-tooltip>
          </template>
          <template slot="project_group_leader" slot-scope="{ text }">
            <a-tooltip v-if="text && text.length > 0" placement="topLeft">
              <template slot="title">
                <span>{{ text | handleTitle }}</span>
              </template>
              <div class="project-group-leader">
                <span>{{ text | handleContent }}</span>
              </div>
            </a-tooltip>
          </template>
          <custom-btns-wrapper
            slot="action"
            slot-scope="{ text, record }"
            :limit="3"
          >
            <a @click="showReport(record)" actionBtn>报表</a>
            <a
              v-if="[1, 2].includes(record.review_status)"
              :disabled="record.review_status == 1"
              actionBtn
              @click="authSubmit(record.id)"
              >提交评审</a
            >
            <a
              v-if="
                record.dba_status === '评审中' || record.dba_status === '待评审'
              "
              @click="urge(record.id)"
              actionBtn
              >催办</a
            >
            <a
              v-if="[0, 3, 4, 5].includes(record.status)"
              @click="terminate(record)"
              actionBtn
              >终止</a
            >
            <a-popconfirm
              title="重新发起审核任务?"
              @confirm="() => reReview(record)"
            >
              <a
                v-if="
                  ![1, 2].includes(record.type) &&
                  [-1, 9].includes(record.status) &&
                  ['未提交', '未通过'].includes(record.dba_status) &&
                  record.version_control == 1
                "
                actionBtn
                >重新review</a
              >
            </a-popconfirm>
            <a-popconfirm
              title="确定移除该数据?"
              @confirm="() => remove(record)"
              v-if="canDo && ![0, 3, 4, 5].includes(record.status)"
            >
              <a actionBtn class="remove">删除</a>
            </a-popconfirm>
          </custom-btns-wrapper>
        </SwitchTable>
        <!-- 新建项目弹窗 -->
        <AddModal ref="addModal" @save="saveProject"></AddModal>
        <!-- 报表抽屉 -->
        <Report ref="Report"></Report>
        <!-- 审核弹窗 -->
        <Audit ref="audit" @refresh="refresh"></Audit>
      </div>
    </PageList>
  </div>
</template>
<script>
import {
  createReview,
  pressToDo,
  removeRecord,
  terminationRecord,
  reviewRetry
} from '@/api/preaudit';
import LimitTags from '@/components/LimitTags';
import Table from '@/components/Table';
import StatusTag from '@/components/Biz/Status/Tag';
import Status from '@/components/Biz/Status';
import AddModal from './AddModal';
import Report from '../../components/Report';
import Audit from '@/components/Biz/AuditModel';
import CardTable from './CardTable';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import PageList from '@/components/PageListNew/SwitchTable/index.vue';
import SwitchTable from '@/components/PageListNew/SwitchTable/table.vue';
import SearchArea from '@/components/Biz/SearchArea/new';
export default {
  name: 'project-review',
  components: {
    CardTable,
    Table,
    Status,
    AddModal,
    Report,
    Audit,
    LimitTags,
    StatusTag,
    PageList,
    SwitchTable,
    SearchArea
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    this.config = config(this);
    // keep-alive是否激活
    this.activated = null;
    return {
      isCollapse: true,
      isShow: false,
      record: {},
      statusColor: this.config.statusColor,
      searchData: {},
      searchParams: {
        fields: this.config.searchFields
      },
      tableParams: {
        url: '/sqlreview/review/list1',
        reqParams: {
          sql_count: '0',
          type: 0
        },
        method: 'post',
        columns: this.config.cardColumns,
        rowKey: 'id',
        showHeader: false,
        cacheKey: 'project-review',
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' }
      },
      cardColumns: this.config.cardColumns,
      listColumns: this.config.listColumns
    };
  },
  created() {},
  mounted() {},
  activated() {
    if (this.activated === false) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      this.$store.commit('common/setSearchCache', {
        'project-review': { sql_count: searchParams.sql_count }
      });
      this.activated = true;
    }
  },
  deactivated() {
    this.activated = false;
  },
  computed: {
    // sql总数为0是否被选中，读取vuex中保存的缓存数值
    sql_count() {
      const data = this.$store.state.common.searchCache['project-review'];
      return data ? data.sql_count === '1' : false;
    },
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  methods: {
    // 筛选sql是否为0
    onChange(bool) {
      this.$store.commit('common/setSearchCache', {
        'project-review': { sql_count: bool ? '1' : '0' }
      });
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { sql_count: bool ? '1' : '0' });
      this.$set(this.tableParams, 'reqParams', {
        sql_count: bool ? '1' : '0',
        type: 0
      });
      // this.$set(table.searchParams, 'sql_count', bool ? '1' : '0');
    },
    // 切换视图
    switchView() {
      const { switchTable } = this.$refs;
      switchTable.switchView();
    },
    // 新建项目
    addProject() {
      const { addModal } = this.$refs;
      addModal.show();
    },
    // 新建项目保存
    saveProject(data) {
      const { addModal, switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const filesList = data.files_list;
      // 请求
      this.$showLoading();
      let params = {};
      if (filesList && filesList.length > 0) {
        const submitData = new FormData();
        data.files_list.forEach(item => {
          submitData.append('files_list', item);
        });
        submitData.append('mode', String(data.mode));
        submitData.append('review_point', String(data.review_point));
        submitData.append('project_id', String(data.project_id));
        submitData.append('review_type', '0');
        params = submitData;
      } else {
        params = { ...data, review_type: 0 };
      }
      createReview(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            addModal.hide();
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    toDetail(record, e) {
      this.$router.push({
        name: 'project-review-detail',
        params: { id: record.id }
      });
    },
    showReport(record) {
      this.record = record;
      this.$refs.Report.show(record, 'project');
    },
    // 查询
    search(data) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.onReset();
    },
    // 提交评审
    authSubmit(id) {
      this.$refs.audit.show(id);
    },
    // 催办
    urge(id) {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const obj = { record_id: id };
      pressToDo(obj)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            table.refreshKeep();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    refresh() {
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      table.refreshKeep();
    },
    remove(record) {
      this.$showLoading();
      removeRecord({ id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 重新review
    reReview(record) {
      this.$showLoading();
      reviewRetry({ review_id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 终止
    terminate(record) {
      this.$showLoading();
      terminationRecord({ id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { switchTable } = this.$refs;
            switchTable.execute('refreshKeep');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    onSearch(value, e) {
      e.stopPropagation()
      const data = { id: value };
      const { switchTable } = this.$refs;
      const { table } = switchTable.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    },
    onCollapse() {
      this.isCollapse = !this.isCollapse;
    },
    onShow() {
      this.isShow = !this.isShow;
    }
  },
  filters: {
    handleTitle(data) {
      const title = data.map(item => {
        return item.name + '(' + item.ch_name + ')';
      });
      return title.join();
    },
    handleContent(data) {
      const names =
        data &&
        data.map(item => {
          return item.ch_name;
        });
      const res = data && data.length > 1 ? names[0] + '...' : names[0];
      return res;
    }
  }
};
</script>

<style lang="less" scoped>
@assetsUrl: '~@/assets';
.project-review-content {
  .header {
    border-radius: 16px;
    margin-bottom: 16px;
    background-color: #fff;
    .summary {
      background-image: url('@{assetsUrl}/img/private/project-review.svg');
      background-size: 560px 178px;
      background-repeat: no-repeat;
      background-position: right;
      padding: 0 24px 0 24px;
      border-radius: 16px;
      cursor: pointer;
      &.collapse {
        background-image: none;
        background-color: #fff;
        background-size: 0;
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 600;
        padding: 16px 0;
        > span {
          .anticon {
            font-size: 24px;
            margin-right: 12px;
          }
        }
        > div {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          .ant-input-search {
            width: 200px;
          }
          .search {
            font-size: 14px;
            color: #1f1f1f;
            font-weight: 600;
            padding: 0 16px;
            cursor: pointer;
          }
          > .anticon {
            font-size: 14px;
            color: #8c8c8c;
            margin-right: 0;
          }
        }
      }
      .des {
        width: 64%;
        padding-bottom: 16px;
        word-break: break-all;
        font-family: PingFangSC-Regular;
        color: #1f1f1f;
      }
      .supports {
        padding-bottom: 18px;
        span {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #8c8c8c;
          margin-right: 22px;
          &:first-child {
            background: #e6f7ff;
            border: 1px solid rgba(145, 213, 255, 1);
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #1890ff;
            padding: 2px 6px;
            margin-right: 16px;
            border-radius: 4px;
          }
          .anticon {
            color: #1f1f1f;
            font-size: 12px;
            &:hover {
              cursor: pointer;
            }
          }
        }
      }
    }
    .search-content {
      // padding: 18px 24px 0 24px;
      border-top: 1px solid #f0f0f0;
      &.collapse {
        .title {
          border-bottom: none;
        }
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        > .anticon {
          font-size: 14px;
          color: #8c8c8c;
          margin-right: 0;
        }
      }
      /deep/.search-area {
        box-shadow: none;
        border: none;
        // .ant-form {
        //   > .ant-row {
        //     > .ant-col {
        //       width: 25%;
        //       .ant-form-item {
        //         .ant-form-item-label {
        //           width: auto;
        //           min-width: 88px;
        //         }
        //         .ant-form-item-control-wrapper {
        //           width: auto;
        //           min-width: 110px;
        //         }
        //       }
        //     }
        //   }
        // }
      }
    }
  }
  .page-list-switch-wrapper {
    .page-list-single {
      /deep/.custom-table {
        .search-area-wrapper {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 24px;
          .custom-table-top-left {
            > div {
              font-family: PingFangSC-Semibold;
              font-size: 16px;
              color: #1f1f1f;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  .sql-count {
    margin: 0 16px;
    display: flex;
    align-items: center;
    .sql-count-text {
      margin-right: 4px;
    }
  }
  .switch-view {
    font-size: 16px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    &:hover {
      cursor: pointer;
      background: #7adcff;
      color: #ffffff;
    }
  }
  .project-group {
    display: flex;
    > span {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #27272a;
      text-align: center;
      border-radius: 4px;
      padding: 0 2px;
      white-space: nowrap;
    }
  }
  .dba-status {
    .ant-tag {
      width: auto !important;
    }
  }
  /deep/.status {
    display: flex;
    align-items: center;
    white-space: nowrap;
    margin-bottom: 0 !important;
    .ant-tag {
      width: auto !important;
    }
    .ant-progress {
      margin-left: 8px;
      margin-right: 12px;
      top: -2px;
      width: 84px;
      .ant-progress-outer {
        .ant-progress-inner {
          // width: 32px;
          border-radius: 0;
          .ant-progress-bg {
            border-radius: 0 !important;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1560px) {
  .project-review-content {
    // .search-content {
    //   /deep/.search-area {
    //     .ant-form {
    //       > .ant-row {
    //         > .ant-col {
    //           width: 33.33%;
    //         }
    //       }
    //     }
    //   }
    // }
    .header {
      .summary {
        background-image: none;
        background-color: #fff;
        .des {
          width: 100%;
        }
      }
    }
  }
}

// @media screen and (min-width: 1720px) {
//   .project-review-content {
//     .search-content {
//       /deep/.search-area {
//         .ant-form {
//           > .ant-row {
//             > .ant-col {
//               width: 20%;
//             }
//           }
//         }
//       }
//     }
//   }
// }
</style>
