<template>
  <div class="card-table">
    <div class="card-table-main-info">
      <div class="left-block">
        <div class="info-box">
          <span class="project-name">
            <span v-if="cardData.sql_text.length <= 16">{{ cardData.sql_text }}</span>
            <LimitLabel
              v-else
              mode="ellipsis"
              :nowrap="true"
              :label="cardData.sql_text || ''"
              :ignoreFirstLineComment="true"
            ></LimitLabel>
          </span>
          <div class="status-box">
            <span class="audit-status">
              <span>审核状态</span>
              <StatusTag type="dba" :status="auditStatus" />
            </span>
          </div>
        </div>

        <div class="tag-box">
          <span>所属项目：{{ cardData.project_name }}</span>
        </div>

        <div class="side-info-box">
          <span class="avatar-part">
            <a-avatar icon="user" />
          </span>
          <a-tooltip v-if="cardData.ch_creater">
            <template slot="title">
              <span>{{ cardData.created_by }}</span>
            </template>
            <span class="created-by">{{ cardData.ch_creater || '--' }}</span>
          </a-tooltip>
          <span class="created-by" v-else>{{ cardData.created_by }}</span>
          <span>于{{ cardData.created_at + ' ' }}</span>
          <span class="event">创建</span>
        </div>
      </div>
      <div class="right-block">
        <div class="right-block-rules">
          <div>
            <span>审核DBA</span>
            <a-tooltip v-if="cardData.ch_dba">
              <template slot="title">
                <span>{{cardData.operate_dba}}</span>
              </template>
              <span class="operate-dba">{{cardData.ch_dba}}</span>
            </a-tooltip>
            <span v-else class="operate-dba">{{cardData.operate_dba || '--'}}</span>
            <!-- <span>{{cardData.operate_dba || '--'}}</span> -->
          </div>
        </div>
        <a-divider class="right-block-divider" type="vertical" />
        <div class="right-block-rules popover">
          <a-popover
            overlayClassName="order-sql-white-card-popover"
            v-if="ruleShowLabel.length > 0 || exceptionShowLabel.length > 0"
          >
            <template slot="content">
              <div class="rule-block" v-if="ruleShowLabel.length > 0">
                <div class="title">触发规则</div>
                <div class="des" v-for="(item, index) in ruleShowLabel" :key="index">
                  <div>
                    <custom-icon
                      type="lu-icon-alarm"
                      :style="{color: item.rule_result == 0 ? '#f6b475' : '#f28d9a'}"
                    />
                    <span>{{item.desc}}</span>
                  </div>
                </div>
              </div>
              <div class="exception-block" v-if="exceptionShowLabel.length > 0">
                <div class="title">解析异常</div>
                <div class="des" v-for="(item, index) in exceptionShowLabel" :key="index">
                  <div>
                    <custom-icon type="lu-icon-unusual" style="#71717a;" />
                    <span>{{item}}</span>
                  </div>
                </div>
              </div>
              <slot name="popoverBottom"></slot>
            </template>
            <div>
              <span>触发规则</span>
              <span class="rules">{{ cardData.ai_comment.count || '--' }}</span>
            </div>
          </a-popover>
          <div v-else>
            <span>触发规则</span>
            <span class="rules">{{ cardData.ai_comment.count || '--' }}</span>
          </div>
        </div>
        <a-divider class="right-block-divider" type="vertical" />
        <div class="right-block-botton">
          <slot name="action"></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import LimitLabel from '@/components/LimitLabel';
import StatusTag from '@/components/Biz/Status/Tag';
export default {
  components: { LimitLabel, StatusTag },
  props: {
    cardData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  computed: {
    ruleShowLabel() {
      return this.cardData.ai_comment.rule_info || [];
    },
    exceptionShowLabel() {
      return this.cardData.ai_comment.error || [];
    },
    auditStatus() {
      let statusText = {
        0: '待审核',
        1: '已通过',
        '-1': '未通过',
        null: '已通过'
      };
      let auditStatus = this.cardData.audit_status;
      return statusText[auditStatus];
    }
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  created() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.card-table {
  .card-table-main-info {
    display: flex;
    justify-content: space-between;
    .left-block {
      display: flex;
      flex-direction: column;
      margin-right: 64px;
      .info-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .status-box {
          > span {
            > span {
              margin-right: 4px;
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #a1a1aa;
              font-weight: 400;
            }
          }
        }
        .project-name {
          position: relative;
          white-space: nowrap;
          top: -2px;
          margin-right: 28px;
          > span {
            font-family: PingFangSC-Semibold;
            font-size: 20px;
            color: #27272a;
            font-weight: 600;
            max-width: 360px;
            display: block;
          }
          /deep/.limit-label {
            pre {
              font-family: PingFangSC-Semibold;
              font-size: 20px !important;
              color: #27272a;
              font-weight: 600 !important;
            }
          }
        }
      }
      .tag-box {
        margin: 12px 0 16px 0;
        > span {
          margin-right: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #27272a;
          letter-spacing: 0;
          font-weight: 500;
          text-align: center;
          border: 1px solid #e4e4e7;
          border-radius: 4px;
          padding: 3px 7px;
        }
      }
      .side-info-box {
        display: flex;
        align-items: center;
        > span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #a1a1aa;
          letter-spacing: 0;

          font-weight: 400;
          &.avatar-part {
            width: 20px;
            .ant-avatar {
              width: 20px;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              margin-bottom: 2px;
              color: #fff;
              background: #4ec3f5;
            }
          }
        }
        .created-by {
          margin: 0 4px 0 8px;
          color: rgb(113, 113, 122);
        }
        .event {
          color: rgb(113, 113, 122);
        }
      }
    }
    .right-block {
      display: flex;
      align-items: center;
      .ant-divider-vertical {
        width: 1px;
        height: 32px;
        margin: 0 12px;
      }
      .right-block-rules {
        padding: 0 18px;
        display: flex;
        align-items: center;
        > div {
          display: flex;
          flex-direction: column;
          text-align: center;
          & span:first-child {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #71717a;
            letter-spacing: 0;
            font-weight: 400;
            margin-bottom: 8px;
            white-space: nowrap;
          }
          & span:last-child {
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: #27272a;
            letter-spacing: 0;
            font-weight: 500;
          }
        }
        &.popover {
          &:hover {
            cursor: pointer;
          }
          .rules {
            color: #e71d36 !important;
          }
        }
      }
      .right-block-botton {
        padding-left: 18px;
        width: auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        > a {
          height: 16px;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #4db5f2;
          letter-spacing: 0;
          line-height: 16px;
          font-weight: 400;
          display: flex;
          align-items: center;
          margin-right: 32px;
          > .anticon {
            margin-right: 4px;
          }
          &.highlight {
            padding: 14px 6px 14px 8px;
            background: #4db5f2;
            border-radius: 4px;
            color: #fff;
            margin-right: 0;
            > .anticon {
              font-size: 12px;
              margin-right: 0;
              margin-left: 4px;
            }
          }
          &:hover {
            color: @primary-color;
            &.highlight {
              color: #fff;
              background: @primary-color;
            }
          }
        }
        > a[disabled] {
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .card-table {
    .card-table-main-info {
      .right-block {
        display: flex;
        justify-content: space-between;
        .right-block-divider {
          display: none;
        }
        .right-block-botton {
          width: 100%;
          min-width: 240px;
          display: none;
        }
      }
      .left-block {
        .info-box {
          .project-name {
            > span {
              max-width: 360px;
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1560px) {
  .card-table {
    .card-table-main-info {
      .left-block {
        .info-box {
          .project-name {
            > span {
              max-width: 500px;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.ant-popover {
  &.order-sql-white-card-popover {
    z-index: 1051;
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 400px;
        max-height: 300px;
        .exception-block,
        .rule-block {
          .title {
            font-size: 12px;
            color: #27272a;
            font-weight: 400;
            margin-bottom: 8px;
          }
          .des {
            > div {
              font-size: 12px;
              color: #27272a;
              font-weight: 400;
              margin-bottom: 8px;
            }
          }
          .des:nth-last-child(1) {
            > div {
              margin-bottom: 0px;
            }
          }
        }
      }
    }
  }
}
</style>
