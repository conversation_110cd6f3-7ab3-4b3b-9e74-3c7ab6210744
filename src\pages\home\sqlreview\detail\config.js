export default function (ctx) {
  const columns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id',
    //   width: 100
    // },
    {
      title: 'SQL文本',
      key: 'sql_text',
      dataIndex: 'sql_text',
      // width: 500,
      scopedSlots: { customRender: 'sql_text' }
    },
    {
      title: 'SQL代码',
      key: 'source_text',
      dataIndex: 'source_text',
      scopedSlots: { customRender: 'sql_text' }
    },
    {
      title: 'AI风险等级',
      key: 'risk',
      dataIndex: 'risk',
      width: 120,
      scopedSlots: { customRender: 'risk' }
    },
    // {
    //   title: 'SQL标签',
    //   key: 'label_type',
    //   dataIndex: 'label_type',
    //   width: 120,
    //   scopedSlots: { customRender: 'label_type' }
    // },
    // {
    //   title: 'SQL备注',
    //   key: 'sqlmap_note',
    //   dataIndex: 'sqlmap_note',
    //   width: 200,
    //   scopedSlots: { customRender: 'sqlmap_note' }
    // },
    {
      title: '审核结果',
      key: 'review_status',
      dataIndex: 'review_status',
      scopedSlots: { customRender: 'review_status' },
      width: 120
    }
  ];
  // .map((item) => {
  //   return {
  //     ...item,
  //     width: undefined
  //   };
  // });
  const searchFields = [
    {
      type: 'Select',
      label: 'Review状态',
      key: 'status_of_review',
      sourceKey: 'review_status',
      allowSearch: true,
      // backSearch: true,
      props: {
        options: [
          // {
          //   label: '未知',
          //   value: '0'
          // },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '不通过',
            value: '-1'
          }
          // {
          //   label: '白名单通过',
          //   value: '2'
          // },
          // {
          //   label: '错误',
          //   value: '9'
          // }
        ]
      }
    },
    {
      type: 'Select',
      label: 'AI风险等级',
      key: 'risk',
      props: {
        options: [
          {
            label: '高风险',
            value: 'high'
          },
          {
            label: '低风险',
            value: 'low'
          },
          {
            label: '无风险',
            value: 'no_risk'
          },
          {
            label: '异常',
            value: 'error'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'SQL标签',
      key: 'label_type',
      props: {
        mode: 'multiple',
        options: [
          {
            label: '无',
            value: 0
          },
          {
            label: '白名单',
            value: 1
          },
          {
            label: '整改中',
            value: 2
          }
        ]
      }
    }
  ];
  const fields = [
    {
      type: 'Label',
      label: '应用名称',
      key: 'project_name'
    },
    (formData = {}) => {
      return {
        type: 'Label',
        label: formData.is_svn == 1 ? 'svn地址' : 'git地址',
        key: 'url'
      };
    },
    {
      type: 'Label',
      label: '开发人员',
      key: 'dev_id_list',
      hideComponent: true,
      slots: [{ key: 'dev_id_list' }]
    },
    {
      type: 'Label',
      label: '数据源',
      key: 'sql_source_list',
      hideComponent: true,
      slots: [{ key: 'sql_source_list' }]
    }
  ];
  return {
    fields,
    columns,
    searchFields
  };
}
