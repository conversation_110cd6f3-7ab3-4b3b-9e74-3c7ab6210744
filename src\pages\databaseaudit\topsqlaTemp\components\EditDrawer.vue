<template>
  <!-- 新建项目抽屉 -->
  <a-drawer
    :title="title"
    :visible="visible"
    @close="hide"
    width="730px"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="config-datasource-edit-drawer"
  >
    <!-- 内容区域 -->
    <a-spin :spinning="spinning">
      <div class="item">
        <div class="title">采集配置</div>
        <!-- 数据源选择OB时 新增选项 -->
        <div
          v-if="['OB_ORACLE', 'OB_MYSQL'].includes(dbType)"
          class="ob-extra-form"
        >
          <div class="des">
            <custom-icon type="info-circle" theme="filled" />
            <span
              >OceanBase数据库审核需要连接到sys系统租户进行数据库执行SQL采集，如需使用数据库审核功能，请配置sys系统租户账号密码</span
            >
          </div>
          <Form
            class="ob-form"
            ref="obForm"
            v-bind="obParams"
            :formData="obData"
          ></Form>
        </div>
        <!-- 数据源选择mysql时 新增选项 -->
        <div class="extra-form-box">
          <Form
            class="extra-form"
            ref="form"
            v-bind="formParams"
            :formData="formData"
          >
            <JCronModal
              :data="collectFrequency"
              ref="JCronModal"
              slot="frequency"
              @cronExpression="cronExpression"
            ></JCronModal>
          </Form>
          <div
            class="extra-columns-box"
            v-if="collectType && collectType == 'jdbc'"
          >
            <div class="extra-columns-radio">
              <span>附加字段采集配置</span>
              <a-radio-group
                :options="options"
                v-model="extraStatus"
                @change="onChange"
              />
            </div>
            <div class="extra-columns-desc" v-if="extraStatus == 1">
              <custom-icon type="exclamation-circle" />
              <span
                >检测到该版本MySQL数据库information
                schema.processlisk存在额外附加字段，是否要对这些字段进行采集?</span
              >
            </div>
            <div class="extra-columns-table" v-if="extraStatus == 1">
              <TableEdit
                ref="tableEdit"
                v-bind="tableParams || {}"
                :dataSource="dataSource || []"
              ></TableEdit>
            </div>
          </div>
        </div>
      </div>
      <!-- 自动审核配置 -->
      <div class="item" v-if="['MYSQL', 'GOLDENDB'].includes(dbType)">
        <div class="title">自动审核配置</div>
        <div class="auto-review-form-box">
          <Form
            class="auto-review-form"
            ref="autoReviewForm"
            v-bind="autoReviewParams"
            :formData="autoReviewData"
          ></Form>
        </div>
      </div>
    </a-spin>
    <!-- 按钮区域 -->
    <div
      class="btns-area"
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 10
      }"
    >
      <a-button @click="checkOpenApi" v-if="collectType == 'openapi'"
        >连接测试</a-button
      >
      <a-button
        @click="checkOb"
        v-if="['OB_ORACLE', 'OB_MYSQL'].includes(dbType)"
        >连接测试</a-button
      >
      <a-divider
        type="vertical"
        v-if="
          collectType == 'openapi' || ['OB_ORACLE', 'OB_MYSQL'].includes(dbType)
        "
      />
      <a-button @click="hide" class="hide-btn">取消</a-button>
      <a-button
        @click="save"
        type="primary"
        :disabled="
          (collectType == 'openapi' && !flag) ||
          (['OB_ORACLE', 'OB_MYSQL'].includes(dbType) && !flag)
        "
        >保存</a-button
      >
    </div>
  </a-drawer>
</template>

<script>
import DataSourceBlocks from '@/components/Biz/DataSourceBlocks';
import TableEdit from '@/components/TableEdit';
import config from './config';
import JCronModal from '@/components/Biz/JCronModal';
import Form from '@/components/Form';
import { checkOpenApi, getExtraColumns } from '@/api/config/dataSource';
import {
  getCollectConfig,
  testObCollectConfig
} from '@/api/databaseaudit/topsql';
import { saveCollectConfig } from '@/api/databaseaudit/auditconfig';
export default {
  components: { DataSourceBlocks, Form, TableEdit, JCronModal },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      spinning: false,
      formData: {
        collect_type: 0
      },
      collectType: 'openapi',
      formParams: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 12 },
        fields: []
      },
      obData: {},
      obParams: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 16 },
        fields: this.config.obFields(this.type)
      },
      dataSource: [],
      tableParams: {
        initEditStatus: true,
        actionBtns: [],
        editConfig: this.config.editConfig(),
        columns: this.config.columns,
        pagination: false,
        rowKey: 'id'
      },
      autoReviewData: {
        // review_frequency: 12,
        // top_strategy: 'execution_total_time',
        // top_sort: 5,
        // filter_day: 12,
        // auto_review_status: 1
      },
      autoReviewParams: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 12 },
        fields: this.config.autoReviewFields()
      },
      id: null,
      title: null,
      openApiType: null,
      flag: false, // 阿里云校验成功 才能保存
      extraStatus: 0,
      options: [
        { label: '不采集', value: 0 },
        { label: '采集', value: 1 }
      ],
      captureEnumOptions: [],
      afteAuditStatus: null,
      dbType: '',
      collectFrequency: ''
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(id, record) {
      this.spinning = true;
      this.title = '采集配置';
      this.visible = true;
      this.id = id;
      this.dbType = record.db_type;
      getCollectConfig({
        data_source_id: this.id
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            const collectType = _.get(resData, 'collect_type');

            this.formData = { ...resData };
            this.obData = { ...resData };
            this.autoReviewData = { ...resData };
            this.openApiType = _.get(resData, 'open_api_type');
            this.extraStatus = _.get(resData, 'extra_status');
            this.afteAuditStatus = _.get(resData, 'after_audit_status');
            const dataSource = _.get(resData, 'extra_column');
            this.collectFrequency = _.get(resData, 'collect_frequency');
            if (!_.isEmpty(dataSource) && this.dbType == 'MYSQL') {
              getExtraColumns({ data_source_id: this.id })
                .then(res => {
                  if (CommonUtil.isSuccessCode(res)) {
                    const resData = _.get(res, 'data.data');
                    const captureEnumOptions = resData[0].capture_enum;
                    this.$set(
                      this.tableParams,
                      'editConfig',
                      this.config.editConfig(captureEnumOptions)
                    );
                    this.dataSource = dataSource;
                    this.$hideLoading({ duration: 0 });
                  } else {
                    this.$hideLoading({
                      method: 'error',
                      tips: _.get(res, 'data.message')
                    });
                  }
                })
                .catch(e => {
                  this.$hideLoading({
                    method: 'error',
                    tips: _.get(e || {}, 'response.data.message') || '请求失败'
                  });
                });
            }
            this.$set(this, 'collectType', collectType);
            switch (this.dbType) {
              case 'ORACLE':
                this.$set(
                  this.formParams,
                  'fields',
                  this.config.oracleFields()
                );
                break;
              case 'MYSQL':
              case 'GOLDENDB':
                this.$set(this.formParams, 'fields', this.config.fields(this.dbType));
                break;
              case 'OB_ORACLE':
              case 'OB_MYSQL':
                this.$set(
                  this.obParams,
                  'fields',
                  this.config.obInfo(resData.examine_user ? 'edit' : 'add')
                );
                this.$set(this.formParams, 'fields', this.config.obFields());
                break;
              case 'DB2':
                this.$set(this.formParams, 'fields', this.config.db2Fields());
                break;
              case 'GAUSSDB':
                this.$set(
                  this.formParams,
                  'fields',
                  this.config.gaussdbFields()
                );
                break;
              default:
                break;
            }
            this.spinning = false;
            this.$hideLoading({ duration: 0 });
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    hide() {
      this.visible = false;
      this.formData = {
        collect_type: 0
      };
      this.obData = {};
      this.collectFrequency = '';
    },
    // 保存
    save() {
      const {
        form,
        tableEdit,
        JCronModal,
        obForm,
        autoReviewForm
      } = this.$refs;
      const JCronForm = JCronModal && JCronModal.$refs.form;

      Promise.all([
        form.validate(),
        obForm && obForm.validate(),
        JCronModal && JCronForm.validate(),
        autoReviewForm && autoReviewForm.validate()
      ]).then(valid => {
        if (valid) {
          this.spinning = true;
          let formData = {};
          let obFormData = {};
          let autoReviewData = {};
          if (JCronModal) {
            JCronModal.handleSubmit();
          }
          if (form) {
            formData = form.getData();
            formData.ssh_pwd = Base64.encode(formData.ssh_pwd || '');
          }
          if (obForm) {
            obFormData = obForm.getData();
            obFormData.examine_pwd = Base64.encode(
              obFormData.examine_pwd || ''
            );
          }
          if (autoReviewForm) {
            autoReviewData = autoReviewForm.getData();
          }
          const tableData = (tableEdit && tableEdit.getData()) || [];
          const params = {
            ...this.formData,
            ...formData,
            ...obFormData,
            ...autoReviewData,
            extra_column: [...tableData],
            data_source_id: this.id,
            collect_type: this.collectType,
            extra_status: this.extraStatus
          };
          saveCollectConfig(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({
                  useMessage: true,
                  tips: '操作成功'
                });
                this.spinning = false;
                // const resData = _.get(res, 'data.data');
                this.$hideLoading({ duration: 0 });
                this.visible = false;
                this.$emit('save');
              } else {
                this.spinning = false;
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.spinning = false;
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    checkOpenApi() {
      const { form } = this.$refs;
      Promise.all([form.validate()]).then(valid => {
        if (valid) {
          this.$showLoading();
          const data = form.getData();
          const pararms = { ...data };
          checkOpenApi(pararms)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.flag = res.data.data.flag || false;
                this.$hideLoading({
                  useMessage: true,
                  tips: '校验成功'
                });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    checkOb() {
      const { form, obForm } = this.$refs;
      Promise.all([form.validate(), obForm.validate()]).then(valid => {
        if (valid) {
          this.$showLoading();
          const data = form.getData();
          const obData = obForm.getData();
          obData.examine_pwd = Base64.encode(obData.examine_pwd || '');
          const params = { ...data, ...obData, data_source_id: this.id };
          testObCollectConfig(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.flag = res.data.data.flag || false;
                this.$hideLoading({
                  useMessage: true,
                  tips: '校验成功'
                });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    onChange(e) {
      this.extraStatus = 0;
      if (e.target.value == 1) {
        this.spinning = true;
        getExtraColumns({ data_source_id: this.id })
          .then(res => {
            if (CommonUtil.isSuccessCode(res)) {
              const resData = _.get(res, 'data.data');
              this.dataSource = resData;
              const captureEnumOptions = resData[0].capture_enum;
              this.$set(
                this.tableParams,
                'editConfig',
                this.config.editConfig(captureEnumOptions)
              );
              this.spinning = false;
              this.$hideLoading({ duration: 0 });
              this.extraStatus = e.target.value;
            } else {
              this.spinning = false;
              this.extraStatus = 0;
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
      } else {
        this.dataSource = [];
      }
    },
    cronExpression(data, type) {
      if (data && data.split(' ').length > 0) {
        if (data.split(' ').includes('undefined')) {
          this.formData = Object.assign({}, this.formData, {
            collect_frequency: '',
            collect_time: type
          });
        } else {
          this.formData = Object.assign({}, this.formData, {
            collect_frequency: data,
            collect_time: type
          });
          this.$refs.form.saving({
            collect_frequency: data,
            collect_time: type
          });
        }
      }
      this.$nextTick(() => {
        this.$refs.form.validate();
      });
    }
  }
};
</script>

<style lang="less" scoped>
.config-datasource-edit-drawer {
  /deep/ .ant-drawer-content-wrapper {
    width: 720px;
    .ant-spin-container {
      .item {
        margin-bottom: 24px;
        .title {
          &::before {
            content: '';
            content: '';
            width: 3px;
            background: @primary-color;
            height: 16px;
            border-radius: 0px;
            margin-right: 8px;
          }
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 8px;
        }
      }
      .hidden-label > .ant-form-item-label {
        // display: none;
        opacity: 0;
      }
      .password {
        padding-bottom: 8px;
      }
      // .collect-type {
      //   padding-top: 16px;
      //   border-top: 1px solid #e8e8e8;
      // }
      .extra-form-box,
      .auto-review-form-box {
        padding: 12px 32px;
        background: #fff;
        .ant-form {
          .ant-row {
            .ant-col-4 {
              padding-right: 16px;
              width: 120px;
              min-height: 36px;
              // white-space: pre-line;
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              .ant-form-item-children {
                .ant-input,
                .ant-input-number,
                .ant-select-selection {
                  height: 36px;
                  border: 1px solid rgba(217, 217, 217, 1);
                }
                .ant-time-picker-input {
                  height: 36px;
                  position: relative;
                  top: 1px;
                }
              }
            }
            .ant-radio-group {
              display: flex;
              .ant-radio-wrapper {
                padding: 6px 8px;
              }
            }
          }
          &.ant-form {
            .ant-row {
              .ant-col-4 {
                padding-right: 16px;
                width: 200px;
                // white-space: pre-line;
                > label {
                  justify-content: flex-start;
                }
              }
              .ant-form-item-control-wrapper {
                flex-grow: 1;
                .ant-form-item-children {
                  .ant-input,
                  .ant-input-number,
                  .ant-select-selection {
                    height: 36px;
                    border: 1px solid rgba(217, 217, 217, 1);
                  }
                }
              }
              .ant-radio-group {
                display: flex;
                .ant-radio-wrapper {
                  padding: 6px 10px;
                  // background: #ffffff;
                  border: 1px solid rgba(228, 228, 231, 1);
                  &.ant-radio-wrapper-checked {
                    border: 1px solid rgba(77, 181, 242, 1);
                  }
                }
              }
            }
          }
        }
        .extra-columns-box {
          background: #fff;
          .extra-columns-radio {
            display: flex;
            // justify-content: space-between;
            padding: 8px 10px 8px 0;
            > span {
              width: 200px;
              font-size: 13px;
              color: #27272a;
              font-weight: 400;
              padding-right: 16px;
            }
          }
          .extra-columns-desc {
            padding: 8px 8px;
            background: #003b72;
            display: flex;
            justify-content: center;
            // align-items: center;
            span {
              font-size: 12px;
              color: #ffffff;
              text-align: justify;
              font-weight: 400;
            }
            .anticon {
              color: #fff;
              margin-right: 4px;
              margin-top: 2px;
            }
          }
          .extra-columns-table {
            // padding: 12px 0;
          }
        }
      }
      .ob-extra-form {
        padding: 16px 32px;
        background: #eff5ff;
        margin-bottom: 16px;
        .des {
          display: flex;
          margin-bottom: 16px;
          .anticon {
            color: #008adc;
            font-size: 16px;
            margin-right: 8px;
            position: relative;
            top: 3px;
          }
          > span {
            font-size: 14px;
            color: #000000;
          }
        }
        .ant-form {
          .ant-row {
            .ant-col-4 {
              padding-right: 16px;
              width: 160px;
              min-height: 36px;
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              .ant-form-item-children {
                .ant-input,
                .ant-input-number,
                .ant-select-selection {
                  height: 36px;
                  border: 1px solid rgba(217, 217, 217, 1);
                }
              }
            }
          }
        }
      }
    }
  }
}
.btns-area {
  display: flex;
  justify-content: flex-end;
  .ant-btn {
    margin-left: 10px;
    &.hide-btn {
      margin-left: 0px;
    }
  }
  .ant-divider {
    height: 32px;
    margin: 0 12px;
  }
}
.ant-steps {
  padding: 24px;
}
.ant-divider-horizontal {
  margin: 0 0 24px 0;
}
.ant-tag-red {
  font-size: 14px;
  padding: 8px;
}

/deep/.ant-row-flex-space-around {
  justify-content: flex-start;
  .backgroundBlock {
    margin-right: 12px;
  }
}
</style>
