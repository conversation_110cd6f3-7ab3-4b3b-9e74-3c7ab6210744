<template>
  <LimitLabel :hasTips="false" :label="dateTxt" :limit="limit"/>
</template>

<script>
import format from 'date-fns/format';
import LimitLabel from '@/components/LimitLabel';
export default {
  components: { LimitLabel },
  props: {
    formatStr: String,
    text: String,
    limit: {
      type: Number,
      default: 16
    }
  },
  data() {
    return {};
  },
  computed: {
    dateTxt() {
      const { text = '', formatStr = 'YYYY/MM/DD HH:mm:ss' } = this;
      return text == '' ? '-' : format(text, formatStr);
    }
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
</style>
