<template>
  <div>
    <div v-if="current == 1" class="import-of-statistics-info">
      <!-- 目标数据库 -->
      <div class="database-content">
        <div class="form-area">
          <Form ref="form" class="database-form" v-bind="databaseParams" :formData="formData"></Form>
          <a-button @click="checkAuth" class="check-auth-btn" :disabled="dataSourceId == null">权限检测</a-button>
          <div :class="['tips', `${tipsIcon[tips]}`]">
            <custom-icon :type="tipsIcon[tips]" theme="filled" />
            <span>{{databaseTips[tips]}}</span>
          </div>
        </div>
        <div class="error-info" v-if="errorData.length > 0">
          <div class="header">
            <div>
              <custom-icon type="exclamation-circle" theme="filled" />
              <span>请参照以下语句进行授权</span>
            </div>
            <custom-icon type="copy" @click="onCopy" />
          </div>
          <div class="content" v-for="(item,index) in errorData" :key="index">
            <span>{{item}}</span>
          </div>
        </div>
      </div>
      <!-- 文件导入 -->
      <div class="file-import-content">
        <div class="file-import-action-area">
          <span class="label">文件导入</span>
          <a-upload name="file" :before-upload="beforeUpload" :showUploadList="false" :multiple="true">
            <a-button class="file-import-btn" :disabled="!isCheck">上传文件</a-button>
            <span class="tips">仅支持sql文件</span>
          </a-upload>
        </div>
        <div class="file-import-table-edit">
          <TableEdit ref="tableEdit" v-bind="tableParams || {}" :dataSource="dataSource"></TableEdit>
        </div>
      </div>
      <div class="btn-area">
        <a-button type="primary" @click="importNow">立即导入</a-button>
      </div>
    </div>
    <Progress ref="progress" v-if="current == 2" @carryOn="carryOn" type="导入" />
    <Result ref="result" v-if="current == 3" :resultData="resultData" type="导入" @carryOn="carryOn" />
    <SubmitModal ref="submit" @submit="submit" />
  </div>
</template>
<script>
import config from './config';
import Form from '@/components/Form';
import TableEdit from '@/components/TableEdit';
import Progress from './components/Progress';
import Result from './components/Result';
import SubmitModal from './components/SubmitModal';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import {
  checkDatabase,
  importOracleStatSql,
  getImportOrExportDetail
} from '@/api/config/dataSource';
export default {
  mixins: [bodyMinWidth(1440)],
  components: { Form, TableEdit, Progress, Result, SubmitModal },
  props: {},
  data() {
    this.config = config(this);
    this.reqCancelHandler = null; // 取消请求句柄
    return {
      current: 1, // 1导入页面， 2导入中， 3导入成功、失败
      activeKey: '',
      dataSourceId: null,
      formData: {},
      databaseParams: {
        layout: 'horizontal',
        multiCols: 2,
        colon: false,
        fields: this.config.importFields()
      },
      dataSource: [],
      tableParams: {
        url: '',
        reqParams: {},
        initEditStatus: true,
        actionBtns: ['remove'],
        actionCbks: {
          remove: this.remove
        },
        editConfig: this.config.editConfig,
        columns: this.config.columns,
        pagination: false
      },
      databaseTips: {
        0: '权限检测失败',
        1: '权限检测成功',
        2: '待检测'
      },
      tips: 2,
      tipsIcon: {
        0: 'exclamation-circle',
        1: 'check-circle',
        2: 'info-circle'
      },
      fileList: [],
      errorData: [],
      submitData: {},
      resultData: {},
      status: null,
      isCheck: false
    };
  },
  computed: {},
  created() {},
  mounted() {},
  beforeDestroy() {
    this.cancel();
  },
  methods: {
    // 上传之前
    beforeUpload(file) {
      // const maxSize = 2 * 1024 * 1024; // byte
      // if (file.size > maxSize) {
      //   this.$message.error('文件大小错误，文件大小不超过2MB');
      //   return;
      // }
      if (!/\.(sql)$/.test(file.name)) {
        this.$message.error('文件格式错误，文件类型支持.sql');
        return;
      }
      const { tableEdit } = this.$refs;
      const data = tableEdit.getData();
      const fileNames = this.dataSource.map(item => item.file);
      if (!fileNames.includes(file.name)) {
        this.fileList.push(file);
        this.dataSource = [
          ...data,
          ...this.dataSource,
          {
            file: file.name,
            data_source_id: this.dataSourceId
          }
        ];
      } else {
        this.$message.warning('该文件已上传，无需重复上传');
      }

      return false;
    },
    checkAuth() {
      const { form } = this.$refs;
      const data = form.getData();
      checkDatabase({ data_source_id: data.data_source_id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.tips = 1;
            this.isCheck = true;
          } else if (res.data.code == 4000) {
            this.tips = 0;
            this.errorData = _.get(res, 'data.data');
            this.$hideLoading({ tips: _.get(res, 'data.message') });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    importNow() {
      if (!this.isCheck) {
        this.$message.warning('请权限检测成功后再导入');
        return;
      }
      const { form, tableEdit } = this.$refs;
      Promise.all([form.validate(), tableEdit.validate()]).then(valid => {
        if (valid) {
          const tableData = tableEdit.getData();
          if (_.isEmpty(tableData)) {
            this.$message.warn('上传文件为空');
            return;
          }
          this.$refs.submit.show();
        }
      });
    },
    // 提交数据
    submit() {
      const { form, tableEdit } = this.$refs;
      const formData = form.getData();
      const tableData = tableEdit.getData();
      const schemas = tableData.map(item => item.schema);

      this.submitData = new FormData();
      this.fileList.forEach(item => {
        this.submitData.append('file', item);
      });

      this.submitData.append('schema', String(schemas));
      this.submitData.append('data_source_id', formData.data_source_id);
      this.$showLoading();
      Promise.all([form.validate(), tableEdit.validate()]).then(valid => {
        if (valid) {
          importOracleStatSql(this.submitData)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.current = 2; // 导入中
                const id = _.get(res, 'data.data.task_id');
                this.onSetInterval({ task_id: id });
                this.$hideLoading({ duration: 0 });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    onCopy() {
      CommonUtil.copy({
        value: this.errorData.toString(),
        callback: () => {
          this.$message.success('复制成功');
        }
      });
    },
    // 表格当行删除
    remove(record) {
      this.dataSource = this.dataSource.filter(
        item => item.file !== record.file
      );
      this.fileList = this.fileList.filter(item => item.name !== record.file);
    },
    // 手动用setTimeout 实现setInterval
    onSetInterval(params) {
      const loop = params => {
        getImportOrExportDetail(params, c => {
          this.reqCancelHandler = c;
        })
          .then(res => {
            if (CommonUtil.isSuccessCode(res)) {
              this.resultData = _.get(res, 'data.data') || {};
              this.status = _.get(res, 'data.data.status');
            } else {
              this.$message.error(_.get(res, 'data.message'));
            }
          })
          .catch(e => {
            this.$hideLoading({
              method: 'error',
              tips: _.get(e, 'response.data.message') || '请求失败'
            });
          })
          .finally(e => {
            clearTimeout(this.timer);
            // 取消接口请求
            if (this.reqCancelHandler) {
              this.reqCancelHandler();
            }
            if (this.status !== 0) {
              this.current = 3;
              this.cancel();
              return;
            }
            this.timer = setTimeout(() => {
              loop(params);
            }, 10000);
          });
      };
      loop(params);
    },
    // 停止计时器
    cancel() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      // 取消接口请求
      if (this.reqCancelHandler) {
        this.reqCancelHandler();
      }
    },
    // 清空数据
    clear() {
      this.tips = 2;
      this.current = 1;
      this.dataSourceId = null;
      this.status = null;
      this.isCheck = false;
      this.resultData = {};
      this.formData = {};
      this.errorData = [];
      this.dataSource = [];
      this.fileList = [];
    },
    // 继续导入
    carryOn() {
      this.cancel();
      this.clear();
    }
  }
};
</script>

<style lang="less" scoped>
.import-of-statistics-info {
  padding: 30px 0;
  /deep/.database-content {
    padding: 0 80px;
    // margin-bottom: 16px;
    .form-area {
      display: flex;
      .ant-form {
        &.database-form {
          .ant-row {
            display: flex;
            .ant-col {
              width: auto;
              // padding-right: 18px;
              .ant-form-item {
                margin-bottom: 24px !important;
                .ant-form-item-label {
                  width: 120px;
                  height: 36px;
                  line-height: 36px;
                  .ant-form-item-no-colon {
                    margin-left: 18px;
                    justify-content: flex-start;
                    > span {
                      font-size: 14px;
                      color: #27272a;
                      font-weight: 400;
                    }
                  }
                }
                .ant-form-item-control-wrapper {
                  .ant-form-item-control {
                    line-height: 36px;
                    .ant-form-item-children {
                      .ant-select {
                        .ant-select-selection {
                          width: 600px;
                          min-height: 36px;
                          .ant-select-selection__rendered {
                            line-height: 36px;
                            .ant-select-selection-selected-value {
                              height: 36px;
                              .biz-instance-item >.instance-item-tag {
                                height: 34px;
                                padding: 0px 16px !important;
                                border: none !important;
                                margin-left: 0px;
                                .ant-tag,
                                .database-image {
                                  transition: none;
                                }
                              }
                            }
                            .ant-select-arrow {
                              color: #27272a;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      .check-auth-btn {
        width: 80px;
        height: 36px;
        color: #008adc;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #008adc;
        margin-left: 12px;
      }
      .tips {
        margin-left: 12px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        > span {
          margin-left: 4px;
        }
        &.exclamation-circle {
          font-size: 12px;
          color: #ef6173;
        }
        &.check-circle {
          font-size: 12px;
          color: #4cbb3a;
        }
        &.info-circle {
          font-size: 12px;
          color: #f29339;
        }
      }
    }
    .error-info {
      margin-left: 120px;
      padding-bottom: 8px;
      margin-bottom: 24px;
      width: 600px;
      border: 1px solid #d9d9d9;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #eff5ff;
        padding: 4px 12px;
        > div {
          font-size: 13px;
          color: #a1a1a1;
        }
      }
      .content {
        span {
          padding: 8px 28px;
          font-size: 13px;
          color: #27272a;
          font-weight: 400;
        }
      }
    }
  }
  /deep/.file-import-content {
    .file-import-action-area {
      padding-left: 116px;
      padding-bottom: 12px;
      > .label {
        color: #000000;
        margin-right: 9px;
        &::before {
          display: inline-block;
          margin-right: 8px;
          color: #f5222d;
          font-size: 14px;
          content: '*';
          position: relative;
          top: 3px;
        }
      }
      .ant-upload {
        .file-import-btn {
          font-size: 13px;
          color: #27272a;
          padding: 4px 21px;
          border: 1px solid #d9d9d9;
        }
        .tips {
          font-size: 12px;
          color: #bfbfbf;
          margin-left: 4px;
        }
      }
      .ant-upload-list {
        display: none;
      }
    }
    .file-import-table-edit {
      padding-left: 200px;
      .table-edit {
        .ant-form {
          .ant-table-wrapper {
            .ant-table {
              width: 600px;
              border: 1px solid #d9d9d9;
              .ant-table-body {
                .ant-table-thead {
                  > tr {
                    > th {
                      border-right: 1px solid #d9d9d9;
                      &:first-child {
                        border-right: none;
                      }
                    }
                  }
                }
                .ant-table-tbody {
                  > tr {
                    > td {
                      border-bottom: 1px solid #ebebec;
                      border-right: 1px solid #ebebec;
                      &:last-child {
                        border-right: none;
                      }
                      .ant-row {
                        .ant-form-item-control-wrapper {
                          .ant-form-item-control {
                            .ant-form-item-children {
                              .ant-select-search,
                              .ant-select {
                                .ant-select-selection {
                                  border: none;
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /deep/.btn-area {
    padding-left: 200px;
    margin-top: 32px;
    display: flex;
    .ant-btn {
      display: flex;
      align-items: center;
      > span {
        padding: 0 24px;
      }
    }
  }
}

@media screen and (max-width: 1440px) {
  .import-of-statistics-info {
    /deep/.database-content {
      .form-area {
        .ant-form {
          &.database-form {
            .ant-row {
              .ant-col {
                .ant-form-item-control-wrapper {
                  .ant-form-item-control {
                    .ant-form-item-children {
                      .ant-select {
                        .ant-select-selection {
                          width: 540px !important;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      .error-info {
        width: 540px;
      }
    }
    /deep/.file-import-content {
      .file-import-table-edit {
        .table-edit {
          .ant-form {
            .ant-table-wrapper {
              .ant-table {
                width: 540px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
