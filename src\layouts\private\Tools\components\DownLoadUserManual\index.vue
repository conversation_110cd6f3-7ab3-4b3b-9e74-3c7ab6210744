<template>
  <!-- 下载用户手册 -->
  <div class="download-user-manual" @click="dowmLoadUser">
    <a>用户操作手册下载</a>
  </div>
</template>

<script>
import { downLoadUserManual } from '@/api/home';

export default {
  components: {},
  props: {},
  data() {
    return {};
  },
  mounted() {},
  created() {},
  methods: {
    // 用户手册下载
    dowmLoadUser() {
      this.$showLoading({ tips: '正在下载' });
      downLoadUserManual()
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.log(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
