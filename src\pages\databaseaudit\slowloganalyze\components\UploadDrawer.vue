<template>
  <!-- 新建项目抽屉 -->
  <a-drawer
    title="上传慢日志"
    :visible="visible"
    @close="hide"
    width="600px"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="database-slow-log-upload-drawer"
  >
    <!-- 内容区域 -->
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="formParams" :formData="data"></Form>
      <a-upload-dragger
        name="file"
        :before-upload="beforeUpload"
        :remove="() => this.fileList = []"
        :file-list="fileList"
        v-if="!isEdit"
      >
        <p class="ant-upload-drag-icon">
          <a-icon type="plus" />
        </p>
        <p class="ant-upload-text">
          将文件拖拽到此处，或
          <span class="tips">点击上传</span>
        </p>
        <!-- <p class="ant-upload-text">支持.zip .tar .sql .xml .txt文件格式，单个文件大小小于2M</p> -->
      </a-upload-dragger>
      <div v-else class="edit-show-block">
        <div class="label">日志文件：</div>
        <div class="value">
          <div>
            <custom-icon type="paper-clip"></custom-icon>
            <LimitLabel :label="fileName" :limit="16" mode="simple"></LimitLabel>
          </div>
          <div>上传成功</div>
        </div>
      </div>
    </a-spin>
    <!-- 按钮区域 -->
    <div
      class="btns-area"
      :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 10,
        }"
    >
      <a-button @click="hide" class="hide-btn">取消</a-button>
      <a-button @click="save" type="primary">保存</a-button>
    </div>
  </a-drawer>
</template>

<script>
import config from './config';
import Form from '@/components/Form';
import LimitLabel from '@/components/LimitLabel';
import { uploadSlowLog, updateSlowLog } from '@/api/databaseaudit/slowlog';

export default {
  components: { Form, LimitLabel },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      spinning: false,
      data: {},
      formData: null,
      formParams: {
        fields: this.config.fields,
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 4 },
        wrapperCol: { span: 12 },
        colon: true
      },
      id: null,
      isEdit: false,
      fileList: [],
      fileName: ''
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(pane, bool) {
      this.visible = true;
      this.isEdit = bool;
      this.id = pane.id;
      this.data = { data_source_id: pane.data_source_id };
      this.fileName = pane.file_simpleness_name;
    },
    hide() {
      this.cancel();
    },
    cancel() {
      this.visible = false;
      this.formData = null;
      this.data = {};
    },
    // 上传之前
    beforeUpload(file) {
      // const maxSize = 2 * 1024 * 1024; // byte
      // if (file.size > maxSize) {
      //   this.$message.error('文件大小错误，文件大小不超过2MB');
      //   return;
      // }
      // if (!/\.(zip|tar|sql|xml|txt)$/.test(file.name)) {
      //   this.$message.error(
      //     '文件格式错误，文件类型支持.zip .tar .sql .xml .txt'
      //   );
      //   return;
      // }
      if (!this.isEdit) {
        this.$set(this.fileList, 0, file);
      }
      return false;
    },
    // 保存
    save() {
      const { form } = this.$refs;
      if (this.fileList.length === 0 && !this.isEdit) {
        this.$message.warning('请上传文件');
        return;
      }
      const data = form.getData();
      this.formData = new FormData();
      this.formData.append('file', this.fileList[0]);
      this.formData.append('data_source_id', data.data_source_id || '');

      Promise.all([form.validate()]).then(valid => {
        if (valid) {
          this.spinning = true;
          const req = this.isEdit ? updateSlowLog : uploadSlowLog;
          const params = this.isEdit
            ? { data_source_id: data.data_source_id, record_id: this.id }
            : this.formData;
          req(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.spinning = false;
                this.cancel();

                this.$emit('save');
                this.$hideLoading({ duration: 0 });
              } else {
                this.spinning = false;
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(e || {}, 'response.data.message') || '请求失败'
                });
              }
            })
            .catch(e => {
              this.spinning = false;
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    }
  }
};
</script>

<style lang="less">
.database-slow-log-upload-drawer {
  .ant-drawer-content-wrapper {
    width: 720px;
    // .ant-drawer-header {
    //   background: #fff;
    //   border-bottom: none;
    //   .ant-drawer-title {
    //     font-size: 20px;
    //     color: #27272a;
    //     font-weight: 600;
    //   }
    //   .ant-drawer-close {
    //     .anticon {
    //       font-size: 16px;
    //       color: #27272a;
    //     }
    //   }
    // }
    .ant-drawer-body {
      .ant-form {
        .ant-row {
          .ant-form-item-label {
            width: 100px;
          }
          .ant-form-item-control-wrapper {
            width: 360px;
          }
        }
      }
      .ant-upload-drag {
        margin-left: 100px;
        width: 360px;
        height: 160px;
        // background: #f4f5f7;
        // border: 1px dotted #a1a1aa;
        .ant-upload-drag-icon {
          // .anticon {
          //   font-size: 36px;
          //   color: #a1a1aa;
          // }
        }
        .ant-upload-text {
          // font-size: 13px;
          // color: #a1a1aa;
          // .tips {
          //   color: #1890ff;
          // }
        }
      }
      .ant-upload-list {
        padding: 0 12px;
        margin-left: 100px;
        width: 360px;
        margin-top: 16px;
        background: rgba(91, 147, 255, 0.1);
        > div {
          > span {
            .ant-upload-list-item {
              margin: 8px 0;
              .anticon {
                font-size: 16px;
              }
              .ant-upload-list-item-name {
                font-size: 13px;
                color: #27272a;
                margin-top: 2px;
              }
              .ant-upload-list-item-card-actions {
                opacity: 1;
                margin-top: 2px;
                .anticon {
                  font-size: 16px;
                  color: #008adc;
                }
              }
            }
          }
        }
        .ant-upload-list-item:hover .ant-upload-list-item-info {
          background-color: rgba(91, 147, 255, 0);
        }
      }
      .edit-show-block {
        display: flex;
        align-items: center;
        .label {
          color: rgba(0, 0, 0, 0.85);
          width: 100px;
          padding-right: 8px;
          display: flex;
          justify-content: flex-end;
        }
        .value {
          width: 360px;
          padding: 8px 16px;
          border-radius: 5px;
          display: flex;
          justify-content: space-between;
          color: rgba(0, 0, 0, 0.85);
          background: #eff5ff;
          .anticon {
            font-size: 15px;
          }
          .limit-label {
            pre {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
</style>
