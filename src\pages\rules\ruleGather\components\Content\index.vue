<template>
  <div class="config-rules-content">
    <!-- 基础信息 -->
    <div class="rules-gather-base-info">
      <a-card style="width: 100%;" class="common-pure-card" :bordered="false">
        <span class="title">
          <a-icon type="profile" />基础信息
        </span>
        <Form
          v-if="type == 'add'"
          ref="baseInfo"
          class="base-info-form"
          v-bind="baseInfoParams"
          :formData="baseInfoData"
        />

        <div class="base-info-box" v-else>
          <div class="info-row">
            <div>
              <span>规则集类型</span>
              <span>{{baseInfoData.rule_type}}</span>
            </div>
            <div>
              <span>数据库类型</span>
              <DbImg
                :type="baseInfoData.db_type"
                :schemaName="baseInfoData.db_type"
                class="db-type-span"
              />
            </div>
          </div>
          <div class="info-row">
            <div>
              <span>规则集名称</span>
              <span>{{baseInfoData.name}}</span>
            </div>
          </div>
        </div>
      </a-card>
    </div>
    <!-- 规则配置 -->
    <div class="rule-gather-config">
      <a-card style="width: 100%;" class="common-pure-card" :bordered="false">
        <span class="title">
          <a-icon type="setting" />规则配置
        </span>
        <div class="container">
          <div class="search-area">
            <div class="rule-type">
              <span>DDL</span>
            </div>
            <SearchArea v-bind="searchParams" @reset="reset" @search="search"></SearchArea>
          </div>
          <Table
            ref="tableEdit"
            v-bind="tableParams  || {}"
            @selectChange="selectChange"
            class="new-view-table"
          >
            <template v-slot:rule_category="{text}">
              <span :style="{color: ruleCategoryColor[text] }">{{ text }}</span>
            </template>
            <LimitLabel slot="rule_name" slot-scope="{text}" :label="text" :limit="18"></LimitLabel>
            <LimitLabel slot="rule_desc" slot-scope="{text}" :label="text" :limit="18"></LimitLabel>
            <span slot="action" slot-scope="{ record }">
              <a @click="modify(record.rule_uid)">编辑</a>
            </span>
          </Table>
        </div>
        <div class="choose">
          <p>已选择{{idsLength}}条</p>
        </div>
      </a-card>
    </div>
    <ruleModal v-if="type !=='detail'" ref="ruleModal" @tableUpdate="tableUpdate"></ruleModal>
  </div>
</template>
<script>
import Form from '@/components/Form';
import Table from '@/components/Table';
import Select from '@/components/Select';
import SearchArea from '@/components/SearchArea';
import LimitLabel from '@/components/LimitLabel';
import ruleModal from './ruleModal';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  mixins: [bodyMinWidth(1280)],
  components: { Form, Table, Select, ruleModal, SearchArea, LimitLabel },
  props: {
    type: {
      type: String,
      default: 'add'
    },
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    },
    rule_type: {
      type: String,
      default: ''
    }
  },
  data() {
    this.config = config(this);
    return {
      form: this.$form.createForm(this, {}),
      baseInfoData: { rule_type: this.rule_type, db_type: '--' },
      baseInfoParams: {
        multiCols: 2,
        layout: 'vertical',
        fields: this.config.baseInfo
      },
      tableParams: {
        url: '/sqlreview/project/ddl_rule_list_page',
        reqParams: {},
        columns: this.config.columns,
        rowSelection: {},
        // scorll: { x: 'max-content' },
        isInitReq: false,
        needCache: true,
        rowKey: 'rule_uid',
        pagination: {}
      },
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 3
      },
      searchData: {},
      ruleCategoryColor: {
        DDL: '#4CBB3A',
        DML: '#F29339'
      },
      idsLength: 0,
      ids: [],
      id: null,
      db_type: null
    };
  },
  created() {},
  mounted() {
    this.type == 'add' &&
      this.$set(this.tableParams, 'reqParams', { _t: new Date() });
  },
  destroyed() {},
  methods: {
    reset() {
      this.$refs.tableEdit.refresh();
    },
    search(data) {
      this.searchData = data;
      this.$refs.tableEdit.refresh(null, { ...data, db_type: this.db_type });
    },
    selectChange(data) {
      this.idsLength = data.selectedRowKeys.length;
    },
    tableUpdate(data) {
      const { tableEdit } = this.$refs;
      this.idsLength = 0;
      tableEdit.refresh(null, { ...this.searchData, db_type: this.db_type });
      tableEdit.setSelectedInfo(this.ids);
    },
    modify(id) {
      this.$refs.ruleModal.show(id);
    },
    onSelectChange(e) {
      this.idsLength = 0;
      this.db_type = e;
      this.$set(this.tableParams, 'reqParams', {
        db_type: e
      });
    }
  },
  watch: {
    dataSource: {
      handler(newVal, oldVal) {
        const { tableEdit } = this.$refs;
        this.baseInfoData = {
          rule_type: 'DDL',
          name: newVal.name,
          db_type: newVal.db_type
        };
        newVal.rules.map(item => {
          this.ids.push(item.rule_uid);
        });
        this.id = newVal.rule_set_id;
        this.db_type = newVal.db_type;
        this.idsLength = newVal.rules.length;
        tableEdit.refresh(null, {
          db_type: newVal.db_type,
          rule_set_id: newVal.rule_set_id
        });
        tableEdit.setSelectedInfo(this.ids);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.config-rules-content {
  .rules-gather-base-info {
    /deep/ .base-info-form {
      padding-top: 12px;
      > .ant-row {
        .ant-col-12 {
          padding-right: 24px !important;
          .ant-form-item {
            .ant-form-item-label {
              label {
                > span {
                  font-size: 14px;
                  color: #27272a;
                  font-weight: 400;
                }
              }
            }
            .ant-form-item-control-wrapper {
              flex-grow: 1;
              .ant-form-item-children {
                .ant-input {
                  height: 36px;
                }
              }
              .ant-select-selection {
                height: 36px;
                .ant-select-selection__rendered {
                  line-height: 36px;
                }
              }
            }
          }
        }
      }
    }
    .base-info-box {
      .info-row {
        display: flex;
        padding: 15px 0;
        border-bottom: 1px solid #f2f2f2;
        &:last-child {
          border-bottom: none;
        }
        > div {
          width: 50%;
          display: flex;
          align-items: center;
          > span {
            font-size: 14px;
            font-weight: 400;
            &:first-child {
              width: 100px;
              color: #27272a;
              white-space: nowrap;
            }
            &:last-child {
              color: #a1a1aa;
            }
          }
          .db-type-span {
            .anticon {
              font-size: 16px;
            }
            .iconText {
              pre {
                font-size: 14px;
                font-weight: 400;
                color: #a1a1aa;
              }
            }
          }
        }
      }
    }
  }
  .rule-gather-config {
    /deep/.common-pure-card {
      position: relative;
      .ant-card-body {
        .container {
          .search-area {
            padding: 12px 16px 0 0;
            margin-bottom: 12px;
            border-radius: 2px;
            border: none;
            box-shadow: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .rule-type {
              > span {
                font-size: 14px;
                color: #71717a;
                font-weight: 400;
                padding: 4px 0;
                width: 52px;
                border-radius: 16px;
                background: #f4f5f7;
                display: inline-block;
                text-align: center;
                border: 1px solid #f4f5f7;
              }
            }
            .ant-row {
              display: flex;
              justify-content: flex-end;
              .ant-col {
                width: 210px;
                height: 32px;
                padding: 0 !important;
                margin-left: 8px;
                &:last-child {
                  width: auto;
                  margin-left: 0px;
                  .seach-area-btns {
                    .ant-btn:last-child {
                      margin-left: 4px;
                    }
                  }
                }
              }
            }
          }
          .new-view-table {
            .ant-table-wrapper {
              .ant-table {
                .ant-table-content {
                  .ant-table-thead {
                    tr {
                      th {
                        &.ant-table-selection-column {
                          display: flex;
                          justify-content: flex-start;
                          padding: 16px 8px !important;
                        }
                      }
                    }
                  }
                  .ant-table-tbody {
                    tr {
                      &.ant-table-row-selected {
                        > td {
                          background: #eff5ff;
                        }
                      }
                      > td {
                        &.ant-table-selection-column {
                          display: flex;
                          justify-content: flex-start;
                          padding: 17px 8px !important;
                        }
                        .limit-label {
                          pre {
                            display: inline-block;
                            margin: 0;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        .choose {
          font-size: 14px;
          color: #008adc;
          font-weight: 600;
          position: absolute;
          bottom: 40px;
        }
      }
    }
  }
  .btns {
    position: absolute;
    top: 0;
    right: 0;

    &.edit {
      top: -72px;
    }
  }
}
</style>
