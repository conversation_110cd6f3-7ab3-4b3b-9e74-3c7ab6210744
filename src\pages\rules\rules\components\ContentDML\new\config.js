export default function (ctx) {
  const dbType = window.localStorage.getItem('db_type') || '';
  const baseInfo = [
    {
      type: 'Select',
      key: 'db_type',
      label: '数据库类型',
      compIcon: 'lu-icon-database',
      props: {
        placeholder: '数据库类型',
        url: '/sqlreview/review/select_db_type',
        getPopupContainer: (el) => {
          return document.body;
        }
      },
      listeners: {
        change(value) {
          const baseInfo = ctx.$refs.baseInfo;
          baseInfo.saving({
            db_type: value,
            ob_mode: null,
            rule_set_uids: null
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    },
    {
      type: 'RadioGroup',
      label: '规则类型',
      key: 'rule_type',
      props: {
        mode: 'tips',
        class: 'inline',
        options: ['IMPALA', 'HIVE'].includes(dbType)
          ? [
            {
              label: 'DML规则',
              value: 'DML'
            }
          ]
          : [
            {
              label: 'DML规则',
              value: 'DML'
            },
            {
              label: 'DDL规则',
              value: 'DDL'
            }
          ],
        disabled: ctx.type !== 'add'
      },
      listeners: {
        change: (value) => {
          const baseInfo = ctx.$refs.baseInfo;
          baseInfo.saving({
            rule_type: value
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Input',
      label: '规则名称',
      key: 'name',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'RadioGroup',
      label: '风险类型',
      key: 'rule_result',
      props: {
        mode: 'tips',
        class: 'inline',
        options: [
          {
            label: '高风险 (不通过)',
            value: 0
          },
          {
            label: '低风险 (通过)',
            value: 1
          }
        ]
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Textarea',
      label: '规则描述',
      key: 'desc',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    (formData = {}) => {
      return {
        type: 'Select',
        label: '所属规则集',
        key: 'rule_set_uids',
        props: {
          // mode: 'tips',
          // class: 'inline',
          // options: [...ctx.rulesOptions]
          mode: 'multiple',
          url: '/sqlreview/project/rule_set_all',
          reqParams: {
            db_type: formData.ob_mode || formData.db_type
          }
        }
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      };
    }
  ];
  const outportResults = [
    {
      type: 'Markdown',
      label: '优化建议',
      key: 'suggest',
      className: 'suggest',
      props: {}
    }
  ];

  return {
    baseInfo,
    outportResults
  };
}
