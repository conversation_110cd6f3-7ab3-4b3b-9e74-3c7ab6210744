<template>
  <div class="quick-audit-initiated-by-me">
    <Table ref="table" v-bind="tableParams || {}" class="new-view-table">
      <!-- table插槽 -->
      <div slot="datasource_name" slot-scope="{ record, text }" class="database-name-box">
        <div v-if="record.audit_type == 1" class="datasource-name">
          <Tag type="Env" :text="record.env.toUpperCase()" />
          <DbImg
            :value="record.db_type"
            :schemaName="`${text}@${record.user}` || '--'"
            :limit="16"
          />
        </div>
        <div v-else class="datasource-name">{{record.db_type}}</div>
      </div>
      <template slot="status" slot-scope="{ text,record }">
        <!-- <a-tag :color="statusColor[record.status]">{{statusText[text]}}</a-tag> -->
        <StatusTag type="quick" :status="text" />
      </template>
      <custom-btns-wrapper slot="action" slot-scope="{ text, record }" :limit="3">
        <a @click="toDetail(record)" actionBtn>详情</a>
      </custom-btns-wrapper>
    </Table>
  </div>
</template>

<script>
import Table from '@/components/Table';
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import StatusTag from '@/components/Biz/Status/Tag';
import config from './config';

export default {
  components: {
    Tag,
    DbImg,
    Table,
    StatusTag
  },
  props: {
    activeKey: String
  },
  data() {
    this.config = config(this);
    return {
      statusText: this.config.statusText,
      statusColor: this.config.statusColor,
      tableParams: {
        url: '/sqlreview/quick_audit/quick_review',
        reqParams: {
          is_all: true
        },
        columns: this.config.initiatedColumns,
        rowKey: 'id',
        needTools: true,
        // needSearchArea: true,
        searchFields: this.config.initiatedSearchFields,
        scroll: { x: 'max-content' },
        pagination: {
          size: ''
        }
      }
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    toDetail(record) {
      this.$router.push({
        name: 'quickAuditDetail',
        query: { quick_audit_id: record.quick_audit_id }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.database-name-box {
  display: inline-block;
  .datasource-name {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border: 1px solid rgba(228, 228, 231, 1);
    border-radius: 4px;
    // width: 270px;
    > .ant-tag {
      margin-right: 0;
      padding: 0 7px 0 0;
      border: none;
      font-size: 14px;
      color: #a1a1aa;
      font-weight: 400;
      background-color: #fff !important;
    }
    > .database-image {
      /deep/.iconClass {
        .limit-label {
          pre {
            font-size: 14px;
            color: #27272a;
            font-weight: 400;
          }
        }
      }
    }
  }
}
</style>