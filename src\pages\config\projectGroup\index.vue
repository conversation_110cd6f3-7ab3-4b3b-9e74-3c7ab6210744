<template>
  <div class="pages-config-project-group page-list-single">
    <div class="frame-button-wrapper">
      <a-button
        @click="projectGroupImport"
        v-if="$permission.projectGroup('import')"
        class="highlight"
        >项目组导入</a-button
      >
      <a-button
        slot="extra"
        class="highlight"
        icon="plus"
        @click="add"
        v-if="$permission.projectGroup('add')"
        >添加项目组</a-button
      >
    </div>
    <Table
      ref="table"
      v-bind="tableParams || {}"
      class="new-view-table small-size"
    >
      <span slot="gr_mgr" slot-scope="{ record }">
        <LimitTags
          :tags="record.gr_mgr_name.map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </span>
      <span slot="dba_mgr" slot-scope="{ record }">
        <LimitTags
          :tags="record.dba_mgr_name.map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </span>
      <span slot="projects_name" slot-scope="{ text }">
        <LimitTags
          :tags="text.map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </span>
      <span slot="user_set" slot-scope="{ text }">
        <LimitTags
          :tags="text.map((item) => ({ label: item.user_label }))"
          :limit="1"
        ></LimitTags>
      </span>
      <span slot="gr_email" slot-scope="{ text }">
        <LimitLabel :label="text" :limit="24"></LimitLabel>
      </span>
      <custom-btns-wrapper slot="action" slot-scope="{ record }">
        <a
          @click="editProject(record)"
          actionBtn
          v-if="$permission.projectGroup('edit')"
          >编辑</a
        >
        <a-popconfirm
          title="确定删除?"
          @confirm="() => remove(record)"
          actionBtn
          v-if="$permission.projectGroup('delete')"
        >
          <a class="remove">删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </Table>
    <div class="config">
      <a-drawer
        title="项目组配置"
        :width="'40%'"
        :visible="visible"
        :bodyStyle="{ padding: '24px 24px 80px 24px' }"
        wrapClassName="config-project-group-model"
        @close="handleCancel"
      >
        <Form
          ref="form"
          class="base-info-form"
          v-bind="formParams"
          :formData="formData"
        ></Form>
        <div
          :style="{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
            zIndex: 1
          }"
        >
          <a-button @click="handleCancel">取消</a-button>
          <a-button @click="handleOk" type="primary">确定</a-button>
        </div>
      </a-drawer>
    </div>
  </div>
</template>
<script>
import LimitTags from '@/components/LimitTags';
import Table from '@/components/Table';
import Form from '@/components/Form';
import LimitLabel from '@/components/LimitLabel';
import {
  addProjectGroup,
  saveProjectGroup,
  getDleProject
} from '../../../api/config/projectGroup';
import config from './config';

export default {
  name: 'project-config',
  components: {
    Table,
    Form,
    LimitLabel,
    LimitTags
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      dataSource: [],
      isEdit: false,
      id: null,
      tableParams: {
        url: '/sqlreview/project_config/list_project_group',
        reqParams: {},
        columns: this.config.columns.filter(item => item.visible != false),
        rowKey: 'id',
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 'max-content' }
      },
      formData: {},
      formParams: {
        gutter: 32,
        colon: true,
        // layout: 'vertical',
        multiCols: 1,
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
        fields: this.config.fields()
      }
    };
  },
  computed: {},
  methods: {
    // 项目导入
    projectGroupImport() {
      this.$router.push({ name: 'project-group-import-config' });
    },
    // 编辑项目
    editProject(record) {
      this.visible = true;
      this.formData = record;
      const userSet = _.get(record, 'user_set')
      const _userSet = userSet.map(item => item.id);
      this.formData = {
        ...this.formData,
        gr_bind_user: _userSet
      }
      this.id = record.id;
      this.isEdit = true;
    },
    // 创建新项目组
    add() {
      this.visible = true;
    },
    // 弹窗确定按钮
    handleOk() {
      const { form, table } = this.$refs;

      form.validate(valid => {
        if (valid) {
          const params = form.getData() || {};
          if (this.isEdit) params.id = this.id;
          const req = this.isEdit ? saveProjectGroup : addProjectGroup;
          this.$showLoading();
          req(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({
                  tips: _.get(res, 'data.message')
                });
                table.refresh();
                this.handleCancel();
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    // 弹窗取消事件
    handleCancel(e) {
      this.visible = false;
      this.isEdit = false;
      this.formData = {};
    },
    // 删除项目
    remove(e) {
      const { table } = this.$refs;
      this.$showLoading();
      getDleProject({
        id: e.id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.config-project-group-model {
  /deep/ .ant-drawer-content-wrapper {
    min-width: 660px;
  }
  /deep/.table-edit-list-like .ant-table-tbody td {
    padding: 4px 16px 4px 0px !important;
  }
}
</style>
