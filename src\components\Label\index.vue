<template>
  <span class="custom-label">
    <span>{{showLabel}}</span>
  </span>
</template>

<script>
// import _ from 'lodash';
// import Http from '@/utils/request';
// import _ from 'lodash';

export default {
  inheritAttrs: false,
  components: {},
  props: {
    defaultValue: String | Number,
    value: String | Number
  },
  data() {
    return {};
  },
  computed: {
    showLabel() {
      const { value, defaultValue } = this;
      return value !== undefined ? value : defaultValue;
    }
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
</style>
