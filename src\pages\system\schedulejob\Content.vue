<template>
  <div class="schedule-job-tabs-content">
    <div class="table-content">
      <div class="top-info">
        <div class="left">
          <a-radio-group
            v-model="value"
            button-style="solid"
            v-if="type == 'database'"
          >
            <a-radio-button value="JDBC"
              >JDBC调度{{ `(${jdbcCount})` }}</a-radio-button
            >
            <a-radio-button value="OpenAPI"
              >OpenAPI调度{{ `(${count})` }}</a-radio-button
            >
          </a-radio-group>
          <a-radio-group
            v-model="value"
            button-style="solid"
            v-if="type == 'system'"
          >
            <a-radio-button value="ClearData"
              >清理数据{{ `(${systemCount})` }}</a-radio-button
            >
          </a-radio-group>
          <a-radio-group
            v-model="value"
            button-style="solid"
            v-if="type == 'agentMaster'"
          >
            <a-radio-button value="AgentMaster"
              >agent master{{ `(${agentMasterCount})` }}</a-radio-button
            >
          </a-radio-group>
        </div>
        <div class="right">
          <Form
            ref="form"
            v-bind="formParams"
            :formData="formData"
            :iconCombine="true"
            class="search-form"
          ></Form>
          <div class="seach-area-btns">
            <a-button @click="search">查询</a-button>
            <a-tooltip>
              <template slot="title">重置</template>
              <custom-icon type="lu-icon-clean" @click="reset" />
            </a-tooltip>
          </div>
        </div>
      </div>
      <Table
        ref="table"
        v-bind="tableParams"
        :dataSource="dataSource || []"
        class="new-view-table small-size"
      >
        <span class="job-name-box" slot="job_name" slot-scope="{ text }">
          <LimitLabel :label="text || ''" mode="ellipsis"></LimitLabel>
        </span>
        <a
          slot="name"
          slot-scope="{ text, record }"
          @click="toDetail(record, 'agent')"
          >{{ text }}</a
        >
        <LimitLabel
          slot="msg"
          slot-scope="{ text }"
          :label="text"
          :limit="24"
        ></LimitLabel>
        <template slot="agent-status" slot-scope="{ text }">
          <span :class="`status-${text}`">{{
            text == 1 ? '在线' : '离线'
          }}</span>
        </template>

        <custom-btns-wrapper slot="action" slot-scope="{ record }">
          <a actionBtn @click="toDetail(record, 'job')">详情</a>
        </custom-btns-wrapper>
      </Table>
    </div>
  </div>
</template>

<script>
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import Form from '@/components/Form';
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import config from './config';
import {
  getOpenApiInfo,
  getJDBCInfo,
  getAgentMasterCount
} from '@/api/config/schedulejob';
export default {
  mixins: [bodyMinWidth(1280)],
  components: {
    Tag,
    Form,
    DbImg,
    Table,
    LimitLabel
  },
  props: {
    type: String,
    query: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    this.config = config(this);
    return {
      value:
        this.type === 'database'
          ? 'JDBC'
          : this.type == 'agentMaster'
          ? 'AgentMaster'
          : 'ClearData',
      formData: {},
      formParams: {
        layout: 'horizontal',
        multiCols: 3,
        fields: this.config.searchFields('job_jdbc_choose')
      },
      dataSource: [],
      tableParams: {
        url: ``,
        reqParams: {},
        columns: [],
        rowKey: this.type == 'agentMaster' ? 'id' : 'job_uuid',
        scroll: { x: 'max-content' }
      },
      statusText: {
        0: '执行中',
        1: '成功',
        2: '失败'
      },
      count: 0,
      jdbcCount: 0,
      systemCount: 0,
      agentMasterCount: 0
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.getOpenApiInfoFn();
    this.getJDBCInfoFn();
    if (this.type == 'agentMaster') this.getAgentMasterCountFn();
  },
  methods: {
    getOpenApiInfoFn() {
      if (this.type == 'system') return;
      getOpenApiInfo()
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.count = _.get(res, 'data.data.count') || 0;
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getJDBCInfoFn() {
      const params = { is_sys: this.type == 'system' ? 1 : 0 };
      getJDBCInfo(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const count = _.get(res, 'data.data.count') || 0;
            this.type == 'system'
              ? (this.systemCount = count)
              : (this.jdbcCount = count);
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getAgentMasterCountFn() {
      getAgentMasterCount()
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.agentMasterCount = _.get(res, 'data.data.count') || 0;
            this.$hideLoading({ duration: 0 });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 查询
    search() {
      const { table, form } = this.$refs;
      const data = form.getData();
      const { searchParams } = table;
      Object.assign(searchParams, { ...data });
      table.refresh(null, data);
    },
    // 重置
    reset() {
      this.formData = {};
      const { table } = this.$refs;
      table.searchParams = {};
      table.refresh();
    },
    toDetail(record, activeKey) {
      const query =
        activeKey == 'job'
          ? {
              type: this.value,
              id: record.data_source_id
                ? record.data_source_id
                : record.job_uuid,
              job_name: record.job_name,
              activeKey
            }
          : {
              id: record.id,
              name: record.name,
              activeKey
            };
      this.$router.push({
        name: 'schedule-job-detail',
        query
      });
    }
  },
  watch: {
    value: {
      handler(newVal) {
        switch (newVal) {
          case 'JDBC':
            this.$set(
              this.tableParams,
              'columns',
              this.config.columns(newVal).filter(item => item.visible !== false)
            );
            if (!_.isEmpty(this.query)) {
              this.formData = { service_id: this.query.id, job_name: 'TopSQL' };
            }

            this.$set(
              this.tableParams,
              'reqParams',
              !_.isEmpty(this.query)
                ? { is_sys: 0, service_id: this.query.id, job_name: 'TopSQL' }
                : { is_sys: 0 }
            );
            this.$set(this.tableParams, 'url', '/sqlreview/api/v1/schedules/');
            this.$set(
              this.formParams,
              'fields',
              this.config.searchFields('job_jdbc_choose')
            );
            break;
          case 'OpenAPI':
            this.$set(
              this.tableParams,
              'columns',
              this.config.columns(newVal).filter(item => item.visible !== false)
            );
            this.$set(this.tableParams, 'reqParams', {});
            this.$set(
              this.tableParams,
              'url',
              '/sqlreview/after_audit/job_list'
            );

            this.$set(
              this.formParams,
              'fields',
              this.config.searchFields('job_openApi_choose')
            );
            break;
          case 'ClearData':
            this.$set(this.tableParams, 'columns', this.config.systemColumns);
            this.$set(this.tableParams, 'reqParams', { is_sys: 1 });
            this.$set(this.tableParams, 'url', '/sqlreview/api/v1/schedules/');
            this.$set(
              this.formParams,
              'fields',
              this.config
                .searchFields('job_jdbc_choose', 'ClearData')
                .filter(item => item.visible !== false)
            );
            break;
          case 'AgentMaster':
            this.$set(
              this.tableParams,
              'columns',
              this.config.agentMasterColumns
            );
            this.$set(this.tableParams, 'reqParams', {});
            this.$set(this.tableParams, 'url', '/sqlreview/agent/agent');
            this.$set(
              this.formParams,
              'fields',
              this.config.agentMasterSearchFields
            );
            break;
          default:
            break;
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.schedule-job-tabs-content {
  padding: 0 24px;
  /deep/.table-content {
    background: #fff;
    border-radius: 16px;
    .top-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0;
      .left {
        display: flex;
        width: 30%;
        margin-bottom: 12px;
        .ant-radio-group {
          .ant-radio-button-wrapper {
            transition: none;
          }
        }
      }
      .right {
        width: 70%;
        display: flex;
        justify-content: space-between;
        .ant-form {
          flex-grow: 1;
          height: 48px;
          padding-right: 24px;
          overflow: hidden;
          * {
            transition: none;
          }
          > .ant-row {
            display: flex;
            justify-content: flex-end;
            > .ant-col {
              vertical-align: top;
              padding-left: 12px !important;
              padding-right: 12px !important;
              .ant-form-item {
                display: flex;
                margin: 0 0 8px 0;
                .ant-form-item-label {
                  display: none;
                }
                .ant-form-item-control-wrapper {
                  flex-grow: 1;
                  flex: auto;
                  width: auto;
                  // max-width: 240px;
                  // min-width: 160px;
                }
              }
            }
          }
        }
        .seach-area-btns {
          padding-top: 4px;
          white-space: nowrap;
          .ant-btn {
            padding: 2px 12px;
            border-radius: 4px !important;
            border: 1px solid #a5d9f8;
            > span {
              font-size: 14px;
              color: #008adc;
              font-weight: 400;
            }
          }
          .anticon {
            font-size: 16px;
            margin: 0 12px;
            border-radius: 50%;
            &:hover {
              color: #000;
              cursor: pointer;
            }
          }
          &:hover {
            .ant-btn {
              span {
                color: #25a7e8;
              }
            }
          }
        }

        &.icon-combine {
          .ant-form {
            > .ant-row {
              > .ant-col {
                padding-left: 24px !important;
                padding-right: 0 !important;
                .ant-form-item {
                  .ant-form-item-label {
                    max-width: 0;
                    min-width: 0;
                  }
                  .ant-form-item-control-wrapper {
                    flex-grow: 1;
                    flex: auto;
                    width: auto;
                    max-width: 100%;
                    min-width: 310px;
                  }
                }
              }
            }
          }
          .seach-area-btns {
            margin-left: 24px;
          }
        }
      }
    }
    .job-name-box {
      display: flex;
      align-items: center;
      min-width: 120px;
    }
    .status-0,
    .status-1 {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      padding: 1px 6px;
      border-radius: 4px;
    }
    .status-1 {
      background: #e6f7ff;
      border: 1px solid rgba(145, 213, 255, 1);
      color: #1890ff;
    }
    .status-0 {
      background: #fafafa;
      border: 1px solid rgba(217, 217, 217, 1);
      color: #1f1f1f;
    }
  }
}
@media screen and (max-width: 1640px) {
  .history-of-statistics-info {
    .search-area {
      .ant-form {
        > .ant-row {
          > .ant-col {
            width: 50%;
          }
        }
      }
    }
  }
}
</style>