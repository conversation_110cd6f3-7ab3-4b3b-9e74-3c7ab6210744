<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="新建Review任务"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import { checkProject } from '@/api/home';
// import common from '@/utils/common';
const formParams = (ctx, _t) => {
  return {
    layout: 'vertical',
    fields: [
      {
        type: 'Select',
        label: '任务类型',
        key: 'review_type',
        props: {
          options: [
            { label: '项目review', value: 0 }
            // { label: '存储过程扫描', value: 1 }
          ]
        },
        listeners: {
          change: value => {
            ctx.$refs.form.saving({
              review_type: value,
              project_id: null,
              review_point: null,
              data_source_id: null,
              schema: null,
              procedure: null
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项' }]
      },
      // 项目review
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'review对象',
          key: 'project_id',
          props: {
            url: '/sqlreview/review/all_project',
            reqParams: {
              _t
            },
            allowSearch: true,
            backSearch: true,
            limit: 30
          },
          listeners: {
            change: value => {
              if (value) {
                ctx.check(value);
              }
              ctx.$refs.form.saving({
                project_id: value,
                review_point: null
              });
            }
          },
          rules: [{ required: true, message: '该项为必填项' }],
          visible: formData.review_type == 0
        };
      },
      (formData = {}) => {
        return {
          type: 'Select',
          label: 'review版本',
          key: 'review_point',
          props: {
            url: '/sqlreview/review/project-ls-git/',
            reqParams: {
              project_id: formData.project_id
            },
            allowSearch: true,
            backSearch: true,
            limit: 50
          },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
          visible: formData.review_type == 0
        };
      },
      // 存储过程
      // (formData = {}) => {
      //   return {
      //     type: 'Select',
      //     label: '数据源选择',
      //     key: 'data_source_id',
      //     props: {
      //       url: '/sqlreview/project/data_source_choices',
      //       reqParams: {}
      //     },
      //     listeners: {
      //       change: value => {
      //         ctx.$refs.form.saving({
      //           data_source_id: value,
      //           procedure: null
      //         });
      //       }
      //     },
      //     rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
      //     visible: formData.review_type == 1
      //   };
      // },
      // (formData = {}) => {
      //   return {
      //     type: 'Input',
      //     label: 'schema',
      //     key: 'schema',
      //     props: {},
      //     listeners: {
      //       blur: e => {
      //         ctx.$refs.form.saving({
      //           schema: e.target.value || undefined,
      //           procedure: null
      //         });
      //       }
      //     },
      //     rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
      //     visible: formData.review_type == 1
      //   };
      // },
      // (formData = {}) => {
      //   return {
      //     type: 'TreeSelect',
      //     label: '存储过程选择',
      //     key: 'procedure',
      //     props: {
      //       url: '/sqlreview/review/procedure-list/',
      //       reqParams: {
      //         data_source_id: formData.data_source_id
      //         // schema: formData.schema
      //       },
      //       treeCheckable: true,
      //       maxTagCount: 4,
      //       beforeLoaded(res) {
      //         return [
      //           {
      //             title: '存储过程',
      //             value: 'PROCEDURE',
      //             key: 'PROCEDURE',
      //             disableCheckbox: (res.PROCEDURE || []).length <= 0,
      //             children: (res.PROCEDURE || []).map(item => {
      //               return {
      //                 ...item,
      //                 title: item.label,
      //                 key: item.value
      //               };
      //             })
      //           },
      //           {
      //             title: '函数',
      //             value: 'FUNCTION',
      //             key: 'FUNCTION',
      //             disableCheckbox: (res.FUNCTION || []).length <= 0,
      //             children: (res.FUNCTION || []).map(item => {
      //               return {
      //                 ...item,
      //                 title: item.label,
      //                 key: item.value
      //               };
      //             })
      //           },
      //           {
      //             title: '包',
      //             value: 'PACKAGE',
      //             key: 'PACKAGE',
      //             disableCheckbox: (res.PACKAGE || []).length <= 0,
      //             children: (res.PACKAGE || []).map(item => {
      //               return {
      //                 ...item,
      //                 title: item.label,
      //                 key: item.value
      //               };
      //             })
      //           }
      //         ];
      //       }
      //     },
      //     rules: [{ required: true, message: '该项为必填项' }],
      //     visible: formData.review_type == 1
      //   };
      // },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: 'review方式',
          key: 'mode',
          props: {
            options: [
              {
                label: '增量',
                value: 1
              },
              {
                label: '全量',
                value: 0
              }
            ]
          },
          listeners: {
            change: value => {
              ctx.$refs.form.saving({
                mode: value
              });
            }
          },
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ]
        };
      },
      (formData = {}) => {
        return {
          type: 'RadioGroup',
          label: '历史标准基线',
          key: 'baseline_switch',
          props: {
            options: [
              {
                label: '加入',
                value: 1
              },
              {
                label: '不加入',
                value: 0
              }
            ]
          },
          visible: formData.mode == 0
        };
      }
    ]
  };
};

export default {
  components: { Form },
  props: {},
  data() {
    return {
      visible: false,
      data: {},
      params: formParams(this)
    };
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.data = {
        mode: '1'
      };
      this.params = formParams(this, +new Date());
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    check(id) {
      checkProject({ project_id: id }).then(res => {
        if (res.data.code != 0) {
          this.data.project_id = undefined;
          this.$hideLoading({ method: 'error', tips: res.data.message });
        }
      });
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      // const { data } = this;
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          this.$emit('save', form.getData());
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
