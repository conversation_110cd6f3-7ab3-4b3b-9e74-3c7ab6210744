<template>
  <div>
    <a-modal
      title="提示"
      width="640px"
      :visible="visible"
      @cancel="onCancel"
      wrapClassName="database-audit-realtime-tips-modal"
    >
      <div class="tips-body">
        <div class="message"><a-icon type="info-circle" />{{ message }}</div>
        <div class="sql-text">
          <Prettier type="sql" :style="contentStyle" :value="label"></Prettier>
        </div>
      </div>
      <div slot="footer">
        <a-button @click="onCopy" class="highlight">复制SQL</a-button>
        <a-button @click="onCancel" class="highlight">关闭</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import Prettier from '@/components/Prettier';
export default {
  name: '',
  components: { Prettier },
  props: {
    dbType: String,
    contentStyle: {
      type: Object,
      default: () => ({})
    },
    format: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      label: '',
      message: ''
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    show(label, msg) {
      this.visible = true;
      this.label = label;
      this.message = msg;
    },
    onCancel() {
      this.hide();
    },
    hide() {
      this.visible = false;
    },
    onCopy() {
      CommonUtil.copy({
        value: this.label.toString(),
        callback: () => {
          this.$message.success('复制成功');
        }
      });
    }
  }
};
</script>

<style lang="less">
.database-audit-realtime-tips-modal {
  .ant-modal-content {
    .ant-modal-close {
      .ant-modal-close-x {
        color: #000;
      }
    }
    .ant-modal-header {
      background: #fff;
      border-bottom: 1px solid #e8e8e8;
      .ant-modal-title {
        color: #000;
      }
      border: none;
    }
    .ant-modal-body {
      .tips-body {
        .message {
          .anticon {
            color: #008adc;
            margin-right: 8px;
          }
        }
        .sql-text {
          margin-top: 12px;
          // .anticon-copy {
          //   display: flex;
          //   justify-content: flex-end;
          //   font-size: 16px;
          //   color: #008adc;
          //   padding: 8px;
          // }
        }
      }
    }
    .ant-modal-footer {
      border: none;
      padding: 10px 24px;
      > div {
        .ant-btn {
          height: 32px;
          font-size: 12px;
          margin-right: 0;
          margin-left: 8px;
          border-radius: 6px;
          &.highlight {
            color: #008adc;
            border-color: #7fc4ed;
            &:hover {
              color: @primary-5;
              border-color: #008adc;
            }
          }

          &.ant-btn-primary {
            background: #008adc;
            color: #ffffff;
            &:hover {
              background: #219be3;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
</style>