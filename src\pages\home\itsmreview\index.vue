<!--
 * @Author: your name
 * @Date: 2021-01-29 17:56:15
 * @LastEditTime: 2021-02-03 14:26:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/index.vue
-->
<template>
  <div class="home-itsm-reivew">
    <div class="frame-button-wrapper">
      <a-button
        slot="extra"
        class="button"
        icon="plus"
        type="primary"
        @click="addProject"
        >新建审核</a-button
      >
    </div>
    <Table ref="table" v-bind="tableParams || {}" class="new-view-table">
      <!-- sql总数筛选 -->
      <template slot="tableTopRight">
        <div class="sql-count">
          <span class="sql-count-text">SQL总数不为0</span>
          <a-switch size="small" @change="onChange" :checked="sqlCount" />
        </div>
        <a-divider type="vertical" />
      </template>
      <!-- 列表模式 -->
      <template slot="project_name" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </template>
      <template slot="project_group" slot-scope="{ text }">
        <span class="project-group" v-if="text && text.length > 0">
          <span>{{ text[0] }}</span>
          <span v-if="text.length > 1">
            <a-tooltip>
              <template slot="title">
                <span>{{ text.toString() }}</span>
              </template>
              <span>...</span>
            </a-tooltip>
          </span>
        </span>
      </template>
      <template slot="review_point" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </template>
      <!-- DBA评审状态 -->
      <template slot="dba_status" slot-scope="{ text, record }">
        <StatusTag type="dba" :status="record.dba_status" />
      </template>
      <template slot="status" slot-scope="{ text, record }">
        <div class="status">
          <Status :status="text" :message="record.error_message"></Status>
        </div>
      </template>
      <template slot="created_by" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_creater" placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{ record.ch_creater }}</span>
          </div>
        </a-tooltip>
        <span v-else>{{ text || '--' }}</span>
      </template>
      <template slot="operator_dba" slot-scope="{ record, text }">
        <a-tooltip v-if="record.ch_dba" placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          <div class="des">
            <a-icon type="user" />
            <span>{{ record.ch_dba }}</span>
          </div>
        </a-tooltip>
        <span v-else>{{ text || '--' }}</span>
      </template>
      <custom-btns-wrapper
        slot="action"
        slot-scope="{ text, record }"
        :limit="3"
      >
        <a @click="toDetail(text, record, $event)" actionBtn>详情</a>
        <a @click="showReport(record)" actionBtn>报表</a>
        <a
          v-if="
            record.status === 1 ||
            record.status === 9 ||
            record.sql_count === 0 ||
            record.sql_count === null ||
            record.is_multi == 2
          "
          disabled
          actionBtn
          >提交评审</a
        >
        <a
          v-else-if="record.dba_status === '未提交' && record.is_multi !== 2"
          @click="authSubmit(record.id)"
          actionBtn
          >提交评审</a
        >
        <a-popconfirm
          title="重新发起审核任务?"
          @confirm="() => reReview(record)"
        >
          <a v-if="record.dba_status === '未提交'" actionBtn>重新review</a>
        </a-popconfirm>
        <a-popconfirm
          title="确定移除该数据?"
          @confirm="() => remove(record)"
          v-if="canDo && ![0, 3, 4, 5].includes(record.status)"
        >
          <a actionBtn>删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </Table>
    <!-- 新建项目弹窗 -->
    <AddModal ref="addModal" @save="saveProject"></AddModal>
    <!-- 报表抽屉 -->
    <Report ref="Report" :routeName="routeName"></Report>
    <!-- 审核弹窗 -->
    <Audit ref="audit" @refresh="refresh"></Audit>
  </div>
</template>

<script>
import { removeRecord, reviewRetry } from '@/api/home';
import { reviewByFlownumberCreate } from '@/api/extraReview/cqxly';
import LimitTags from '@/components/LimitTags';
import Table from '@/components/Table';
import StatusTag from '@/components/Biz/Status/Tag';
import Status from '@/components/Biz/Status';
import SearchArea from '@/components/SearchArea';
import AddModal from './AddModal';
import Report from '@/pages/home/<USER>/Report';
import Audit from '@/components/Biz/AuditModel';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';
export default {
  name: 'itsm-review',
  components: {
    Table,
    Status,
    SearchArea,
    AddModal,
    Report,
    Audit,
    LimitTags,
    StatusTag
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    this.config = config(this);
    return {
      record: {},
      statusColor: this.config.statusColor,
      tableParams: {
        url: '/sqlreview/review/list1',
        reqParams: {
          sql_count: '0',
          type: 3
        },
        method: 'post',
        columns: this.config.columns,
        rowKey: 'id',
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        pagination: {
          size: ''
        },
        scroll: { x: 'max-content' }
      },
      sqlCount: false,
      routeName: this.$route.name
    };
  },
  created() {},
  mounted() {},
  computed: {
    canDo() {
      const user = _.get(this.$store.state, 'account.user');
      return ['dba', 'admin'].includes(user.role);
    }
  },
  methods: {
    // 筛选sql是否为0
    onChange(data) {
      this.$set(this.tableParams, 'reqParams', {
        ...this.tableParams.reqParams,
        sql_count: data ? '1' : '0'
      });
      this.sqlCount = data;
    },
    // 新建项目
    addProject() {
      const { addModal } = this.$refs;
      addModal.show();
    },
    // 新建项目保存
    saveProject(data = {}) {
      // 请求
      this.$showLoading();
      delete data['ref_type'];
      reviewByFlownumberCreate({
        ...data,
        type: 'ITSM'
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              useMessage: true,
              tips: _.get(res, 'data.message') || '发起审核成功'
            });
            const { addModal } = this.$refs;
            addModal.hide();
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 重新review
    reReview(record) {
      this.$showLoading();
      reviewRetry({ review_id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            this.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    remove(record) {
      this.$showLoading();
      removeRecord({ id: record.id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
            const { table } = this.$refs;
            table.refreshKeep();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    toDetail(record, e) {
      this.$router.push({
        name: 'itsm-review-detail',
        params: { id: record.id }
      });
    },
    showReport(record) {
      this.record = record;
      this.$refs.Report.show(record);
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.refresh();
    },
    // 提交评审
    authSubmit(id) {
      this.$refs.audit.show(id);
    },
    refresh() {
      const { table } = this.$refs;
      table.refreshKeep();
    }
  }
};
</script>

<style lang="less" scoped>
.home-itsm-reivew {
  background: #fff;
  border-radius: 16px;
  /deep/.new-view-table {
    .search-area-wrapper {
      padding: 0 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f4f4f5;
      .custom-table-top-right {
        align-items: center;
      }
    }
    .sql-count {
      margin-right: 12px;
      display: flex;
      align-items: center;
      .sql-count-text {
        margin-right: 4px;
      }
    }
    .project-group {
      display: flex;
      > span {
        margin-right: 16px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #27272a;
        letter-spacing: 0;
        font-weight: 400;
        text-align: center;
        border: 1px solid #e4e4e7;
        border-radius: 4px;
        padding: 4px 7px;
        white-space: nowrap;
      }
    }
  }
}
</style>