<template>
  <a-drawer
    placement="right"
    :maskClosable="true"
    :closable="true"
    :visible="visible"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="import-and-export-detail-drawer"
    @close="onClose"
    width="720px"
  >
    <a-spin tip="加载中" :spinning="spinning" class="history-detail-page">
      <div :class="['text-content', `status-${status}`]">
        <custom-icon :type="statusIcon[status]" />
        <div>
          <div class="bold">
            {{
              (status == 1 ? '' : '部分') +
              (taskType == 1 ? '导入' : '导出') +
              statusText[status]
            }}
          </div>
          <div class="dark">
            <span>{{ createdBy }}</span>
            <span>{{ createdAt }}</span>
          </div>
        </div>
      </div>
      <div class="result-table">
        <div class="header-info">
          <div class="left">
            <custom-icon type="calendar" />
            <div>{{ (taskType == 1 ? '导入' : '导出') + '结果' }}</div>
          </div>
          <div :class="['right', sDbType && tDbType && 'compact']">
            <span v-if="sDbType" class="target-database">{{
              '源数据库: '
            }}</span>

            <InstanceItem
              view="new"
              mode="ellipsis"
              :tagText="sEnv"
              :src="sDbType"
              :text="sName + '(' + sDburl + ')'"
              v-if="sDbType"
            />

            <span v-if="tDbType" class="target-database">{{
              '目标数据库: '
            }}</span>

            <InstanceItem
              view="new"
              mode="ellipsis"
              :tagText="tEnv"
              :src="tDbType"
              :text="tName + '(' + tDburl + ')'"
              v-if="tDbType"
            />
          </div>
        </div>
        <Table ref="table" v-bind="tableParams || {}" :dataSource="dataSource">
          <template slot="status" slot-scope="{ text }">
            <span :class="['status-info', `status-${text}`]">{{
              statusText[text]
            }}</span>
          </template>
        </Table>
      </div>
    </a-spin>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 10
      }"
    >
      <a-button @click="onClose">关闭</a-button>
    </div>
  </a-drawer>
</template>
<script>
import config from './config';
import Table from '@/components/Table';
import InstanceItem from '@/components/Biz/InstanceItem';
import { getImportOrExportDetail } from '@/api/config/dataSource';
export default {
  name: 'import-and-export',
  components: { Table, InstanceItem },
  props: {},
  data() {
    this.config = config(this);
    return {
      spinning: false,
      visible: false,
      dataSource: [],
      tableParams: {
        url: '',
        reqParams: {},
        columns: [],
        rowKey: 'id',
        scroll: { x: 'max-content' }
      },
      createdAt: '',
      createdBy: '',
      tDbType: null,
      tEnv: null,
      tName: null,
      tDburl: null,
      sDbType: null,
      sEnv: null,
      sName: null,
      sDburl: null,
      status: 1,
      statusText: {
        1: '成功',
        2: '失败'
      },
      statusIcon: {
        1: 'lu-icon-success',
        2: 'lu-icon-error'
      },
      taskType: 1
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.taskType = data.task_type;
      let columns = [];
      if (this.taskType == 1) {
        columns = this.config.columns;
      } else if (this.taskType == 2 && data.target_type !== 'file') {
        columns = this.config.resColumns.filter(item => item.visible !== true);
      } else if (this.taskType == 2 && data.target_type == 'file') {
        columns = this.config.resColumns.filter(item => item.visible !== false);
      }
      this.$set(this.tableParams, 'columns', columns);

      this.init({ task_id: data.id });
    },
    init(params) {
      this.spinning = true;
      getImportOrExportDetail(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data') || {};
            this.dataSource = resData.results;
            this.createdAt = resData.created_at;
            this.createdBy = resData.created_by;
            this.tDbType = resData.t_db_type;
            this.tDburl = resData.t_db_url;
            this.tEnv = resData.t_env;
            this.tName = resData.t_name;
            this.status = resData.status;
            this.sDbType = resData.s_db_type;
            this.sDburl = resData.s_db_url;
            this.sEnv = resData.s_env;
            this.sName = resData.s_name;
            this.spinning = false;
          } else {
            this.spinning = false;
            this.$message.error(_.get(res, 'data.message'));
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    onClose() {
      this.visible = false;
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.history-detail-page {
  padding: 50px 8px;
  .text-content {
    display: flex;
    padding: 20px;
    align-items: center;
    background: #ecf5ee;
    &.status-2 {
      background: #fdeff1;
    }
    .anticon {
      font-size: 48px;
      color: #008adc;
      margin-right: 20px;
    }
    > div {
      .bold {
        font-size: 13px;
        color: #27272a;
        font-weight: 600;
        margin-bottom: 8px;
      }
      .dark {
        span {
          font-size: 13px;
          color: #a1a1a1;
          font-weight: 400;
        }
      }
    }
  }
  /deep/.result-table {
    margin-top: 54px;
    .header-info {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-bottom: 12px;
      > div {
        display: flex;
        align-items: center;
        &.left {
          font-size: 14px;
          color: #000000;
          .anticon-calendar {
            margin-right: 4px;
          }
        }
        &.right {
          > span {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.25);
          }
          .target-database {
            margin-left: 12px;
          }
          .biz-instance-item {
            .ant-tag {
              border: none;
            }
          }
          &.compact {
            .biz-instance-item {
              width: 200px !important;
            }
          }
        }
      }
    }
    .status-info {
      &.status-2 {
        color: #ef6173;
      }
    }
    .custom-table {
      .ant-table-content {
        .ant-table-body {
          border: 1px solid #ebebec;
          .ant-table-thead {
            > tr {
              > th {
                border-right: 1px solid #ebebec;
                &:last-child {
                  border-right: none;
                }
              }
            }
          }
          .ant-table-tbody {
            > tr {
              > td {
                border-bottom: 1px solid #ebebec;
                border-right: 1px solid #ebebec;
                &:last-child {
                  border-right: none;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
