<template>
  <div>
    <div class="header-info">
      <div class="left">
        <span class="tag">{{ sqlMap.review_point }}</span>
      </div>
      <div class="right" v-if="sqlMap.compare_review_point">
        <span class="tag">{{ sqlMap.compare_review_point }}</span>
      </div>
    </div>
    <div
      class="part-1 sql-list-item review-wraper"
      :id="`sql-item-${sqlMap.id}`"
    >
      <div class="review-wraper2">
        <!-- 有历史记录 -->
        <div v-if="sqlMap.compare_sql_text" class="redo">
          <Diff :list="diffList" />
        </div>
        <!-- 没有历史记录 -->
        <div v-else-if="sqlMap.sql_text" class="redo">
          <Diff :list="diffList" :isShowSingleWhenNoDiff="true" />
        </div>
        <div v-else class="ai-comment-part">暂无数据</div>
      </div>
      <div
        v-if="sqlMap.length === 1 && sqlMap.sql_format === 'sql'"
        class="ai-comment-part"
      >
        暂无数据
      </div>
    </div>
  </div>
</template>

<script>
import Diff from '@/components/Diff';
import CodeMirror from '@/components/CodeMirror';
import Coder from '@/components/Coder';
export default {
  components: { CodeMirror, Coder, Diff },
  props: {
    detaliData: {
      type: Object,
      default: () => {}
    },
    formatTitle: {
      type: Object,
      default: () => {}
    },
    sqlMap: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    diffList() {
      const res = { oldStr: '', newStr: '', filename: '' };

      res.newStr = this.sqlMap.compare_sql_text;
      res.oldStr = this.sqlMap.sql_text;
      res.filename = this.sqlMap.compare_sql_text
        ? `${this.formatTitle[this.detaliData.sql_format]}当前记录` +
          ' → ' +
          `${this.formatTitle[this.detaliData.sql_format]}历史记录`
        : `${this.formatTitle[this.detaliData.sql_format]}当前记录`;

      const list = [
        {
          filename: res.filename,
          oldStr: res.oldStr || '',
          newStr: res.newStr || res.oldStr,
          type: this.detaliData.sql_format,
          keywords: this.detaliData.java_sign
        }
      ];
      return list;
    }
  },
  mounted() {
    this.highlightLineFn(this.detaliData);
  },
  methods: {
    highlightLineFn(data) {
      const doc = _.get(this.$refs, 'coder.0.coder.doc');
      const signArr = (data.java_sign || []).filter(item => item);
      if (doc) {
        doc.eachLine(line => {
          if (line.text && signArr.find(item => line.text.includes(item))) {
            doc.addLineClass(line, '', 'highlight-line');
          }
        });
      }
    }
  },
  watch: {
    detaliData: {
      handler(newVal) {
        // Coder 组件是否存在取决于v-else-if="item.sql_text"
        // 这个item来自props透传过来的sql_list
        // 所以这里加 this.$nextTick 防止Coder组件没有挂载上
        this.$nextTick(() => {
          this.highlightLineFn(newVal);
        });
      }
      // immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
@import './commonClass.less';
/deep/ .highlight-line {
  background: @primary-2;
}
/deep/ .custom-coder .CodeMirror {
  background: rgb(236, 244, 254);
}
.redo {
  margin-top: -24px;
}
.part-1 {
  padding: 0 !important;
}
.header-info {
  display: flex;
  justify-content: space-between;

  .left,
  .right {
    width: 50%;
    display: flex;
    flex-direction: column;
    .tag {
      width: fit-content;
      background: #e6f4ff;
      border: 1px solid rgba(145, 202, 255, 1);
      border-radius: 4px;
      font-family: PingFangSC-Regular;
      color: #1677ff;
      padding: 6px 16px 6px 32px;
      margin-bottom: 16px;
      position: relative;
      &::before {
        display: block;
        content: '';
        width: 8px;
        height: 8px;
        position: absolute;
        top: 14px;
        left: 16px;
        border-radius: 50%;
        background: @primary-color;
      }
    }
  }
  .left {
    padding: 0 8px 0 0;
  }
  .right {
    padding: 0 0 0 8px;
    .tag {
      background: #fafafa;
      border: 1px solid rgba(217, 217, 217, 1);
      color: #1f1f1f;
      &::before {
        display: block;
        content: '';
        width: 8px;
        height: 8px;
        position: absolute;
        top: 14px;
        left: 16px;
        border-radius: 50%;
        background: #1f1f1f;
      }
    }
  }
}
</style>