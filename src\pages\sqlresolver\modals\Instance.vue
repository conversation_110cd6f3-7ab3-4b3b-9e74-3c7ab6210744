<template>
  <a-modal
    v-model="visible"
    title="请选择实例"
    okText="保存"
    :maskClosable="false"
    :width="'50%'"
    :dialogStyle="{ 'minWidth': '400px', 'maxWidth': '800px' }"
    wrapClassName="page-sqlresolver-instance-modal"
    @cancel="onCancel"
    @ok="onOk"
  >
    <custom-form ref="form" v-bind="formParams" :formData="formData"></custom-form>
  </a-modal>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    this.instanceList = [];
    return {
      visible: false,
      isEdit: false,
      formParams: {
        fields: this.getFields(),
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        colon: true
      },
      formData: {},
      data: {}
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.data = data;
      this.$set(this.formParams, 'fields', this.getFields());
      // this.isEdit = data.id != null;
    },
    hide() {
      this.visible = false;
      this.formData = {};
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      // const { data } = this;
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          this.hide();
          this.$emit(
            'save',
            this.instanceList.find(
              item => item.value == form.getData().instance
            )
          );
        }
      });
    },
    getFields() {
      return [
        {
          type: 'DataBaseChoose',
          label: '实例',
          key: 'instance',
          // width: 300,
          props: {
            url: `${GLOBAL_CONFIG.ProjectApiPrefix}/sql_tool/get_instance_list`,
            reqParams: {
              _t: +new Date()
            },
            mode: 'default',
            placeholder: '请选择实例',
            backSearch: false,
            loaded: (data = []) => {
              this.instanceList = data;
            },
            beforeLoaded(data) {
              return data.map(item => {
                return {
                  ...item,
                  db_type: item.type,
                  instance_usage: item.usage,
                  showText:
                    item.label + (item.ip ? `(${item.ip}:${item.port})` : '')
                };
              });
            }
          },
          listeners: {
            change: value => {}
          },
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ]
        }
      ];
    }
  }
};
</script>

<style lang="less">
.page-sqlresolver-instance-modal {
}
</style>