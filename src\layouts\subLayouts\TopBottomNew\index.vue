<template>
  <!-- 上下布局 -->
  <a-layout
    id="layout-root"
    :class="[mode, 'top-bottom-new ', layout, frameClassName]"
  >
    <!-- 头部 -->
    <a-layout-header class="header">
      <Logo :logo="logo" v-if="!collapsed" />
      <div class="logo" v-else>
        <img src="@/assets/img/private/blue.svg" alt />
      </div>
      <!-- 顶部右侧tools -->
      <Tools v-if="!mode.startsWith('frame-inset')" @reload="reload" />
    </a-layout-header>
    <!-- 下部 -->
    <a-layout :class="`layout-part-right ${collapsed ? 'menu-collapsed' : ''}`">
      <!-- 左侧菜单 -->
      <a-layout-sider
        :class="collapsed ? 'menu-collapsed' : ''"
        :width="mode.startsWith('frame-inset') ? 130 : 178"
        v-model="collapsed"
      >
        <div
          class="menu-group-item"
          v-for="item in menuGroup"
          :key="item.key"
          v-show="item.children.length > 0"
        >
          <div>{{ item.title }}</div>
          <MenuView
            menuMode="vertical"
            :collapsed="collapsed"
            :menusConfig="item.children"
          ></MenuView>
        </div>
        <Collapse :collapsed="collapsed" @onCollapse="onCollapse" />
      </a-layout-sider>
      <!-- 内容 -->
      <a-layout
        id="rootContent"
        :class="`content ${navi.length <= 0 && 'no-navi'}`"
      >
        <div class="navi-wrapper" v-if="navi.length > 0 && !ignorePageTitle">
          <!-- router信息 -->
          <a-breadcrumb
            class="router-info"
            separator=">"
            v-if="navi.length >= 2 || ignorePageTitle"
          >
            <a-breadcrumb-item v-for="(item, index) in navi" :key="item.path">
              <custom-icon :type="item.icon || 'home'" v-if="index === 0" />
              <router-link
                :to="item.path"
                v-if="item.path !== 'null' && index < navi.length - 1"
                >{{ item.name }}</router-link
              >
              <span style="margin-left: 0" v-else>{{ item.name }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
          <!-- 标题 -->
          <div class="page-title" v-if="!ignorePageTitle && navi.length < 2">
            {{ pageTitle }}
          </div>
        </div>

        <a-layout-content :class="contentType">
          <keep-alive :include="metaNavis">
            <router-view
              v-if="
                isRouterAlive &&
                routerCache === true &&
                $route.meta.keepAlive === true
              "
            ></router-view>
          </keep-alive>
          <router-view
            ref="routerView"
            v-if="
              isRouterAlive &&
              !(routerCache === true && $route.meta.keepAlive === true)
            "
          ></router-view>
        </a-layout-content>

        <!-- footer -->
        <div class="footer">
          <div>
            版权所有 ©{{ currYear }} 陆金所控股有限公司 Copyright Shanghai
            Lujiazui International Financial Awwet Exchange Co., Ltd. ALL Rights
            Reserved
          </div>
        </div>
      </a-layout>
      <a-back-top style="right: 50px" :target="getScrollTarget" />
    </a-layout>
  </a-layout>
</template>
<script>
import menuConfig from '../../MenuView/config';
import MenuView from '../../MenuView/index';
import Tools from '../../private/Tools/index';
import Logo from '../../private/Logo/index';
import Collapse from '../../private/Collapse.vue';
import { getYear } from 'date-fns';
import Config from '@/utils/config';
import '../../private/index';
import privateConfig from '../../private/config.js';
import mixins from '../mixins';
export default {
  components: { MenuView, Tools, Logo, Collapse },
  props: {
    mode: String,
    layout: String,
    logo: String
  },
  mixins: [mixins],
  data() {
    const menuGroup = privateConfig.menuGroup(menuConfig(this));
    return {
      collapsed: false,
      currYear: getYear(new Date()),
      contentType: '',
      // mode: mode ? 'frame-' + mode : '',
      // layout,
      projectName: Config.ProjectName,
      isRouterAlive: true,
      routerCache: Config.routerCache,
      // logo,
      announce: Config.announce,
      menuGroup
    };
  },
  computed: {
    pageTitle() {
      return _.get(this.$route, 'meta.desc');
    },
    frameType() {
      return _.get(this.$route, 'meta.frameType');
    },
    frameClassName() {
      const frameType = this.frameType;
      let res = '';
      switch (frameType) {
        case 'ignorePageTopSpace':
          res = 'ignore-page-top-space';
          break;
        case 'fullsize':
          // res = 'show-header-menu-page';
          res = 'show-header-and-page';
          break;
        case 'pageNarrowMargins':
          res = 'page-narrow-margins';
          break;
        default:
          res = frameType;
          break;
      }
      return res;
    },
    ignorePageTitle() {
      return ['ignorePageTopSpace', 'fullsize', 'pageNarrowMargins'].includes(
        this.frameType
      );
    }
  },
  created() {
    // if (window && window.innerWidth < 1440) {
    //   this.onCollapse();
    // }
  },
  mounted() {},
  destroyed() {},
  methods: {
    onCollapse() {
      this.collapsed = !this.collapsed;
    }
  },
  watch: {}
};
</script>
<style lang="less" scoped>
@import '../base.less';
@assetsUrl: '~@/assets';
// 上下布局
#layout-root.top-bottom-new {
  .ant-layout-header {
    z-index: 12;
    background: #003b72 !important;
    box-shadow: 0px 2px 4px 0px rgba(200, 200, 200, 0.5);
    display: flex;
    justify-content: space-between;
    padding: 0;
    position: fixed;
    height: 48px;
    line-height: 48px;

    /deep/ .header-logo {
      color: white;
      display: flex;
      align-items: center;
      padding-left: 16px;

      > img {
        width: auto;
        height: 18px;
      }
      > span {
        font-size: 16px;
        position: relative;
        margin-left: 12px;
      }
    }

    /deep/ .header-tools {
      color: rgba(255, 255, 255, 0.65);
      position: relative;

      // > div {
      //   border-color: #3a3a3a;

      //   &:hover {
      //     background: #0360b7;

      //   }
      // }
    }
    .logo {
      > img {
        width: 80px !important;
        height: 20px;
        margin: 8px 0 4px 0;
      }
    }
  }

  .ant-layout-sider {
    padding: 0 0 72px 0;
    left: 0;
    top: 48px;
    bottom: 0;
    height: auto;
    border-radius: 0;
    // background: #003b72;
    background: #fff;
    z-index: 12;
    box-shadow: 0px 4px 8px 0px rgba(200, 200, 200, 0.5);
    .menu-group-item {
      > div {
        font-size: 12px;
        padding: 0 16px;
        // color: #fff;
        color: #008adc;
        background: transparent;
      }
      /deep/.ant-menu {
        background: transparent;
        > .ant-menu-item {
          // color: rgba(255, 255, 255, 0.5);
          color: #1f1f1f;
          .anticon {
            margin-right: 6px;
          }
          &:hover {
            color: #fff;
          }
        }
      }
      &:first-child {
        > div {
          margin-top: 16px;
        }
      }
    }

    /deep/ .custom-menu {
      padding: 8px 0;

      .ant-menu-item,
      .ant-menu-submenu-title {
        margin: 0;
        transition: none;
      }

      .ant-menu-item-active {
        > i,
        > span {
          // color: #fff !important;
          color: #25a7e8 !important;
        }
      }

      .ant-menu-item-selected {
        // background: #4096ff !important;
        background: #e6faff !important;
        font-weight: 500;

        > i,
        > span {
          // color: #fff !important;
          color: #1677ff !important;
        }
        &::after {
          display: none;
        }
      }

      .ant-menu-sub {
        background: #f4f5f7 !important;
      }

      .ant-menu-submenu-active {
        .ant-menu-submenu-title {
          > span {
            > .anticon {
              color: #71717a !important;
            }
            > span {
              color: #71717a !important;
            }
          }
          .ant-menu-submenu-arrow {
            &::before,
            &::after {
              background: #71717a !important;
            }
          }
        }
      }

      .ant-menu-submenu-selected {
        .ant-menu-submenu-title {
          background: #f4f5f7 !important;
          font-weight: 500;
          > span {
            > .anticon {
              color: #27272a !important;
            }
            > span {
              color: #27272a !important;
              opacity: 1;
            }
          }
        }
      }
    }

    /deep/ .ant-layout-sider-trigger {
      background: #ffffff;
      border-top: 1px solid #f0f2f5;
      i {
        color: #27272a;
      }
    }
  }

  .layout-sider-mask {
    position: fixed;
    left: 0;
    width: 218px;
    top: 64px;
    bottom: 0;
    background: #f9f9f9;
    z-index: 10;
  }

  .layout-part-right {
    padding: 48px 0 0 178px;
    .ant-layout.content {
      padding: 60px 24px 24px 24px;
      .navi-wrapper {
        position: absolute;
        left: 28px;
        height: 60px;
        top: 0;
        /deep/ .router-info {
          opacity: 1;
          > span:not(:last-child) {
            color: rgba(0, 0, 0, 0.25);
          }
          a {
            color: fade(@primary-color, 65);
            &:hover {
              color: @primary-color;
            }
          }
          .ant-breadcrumb-separator {
            margin: 0 4px 0 6px;
          }
        }
        .page-title {
          font-size: 24px;
          font-weight: 500;
          color: #000000;
        }
      }

      .icon-refresh {
        .anticon {
          margin-right: 4px;
        }
      }
    }

    .footer {
      height: auto;
      line-height: inherit;
      color: rgba(0, 0, 0, 0.4);
      margin-top: 24px;
      font-size: 12px;
    }

    &.menu-collapsed {
      padding: 48px 0 0 80px;

      .layout-sider-mask {
        width: 128px;
      }

      .ant-layout-sider {
        .menu-group-item {
          > div {
            padding: 16px 0 0 0;
            text-align: center;
          }
        }

        /deep/ .custom-menu {
          .ant-menu-submenu-selected {
            .ant-menu-submenu-title {
              > span {
                > .anticon {
                }
                > span {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }

  // 忽略page上面高间距
  &.ignore-page-top-space {
    .ant-layout.layout-part-right {
      .ant-layout.content {
        padding-top: 42px;
      }
    }
  }

  // 页面窄边距
  &.page-narrow-margins {
    .ant-layout.layout-part-right {
      .ant-layout.content {
        padding: 16px 24px;
      }
    }
  }

  // 内嵌模式（带菜单）
  &.frame-inset-with-menu {
    min-width: 0;

    .layout-sider-mask {
      top: 0;
      width: 154px;
    }
    .layout-part-right {
      padding: 0 0 0 130px;

      &.menu-collapsed {
        padding: 0 0 0 80px;

        .layout-sider-mask {
          width: 104px;
        }
      }
    }

    .ant-layout-header {
      display: none;
    }

    .ant-layout-sider {
      top: 0;
      left: 0;
      bottom: 0;
    }
  }

  // 只显示顶部 + 内容
  &.show-header-and-page {
    .ant-layout-header {
      display: flex;
      /deep/ .custom-menu {
        line-height: 64px;
        background: transparent;
        color: #fff;
        border-bottom: 0;
        margin-left: 32px;
        display: flex;
        &::after {
          display: none;
        }
        .column-layout() {
          display: flex;
          flex-direction: column;
          line-height: 24px;
          justify-content: center;
          align-items: center;
          font-weight: 500;
        }
        > .ant-menu-item {
          border-bottom: 0;
          padding: 0 16px;
          top: 0;
          .column-layout();
          > .anticon {
            color: #fff !important;
            margin-right: 0;
            font-size: 20px;
            transform: translateY(10px);
            transition-property: all;
            transition-timing-function: linear;
          }
          > span {
            color: #fff !important;
            margin-top: 2px;
            font-size: 13px;
            opacity: 0;
            transition-duration: 0.15s;
          }
          &.ant-menu-item-selected,
          &.ant-menu-item-active {
            background: fade(@primary-7, 60%);
            > .anticon {
              transform: translateY(0px) scale(0.8);
            }
            > span {
              opacity: 1;
            }
          }
        }
        > .ant-menu-submenu {
          border-bottom: 0;
          top: 0;
          display: flex;
          justify-content: center;
          > .ant-menu-submenu-title {
            display: flex;
            > span {
              .column-layout();
            }
          }
          .ant-menu-submenu-title {
            padding: 0 16px;
            background: transparent !important;
            > span {
              > .anticon {
                color: #fff !important;
                margin-right: 0;
                font-size: 20px;
                transform: translateY(10px);
                transition-property: all;
                transition-timing-function: linear;
                transition-duration: 0.1s;
              }
              > span {
                color: #fff !important;
                margin-top: 2px;
                font-size: 13px;
                opacity: 0;
                transition-duration: 0.2s;
              }
            }
          }
          &.ant-menu-submenu-selected,
          &.ant-menu-submenu-active {
            background: fade(@primary-7, 60%);
            .ant-menu-submenu-title {
              > span {
                > .anticon {
                  transform: translateY(0px) scale(0.8);
                }
                > span {
                  opacity: 1;
                }
              }
            }
          }
        }
      }
    }
    .ant-layout-sider {
      display: none !important;
    }
    .layout-sider-mask {
      display: none !important;
    }
    .layout-part-right {
      padding-left: 0;
      #rootContent {
        padding: 0;
      }
      .navi-wrapper {
        display: none !important;
      }
    }
    .footer {
      display: none;
    }

    &.frame-inset {
      .ant-layout-header {
        display: none;
      }
    }
    &.frame-inset-with-menu {
      .ant-layout-header {
        display: none;
      }
      .ant-layout-sider {
        display: block !important;
      }
      .layout-sider-mask {
        display: block !important;
      }
      .layout-part-right {
        padding-left: 154px;
        &.menu-collapsed {
          padding-left: 104px;
        }
      }
    }
  }

  // 显示顶部+菜单+页面
  &.show-header-menu-page {
    .ant-layout-sider {
      left: 0;
      top: 60px;
      bottom: 0;
    }
    .layout-sider-mask {
      display: none !important;
    }
    .layout-part-right {
      padding-left: 170px;
      #rootContent {
        padding: 0 0 0 8px;
      }
      .navi-wrapper {
        display: none !important;
      }

      &.menu-collapsed {
        padding-left: 80px;
      }
    }
    .footer {
      display: none;
    }
  }
}
</style>
<style lang="less">
// 菜单折叠浮层
.ant-menu-dark.ant-menu-submenu-popup.layout-menu-popup-submenu {
  background: linear-gradient(180deg, #003b72 51.04%, #2e76ab 100%) !important;
  transition: none !important;
  .ant-menu-sub {
    background: transparent;
  }
  .ant-menu-item-selected {
    background-color: #3ca3f2 !important;
  }
}

#layout-root.left-right-new {
  // 外框间距变化，关联变化
  @layout-padding-top: 70px;
  @layout-padding: 40px;
  @btn-height: 36px;
  // .frame-button-wrapper,
  // .frame-button-wrapper-relative,
  // .frame-button-wrapper-relative-blank {
  //   display: flex;
  //   > .ant-btn {
  //     height: @btn-height;
  //     font-size: 14px;
  //     font-weight: 600;
  //     margin-right: 0;
  //     margin-left: 8px;
  //   }
  // }
  // .frame-button-wrapper {
  //   position: absolute;
  //   right: @layout-padding;
  //   top: 20px;
  //   z-index: 10;
  //   background: transparent;
  // }
  // .frame-button-wrapper-relative {
  //   position: absolute;
  //   right: -24px;
  //   top: -(@btn-height + 14px) - 24px;
  //   z-index: 10;
  //   background: transparent;
  // }
  // .frame-button-wrapper-relative-blank {
  //   position: absolute;
  //   right: 0px;
  //   top: -(@btn-height + 14px);
  //   z-index: 10;
  //   background: transparent;
  // }

  // // 默认样式覆盖
  // button.ant-btn {
  //   border-radius: 8px;
  // }

  // 分页新ui
  .ant-table-pagination {
    .hover-status {
      box-shadow: inset 0px 0px 0px 1px #25a7e8;
    }
    // 条数
    .ant-pagination-total-text {
      color: #a1a1aa;
    }
    // 跳至
    .ant-pagination-options-quick-jumper {
      display: none;
    }
    // pagesize
    .ant-pagination-options-size-changer {
      margin-right: 0;
    }
    // 边框
    > li {
      &.ant-pagination-item,
      &.ant-pagination-jump-prev,
      &.ant-pagination-jump-next {
        border: 1px solid #e4e4e7;
        border-right: none;
        border-radius: 0;
        margin-right: 0;
      }
    }
    // 正常页
    .ant-pagination-item {
      &:hover {
        .hover-status;
      }
      // 选中页
      &.ant-pagination-item-active {
        background: #008adc;
        border: none;
        > a {
          font-size: 14px;
          color: #ffffff;
          font-weight: 400;
        }
      }
    }
    // 向前五页, 向后五页
    .ant-pagination-jump-prev,
    .ant-pagination-jump-next {
      &:hover {
        .hover-status;
      }
    }
    // 上一页, 下一页
    .ant-pagination-prev,
    .ant-pagination-next {
      &:not(.ant-pagination-disabled) {
        > .ant-pagination-item-link {
          &:hover {
            .hover-status;
            border-color: #d9d9d9;
          }
        }
      }
    }
    .ant-pagination-prev {
      margin-right: 0;
      > .ant-pagination-item-link {
        border-radius: 4px 0 0 4px;
        border-right: none;
      }
    }
    .ant-pagination-next {
      margin-right: 0;
      > .ant-pagination-item-link {
        border-radius: 0 4px 4px 0;
      }
    }

    // size = default
    &:not(.mini) {
      @size: 38px;
      > li:not(.ant-pagination-total-text):not(.ant-pagination-options) {
        min-width: @size;
        height: @size;
        line-height: @size;
      }
      // 条数
      .ant-pagination-total-text {
        font-size: 16px;
        font-weight: 400;
        margin-right: 16px;
      }

      // 跳转内容
      .ant-pagination-options {
        .ant-pagination-options-size-changer {
          height: @size;
          .ant-select-selection--single {
            height: @size;
            .ant-select-selection__rendered,
            .ant-select-arrow {
              line-height: @size;
            }
          }
        }
      }
    }

    // size = small
    &.mini {
      @size: 32px;
      > li {
        &.ant-pagination-item,
        &.ant-pagination-jump-prev,
        &.ant-pagination-jump-next {
          border-color: #e4e4e7 !important;
        }
      }
      > li:not(.ant-pagination-total-text):not(.ant-pagination-options) {
        min-width: @size;
        height: @size;
        line-height: @size;
      }
      // 条数
      .ant-pagination-total-text {
        font-weight: 400;
        margin-right: 12px;
      }

      // 上一页, 下一页
      .ant-pagination-prev,
      .ant-pagination-next {
        > .ant-pagination-item-link {
          border-color: #d9d9d9;
        }
      }

      // 跳转内容
      .ant-pagination-options {
        margin-left: 12px;
        .ant-pagination-options-size-changer {
          height: @size;
          .ant-select-selection--single {
            height: @size;
            .ant-select-selection__rendered,
            .ant-select-arrow {
              line-height: @size;
            }
          }
        }
      }
    }
  }

  .custom-table {
    div.custom-table-tools {
      > .anticon {
        color: #27272a;
        &:hover {
          color: #fff;
          background: @primary-4;
        }
      }
    }
    .ant-table,
    .ant-table-fixed {
      .ant-table-thead {
        > tr {
          > th {
            background: #fff;
            font-family: PingFangSC-Regular;
            color: #a1a1aa !important;
            font-weight: 400;
            border-color: #f4f4f5;
            border-top: 1px solid #f4f5f7;
          }
        }
      }
      .ant-table-tbody > tr {
        background: #fff !important;
        > td {
          border-bottom: 1px solid #f4f5f7;
        }
        &.ant-table-row-hover {
          > td {
            background: #fbfcff;
          }
        }
        &:hover {
          > td {
            background: #fbfcff;
          }
        }
      }
    }
  }
  // 新table模式
  // .new-card-table {
  // }
}
@import './newViewTable.less';
@import './frameButtonWrapper.less';
</style>
