<template>
  <div class="biz-private-rules">
    <Rules :ruleData="group"></Rules>
    <EditModal ref="EditModal" @save="onConditionSave" />
  </div>
</template>

<script>
import Rules from '@/components/Biz/RuleComps/Rules/index';
import EditModal from './EditModal';
import config from './config';

const group = {
  role_type: 'group',
  item_order: 1,
  relation: 'OR',
  children: [
    {
      role_type: 'group',
      item_order: 3,
      relation: 'AND',
      children: [
        {
          role_type: 'item',
          item_order: 1,
          index_code: 'code3',
          target_operator: 'gt',
          target_value: '123'
        },
        {
          role_type: 'group',
          item_order: 3,
          relation: 'OR',
          children: [
            {
              role_type: 'item',
              item_order: 1,
              index_code: 'code4',
              target_operator: 'gt',
              target_value: '123'
            },
            {
              role_type: 'group',
              item_order: 3,
              relation: 'OR',
              children: [
                {
                  role_type: 'item',
                  item_order: 1,
                  index_code: 'code5',
                  target_operator: 'gt',
                  target_value: '123',
                  children: [
                    {
                      role_type: 'group',
                      item_order: 3,
                      relation: 'OR',
                      children: [
                        {
                          role_type: 'item',
                          item_order: 2,
                          index_code: 'code7',
                          target_operator: 'gt',
                          target_value: '456'
                        },
                        {
                          role_type: 'group',
                          item_order: 3,
                          relation: 'OR',
                          children: [
                            {
                              role_type: 'item',
                              item_order: 2,
                              index_code: 'code8',
                              target_operator: 'gt',
                              target_value: '456'
                            },
                            {
                              role_type: 'item',
                              item_order: 2,
                              index_code: 'code9',
                              target_operator: 'gt',
                              target_value: '456'
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
                {
                  role_type: 'item',
                  item_order: 2,
                  index_code: 'code6',
                  target_operator: 'gt',
                  target_value: '369'
                }
              ]
            }
          ]
        }
      ]
    },
    {
      role_type: 'item',
      item_order: 1,
      index_code: 'code1',
      target_operator: 'gt',
      target_value: '123'
    },
    {
      role_type: 'item',
      item_order: 2,
      index_code: 'code2',
      target_operator: 'gt',
      target_value: '456'
    }
  ]
};
export default {
  components: { Rules, EditModal },
  props: {},
  data() {
    this.config = config(this);
    return {
      group
    };
  },
  created() {},
  mounted() {
    const { EditModal } = this.$refs;
    this.$bus.$on('input-modal', data => {
      EditModal.show(this.type, data);
    });
  },
  destroyed() {
    this.$bus.$off('input-modal');
  },
  methods: {
    onConditionSave(e) {
      // EditModal.hide();
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
.biz-private-rules {
  padding: 8px 0;
}
</style>