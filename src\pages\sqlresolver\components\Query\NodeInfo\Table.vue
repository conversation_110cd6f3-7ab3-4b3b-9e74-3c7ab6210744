<template>
  <a-spin class="psc-right-query-node-info-table" :spinning="loading">
    <Table ref="table" v-bind="tableParams" :columns="columns" :dataSource="dataSource" bordered>
      <template v-for="sItem in scopedSlots" v-slot:[sItem]="{text, record, index, column}">
        <slot :name="sItem" v-bind="{text, record, index, column}"></slot>
      </template>
    </Table>
  </a-spin>
</template>

<script>
import Table from '@/components/Table';
// import config from './config';

export default {
  components: { Table },
  // inject: ['instanceItem'],
  props: {
    columns: Array,
    reqInstance: Function,
    reqParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    // this.config = config(this);
    return {
      tableParams: {
        size: 'small'
      },
      dataSource: [],
      loading: false
    };
  },
  computed: {
    scopedSlots() {
      return Object.keys(this.$scopedSlots);
    }
  },
  created() {
    this.getData();
  },
  mounted() {},
  methods: {
    getData() {
      const { reqInstance, reqParams = {} } = this;

      this.loading = true;
      reqInstance(reqParams)
        .then(res => {
          this.loading = false;
          if (CommonUtil.isSuccessCode(res)) {
            const data = _.get(res, 'data.data');
            this.dataSource = data;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    refresh() {
      this.getData();
    }
  }
};
</script>

<style lang="less" scoped>
.psc-right-query-node-info-table {
  /deep/ .ant-table-small {
    border: 1px solid #e8e8e8;
    .ant-table-placeholder {
      border: none !important;
    }
    .ant-table-content {
      border: none !important;
    }
  }
}
</style>
