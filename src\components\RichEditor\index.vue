<template>
  <div :class="['custom-rich-editor', disabled && 'disabled']">
    <div class="custom-rich-editor-container"></div>
  </div>
</template>

<script>
import E from 'wangeditor';
import hljs from 'highlight.js';

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: String,
    options: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.defaultOptions = {};
    return {};
  },
  created() {},
  mounted() {
    this.init();
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
    }
  },
  methods: {
    init() {
      const editor = new E(
        this.$el.querySelector('.custom-rich-editor-container')
      );
      console.log(editor, this.options, 666);
      // 属性挂载
      Object.assign(
        editor.config,
        {
          excludeMenus: ['emoticon', 'video', 'fontName', 'todo'],
          zIndex: 500,
          focus: false
        },
        this.options
      );
      // 挂载highlight插件
      editor.highlight = hljs;
      // 配置 onchange 回调函数
      editor.config.onchange = newHtml => {
        // console.log('change 之后最新的 html', newHtml);
        this.$emit('change', newHtml);
      };
      editor.create();
      // 初始值
      this.value && editor.txt.html(this.value);
      // 是否禁用
      this.disabled && editor.disable();
      this.editor = editor;
    },
    getData() {
      let res = '';
      if (this.editor) {
        res = this.editor.txt.html();
      }
      return res;
    }
  },
  watch: {
    disabled(newVal) {
      if (this.editor) {
        this.editor[newVal ? 'disable' : 'enable']();
      }
    },
    value(newVal, oldVal) {
      // console.log('newVal: ', newVal);
      // console.log('oldVal: ', oldVal);
      if (this.editor && !this.editor.isFocus && newVal !== oldVal) {
        this.editor.txt.html(newVal || '');
      }
    }
  }
};
</script>
<style lang="less" scoped>
.custom-rich-editor {
  line-height: 24px;
  /deep/ .custom-rich-editor-container {
    .w-e-toolbar .w-e-menu {
      height: 32px;
    }
  }

  &.disabled {
    opacity: 0.5;
  }
}
</style>