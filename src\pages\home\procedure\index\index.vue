<template>
  <div class="procedure page-list-single">
    <!-- <a-card style="100%" class="common-page-card" title="存储过程游标扫描列表" :bordered="false"> -->
    <div class="frame-button-wrapper">
      <a-button slot="extra" class="highlight" icon="plus" @click="addProject">存储过程扫描</a-button>
    </div>
    <!-- <SearchArea v-bind="searchParams" :needCache="true" @reset="reset" @search="search"></SearchArea> -->
    <Table ref="table" v-bind="tableParams || {}">
      <!-- table插槽 -->
      <template slot="review_count" slot-scope="{ text, record }">
        <a @click="toDetail(text, record, $event)">{{text}}</a>
      </template>
      <template slot="status" slot-scope="{ text, record }">
        <Status :status="text" :message="record.error_message" from="procedure"></Status>
      </template>
      <span slot="action" slot-scope="{ text, record }">
        <a @click="toDetail(text, record, $event)">详情</a>
      </span>
    </Table>
    <EditModal ref="editModal" @save="onSave"></EditModal>
    <!-- </a-card> -->
  </div>
</template>

<script>
import { reviewProcedure } from '@/api/procedure';
import Table from '@/components/Table';
import Status from '@/components/Biz/Status';
import SearchArea from '@/components/SearchArea';
import EditModal from './components/editModal';
import config from './config';
export default {
  name: 'procedure',
  components: {
    Table,
    Status,
    SearchArea,
    EditModal
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: '/sqlreview/review/review-procedure/',
        columns: this.config.columns,
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 1000 }
      },
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 3
      }
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    addProject() {
      const { editModal } = this.$refs;
      editModal.show();
    },
    onSave(data) {
      const { editModal, table } = this.$refs;
      console.log(JSON.stringify(data), '898989');

      // 请求
      this.$showLoading();
      reviewProcedure({
        ...data
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            editModal.hide();
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    toDetail(text, record, $event) {
      this.$router.push({
        name: 'home-procedure-detail',
        params: { id: record.id }
      });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      table.refresh(null, data);
    },
    reset() {
      const { table } = this.$refs;
      table.refresh();
    }
  }
};
</script>

<style scoped lang="less">
</style>
