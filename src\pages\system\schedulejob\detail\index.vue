<template>
  <div class="order-detail-container">
    <JobDetail ref="jobDetail" v-if="activeKey == 'job'">
    </JobDetail>
    <AgentDetail ref="agentDetail" v-else>
    </AgentDetail>
  </div>
</template>

<script>
import bodyMinWidth from '@/mixins/bodyMinWidth';
import JobDetail from './JobDetail';
import AgentDetail from './AgentDetail';

export default {
  name: 'schedule-job-detail',
  components: {
    JobDetail,
    AgentDetail
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  computed: {},
  data() {
    return {
      activeKey: 'job'
    };
  },
  mounted() {},
  created() {},
  methods: {},
  watch: {
    '$route.query.activeKey': {
      handler(newVal) {
        if (newVal) {
          this.activeKey = newVal;
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less">
</style>
