export default function (ctx) {
  const lineOption = (color1, color2, data = []) => {
    return {
      color: [color2],
      grid: {
        top: 0,
        bottom: 0,
        left: '5%',
        right: 0,
        containLabel: false
      },
      xAxis: {
        show: false,
        type: 'category',
        boundaryGap: false,
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        show: false,
        type: 'value'
      },
      series: [
        {
          data: [...data],
          type: 'line',
          smooth: true,
          showSymbol: false,
          areaStyle: {},
          lineStyle: {
            color: color1,
            width: 2
          }
        }
      ]
    };
  };

  const monitorLineOption = (data = []) => {
    return {
      color: ['rgb(248,239,254)'],
      grid: {
        top: 0,
        bottom: 0,
        left: '5%',
        right: 0,
        containLabel: false
      },
      xAxis: {
        show: false,
        type: 'category'
      },
      yAxis: {
        show: false,
        type: 'value'
      },
      series: [
        {
          data: data.map(item => item.value),
          type: 'line',
          smooth: true,
          showSymbol: false,
          areaStyle: {},
          lineStyle: {
            color: 'rgb(239,219,254)',
            width: 2
          }
        }
      ]
    };
  };

  const monitorBarOption = (data = []) => {
    return {
      color: ['rgb(207,237,254)'],
      grid: {
        top: 0,
        bottom: 0,
        left: '5%',
        right: 0,
        containLabel: false
      },
      xAxis: {
        type: 'category',
        show: false,
        data: data.map(item => item.value)
      },
      yAxis: {
        type: 'value',
        axisLine: 'none',
        axisTick: 'none',
        show: false
      },
      series: [
        {
          data: data.map(item => item.value),
          type: 'bar'
        }
      ]
    };
  };
  const popoverOption = (title, data = [], timeArr = []) => {
    return {
      title: [
        {
          text: title,
          textStyle: {
            color: '#27272A',
            fontSize: '14px',
            fontWeight: 600
          }
        }
      ],
      grid: {
        // top: '5%',
        bottom: '2%',
        left: '5%',
        right: '4%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: timeArr,
        axisLabel: {
          rotate: timeArr.length > 6 ? 30 : 0,
          color: 'rgba(0,0,0,0.65)',
          fontSize: 10
        }
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false
        },
        axisLabel: {
          color: 'rgba(0,0,0,0.65)',
          fontSize: 10
        }
      },
      series: [
        {
          data: [...data],
          type: 'line',
          smooth: true,
          showSymbol: false,
          lineStyle: {
            color: '#52c41a',
            width: 2
          }
        }
      ]
    };
  };
  const barOption = (data = []) => {
    return {
      color: ['#3E60C1'],
      grid: {
        top: '4%',
        bottom: '4%',
        left: '-18px',
        right: '4%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        show: false
      },
      yAxis: [
        {
          type: 'category',
          inverse: true,
          axisLine: 'none',
          axisTick: 'none',
          show: true,
          axisLabel: {
            margin: 20,
            color: '#7F7F7F',
            fontSize: '12',
            formatter: (value) => ''
          },
          data: data.map(item => item.value)
        }
      ],
      series: [
        {
          data: data.map((item, index) => {
            return {
              value: item.value,
              itemStyle: {
                ...item.itemStyle,
                shadowBlur: 1,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                borderRadius: 12
              }
            };
          }),
          type: 'bar',
          barMaxWidth: '30%',
          barWidth: 6,
          showBackground: true,
          backgroundStyle: {
            color: '#F5F5F5'
          }
        }
      ]
    };
  };
  const pieOption = (data = [], rate = 0) => {
    return {
      // title: {
      //   text: '高风险',
      //   textStyle: {
      //     color: '#b1b1b1',
      //     fontSize: 14,
      //     fontWeight: 400
      //   },
      //   subtext: rate,
      //   subtextStyle: {
      //     color: '#000000',
      //     fontSize: 24,
      //     fontWeight: 600
      //   },
      //   left: '12%',
      //   top: '40%'
      // },
      color: ['rgb(250, 173, 20)', 'rgb(82, 196, 26)', 'rgb(255, 120, 117)'],
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        top: 28,
        right: 0,
        itemHeight: 12,
        selectedMode: false, // 取消图列上的点击事件
        textStyle: {
          fontSize: '12px',
          color: '#1F1F1F'
        },
        formatter: function (name) {
          let rate = 0
          data.forEach(item => {
            if (item.name == name) {
              rate = item.rate
            }
          })
          return ' ' + ' ' + name + ' ' + ' ' + ' ' + rate
        },
        data: data
      },
      series: [
        {
          name: '风险类型',
          type: 'pie',
          radius: ['76%', '86%'],
          center: ['24%', '50%'],
          label: {
            show: false,
            positon: 'center'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0,0,0,0.5)'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        },
        {
          name: '中间文字',
          z: 100,
          type: 'gauge',
          radius: '-50%',
          center: ['24%', '50%'], // 需和type: 'pie'中的center一致
          // 配置中间的数字的样式
          detail: {
            // 调节数字位置
            offsetCenter: [-1, 10],
            color: '#000000',
            fontSize: 22,
            fontWeight: 600,
            formatter: function (value) {
              return value + '%'
            }
          },
          pointer: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          // 中间的字和数字 数据
          data: [
            {
              value: rate,
              name: '高风险',
              title: {
                show: true,
                color: '#b1b1b1',
                fontSize: 14,
                fontWeight: 400,
                offsetCenter: [0, '50%']
              }
            }
          ]
        }
      ]
    };
  };
  const codeReviewOption = (data = [], count = 0) => {
    return {
      color: ['rgb(250, 173, 20)', 'rgb(82, 196, 26)', 'rgb(255, 120, 117)', 'rgb(178,126,234)'], // 高，低，无，异
      tooltip: {
        trigger: 'item'
      },
      legend: {
        // orient: 'vertical',
        top: 'bottom',
        // left: 'right',
        itemHeight: 12,
        selectedMode: false, // 取消图列上的点击事件
        textStyle: {
          fontSize: '12px',
          color: '#1F1F1F'
        },
        formatter: function (name) {
          let rate = 0
          data.forEach(item => {
            if (item.name == name) {
              rate = item.rate
            }
          })
          return ' ' + ' ' + name + ' ' + ' ' + ' ' + rate
        },
        data: data
      },
      series: [
        {
          name: '风险类型',
          type: 'pie',
          radius: ['66%', '76%'],
          center: ['50%', '36%'],
          label: {
            show: false,
            positon: 'center'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0,0,0,0.5)'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        },
        {
          name: '中间文字',
          z: 100,
          type: 'gauge',
          radius: '-50%',
          center: ['50%', '36%'], // 需和type: 'pie'中的center一致
          // 配置中间的数字的样式
          detail: {
            // 调节数字位置
            offsetCenter: [0, 10],
            color: '#000000',
            fontSize: 24,
            fontWeight: 600,
            formatter: function (value) {
              return value
            }
          },
          pointer: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          // 中间的字和数字 数据
          data: [
            {
              value: count,
              name: 'SQL总数',
              title: {
                show: true,
                color: '#b1b1b1',
                fontSize: 16,
                fontWeight: 400,
                offsetCenter: [0, '30%']
              }
            }
          ]
        }
      ]
    };
  };

  const option = (data = {}, names = []) => {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params) {
          let total = 0
          params.forEach(item => {
            total += item.value
          })
          let result = params[0].name + '<br/>' + '<div style="height:6px;"></div>';
          params.forEach(item => {
            result += item.marker + '&nbsp;' + item.seriesName + ': ' + '&nbsp;&nbsp;&nbsp;' +
              `<span style="color: '#f9f9f9';">${item.value == 0 ? 0 : (item.value / total * 100).toFixed(2)}%</span>` + '<br/>' +
              '<div style="height:6px;"></div>' + '';
          });
          return result;
        }
      },
      legend: {
        icon: 'circle',
        itemHeight: 6,
        textStyle: {
          fontSize: 12
        }
      },
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
        axisLabel: {
          interval: 10,
          rotate: 30,
          color: 'rgba(0,0,0,0.65)',
          fontSize: 10
        }
      },
      yAxis: {
        type: 'category',
        inverse: true,
        axisLine: 'none',
        axisTick: 'none',
        show: true,
        axisLabel: {
          margin: 20,
          color: '#7F7F7F',
          fontSize: '12',
          formatter: function (value) {
            // 如果需要根据值来决定是否换行，可以在这里添加逻辑
            var ret = '';
            var maxLength = 10; // 设定最大显示长度
            var valLength = value.length; // 当前数值长度
            var rowN = Math.ceil(valLength / maxLength); // 需要换行的行数
            if (rowN > 1) {
              for (var i = 0; i < rowN; i++) {
                var temp = ''; // 每次截取的字符串
                var start = i * maxLength; // 开始截取的位置
                var end = start + maxLength; // 结束截取的位置
                // 这里也可以用 slice 替代 substring
                temp = value.substring(start, end) + '\n';
                ret += temp; // 拼接生成最后的字符串
              }
              return ret;
            } else {
              return value;
            }
          }
        },
        data: names
      },
      series: [
        {
          name: '高风险',
          type: 'bar',
          barWidth: 16,
          stack: 'total',
          itemStyle: {
            color: '#FF7875',
            fontSize: 8
          },
          data: data && data.high_risk.map(item => item.value)
        },
        {
          name: '低风险',
          type: 'bar',
          barWidth: 16,
          stack: 'total',
          itemStyle: {
            color: '#FAAD14'
          },
          data: data && data.lower_risk.map(item => item.value)
        },
        {
          name: '无风险',
          type: 'bar',
          barWidth: 16,
          stack: 'total',
          itemStyle: {
            color: '#52C41A'
          },
          data: data && data.no_risk.map(item => item.value)
        }
        // {
        //   name: '整改中',
        //   type: 'bar',
        //   barWidth: 16,
        //   stack: 'total',
        //   itemStyle: {
        //     color: '#4096FF'
        //   },
        //   data: data && data.modifying_sql.map(item => item.value)
        // },
        // {
        //   name: '白名单',
        //   type: 'bar',
        //   barWidth: 16,
        //   stack: 'total',
        //   itemStyle: {
        //     color: '#52C41A'
        //   },
        //   data: data && data.whitelist_sql.map(item => item.value)
        // }
      ],
      dataZoom: [{
        // 设置滚动条的隐藏或显示
        show: names.length > 8,
        // 设置类型
        type: 'slider',
        // 设置背景颜色
        backgroundColor: '#f2f2f2',
        // 设置选中范围的填充颜色
        fillerColor: '#d2d2d2',
        // 设置边框颜色
        // borderColor: "rgb(19, 63, 100)",
        // 是否显示detail，即拖拽时候显示详细数值信息
        showDetail: false,
        // 数据窗口范围的起始数值
        startValue: 0,
        // 数据窗口范围的结束数值（一页显示多少条数据）
        endValue: 8,
        // 控制哪个轴，如果是number表示控制一个轴，
        // 如果是Array表示控制多个轴。此处控制第二根轴
        yAxisIndex: [0, 1],
        // empty：当前数据窗口外的数据，被设置为空。
        // 即不会影响其他轴的数据范围
        filterMode: 'empty',
        // 滚动条高度
        width: 9,
        // 滚动条显示位置
        height: '80%',
        // 距离右边
        right: 0,
        // 控制手柄的尺寸
        handleSize: 0,
        // 是否锁定选择区域（或叫做数据窗口）的大小
        zoomLoxk: true,
        // 组件离容器上侧的距离
        // 如果top的值为'top', 'middle', 'bottom'，组件会根据相应的位置自动对齐
        top: 'middle'
      },
      {
        // 没有下面这块的话，只能拖动滚动条，
        // 鼠标滚轮在区域内不能控制外部滚动条
        type: 'inside',
        // 控制哪个轴，如果是number表示控制一个轴，
        // 如果是Array表示控制多个轴。此处控制第二根轴
        yAxisIndex: [0, 1],
        // 滚轮是否触发缩放
        zoomOnMouseWheel: false,
        // 鼠标移动能否触发平移
        moveOnMouseMove: true,
        // 鼠标滚轮能否触发平移
        moveOnMouseWheel: true
      }
      ]
    }
  };
  return {
    option,
    barOption,
    lineOption,
    pieOption,
    popoverOption,
    codeReviewOption,
    monitorBarOption,
    monitorLineOption
  };
}
