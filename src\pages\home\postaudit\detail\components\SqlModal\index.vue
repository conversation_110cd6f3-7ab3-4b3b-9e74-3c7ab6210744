<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal v-model="visible" title="影响的sql语句" :footer="null" width="60%" @cancel="onCancel">
    <Table ref="table" v-bind="tableParams || {}">
      <!-- table插槽 -->
      <template slot="sql" slot-scope="{ text }">
        <LimitLabel :label="text || ''" :limit="80"></LimitLabel>
      </template>
    </Table>
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
import LimitLabel from '@/components/LimitLabel';
import config from './config';
// import common from '@/utils/common'

export default {
  components: { Table, LimitLabel },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      tableParams: {
        url: '/sqlreview/review/index-sql-list/',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id'
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data) {
      this.visible = true;
      this.$set(this.tableParams, 'reqParams', {
        index_id: data.id
      });
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    }
  }
};
</script>

<style lang="less" scoped>
</style>
