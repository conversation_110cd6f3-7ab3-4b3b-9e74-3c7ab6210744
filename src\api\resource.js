import Http from '@/utils/request'

export function getResourceData(params = {}) {
  return Http({
    url: `/sqlreview/project_config/resource/page`,
    method: 'get',
    params: params
  });
}

export function resourceSave(params = {}) {
  return Http({
    url: `/sqlreview/project_config/resource/save`,
    method: 'post',
    data: params
  });
}

export function resourceUpdate(params = {}) {
  return Http({
    url: `/sqlreview/project_config/resource/update`,
    method: 'post',
    data: params
  });
}

export function resourceRemove(params = {}) {
  return Http({
    url: `/sqlreview/project_config/resource/delete`,
    method: 'post',
    data: params
  });
}

export function updateAllResource(params = {}) {
  return Http({
    url: `/bettle/base/resource/update_all_resource`,
    method: 'post',
    data: params,
    timeout: 30000
  });
}

export function roleUpdate(params = {}) {
  return Http({
    url: `/bettle/base/role/update/${params.role_id}/${params.menu_code}`,
    method: 'put',
    data: params
  });
}
export function roleUpdateBatch(params = {}) {
  return Http({
    url: `/bettle/base/role/update/batch`,
    method: 'put',
    data: params
  });
}