<template>
  <div class="home-detail">
    <div class="frame-button-wrapper">
      <a-button type="primary" @click="download">导出</a-button>
    </div>
    <div class="common-view">
      <!-- <div>
        <span class="common-view-title">时间范围：</span>
        <LimitLabel :hasTips="true" :label="infoData.time_range" :limit="20"></LimitLabel>
      </div>-->
      <!-- <div v-if="this.db_type == 'oracle'">
        <span class="common-view-title">审核用户：</span>
        {{ infoData.user || '--' }}
      </div> -->
      <div>
        <span class="common-view-title">数据源：</span>
        <LimitLabel :hasTips="true" :label="infoData.data_source" :limit="20"></LimitLabel>
        <!-- {{ infoData.data_source || '--' }} -->
      </div>
      <div>
        <span class="common-view-title">创建人：</span>
        {{ infoData.created_by || '--' }}
      </div>
      <div>
        <span class="common-view-title">审核规则：</span>
        <div v-if="infoData.rule_set.length === 0">--</div>
        <div v-else class="common-tag-style">
          <div class="common-view-tag" :class="flag ? '' : 'tag-item'">
            <a-tag
              class="tag-item"
              v-for="(item, index) in infoData.rule_set"
              :key="index"
            >{{ item }}</a-tag>
          </div>
          <a-icon
            v-if="infoData.rule_set.length > 2"
            :type="flag ? 'double-left' : 'double-right'"
            @click="toggleUpDown"
          />
        </div>
      </div>
    </div>
    <PageList
      ref="PageList"
      :searchParams="searchParams"
      :tableParams="tableParams"
      :needSplitSearch="false"
    >
      <!-- mysqle table插槽 -->
      <template v-slot:slow_query_tables="{text}">
        <LimitLabel :limit="16" :label="text || ''"></LimitLabel>
      </template>
      <template slot="sql_text" slot-scope="{ text }">
        <LimitLabel :label="text || ''" :limit="28" format="sql"></LimitLabel>
      </template>
      <template slot="oracle_event" slot-scope="{ text }">
        <LimitLabel :limit="16" :label="text || '--'"></LimitLabel>
      </template>
      <span slot="level" slot-scope="{ record }">
        <span v-if="record.level === null">--</span>
        <a-badge :color="record.level | levelColor" :text="record.level | levelStatus" />
      </span>
      <!-- 审核结果 -->
      <span slot="status" slot-scope="{ record }">
        <a-tag :color="record.status | color">{{record.status | status}}</a-tag>
      </span>
      <span slot="action" slot-scope="{ record }">
        <a @click="toDetail(record)">详情</a>
      </span>
      <!--千分位 -->
      <template slot="thousandth" slot-scope="{ text }">
        <a-statistic :value="text" :precision="2"></a-statistic>
      </template>
    </PageList>
  </div>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
import config from './config';
import common from '@/utils/common';
import PageList from '@/components/PageListNew';
import { afterwardsReortExport } from '@/api/home';

export default {
  name: 'HomePage',
  components: { LimitLabel, PageList },
  data() {
    this.config = config(this);
    this.report_id = this.$route.query.report_id;
    this.task_id = this.$route.query.task_id;
    this.db_type = this.$route.query.db_type;
    this.searchData = {};
    return {
      tableParams: {
        url:
          this.db_type == 'mysql'
            ? '/sqlreview/review/afterwards_detail_mysql_list'
            : '/sqlreview/review/afterwards_detail_list',
        reqParams: {
          report_id: this.report_id
        },
        method: 'post',
        loaded: this.onTableLoaded,
        columns:
          this.db_type == 'mysql'
            ? this.config.mysqlColumns
            : this.config.columns,
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 'max-content' } // 搭配this.config.columns里面item.width不设置使用,且如果y轴也需要滚动不兼容
      },
      searchParams: {
        fields:
          this.db_type == 'mysql'
            ? this.config.mysqlSearchFields
            : this.config.searchFields,
        multiCols: 3
      },
      infoData: {
        rule_set: []
      },
      flag: false
    };
  },
  mounted() {},
  created() {
    this.setNavi();
  },
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },
  methods: {
    // 上下折叠
    toggleUpDown() {
      this.flag = !this.flag;
    },
    getColumns(role) {
      let columns = this.config.columns;
      return columns;
    },
    toDetail(record) {
      const searchData = this.$refs.PageList.$refs.table.searchParams;
      this.$router.push({
        name: 'home-postaudit-sqldetail',
        query: {
          id: record.id,
          task_id: this.task_id,
          report_id: this.report_id,
          db_type: record.db_type,
          detail_id: record.detail_id
        },
        params: { searchData: searchData }
      });
    },
    onTableLoaded(req, res) {
      this.infoData = res.data.data.header;
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'home-postaudit-detail') {
          path = sourcePath + '?task_id=' + this.task_id;
        }
        return path;
      });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      this.searchData = { ...data };
      if (this.searchData.status && this.searchData.status.length > 0) {
        this.searchData.status = this.searchData.status.join(',');
      }
      table.refresh(null, this.searchData);
    },
    // 重置
    reset(data) {
      const { table } = this.$refs;
      this.searchData = data || {};
      table.refresh();
    },
    download() {
      this.$showLoading({
        tips: `下载中...`
      });
      afterwardsReortExport({
        report_id: this.report_id,
        task_id: this.task_id
      })
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    }
  },
  watch: {
    '$store.state.account.user'(newVal = {}) {
      this.tableParams = Object.assign({}, this.tableParams, {
        columns: this.getColumns(newVal.role)
      });
    }
  },
  filters: {
    levelStatus(value) {
      let obj = {
        0: '未知',
        1: '高风险',
        2: '中风险',
        3: '低风险',
        9: '无风险'
      };
      return obj[value];
    },
    levelColor(value) {
      let obj = {
        0: '#B0AEAE',
        1: '#FF4D4F',
        2: '#FF9358',
        3: '#1EDFA9',
        9: '#52C41A'
      };
      return obj[value];
    },
    status(value) {
      let obj = {
        0: '未知',
        1: '通过',
        '-1': '未通过',
        2: '白名单通过',
        9: '错误'
      };
      return obj[value];
    },
    color(value) {
      let obj = {
        0: '#B0AEAE',
        1: '#52C41A',
        '-1': '#FF4D4F',
        2: '#52C41A',
        9: '#FF4D4F'
      };
      return obj[value];
    },
    type(value) {
      let obj = {
        1: '单次',
        2: '多次'
      };
      return obj[value];
    }
  }
};
</script>

<style lang="less" scoped>
.home-detail {
  .common-view {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    margin-top: -8px;
    padding: 16px;
    background: rgba(24, 144, 255, 0.06);
    > div {
      width: 18.75%;
      min-width: 300px;
      flex-shrink: 0;
      color: rgba(0, 0, 0, 0.6);
      text-align: center;
      display: flex;
      align-items: center;
      padding: 0 16px;
      margin-bottom: 8px;
      .common-view-title {
        font-weight: 500;
        color: #000000;
        flex-shrink: 0;
      }
      .tag-item {
        height: 24px;
        overflow: hidden;
      }
      .common-tag-style {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .anticon-double-right {
          transform: rotate(90deg);
        }
        .anticon-double-left {
          transform: rotate(90deg);
        }
        .common-view-tag {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-wrap: wrap;
          .tag-item {
            margin-bottom: 3px;
          }
        }
      }
    }
    > div:nth-child(1) {
      width: 25%;
    }
  }
  /deep/.limit-label pre {
    font-size: 14px;
  }
  @media screen and (max-width: 1500px) {
    .common-view > div {
      width: 33.3%;
    }
  }

  @media screen and (max-width: 1500px) {
    .common-view > div:nth-child(1) {
      width: 33.3%;
    }
  }
  /deep/.ant-statistic-content {
    color: rgba(0, 0, 0, 0.65);
    font-size: 16px;
  }
}
</style>
