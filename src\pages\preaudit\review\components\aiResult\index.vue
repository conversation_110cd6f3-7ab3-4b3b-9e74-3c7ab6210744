<template>
  <a-card type="small" :bordered="false" class="small-card">
    <div class="title">
      <!-- <a-icon type="robot" /> -->
      <span>AI判定结果</span>
    </div>
    <div class="ai-comment-part">
      <div
        :class="['result', !isTaiLong && flag ? 'has-sql-modify' : '']"
        :style="{background: backgroundColor[dataInfo.risk]}"
      >
        <!-- <div class="result has-sql-modify" :style="{background: backgroundColor[dataInfo.ai_status]}"> -->
        <div class="result-icon">
          <a-icon :style="{color: statusColor[dataInfo.risk]}" :type="statusType" />
        </div>
        <div class="result-text">
          <div
            class="no-pass"
            :style="{color: statusColor[dataInfo.risk ] }"
          >{{ dataInfo.risk | riskStatus }}</div>
          <!-- SQL改写 -->
          <!-- <sqlModify @viewModifyPlan="onViewModifyPlan" v-if="!isTaiLong && flag"></sqlModify> -->
          <!-- <sqlModify @viewModifyPlan="onViewModifyPlan"></sqlModify> -->
        </div>
      </div>

      <div class="part">
        <div class="part-level">
          <div v-for="(item, index) in dataInfo.ai_comment" :key="index" class="level">
            <span>
              <!-- rule_result 0 高风险 1 低风险 -->
              <custom-icon
                class="ccg-icon high"
                type="lu-icon-alarm"
                v-if="item.rule_result == 0"
              />
              <custom-icon
                class="ccg-icon low"
                type="lu-icon-alarm"
                v-if="item.rule_result == 1"
              />
              <custom-icon v-if="item.all_wrong == 1" class="ccg-icon error" type="lu-icon-unusual"  />
            </span>
            <span class="text">{{ item.ai_comment }}</span>
            <!-- <span class="ccg-num">
              <a-popover placement="top" v-if="item.suggest" overlayClassName="suggest-part">
                <template slot="content">
                  <div class="suggest-title">优化建议</div>
                  <RichEditorViewer class="rich-editor-preview" v-model="item.suggest"></RichEditorViewer>
                </template>
                <custom-icon type="bulb" theme="filled" style="margin-left: 4px" />
              </a-popover>
            </span>-->
          </div>
          <div v-if="dataInfo.ai_comment.length<=0 && sqlErrorMessage.length <= 0" class="part-no">
            <custom-empty />
          </div>
        </div>
        <div v-if="sqlErrorMessage && sqlErrorMessage.length > 0">
          <Table v-bind="tableParams" :dataSource="sqlErrorMessage">
            <div
              slot="error_message"
              slot-scope="{ text }"
              :class="['error-message', sqlPlanInfo.length > 0 ? 'has-splan-info' : '']"
            >
              <LimitLabel :label="text || ''" mode="ellipsis" :block="true"></LimitLabel>
            </div>
          </Table>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
import sqlModify from '@/components/Biz/ReviewDetail/sqlModify';
import RichEditorViewer from '@/components/RichEditor/viewer';
import Table from '@/components/Table';
import config from './config';
export default {
  components: { Table, LimitLabel, sqlModify, RichEditorViewer },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    },
    pieOption: {
      type: Object,
      default: () => {}
    },
    sqlErrorMessage: {
      type: Array,
      default: () => []
    },
    flag: {
      type: Boolean,
      default: false
    },
    sqlPlanInfo: {
      type: Array,
      default: () => []
    },
    id: String | Number
  },
  computed: {
    statusType() {
      if (this.dataInfo.ai_status === 1 || this.dataInfo.ai_status === 2) {
        return 'file-done';
      } else if (
        this.dataInfo.ai_status === -1 ||
        this.dataInfo.ai_status === 9
      ) {
        return 'exception';
      } else {
        return 'file-unknown';
      }
    },
    sqlScore() {
      let score = 0;
      if (this.dataInfo.rule_category) {
        const ruleCategory = this.dataInfo.rule_category.map(
          item => item.value
        );
        if (ruleCategory.length > 0) {
          score = ruleCategory.reduce((prev, curr) => {
            return prev + curr;
          });
        }
      }
      return score;
    },
    // 泰隆银行特有
    isTaiLong() {
      const specificConfig = process.channel === 'TaiLongBank';
      return specificConfig;
    }
  },
  data() {
    this.config = config(this);
    return {
      statusColor: this.config.statusColor,
      levelColor: this.config.levelColor,
      chartLevelColor: this.config.chartLevelColor,
      // sqlErrorMessage: [],
      tableParams: {
        url: '',
        reqParams: {},
        columns: this.config.columns,
        showHeader: false,
        size: 'small',
        rowKey: 'id',
        // scroll: { x: 600 },
        pagination: false
      },
      backgroundColor: {
        null: 'rgba(58, 151, 76, 0.2)',
        low: 'rgba(242,147,57, 0.2)',
        high: 'rgba(231, 29, 54, 0.05)',
        error: 'rgba(0, 59, 114, 0.06)'
      }
    };
  },
  methods: {
    // 查看sql改写方案
    // onViewModifyPlan() {
    //   this.$router.push({
    //     name: 'home-sqlreview-plan',
    //     params: {
    //       id: this.$route.params.id,
    //       params: this.$route.params.searchData
    //     }
    //   });
    // }
  },
  watch: {
    // dataInfo: {
    //   handler(newVal) {
    //     this.sqlErrorMessage = newVal.sqlErrorMessage || [];
    //   }
    // }
  },
  filters: {
    riskStatus(value) {
      let obj = {
        null: '无风险',
        low: '低风险',
        high: '高风险',
        error: '异常'
      };
      return obj[value];
    }
  }
};
</script>

<style lang="less" scoped>
.small-card {
  margin-bottom: 24px;
  border-radius: 8px;
  /deep/.ant-card-body {
    padding: 32px;
  }
  .title {
    font-family: PingFangSC-Semibold;
    font-size: 16px;
    color: #27272a;
    font-weight: 600;
    margin: 6px 0 16px 0;
  }
  .ai-comment-part {
    display: flex;
    .result {
      width: 180px;
      min-width: 180px;
      height: 141px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      &.has-sql-modify {
        justify-content: flex-start;
        .sql-modify-content {
          margin-bottom: 2px;
          /deep/ .sql-modify-button {
            .custom-icon {
              margin-bottom: 6px;
            }
          }
        }
        .result-icon {
          margin-top: 26px;
        }
      }
      .result-icon {
        width: 100%;
        text-align: center;
      }
      .anticon-file-unknown {
        font-size: 36px;
      }
      .anticon-file-done {
        font-size: 36px;
      }
      .anticon-exception {
        font-size: 36px;
      }
      .result-text {
        width: 100%;
        text-align: center;
      }
      .no-pass {
        // font-family: PingFangSC-Medium;
        font-size: 20px;
        color: #e71d36;
        font-weight: 500;
        margin: 6px 0 2px 0;
      }
    }

    .part {
      position: relative;
      width: 90%;
      height: 141px;
      text-align: left;
      padding: 0 20px;
      overflow-y: auto;
      .error-message {
        max-width: 400px;
        &.has-splan-info {
          max-width: 450px;
        }
      }
      .part-level {
        .level {
          width: 100%;
          padding: 0;
          margin: 2px 0;
          background-color: transparent;
          color: #0f78fb;
          // line-height: 25px;
          .level0 {
            color: #b0aeae;
          }
          .level1 {
            color: #ff4d4f;
          }
          .level2 {
            color: #ff9358;
          }
          .level3 {
            color: #1edfa9;
          }
          .level9 {
            color: #52c41a;
          }
          .ccg-icon {
            &.high {
              color: #e71d36;
              margin-right: 4px;
            }
            &.low {
              color: #f29339;
              margin-right: 4px;
            }
            &.error {
              color: #71717a;
              // margin-right: 10px;
            }
          }
          .ccg-num {
            text-align: right;
            // color: #71717a;
            font-size: 14px;
            white-space: nowrap;
          }
          .text {
            font-size: 14px;
            color: #71717a;
            font-weight: 400;
          }
        }
      }
      .part-no {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}
/deep/.ant-badge-status-processing:after {
  -webkit-animation: none;
  animation: none;
}
/deep/ .ant-list-item {
  justify-content: stretch;
}
@media screen and (max-width: 1560px) {
  .small-card {
    .ai-comment-part {
      .part {
        .error-message {
          max-width: 300px;
          &.has-splan-info {
            max-width: 300px;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.ant-popover {
  &.suggest-part {
    .ant-popover-content {
      .ant-popover-inner {
        .ant-popover-inner-content {
          max-width: 300px;
          max-height: 200px;
          // font-family: .PingFangSC-Regular;
          font-size: 12px;
          font-weight: 400;
          .suggest-title {
            color: #27272a;
            height: 24px;
            line-height: 24px;
          }
          .suggest-des {
            color: #71717a;
            height: 24px;
            line-height: 24px;
          }
        }
      }
    }
  }
}
</style>