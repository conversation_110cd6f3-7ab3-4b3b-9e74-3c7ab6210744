<template>
  <Select
    class="biz-data-base-choose"
    ref="select"
    v-bind="selectProps"
    :reqParams="reqParams"
    :value="localVal"
    :disabledKeys="disabledKeys"
    dropdownClassName="biz-data-base-choose-drapdown"
    @change="onChange"
  >
    <InstanceItem
      slot="dataBase"
      slot-scope="{ data }"
      class="app-add-choose-data-base"
      :tagText="data.instance_usage"
      :isNeedTips="data.isNeedTips"
      :src="data.db_type"
      :view="data.view"
      :text="
        data.showText
          ? data.showText
          : data.ip && data.port
          ? `${data.label}@${data.ip}:${data.port}${
              data.instance_name ? '(实例-' + data.instance_name + ')' : ''
            }`
          : `${data.label}${
              data.instance_name ? '(实例-' + data.instance_name + ')' : ''
            }`
      "
      :disabled="disabledKeys.includes(data.value)"
      :sourceInstance="data"
      :LimitLabelProps="LimitLabelProps"
    ></InstanceItem>
    <div
      slot="extra"
      slot-scope="{ value }"
      v-if="selectProps.mode !== 'default' && needConfirm"
    >
      <a-divider style="margin: 4px 0" />
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4px 8px 8px 8px;
        "
        @mousedown="(e) => e.preventDefault()"
      >
        <div style="font-size: 12px">
          已选中
          <span style="color: rgba(0, 0, 0, 0.45)">{{
            (value || []).length
          }}</span>
        </div>
        <a-button size="small" type="primary" @click="onChoose(value)"
          >确定</a-button
        >
      </div>
    </div>
  </Select>
</template>

<script>
import Select from '@/components/Select';
import InstanceItem from '@/components/Biz/InstanceItem';
const defaultProps = ctx => {
  return {
    url: '',
    loaded: data => {
      ctx.$emit('getList', data);
    },
    mode: 'multiple',
    placeholder: '请选择数据库',
    optionItemSlot: 'dataBase',
    optionLabelProp: 'label',
    optionFilterProp: 'label',
    backSearch: true,
    dropdownMatchSelectWidth: true
  };
};
export default {
  components: { Select, InstanceItem },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    reqParams: {
      type: Object,
      default: () => {}
    },
    // value: {
    //   type: Array,
    //   default: () => []
    // },
    value: {
      type: [String, Number, Boolean, Object, Array],
      default: undefined
    },
    disabledKeys: {
      type: Array,
      default: () => []
    },
    isClear: {
      type: Boolean,
      default: true
    },
    needConfirm: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      localVal: this.value,
      // params: Object.assign(
      //   {
      //     url: '',
      //     loaded: data => {
      //       this.$emit('getList', data);
      //     },
      //     mode: 'multiple',
      //     placeholder: '请选择数据库',
      //     optionItemSlot: 'dataBase',
      //     optionLabelProp: 'label',
      //     optionFilterProp: 'label',
      //     backSearch: true,
      //     dropdownMatchSelectWidth: true
      //   },
      //   this.$attrs
      // ),
      LimitLabelProps: { popoverProps: { placement: 'top' } }
    };
  },
  computed: {
    selectProps() {
      return { ...defaultProps(this), ...this.$attrs };
    }
  },
  created() {},
  mounted() {},
  methods: {
    onChange(value) {
      this.localVal = value;
      this.$emit('change', value);
    },
    // 确定
    onChoose(value) {
      this.onChange(value);
      this.$emit('choose', value);
      this.blur();
      if (this.isClear) this.localVal = [];
    },
    focus() {
      const { select } = this.$refs;
      if (select) {
        this.$nextTick(() => {
          select.onDropdownVisibleChange(true);
          select.exec('focus');
        });
      }
    },
    blur() {
      const { select } = this.$refs;
      if (select) {
        this.$nextTick(() => {
          select.onDropdownVisibleChange(false);
          select.exec('blur');
        });
      }
    },
    getEmpty() {
      return ['multiple', 'tags'].includes(this.selectProps.mode)
        ? []
        : undefined;
    }
  },
  watch: {
    // reqParams: {
    //   handler(newVal) {
    //     this.$set(this.params, 'reqParams', newVal);
    //   },
    //   immediate: true
    // },
    value: {
      handler(newVal) {
        this.localVal = newVal || this.getEmpty();
      }
    },
    disabledKeys: {
      handler(newVal) {
        this.disabledKeys = newVal || [];
      }
    }
  }
};
</script>
<style lang="less" scoped>
.biz-data-base-choose {
  width: 100%;
}
</style>
<style lang="less">
.biz-data-base-choose {
  &.selected-children {
    .ant-select-selection-selected-value {
      height: 32px;
      .biz-instance-item {
        .instance-item-tag {
          height: 30px;
          padding: 0px 16px !important;
          border: none !important;
          margin-left: -10px;
          .ant-tag,
          .database-image {
            transition: none;
          }
          .database-image {
            width: 95% !important;
          }
        }
      }
    }
  }
}
.biz-data-base-choose-drapdown {
  &.ant-select-dropdown {
    .ant-select-dropdown-menu {
      .ant-select-dropdown-menu-item {
        // padding: 0;
        &.custom-select-search-tips {
          margin-left: 10px;
        }
        &.ant-select-dropdown-menu-item-active {
          background: rgba(33, 155, 227, 0.15);
        }
        &.ant-select-dropdown-menu-item-selected {
          background: @primary-1;
        }
        .app-add-choose-data-base {
          &.biz-instance-item {
            .instance-item-tag {
              border: none;
              border-radius: 0;
              background: transparent;
            }
          }
        }
      }
    }
  }
}
</style>