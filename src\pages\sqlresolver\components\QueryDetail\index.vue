<template>
  <div class="psc-right-query-detail">
    <a-tabs v-model="activeKey" @change="onChange">
      <!-- 校验 -->
      <template v-if="resultType === 'check'">
        <template v-for="(item, index) in checkList">
          <a-tab-pane :key="`check_${index}`">
            <div class="tab-check-name" slot="tab">
              <span>{{getTabNamme(checkList, index)}}</span>
              <custom-icon :type="getCheckStatus(item).icon"></custom-icon>
            </div>
            <Check
              :info="item"
              :resize="resize"
              :resultActiveTab="activeKey"
              :resultTab="`check_${index}`"
              @markCoder="onMarkCoder"
            />
          </a-tab-pane>
        </template>
        <!-- 站位 -->
        <a-tab-pane key="info" tab="信息" v-if="checkList.length <= 0">
          <!-- <Info :list="infoList" /> -->
        </a-tab-pane>
      </template>
      <!-- 运行 -->
      <template v-else>
        <template v-for="(item, index) in resultList">
          <a-tab-pane :key="`result_${index}`" :tab="getTabNamme(resultList, index)">
            <Result
              :info="item"
              :resize="resize"
              :resultActiveTab="activeKey"
              :resultTab="`result_${index}`"
            />
          </a-tab-pane>
        </template>
        <!-- 基础结果信息 -->
        <a-tab-pane key="info" tab="信息">
          <Info :list="infoList" />
        </a-tab-pane>
      </template>
    </a-tabs>
  </div>
</template>

<script>
import Info from './Info';
import Result from './Result';
import Check from './Check';
import ResizeObserver from 'resize-observer-polyfill';

export default {
  components: { Info, Result, Check },
  props: {
    database: Object,
    queryTab: String,
    queryActiveTab: String
  },
  inject: ['instanceItem'],
  data() {
    // const panes = [
    //   { title: '信息', content: 'Content of Tab 1', key: 'info' },
    //   { title: '结果', content: 'Content of Tab 2', key: 'result' }
    // ];
    // this._resize = _.debounce(this.setResize, 300);
    this._resize = this.setResize;
    return {
      activeKey: '',
      infoList: [],
      resize: 0,
      resultType: 'run'
    };
  },
  computed: {
    resultList() {
      return this.infoList.filter(
        item => item && item.result && item.data != null
      );
    },
    checkList() {
      return [...this.infoList];
    }
  },
  beforeDestroy() {
    if (this.ro) {
      this.ro.unobserve(this.$el);
      this.ro = null;
    }
  },
  mounted() {
    this.ro = new ResizeObserver((entries, observer) => {
      // console.log(entries, 'entries');
      this._resize();
    });
    this.ro.observe(this.$el);

    this.$bus.$on('sqlresolver-database-run-done', data => {
      const { instanceId, list = [] } = data;
      if (instanceId != _.get(this.instanceItem, 'key')) {
        return;
      }
      if (this.queryTab === this.queryActiveTab) {
        this.resultType = 'run';
        console.log(list);
        this.infoList = list;
        const _key = this.resultList.length > 0 ? 'result_0' : 'info';
        const isCurr = _key === this.activeKey;
        this.activeKey = _key;

        // 不是当前页了，需要resize
        if (!isCurr) {
          this.onChange();
        }
      }
    });

    this.$bus.$on('sqlresolver-database-check-done', data => {
      const { instanceId, list = [] } = data;
      if (instanceId != _.get(this.instanceItem, 'key')) {
        return;
      }
      if (this.queryTab === this.queryActiveTab) {
        this.resultType = 'check';
        console.log(list);
        this.infoList = list;
        this.activeKey = 'check_0';
      }
    });
  },
  methods: {
    setResize() {
      this.resize = this.resize + 1;
    },
    onChange() {
      setTimeout(() => {
        this._resize();
      }, 300);
    },
    getTabNamme(list, index) {
      const baseName = this.resultType === 'check' ? '校验' : '结果';
      return `${baseName}${list.length <= 1 ? '' : index + 1}`;
    },
    getCheckStatus(item) {
      if (!item) return;
      let map = {
        0: '未知',
        1: '通过',
        2: '白名单通过',
        9: '错误',
        '-1': '不通过'
      };
      const status = item.ai_status;
      let icon;
      let viewStatus;
      if (status == 0) {
        icon = 'lu-icon-warning';
        viewStatus = 'warning';
      } else if ([1, 2].includes(status)) {
        icon = 'lu-icon-success';
        viewStatus = 'success';
      } else {
        icon = 'lu-icon-error';
        viewStatus = 'error';
      }
      return {
        icon,
        viewStatus,
        name: map[status]
      };
    },
    onMarkCoder(pos) {
      this.$bus.$emit('sqlresolver-database-mark-pos', {
        markPos: pos,
        queryTab: this.queryTab,
        instanceId: _.get(this.instanceItem, 'key')
      });
    }
  }
};
</script>

<style lang="less" scoped>
.psc-right-query-detail {
  padding: 8px 12px;
  height: 100%;

  /deep/ .ant-tabs {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: 100%;
    > .ant-tabs-bar {
      margin-bottom: 0;

      .ant-tabs-tab,
      .ant-tabs-tab-active {
        font-weight: normal;
        color: rgba(0, 0, 0, 0.65);
        border-radius: 8px;
        padding: 5px 8px;
        margin: 4px 8px 12px 0;
      }
      .ant-tabs-tab-active {
        background: rgba(0, 0, 0, 0.04);
      }

      .tab-check-name {
        // display: flex;
        // align-items: center;

        // > .custom-icon {
        //   margin-left: 4px;
        // }
      }
    }

    > .ant-tabs-content {
      display: flex;
      flex-grow: 1;
      > .ant-tabs-tabpane {
        position: relative;
        // overflow: auto;
        &.ant-tabs-tabpane-active {
          flex-grow: 1;
        }
        &.ant-tabs-tabpane-inactive {
          // width: 0;
        }
      }
    }

    .ant-tabs-ink-bar {
      display: none !important;
    }
  }
}
</style>
