export default function (ctx) {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: 'SQL文本',
      key: 'sql_text',
      dataIndex: 'sql_text',
      // width: 400,
      scopedSlots: { customRender: 'sql_text' }
    },
    {
      title: 'SQL代码',
      key: 'source_text',
      dataIndex: 'source_text',
      scopedSlots: { customRender: 'sql_text' }
    },
    {
      title: 'AI风险等级',
      key: 'risk',
      dataIndex: 'risk',
      width: 120,
      scopedSlots: { customRender: 'risk' }
    },
    // {
    //   title: 'SQL标签',
    //   key: 'label_type',
    //   dataIndex: 'label_type',
    //   width: 120,
    //   scopedSlots: { customRender: 'label_type' }
    // },
    // {
    //   title: 'SQL备注',
    //   key: 'sqlmap_note',
    //   dataIndex: 'sqlmap_note',
    //   width: 200,
    //   scopedSlots: { customRender: 'sqlmap_note' }
    // },
    {
      title: '审核结果',
      key: 'review_status',
      dataIndex: 'review_status',
      scopedSlots: { customRender: 'review_status' },
      width: 120
    }
  ]
  // .map((item) => {
  //   return {
  //     ...item,
  //     width: undefined
  //   };
  // });
  const fields = [
    {
      type: 'Select',
      label: 'Review状态',
      key: 'status_of_review',
      sourceKey: 'review_status',
      props: {
        // mode: 'multiple',
        options: [
          // {
          //   label: '未知',
          //   value: '0'
          // },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '不通过',
            value: '-1'
          }
          // {
          //   label: '白名单通过',
          //   value: '2'
          // },
          // {
          //   label: '错误',
          //   value: '9'
          // }
        ]
        // separator: ','
      }
    },
    {
      type: 'Select',
      label: 'AI风险等级',
      key: 'risk',
      props: {
        options: [
          {
            label: '高风险',
            value: 'high'
          },
          {
            label: '低风险',
            value: 'low'
          },
          {
            label: '无风险',
            value: 'no_risk'
          },
          {
            label: '异常',
            value: 'error'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'SQL标签',
      key: 'label_type',
      props: {
        mode: 'multiple',
        options: [
          {
            label: '无',
            value: 0
          },
          {
            label: '白名单',
            value: 1
          },
          {
            label: '整改中',
            value: 2
          }
        ]
      }
    }
  ];

  const dateSourceColumns = [
    {
      title: '标签ID',
      dataIndex: 'label_id',
      key: 'label_id',
      scopedSlots: { customRender: 'label_id' }
    },
    {
      title: '标签ID类型',
      dataIndex: 'label_type',
      key: 'label_type',
      scopedSlots: { customRender: 'label_type' },
      width: 100
    },
    {
      title: 'SQL/XML',
      dataIndex: 'sql_source',
      key: 'sql_source',
      width: 220,
      scopedSlots: { customRender: 'sql_source' }
    },
    {
      title: '标签属性',
      dataIndex: 'label_attribute',
      key: 'label_attribute',
      customRender: (text) => {
        return text == 0 ? '白名单' : '整改中'
      },
      width: 220
    },
    {
      title: 'AI风险等级',
      dataIndex: 'risk',
      key: 'risk',
      scopedSlots: { customRender: 'risk' },
      width: 150
    },
    {
      title: 'schema',
      dataIndex: 'schema',
      key: 'schema',
      scopedSlots: { customRender: 'schema' },
      width: 150
    },
    {
      title: '状态',
      dataIndex: 'label_status',
      key: 'label_status',
      scopedSlots: { customRender: 'label_status' },
      width: 120
    },
    {
      title: '申请人',
      dataIndex: 'created_by',
      key: 'created_by',
      scopedSlots: { customRender: 'created_by' },
      width: 120
    },
    {
      title: '审核人',
      dataIndex: 'audit_user',
      key: 'audit_user',
      scopedSlots: { customRender: 'audit_user' },
      width: 120
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      scopedSlots: { customRender: 'created_at' },
      width: 120
    },
    {
      title: '失效时间',
      dataIndex: 'expired_time',
      key: 'expired_time',
      scopedSlots: { customRender: 'expired_time' },
      width: 120
    },
    {
      title: '有效期',
      dataIndex: 'permanent_day',
      key: 'permanent_day',
      customRender: text => {
        return text == 0 || text == '永久' ? '永久' : text + '天'
      },
      width: 120
    },
    // {
    //   title: '关联项目',
    //   dataIndex: 'projects',
    //   key: 'projects',
    //   width: 220,
    //   scopedSlots: { customRender: 'limit-tag' }
    // },
    // {
    //   title: 'DBA负责人',
    //   dataIndex: 'dba_leader',
    //   key: 'dba_leader',
    //   scopedSlots: { customRender: 'limit-tag' },
    //   width: 120
    // },
    // {
    //   title: '项目负责人',
    //   dataIndex: 'project_group_leader',
    //   key: 'project_group_leader',
    //   scopedSlots: { customRender: 'limit-tag' },
    //   width: 120
    // },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 120,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const dateSourceFields = [
    {
      type: 'Input',
      label: '标签ID',
      key: 'label_id'
    },
    {
      type: 'Input',
      label: 'SQL/XML',
      key: 'sql_source'
    },
    {
      type: 'Select',
      label: '标签ID类型',
      key: 'label_type',
      props: {
        options: [
          { label: 'SQL_ID', value: 0 },
          { label: 'XML_TEXT', value: 1 },
          { label: 'XML_ID', value: 2 },
          { label: 'TABLE', value: 3 },
          { label: 'JAVA', value: 4 },
          { label: 'PROCEDURE', value: 5 }
        ]
      }
    },
    {
      type: 'Select',
      label: 'AI风险等级',
      key: 'risk',
      props: {
        options: [
          { label: '高风险', value: -1 },
          { label: '未审核', value: 0 },
          { label: '无风险', value: 1 },
          // { label: '低风险', value: 2 },
          // { label: '审核中', value: 3 },
          { label: '低风险', value: 2 },
          { label: '异常', value: 9 }
        ]
      }
    },
    {
      type: 'Select',
      label: '状态',
      key: 'label_status',
      props: {
        options: [
          {
            label: '待审核',
            value: 0
          },
          {
            label: '审核通过',
            value: 1
          },
          {
            label: '审核未通过',
            value: -1
          },
          // {
          //   label: '已删除',
          //   value: 2
          // },
          {
            label: '已失效',
            value: 3
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '申请人',
      key: 'created_by',
      props: {
        url: '/sqlreview/project_config/select_user',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'Select',
      label: '审核人',
      key: 'audit_user',
      props: {
        url: '/sqlreview/project_config/select_user',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'RangePicker',
      label: '生效时间',
      key: 'audit_time',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    },
    {
      type: 'RangePicker',
      label: '失效时间',
      key: 'expired_time',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    },
    // {
    //   type: 'Select',
    //   label: '项目',
    //   key: 'projects',
    //   props: {
    //     url: '/sqlreview/review/all_project',
    //     reqParams: {},
    //     allowSearch: true,
    //     backSearch: true,
    //     limit: 20
    //   }
    // },
    // {
    //   type: 'Select',
    //   label: '负责人',
    //   key: 'leader',
    //   props: {
    //     url: ' /sqlreview/project_config/select_user',
    //     reqParams: {
    //       type: 'dba_and_leader'
    //     },
    //     allowSearch: true,
    //     backSearch: true,
    //     limit: 20
    //   }
    // },
    {
      type: 'Input',
      label: '',
      key: 'btns',
      hideComponent: true,
      slots: [{ key: 'btns' }],
      rules: []
    }
  ];
  return {
    columns,
    searchFields: fields,
    dateSourceColumns,
    dateSourceFields
  };
}
