<template>
  <div @click="stopTest">
    <div style="margin-bottom: 12px;">
      <Actions :stop="true" :btnList="btnList" @btnsFn="btnsFn"></Actions>
    </div>
    <div style="margin-bottom: 12px;">
      <Actions :stop="true" :btnList="btnList" @btnsFn="btnsFn" ellipsisDirection="v" :limit="3"></Actions>
    </div>
    <div style="margin-bottom: 12px;">
      <Actions :stop="true" :btnList="btnList" @btnsFn="btnsFn" ellipsisDirection="v" :limit="0"></Actions>
    </div>
    <div style="margin-bottom: 12px;">
      <Actions :stop="true" :btnList="btnList" @btnsFn="btnsFn" ellipsisDirection="v" :ellipsisUseName="true" :limit="0"></Actions>
    </div>
  </div>
</template>

<script>
import Actions from '@/components/Actions';
export default {
  components: { Actions },
  props: {},
  data() {
    return {
      btnList: [
        {
          name: '编辑',
          type: 'edit',
          icon: 'edit'
        },
        {
          name: '删除',
          type: 'delete',
          icon: 'delete',
          confirm: '确认删除'
        },
        {
          name: '下载',
          type: 'download',
          icon: 'download',
          children: [
            {
              name: '下载1',
              type: 'download1',
              icon: 'download'
            },
            {
              name: '下载2',
              type: 'download2',
              icon: 'download'
            }
          ]
        },
        {
          name: '设置',
          type: 'setting',
          icon: 'setting'
        },
        {
          name: '关闭',
          type: 'close',
          icon: 'close',
          confirm: '确认关闭'
        }
      ]
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    stopTest() {
      console.log(111111111111);
    },
    btnsFn(key) {
      // if (key == 'edit') {
      //   console.log('edit');
      // } else if (key == 'delete') {
      //   console.log('delete');
      // } else if (key == 'setting') {
      //   console.log('setting');
      // } else if (key == 'download') {
      //   console.log('download');
      // } else if (key == 'close') {
      //   console.log('close');
      // }
      console.log(key);
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>
