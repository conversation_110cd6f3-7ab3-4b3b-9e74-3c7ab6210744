export default function (ctx) {
  // 统计信息导入
  const columns = [
    {
      title: '文件',
      dataIndex: 'file_path',
      key: 'file_path'
    },
    {
      title: '目标Schema',
      dataIndex: 'target_schema',
      key: 'target_schema'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'status' }
    },
    {
      title: '原因',
      dataIndex: 'error_message',
      key: 'error_message',
      scopedSlots: { customRender: 'error_message' }
    }
  ];

  const resColumns = [
    {
      title: '源Schema',
      dataIndex: 'source_schema',
      key: 'source_schema'
    },
    {
      title: '目标Schema',
      dataIndex: 'target_schema',
      key: 'target_schema',
      visible: false
    },
    {
      title: '文件',
      dataIndex: 'file_path',
      key: 'file_path',
      scopedSlots: { customRender: 'file_path' },
      visible: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'status' }
    },
    {
      title: '原因',
      dataIndex: 'error_message',
      key: 'error_message',
      scopedSlots: { customRender: 'error_message' }
    }
  ];

  const exportColumns = (exportType, selectedKeys = []) => {
    return exportType == 1
      ? [
          {
            title: '源schema',
            dataIndex: 'schema',
            key: 'schema',
            width: 300
          },
          {
            title: '目标schema',
            dataIndex: 'to_schema',
            key: 'to_schema',
            width: 300
          }
        ]
      : [
          {
            title: '源schema',
            dataIndex: 'schema',
            key: 'schema',
            width: 300
          },
          {
            title: '导出文件名',
            dataIndex: 'export_file',
            key: 'export_file',
            customRender: (text, record) => {
              return selectedKeys.includes(record.id)
                ? `slowlog_${record.schema}.sql`
                : '--';
            },
            width: 300
          }
        ];
  };

  // 历史记录
  const historyColumns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      customRender: (text) => {
        return text == 1 ? '导入' : '导出';
      }
    },
    {
      title: '源',
      dataIndex: 'source_type',
      key: 'source_type',
      scopedSlots: { customRender: 'source_type' }
    },
    {
      title: '目标',
      key: 'target_type',
      dataIndex: 'target_type',
      scopedSlots: { customRender: 'target_type' }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'status' }
    },
    {
      title: '创始人',
      dataIndex: 'created_by',
      key: 'created_by'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at'
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      fixed: 'right'
    }
  ];

  return {
    columns,
    historyColumns,
    resColumns,
    exportColumns
  };
}
