<template>
  <a-modal
    v-model="visible"
    title="统计信息收集"
    okText="重新收集"
    :maskClosable="false"
    :width="'60%'"
    :dialogStyle="{ 'minWidth': '700px', 'maxWidth': '800px' }"
    wrapClassName="config-datasource-statistics-drawer"
    @cancel="onCancel"
    @ok="onOk"
  >
    <div class="title">统计信息收集记录</div>
    <div class="get-statistics-wrapper">
      <Table
        ref="table"
        v-bind="tableParams || {}"
        :dataSource="dataSource || []"
        bordered
        size="small"
      >
        <template slot="message" slot-scope="{ record, text }">
          <span :style="{'color': record.status == -1 ? 'red' : ''}">{{text}}</span>
        </template>
        <DateFormat slot="created_at" slot-scope="{text}" :text="text" :limit="20" />
      </Table>
    </div>
  </a-modal>
</template>
<script>
import Table from '@/components/Table';
import DateFormat from '@/components/DateFormat';
import config from './config';
import { getStatisticsInfo } from '@/api/config/dataSource';
export default {
  components: { Table, DateFormat },
  data() {
    this.config = config(this);
    return {
      data: {},
      dataSource: [],
      visible: false,
      tableParams: {
        url: '/sqlreview/project/get_mysql_report_list',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id'
      }
    };
  },
  mounted() {},
  methods: {
    show(data = {}) {
      this.data = data;
      this.$set(this.tableParams, 'reqParams', {
        data_source_id: data.id,
        _t: +new Date()
      });
      this.visible = true;
    },
    onOk() {
      const { table } = this.$refs;
      this.$showLoading();
      getStatisticsInfo({
        data_source_id: this.data.id
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: '统计信息获取成功' });
            table.refresh();
          } else if (_.get(res, 'data.code') == 4002) {
            this.$hideLoading({
              method: 'warn',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    onCancel() {
      this.hide();
      this.$emit('refrsh');
    },
    hide() {
      this.visible = false;
    }
  }
};
</script>
<style lang="less" scoped>
.config-datasource-statistics-drawer {
  /deep/ .ant-drawer-content-wrapper {
    min-width: 900px;
  }
  .title {
    padding-bottom: 16px;
  }
}
/deep/ .ant-table-content {
  border: 1px solid #e8e8e8;
}
</style>
