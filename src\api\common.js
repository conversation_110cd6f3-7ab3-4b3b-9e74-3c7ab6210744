import Http from '@/utils/request'

export function refreshToken (params = {}) {
  return Http({
    url: `/sqlreview/token/`,
    method: 'post',
    data: params
  });
}

export function jksRefreshToken (params = {}) {
  return Http({
    url: `/sqlreview/token_for_jks/`,
    method: 'post',
    data: params
  });
}

export function modifyPwd (params = {}) {
  return Http({
    url: `/sqlreview/modify-psw/`,
    method: 'post',
    data: params
  });
}

export function verifyToken (params = {}) {
  return Http({
    url: `/sqlreview/verify_token/`,
    method: 'post',
    data: params
  });
}
export default {};