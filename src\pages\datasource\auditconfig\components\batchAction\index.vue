<template>
  <!-- 新建项目抽屉 -->
  <a-drawer
    :title="title"
    :visible="visible"
    @close="hide"
    width="600px"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="audit-config-batch-action-drawer"
  >
    <!-- 内容区域 -->
    <a-spin :spinning="spinning">
      <div class="area-box" v-if="afterAuditStatus == 1">
        <span class="title">数据库类型</span>
        <custom-form
          v-bind="customFormParams"
          :formData="customFormData"
          class="custom-form"
          ref="customForm"
        >
        </custom-form>
      </div>

      <div class="area-box" v-if="afterAuditStatus == 1">
        <span class="title">数据库配置</span>
        <Form
          ref="form"
          class="area-form"
          v-bind="formParams"
          :formData="formData"
        >
          <JCronModal
            :data="collectFrequency"
            ref="JCronModal"
            slot="frequency"
            @cronExpression="cronExpression"
          ></JCronModal
        ></Form>
      </div>
      <!-- 自动审核配置 -->
      <!-- <div class="area-box" v-show="dbType == 'POSTGRE'"> -->
      <div class="area-box" v-if="false">
        <div class="title">自动审核配置</div>
        <Form
          ref="autoReviewForm"
          class="area-form"
          v-bind="autoReviewParams"
          :formData="autoReviewData"
        ></Form>
      </div>
      <div class="area-box">
        <div class="table-block">
          <span class="title">选择数据源</span>
          <div class="search-area">
            <Select
              v-bind="selectParams"
              v-model="selectData"
              @change="onSearchDbType"
              v-if="afterAuditStatus == 0"
            />
            <InputSearch
              placeholder="ID/数据源名/连接串"
              @search="onSearchTable"
              ref="inputSearch"
            ></InputSearch>
          </div>
        </div>
        <div class="tips" v-if="dbType == 'OB_ORACLE'">
          未配置过sys账号密码的数据源请单独进行配置，不能批量开启
        </div>
        <Table
          ref="table"
          v-bind="tableParams"
          class="new-view-table small-size"
          @selectChange="selectChange"
        >
          <div slot="name" slot-scope="{ text, record }" class="name-box">
            <Tag type="Env" :text="record.env.toUpperCase()" />
            <DbImg
              value="OCEANBASE"
              mode="ellipsis"
              :schemaName="text"
              v-if="
                record.db_type == 'OB_MYSQL' || record.db_type == 'OB_ORACLE'
              "
            />
            <DbImg
              value="TDSQL"
              mode="ellipsis"
              :schemaName="text"
              v-else-if="
                record.db_type == 'TD_MYSQL' || record.db_type == 'TD_PGSQL'
              "
            />
            <DbImg
              v-else
              :value="record.db_type"
              :schemaName="text"
              mode="ellipsis"
            />
          </div>
        </Table>
      </div>
    </a-spin>
    <!-- 按钮区域 -->
    <div
      class="btns-area"
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 10
      }"
    >
      <a-button @click="hide" class="hide-btn">取消</a-button>
      <a-button @click="save" type="primary">确定</a-button>
    </div>
  </a-drawer>
</template>

<script>
import config from './config';
import Tag from '@/components/Biz/Tag';
import DbImg from '@/components/CustomImg/DbImg';
import Table from '@/components/Table';
import Form from '@/components/Form';
import Select from '@/components/Select';
import InputSearch from '@/components/InputSearch';
import JCronModal from '@/components/Biz/JCronModal';
import { batchSave } from '@/api/databaseaudit/auditconfig';
export default {
  components: { Table, Form, JCronModal, Tag, DbImg, InputSearch, Select },
  props: {},
  data() {
    this.config = config(this);
    return {
      title: '批量开启',
      visible: false,
      spinning: false,
      dataSource: [],
      tableParams: {
        url: `/sqlreview/after_audit/db_collect/batch_list`,
        reqParams: {},
        isInitReq: false,
        columns: this.config.columns,
        rowKey: 'id',
        rowSelection: {
          type: 'checkbox',
          columnWidth: '40',
          getCheckboxProps: record => ({
            // 选择框的默认属性配置
            props: {
              disabled: false
            }
          })
        }
      },
      selectedRowKeys: [],
      formData: {},
      formParams: {
        colon: false,
        layout: 'horizontal',
        hideRequiredMark: true,
        fields: this.config.oracleFields()
      },
      autoReviewData: {},
      autoReviewParams: {
        colon: false,
        layout: 'horizontal',
        hideRequiredMark: true,
        fields: this.config.autoReviewFields()
      },
      customFormData: {},
      customFormParams: {
        layout: 'horizontal',
        fields: this.getFields()
      },
      selectData: undefined,
      selectParams: {
        url: '/sqlreview/review/select_db_type/',
        placeholder: '请选择数据库'
      },
      collectFrequency: '',
      searchValue: '',
      afterAuditStatus: 1,
      dbType: null
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(num) {
      this.visible = true;
      this.afterAuditStatus = num;
      this.title = num == 1 ? '批量开启' : '批量关闭';
      if (num == 0) {
        this.$nextTick(() => {
          this.$set(this.tableParams, 'reqParams', {
            after_audit_status: 0
          });
        });
      }
    },
    hide() {
      const { table, inputSearch } = this.$refs;
      this.visible = false;
      this.afterAuditStatus = null;
      this.formData = {};
      this.customFormData = {};
      this.autoReviewData = {};
      this.selectedRowKeys = [];
      this.selectData = undefined;
      this.$set(this.tableParams, 'reqParams', {});
      inputSearch.searchVal = '';
      table.searchParams = {};
      this.dbType = null;
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    // 表格搜索
    onSearchTable(e) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { name: e });
      table.refresh();
    },
    onSearchDbType(e) {
      this.selectData = e;
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, { db_type: e });
      table.refresh();
    },
    cronExpression(data, type) {
      if (data && data.split(' ').length > 0) {
        if (data.split(' ').includes('undefined')) {
          this.formData = Object.assign({}, this.formData, {
            collect_frequency: '',
            collect_time: type
          });
        } else {
          this.formData = Object.assign({}, this.formData, {
            collect_frequency: data,
            collect_time: type
          });
          this.$refs.form.saving({
            collect_frequency: data,
            collect_time: type
          });
        }
      }
      this.$nextTick(() => {
        this.$refs.form.validate();
      });
    },
    getFields() {
      return [
        {
          type: 'DataBaseChoose',
          label: '数据库类型',
          key: 'db_type',
          props: {
            url: '/sqlreview/review/select_db_type/',
            reqParams: {},
            loaded(data) {},
            beforeLoaded(data) {
              return data.map(item => {
                return {
                  ...item,
                  showText: item.label,
                  db_type: item.label,
                  instance_usage: undefined,
                  view: 'new',
                  limit: 'limit'
                };
              });
            },
            mode: 'default',
            optionLabelProp: 'children',
            placeholder: '请选择数据库',
            backSearch: false
          },
          listeners: {
            change: value => {
              const { table, inputSearch } = this.$refs;
              this.$set(this, 'dbType', value);
              this.formData = {};
              this.selectData = undefined;
              inputSearch.searchVal = '';
              this.selectedRowKeys = [];
              table.selectedRowKeys = [];
              table.searchParams = {};
              if (value == 'ORACLE') {
                this.$set(
                  this.formParams,
                  'fields',
                  this.config.oracleFields()
                );
              } else if (value == 'POSTGRE') {
                this.$set(this.formParams, 'fields', this.config.pgFields());
                this.formData = {
                  collect_type: 'jdbc',
                  jdbc_frequency: 1000,
                  statistical_interval: 60000
                };
              } else if (value == 'MYSQL' || value == 'GOLDENDB') {
                this.$set(this.formParams, 'fields', this.config.mysqlFields());
                this.formData = {
                  collect_type: 'JDBC'
                };
              } else if (value == 'DB2' || value == 'GAUSSDB') {
                this.$set(this.formParams, 'fields', this.config.db2Fields());
              } else {
                this.$set(this.formParams, 'fields', this.config.obFields());
              }
              const { searchParams } = table;
              Object.assign(searchParams, {
                db_type: value,
                after_audit_status: this.afterAuditStatus
              });
              table.refresh();
            }
          },
          rules: [
            { required: true, message: '该项为必填项', trigger: 'change' }
          ]
        }
      ];
    },
    // 保存
    save() {
      const { form, JCronModal, customForm, autoReviewForm } = this.$refs;
      const JCronForm = JCronModal && JCronModal.$refs.form;
      if (_.isEmpty(this.selectedRowKeys)) {
        this.$message.warn('请选择数据源');
        return;
      }
      Promise.all([
        form && form.validate(),
        JCronModal && JCronForm.validate(),
        customForm && customForm.validate(),
        autoReviewForm && autoReviewForm.validate()
      ]).then(valid => {
        if (valid) {
          let params = {};
          if (this.afterAuditStatus == 1) {
            if (JCronModal) {
              JCronModal.handleSubmit();
            }
            let formData = form.getData() || {};
            let customData = customForm.getData() || {};
            let autoReviewData =
              (autoReviewForm && autoReviewForm.getData()) || {};
            params = {
              ...this.formData,
              ...formData,
              ...customData,
              ...autoReviewData,
              is_covered: formData.is_covered ? 1 : 0,
              data_source_list: this.selectedRowKeys,
              after_audit_status: 1
            };
          } else {
            params = {
              data_source_list: this.selectedRowKeys,
              after_audit_status: 0
            };
          }
          this.spinning = true;
          batchSave(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.spinning = false;
                this.$hideLoading({ duration: 0 });
                this.$emit('save');
                this.hide();
              } else {
                this.spinning = false;
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.spinning = false;
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    }
  }
};
</script>

<style lang="less">
.audit-config-batch-action-drawer {
  &.ant-drawer {
    .ant-drawer-content-wrapper {
      .ant-drawer-header {
        padding: 20px 24px;
        background: #fff !important;
        .ant-drawer-title {
          font-size: 20px;
          color: #27272a;
          font-weight: 600;
        }
        .ant-drawer-close {
          .anticon-close {
            color: #27272a;
          }
        }
      }
      .ant-drawer-body {
        padding: 0 40px;
        .content {
        }
      }
    }
    .btns-area {
      display: flex;
      justify-content: flex-end;
      .ant-btn {
        height: 36px;
        background: #ffffff;
        border: 1px solid #008adc;
        font-size: 14px;
        color: #008adc;
        font-weight: 600;
        border-radius: 6px;
        margin-left: 16px;
      }
      .ant-btn-primary {
        background: #008adc;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
  .area-box {
    margin-bottom: 32px;
    &:first-child {
      .ant-form {
        .ant-row {
          .ant-form-item-label {
            display: none;
          }
        }
        .ant-form-item-control-wrapper {
          .ant-form-item-control {
            .ant-form-item-children {
              .ant-select-selection__rendered {
                display: flex;
                align-items: center;
                .biz-instance-item {
                  .instance-item-tag {
                    &.new {
                      padding: 0;
                      border: none;
                      background: transparent;
                      width: 300px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .area-form {
      &.ant-form {
        background: #fafafa;
        padding: 20px 12px;
        > .ant-row {
          display: flex !important;
          margin-bottom: 14px;
          &:last-child {
            margin-bottom: 0;
          }
          .ant-form-item-label {
            font-size: 13px;
            color: #27272a;
            width: 100px;
            text-align: left;
            line-height: 24px;
            min-height: 24px;
          }
          > .ant-form-item-control-wrapper {
            line-height: 24px;
            > .ant-form-item-control {
              line-height: 24px;
              .ant-form-item-children {
                .ant-time-picker,
                .ant-select-selection,
                .ant-time-picker-input,
                .ant-input-number-input,
                .ant-input-number,
                .ant-select-selection__rendered {
                  height: 24px;
                  line-height: 24px;
                  width: 120px !important;
                  font-size: 13px;
                  color: rgba(0, 0, 0, 0.85);
                }

                .ant-select-selection {
                  border: 1px solid rgba(217, 217, 217, 1);
                }
                .ant-time-picker-input {
                  border: 1px solid rgba(217, 217, 217, 1);
                }
                .ant-time-picker {
                  margin-bottom: 1px;
                }
              }
            }
          }
        }
        .jcron-form {
          .ant-row {
            display: flex;
            align-items: center;
            .ant-col {
              width: 120px;
              margin-right: 8px;
              .ant-row {
                .ant-form-item-control-wrapper {
                  .ant-form-item-control {
                    line-height: 24px;
                  }
                }
              }
            }
          }
        }
      }
    }
    .title {
      display: flex;
      font-size: 14px;
      color: #27272a;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .tips {
      font-size: 14px;
      font-weight: 400;
      padding: 8px 15px;
      margin-bottom: 8px;
      background: #e6f7ff;
      color: rgba(0, 0, 0, 0.85);
      border-radius: 4px;
      border: 1px solid rgba(145, 213, 255, 1);
    }
    .table-block {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      .search-area {
        display: flex;
        // align-items: center;
        .custom-select {
          width: 180px;
          margin-right: 8px;
          background: #ffffff;
          .ant-select-selection {
            height: 26px;
            color: rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(217, 217, 217, 1);
            .ant-select-selection__rendered {
              line-height: 26px;
              color: rgba(0, 0, 0, 0.85);
            }
          }
        }
        .custom-input-search {
          width: 180px;
          background: #ffffff;
          .ant-input-affix-wrapper {
            .ant-input {
              height: 26px;
              color: rgba(0, 0, 0, 0.85);
              border: 1px solid rgba(217, 217, 217, 1);
            }
            .ant-input-suffix {
              right: 8px;
              color: rgba(0, 0, 0, 0.25);
            }
          }
        }
      }
    }
  }

  .name-box {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    .ant-tag {
      border-radius: 6px;
      white-space: nowrap;
      color: #fff !important;
    }
    .limit-label {
      width: 300px;
    }
  }
}
</style>
