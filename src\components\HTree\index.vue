<template>
  <div class="h-tree">
    <template v-for="item in data">
      <HTreeItem :key="item.value" :value="item" @select="onSelect"></HTreeItem>
    </template>
  </div>
</template>

<script>
import HTreeItem from './HTreeItem';
// import common from '@/utils/common';
// import _ from 'lodash';

export default {
  inheritAttrs: false,
  components: { HTreeItem },
  props: {
    treeData: {
      type: Array,
      default: () => []
    },
    expandedLevel: Number
  },
  data() {
    return {
      data: this.getTreeData(this.treeData)
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    getTreeData(data = []) {
      const { expandedLevel } = this;
      const loop = (list = [], level, isRoot) => {
        list.forEach((item, index) => {
          if (isRoot) {
            item.isRoot = true;
          }
          if (!item.children || item.children.length <= 0) {
            item.isLeaf = true;
          }
          if (index === 0) {
            item.isFirst = true;
          }
          if (index === list.length - 1) {
            item.isLast = true;
          }
          if (expandedLevel != null && level <= expandedLevel) {
            item.expanded = true;
          }

          if (item.children && item.children.length > 0) {
            loop(item.children, ++level);
          }
        });
      };
      loop(data, 0, true);

      return [...data];
    },
    onSelect(data) {
      this.$emit('select', data);
    }
  },
  watch: {
    treeData(newVal) {
      this.data = this.getTreeData(newVal);
    }
  }
};
</script>

<style lang="less" scoped>
.h-tree {
}
</style>
