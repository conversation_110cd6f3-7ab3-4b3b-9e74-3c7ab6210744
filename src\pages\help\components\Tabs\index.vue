<template>
  <div>
    <div v-for="item in list" :key="item">
      <Tabs v-model="activeKey" :tabsList="tabsList" :mode="item" @change="onChange">
        <div slot="one" slot-scope="{ item }">切换111111{{item.key}}</div>
        <div slot="two" slot-scope="{ item }">切换2222222{{item.key}}</div>
        <div slot="three" slot-scope="{ item }">切换333333333333333{{item.key}}</div>
      </Tabs>
      <a-divider/>
    </div>
  </div>
</template>

<script>
import Tabs from '@/components/Tabs';
export default {
  components: { Tabs },
  props: {},
  data() {
    return {
      activeKey: 'two',
      list: ['', 'scale', 'divider', 'tag', 'badge'],
      tabsList: [
        {
          tab: '切换1',
          key: 'one',
          count: '6'
        },
        {
          tab: '切换2',
          key: 'two',
          count: '102'
        },
        {
          tab: '切换3',
          key: 'three',
          count: '0'
        }
      ]
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    onChange(e) {
      console.log(e, this.activeKey);
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>
