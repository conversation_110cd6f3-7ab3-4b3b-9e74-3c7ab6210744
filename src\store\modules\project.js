export default {
  namespaced: true,
  state: {
    version: '', // 版本号
    isFirstLogin: '', // 账号是否第一次登录
    hasSubscribe: '', // 我的应用是否一个都没关注
    closeForever: '', // 判断我关注的应用弹窗是否不再弹出
    permissionManagement: '', // 系统配置 项目权限隔离 用户逻辑是否选取 （1是选取）
    sqlToolSwitch: '', // 判断sql编辑器，是否显示 0 不展示，1 展示
    cybrearkSwitch: 0, // cybrebark的开关 1：开，0 ：关。 开的话数据源配置 新增实例密码不做必填校验
    reviewProcedureSwitch: '', // 判断存储过程扫描，是否显示 0 不展示，1 展示
    dmlSwitch: '', // dml_switch开关 1：开，0 ：关。 0 不展示，1 展示
    isThirdLogin: null, // is_third_login true为第三方登录
    sqlresolverUrl: '', // sql查询平台地址
    metaUrl: '', // 数据监控地址
    operatingWiki: '', // 操作手册地址
    ruleUrl: '', // 规则地址
    ruleSetUrl: '', // 规则集地址
    topSqlUrl: '', // topsql地址
    auditConfigUrl: '', // audit_config_url地址
    dsmUrl: '' // 数据源地址
  },
  mutations: {
    setVersion(state, version = '') {
      state.version = version;
    },
    setIsFirstLogin(state, isFirstLogin = '') {
      state.isFirstLogin = isFirstLogin;
    },
    setHasSubscribe(state, hasSubscribe = '') {
      state.hasSubscribe = hasSubscribe;
    },
    setCloseForever(state, closeForever = {}) {
      state.closeForever = closeForever.closeForever;
    },
    setPermissionManagement(state, permissionManagement = '') {
      state.permissionManagement = permissionManagement
    },
    setSqlToolSwitch(state, sqlToolSwitch = '') {
      state.sqlToolSwitch = sqlToolSwitch
    },
    setCybrearkSwitch(state, cybrearkSwitch = '') {
      state.cybrearkSwitch = cybrearkSwitch
    },
    setReviewProcedureSwitch(state, reviewProcedureSwitch = '') {
      state.reviewProcedureSwitch = reviewProcedureSwitch
    },
    setDmlSwitch(state, dmlSwitch = '') {
      state.dmlSwitch = dmlSwitch
    },
    setIsThirdLogin(state, isThirdLogin = null) {
      state.isThirdLogin = isThirdLogin
    },
    setSqlresolverUrl(state, sqlresolverUrl = '') {
      state.sqlresolverUrl = sqlresolverUrl
    },
    setDsmUrl(state, dsmUrl = '') {
      state.dsmUrl = dsmUrl
    },
    setOperatingWiki(state, operatingWiki = '') {
      state.operatingWiki = operatingWiki
    },
    setRuleUrl(state, ruleUrl = '') {
      state.ruleUrl = ruleUrl
    },
    setRuleSetUrl(state, ruleSetUrl = '') {
      state.ruleSetUrl = ruleSetUrl
    },
    setTopSqlUrl(state, topSqlUrl = '') {
      state.topSqlUrl = topSqlUrl
    },
    setAuditConfigUrl(state, auditConfigUrl = '') {
      state.auditConfigUrl = auditConfigUrl
    },
    setMetaUrl(state, metaUrl = '') {
      state.metaUrl = metaUrl
    }
  }
};
