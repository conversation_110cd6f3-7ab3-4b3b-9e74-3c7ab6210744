<template>
  <div>
    <div class="part-1 sql-list-item review-wraper" :id="`sql-item-${sqlMap.id}`">
      <div class="review-wraper2">
        <!-- 有历史记录 -->
        <div v-if="sqlMap.compare_sql_text" class="redo">
          <!-- <code-mirror
            :orig="item.sql_text"
            :origTitle="`${formatTitle[item.sql_format]}当前记录`"
            :value="item.compare_sql_text"
            :valueTitle="`${formatTitle[item.sql_format]}历史记录`"
            :format="item.sql_format"
          />-->
          <Diff :list="diffList" />
        </div>
        <!-- 没有历史记录 -->
        <div v-else-if="sqlMap.sql_text" class="redo">
          <Diff :list="diffList" :isShowSingleWhenNoDiff="true" />
          <!-- <div>
            <h4>
              <a-icon type="exception" />
              {{formatTitle[item.sql_format]}}当前记录
            </h4>
          </div>-->
          <!-- <div> -->
          <!-- <Coder
              ref="coder"
              :value="item.sql_text"
              :type="item.sql_format"
              height="auto"
              :options="{ theme: 'default', readOnly: true }"
          />-->
          <!-- <pre v-highlight><code>{{ item.sql_text }}</code></pre> -->
          <!-- </div> -->
        </div>
        <div v-else class="ai-comment-part">暂无数据</div>
      </div>
      <div v-if="sqlMap.length === 1 && sqlMap.sql_format === 'sql'" class="ai-comment-part">暂无数据</div>
    </div>
  </div>
</template>

<script>
import Diff from '@/components/Diff';
import CodeMirror from '@/components/CodeMirror';
import Coder from '@/components/Coder';
export default {
  components: { CodeMirror, Coder, Diff },
  props: {
    detaliData: {
      type: Object,
      default: () => {}
    },
    formatTitle: {
      type: Object,
      default: () => {}
    },
    sqlMap: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    diffList() {
      const sqlList = this.sqlMap;
      const res = { oldStr: '', newStr: '', filename: '' };

      res.newStr = sqlList.compare_sql_text;
      res.oldStr = sqlList.sql_text;
      res.filename = sqlList.compare_sql_text
        ? `${this.formatTitle[sqlList.sql_format]}当前记录` +
          ' → ' +
          `${this.formatTitle[sqlList.sql_format]}历史记录`
        : `${this.formatTitle[sqlList.sql_format]}当前记录`;

      const list = [
        {
          filename: res.filename,
          oldStr: res.oldStr || '',
          newStr: res.newStr || res.oldStr,
          type: this.detaliData.sql_format,
          keywords: this.detaliData.java_sign
        }
      ];
      return list;
    }
  },
  mounted() {
    this.highlightLineFn(this.detaliData);
  },
  methods: {
    highlightLineFn(data) {
      const doc = _.get(this.$refs, 'coder.0.coder.doc');
      const signArr = (data.java_sign || []).filter(item => item);
      if (doc) {
        doc.eachLine(line => {
          if (line.text && signArr.find(item => line.text.includes(item))) {
            doc.addLineClass(line, '', 'highlight-line');
          }
        });
      }
    }
  },
  watch: {
    detaliData: {
      handler(newVal) {
        // Coder 组件是否存在取决于v-else-if="item.sql_text"
        // 这个item来自props透传过来的sql_list
        // 所以这里加 this.$nextTick 防止Coder组件没有挂载上
        this.$nextTick(() => {
          this.highlightLineFn(newVal);
        });
      }
      // immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
@import './commonClass.less';
/deep/ .highlight-line {
  background: @primary-2;
}
/deep/ .custom-coder .CodeMirror {
  background: rgb(236, 244, 254);
}
.redo {
  margin-top: -24px;
}
.part-1 {
  padding: 0 !important;
}
</style>