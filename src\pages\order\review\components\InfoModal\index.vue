<template>
  <a-modal
    v-model="visible"
    width="60%"
    title="统计信息"
    :maskClosable="false"
    :mask="false"
    :footer="null"
    wrapClassName="review-total-info-modal"
    :maskStyle="{ 'pointerEvents': 'none' }"
    @cancel="onCancel"
  >
    <a-spin :spinning="loading">
      <a-tabs default-active-key="table">
        <a-tab-pane key="table" tab="表信息">
          <Table v-bind="tableParams" :dataSource="tableData" bordered></Table>
        </a-tab-pane>
        <a-tab-pane key="index" tab="索引信息">
          <Table v-bind="indexParams" :dataSource="indexData" bordered></Table>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </a-modal>
</template>

<script>
import Table from '@/components/Table';
import { getTableInfo } from '@/api/review';
import config from './config';

export default {
  components: { Table },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      loading: false,
      tableParams: {
        columns: this.config.tableColumns,
        pagination: false,
        rowKey: 'id'
      },
      tableData: [],
      indexParams: {
        columns: this.config.indexColumns(),
        pagination: false,
        rowKey: 'id'
      },
      indexData: []
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(record = {}) {
      this.visible = true;

      // 发起请求
      this.loading = true;
      ReqUtil.req({
        ctx: this,
        reqInstance: getTableInfo,
        params: {
          detail_id: record.id
        },
        needLoading: false,
        cbk: data => {
          data = data || {};
          this.tableData = (data.table_list || []).map((item, index) => ({
            ...item,
            id: index
          }));
          this.indexData = (data.index_list || []).map((item, index) => ({
            ...item,
            id: index
          }));
          // 处理索引合并
          this.combineIndexTable(this.indexData);
        },
        err: res => {
          this.tableData = [];
          this.indexData = [];
        }
      })
        .then(() => {
          this.$hideLoading({ duration: 0 });
          this.loading = false;
        })
        .catch(e => {
          this.loading = false;
        });
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    combineIndexTable(data) {
      const combineColumns = ['table_name', 'index_name'];
      let map = { table_name: {}, index_name: {} };
      data.forEach((item, index) => {
        combineColumns.forEach(key => {
          let colVal = item[key];
          let colMap = map[key];
          // console.log(colVal, colMap[colVal], index);
          if (colVal) {
            let uid = colVal + '_' + index;
            colMap[uid] = {
              index,
              rowSpan: 1
            };
            // 和前面值相同
            if (index > 0 && data[index - 1][key] == colVal) {
              colMap[uid].rowSpan = 0;
              let pid = colVal + '_' + (index - 1);
              if (!colMap[pid].parent) {
                colMap[pid].rowSpan += 1;
                colMap[uid].parent = colMap[pid];
              } else {
                colMap[pid].parent.rowSpan += 1;
                colMap[uid].parent = colMap[pid].parent;
              }
            }
          }
        });
      });

      console.log(map, 8989);
      this.$set(
        this.indexParams,
        'columns',
        this.config.indexColumns({ combineInfo: map })
      );
    }
  }
};
</script>

<style lang="less">
.review-total-info-modal {
  pointer-events: none;

  .ant-modal-header {
    cursor: move;
  }
  .ant-modal-content {
    position: absolute;
    width: 100%;
  }
}
</style>
