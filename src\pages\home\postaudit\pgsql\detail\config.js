export default function (ctx) {
  const columns = [
    {
      title: '审核结果',
      dataIndex: 'ai_status',
      key: 'ai_status',
      scopedSlots: { customRender: 'ai_status' },
      width: 150
    },
    {
      title: 'SQL文本',
      dataIndex: 'query',
      key: 'query',
      scopedSlots: { customRender: 'query' },
      width: 200
    },
    {
      title: '用户名',
      key: 'username',
      dataIndex: 'username',
      scopedSlots: { customRender: 'username' },
      width: 180
    },
    {
      title: '数据库名',
      key: 'db_name',
      dataIndex: 'db_name',
      scopedSlots: { customRender: 'db_name' },
      width: 200
    },
    {
      title: '执行次数',
      dataIndex: 'calls',
      key: 'calls',
      sorter: true,
      scopedSlots: { customRender: 'calls' },
      width: 150
    },
    {
      title: '更新时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true,
      scopedSlots: { customRender: 'created_at' },
      width: 150
    },
    {
      title: 'SQLID',
      key: 'query_id',
      dataIndex: 'query_id',
      scopedSlots: { customRender: 'query_id' },
      width: 180
    },
    {
      title: '总耗时',
      key: 'total_exec_time',
      dataIndex: 'total_exec_time',
      sorter: true,
      width: 180
    },
    {
      title: '平均耗时',
      dataIndex: 'mean_plan_time',
      key: 'mean_plan_time',
      sorter: true,
      width: 300
    },
    {
      title: '读IO耗时',
      dataIndex: 'blk_read_time',
      key: 'blk_read_time',
      sorter: true,
      width: 300
    },
    {
      title: '写IO耗时',
      dataIndex: 'blk_write_time',
      key: 'blk_write_time',
      sorter: true,
      width: 300
    },
    {
      title: '单次最长时间',
      dataIndex: 'max_exec_time',
      key: 'max_exec_time',
      sorter: true,
      width: 300
    },
    {
      title: '单次最短时间',
      dataIndex: 'min_exec_time',
      key: 'min_exec_time',
      sorter: true,
      width: 150
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 80,
      fixed: 'right'
    }
  ].map(item => {
    return {
      ...item,
      width: undefined
    }
  });
  const fields = [
    {
      type: 'Select',
      label: '审核结果',
      key: 'ai_status',
      props: {
        options: [
          {
            label: '未知',
            value: 0
          },
          {
            label: '通过',
            value: 1
          },
          {
            label: '未通过',
            value: -1
          },
          {
            label: '白名单通过',
            value: 2
          },
          {
            label: '错误',
            value: 9
          }

        ]
      }
    },
    {
      type: 'Input',
      label: '用户名',
      key: 'username'
    },
    {
      type: 'Input',
      label: '数据库名',
      key: 'db_name'
    },
    {
      type: 'Input',
      label: 'SQL文本',
      key: 'query'
    }
  ];
  return {
    columns,
    searchFields: fields
  };
}
