<template>
  <div class="container">
    <a-spin class="spin" :spinning="headerInfoLoading">
      <a-card class="common-pure-card box-card" :bordered="false">
        <div class="container-title">
          <!-- <div class="title">BU标识：陆金所</div> -->
          <div class="title"></div>
          <div class="detail" @click="view">
            <a>详情</a>
          </div>
        </div>
        <div class="rule-data">
          <div class="info-title">
            应用数量
            <div class="number">{{headerInfoData.project_count}}</div>
          </div>
          <div class="info-title">
            发起SQL条数
            <div class="number">{{headerInfoData.detail_count}}</div>
          </div>
          <div class="info-title">
            AI识别率
            <div class="number">{{headerInfoData.ai_review_count}}</div>
          </div>
          <div class="info-title">
            自动通过率
            <div class="number">{{headerInfoData.ai_pass_count}}</div>
          </div>
          <div class="info-title">
            人工审核
            <div class="number">{{headerInfoData.dba_review_count}}</div>
          </div>
        </div>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
export default {
  props: {
    headerInfoData: {
      type: Object,
      default: () => {}
    },
    headerInfoLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  methods: {
    // 查看详情
    view() {
      this.$router.push({
        name: 'reportDetail'
      });
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  flex: 1;
  .common-pure-card {
    height: 412px;
    .container-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
      }
    }
    .rule-data {
      padding-top: 40px;
      height: 260px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      .info-title {
        width: 33.33%;
        height: 72px;
        font-size: 16px;
        color: #262629;
        font-weight: 400;
        .number {
          font-size: 24px;
          color: #5964ff;
          font-weight: 600;
          margin-top: 12px;
        }
      }
    }
  }
}
</style>
