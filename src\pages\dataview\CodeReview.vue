<template>
  <!-- 代码审核 -->
  <div class="half-block small-size">
    <a-skeleton :loading="loading" active class="code-review">
      <div class="list-view-area">
        <div class="title">代码审核</div>
        <div class="content" v-for="(value, key) in codeReviewData" :key="key">
          <a-popover
            v-if="value.chart_list && value.chart_list.length > 0"
            overlayClassName="data-view-code-review"
          >
            <template slot="content">
              <Chart
                :option="getOption(key, value.chart_list, timeRange)"
                ref="line"
              />
            </template>
            <div class="label">
              <span :class="riskClass[key]" v-if="isRiskTagFn(key)"
                >{{ riskTag[key] }}
              </span>
              {{ riskMap[key] }} {{ isRiskTagFn(key) ? 'SQL' : '' }}
            </div>
            <div :class="['value', riskClass[key]]">
              {{ value.total || 0 }}
              <custom-icon
                :type="getIcon(value.tendency)"
                v-if="value.tendency"
              />
            </div>
          </a-popover>
          <div v-if="!value.chart_list">
            <div class="label">
              <span :class="riskClass[key]" v-if="isRiskTagFn(key)"
                >{{ riskTag[key] }}
              </span>
              {{ riskMap[key] }} {{ isRiskTagFn(key) ? 'SQL' : '' }}
            </div>
            <div :class="['value', riskClass[key]]">
              {{ value.total || 0 }} <custom-icon type="up" />
            </div>
          </div>
        </div>
      </div>
      <div class="chart-view-area" v-if="projectType == 'group'">
        <Chart :option="option" ref="bar" />
        <!-- <div class="header-info">
          <span><span class="high-risk"></span>高风险</span>
          <span><span class="lower-risk"></span>低风险</span>
          <span><span class="modifying"></span>整改中</span>
          <span><span class="white-list"></span>白名单</span>
        </div> -->
        <!-- <div
          :class="['lager-chart-view', `col-${barData.length}`]"
          v-if="barData.length < 4 && barData.length > 0"
        >
          <div v-for="(item, index) in barData" :key="index">
            <a-tooltip>
              <template slot="title">
                <div v-for="(elem, index) in item.options" :key="index">
                  <div>{{ riskMap[elem.name] }}: {{ elem.value }}</div>
                </div>
              </template>
              <span>{{ item.projectName }}</span>
              <Chart :option="handleItem(item.options)" ref="bar" />
            </a-tooltip>
          </div>
        </div> -->
        <!-- <div
          :class="['lager-chart-view', `col-${barData.length}`]"
          v-if="barData.length >= 4"
        >
          <div v-for="(item, index) in barData.slice(0, 4)" :key="index">
            <a-tooltip>
              <template slot="title">
                <div v-for="(elem, index) in item.options" :key="index">
                  <div>{{ riskMap[elem.name] }}: {{ elem.value }}</div>
                </div>
              </template>
              <span>{{ item.projectName }}</span>
              <Chart :option="handleItem(item.options)" ref="bar" />
            </a-tooltip>
          </div>
        </div> -->
        <!-- <div
          :class="['small-chart-view', `col-${barData.length}`]"
          v-if="barData.length >= 4"
        >
          <div
            v-for="(item, index) in barData.slice(4, barData.length)"
            :key="index"
          >
            <a-tooltip>
              <template slot="title">
                <div v-for="(elem, index) in item.options" :key="index">
                  <div>{{ riskMap[elem.name] }}: {{ elem.value }}</div>
                </div>
              </template>
              <span>{{ item.projectName }}</span>
              <Chart :option="handleItem(item.options)" ref="bar" />
            </a-tooltip>
          </div>
        </div> -->
        <!-- <Empty v-if="barData.length <= 0" /> -->
      </div>
      <div class="single-chart-view-area" v-if="projectType == 'project'">
        <div class="project-name">项目：{{ projectName }}</div>
        <div class="chart-container">
          <Chart :option="codeReviewOption" ref="pie" />
        </div>
      </div>
    </a-skeleton>
  </div>
</template>

<script>
import Chart from '@/components/Chart';
import Empty from './Empty';
import config from './config';
export default {
  components: { Chart, Empty },
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    codeReviewData: {
      type: Object,
      default: () => {}
    },
    barData: Array,
    timeRange: {
      type: Array,
      default: () => []
    },
    riskClass: {
      type: Object,
      default: () => {}
    },
    riskMap: {
      type: Object,
      default: () => {}
    },
    riskTag: {
      type: Object,
      default: () => {}
    },
    codeReviewOption: {
      type: Object,
      default: () => {}
    },
    projectType: String,
    projectName: String
  },
  data() {
    this.config = config(this);
    return {
      option: {},
      lineOption: this.config.popoverOption(),
      barOption: this.config.barOption([
        { value: 6, itemStyle: { color: '#FF7875' } },
        { value: 7, itemStyle: { color: '#FAAD14' } },
        { value: 8, itemStyle: { color: '#4096FF' } },
        { value: 9, itemStyle: { color: '#52C41A' } }
      ]),
      pieOption: this.config.codeReviewOption()
    };
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    handleItem(item) {
      return this.config.barOption(item);
    },
    isRiskTagFn(key) {
      const bool = [
        'high_risk',
        'lower_risk',
        'no_risk',
        'modifying_sql',
        'whitelist_sql'
      ].includes(key);
      return bool;
    },
    getOption(key, data, time) {
      const text = this.riskMap[key];
      const extraText = this.isRiskTagFn(key) ? 'SQL' : '';
      const title = text + extraText;
      return this.config.popoverOption(title, data, time);
    },
    getIcon(val) {
      let icon = '';
      if (val > 0) {
        icon = 'caret-up';
      }

      if (val < 0) {
        icon = 'caret-down';
      }
      return icon;
    }
  },
  watch: {
    barData: {
      handler(newVal) {
        let names = [];
        let highRisk = [];
        let lowerRisk = [];
        let noRisk = [];
        let modifyingSql = [];
        let whitelistSql = [];
        newVal.forEach(item => {
          names.push(item.project_name);
          item.risk_value.forEach(el => {
            if (el.name == 'high_risk') {
              highRisk.push(el);
            }
            if (el.name == 'lower_risk') {
              lowerRisk.push(el);
            }
            if (el.name == 'no_risk') {
              noRisk.push(el);
            }
            if (el.name == 'modifying_sql') {
              modifyingSql.push(el);
            }
            if (el.name == 'whitelist_sql') {
              whitelistSql.push(el);
            }
          });
        });
        const data = {
          high_risk: highRisk,
          lower_risk: lowerRisk,
          no_risk: noRisk,
          modifying_sql: modifyingSql,
          whitelist_sql: whitelistSql
        };
        this.$set(this, 'option', this.config.option(data, names));
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.half-block {
  height: 420px;
  width: calc(50% + 0px);
  border-radius: 8px;
  background: #ffffff;
  margin-right: 16px;
  padding: 16px 24px;
  box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
  .code-review {
    display: flex;
    justify-content: space-between;
    .list-view-area {
      width: 48%;
      .title {
        font-size: 16px;
        color: #1f1f1f;
        padding-bottom: 16px;
        font-weight: bold;
      }
      .content {
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        &:last-child {
          border-bottom: none;
        }
        &:hover {
          background: rgb(229, 243, 254);
        }
        > span,
        > div {
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 16px 8px;
        }

        .label {
          span {
            display: inline-block;
            margin-right: 8px;
            width: 22px;
            height: 22px;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
            line-height: 20px;
          }
          .high-risk {
            color: #f5222d;
            border: 1px solid #ffa39e;
            background: #fff1f0;
          }
          .lower-risk {
            color: #faad14;
            border: 1px solid #ffe58f;
            background: #fffbe6;
          }
          .no-risk {
            color: #52c41a;
            border: 1px solid #b7eb8f;
            background: #f6ffed;
          }
          .modifying,
          .white-list {
            color: rgba(0, 0, 0, 0.85);
            background: #fafafa;
            border: 1px solid #d9d9d9;
          }
        }
        .value {
          font-size: 20px;
          color: #1f1f1f;
          font-weight: 500;
          .anticon {
            font-size: 14px;
            font-weight: 400;
            margin-left: 4px;
          }
          .anticon-caret-up {
            color: #f5222d;
          }
          .anticon-caret-down {
            color: #52c41a;
          }
          .anticon-up {
            opacity: 0;
          }
          &.high-risk,
          &.lower-risk,
          &.no-risk,
          &.modifying,
          &.white-list {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .chart-view-area {
      width: 50%;
      .header-info {
        display: flex;
        justify-content: flex-end;
        padding-bottom: 16px;
        span {
          margin-left: 8px;
          display: flex;
          align-items: center;
          span {
            display: inline-block;
            margin-right: 8px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
          }
          .high-risk {
            background: #f5222d;
          }
          .lower-risk {
            background: #ffc53d;
          }
          .no-risk {
            color: #52c41a;
            border: 1px solid #b7eb8f;
            background: #f6ffed;
          }
          .modifying {
            background: #4096ff;
          }
          .white-list {
            background: #52c41a;
          }
        }
      }
      .lager-chart-view {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        > div {
          width: 48%;
          height: 120px;
          // margin-bottom: 16px;
          padding: 11px;
          &:hover {
            border-radius: 8px;
            background: #ffffff;
            box-shadow: 0px 2px 7px 0px rgba(0, 0, 0, 0.15);
          }
        }
        &.col-1,
        &.col-2 {
          > div {
            width: 100%;
            height: 175px;
          }
        }
        &.col-3 {
          > div {
            width: 100%;
            height: 118px;
          }
        }
        &.col-4 {
          > div {
            width: 48%;
            height: 175px;
          }
        }
        &.col-5,
        &.col-6 {
          > div {
            width: 48%;
            height: 118px;
          }
        }
      }
      .small-chart-view {
        display: flex;
        justify-content: space-between;
        > div {
          padding: 11px;
          width: 24%;
          height: 120px;
          &:hover {
            border-radius: 8px;
            background: #ffffff;
            box-shadow: 0px 2px 7px 0px rgba(0, 0, 0, 0.15);
          }
        }
        &.col-5 {
          > div {
            width: 100%;
            height: 118px;
          }
        }
        &.col-6 {
          > div {
            width: 48%;
            height: 118px;
          }
        }
        &.col-7 {
          > div {
            width: 33%;
          }
        }
        &.col-8 {
          > div {
            width: 24%;
          }
        }
      }
      .ant-empty {
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-content: center;
        height: 400px;
      }
    }
    .single-chart-view-area {
      width: 48%;
      display: flex;
      flex-direction: column;
      align-items: center;
      .project-name {
        width: 100%;
        border-bottom: 1px solid #e8e8e8;
        text-align: left;
        padding: 12px 0 12px 0;
      }
      .chart-container {
        // height: 100%;
        // width: 100%;
        height: 340px;
        width: 250px;
      }
    }
  }
}
</style>
<style lang="less">
// popover美化
.ant-popover {
  &.data-view-code-review {
    .ant-popover-inner {
      .ant-popover-inner-content {
        width: 600px;
        height: 300px;
      }
    }
  }
}
</style>