<template>
  <div class="login-container">
    <div class="content-box">
      <div class="center_aisql_icon">
        <img
          v-if="isTaiLong"
          class="left_top_icon"
          src="~@/assets/img/private/login/tailong_icon.png"
        />
        <img
          v-else-if="isBiGuiYuan"
          class="left_top_icon"
          src="~@/assets/img/private/login/logo-bgy-aisql.svg"
        />
        <!-- <img class="left_top_icon" src="~@/assets/img/private/login/tailong_icon.png" /> -->
        <img v-else class="left_top_icon" src="~@/assets/img/private/login/AISQL-logo.svg" />
        <img class="center_aisql" src="~@/assets/img/private/login/center.png" />
      </div>
      <div class="content">
        <div class="login">
          <div class="top_circle"></div>
          <div class="bottom_circle"></div>
          <div class="login-title-box">
            <!-- <span class="login-title-img"></span> -->
            <span class="login-title-text">数据库代码审核平台</span>
          </div>
          <div class="login-form">
            <a-form @submit="onSubmit" :form="form">
              <a-alert
                type="error"
                :closable="false"
                v-show="error"
                :message="error"
                showIcon
                style="margin-bottom: 24px"
              />
              <a-form-item>
                <a-input
                  placeholder="请输入用户名"
                  v-decorator="[
                  'username',
                  { rules: [{ required: true, message: '请输入用户名' }] }
                ]"
                />
              </a-form-item>
              <a-form-item>
                <a-input
                  placeholder="请输入密码"
                  type="password"
                  v-decorator="[
                  'password',
                  { rules: [{ required: true, message: '请输入密码' }] }
                ]"
                />
              </a-form-item>
              <!-- <a-checkbox
              v-decorator="[
                'remember',
                {
                  valuePropName: 'checked',
                  initialValue: true,
                },
              ]"
              @change="onChange"
              >记住密码</a-checkbox>-->
              <a-form-item>
                <!-- type="primary" -->
                <a-button html-type="submit" :loading="loading" @click="onSubmit">登录</a-button>
                <div v-if="errlogging" class="err">{{ errMessage }}</div>
              </a-form-item>
            </a-form>
          </div>
        </div>
      </div>
    </div>

    <div class="conner_left_top">
      <img src="~@/assets/img/private/login/conner_left_top.png" alt />
    </div>
    <div class="conner_right_top">
      <img src="~@/assets/img/private/login/conner_right_top.png" alt />
    </div>
    <div class="conner_left_bottom">
      <img src="~@/assets/img/private/login/conner_left_bottom.png" alt />
    </div>
    <div class="conner_right_bottom">
      <img src="~@/assets/img/private/login/conner_right_bottom.png" alt />
    </div>

    <ActivationCode ref="activity"></ActivationCode>
  </div>
</template>

<script>
// import GlobalFooter from '../../layouts/GlobalFooter'
import { getLogin } from '@/api/login';
import Config from '@/utils/config';
import ActivationCode from '@/components/Biz/ActivationCode';
import Cookie from 'js-cookie';
import _ from 'lodash';
import { Base64 } from 'js-base64';

export default {
  name: 'Login',
  components: { ActivationCode },
  data() {
    return {
      form: this.$form.createForm(this, {}),
      errlogging: false,
      error: '',
      errMessage: '',
      loading: false
    };
  },
  computed: {
    systemName() {
      return 'bettle';
    },
    linkList() {
      return this.$store.state.setting.footerLinks;
    },
    copyright() {
      return this.$store.state.setting.copyright;
    },
    isTaiLong() {
      const specificConfig = process.channel === 'TaiLongBank';
      return specificConfig;
    },
    isBiGuiYuan() {
      const specificConfig = process.channel === 'BiGuiYuan';
      return specificConfig;
    }
  },
  mounted() {
    const isActivation = sessionStorage.getItem('isActivation');
    if (isActivation) {
      this.$refs.activity.show();
    }
  },
  methods: {
    onSubmit(e) {
      e.preventDefault();
      const channelConfig = window.CHANNEL_INFO;
      const homePath = channelConfig.home;
      this.form.validateFields((err, values) => {
        if (!err) {
          // this.logging = true
          // // console.log(this.form)
          // this.$axios.post('/login', {
          //   name: this.form.getFieldValue('name'),
          //   password: this.form.getFieldValue('password')
          // }).then((res) => {
          //   this.logging = false
          //   const result = res.data
          //   if (result.code >= 0) {
          //     const user = result.data.user
          //     this.$router.push('/batch/list')
          //     this.$store.commit('account/setuser', user)
          //     // this.$message.success(result.message, 3)
          //   } else {
          //     this.error = result.message
          //   }
          // })
          // console.log(this.form.getFieldValue('username'));
          // this.$showLoading()
          this.loading = true;
          getLogin({
            user_name: this.form.getFieldValue('username'),
            password: Base64.encode(this.form.getFieldValue('password') || '')
          }).then(res => {
            const result = res.data || {};
            const code = result.code;
            if (code === 0) {
              this.errlogging = false;
              // let lastUserName = _.get(
              //   this.$store.state.account,
              //   'user.lastUserName'
              // );
              // let isOther =
              //   lastUserName &&
              //   _.get(res, 'data.data.user.name') !== lastUserName;
              // const role = result.data.user.role;
              // this.$store.commit(
              //   'account/setUser',
              //   _.get(res, 'data.data.user')
              // );
              Cookie.set(Config.TokenKey, _.get(res, 'data.data.token'));
              // if (role === 'dba' || role === 'leader') {
              //   this.$router.push({ name: 'orderList' });
              // } else {
              //   this.$router.push({ name: 'data-view' });
              // }
              // const loginTarget = sessionStorage.getItem('login_target');
              // this.$router.push({
              //   path:
              //     !isOther && loginTarget != '/login'
              //       ? loginTarget || homePath
              //       : homePath
              //   // path: sessionStorage.getItem('login_target') || homePath
              // });
              this.$router.push({ path: homePath });
              this.loading = false;
              // this.$hideLoading({ tips: _.get(res, 'data.message') });
            } else if (code == '10001') {
              this.loading = false;
              this.errlogging = true;
              this.errMessage = result.message;
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
              const isActivation = sessionStorage.getItem('isActivation');
              if (isActivation) {
                this.$refs.activity.show();
              }
            } else {
              this.loading = false;
              this.errlogging = true;
              this.errMessage = result.message;
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          });
        }
      });
    },
    onChange(e) {
      const username = this.form.getFieldValue('username');
      const password = this.form.getFieldValue('password');
      console.log(e, username, password);
    }
  }
};
</script>

<style lang="less" scoped>
@assetsUrl: '~@/assets';
.login-container {
  // position: relative;
  // display: flex;
  // flex-direction: column;
  height: 100%;
  // min-width: 1366px;
  overflow: auto;
  // background: #f0f2f5 url('@{assetsUrl}/img/private/login/login_bg.png')
  //   no-repeat center;
  // background-size: 100% 100%;
  background: #0a013c;
  display: flex;
  align-items: center;

  .content-box {
    width: 100%;
    display: flex;
    max-height: 80%;
    align-items: center;
    .content {
      // flex: 1;
      display: flex;
      justify-content: flex-end;
      // align-self: center;
      // width: 60%;
      // margin-top: 120px;
      .login {
        margin: 0 200px 0 100px;
        width: 100%;
        min-width: 420px;
        min-height: 486px;
        position: relative;
        border-radius: 2%;
        background: rgba(48, 118, 249, 0.15);
        padding: 0 50px;
        z-index: 999;
        .top_circle {
          position: absolute;
          top: -156px;
          left: -104px;
          width: 278px;
          height: 278px;
          background: url('@{assetsUrl}/img/private/login/top_circle.png')
            no-repeat center;
        }
        .bottom_circle {
          position: absolute;
          bottom: -110px;
          right: 26px;
          width: 254px;
          height: 264px;
          background: url('@{assetsUrl}/img/private/login/bottom_circle.png')
            no-repeat center;
        }
        .login-form {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width: 80%;
          .ant-form-item {
            margin-bottom: 40px;
            .ant-input {
              height: 40px;
              border: 1px solid rgba(255, 255, 255, 0.2);
              border-radius: 2px;
              background: inherit;
              font-family: PingFangSC-Regular;
              font-size: 16px;
              color: rgba(255, 255, 255, 0.65);
              line-height: 24px;
              font-weight: 400;
            }
          }
          .ant-form-item:nth-child(4) {
            height: 48px;
          }
          .ant-checkbox-wrapper {
            margin-bottom: 40px;
          }
          .ant-btn {
            border: none;
            width: 100%;
            height: 48px;
            font-size: 16px;
            border-radius: 4px;
            background-image: linear-gradient(153deg, #3facff 0%, #69ffbd 100%);
            font-family: PingFangSC-Regular;
            font-size: 20px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 28px;
            font-weight: 400;
          }
        }
        .login-title-box {
          text-align: center;
          margin-top: 67px;
          margin-bottom: 40px;
          height: 45px;
          line-height: 45px;
          .login-title-text {
            // font-family: PingFangSC-Medium;
            font-size: 32px;
            color: #1fe0a9;
            letter-spacing: 0;
            text-align: left;
            font-weight: 500;
          }
        }
        .icon {
          font-size: 24px;
          color: rgba(0, 0, 0, 0.2);
          margin-left: 16px;
          vertical-align: middle;
          cursor: pointer;
          transition: color 0.3s;

          &:hover {
            color: #1890ff;
          }
        }
      }
      .err {
        color: red;
        font-size: 12px;
      }
    }
    .center_aisql_icon {
      // width: 100%;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      .left_top_icon {
        // margin: 0 0 50px 140px;
        width: 200px;
        // min-width: 250px;
        position: fixed;
        left: 10%;
        top: 10%;
      }
      .center_aisql {
        width: 70%;
        min-width: 500px;
        margin: 0 0 0 130px;
      }
    }
  }
  .conner_left_top {
    position: absolute;
    top: 0;
    left: 0;
    width: 25%;
    img {
      width: 100%;
    }
  }
  .conner_right_top {
    position: absolute;
    top: 0;
    right: 0;
    width: 20%;
    img {
      width: 100%;
    }
  }
  .conner_left_bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 20%;
    img {
      width: 100%;
    }
  }
  .conner_right_bottom {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20%;
    img {
      width: 100%;
    }
  }
}
</style>
