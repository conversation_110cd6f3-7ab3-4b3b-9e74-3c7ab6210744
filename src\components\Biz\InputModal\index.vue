<template>
  <div class="input-modal">
    <a-input v-bind="inputProps" :value="value">
      <a-icon
        slot="suffix"
        class="suffix-icon"
        :type="suffixIcon"
        v-if="suffixIcon"
      />
    </a-input>
    <div class="input-modal-mask" @click="openModal"></div>
  </div>
</template>

<script>
// import common from '@/utils/common';
// import _ from 'lodash';
const defaultProps = {
  placeholder: '请选择'
};

export default {
  inheritAttrs: false,
  components: {},
  props: {
    eventName: {
      type: String,
      default: 'input-modal'
    },
    suffixIcon: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  },
  computed: {
    inputProps() {
      return { ...defaultProps, ...this.$attrs };
    }
  },
  created() {},
  mounted() {},
  methods: {
    openModal() {
      this.$bus.$emit(this.eventName, this.$attrs);
    }
  },
  watch: {
    value(newVal) {
      this.$emit('change');
    }
  }
};
</script>

<style lang="less" scoped>
.input-modal {
  position: relative;
  display: inline-block;
  .input-modal-mask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    cursor: pointer;
  }
  i {
    color: rgba(0, 0, 0, 0.25);
    font-size: 12px;
  }
}
</style>
