export default function(ctx) {
  const fields = [
    {
      type: 'DataBaseChoose',
      label: '实例名称',
      key: 'instance',
      props: {
        url: '/sqlreview/project/data_source_choices',
        reqParams: {},
        placeholder: '数据源',
        class: 'selected-children',
        loaded(data) {
          // ctx.dataSourceOption = data;
        },
        beforeLoaded(data) {
          return data.map(item => {
            return {
              ...item,
              instance_usage: item.env,
              showText: item.label + '(' + item.db_url + ')'
            };
          });
        },
        getPopupContainer: el => {
          return document.body;
        },
        mode: 'default',
        optionLabelProp: 'children',
        allowSearch: true,
        backSearch: true,
        limit: 50,
        allowClear: false
      },
      width: '300px'
    },
    {
      type: 'RangePicker',
      label: '执行时间',
      key: 'created_at',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      },
      width: '300px'
    }
  ];
  return {
    searchFields: fields
  };
}
