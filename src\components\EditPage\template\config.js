export default function (ctx) {
  const searchFields = [
    {
      type: 'Input',
      label: '工单单号',
      key: 'order_id'
    }
  ]
  const columns = [
    {
      title: '工单号',
      dataIndex: 'order_id',
      key: 'order_id'
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' }
    }
  ]
  const editConfig = {
    'xxxxxx': {
      type: 'Input',
      rules: [{ required: true, message: 'xxxxx', trigger: 'blur' }],
      props: {
        style: { width: '90%' }
      },
      plainText: true
    }
  }
  return {
    searchFields,
    columns,
    editConfig
  }
}