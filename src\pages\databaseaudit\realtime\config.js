export default function (ctx) {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      scopedSlots: { customRender: 'id' },
      width: 120
    },
    {
      title: 'USER',
      dataIndex: 'user',
      key: 'user',
      scopedSlots: { customRender: 'user' },
      width: 150
    },
    {
      title: 'HOST',
      key: 'host',
      dataIndex: 'host',
      scopedSlots: { customRender: 'host' }
      // width: 120
    },
    {
      title: 'INFO',
      key: 'info',
      dataIndex: 'info',
      scopedSlots: { customRender: 'info' }
      // width: 120
    },
    {
      title: 'TIME',
      dataIndex: 'time',
      key: 'time',
      width: 150
    },
    {
      title: 'STATE',
      key: 'state',
      dataIndex: 'state',
      width: 150,
      scopedSlots: { customRender: 'state' }
    },
    {
      title: 'DB',
      dataIndex: 'db',
      key: 'db',
      scopedSlots: { customRender: 'db' },
      width: 200
    },
    {
      title: 'COMMAND',
      key: 'command',
      dataIndex: 'command',
      width: 150,
      scopedSlots: { customRender: 'command' }
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      visible: $permissionBatch.some([
        { module: 'realTimeSql', values: ['kill'] }
      ]),
      width: 170,
      fixed: 'right'
    }
  ];
  const obColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      scopedSlots: { customRender: 'id' },
      width: 120
    },
    {
      title: 'USER',
      dataIndex: 'user',
      key: 'user',
      scopedSlots: { customRender: 'user' },
      width: 150
    },
    {
      title: 'DB',
      dataIndex: 'db',
      key: 'db',
      scopedSlots: { customRender: 'db' },
      width: 200
    },
    {
      title: 'COMMAND',
      key: 'command',
      dataIndex: 'command',
      width: 150,
      scopedSlots: { customRender: 'command' }
    },
    {
      title: 'HOST',
      key: 'host',
      dataIndex: 'host',
      scopedSlots: { customRender: 'host' }
      // width: 120
    },
    {
      title: 'INFO',
      key: 'info',
      dataIndex: 'info',
      scopedSlots: { customRender: 'info' }
      // width: 120
    },
    {
      title: 'sql_id',
      dataIndex: 'sql_id',
      key: 'sql_id',
      scopedSlots: { customRender: 'sql_id' },
      width: 220
    },
    {
      title: 'sql_port',
      dataIndex: 'sql_port',
      key: 'sql_port',
      scopedSlots: { customRender: 'sql_port' },
      width: 120
    },
    {
      title: 'ssl_cipher',
      dataIndex: 'ssl_cipher',
      key: 'ssl_cipher',
      scopedSlots: { customRender: 'ssl_cipher' },
      width: 120
    },
    {
      title: 'state',
      dataIndex: 'state',
      key: 'state',
      scopedSlots: { customRender: 'state' },
      width: 120
    },
    {
      title: 'svr_ip',
      dataIndex: 'svr_ip',
      key: 'svr_ip',
      scopedSlots: { customRender: 'svr_ip' },
      width: 200
    },
    {
      title: 'tenant',
      dataIndex: 'tenant',
      key: 'tenant',
      scopedSlots: { customRender: 'tenant' },
      width: 120
    },
    {
      title: 'thread_id',
      dataIndex: 'thread_id',
      key: 'thread_id',
      scopedSlots: { customRender: 'thread_id' },
      width: 120
    },
    {
      title: 'time',
      dataIndex: 'time',
      key: 'time',
      scopedSlots: { customRender: 'time' },
      width: 120
    },
    {
      title: 'total_time',
      dataIndex: 'total_time',
      key: 'total_time',
      scopedSlots: { customRender: 'total_time' },
      width: 120
    },
    {
      title: 'trace_id',
      dataIndex: 'trace_id',
      key: 'trace_id',
      scopedSlots: { customRender: 'trace_id' },
      width: 220
    },
    {
      title: 'trans_state',
      dataIndex: 'trans_state',
      key: 'trans_state',
      scopedSlots: { customRender: 'trans_state' },
      width: 120
    },
    {
      title: 'svr_port',
      dataIndex: 'svr_port',
      key: 'svr_port',
      scopedSlots: { customRender: 'svr_port' },
      width: 120
    },
    {
      title: 'proxy_sessid',
      dataIndex: 'proxy_sessid',
      key: 'proxy_sessid',
      scopedSlots: { customRender: 'proxy_sessid' },
      width: 120
    },
    {
      title: 'master_sessid',
      dataIndex: 'master_sessid',
      key: 'master_sessid',
      scopedSlots: { customRender: 'master_sessid' },
      width: 120
    },
    {
      title: 'retry_cnt',
      dataIndex: 'retry_cnt',
      key: 'retry_cnt',
      scopedSlots: { customRender: 'retry_cnt' },
      width: 120
    },
    {
      title: 'user_client_ip',
      dataIndex: 'user_client_ip',
      key: 'user_client_ip',
      scopedSlots: { customRender: 'user_client_ip' },
      width: 220
    },
    {
      title: 'user_host',
      key: 'user_host',
      dataIndex: 'user_host',
      scopedSlots: { customRender: 'user_host' }
      // width: 120
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 170,
      visible: $permissionBatch.some([
        { module: 'realTimeSql', values: ['kill'] }
      ]),
      fixed: 'right'
    }
  ];

  const chartColumns = [
    {
      title: '节点',
      dataIndex: 'inst_id',
      key: 'inst_id',
      scopedSlots: { customRender: 'inst_id' },
      width: 60
    },
    {
      title: 'Active Sessions',
      dataIndex: 'active_sessions',
      key: 'active_sessions',
      scopedSlots: { customRender: 'chart_render' },
      width: 120
    },
    {
      title: 'Running SQL',
      key: 'running_sql',
      dataIndex: 'running_sql',
      scopedSlots: { customRender: 'chart_render' },
      width: 120
    },
    {
      title: 'Lock Sessions',
      dataIndex: 'locked_sessions',
      key: 'locked_sessions',
      scopedSlots: { customRender: 'chart_render' },
      width: 120
    },
    {
      title: 'Lock Tables',
      dataIndex: 'table_locks',
      key: 'table_locks',
      scopedSlots: { customRender: 'chart_render' },
      width: 120
    },
    {
      title: 'Connections',
      dataIndex: 'connections',
      key: 'connections',
      scopedSlots: { customRender: 'chart_render' },
      width: 120
    },
    {
      title: 'IOwait Time(ms)',
      key: 'io_wait_time',
      dataIndex: 'io_wait_time',
      scopedSlots: { customRender: 'chart_render' },
      width: 120
    },
    {
      title: 'OS Load',
      key: 'os_load',
      dataIndex: 'os_load',
      scopedSlots: { customRender: 'chart_render' },
      width: 120
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const lineOption = (color1, color2, data = []) => {
    return {
      color: [color2],
      grid: {
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        containLabel: false
      },
      xAxis: {
        show: false,
        type: 'category',
        boundaryGap: false,
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        show: false,
        type: 'value'
      },
      series: [
        {
          data: [...data],
          type: 'line',
          smooth: true,
          showSymbol: false,
          areaStyle: {},
          lineStyle: {
            color: color1,
            width: 2
          }
        }
      ]
    };
  };
  const barOption = (data = [], num, dbType) => {
    return {
      color: ['#3E60C1'],
      grid: {
        top: '4%',
        bottom: '4%',
        left: '4%',
        right: '4%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        show: false
      },
      yAxis: [
        {
          type: 'category',
          data:
            dbType == 'DB2'
              ? ['READ', 'WRITE', 'DDL', 'OTHER']
              : ['Select', 'Insert', 'Update', 'Delete'],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#27272A',
            padding: [0, 8, 0, 0],
            overflow: 'break'
          },
          inverse: true
        },
        {
          type: 'category',
          inverse: true,
          axisLine: 'none',
          axisTick: 'none',
          show: true,
          axisLabel: {
            margin: 20,
            color: '#7F7F7F',
            fontSize: '12',
            formatter: (value) => value
          },
          data: data
        }
      ],
      series: [
        {
          data: data.map((item, index) => {
            return {
              value: item,
              itemStyle: {
                color: '#3E60C1',
                shadowBlur: 1,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            };
          }),
          type: 'bar',
          barMaxWidth: dbType == 'ORACLE' ? `${32 - 2 * num}%` : '30%',
          barWidth: 32,
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)'
          }
        }
      ]
    };
  };

  const obInfo = (type) => {
    return [
      {
        type: 'Input',
        label: '数据库审核账号',
        key: 'examine_user',
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'RadioGroup',
        label: ' ',
        className: 'hidden-label',
        key: 'examine_pwd_type',
        props: {
          mode: 'tips',
          class: 'inline',
          options: [
            {
              label: '密码',
              value: 0
            },
            {
              label: '密钥',
              value: 1
            }
          ]
        },
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              examine_pwd_type: value,
              examine_pwd: null
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      (formData = {}) => {
        const password = {
          type: 'InputPassword',
          label: '数据库审核密码',
          key: 'examine_pwd',
          className: 'password',
          props: { placeholder: '请输入' },
          rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
        };
        if (type === 'edit') {
          password.rules = [];
          password.props = {
            placeholder:
              formData.examine_pwd_type == 0
                ? '填写该项，会更新密码'
                : '密码为非必填'
          };
        }
        if (formData.examine_pwd_type == 1) {
          password.rules = [];
          password.props = {
            placeholder: '密码为非必填'
          };
        }
        return password;
      }
    ];
  };
  return {
    lineOption,
    barOption,
    columns,
    obInfo,
    obColumns,
    chartColumns
  };
}
