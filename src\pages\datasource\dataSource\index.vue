<template>
  <div class="config-datasource page-list-with-tabs">
    <div class="content-tabs">
      <iframe v-if="ludsm.lu_dsm_enable === true" style="width: 100%; height: 836px;" frameborder="0" :src="ludsm.lu_dsm_url"></iframe>
      <Tabs
        v-else
        v-model="activeKey"
        :tabsList="tabsList"
        :mode="'scale'"
        @change="onChange"
        :animated="false"
      >
        <InstanceTableList
          slot="instance"
          :activeKey="activeKey"
          ref="instance"
          @onChangeTab="onChange"
        ></InstanceTableList>
        <DatabaseTableList
          slot="database"
          :activeKey="activeKey"
          ref="database"
          :datasourceName="datasourceName"
        ></DatabaseTableList>
      </Tabs>
      <div v-if="ludsm.lu_dsm_enable !== true" class="frame-button-wrapper">
        <a-button
          slot="extra"
          class="highlight"
          icon="plus"
          v-if="isTaiLong"
          @click="pullDatasource"
        >拉取数据源</a-button>
        <!-- <a-button v-if="activeKey === 'database'" type="primary" @click="databaseSync">数据库同步</a-button> -->
        <a-button
          @click="toImportAndExmport"
          v-if="activeKey === 'instance'"
          class="highlight"
        >统计信息导入/导出</a-button>
        <a-button @click="instanceImport" v-if="activeKey === 'instance'" class="highlight">数据源导入</a-button>
        <!-- <a-button @click="batchAuth" v-if="!isTaiLong">+批量授权用户</a-button> -->
        <a-button v-if="activeKey === 'instance'" type="primary" @click="add" icon="plus">新增数据源</a-button>
      </div>
    </div>
  </div>
</template>
<script>
import DatabaseTableList from './DatabaseTableList';
import InstanceTableList from './InstanceTableList';
import Tabs from '@/components/Tabs';
import common from '@/utils/common';
import config from './config';

export default {
  name: 'data-source-config',
  components: { DatabaseTableList, InstanceTableList, Tabs },
  props: {},
  data() {
    this.config = config(this);
    const activeKey = _.get(this.$route, 'query.activeKey');
    // keep-alive是否激活
    this.activated = null;
    return {
      datasourceName: '',
      activeKey: activeKey || 'instance', // 默认选中 tab
      tabsList: [
        {
          tab: '数据源',
          key: 'instance'
        },
        {
          tab: '数据库',
          key: 'database'
        }
      ]
    };
  },
  computed: {
    // 泰隆银行特有
    isTaiLong() {
      const specificConfig = process.channel === 'TaiLongBank';
      return specificConfig;
    },
    ludsm() {
      return this.$store.state.account.ludsm || {};
    }
  },
  created() {},
  mounted() {
    this.setNavi();
  },
  activated() {
    if (this.activated === false) {
      const ref = this.$refs[this.activeKey];
      ref.$refs.table.refreshKeep();
      this.activated = true;
    }
  },
  deactivated() {
    this.activated = false;
  },
  methods: {
    instanceImport() {
      this.$router.push({ name: 'instance-import' });
    },
    addRules() {
      this.$router.push({ name: 'rules-config-add' });
    },
    // 切换 tab 选项卡
    onChange(activeKey, name) {
      this.activeKey = activeKey;
      this.datasourceName = name;
    },
    // 新增数据源
    add() {
      this.$refs.instance.addProject();
    },
    // 数据库同步
    databaseSync() {
      this.$refs.database.databaseSyncFn();
    },
    batchAuth() {
      const { instance, database } = this.$refs;
      this.activeKey == 'instance'
        ? instance.batchAuthShow()
        : database.batchAuthShow();
    },
    // 拉去数据源
    pullDatasource() {
      this.$refs.instance.pullDatasource();
    },
    // 去统计信息导入导出页面
    toImportAndExmport() {
      this.$router.push({
        name: 'import-and-export'
      });
    },
    setNavi() {
      const activeKey = _.get(this.$route, 'query.activeKey');
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'data-source') {
          activeKey
            ? (path = sourcePath + '?activeKey=' + 'database')
            : (path = sourcePath);
        }
        return path;
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.search-area {
  .form {
    .ant-form-item-label {
      max-width: 130px !important;
    }
  }
}
</style>
