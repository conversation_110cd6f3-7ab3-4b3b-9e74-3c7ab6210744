<template>
  <div :class="className"></div>
</template>

<script>
// import hljs from 'highlight.js';
// import 'highlight.js/styles/github.css';
// import 'highlight.js/styles/googlecode.css';
import { createPatch } from 'diff';
// import { html, parse } from 'diff2html';
import 'diff2html/bundles/css/diff2html.min.css';
import { Diff2HtmlUI } from 'diff2html/lib/ui/js/diff2html-ui';
import xmlFormatter from 'xml-formatter';
import jsonFormatter from 'json-beautify';
import sql from '@/utils/highlight/languages/sql.js';

export default {
  name: 'Diff',
  props: {
    dbType: String,
    /**
     * ListItem - diff数组每一项描述
     * @typedef {Object} ListItem
     * @property {String} [filename] - 头部名称，默认：original → modified
     * @property {String} oldStr - 左侧文案
     * @property {String} newStr - 右侧文案
     * @property {String} type - 文件类型
     * @property {Array} [keywords] - 按照特殊keywords标红行
     */
    list: {
      type: Array,
      default: () => []
    },
    options: {
      type: Object,
      default: () => ({})
    },
    format: Boolean,
    isShowWhenNoDiff: {
      type: Boolean,
      default: true
    },
    isShowSingleWhenNoDiff: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    list() {
      this.initUI();
    }
  },
  computed: {
    className() {
      let arr = ['custom-diff'];
      return arr;
    }
  },
  data(vm) {
    return {};
  },
  mounted() {
    this.initUI();
  },
  methods: {
    initUI() {
      const { list, options } = this;
      const resList = list;
      // 清空
      this.$el.innerHTML = '';

      // 获取对比str
      const diffStrArr = [];
      const patchArr = [];
      resList.forEach(item => {
        const { type } = item;
        let oldVal = this.formatVal(item.oldStr, type);
        let newVal = this.formatVal(item.newStr, type);
        if (this.isShowSingleWhenNoDiff && oldVal === newVal) {
          // 文案无差异，且isShowSingleWhenNoDiff = true，设置标识
          oldVal = oldVal + '\nsrc';
          newVal = newVal + '\ndst';
          item.showNoDiff = true;
          item.showSingle = true;
        } else if (this.isShowWhenNoDiff && oldVal === newVal) {
          // 文案无差异，且isShowWhenNoDiff = true，设置标识
          oldVal = oldVal + '\nsrc';
          newVal = newVal + '\ndst';
          item.showNoDiff = true;
        }
        let args = [
          item.filename || 'original → modified',
          oldVal,
          newVal,
          item.type ? `.${type}` : '',
          item.type ? `.${type}` : '',
          { context: Number.MAX_SAFE_INTEGER }
        ];
        const diffStr = createPatch(...args);
        if (oldVal != newVal) {
          diffStrArr.push(diffStr);
        }
        patchArr.push(diffStr);
      });
      // target
      const targetElement = this.$el;
      // config
      const needToggle = diffStrArr.length > 1;
      const configuration = {
        drawFileList: false,
        fileListToggle: needToggle,
        fileListStartVisible: true,
        fileContentToggle: needToggle,
        // matching: 'lines',
        outputFormat: 'side-by-side',
        synchronisedScroll: true,
        highlight: true,
        renderNothingWhenEmpty: false
      };
      // console.log(list, diffStrArr, patchArr);

      const diff2htmlUi = new Diff2HtmlUI(
        targetElement,
        diffStrArr.length <= 0 ? patchArr[0] : diffStrArr.join('\n'),
        Object.assign({}, configuration, options)
      );
      diff2htmlUi.hljs && diff2htmlUi.hljs.registerLanguage('sql', sql);
      diff2htmlUi.draw();
      diff2htmlUi.highlightCode();
      // console.log(diff2htmlUi, 'diff2htmlUi')
      // 设置filename
      this.$el.querySelectorAll('.d2h-file-name').forEach((node, index) => {
        const item = list[index];
        const filename =
          item && item.filename ? item.filename : 'original → modified';
        node.innerHTML = filename;
      });
      // 根据特殊标识，设置样式
      resList.forEach((item, index) => {
        const dom = this.$el.querySelector(
          `.d2h-wrapper .d2h-file-wrapper:nth-child(${index + 1})`
        );
        if (item.showNoDiff) {
          dom && dom.classList.add('show-no-diff');
        }
        if (item.showSingle) {
          dom && dom.classList.add('show-single');
        }
      });
      this.localList = resList;
      // 处理特殊高亮
      this.highlightLinesByKeyword();
    },
    formatVal(value, type) {
      let res = value;
      if (this.format) {
        try {
          switch (type) {
            case 'sql':
              let defaultOptions = {};
              if (this.dbType) {
                defaultOptions = FORMAT_CONFIG.SqlFormat.getConfig(this.dbType);
              }
              res = sqlFormatter.format(value, defaultOptions);
              break;
            case 'xml':
              res = value ? xmlFormatter(value) : '';
              break;
            case 'json':
              let jsonObj = value;
              if (_.isString(value) && value) {
                jsonObj = JSON.parse(value);
              }
              res = value ? jsonFormatter(jsonObj, null, 2, 20) : '';
              break;
            default:
              break;
          }
        } catch (e) {
          // this.$message.warning('格式化错误，请检查');
          console.log(e);
        }
      }

      return res;
    },
    refresh() {
      this.initUI();
    },
    highlightLinesByKeyword() {
      const sign = (lines, trs, keywords) => {
        for (let i = 0; i < trs.length; i++) {
          const tr = trs[i];
          if (tr) {
            const lineNum = tr.querySelector('.d2h-code-side-linenumber');
            if (lineNum) {
              const lineStr = lines[lineNum.innerText - 1];
              if (lineStr && keywords.find(key => lineStr.includes(key))) {
                tr.classList.add('hljs-line-by-keyword');
              }
            }
          }
        }
      };
      this.localList.forEach((item, index) => {
        const { oldStr, newStr, keywords } = item;
        const oldLines = oldStr.split('\n');
        const newLines = newStr.split('\n');

        const oldTrs = [
          ...this.$el.querySelectorAll(
            `.d2h-wrapper .d2h-file-wrapper:nth-child(${index +
              1}) .d2h-file-side-diff:first-child tbody tr`
          )
        ];
        const newTrs = [
          ...this.$el.querySelectorAll(
            `.d2h-wrapper .d2h-file-wrapper:nth-child(${index +
              1}) .d2h-file-side-diff:last-child tbody tr`
          )
        ];
        if (keywords && keywords.length > 0) {
          sign(oldLines, oldTrs, keywords);
          sign(newLines, newTrs, keywords);
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.custom-diff {
  /deep/ .d2h-wrapper {
    .d2h-file-wrapper {
      margin-bottom: 24px;

      &.show-no-diff {
        .d2h-file-side-diff {
          .d2h-diff-tbody {
            > tr:last-child {
              display: none;
            }
          }
        }
      }
      &.show-single {
        .d2h-file-side-diff {
          width: 100%;
          &:last-child {
            display: none;
          }
        }
      }
    }
    // header
    .d2h-file-header {
      .d2h-file-name {
        font-size: 13px;
        font-weight: 500;
      }
    }
    // content
    .d2h-files-diff {
      display: flex;
    }
    .d2h-files-diff.d2h-d-none {
      display: none;
    }
    .d2h-file-side-diff {
      margin-right: 0;

      .d2h-code-line-ctn {
        word-wrap: normal;
        background: none;
        display: inline-block;
        padding: 0;
        user-select: text;
        vertical-align: middle;
        white-space: pre;
        width: 100%;
      }
    }
    .d2h-tag {
      display: none;
    }
    td {
      white-space: normal;
    }
    tr.hljs-line-by-keyword {
      .d2h-code-line-ctn {
        // background: repeating-linear-gradient(
        //   45deg,
        //   rgba(68, 206, 246, 0.1),
        //   rgba(68, 206, 246, 0.3) 2px,
        //   white 5px,
        //   white 10px
        // );
        box-shadow: 0px 0px 4px 4px rgba(255, 0, 0, 0.22);
      }
    }
  }
}
</style>