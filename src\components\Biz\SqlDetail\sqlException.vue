<template>
  <div class="review-wraper">
    <!-- 第二部分：其他sql集合 -->
    <div
      :class="['part-2', 'review-wraper', part2Show && 'expanded']"
      v-show="sql_list.length >= 2"
    >
      <h4 @click="onExpand()">
        <a-icon type="exception" />
        <span>动态拼接信息</span>
        <!-- 折叠按钮 -->
        <custom-icon :class="{ 'icon-sql-expand': true, expanded: part2Show }" type="lu-icon-right"></custom-icon>
      </h4>
      <div
        class="sql-list-item"
        :id="`sql-item-${item.id}`"
        v-for="(item, idx) in sql_list"
        v-bind:key="item.id || `item-${idx}`"
      >
        <!-- 1. 根据 format: sql | xml 展示 -->
        <div
          :class="{
            'review-wraper2': true,
            'sql-detail-item': true,
            'custom-bg': true
          }"
        >
          <div>
            <div>
              <h4>
                <!-- <a-icon type="exception" />当前SQL信息 -->
                当前SQL信息
              </h4>
              <template v-if="item.ai_comment">
                <!-- <pre>{{item.ai_comment}}</pre> -->
                <pre v-for="(item, index) in item.ai_comment" :key="index" style="flex-grow: 1;">{{ item.ai_comment }}</pre>
                <!-- <a-divider style="margin: 0px;" /> -->
              </template>
              <sql-highlight :sql="item.sql_text" title="[SQL文本]"></sql-highlight>
            </div>
            <div v-if="item.sql_plan && item.sql_plan.length > 0">
              <template>
                <h4>
                  <!-- <a-icon type="exception" /> -->
                  {{ '当前执行计划信息' }}
                </h4>

                <div class="redo">
                  <pre v-for="(item, index) in item.sql_plan" :key="index">{{ item }}</pre>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CodeMirror from '@/components/CodeMirror';
import SqlHighlight from '@/components/SqlHighlight';
export default {
  components: { CodeMirror, SqlHighlight },
  props: {
    sql_list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      part2Show: true
    };
  },
  mounted() {
    // this.onExpand();
  },
  methods: {
    onExpand() {
      this.part2Show = !this.part2Show;
      let dom = this.$el.querySelector(`.part-2`);
      CommonUtil.toggleDom({
        element: dom,
        time: 0.3,
        startHeight: '48px',
        show: this.part2Show
      });
    }
  }
};
</script>

<style lang="less" scoped>
@import './commonClass.less';
</style>
