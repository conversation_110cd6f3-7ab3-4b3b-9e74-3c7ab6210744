<template>
  <a-drawer
    title="高级筛选"
    :width="450"
    :visible="visible"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="custom-table-filter-drawer"
    @close="hide"
  >
    <!-- 内容区域 -->
    <!-- <a-spin :spinning="loading"></a-spin> -->
    <custom-form
      ref="form"
      :fields="localFields"
      v-bind="searchProps"
      :formData="data"
      class="custom-table-filter-drawer-form"
    ></custom-form>

    <!-- 按钮区域 -->
    <div
      :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 10,
        }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="onReset">重置</a-button>
      <a-button type="primary" @click="onSearch">查询</a-button>
    </div>
  </a-drawer>
</template>

<script>
// import config from './config';

export default {
  components: {},
  props: {
    fields: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    // this.config = config(this);
    return {
      visible: false,
      loading: false,
      isEdit: false,
      localFields: this.fields,
      searchProps: {
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
        colon: true
      },
      data: {}
    };
  },
  mounted() {},
  created() {},
  computed: {},
  methods: {
    show(data = {}) {
      this.visible = true;
      this.setData(data);
      // this.isEdit = data.id != null;
    },
    hide() {
      this.visible = false;
    },
    onReset() {
      this.hide();
      this.$emit('reset');
    },
    onSearch() {
      const formData = this.$refs.form.getData() || {};
      this.hide();
      this.$emit('search', formData);
    },
    setData(data = {}) {
      this.data = data;
    }
  },
  watch: {
    fields(newVal) {
      this.localFields = newVal;
    }
  }
};
</script>

<style lang="less">
@import '~@/style/app.less';
.custom-table-filter-drawer {
  .custom-table-filter-drawer-form {
    .formRangeLabel(120px, 120px);
  }
}
</style>