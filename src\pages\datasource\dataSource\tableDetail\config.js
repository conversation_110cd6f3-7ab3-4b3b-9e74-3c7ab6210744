export default function (ctx) {
  const columns = [
    {
      title: '表名称',
      dataIndex: 'table_name',
      key: 'table_name',
      // scopedSlots: { customRender: 'table_name' },
      // width: 650,
      shower: {
        useSlot: 'table_name'
      },
      scopedSlots: { customRender: 'Shower' }
    },
    // {
    //   title: '行数',
    //   dataIndex: 'row',
    //   key: 'row',
    //   width: 120
    // },
    // {
    //   title: '容量(MB)',
    //   key: 'volume',
    //   dataIndex: 'volume',
    //   width: 120
    // },
    {
      title: '权限控制',
      dataIndex: 'permission_status',
      key: 'permission_status',
      // scopedSlots: { customRender: 'permission_status' },
      // width: 200,
      shower: {
        useSlot: 'permission_status'
      },
      scopedSlots: { customRender: 'Shower' }
    }
    // {
    //   title: '操作',
    //   key: 'action',
    //   scopedSlots: { customRender: 'action' },
    //   width: 150
    // }
  ];
  const searchFields = [
    // {
    //   type: 'Input',
    //   label: 'ID',
    //   key: 'id'
    // },
    {
      type: 'Input',
      label: '名称',
      key: 'name',
      mainSearch: true,
      props: {
        placeholder: '请输名称'
      }
    },
    {
      type: 'Input',
      label: '连接串',
      key: 'db_url'
    },
    {
      type: 'Select',
      label: '环境',
      key: 'env',
      props: {
        options: [
          {
            label: '测试',
            value: 'TEST'
          },
          {
            label: '生产',
            value: 'PROD'
          }
        ]
      }
    },
    // {
    //   type: 'Select',
    //   label: '管控模式',
    //   key: 'is_permission_control',
    //   props: {
    //     options: [
    //       {
    //         label: '自由操作',
    //         value: '0'
    //       },
    //       {
    //         label: '安全协同',
    //         value: '1'
    //       }
    //     ]
    //   }
    // },
    {
      type: 'Select',
      label: '数据库类型',
      key: 'db_type',
      props: {
        options: [
          {
            label: 'ORACLE',
            value: 'ORACLE'
          },
          {
            label: 'MYSQL',
            value: 'MYSQL'
          },
          {
            label: 'POSTGRE',
            value: 'POSTGRE'
          },
          {
            label: 'TIDB',
            value: 'TIDB'
          },
          {
            label: 'OB_MYSQL',
            value: 'OB_MYSQL'
          },
          {
            label: 'TD_MYSQL',
            value: 'TD_MYSQL'
          },
          {
            label: 'DB2',
            value: 'DB2'
          },
          {
            label: 'SQLSERVER',
            value: 'SQLSERVER'
          }
        ]
      }
    }
  ];
  const indexColumns = [
    {
      title: '索引名称',
      dataIndex: 'index_name',
      key: 'index_name'
      // width: 200
    },
    {
      title: '索引类型',
      key: 'non_unique',
      dataIndex: 'non_unique'
      // width: 200
    },
    {
      title: '索引字段',
      dataIndex: 'column_name',
      key: 'column_name'
      // width: 200
    }
    // {
    //   title: '字段类型',
    //   key: 'data_type',
    //   dataIndex: 'data_type',
    //   width: 200
    // },
    // {
    //   title: '字段位置',
    //   key: 'column_position',
    //   dataIndex: 'column_position',
    //   width: 200
    // },
    // {
    //   title: '区分度',
    //   key: 'cardinality',
    //   dataIndex: 'cardinality',
    //   width: 200
    // }
  ];

  const fieldColumns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id',
    //   width: 100
    // },
    {
      title: '字段名',
      dataIndex: 'name',
      key: 'name'
      // width: 200
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type'
      // width: 200
    },
    {
      title: '可空',
      dataIndex: 'is_nullable',
      key: 'is_nullable',
      customRender: (text, record, index) => {
        return text == true ? '是' : '否';
      }
      // width: 100
    },
    {
      title: '自增',
      key: 'is_autoincrement',
      dataIndex: 'is_autoincrement',
      customRender: (text, record, index) => {
        return text == true ? '是' : '否';
      }
      // width: 100
    },
    {
      title: '分区列',
      dataIndex: 'is_partition',
      key: 'is_partition',
      customRender: (text) => {
        return text == true ? '是' : '否';
      }
      // width: 100
    },
    {
      title: '默认值',
      key: 'default',
      dataIndex: 'default',
      customRender: (text) => {
        return text || '--';
      }
      // width: 200
    },
    {
      title: '备注',
      key: 'comment',
      dataIndex: 'comment',
      customRender: (text) => {
        return text || '--';
      }
      // width: 200
    },
    {
      title: '权限控制',
      key: 'permission_status',
      dataIndex: 'permission_status',
      scopedSlots: { customRender: 'permission_status' }
      // width: 200
    },
    {
      title: '脱敏控制',
      key: 'sensitive_status',
      dataIndex: 'sensitive_status',
      scopedSlots: { customRender: 'sensitive_status' }
      // width: 200
    }
  ];
  return {
    columns,
    searchFields,
    indexColumns,
    fieldColumns
  };
}
