<template>
  <div class="container">
    <a-spin class="spin" :spinning="commonViewLoading">
      <a-card class="common-pure-card box-card" :bordered="false">
        <div class="title">{{commonViewTitle}}</div>
        <div class="rule-data">
          <Chart :option="commonViewOption" />
        </div>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import Chart from '@/components/Chart';
export default {
  components: { Chart },
  props: {
    commonViewLoading: {
      type: Boolean,
      default: false
    },
    commonViewOption: {
      default: () => {}
    },
    commonViewTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  }
};
</script>

<style lang="less" scoped>
.container {
  flex: 1;
  margin-left: 24px;
  &:first-child {
    margin-left: 0;
  }
  .common-pure-card {
    height: 412px;
  }
  .title {
    margin-bottom: 10px;
    font-size: 16px;
    letter-spacing: 0;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
  .rule-data {
    height: 320px;
  }
}
</style>
