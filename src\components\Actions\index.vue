<template>
  <div :class="['custom-actions', baseVisible && 'visible']">
    <template v-for="(item, index) in handleBtnList.slice(0, limit)">
      <span class="action-btn" :key="index">
        <a-tooltip @visibleChange="onVisibleChange" v-if="!item.children" :overlayClassName="hideToolTip ? 'hide-tooltip' : ''">
          <template slot="title">{{item.name}}</template>
          <a-popconfirm
            :title="item.confirm"
            @confirm="btnsFn(item.type)"
            @visibleChange="onVisibleChange"
            v-if="item.confirm"
          >
            <a @click="(e) => btnClick(e, item)" :class="{'a-disabled': disabled || item.disabled}">
              <custom-icon :type="item.icon" />
            </a>
          </a-popconfirm>
          <a @click="(e) => btnsFn(item.type, e, item)" v-else :class="{'a-disabled': disabled || item.disabled}">
            <custom-icon :type="item.icon" />
          </a>
        </a-tooltip>
        <a-popover @visibleChange="onVisibleChange" v-else>
          <div
            slot="content"
            class="action-btn"
            v-for="(cItem, index) in item.children"
            :key="index"
          >
            <!-- <a-popconfirm
              :title="cItem.confirm"
              @confirm="btnsFn(cItem.type)"
              @visibleChange="(e) => onVisibleChange"
              v-if="cItem.confirm"
            >
              <a>{{cItem.name}}</a>
            </a-popconfirm>-->
            <a @click="btnsFn(cItem.type)" :class="{'a-disabled': disabled || cItem.disabled}">{{cItem.name}}</a>
          </div>
          <a :class="{'a-disabled': disabled || item.disabled}">
            <custom-icon :type="item.icon" />
          </a>
        </a-popover>
      </span>
    </template>
    <a-popover
      placement="bottom"
      :visible="popoverVisible"
      overlayClassName="custom-actions-ellipsis"
      @visibleChange="onPopoverVisibleChange"
      v-if="handleBtnList.length > limit"
    >
      <div
        slot="content"
        class="action-btn"
        v-for="(item, index) in handleBtnList.slice(limit)"
        :key="index"
      >
        <a-tooltip
          placement="left"
          @visibleChange="(e) => onVisibleChange(e, 'ellipsis')"
          v-if="!ellipsisUseName"
        >
          <template slot="title">{{item.name}}</template>
          <a-popconfirm
            :title="item.confirm"
            @confirm="btnsFn(item.type)"
            @visibleChange="(e) => onVisibleChange(e, 'ellipsis')"
            v-if="item.confirm"
          >
            <a :class="{'a-disabled': disabled || item.disabled}">
              <custom-icon :type="item.icon" />
            </a>
          </a-popconfirm>
          <a @click="btnsFn(item.type)" v-else :class="{'a-disabled': disabled || item.disabled}">
            <custom-icon :type="item.icon" />
          </a>
        </a-tooltip>
        <template v-else>
          <a-popconfirm
            :title="item.confirm"
            @confirm="btnsFn(item.type)"
            @visibleChange="(e) => onVisibleChange(e, 'ellipsis')"
            v-if="item.confirm"
          >
            <a :class="{'a-disabled': disabled || item.disabled}">
              <custom-icon :type="item.icon" />
              {{item.name}}
            </a>
          </a-popconfirm>
          <a @click="btnsFn(item.type)" v-else :class="{'a-disabled': disabled || item.disabled}">
            <custom-icon :type="item.icon" />
            {{item.name}}
          </a>
        </template>
      </div>
      <a>
        <custom-icon :class="['action-ellipsis', ellipsisDirection]" type="ellipsis" />
      </a>
    </a-popover>
  </div>
</template>

<script>
export default {
  props: {
    limit: {
      type: Number,
      default: 2
    },
    stop: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    ellipsisDirection: {
      type: String,
      default: 'h' // h:横向, v: 纵向
    },
    ellipsisUseName: {
      type: Boolean,
      default: false
    },
    hideToolTip: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  data() {
    return {
      baseCount: 0,
      baseVisible: false,
      popoverCount: 0,
      popoverVisible: false,
      popoverStat: false
    };
  },
  computed: {
    handleBtnList() {
      return this.btnList.filter(item => item.visible != false);
    }
  },
  mounted() {},
  destroyed() {},
  methods: {
    btnClick(e, item) {
      if (this.stop || (item && item.stop)) {
        e && e.stopPropagation();
      }
    },
    btnsFn(key, e, item) {
      this.$emit('btnsFn', key);
      if (this.stop || (item && item.stop)) {
        e && e.stopPropagation();
      }
    },
    onVisibleChange(visible, ellipsis) {
      if (visible) {
        this.baseCount += 1;
        ellipsis && this.popoverVisible && (this.popoverCount += 1);
      } else {
        this.baseCount -= 1;
        ellipsis && this.popoverVisible && (this.popoverCount -= 1);
      }
      this.popoverVisible = this.popoverCount > 0 || this.popoverStat;
      this.baseVisible = this.baseCount > 0 || this.popoverVisible;
      // console.log('onVisibleChange', this.baseCount, this.popoverCount);
    },
    onPopoverVisibleChange(visible) {
      this.popoverStat = visible;
      this.popoverVisible = this.popoverCount > 0 || visible;
      this.baseVisible = this.baseCount > 0 || this.popoverVisible;
      // console.log('onPopoverVisibleChange', this.baseCount, this.popoverCount);
    }
  }
};
</script>

<style lang="less" scoped>
.custom-actions {
  .action-btn,
  .action-ellipsis {
    margin: 0 6px;

    &.v {
      transform: rotate(90deg);
    }
  }
  &.visible {
    visibility: visible !important;
  }
}
</style>
<style lang="less" scoped>
.custom-actions-ellipsis {
  .action-btn {
    margin: 4px;
  }
}
.a-disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  pointer-events: none;
  cursor: default;
}
</style>
<style>
.hide-tooltip {
  display: none!important;
}
</style>