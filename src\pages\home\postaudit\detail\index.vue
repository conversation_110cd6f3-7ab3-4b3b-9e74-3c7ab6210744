<template>
  <div class="home-detail">
    <div class="common-view">
      <div>
        <span class="common-view-title">任务名称：</span>
        <LimitLabel :label="taskInfo.name" :limit="20"></LimitLabel>
      </div>
      <div>
        <span class="common-view-title">数据源：</span>
        <LimitLabel v-if="taskInfo.data_source" :label="taskInfo.data_source.name" :limit="20"></LimitLabel>
        <span v-if="taskInfo.data_source == null">'--</span>
      </div>
      <!-- <div v-if="taskInfo && taskInfo.db_type == 'oracle'">
        <span class="common-view-title">审核用户：</span>
        {{ taskInfo.schema == null ? '--' : taskInfo.schema.schema || '--' }}
      </div>-->
      <div>
        <span class="common-view-title">创建人：</span>
        {{ taskInfo.created_by || '--' }}
      </div>
      <div>
        <span class="common-view-title">任务类型：</span>
        <span v-if="!taskInfo.type">--</span>
        <span v-else>{{ taskInfo.type | type }}</span>
      </div>
    </div>
    <PageList
      ref="PageList"
      title="规则集"
      :searchParams="searchParams"
      :tableParams="tableParams"
      :needSplitSearch="false"
    >
      <!-- table插槽 -->
      <span slot="status" slot-scope="{ record }">
        <span v-if="record.status === null">--</span>
        <a-badge :color="record.status | color" :text="record.status | status" />
        <template v-if="record.error_message && record.error_message != null">
          <a-popover>
            <template slot="content">{{record.error_message}}</template>
            <a-icon type="question-circle" />
          </a-popover>
        </template>
      </span>
      <span slot="level_category" slot-scope="{ record }" class="level_category">
        <span>
          高风险
          <span style="color: #f73232">{{record.level_category.high}}</span> 条
        </span>
        <a-divider type="vertical" />
        <span>
          中风险
          <span style="color: #ff9f28">{{ record.level_category.middle }}</span> 条
        </span>
        <a-divider type="vertical" />
        <span>
          低风险
          <span style="color: #14c55f">{{ record.level_category.low }}</span> 条
        </span>
      </span>
      <span slot="action" slot-scope="{ record }">
        <a @click="toDetail(record)">详情</a>
      </span>
    </PageList>
  </div>
</template>

<script>
import { afterwardsReviewMiddle } from '@/api/home';
import config from './config';
import PageList from '@/components/PageListNew';
import LimitLabel from '@/components/LimitLabel';

export default {
  name: 'HomePage',
  components: { PageList, LimitLabel },
  props: {},
  data() {
    this.config = config(this);
    this.task_id = this.$route.query.task_id;
    this.searchData = {};
    return {
      tableParams: {
        url: 'sqlreview/review/afterwards_report_list',
        reqParams: {
          task_id: this.task_id
        },
        loaded: this.onTableLoaded,
        columns: this.config.columns,
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true
      },
      searchParams: {
        fields: this.config.searchFields,
        multiCols: 3
      },
      taskInfo: {}
    };
  },
  mounted() {},
  created() {
    this.getMiddleData();
  },
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },
  methods: {
    getMiddleData() {
      afterwardsReviewMiddle({ task_id: this.$route.query.task_id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.taskInfo = _.get(res, 'data.data');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(() => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(res, 'data.message')
          });
        });
    },
    getColumns(role) {
      let columns = this.config.columns;
      return columns;
    },
    toDetail(record) {
      this.$router.push({
        name: 'home-postaudit-review',
        query: {
          task_id: this.task_id,
          report_id: record.id,
          db_type: record.db_type
        }
      });
    },
    onTableLoaded(res) {},
    // 查询
    search(data) {
      const { table } = this.$refs;
      this.searchData = { ...data };
      if (this.searchData.status && this.searchData.status.length > 0) {
        this.searchData.status = this.searchData.status.join(',');
      }
      table.refresh(null, this.searchData);
    },
    // 重置
    reset(data) {
      const { table } = this.$refs;
      this.searchData = data || {};
      table.refresh();
    }
  },
  watch: {
    '$store.state.account.user'(newVal = {}) {
      this.tableParams = Object.assign({}, this.tableParams, {
        columns: this.getColumns(newVal.role)
      });
    }
  },
  filters: {
    status(value) {
      let obj = {
        0: '审核完成',
        1: '扫描审核中',
        2: '慢sql获取中',
        9: '审核失败'
      };
      return obj[value];
    },
    color(value) {
      let obj = {
        0: '#14c55f',
        1: '#ff9f28',
        2: '#4F96FB',
        9: '#f73232'
      };
      return obj[value];
    },
    type(value) {
      let obj = {
        1: '单次',
        2: '多次'
      };
      return obj[value];
    }
  }
};
</script>

<style lang="less" scoped>
.home-detail {
  .common-view {
    display: flex;
    flex-wrap: wrap;
    word-break: break-all;
    // justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    margin-top: -8px;
    padding: 16px;
    background: rgba(24, 144, 255, 0.06);
    > div {
      width: 25%;
      flex-shrink: 0;
      color: rgba(0, 0, 0, 0.6);
      text-align: center;
      display: flex;
      align-items: center;
      padding: 0 16px;
      .common-view-title {
        font-weight: 500;
        color: #000000;
        flex-shrink: 0;
      }
    }
  }
  /deep/ .ant-table .level_category {
    > span {
      white-space: nowrap;
    }
    .ant-divider {
      margin: 0 2px;
    }
  }
  @media screen and (max-width: 1500px) {
    .common-view > div {
      width: 33.3%;
      margin-bottom: 8px;
    }
  }
    @media screen and (max-width: 1200px) {
    .common-view > div {
      width: 50%;
      margin-bottom: 8px;
    }
  }
}
</style>
