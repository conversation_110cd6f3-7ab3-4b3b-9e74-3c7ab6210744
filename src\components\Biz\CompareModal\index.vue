<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal v-model="visible" :title="title" :maskClosable="false" width="700px" @cancel="onCancel">
    <CodeMirror v-bind="params"></CodeMirror>
    <template slot="footer">
      <a-button @click="hide()">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import CodeMirror from '../../CodeMirror';
// import common from '@/utils/common';

export default {
  components: { CodeMirror },
  props: {},
  data() {
    return {
      visible: false,
      title: '',
      params: {}
    };
  },
  mounted() {},
  created() {},
  methods: {
    show({ title, params }) {
      this.title = title;
      this.params = params;
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
    onCancel() {
      this.hide();
    }
  }
};
</script>

<style lang="less" scoped>
</style>
