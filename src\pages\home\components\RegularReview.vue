<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="定时审核"
    okText="保存"
    width="580px"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="params" :formData="formData" class="add-form">
      </Form>
    </a-spin>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
const formParams = {
  layout: 'horizontal',
  labelCol: { span: 6 },
  wrapperCol: { span: 12 },
  fields: [
    {
      type: 'RadioGroup',
      label: '是否使用',
      key: 'review_type',
      width: '500',
      props: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    },
    {
      type: 'Select',
      label: '状态',
      key: 'ref_id',
      props: {
        url: '/sqlreview/review/select_number',
        reqParams: {
          type: 4
        },
        allowSearch: true,
        backSearch: true,
        limit: 10
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    },
    {
      type: 'Select',
      label: '审核周期',
      key: 'ref_id',
      props: {
        url: '/sqlreview/review/select_number',
        reqParams: {
          type: 4
        },
        allowSearch: true,
        backSearch: true,
        limit: 10
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    }
  ]
};

export default {
  components: { Form },
  props: {},
  data() {
    return {
      spinning: false,
      visible: false,
      formData: {},
      params: formParams
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(type = '') {
      this.type = type;

      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      this.data = {};
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData();
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .add-form {
  .ant-row .ant-form-item-control-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        width: 150px;
      }
    }
  }
}
</style>
