<template>
  <a-modal
    v-model="visible"
    okText="确定"
    cancelText="取消"
    @ok="save"
    @cancel="onCancel"
    width="600px"
    wrapClassName="real-time-database-audit-modal"
  >
    <div slot="title" class="title">
      <span>数据库审核配置</span>
    </div>
    <a-spin :spinning="spinning">
      <!-- 数据源选择OB时 新增选项 -->
      <div class="ob-form">
        <div class="des">
          <custom-icon type="info-circle" theme="filled" />
          <span>OceanBase数据库审核需要连接到sys系统租户进行数据库执行SQL采集，如需使用数据库审核功能，请配置sys系统租户账号密码</span>
        </div>
        <Form class="ob-form" ref="form" v-bind="formParams" :formData="formData"></Form>
      </div>
    </a-spin>
    <template slot="footer">
      <a-button @click="test">链接测试</a-button>
      <a-button @click="onCancel">取消</a-button>
      <a-button type="primary" @click="save" :disabled="!isCheck">保存</a-button>
    </template>
  </a-modal>
</template>
<script>
import config from './config';
import Form from '@/components/Form';
import {
  realTimeCheckAuth,
  realTimeConfigSave
} from '@/api/databaseaudit/realtime';
export default {
  props: {
    id: String | Number
  },
  components: { Form },
  data() {
    this.config = config(this);
    return {
      spinning: false,
      visible: false,
      isCheck: false,
      formData: {},
      formParams: {
        fixedLabel: true,
        gutter: 64,
        layout: 'horizontal',
        compact: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 12 },
        fields: this.config.obInfo()
      }
    };
  },
  methods: {
    show(id) {
      this.visible = true;
    },
    onCancel() {
      this.hide();
    },
    hide() {
      this.visible = false;
      this.isCheck = false;
    },
    test() {
      const { form } = this.$refs;
      const data = form.getData();
      data.examine_pwd = Base64.encode(data.examine_pwd || '');
      const params = {
        ...data,
        data_source_id: this.id
      };
      Promise.all([form.validate()]).then(valid => {
        if (valid) {
          this.$showLoading();
          realTimeCheckAuth(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({
                  useMessage: true,
                  tips: '连接测试成功'
                });
                this.isCheck = true;
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    save() {
      const { form } = this.$refs;
      const data = form.getData();
      data.examine_pwd = Base64.encode(data.examine_pwd || '');
      const params = {
        ...data,
        data_source_id: this.id
      };
      Promise.all([form.validate()]).then(valid => {
        if (valid) {
          this.$showLoading();
          realTimeConfigSave(params)
            .then(res => {
              if (CommonUtil.isSuccessCode(res)) {
                this.$hideLoading({
                  useMessage: true,
                  tips: '校验成功'
                });
                this.hide();
                this.$emit('save');
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    }
  },
  watch: {}
};
</script>

<style lang="less">
.real-time-database-audit-modal {
  .ant-modal-header {
    padding: 32px 40px 12px 40px;
    background: #fff !important;
    .title {
      display: flex;
      font-size: 16px;
      color: #27272a;
      text-align: justify;
      font-weight: 600;
      .anticon {
        color: #e71d36;
        font-size: 16px;
        margin-right: 4px;
        padding-top: 2px;
      }
    }
  }
  .ant-modal-close {
    .ant-modal-close-x {
      color: #27272a;
    }
  }
  .ant-modal-body {
    padding: 0 40px;
    .content {
      > div {
        padding: 0 24px 48px 24px;
        border-bottom: 1px solid #e8e8e8;
        .des {
          font-size: 13px;
          color: #27272a;
          text-align: justify;
        }
      }
    }
  }
  .ant-modal-footer {
    .ant-btn {
      width: 104px;
      height: 36px;
      background: #ffffff;
      border: 1px solid #008adc;
      font-size: 14px;
      color: #008adc;
      font-weight: 600;
      border-radius: 6px;
    }
    .ant-btn-primary {
      background: #008adc;
      color: #ffffff;
      font-weight: 600;
    }
  }
  .ob-form {
    margin-bottom: 16px;
    .des {
      display: flex;
      margin-bottom: 16px;
      .anticon {
        color: #008adc;
        font-size: 16px;
        margin-right: 8px;
        position: relative;
        top: 3px;
      }
      > span {
        font-size: 14px;
        color: #000000;
      }
    }
    .ant-form {
      .ant-row {
        .ant-col-4 {
          // padding-right: 16px;
          width: 160px;
          min-height: 36px;
          .ant-form-item-no-colon {
            display: flex;
            justify-content: start;
          }
        }
        .ant-form-item-control-wrapper {
          flex-grow: 1;
          .ant-form-item-children {
            .ant-input,
            .ant-input-number,
            .ant-select-selection {
              height: 36px;
              border: 1px solid rgba(217, 217, 217, 1);
            }
          }
        }
      }
    }
  }
}
</style>
