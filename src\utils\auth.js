import Vue from 'vue'
import store from '../store'
import Auth from '@/components/Auth'
import './permission';

// 老代码
// Vue.directive('auth', {
//   bind() {
//     // console.log('auth bind')
//   },
//   inserted: function (el, binding, vnode) {
//     // console.log(el, binding, vnode, 666)
//     if (!store.state.auth) return;
//     const auth = store.state.auth.data;
//     const { value } = binding;
//     // 处理特殊类型
//     if (_.isPlainObject(value)) {
//       const { type, key, field, disabledStyle } = value;
//       if (!key) {
//         return;
//       }
//       if (auth[key] == null) {
//         // 删除
//         RemoveNode(el);
//       } else {
//         const { source_ext: sourceExt = {} } = auth[key] || {};
//         switch (type) {
//           case 'FormItem':
//             if (sourceExt.disabled) {
//               // 禁用
//               const form = vnode.context;
//               const fields = [...form.fieldArray];
//               const matchItem = fields.find(itm => itm.key == field) || {};
//               _.set(matchItem, 'props.disabled', true);
//               form.refresh();
//             }
//             break;
//           case 'tableEditAction':
//             if (sourceExt.disabled) {
//               // 禁用
//               el.querySelectorAll('a').forEach(node => { node.setAttribute('disabled', 'disabled'); });
//             }
//             break;
//           default:
//             if (sourceExt.disabled) {
//               // 禁用
//               DisableNode(el, disabledStyle);
//             }
//             break;
//         }
//       }
//     } else {
//       if (!value) {
//         return;
//       }
//       if (auth[value] == null) {
//         // 删除
//         RemoveNode(el);
//       } else {
//         const { source_ext: sourceExt = {} } = auth[value];
//         if (sourceExt.disabled) {
//           // 禁用
//           DisableNode(el);
//         }
//       }
//     }
//     // const auth = vnode.
//   },
//   update() {
//     // console.log('auth update')
//   },
//   componentUpdated() {
//     // console.log('auth componentUpdated')
//   }
// });

// 注册一个全局自定义指令 `v-auth`（该方式对于属性设置比较难，故只用于处理元素有无的情况，如需属性可用auth函数和auth组件）
Vue.directive('auth', {
  inserted: function (el, binding, vnode) {
    // console.log(el, binding, vnode, 666)
    const { value = {} } = binding;
    const { code, type } = value;
    // const AuthKeys = window.AuthKeys || {};
    // const AuthMap = window.AuthMap || {};
    // const authItem = AuthMap[key];
    const auth = store.state.auth;
    if (code && auth) {
      if (type === 'DefaultShow' || type === 'DefaultHide') {
        let show = null;
        if (type === 'DefaultShow') {
          auth.elements.forEach(item => {
            if (item.id.startsWith(code)) {
              const sourceExt = item.source_ext || {};
              if (sourceExt._show == 0) {
                show = 0;
                return false;
              }
            }
          });
        } else if (type === 'DefaultHide') {
          auth.elements.forEach(item => {
            if (item.id.startsWith(code)) {
              const sourceExt = item.source_ext || {};
              if (sourceExt._show == 1) {
                show = 1;
                return false;
              }
            }
          });
        }

        if (
          (type === 'DefaultShow' && show == 0) ||
          (type === 'DefaultHide' && show != 1)
        ) {
          RemoveNode(el);
        }
      } else if (type === 'ArrayMerge') {
        const authItems = el.querySelectorAll('[authKey]');
        const { includes, excludes } = getArrayMergeInfo(auth, code);
        arrayMergeFilter(authItems, { includes, excludes });

        // 特殊处理
        const setAuthInfo = _.get(vnode, 'componentInstance.$setAuthInfo');
        setAuthInfo && setAuthInfo(value);
      }
    }
  }
});
// 删除节点
const RemoveNode = (el) => {
  el && el.parentNode && el.parentNode.removeChild(el);
}

// 禁用节点
// const DisableNode = (el, disabledStyle) => {
//   // el.disabled = 'disabled';
//   el.setAttribute('disabled', 'disabled');
//   disabledStyle && (el.className = el.className + ` ele-disabled-${disabledStyle}`);
//   // 这种修改props，会报warning
//   // vnode && vnode.componentInstance && (vnode.componentInstance.disabled = true);
// }

// --------------------------------------  auth 函数  -------------------------------------- //
window.$Auth = (params = {}, data, cbk) => {
  const { code, type, matchKey = 'key' } = params;
  // const AuthKeys = window.AuthKeys || {};
  // const AuthMap = window.AuthMap || {};
  // const authItem = AuthMap[key];
  const auth = store.state.auth;
  if (!auth) {
    return data;
  }

  let res = data;
  switch (type) {
    case 'ArrayMerge':
      const { includes, excludes } = getArrayMergeInfo(auth, code);
      if (_.isFunction(cbk)) {
        cbk({ type, excludes, includes });
      } else if (_.isArray(data)) {
        if (includes.length > 0) {
          res = data.filter(item => includes.includes(item[matchKey]));
        } else {
          // console.log(code, data, excludes, 999)
          res = data.filter(item => !excludes.includes(item[matchKey]));
        }
        // console.log('ArrayMerge', code, res)
      }
      break;
    default:
      _.forEach(auth.elements, (item, index) => {
        if (item.id.startsWith(code)) {
          const sourceExt = item.source_ext || {};
          if (_.isPlainObject(data)) {
            _.forEach(sourceExt, (itm, key) => {
              if (!key.startsWith('_')) {
                res[key] = itm;
              }
            })
          }
          return false;
        }
      })
      break;
  }
  return res;
}

// --------------------------------------  auth 组件  -------------------------------------- //
Vue.component(GLOBAL_COMPONENTS['custom-auth'], Auth);

// --------------------------------------  auth func  -------------------------------------- //
// arrayMerge获取includes、excludes
const getArrayMergeInfo = (auth, code) => {
  let includes = [];
  let excludes = [];
  let hasExcludes = false;
  let hasIncludes = false;
  _.forEach(auth.elements, (item, index) => {
    if (item.id.startsWith(code)) {
      const sourceExt = item.source_ext || {};
      const _excludes = sourceExt._excludes != null ? (sourceExt._excludes + '').split(',') : [];
      const _includes = sourceExt._includes != null ? (sourceExt._includes + '').split(',') : [];
      excludes = [...excludes, ..._excludes];
      includes = [...includes, ..._includes];
      if (sourceExt._excludes != null) hasExcludes = true;
      if (sourceExt._includes != null) hasIncludes = true;
    }
  })
  // 如果没有配置excludes或includes，有initialValue则使用
  const authItemConfig = AuthMap[code] || {};
  const initialValue = authItemConfig.initialValue || {};
  if (!hasExcludes) {
    excludes = initialValue._excludes != null ? (initialValue._excludes + '').split(',') : [];
  }
  if (!hasIncludes) {
    includes = initialValue._includes != null ? (initialValue._includes + '').split(',') : [];
    includes = includes.filter(item => item);
  }
  return { includes, excludes };
}

// arrayMerge过滤
const arrayMergeFilter = (elements, params = {}) => {
  const { includes, excludes } = params;
  if (includes.length > 0) {
    elements.forEach(item => {
      if (!includes.includes(item.getAttribute('authKey'))) {
        RemoveNode(item);
      }
    });
  } else {
    elements.forEach(item => {
      if (excludes.includes(item.getAttribute('authKey'))) {
        RemoveNode(item);
      }
    });
  }
}
window.AuthArrayMergeFilter = arrayMergeFilter;

// 强制显示
const forceShow = (params = {}, authKey) => {
  let flag = false;
  $Auth(params, null, (data) => {
    const { type } = data;
    switch (type) {
      case 'ArrayMerge':
        const { includes } = data;
        if (includes.includes(authKey + '!')) {
          flag = true
        }
        break;
      default:
        break;
    }
  });
  return flag;
}
window.AuthForceShow = forceShow;
Vue.prototype.AuthForceShow = forceShow;