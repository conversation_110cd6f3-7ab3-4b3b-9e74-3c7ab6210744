<template>
  <div style="margin-bottom: 15px">
    <a-radio-group v-model="radioValue" @change="onChange">
      <a-radio :style="radioStyle" :value="0">
        立即执行
      </a-radio>
      <a-radio :style="radioStyle" :value="1">
        指定时间
        <a-time-picker
          style="margin-left: 20px"
          placeholder="开始时间"
          v-model="timeStart"
          @change="
                (val, dateStrings) => changeTime(val, dateStrings, 'timeStart')
              "
          value-format="H:m:s"
          :disabled="radioValue == 0"
        />~
        <a-time-picker
          placeholder="结束时间"
          v-model="timeEnd"
          :disabledHours="getDisabledHours"
          :disabledMinutes="getDisabledMinutes"
          :disabledSeconds="getDisabledSeconds"
          @change="
                (val, dateStrings) => changeTime(val, dateStrings, 'timeEnd')
              "
          value-format="H:m:s"
          :disabled="radioValue == 0"
        />
      </a-radio>
    </a-radio-group>
  </div>
</template>
<script>

export default {
  props: {},
  components: { },
  data() {
    return {
      id: '',
      radioValue: 0,
      visible: false,
      spinning: false,
      message: '',
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
        marginBottom: '15px'
      },
      timeStart: '',
      timeEnd: ''
    };
  },
  methods: {
    onChange(e) {
      this.radioValue = e.target.value;
      this.clear();
    },
    clear() {
      this.timeStart = '';
      this.timeEnd = '';
    },
    changeTime(val, dateStrings, type) {
      this.radioValue = 1;
      if (type === 'timeStart') {
        this.timeStart = dateStrings;
      } else {
        this.timeEnd = dateStrings;
      }
    },
    getDisabledHours() {
      let hours = [];
      let time = this.timeStart;
      let timeArr = time.split(':');
      for (var i = 0; i < parseInt(timeArr[0]); i++) {
        hours.push(i);
      }
      return hours;
    },
    getDisabledMinutes(selectedHour) {
      let time = this.timeStart;
      let timeArr = time.split(':');
      let minutes = [];
      if (selectedHour == parseInt(timeArr[0])) {
        for (var i = 0; i < parseInt(timeArr[1]); i++) {
          minutes.push(i);
        }
      }
      return minutes;
    },
    getDisabledSeconds(selectedHour, selectedMinute) {
      let time = this.timeStart;
      let timeArr = time.split(':');
      let second = [];
      if (
        selectedHour == parseInt(timeArr[0]) &&
        selectedMinute == parseInt(timeArr[1])
      ) {
        for (var i = 0; i <= parseInt(timeArr[2]); i++) {
          second.push(i);
        }
      }
      return second;
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>
