<template>
  <div class="rule-label">
    <a-popover overlayClassName="order-rule-label-popover" v-if="value !== null">
      <template slot="content">
        <div class="rule-block" v-if="ruleShowLabel.length > 0">
          <div class="title">触发规则</div>
          <div class="des" v-for="(item, index) in ruleShowLabel" :key="index">
            <div>
              <!-- 1是低风险 0高风险 -->
              <custom-icon
                type="lu-icon-alarm"
                :style="{color: item.rule_result == 1 ? '#f29339' : '#e71d36'}"
              />
              <span>{{item.desc}}</span>
            </div>
          </div>
        </div>
        <div class="exception-block" v-if="exceptionShowLabel.length > 0">
          <div class="title">审核异常</div>
          <div class="des" v-for="(item, index) in exceptionShowLabel" :key="index">
            <div>
              <custom-icon type="lu-icon-unusual" style="color: #71717a;" />
              <span>{{item}}</span>
            </div>
          </div>
        </div>
        <slot name="popoverBottom"></slot>
      </template>
      <span :class="['risk-level',`risk-${value}`]">{{riskText[value]}}</span>
    </a-popover>
    <span v-else class="risk-level risk-null">{{'无风险'}}</span>
  </div>
</template>

<script>
export default {
  inheritAttrs: false,
  components: {},
  props: {
    aiComment: {
      type: Object,
      default: () => {}
    },
    value: String
  },
  data() {
    return {
      riskText: {
        high: '高风险',
        low: '低风险',
        null: '无风险',
        error: '异常'
      }
    };
  },
  computed: {
    ruleShowLabel() {
      return this.aiComment.rule || [];
    },
    exceptionShowLabel() {
      return this.aiComment.error || [];
    }
  },
  created() {},
  beforeDestroy() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.rule-label {
  cursor: pointer;
  display: flex;
  .risk-level {
    width: 56px;
    height: 28px;
    font-size: 12px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .risk-high {
    color: #e71d36;
    background: #fdeff1;
  }
  .risk-low {
    color: #991b1b;
    background: #fef5ec;
  }
  .risk-null {
    color: #3a974c;
    background: #ecf5ee;
  }
  .risk-error {
    color: #71717a;
    background: rgba(0, 59, 114, 0.06);
  }
}
</style>
<style lang="less">
.ant-popover {
  &.order-rule-label-popover {
    z-index: 1051;
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 400px;
        max-height: 200px;
        .exception-block,
        .rule-block {
          .title {
            font-size: 12px;
            color: #27272a;
            font-weight: 400;
            margin-bottom: 6px;
          }
          .des {
            > div {
              font-size: 12px;
              color: #27272a;
              font-weight: 400;
              margin-bottom: 6px;
            }
          }
        }
      }
    }
  }
}
</style>
