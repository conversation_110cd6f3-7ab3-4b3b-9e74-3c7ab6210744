<template>
  <div class="immediate page-list-single">
    <Table ref="table" v-bind="tableParams || {}">
      <span slot="running_status" slot-scope="{ record }">
        <a-switch
          v-if="record.running_status !== null"
          checked-children="运行"
          un-checked-children="停止"
          :checked="record.running_status === 1"
          @change="onChange(record)"
        />
      </span>
      <span
        slot="running_time"
        slot-scope="{ record }"
      >{{record.running_time === 0 ? '一直': record.running_time == 1? '每天' + record.run_start_time + '-' + record.run_stop_time: ''}}</span>
      <template slot="env" slot-scope="{ record }">
        <span>{{ record.env === 'test' ? '测试' : '生产' }}</span>
      </template>
      <template v-slot:db_type="{ text }">
        <DbImg :type="text" v-if="text != ''" />
        <span v-else></span>
      </template>
      <custom-btns-wrapper slot="action" slot-scope="{ text, record }" :limit="3">
        <a @click="toDetail(text, record, $event)" actionBtn>查看报告</a>
        <a @click="toSetting(record)" v-if="!isDev" actionBtn>设置</a>
        <!-- <a-popconfirm title="确定删除?" @confirm="() => del('delete', record.id)" actionBtn>
          <a class="remove">删除</a>
        </a-popconfirm>-->
      </custom-btns-wrapper>
    </Table>
    <Audit ref="audit" @refresh="refreshKeep"></Audit>
  </div>
</template>

<script>
import Table from '@/components/Table';
import SearchArea from '@/components/SearchArea';
import Audit from './components/AuditModel';
import DbImg from '@/components/CustomImg/DbImg';
import { getTableList } from '@/api/immediate';

import config from './config';
// import common from '@/utils/common';

export default {
  components: { Table, SearchArea, Audit, DbImg },
  props: {},
  data() {
    this.config = config(this);
    return {
      tableParams: {
        url: '/sqlreview/real_time/real_time_list',
        methods: 'post',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id',
        // isInitReq: false,
        // needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        scroll: { x: 1450 },
        needSearchArea: true
      }
    };
  },
  mounted() {},
  computed: {
    isDev() {
      const user = this.$store.state.account.user || {};
      return user.role === 'developer';
    }
  },
  created() {},
  methods: {
    toSetting(record) {
      this.$refs.audit.show(record);
    },
    toDetail(text, record, e) {
      this.$store.commit('common/setPageCache', {
        homePostaudit: { dbType: record.db_type }
      });
      this.$router.push({
        name: 'immediateReport',
        query: {
          db_type: record.db_type,
          id: record.id,
          name: record.name,
          env: record.env
        }
      });
    },
    // 查询
    // search(data) {
    //   const { table } = this.$refs;
    //   table.refresh(null, data);
    // },
    // 重置
    // reset() {
    //   const { table } = this.$refs;
    //   table.refresh();
    // },
    onChange(record) {
      const params = {
        data_source: record.id,
        status: record.running_status === 0 ? 1 : 0
      };
      this.save(params);
    },
    save(params) {
      const { table } = this.$refs;
      this.$showLoading();
      getTableList(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.dataSource = res.data.data.result || [];
            table.refreshKeep();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    refreshKeep() {
      this.$refs.table.refreshKeep();
    }
  }
};
</script>

<style lang="less" scoped>
.ant-dropdown-link {
  margin-top: 5px;
}
</style>
