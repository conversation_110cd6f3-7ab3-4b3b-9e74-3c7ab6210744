<template>
  <div class="pages-config-project page-list-single">
    <div class="frame-button-wrapper">
      <a-button
        @click="projectImport"
        v-if="$permission.project('import')"
        class="highlight"
        >项目导入</a-button
      >
      <a-button
        @click="projectExport"
        v-if="$permission.project('export')"
        class="highlight"
        >项目导出</a-button
      >
      <a-button
        slot="extra"
        type="primary"
        icon="plus"
        @click="pullProject"
        v-if="isTaiLong"
        >项目拉取</a-button
      >
      <a-button
        slot="extra"
        type="primary"
        icon="plus"
        @click="makeNewproject"
        v-if="$permission.project('add')"
        >添加项目配置</a-button
      >
    </div>
    <Table ref="table" v-bind="tableParams || {}" class="new-view-table">
      <!-- table插槽 -->
      <span slot="group_name" slot-scope="{ text }">
        <LimitTags
          :tags="text.map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </span>
      <span slot="dataSource" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </span>
      <span slot="testDataSource" slot-scope="{ text }">
        <LimitTags
          :tags="text.split(',').map((item) => ({ label: item }))"
          :limit="1"
        ></LimitTags>
      </span>
      <span slot="review_url" slot-scope="{ text }">
        <LimitLabel :label="text" :limit="12"></LimitLabel>
      </span>
      <custom-btns-wrapper slot="action" slot-scope="{ record }">
        <a
          @click="editProject(record)"
          actionBtn
          v-if="$permission.project('edit')"
          >编辑</a
        >
        <a-popconfirm
          title="删除项目会同时将项目的审核任务同时删除，是否确认？"
          @confirm="() => removeProject(record)"
          actionBtn
          v-if="$permission.project('delete')"
        >
          <a class="remove">删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </Table>
    <div class="config">
      <a-drawer
        title="项目配置"
        width="800px"
        :visible="visible"
        :drawerStyle="{ paddingBottom: '80px' }"
        wrapClassName="config-project-modal"
        @close="handleCancel"
      >
        <Form
          ref="baseInfo"
          class="base-info-form"
          v-bind="baseInfoParams"
          :formData="baseInfoData"
        >
          <!-- <JCronModal
            :data="rowData.time_interval"
            ref="JCronModal"
            slot="time_interval"
            @cronExpression="cronExpression"
          ></JCronModal> -->

          <Form
            ref="gitForm"
            slot="git_address"
            class="git-form"
            v-bind="gitFormParams"
            :formData="gitFormData"
          ></Form>

          <CheckInfo
            v-if="checkStatus"
            :value="checkInfoData"
            ref="check"
            slot="check"
          ></CheckInfo>
        </Form>
        <div
          :style="{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
            zIndex: 1
          }"
        >
          <a-button type="primary" @click="checkBtn">数据源权限检测</a-button>
          <a-button @click="handleCancel">取消</a-button>
          <a-button @click="handleOk" type="primary" :disabled="!checkStatus"
            >确定</a-button
          >
        </div>
      </a-drawer>
    </div>
    <PullProjectModal ref="pullProject" @refresh="reset" />
  </div>
</template>
<script>
import LimitTags from '@/components/LimitTags';
import Table from '@/components/Table';
import Select from '@/components/Select';
import SearchArea from '@/components/SearchArea';
import Form from '@/components/Form';
// import JCronModal from './components/JCronModal';
import JCronModal from '@/components/Biz/JCronModal';
import CheckInfo from './components/CheckInfo';
import DateFormat from '@/components/DateFormat';
import LimitLabel from '@/components/LimitLabel';
import PullProjectModal from './components/PullProjectModal';
import InstanceItem from '@/components/Biz/InstanceItem';
import DataBaseChoose from '@/components/Biz/DataBaseChoose';
import {
  getNewProject,
  getEditProject,
  getDleProject,
  getSchemaEdit,
  getCheckData,
  projectDownload
} from '@/api/config/project';
import config from './config';
import { Base64 } from 'js-base64';

export default {
  name: 'project-config',
  components: {
    Table,
    Select,
    SearchArea,
    Form,
    JCronModal,
    CheckInfo,
    DateFormat,
    LimitLabel,
    LimitTags,
    PullProjectModal,
    InstanceItem,
    DataBaseChoose
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this, {}),
      bottomData: {},
      dataSource: [],
      isEdit: false,
      id: '',
      tableParams: {
        url: '/sqlreview/project/list',
        reqParams: {},
        columns: this.config.columns.filter(item => item.visible != false),
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        searchFields: this.config.fields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 'max-content' }
      },
      searchParams: {
        fields: this.config.fields,
        multiCols: 3
      },
      baseInfoData: {
        intercept_switch: true
      },
      baseInfoParams: {
        gutter: 32,
        colon: true,
        // layout: 'vertical',
        multiCols: 1,
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
        fields: this.config.baseInfo()
      },
      gitFormData: {},
      gitFormParams: {
        gutter: 32,
        colon: true,
        multiCols: 2,
        fields: this.config.gitFormFields()
      },
      columnsMadal: this.config.columnsMadal,
      radioFormIsShow: false,
      env: '', // 环境
      dataSourceOption: [], // 保存数据源全部数据
      editSchemaList: [], // 编辑时保存schemaList
      rowData: {}, // 编辑时保存当前行数据
      activeType: null,
      checkStatus: false,
      failMessageList: [],
      checkInfoData: []
    };
  },
  computed: {
    // 泰隆银行特有
    isTaiLong() {
      const specificConfig = process.channel === 'TaiLongBank';
      return specificConfig;
    }
  },
  methods: {
    // 获取cron时间设置
    cronExpression(data) {
      if (data.split(' ').length > 0) {
        if (data.split(' ').includes('undefined')) {
          this.baseInfoData = Object.assign({}, this.baseInfoData, {
            time_interval: ''
          });
          this.$refs.baseInfo.validate();
        } else {
          this.baseInfoData = Object.assign({}, this.baseInfoData, {
            time_interval: data
          });
        }
      }
      this.$nextTick(() => {
        this.$refs.baseInfo.validate();
      });
    },
    // 编辑时获取数据
    getEditSchemaList(id) {
      getSchemaEdit({ project_id: id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.editSchemaList = res.data.data || [];
            this.gitFormData = {
              pull_type: this.rowData.pull_type,
              review_url: this.rowData.review_url
            };
            this.baseInfoData = {
              // pull_type: this.rowData.pull_type,
              // ssh_id_rsa: this.rowData.ssh_id_rsa,
              // scan_strategy: this.rowData.scan_strategy,
              // name: this.rowData.name,
              // auto_status: this.rowData.auto_status,
              // rule_set: this.rowData.rule_set,
              // time_interval: this.rowData.time_interval,
              // review_type: this.rowData.review_type,
              // review_tag: this.rowData.review_tag,
              // review_url: this.rowData.review_url,
              // user: this.rowData.user,
              ...this.rowData,
              group_name: this.rowData.group_id,
              data_sourceSchema_list: this.editSchemaList,
              is_review: this.rowData.is_review ? '是' : '否',
              black_list: (this.rowData.black_list || []).map(item => {
                return {
                  path: item
                };
              }),
              white_list: (this.rowData.white_list || []).map(item => {
                return {
                  path: item
                };
              }),
              type: this.rowData.review_type
            };

            if (
              this.isEdit &&
              !['clearcase', 'agent'].includes(this.rowData.review_type)
            ) {
              const _config = this.config.baseInfo().map(item => {
                if (_.isFunction(item) && item().key == 'password') {
                  item = (formData = {}) => {
                    return {
                      type: 'InputPassword',
                      label: '密码',
                      key: 'password',
                      width: '500',
                      visible:
                        (formData.review_type == 'git' &&
                          formData.pull_type !== 'ssh') ||
                        formData.review_type == 'svn',
                      props: { placeholder: '填写该项，会更新密码' },
                      rules: []
                    };
                  };
                }
                return item;
              });
              this.$set(this.baseInfoParams, 'fields', _config);
            }
            if (this.isEdit && this.rowData.review_type == 'clearcase') {
              const _config = this.config.baseInfo().map(item => {
                if (_.isFunction(item) && item().key == 'cc_pwd') {
                  item = (formData = {}) => {
                    return {
                      type: 'Input',
                      label: 'CC中转服务器密码',
                      key: 'cc_pwd',
                      width: '500',
                      visible: formData.review_type == 'clearcase',
                      props: { placeholder: '填写该项，会更新密码' },
                      rules: []
                    };
                  };
                }
                return item;
              });
              this.$set(this.baseInfoParams, 'fields', _config);
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(err => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(err, 'data.message')
          });
        });
    },
    // 编辑项目
    editProject(e) {
      this.rowData = e;
      this.visible = true;
      this.activeType = true;
      this.isEdit = true;
      this.id = e.id;
      this.getEditSchemaList(e.id);
    },
    // 删除项目
    removeProject(e) {
      const { table } = this.$refs;
      getDleProject({
        id: e.id
      }).then(res => {
        if (_.get(res, 'data.code') == 0) {
          this.$hideLoading({ tips: _.get(res, 'data.message') });
          table.refresh();
        } else {
          this.$hideLoading({
            method: 'error',
            tips: _.get(res, 'data.message')
          });
        }
      });
    },
    // 创建新项目
    makeNewproject() {
      this.visible = true;
      this.checkStatus = false;
      this.$set(this.baseInfoParams, 'fields', this.config.baseInfo());
      this.$set(this.baseInfoData, 'scan_strategy', 0);
      // this.FormConfig = this.config.radioFormConfig;
    },
    // 弹窗确定按钮
    handleOk(e) {
      const { table, baseInfo, JCronModal, gitForm } = this.$refs;
      const dataSourceSchema = baseInfo.$refs.data_sourceSchema_list[0];
      const projectMaster = baseInfo.$refs.project_master && baseInfo.$refs.project_master[0];
      if (JCronModal) {
        JCronModal.handleSubmit();
      }
      e.preventDefault();
      // let schemaList = baseInfo.getData().data_sourceSchema_list || [];
      // let schemaId = [];
      // let datasourceId = [];
      // schemaList.forEach(item => {
      //   !_.isEmpty(item.schema) && schemaId.push(item.schema);
      //   !!item.datasource_id && datasourceId.push(item.datasource_id);
      // });
      // if (
      //   datasourceId.length <= 0 ||
      //   schemaId.length <= 0 ||
      //   datasourceId.length !== schemaId.length
      // ) {
      //   this.$message.warning('请选择数据源/schema');
      //   return;
      // }
      this.$nextTick(() => {
        Promise.all([
          dataSourceSchema.validate(),
          projectMaster && projectMaster.validate(),
          baseInfo.validate()
        ]).then(valid => {
          if (valid) {
            const paramsData = baseInfo.getData();
            const gitFormData = gitForm && gitForm.getData();
            const schemaList = paramsData.data_sourceSchema_list || [];
            const dataSource = [];
            schemaList.forEach(item => {
              dataSource.push(item.datasource_id);
            });
            const params = {
              // group_name: paramsData.group_name,
              // ssh_id_rsa: paramsData.ssh_id_rsa,
              // scan_strategy: paramsData.scan_strategy,
              // name: paramsData.name,
              // auto_status: paramsData.auto_status,
              // review_type: paramsData.review_type || '',
              // review_url: paramsData.review_url || '',
              // review_tag: paramsData.review_tag || '',
              // is_review: paramsData.is_review,
              // time_interval: paramsData.time_interval,
              // user: paramsData.user || '',
              ...paramsData,
              schema_List: schemaList,
              pull_type: gitFormData && gitFormData.pull_type,
              cc_pwd: Base64.encode(paramsData.cc_pwd || ''),
              password: Base64.encode(paramsData.password || ''),
              data_source: dataSource.join(',') || '', // 数据源
              rule_set: paramsData.rule_set.join(','),
              black_list: (paramsData.black_list || [])
                .map(item => item.path)
                .filter(item => item),
              white_list: (paramsData.white_list || [])
                .map(item => item.path)
                .filter(item => item),
              id: this.id
            };
            this.$showLoading();
            if (this.isEdit) {
              getEditProject(params).then(res => {
                if (_.get(res, 'data.code') == 0) {
                  this.$hideLoading({
                    tips: _.get(res, 'data.message')
                  });
                  table.refresh();
                  if (JCronModal) {
                    JCronModal.close();
                  }
                  this.handleCancel();
                } else {
                  if (JCronModal) {
                    JCronModal.close();
                  }
                  this.$hideLoading({
                    method: 'error',
                    tips: _.get(res, 'data.message')
                  });
                  // this.handleCancel();
                }
              });
            } else {
              getNewProject(params).then(res => {
                if (_.get(res, 'data.code') == 0) {
                  this.$hideLoading({
                    tips: _.get(res, 'data.message')
                  });
                  table.refresh();
                  if (JCronModal) {
                    JCronModal.close();
                  }
                  this.handleCancel();
                } else {
                  if (JCronModal) {
                    JCronModal.close();
                  }
                  this.$hideLoading({
                    method: 'error',
                    tips: _.get(res, 'data.message')
                  });
                  // this.handleCancel();
                }
              });
            }
          }
        });
      });
    },
    // 弹窗取消事件
    handleCancel(e) {
      this.checkStatus = false;
      const { baseInfo } = this.$refs;
      // if (bottomForm.active) {
      //   bottomForm.resetFields();
      //   bottomForm.active = null;
      // }
      baseInfo.resetFields();
      this.rowData = {};
      // this.bottomData = {};
      this.baseInfoData = {
        intercept_switch: true
      };
      this.gitFormData = {};
      this.baseInfoData = Object.assign({}, this.baseInfoData, {
        time_interval: '',
        data_sourceSchema_list: []
      });
      this.isEdit = false;
      // this.radioFormIsShow = false;
      this.visible = false;
    },
    // getEditConfig() {
    //   let editConfig = {};
    //   let _config = _.merge({}, this.config.radioFormConfig).config;
    //   _config.map(item => {
    //     let b = item.form.find(i => i.key === 'password');
    //     if (b) {
    //       b.rules = [];
    //       b.props = { placeholder: '填写该项，会更新密码' };
    //     }
    //   });
    //   editConfig = {
    //     config: _config
    //   };
    //   return editConfig;
    // },
    // 查询
    search(data) {
      const { table } = this.$refs;
      table.refresh(null, data);
    },
    // 重置
    reset() {
      const { table } = this.$refs;
      table.refresh();
    },
    // 数据源检测按钮
    async checkBtn() {
      const { baseInfo, JCronModal } = this.$refs;
      const dataSourceSchema = baseInfo.$refs.data_sourceSchema_list[0];
      const projectMaster = baseInfo.$refs.project_master && baseInfo.$refs.project_master[0];
      const checkData = baseInfo.getData().data_sourceSchema_list || [];
      const checkList = checkData.map(item => ({
        data_source_id: item.datasource_id,
        schema_list: item.schema
      }));
      if (JCronModal) {
        await JCronModal.handleSubmit();
      }

      Promise.all([
        dataSourceSchema.validate(),
        projectMaster && projectMaster.validate(),
        baseInfo.validate()
      ]).then(valid => {
        if (valid) {
          this.$showLoading();
          getCheckData(checkList)
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.checkInfoData = _.get(res, 'data.data.results');
                this.$set(this, 'checkStatus', true);
                if (this.isEdit) {
                  const _config = this.config.baseInfo().map(item => {
                    if (_.isFunction(item) && item().key == 'password') {
                      item = (formData = {}) => {
                        return {
                          type: 'InputPassword',
                          label: '密码',
                          key: 'password',
                          width: '500',
                          visible: formData.pull_type !== 'ssh',
                          props: { placeholder: '填写该项，会更新密码' },
                          rules: []
                        };
                      };
                    }
                    if (_.isFunction(item) && item().key == 'cc_pwd') {
                      item = (formData = {}) => {
                        return {
                          type: 'Input',
                          label: 'CC中转服务器密码',
                          key: 'cc_pwd',
                          width: '500',
                          visible: formData.review_type == 'clearcase',
                          props: { placeholder: '填写该项，会更新密码' },
                          rules: []
                        };
                      };
                    }
                    return item;
                  });
                  this.$set(this.baseInfoParams, 'fields', _config);
                } else {
                  this.$set(
                    this.baseInfoParams,
                    'fields',
                    this.config.baseInfo()
                  );
                }
                this.$hideLoading({
                  method: 'success',
                  tips: '校验成功'
                });
              } else {
                this.$set(this, 'checkStatus', true);
                if (this.isEdit) {
                  const _config = this.config.baseInfo().map(item => {
                    if (item.key === 'password') {
                      item.rules = [];
                      item.props = { placeholder: '填写该项，会更新密码' };
                    }
                    if (_.isFunction(item) && item().key == 'cc_pwd') {
                      item = (formData = {}) => {
                        return {
                          type: 'Input',
                          label: 'CC中转服务器密码',
                          key: 'cc_pwd',
                          width: '500',
                          visible: formData.review_type == 'clearcase',
                          props: { placeholder: '填写该项，会更新密码' },
                          rules: []
                        };
                      };
                    }
                    return item;
                  });
                  this.$set(this.baseInfoParams, 'fields', _config);
                } else {
                  this.$set(
                    this.baseInfoParams,
                    'fields',
                    this.config.baseInfo()
                  );
                }
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    },
    // 项目拉取，只有泰隆银行有
    pullProject() {
      this.$refs.pullProject.show();
    },
    // 项目导入
    projectImport() {
      this.$router.push({ name: 'project-import-config' });
    },
    // 项目导入
    projectExport() {
      this.$showLoading({
        tips: `下载中...`
      });
      const { table } = this.$refs;
      const { searchParams } = table;
      projectDownload(searchParams)
        .then(res => {
          CommonUtil.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.config-project-modal {
  /deep/ .ant-drawer-content-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        width: 200px;
      }
    }
  }
  /deep/ .ant-col-24:nth-child(2) {
    .ant-table-row-cell-break-word {
      // padding: 12px 16px 20px 16px !important;
    }
  }
  /deep/.table-edit-list-like .ant-table-tbody td {
    padding: 4px 16px 4px 0px !important;
  }
  /deep/.ant-form .ant-form-item-label {
    text-align: right;
    padding-right: 12px;
    width: 180px;
    line-height: 36px;
  }
  /deep/ .base-info-form {
    // > .ant-row > .ant-col > .ant-form-item > .ant-form-item-control-wrapper {
    //   width: calc(100% - 220px);
    // }
    line-height: 2;
    .git-form {
      .ant-row {
        display: flex;
        align-content: center;
        .ant-col {
          width: 100px;
          margin-right: 20px;
          .ant-form-item-control {
            line-height: 32px;
          }
        }
      }
    }

    > .ant-row {
      margin: 0 !important;
      > .ant-col {
        padding-left: 0 !important;
        padding-right: 0 !important;
        &.group-start {
          padding-top: 16px;
        }
        &.group-end {
          padding-bottom: 24px;
          border-bottom: 1px solid #e8e8e8;
        }
        .ant-form-item {
          margin-bottom: 0;
          .ant-form-item-control-wrapper {
            .ant-form-item-control {
              line-height: 36px;
              &.has-error {
                margin-bottom: 16px;
              }
              .ant-form-item-children {
                .ant-input {
                  height: 36px;
                  width: 500px;
                }
                .ant-radio-button-wrapper {
                  height: 36px;
                  line-height: 36px;
                }
                .ant-select-selection,
                .ant-select-selection-selected-value,
                .ant-select-selection--single,
                .ant-select-selection__rendered {
                  min-height: 36px;
                  line-height: 36px;
                }
                .ant-select-selection__rendered {
                  > ul > li {
                    height: 28px;
                    line-height: 27px;
                    &.ant-select-selection__choice {
                      max-width: 88%;
                    }
                  }
                }
                .ant-select-selection--multiple {
                  padding-bottom: 0;
                  .ant-select-selection__clear {
                    top: 18px;
                  }
                }
                .table-edit {
                  .ant-form {
                    .ant-table-body {
                      .ant-table-tbody {
                        > tr {
                          width: 568px;
                          display: block !important;
                          padding-bottom: 8px;
                          &:last-child {
                            padding-bottom: 0;
                          }
                          td {
                            padding: 0 8px 0 0 !important;
                            .ant-row {
                              .ant-form-item-control {
                                .ant-form-item-children {
                                  .ant-select {
                                    width: 236px;
                                  }
                                  .ant-select-selection-selected-value {
                                    white-space: nowrap !important;
                                  }
                                  .biz-data-base-choose {
                                    width: 234px !important;
                                    line-height: 36px;
                                    .instance-item-tag {
                                      line-height: 26px;
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  /deep/ form .ant-select,
  form .ant-cascader-picker {
    width: 240px;
  }
  /deep/ .check {
    > .ant-form-item {
      > .ant-form-item-label {
        > label::after {
          content: '';
        }
      }
    }
  }
}
</style>
