<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    title="新建审核对象"
    okText="保存"
    width="650px"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="params" :formData="formData" class="add-form">
        <JCronModal
          :data="collectTimeStrategy"
          ref="JCronModal"
          slot="timeStrategy"
          @cronExpression="cronExpression"
        ></JCronModal>
      </Form>
    </a-spin>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import config from './config';
import JCronModal from '@/components/Biz/JCronModal';
import moment from 'moment';

export default {
  components: { Form, JCronModal },
  props: {},
  data() {
    this.config = config(this);
    return {
      spinning: false,
      visible: false,
      formData: {
        source: 'XLY'
      },
      params: {
        layout: 'horizontal',
        labelCol: { span: 6 },
        wrapperCol: { span: 17 },
        fields: this.config.fields()
      },
      collectTimeStrategy: '',
      data: {},
      isEdit: false
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      this.data = data;
      if (data.id) {
        this.isEdit = true;
        this.formData = {
          ...data,
          status: Boolean(data.status == 1),
          time_range: data.review_time_range
        };
        this.collectTimeStrategy = data.corn || '';
      } else {
        this.formData = {
          source: 'XLY'
        };
        this.collectTimeStrategy = '';
      }
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      this.formData = {};
      this.isEdit = false;
      this.collectTimeStrategy = '';
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form, JCronModal } = this.$refs;
      // const JCronForm = JCronModal && JCronModal.$refs.form;
      Promise.all([
        form && form.validate()
        // JCronModal && JCronForm.validate()
      ]).then(valid => {
        if (valid) {
          // 触发组件提交数据
          if (JCronModal) {
            JCronModal.handleSubmit();
          }
          const data = form.getData();
          if (data.time_range) {
            data.time_range = `${moment(data.time_range[0]).format('YYYY-MM-DD HH:mm:ss')},${moment(data.time_range[1]).format('YYYY-MM-DD HH:mm:ss')}`;
          }
          let reqParams = {
            ...data
          }
          if (this.data.id) { reqParams.id = this.data.id; }
          this.$emit('save', reqParams);
        }
      });
    },
    cronExpression(data, type) {
      if (data && data.split(' ').length > 0) {
        if (data.split(' ').includes('undefined')) {
          this.formData = Object.assign({}, this.formData, {
            corn: '',
            unit: type
          });
        } else {
          this.formData = Object.assign({}, this.formData, {
            corn: data,
            unit: type
          });
          this.$refs.form.saving({
            corn: data,
            unit: type
          });
        }
      }
      this.$nextTick(() => {
        this.$refs.form.validate();
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .add-form {
  .ant-row .ant-form-item-control-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        width: 150px;
      }
    }
  }
}
</style>
