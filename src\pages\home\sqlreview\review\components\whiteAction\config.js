const options = [
  { label: '低档（QPS100以下）', value: 'low' },
  { label: '中档（QPS100~300）', value: 'middle' },
  { label: '高档（QPS300以上）', value: 'high' }
];
const sqlOptions = [
  { label: '低档（低于1000条/天）', value: 'low' },
  { label: '中档（1000-10000条/天）', value: 'middle' },
  { label: '高档（大于10000条/天）', value: 'high' }
];
export default function (ctx) {
  const fields = [
    // {
    //   type: 'RadioGroup',
    //   label: '平均调用频率',
    //   key: 'sqlmap_average_frequency',
    //   props: {
    //     options: options
    //   },
    //   width: 'auto',
    //   slots: [
    //     { key: 'average_private', wrapperStyle: { display: 'inline-block' } }
    //   ]
    //   // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    // },
    {
      type: 'RadioGroup',
      label: '峰值调用频率',
      key: 'sqlmap_max_frequency',
      props: {
        options: options
      },
      width: 'auto',
      slots: [
        { key: 'max_private', wrapperStyle: { display: 'inline-block' } }
      ]
      // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'TableEdit',
      label: '每月数据量增加',
      key: 'sqlmap_monthly_increase',
      getDataMethod: 'getData',
      resetFieldsMethod: 'resetFields',
      initialValue: [],
      props: {
        columns: [
          {
            title: '表名',
            dataIndex: 'table_name',
            key: 'table_name',
            width: 300
          },
          {
            title: 'SCHEMA名',
            dataIndex: 'schema',
            key: 'schema',
            width: 300
          },
          {
            title: '数据增加档数',
            dataIndex: 'frequency',
            key: 'frequency',
            width: 300,
            scopedSlots: { customRender: 'frequency' }
          }
        ],
        editConfig: {
          frequency: (row, record = {}) => {
            return {
              type: 'Select',
              props: {
                options: sqlOptions,
                placeholder: '请选择'
                // mode: 'tags',
                // maxTags: 1
                // separator: ','
              },
              rules: [
                // { required: true, message: '该项为必填项' }
              ]
            };
          }
        },
        // mode: 'list',
        initEditStatus: true,
        pagination: false,
        size: 'small'
        // leastNum: 1,
        // actionBtns: ['add', 'remove'],
        // actionBtnsIcons: {
        //   add: 'plus-circle',
        //   remove: 'close-circle'
        // }
      },
      width: '80%'
    },
    // {
    //   type: 'RadioGroup',
    //   label: '白名单申请',
    //   key: 'sqlmap_white_list',
    //   props: {
    //     options: [
    //       { label: '加入白名单', value: 1 },
    //       { label: '不加入', value: 0 }
    //     ]
    //   }
    // },
    {
      type: 'Textarea',
      label: '备注',
      key: 'sqlmap_note',
      width: '80%',
      props: {
        // size: 'small'
      }
      // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    }
  ];
  return {
    fields
  };
}
