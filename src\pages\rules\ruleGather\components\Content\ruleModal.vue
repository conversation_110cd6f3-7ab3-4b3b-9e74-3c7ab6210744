<template>
  <div class="config-rules-edit">
    <a-modal title="规则编辑" width="1000px" :visible="visible" @ok="onSave" @cancel="onCancel">
      <a-spin tip="加载中" :spinning="loading">
        <Content :type="type" ref="content" :dataSource="dataSource" />
      </a-spin>
    </a-modal>
  </div>
</template>

<script>
import { ruleDDLAdd, ruleDDLDetail } from '@/api/config/rule';
// import _ from 'lodash';
// import common from '@/utils/common';
import Content from './indexRule';

export default {
  components: { Content },
  props: {},
  data() {
    const type = this.$route.name === 'rules-config-detail' ? 'detail' : 'edit';
    return {
      visible: false,
      loading: false,
      title: type === 'edit' ? '规则修改' : '规则查看',
      dbType: '',
      type,
      dataSource: {},
      id: null
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(id) {
      this.id = id;
      this.visible = true;
      this.getDataInfo(id);
    },
    // 编辑时或许详情数据
    getDataInfo(id) {
      this.loading = true;
      ruleDDLDetail({
        rule_uid: id
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.loading = false;
            let dataSource = _.get(res, 'data.data') || {};
            this.dataSource = dataSource;
            this.dbType =
              dataSource.db_type ||
              window.localStorage.getItem('db_type') ||
              '';
            if (this.dbType) {
              this.title += `（${this.dbType}）`;
            }
          } else {
            this.$message.error(_.get(res, 'data.message'));
            this.loading = false;
          }
        })
        .catch(e => {
          console.error(e);
          this.loading = false;
          this.$message.error('请求失败');
        });
    },
    onCancel() {
      this.visible = false;
      // this.$router.push({ name: 'rules-config' });
    },
    handleOk() {
      console.log('ok');
    },
    // 点击编辑进行新增规则
    onSave() {
      const { content } = this.$refs;
      content.getData().then(data => {
        // 请求
        this.$showLoading();
        ruleDDLAdd({
          ...data,
          rule_uid: this.id,
          db_type: this.dbType
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ tips: _.get(res, 'data.message') });
              this.visible = false;
              this.$emit('tableUpdate', true);
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          });
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
