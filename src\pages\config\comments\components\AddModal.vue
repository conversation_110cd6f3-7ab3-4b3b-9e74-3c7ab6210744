<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建项目弹窗 -->
  <a-modal
    v-model="visible"
    :title="title"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </a-modal>
</template>
<script>
import Form from '@/components/Form';
// import common from '@/utils/common';
const formParams = () => {
  return {
    layout: 'vertical',
    fields: [
      {
        type: 'Select',
        label: '应用分类',
        key: 'review_type',
        props: {
          url: '/sqlreview/common/item-list/',
          reqParams: {
            parent_item_key: 'app_classify'
          },
          loading: true
        },
        rules: [{ required: true, message: '该项为必填项' }]
      },
      {
        type: 'Select',
        label: '意见类型',
        key: 'type',
        props: {
          options: [
            { label: '通过', value: 'PASS' },
            { label: '不通过', value: 'NOT_PASS' }
          ]
        },
        rules: [{ required: true, message: '该项为必填项' }]
      },
      {
        type: 'Textarea',
        label: '意见描述',
        key: 'desc',
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'InputNumber',
        label: '排序',
        key: 'order',
        props: {
          placeholder: '请输入数字',
          min: 0,
          max: 32767
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
    ]
  };
};

export default {
  components: { Form },
  props: {
    comments: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      visible: false,
      title: '新建评审意见',
      data: {},
      params: formParams()
    };
  },
  watch: {
    comments(val) {
      this.data = { ...val };
    }
  },
  mounted() {},
  created() {},
  methods: {
    show(data = {}) {
      const { type } = data;
      this.params = formParams();
      this.visible = true;
      this.title = `${type === 'edit' ? '编辑' : '新建'}评审意见`;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { data } = this;
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
