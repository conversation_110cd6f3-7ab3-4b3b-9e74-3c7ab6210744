export default function (ctx) {
  const columns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '操作时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150
    },
    {
      title: '操作用户',
      dataIndex: 'opertor',
      key: 'opertor',
      width: 160
    },
    {
      title: '数据库/schema',
      key: 'schema',
      dataIndex: 'schema',
      scopedSlots: { customRender: 'schema' },
      width: 150
    },
    {
      title: 'SQL',
      key: 'sql_text',
      dataIndex: 'sql_text',
      scopedSlots: { customRender: 'sql_text' },
      width: 120
    },
    {
      title: '执行结果',
      key: 'status',
      dataIndex: 'status',
      scopedSlots: { customRender: 'status' },
      width: 150
    },
    {
      title: '行数',
      key: 'line_count',
      dataIndex: 'line_count',
      sorter: true,
      width: 120
    },
    {
      title: '耗时(ms)',
      key: 'work_time',
      dataIndex: 'work_time',
      sorter: true,
      width: 120
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = [
    {
      type: 'Input',
      label: '操作用户',
      key: 'opertor',
      mainSearch: true,
      props: {
        placeholder: '请输入操作用户'
      }
    },
    {
      type: 'Input',
      label: '数据库/schema',
      key: 'schema'
    },
    {
      type: 'Input',
      label: 'SQL',
      key: 'sql_text'
    },
    {
      type: 'Select',
      label: '执行结果',
      key: 'status',
      props: {
        options: [
          {
            label: '成功',
            value: '1'
          },
          {
            label: '失败',
            value: '-1'
          }
        ]
      }
    }
    // {
    //   type: 'Input',
    //   label: '行数',
    //   key: 'line_count'
    // },
    // {
    //   type: 'Input',
    //   label: '耗时',
    //   key: 'work_time'
    // }
  ];

  const columnsLogin = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '登录时间',
      dataIndex: 'login_time',
      key: 'login_time',
      scopedSlots: { customRender: 'login_time' },
      width: 150
    },
    {
      title: '登录用户',
      dataIndex: 'user',
      key: 'user',
      scopedSlots: { customRender: 'user' },
      width: 100
    },
    {
      title: '来源ip',
      key: 'ip',
      dataIndex: 'ip',
      scopedSlots: { customRender: 'ip' },
      width: 100
    },
    {
      title: '登录状态',
      key: 'status',
      dataIndex: 'status',
      scopedSlots: { customRender: 'status' },
      width: 100
    }
  ];
  const fieldsLogin = [
    {
      type: 'Input',
      label: '登录用户',
      key: 'user',
      mainSearch: true,
      props: {
        placeholder: '请输入登录用户'
      }
    },
    // {
    //   type: 'Input',
    //   label: '登录时间',
    //   key: 'login_time'
    // },
    // {
    //   type: 'Input',
    //   label: '登录用户',
    //   key: 'user'
    // },
    {
      type: 'Input',
      label: '来源ip',
      key: 'ip'
    },
    // {
    //   type: 'Input',
    //   label: '执行结果',
    //   key: 'review_point'
    // },
    {
      type: 'Select',
      label: '登录状态',
      key: 'status',
      props: {
        options: [
          {
            label: '成功',
            value: '1'
          },
          {
            label: '失败',
            value: '-1'
          }
        ]
      }
    }
  ];

  const trackingColumns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: '操作页面',
      dataIndex: 'page',
      key: 'page'
    },
    {
      title: '操作用户',
      dataIndex: 'user',
      key: 'user'
    },
    {
      title: '操作时间',
      dataIndex: 'created_time',
      key: 'created_time'
    },
    {
      title: '操作内容',
      dataIndex: 'action_content',
      key: 'action_content'
    },
    {
      title: '备注',
      dataIndex: 'content',
      key: 'content'
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      fixed: 'right'
    }
  ];

  const trackingFields = [
    {
      type: 'Input',
      label: '操作用户',
      key: 'operator',
      mainSearch: true,
      props: {
        placeholder: '请输入操作用户'
      }
    }
  ];

  return {
    columns,
    fields,
    columnsLogin,
    fieldsLogin,
    trackingColumns,
    trackingFields
  };
}
