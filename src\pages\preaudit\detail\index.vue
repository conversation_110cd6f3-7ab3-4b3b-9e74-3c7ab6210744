<template>
  <div class="preaudit-detail">
    <!-- 顶部气泡卡片 -->
    <div :class="['sql-detail', isProcedureReviewDetail && 'procedure']">
      <a-popover placement="rightBottom">
        <template slot="content">
          <div class="form-box">
            <div class="form-item">
              <span class="name">应用名称：</span>
              <span>{{ appName || '--' }}</span>
            </div>
            <div class="form-item">
              <span class="name">{{
                formData.is_svn == 1 ? 'svn地址：' : 'git地址：'
              }}</span>
              <span>{{ formData.url || '--' }}</span>
            </div>
            <div class="form-item">
              <span class="name">开发人员：</span>
              <span>
                <LimitTags :tags="formData.dev_id_list" :limit="3"></LimitTags>
              </span>
            </div>
            <div class="form-item">
              <span class="name">数据源：</span>
              <span>
                <LimitTags
                  :tags="formData.sql_source_list"
                  :limit="3"
                ></LimitTags>
              </span>
            </div>
          </div>
        </template>
        <div class="project-info">
          <span
            class="circle"
            :style="{ background: statusColor[analyStatus] }"
          ></span>
          <LimitLabel
            :label="appName || '--'"
            :limit="20"
            class="title-project-name"
          ></LimitLabel>
          <custom-icon type="lu-icon-metadata" />
        </div>
      </a-popover>
    </div>
    <div class="frame-button-wrapper">
      <a-button
        class="review-detail-btn"
        type="primary"
        v-if="isVirtualLogin"
        @click="toLogin"
        >登录</a-button
      >
      <a-button @click="toBack" v-if="jksuser !== 'jksuser'" class="highlight"
        >返回</a-button
      >
      <!-- <a-button
        icon="download"
        @click="download"
        class="highlight"
        v-if="![0, 3, 4, 5, 9].includes(status)"
        >导出</a-button
      > -->
      <a-dropdown
        v-if="![0, 3, 4, 5, 9].includes(status)"
        overlayClassName="pre-audit-detail-overlay"
        :getPopupContainer="getPopupContainer"
        class="download"
      >
        <a-menu
          class="download-menu"
          slot="overlay"
          @click="(event) => handleMenuClick(event)"
        >
          <a-menu-item key="1"> 导出excel </a-menu-item>
          <a-menu-item key="2"> 导出html </a-menu-item>
        </a-menu>
        <a-button icon="download" class="highlight"> 导出 </a-button>
      </a-dropdown>
      <a-button
        @click="authSubmit"
        class="highlight"
        v-if="reviewStatus == 1 && isProjectReviewDetail"
        >提交评审</a-button
      >
      <a-upload
        accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        :before-upload="beforeUpload"
        :file-list="fileList"
      ></a-upload>
    </div>
    <!-- </div> -->
    <!-- 顶部提示 有条件提示 -->
    <div
      style="width: 100%"
      class="tips-content"
      :bordered="false"
      v-if="commentContent && isProjectReviewDetail"
    >
      <a-icon type="close-circle" />提交评审被一键驳回。驳回原因：{{
        commentContent
      }}
    </div>
    <!-- 上面基础信息区域 -->
    <BaseInfo
      :dataSourceList="dataSourceList"
      :projectGroup="projectGroup"
      :topFormData="topFormData"
      :reviewInfo="reviewInfo"
      :appName="appName"
      :id="id"
    />
    <!-- 下面表格区域 -->
    <a-card style="width: 100%" class="common-page-card" :bordered="false">
      <a-row
        class="review-false"
        v-if="
          topFormData.analy_status == 'Review失败' && topFormData.error_message
        "
      >
        <div class="picture">
          <img src="@/assets/img/private/error.svg" alt />
        </div>
        <div class="title">Review失败</div>
        <div class="error-message">{{ topFormData.error_message }}</div>
        <a-button
          class="highlight re-review-btn"
          @click="reReview('task')"
          v-if="isProjectReviewDetail && versionControl !== 0"
          >重新review</a-button
        >
      </a-row>
      <a-row class="review-detail-table-block" v-else>
        <!-- 触发规则列表 -->
        <RuleList
          :errorOptions="errorOptions"
          :highRiskOptions="highRiskOptions"
          :lowRiskOptions="lowRiskOptions"
          :noRiskOptions="noRiskOptions"
          :sqlOptions="sqlOptions"
          :functionOptions="functionOptions"
          :packageOptions="packageOptions"
          :procedureOptions="procedureOptions"
          :agentOptions="agentOptions"
          @setParams="setParams"
        />
        <div
          class="divider-line"
          v-if="
            noRiskOptions.length > 0 ||
            errorOptions.length > 0 ||
            lowRiskOptions.length > 0 ||
            highRiskOptions.length > 0 ||
            sqlOptions.length > 0 ||
            functionOptions.length > 0 ||
            packageOptions.length > 0 ||
            procedureOptions.length > 0 ||
            agentOptions.length > 0
          "
        ></div>
        <!-- 审核SQL列表 -->
        <div
          :class="[
            'review-sql-table',
            errorOptions.length <= 0 &&
            lowRiskOptions.length <= 0 &&
            highRiskOptions.length <= 0
              ? 'no-risk'
              : ''
          ]"
        >
          <Table
            ref="table"
            v-bind="tableParams || {}"
            :rowSelection="rowSelection"
            @reset="reset"
            class="new-card-table"
            @selectChange="selectChange"
            :dataSource="dataSource"
          >
            <!-- 表格头上插槽 -->
            <template slot="tableTopLeft">
              <div class="review-switch" v-if="!isProcedureReviewDetail">
                <a-radio-group v-model="value" @change="onChange">
                  <a-radio-button value="sql-text">
                    {{ isProjectReviewDetail ? 'SQL文本' : 'SQL' }}
                  </a-radio-button>
                  <a-radio-button value="sql-source">
                    {{ isProjectReviewDetail ? 'SQL代码' : '代码' }}
                  </a-radio-button>
                </a-radio-group>
              </div>
              <div class="review-search">
                <InputSearch
                  :placeholder="placeholder"
                  @search="tableSearch"
                  ref="inputSearch"
                ></InputSearch>
              </div>
            </template>

            <template slot="tableTopRight">
              <div
                class="review-box"
                v-if="isSubmitFlag && isProjectReviewDetail"
              >
                <a-popconfirm
                  v-if="hasMulDataSource && jksuser !== 'jksuser'"
                  title="重新review会对SQL重新进行AI风险等级判定，是否确认?"
                  @confirm="() => eidtDataSource()"
                >
                  <a-tooltip placement="top">
                    <template slot="title">
                      <span>重新review</span>
                    </template>
                    <custom-icon type="lu-icon-re-review" class="review-btn" />
                  </a-tooltip>
                </a-popconfirm>
                <a-popconfirm
                  v-else-if="!hasMulDataSource && jksuser !== 'jksuser'"
                  title="重新review会对SQL重新进行AI风险等级判定，是否确认?"
                  @confirm="() => reReview()"
                >
                  <a-tooltip placement="top">
                    <template slot="title">
                      <span>重新review</span>
                    </template>
                    <custom-icon type="lu-icon-re-review" class="review-btn" />
                  </a-tooltip>
                </a-popconfirm>
              </div>

              <a-tooltip
                placement="top"
                v-if="isProjectReviewDetail && jksuser !== 'jksuser'"
              >
                <template slot="title">
                  <span>打标</span>
                </template>
                <div class="batch-tag-box">
                  <custom-icon
                    type="tags"
                    class="batch-tag-btn"
                    v-if="
                      status == '-1' && ['未提交', '未通过'].includes(dbaStatus)
                    "
                    @click="batchAddTag"
                  />
                </div>
              </a-tooltip>
              <a-divider
                type="vertical"
                v-if="
                  isProjectReviewDetail &&
                  (isSubmitFlag ||
                    (status == '-1' &&
                      ['未提交', '未通过'].includes(dbaStatus)))
                "
              />
            </template>
            <template slot="tableMiddle" v-if="isProjectReviewDetail || fileError.error_count">
              <div class="des" v-if="reviewStatus == 1">
                <custom-icon type="exclamation-circle" />
                <span
                  >存在高风险SQL，请评估整改，或为SQL添加标签和备注。(<span
                    @click="addGuide"
                    >操作指南</span
                  >)</span
                >
              </div>
              <div
                class="des"
                v-if="fileError && fileError.error_count > 0"
                style="padding: 8px 0"
              >
                <custom-icon type="exclamation-circle" />
                <span
                  >{{ fileError.error + '。' }} (<span @click="onOpen"
                    >查看详情</span
                  >)</span
                >
              </div>
            </template>
            <!-- table插槽 -->
            <template slot="sql_text" slot-scope="{ record, text }">
              <a class="sql-text-box" @click="toDetail(text, record, $event)">
                <custom-icon
                  class="sql-text-icon"
                  :type="iconMap[record.sql_format]"
                  mode="svg"
                />
                <LimitLabel
                  :label="text || ''"
                  mode="ellipsis"
                  :needCopy="true"
                  :ignoreFirstLineComment="true"
                  :popoverProps="{
                    overlayClassName: 'home-sqlreview-small-size'
                  }"
                ></LimitLabel>
                <LabelCard
                  v-if="record.label_obj_id"
                  ref="labelCard"
                  mode="icon"
                  :id="record.label_obj_id"
                  :labelStatus="record.label_status"
                  @refresh="refresh"
                />
              </a>
            </template>
            <!-- 风险等级 -->
            <div class="risk-content" slot="risk" slot-scope="{ record, text }">
              <RuleLabel
                :aiComment="record.ai_comment || {}"
                :value="text"
                v-if="record.ai_status !== 0"
              />
              <custom-icon
                type="lu-icon-loading"
                class="loading"
                v-if="record.ai_status == 0"
              />
            </div>
            <div slot="review_status" slot-scope="{ text, record }">
              <custom-icon
                v-if="record.ai_status !== 0"
                :class="text == 1 ? 'review-pass' : 'review-no-pass'"
                :type="text == 1 ? 'lu-icon-success' : 'lu-icon-disable'"
              />
              <span
                v-if="record.ai_status !== 0 && isProjectReviewDetail"
                style="color: #a1a1aa; fontsize: 12px; marginleft: 4px"
                >{{ record.review_desc }}</span
              >
              <custom-icon
                v-if="record.ai_status == 0"
                type="lu-icon-loading"
                class="loading"
              />
            </div>
          </Table>
        </div>
      </a-row>
      <!-- 审核弹窗 -->
      <Audit
        ref="audit"
        @refresh="refresh"
        @add="addGuide"
        :isAdd="true"
      ></Audit>
      <!-- 打标弹窗 -->
      <TagModal ref="tag" @saveLabel="onSaveLabel"></TagModal>
      <!-- 错误信息查看详情弹窗 -->
      <DetailModal ref="detailModal"></DetailModal>
      <!-- SQL备注弹窗 -->
      <!-- <NoteModal
        ref="note"
        @saveNote="onSaveNote"
      ></NoteModal> -->
      <!-- 指定数据源弹窗 -->
      <DataSourceModal
        ref="dataSource"
        @reviewRetry="reReview"
        :projectId="projectId"
      ></DataSourceModal>
    </a-card>
  </div>
</template>

<script>
import step from './step';
import guide from '@/utils/driver/driver.js';
import {
  exportReviewDetail,
  batchUpdateDetail,
  getUpdateProgress,
  reviewRetry,
  beforeReviewDetailPage,
  saveLabel,
  deleteLabel,
  saveSqlMap
} from '@/api/home';
import { getLogout } from '@/api/login';
import Table from '@/components/Table';
import LimitTags from '@/components/LimitTags';
import Form from '@/components/Form';
import Status from '@/components/Biz/Status';
import LimitLabel from '@/components/LimitLabel';
import RuleLabel from './components/ruleLabel';
import RuleList from './components/RuleList';
import BaseInfo from './components/BaseInfo';
import DetailModal from '@/components/Biz/ReviewDetail/DetailModal';
import CheckboxGroup from '@/components/CheckboxGroup';
import InputSearch from '@/components/InputSearch';
import MarkdownViewer from '@/components/Markdown/viewer';
import StatusTag from '@/components/Biz/Status/Tag';
import TagModal from '@/components/Biz/ReviewDetail/TagModal';
// import NoteModal from '@/components/Biz/ReviewDetail/NoteModal';
import DataSourceModal from './components/DataSourceModal';
import Audit from '@/components/Biz/AuditModel';
import LabelCard from '@/components/Biz/LabelCard';
import config from './config';
import common from '@/utils/common';
import bodyMinWidth from '@/mixins/bodyMinWidth';

export default {
  name: 'project-review-detail',
  components: {
    Table,
    Status,
    RuleLabel,
    LimitTags,
    LimitLabel,
    CheckboxGroup,
    Form,
    InputSearch,
    StatusTag,
    MarkdownViewer,
    TagModal,
    Audit,
    DataSourceModal,
    RuleList,
    BaseInfo,
    LabelCard,
    DetailModal
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  data() {
    this.config = config(this);
    const id = this.$route.params.id;
    this.searchData = {};
    const jksuser = this.$store.state.account.user.name;
    return {
      id,
      step,
      jksuser,
      dataSource: [],
      tableParams: {
        url: '/sqlreview/review/review-detail-list/',
        method: 'post',
        reqParams: {
          review_id: this.$route.params.id,
          is_status: '0'
        },
        columns: this.config.columns.filter(item => item.key !== 'source_text'),
        rowKey: 'id',
        isInitReq: false,
        needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        scroll: { x: 700 },
        pagination: {},
        ignoreFastReqTips: true,
        loaded: this.onTableLoaded
      },
      selectedRowKeys: [],
      formData: {},
      appName: '',
      status: null,
      reviewStatus: null,
      dbaStatus: null,
      projectGroup: [],
      fileList: [],
      detail: 'homedetail',
      topFormData: {}, // 上方表单数据
      spinning: false,
      noRiskOptions: [],
      lowRiskOptions: [],
      highRiskOptions: [],
      errorOptions: [],
      sqlOptions: [],
      functionOptions: [],
      packageOptions: [],
      procedureOptions: [],
      agentOptions: [],
      placeholder: '搜索SQL/ID',
      reviewInfo: {},
      analyStatus: null,
      statusColor: {
        '0': '#ff9f28',
        '1': '#14c55f',
        '-1': '#f73232',
        '9': '#f73232',
        '2': '#f73232',
        '3': '#ff9f28',
        '4': '#ff9f28',
        '5': '#ff9f28'
      },
      iconMap: {
        xml: 'lu-icon-xml',
        sql: 'lu-icon-sql',
        java: 'lu-icon-java'
      },
      labelColor: {
        0: '#F29339',
        1: '#3A974C',
        '-1': '#EF6173'
      },
      percent: 0,
      isSubmitFlag: false,
      hasMulDataSource: false,
      visible: false,
      sqlType: 'SQL文本',
      routeName: 'project-review-review',
      value: 'sql-text',
      projectId: null,
      count: null, // 表格总数
      includes: [], // 表格单选所有值
      excludes: [], // 表格全选后排除的值
      pageKeys: [], // 表格当前页所有key
      tableCheckAll: false, // 表格全选状态true/false
      tableIndeterminate: false, // 表格半选状态true/false
      isSelectAll: false,
      timestamp: null,
      commentContent: '',
      dataSourceList: [],
      versionControl: null,
      fileError: {}
    };
  },
  computed: {
    is_status() {
      const _key = 'project-review-detail';
      const data = this.$store.state.common.searchCache[_key];
      return data && data.is_status ? data.is_status === '1' : false;
    },
    isVirtualLogin() {
      const flag = _.get(this.$store.state.account, 'user.virtual_login');
      return flag;
    },
    rowSelection() {
      return {
        type: 'checkbox',
        columnWidth: '40',
        columnTitle: (
          <a-checkbox
            indeterminate={this.tableIndeterminate}
            checked={this.tableCheckAll}
            onChange={this.onCheckAllChange}
          />
        ),
        getCheckboxProps: record => ({
          // 选择框的默认属性配置
          props: {
            disabled: this.jksuser == 'jksuser'
          }
        }),
        selectedRowKeys: this.selectedRowKeys,
        onSelect: this.onSelect
      };
    },
    isProjectReviewDetail() {
      const name = this.$route.name;
      return name == 'project-review-detail';
    },
    isProcedureReviewDetail() {
      const name = this.$route.name;
      return name == 'procedure-review-detail';
    }
  },
  mounted() {
    this.getbeforeReviewDetailData();
  },
  created() {
    // this.setNavi();
  },
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    guide(this.step).destroy();
  },
  methods: {
    getPopupContainer() {
      return document.getElementById('rootContent');
    },
    handleMenuClick(e) {
      this.download(e.key);
    },
    // 提交评审
    authSubmit() {
      this.$refs.audit.show(this.id);
    },
    onTableLoaded(req, res) {
      const resData = _.get(res, 'data.data');
      const results = resData.results;
      const ids = results.map(item => item.id);
      this.pageKeys = ids;
      this.percent = resData.label_rate;
      this.dataSource = results;
      this.count = resData.count;
      this.timestamp = resData.timestamp;
      if (this.isSelectAll) {
        ids.forEach(item => {
          if (!this.excludes.includes(item)) {
            this.selectedRowKeys.push(item);
          }
        });
      }
    },
    toLogin() {
      getLogout().then(res => {
        if (_.get(res, 'data.code') == 0) {
          let data = _.get(res, 'data.data');
          // 跳转回登录
          window.Login.go(data);
        }
      });
    },
    onChange(e) {
      this.value = e.target.value;
      const columns =
        this.value == 'sql-text'
          ? this.config.columns.filter(item => item.key !== 'source_text')
          : this.config.columns.filter(item => item.key !== 'sql_text');
      this.$set(this.tableParams, 'columns', columns);
    },
    toDetail(text, record, e) {
      const searchData = this.$refs.table.searchParams;
      const routeObj = {
        'procedure-review-detail': 'procedure-review-review',
        'project-review-detail': 'project-review-review',
        'file-review-detail': 'file-review-review'
      };
      this.$router.push({
        name: routeObj[this.routeName],
        params: { id: record.id, searchData: searchData },
        query: this.$route.query
      });
    },
    download(key) {
      this.$showLoading({
        tips: `下载中...`
      });
      const searchData = {
        ...this.$refs.table.searchParams
      };
      exportReviewDetail({
        sqlId: this.id,
        export_file_type: key,
        ...searchData
      })
        .then(res => {
          common.downLoad(this, res);
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    beforeUpload(file) {
      if (
        file &&
        ['.xls', '.xlsx'].filter(item => file.name.endsWith(item)).length <= 0
      ) {
        this.$message.error('请选择正确格式！');
      } else {
        this.batchUpdate(file);
      }

      return false;
    },
    batchUpdate(file) {
      this.$showLoading({
        tips: `上传中...`
      });
      let formData = new FormData();
      formData.append('file', file);
      formData.append('task_name', 'review_detail_from_excel');
      batchUpdateDetail(formData)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            const data = _.get(res, 'data.data') || {};
            if (data.task_id != null) {
              this.updateProgress(data, '批量更新');
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    updateProgress(params, action) {
      this.$showLoading({
        tips: `正在${action}...`
      });
      getUpdateProgress(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const data = _.get(res, 'data.data') || {};
            const { task_status: taskStatus } = data;
            if (taskStatus == 2) {
              this.$hideLoading({ tips: `${action}成功` });
              this.$refs.table.refresh(null, this.searchData);
            } else if (taskStatus == 4) {
              this.$hideLoading({
                method: 'error',
                tips: `${action}失败`
              });
            } else {
              this.timer = setTimeout(() => {
                this.updateProgress(params, action);
              }, 5000);
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    reReview(data) {
      if (
        data !== 'task' &&
        _.isEmpty(this.selectedRowKeys) &&
        !this.isSelectAll
      ) {
        this.$message.warning('未选择重新review数据！');
        return;
      }
      const table = this.$refs.table;
      const searchParams = table ? table.searchParams : {};
      const params =
        data == 'task'
          ? { review_id: this.id }
          : {
              review_id: this.id,
              retry_list: this.isSelectAll ? this.excludes : this.includes,
              re_audit_list: data,
              is_select_all: this.isSelectAll ? 1 : 0,
              timestamp: this.timestamp,
              ...searchParams
            };
      this.$showLoading();
      reviewRetry(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            this.getbeforeReviewDetailData();
            this.reset();
            this.$refs.table && this.$refs.table.refreshClear();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 查询
    search(data) {
      const { table } = this.$refs;
      this.searchData = { ...data };
      if (this.searchData.status && this.searchData.status.length > 0) {
        this.searchData.status = this.searchData.status.join(',');
      }
      table.refresh(null, this.searchData);
    },
    // 重置
    reset(data) {
      const inputSearch = this.$refs.inputSearch;
      inputSearch && (inputSearch.searchVal = '');
      this.isSelectAll = false;
      this.tableCheckAll = false;
      this.tableIndeterminate = false;
      this.selectedRowKeys = [];
      this.count = null;
      this.includes = [];
      this.excludes = [];
      this.pageKeys = [];
    },
    refresh() {
      const { table } = this.$refs;
      table.refreshKeep();
      this.getbeforeReviewDetailData();
    },
    // 返回
    toBack() {
      // this.$router.push({
      //   // name: 'home-sqlreview'
      // });
      const { navi = [] } = this.$route.meta || {};
      const prevKey = navi[navi.length - 2];
      const prevItem = window.routesMap[prevKey];
      let path = '';
      if (prevItem) {
        if (prevKey === 'timing-review-tasklist') {
          path = prevItem.path.replace(':id', this.$route.query.task_id);
        } else {
          path = prevItem.path;
        }
      }
      if (path) {
        this.$router.push({
          path
        });
        return;
      }
      this.$router.back();
    },
    setNavi() {
      common.setNavis(this, (key, sourcePath) => {
        let path = null;
        if (key === 'timing-review-tasklist') {
          path = sourcePath.replace(':id', this.$route.query.task_id);
          return path;
        }
        return path;
      });
    },
    // 获取事前审核页面数据
    getbeforeReviewDetailData() {
      this.spinning = true;
      const obj = { review_id: this.id };
      beforeReviewDetailPage(obj)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            let obj = res.data.data.header;
            this.commentContent = obj.comment_content || '';
            this.dataSourceList = _.get(res, 'data.data.datasource_list');
            this.analyStatus = obj.analy_status;
            this.projectGroup = obj.project_group;
            this.status = obj.status;
            this.reviewStatus = obj.review_status;
            this.dbaStatus = obj.dba_status;
            this.topFormData = this.statusToText(obj);
            this.reviewInfo = _.get(res, 'data.data.review_info');
            this.hasMulDataSource = _.get(res, 'data.data.has_mul_data_source');
            this.projectId = _.get(res, 'data.data.project_id');
            let formData = _.get(res, 'data.data');
            this.appName = formData.project_name;
            this.isSubmitFlag = _.get(res, 'data.data.is_submit_flag');
            this.versionControl = _.get(res, 'data.data.version_control');
            this.fileError = _.get(res, 'data.data.file_error');
            const sqlSourceList = [];
            const devIdList = [];
            formData.sql_source_list.map(item => {
              sqlSourceList.push({ label: item, value: item });
            });
            formData.dev_id_list.map(item => {
              devIdList.push({ label: item, value: item });
            });

            const resFormData = {
              url: formData.url,
              dev_id_list: devIdList,
              sql_source_list: sqlSourceList,
              project_name: formData.project_name,
              is_svn: formData.is_svn
            };
            this.$set(this, 'formData', resFormData);

            let arr = _.get(res, 'data.data.header.rule_sort_dict');
            const noRiskArr = arr.no_risk;
            const lowRiskArr = arr.low_risk;
            const highRiskArr = arr.high_risk;
            const errorArr = arr.error;
            const sqlArr = arr.sql_source;
            const functionArr = arr.function;
            const packageArr = arr.package;
            const procedureArr = arr.procedure;
            this.noRiskOptions = [];
            this.lowRiskOptions = [];
            this.highRiskOptions = [];
            this.errorOptions = [];
            this.sqlOptions = [];
            this.functionOptions = [];
            this.packageOptions = [];
            this.procedureOptions = [];
            this.agentOptions = [];

            noRiskArr &&
              noRiskArr.forEach(item => {
                this.noRiskOptions.push({
                  label: item.name,
                  value: item.id,
                  num: item.rule_count
                });
              });
            lowRiskArr &&
              lowRiskArr.forEach(item => {
                this.lowRiskOptions.push({
                  label: item.name,
                  value: item.id,
                  num: item.rule_count
                });
              });

            highRiskArr &&
              highRiskArr.forEach(item => {
                this.highRiskOptions.push({
                  label: item.name,
                  value: item.id,
                  num: item.rule_count
                });
              });

            errorArr &&
              errorArr.forEach(item => {
                this.errorOptions.push({
                  label: item.name,
                  value: item.id,
                  num: item.rule_count
                });
              });

            sqlArr &&
              sqlArr.forEach(item => {
                if (res.data.data.project_review_type == 'agent') {
                  this.agentOptions.push({
                    label: item.name,
                    value: item.file_path,
                    num: item.count
                  });
                } else {
                  this.sqlOptions.push({
                    label: item.name,
                    value: item.file_path,
                    num: item.count,
                    error: item.error
                  });
                }
              });

            functionArr &&
              functionArr.forEach(item => {
                this.functionOptions.push({
                  label: item.name,
                  value: item.file_path,
                  num: item.count
                });
              });

            packageArr &&
              packageArr.forEach(item => {
                this.packageOptions.push({
                  label: item.name,
                  value: item.file_path,
                  num: item.count
                });
              });

            procedureArr &&
              procedureArr.forEach(item => {
                this.procedureOptions.push({
                  label: item.name,
                  value: item.file_path,
                  num: item.count
                });
              });

            this.spinning = false;
          } else {
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 分析状态数字转文字
    statusToText(obj) {
      for (let key in obj) {
        if (key === 'analy_status') {
          if (obj[key] === 0) {
            // obj[key] = '进行中';
            obj[key] = '队列中';
          } else if (obj[key] === 1) {
            obj[key] = '通过';
          } else if (obj[key] === -1 || obj[key] === 2) {
            obj[key] = '未通过';
          } else if (obj[key] === 3) {
            obj[key] = '拉取代码中';
          } else if (obj[key] === 4) {
            obj[key] = '代码解析中';
          } else if (obj[key] === 5) {
            obj[key] = '代码审核中';
          } else if (obj[key] === 9) {
            obj[key] = 'Review失败';
          }
        }
      }
      return obj;
    },
    setParams(checked, data, extra) {
      // checked null表示单选， true/false表示全选/反选
      if (checked !== null) {
        this.$set(this.tableParams, 'reqParams', {
          ...this.tableParams.reqParams,
          rule_id: checked ? [] : data,
          _t: +new Date()
        });
        if (extra) {
          this.$set(this.tableParams, 'reqParams', {
            ...this.tableParams.reqParams,
            rule_id: checked ? [] : data,
            file_path: checked ? [] : extra,
            _t: +new Date()
          });
          this.$set(this.$refs.table.searchParams, 'file_path', extra);
        }
        this.$set(this.$refs.table.searchParams, 'rule_id', data);
      } else {
        this.$set(this.tableParams, 'reqParams', {
          ...this.tableParams.reqParams,
          rule_id: data,
          _t: +new Date()
        });
        if (extra) {
          this.$set(this.tableParams, 'reqParams', {
            ...this.tableParams.reqParams,
            rule_id: data,
            file_path: extra,
            _t: +new Date()
          });
          this.$set(this.$refs.table.searchParams, 'file_path', extra);
        }
        this.$set(this.$refs.table.searchParams, 'rule_id', data);
      }
    },
    // 打标
    addSqlTag(record) {
      this.$refs.tag.show(record);
    },
    // 打备注
    // addNote(record) {
    //   this.$refs.note.show(record);
    // },
    tableSearch(params) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, {
        sql_text: params
      });
      table.refresh();
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    // 用户手动选择/取消选择某列的回调
    onSelect(record, selected) {
      if (selected) {
        this.includes.push(record.id);
        this.excludes = this.excludes.filter(item => item !== record.id);

        this.selectedRowKeys.push(record.id);

        // this.tableIndeterminate = true;
        this.tableCheckAll = this.includes.length == this.count;
        this.tableIndeterminate = this.includes.length !== this.count;
      }
      if (!selected) {
        this.excludes.push(record.id);
        this.includes = this.includes.filter(item => item !== record.id);

        this.selectedRowKeys = this.selectedRowKeys.filter(
          item => item !== record.id
        );

        this.tableCheckAll = false;
        this.tableIndeterminate = this.excludes.length !== this.count;
      }
      if (this.isSelectAll && _.isEmpty(this.excludes)) {
        this.tableIndeterminate = false;
        this.tableCheckAll = true;
      }
    },
    // 表格全选
    onCheckAllChange(e) {
      const checked = _.get(e, 'target.checked');
      this.tableCheckAll = checked;
      this.isSelectAll = checked;
      this.tableIndeterminate = false;
      if (checked) {
        const arr = [...this.selectedRowKeys, ...this.pageKeys];
        this.selectedRowKeys = [...new Set(arr)];
        this.excludes = [];
      }
      if (!checked) {
        this.selectedRowKeys = [];
      }
    },
    getData() {
      return this.selectedRowKeys;
    },
    // 保存打标
    onSaveLabel(data) {
      this.$showLoading();
      const { searchParams } = this.$refs.table;
      // const ids = !_.isEmpty(this.selectedRowKeys)
      //   ? this.selectedRowKeys
      //   : [data.id];
      const params = {
        label_attribute: data.label_attribute,
        permanent_day: data.permanent_day,
        is_select_all: this.isSelectAll ? 1 : 0,
        id: this.isSelectAll ? this.excludes : this.includes,
        count: this.count,
        timestamp: this.timestamp,
        review_id: this.$route.params.id,
        ...searchParams
      };
      saveLabel(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            if (!_.isEmpty(this.selectedRowKeys)) {
              this.$refs.table.refreshClear();
            } else {
              this.$refs.table.refreshKeep();
            }
            this.reset();
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 保存SQL备注
    onSaveNote(data) {
      this.$showLoading();
      saveSqlMap(data)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$refs.table.refreshKeep();
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    batchAddTag() {
      if (_.isEmpty(this.selectedRowKeys) && !this.isSelectAll) {
        this.$message.warning('未选择打标数据！');
        return;
      }
      this.$refs.tag.show();
    },
    // 删除打标
    onDeleteLabel(id) {
      this.$showLoading();
      deleteLabel({ id })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$refs.table.refreshKeep();
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 页面指导
    addGuide() {
      guide(this.step).drive();
    },
    // 打开详情弹窗
    onOpen() {
      this.$refs.detailModal.show(this.id);
    },
    handleMenuNode(e) {
      this.sqlType = e.key == 'sql_text' ? 'SQL文本' : 'SQL代码';
      this.visible = false;
    },
    // 指定数据源
    eidtDataSource() {
      if (_.isEmpty(this.selectedRowKeys)) {
        this.$message.warning('未选择重新review数据！');
        return;
      }
      this.$refs.dataSource.show();
    }
  },
  watch: {
    '$route.name': {
      handler(newVal) {
        this.routeName = newVal;
      },
      immediate: true
    }
  }
};
</script>

<style lang="less">
// popover美化
.ant-popover {
  &.home-sqlreview-small-size {
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 600px;
        max-height: 300px;
      }
    }
  }
}
.ant-popover {
  &.home-sqlreview-label-type {
    .ant-popover-inner {
      .ant-popover-inner-content {
        max-width: 400px;
        max-height: 200px;
        .text {
          padding: 2px 16px;
          border-radius: 16px;
          border: 1px solid #e4e4e7;
          display: inline-block;
          &.audit-status-red {
            background: #e71d36;
            border: 1px solid #e71d36;
            > span {
              color: #fff;
            }
          }
          > span {
            font-size: 12px;
            color: #27272a;
          }
          .anticon {
            display: none;
            color: #fff;
            width: 16px;
            height: 16px;
            line-height: 18px;
            font-size: 12px;
            border-radius: 50%;
            margin-left: 4px;
            border: none;
          }
          &:hover {
            cursor: pointer;
            .anticon {
              display: inline-flex;
              background: #008adc;
              justify-content: center;
              align-items: center;
            }
          }
        }

        .label-type-content {
          > div {
            display: flex;
            justify-content: space-between;
            padding: 4px 10px;
            > span {
              font-size: 12px;
              font-weight: 400;
              &:first-child {
                color: #27272a;
              }
              &:last-child {
                color: #a1a1aa;
              }
              &.status-0 {
                color: #f29339;
              }
              &.status-1 {
                color: #4cbb3a;
              }
              &.status-2 {
                color: #e71d36;
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less" scoped>
/deep/.ant-table-pagination.ant-pagination {
  margin: 24px 0 0 0;
}
.form-box {
  .form-item {
    // height: 28px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #27272a;
    line-height: 22px;
    font-weight: 400;
    display: flex;
    margin-bottom: 4px;
    .name {
      width: 66px;
      display: flex;
      justify-content: flex-end;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
.project-info {
  display: flex;
  align-items: center;
  padding: 0 8px;
  .circle {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-top: 2px;
    border-radius: 50%;
  }
  .title-project-name {
    display: inline-block;
    padding: 0 8px;
    font-size: 12px;
    line-height: 28px;
  }
  .custom-icon {
    color: @primary-color;
  }
}
.preaudit-detail {
  .sql-detail {
    .frame-fixed-top-left(15px, 168px);
    background: #ffffff;
    border: 1px solid #e4e4e7;
    border-radius: 24px;
    display: block;
    &.procedure {
      .frame-fixed-top-left(15px, 198px);
    }
  }
  .frame-button-wrapper {
    display: flex;
    .ant-btn {
      border-radius: 8px;
      font-size: 14px;
      color: #008adc;
      font-weight: 600;
      margin-left: 8px;
    }
    .review-detail-btn {
      color: #ffffff;
    }
  }
  .common-page-card {
    margin-bottom: 12px;
    border-radius: 8px;
    padding: 24px;
  }
  .tips-content {
    margin-bottom: 12px;
    border-radius: 8px;
    padding: 16px 24px;
    background-color: rgba(231, 29, 54, 0.1);
    word-break: break-all;
    .anticon {
      color: rgb(231, 29, 54);
      margin-right: 8px;
    }
  }

  .review-false {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 130px;
    margin-bottom: 400px;
    .title {
      font-size: 24px;
      color: #27272a;
      font-weight: 400;
      margin: 20px 0 27px 0;
    }
    .error-message {
      background: #fdeff1;
      border-radius: 6px;
      font-weight: 400;
      font-size: 14px;
      padding: 8px 16px;
      color: #e71d36;
      text-align: center;
    }
    .re-review-btn {
      margin-top: 24px;
      color: #008adc;
      border-color: #7fc4ed;
      &:hover {
        color: #25a7e8;
        border-color: #008adc;
      }
    }
  }
  .review-detail-table-block {
    display: flex;
    .divider-line {
      width: 1px;
      height: 690px;
      background: #e4e4e7;
    }
    .review-sql-table {
      width: 75%;
      padding-left: 24px;
      &.no-risk {
        width: 100%;
      }
      .title {
        display: flex;
        margin-bottom: 8px;
        align-items: center;
        > div {
          font-size: 16px;
          color: #27272a;
          font-weight: 600;
        }
      }
      /deep/.custom-table {
        .search-area-wrapper {
          height: 32px;
          line-height: 32px;
          display: flex;
          justify-content: space-between;
          margin: 0 0 16px 0;
          .custom-table-top-left {
            display: flex;
            justify-content: space-between;
            flex-grow: 1;
            .search-area-simple {
              display: none;
            }
            .review-search {
              display: flex;
              align-items: center;
              margin-right: 8px;
              .custom-input-search {
                background: #ffffff;
                .ant-input-affix-wrapper {
                  .ant-input {
                    width: 300px;
                    height: 30px;
                    background: #ffffff;
                    border: 1px solid rgba(161, 161, 161, 0.5);
                  }
                  .ant-input-suffix {
                    right: 8px;
                    color: rgba(24, 24, 27, 0.5);
                  }
                }
              }
            }
          }
          .custom-table-top-right {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
            flex-grow: 0;
            .batch-tag-box {
              .batch-tag-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 26px;
                height: 26px;
                &:hover {
                  color: #fff;
                  background: #4ec3f5;
                  border-radius: 50%;
                }
              }
            }
            .review-box {
              margin-left: 8px;
              .review-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 26px;
                height: 26px;
                &:hover {
                  color: #fff;
                  background: #4ec3f5;
                  border-radius: 50%;
                  cursor: pointer;
                }
              }
            }
            .custom-table-tools {
              padding-bottom: 2px;
            }
          }
        }
        .des {
          .anticon {
            font-size: 14px;
            color: #e71d36;
            margin-right: 4px;
          }
          > span {
            font-size: 14px;
            color: #27272a;
            font-weight: 400;
            > span {
              color: #008adc;
              cursor: pointer;
            }
          }
        }
        .ant-table-thead > tr > th .anticon-filter,
        .ant-table-thead > tr > th .ant-table-filter-icon {
          top: -3px;
        }
        .sql-text-box {
          display: flex;
          align-items: center;
          // max-width: 500px;
          .sql-text-icon {
            display: inline-block;
            margin-top: 4px;
            font-size: 16px;
            margin-right: 8px;
            opacity: 0.5;
          }
          .label-icon {
            margin-left: 8px;
          }
        }
        .limit-label {
          > pre {
            font-weight: 400;
            color: #008adc;
          }
        }
        .review-no-pass {
          font-size: 16px;
          color: #e71d36;
        }
        .review-pass {
          font-size: 16px;
          color: #3a974c;
        }
        .risk-content {
          display: flex;
          align-items: center;
          .anticon {
            margin-left: 8px;
          }
        }
        @keyframes rotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
        .loading {
          animation: rotate 2s linear infinite;
        }
      }
    }
  }
}
@media screen and (max-width: 1250px) {
  .preaudit-detail {
    .review-detail-info {
      .review-detail-main-info {
        .right-block {
          .right-block-content {
            > div {
              margin-left: 16px;
              & span:first-child {
                font-size: 14px;
              }
              & span:last-child {
                font-size: 20px;
              }
              .error {
                font-size: 20px;
              }
              .pass {
                font-size: 20px;
              }
            }
            .ant-divider-vertical {
              margin-left: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
