export default function (ctx) {
  const fields = [
    {
      type: 'Input',
      label: '测试input',
      key: 'testSearch'
    },
    {
      type: 'Select',
      label: '测试select',
      key: 'testSelect',
      props: {
        url: '/api/home/<USER>/select',
        reqParams: {
          from: 'pageListAllConfig'
        }
      }
    },
    {
      type: 'Input',
      label: '项目编号|项目名|app',
      key: 'langInput'
    },
    {
      type: 'Input',
      label: '测试input',
      key: 'testSearch1'
    },
    {
      type: 'Select',
      label: '测试select',
      key: 'testSelect1',
      props: {
        url: '/api/home/<USER>/select',
        reqParams: {
          from: 'pageListAllConfig'
        }
      }
    },
    {
      type: 'Input',
      label: '项目编号',
      key: 'langInput1'
    }
  ];
  return {
    fields
  };
};