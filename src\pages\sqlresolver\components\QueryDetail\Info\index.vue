<template>
  <div class="psc-right-query-detail-info">
    <template v-for="(item, index) in list">
      <!-- 成功 -->
      <div class="psc-rqdi-item psc-rqdi-item-workorder" :key="index" v-if="item.work_type">
        <div class="sql-text">
          <custom-icon type="lu-icon-success" />新建工单成功
        </div>
        <div class="work-detail">
          工单名称：{{item.work_name}}
          <a @click="toWorkOrderDetail(item)">查看详情</a>
        </div>
      </div>
      <!-- 成功 -->
      <div class="psc-rqdi-item psc-rqdi-item-success" :key="index" v-else-if="item.result">
        <!-- <pre class="sql-text">{{item.sql_text}}</pre> -->
        <LimitLabel mode="ellipsis" :label="item.sql_text || ''" :block="true"></LimitLabel>
        <div class="sql-result-info-item sql-status">> OK</div>
        <div class="sql-result-info-item sql-time">> {{item.elapsed_time}}ms</div>
        <div class="sql-result-info-item sql-affect">> 影响: {{item.affect_rows || 0}}条</div>
      </div>
      <!-- 失败 -->
      <div class="psc-rqdi-item psc-rqdi-item-error" :key="index" v-else>
        <!-- <div class="sql-text">{{item.sql_text}}</div> -->
        <template v-if="item.description">
          <div class="sql-error-description">{{item.description}}</div>
          <div class="sql-error-suggestion">【解决方法】{{item.suggestion}}</div>
        </template>
        <template v-if="item.message">
          <div class="sql-error-description">{{item.message}}</div>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
import LimitLabel from '@/components/LimitLabel';
export default {
  components: { LimitLabel },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  computed: {},
  mounted() {},
  methods: {
    toWorkOrderDetail(data) {
      const url = this.$router.resolve({
        path: `/workOrder/detail/${data.id}?workType=${data.work_type || ''}&workOrderType=${data.work_order_type || ''}`
      });
      window.open(url.href, '_blank');
    }
  }
};
</script>

<style lang="less" scoped>
.psc-right-query-detail-info {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  padding: 12px 0;
  overflow: auto;
  .psc-rqdi-item {
    padding-bottom: 12px;
    margin-bottom: 12px;

    &:not(:last-child) {
      border-bottom: 1px solid @border-color;
    }

    .sql-text {
      margin-bottom: 4px;
    }
    .sql-result-info-item {
      // font-size: 12px;
      line-height: 24px;
    }

    .sql-error-description {
      color: #d9363e;
    }
  }

  .psc-rqdi-item-workorder {
    .sql-text {
      .custom-icon {
        margin-right: 8px;
      }
    }
    .work-detail {
      padding-left: 20px;
    }
  }
}
</style>
