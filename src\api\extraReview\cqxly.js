import Http from '@/utils/request'

// cq/效率云 下载模板接口
export function downloadReviewTemplate(params = {}) {
  return Http({
    url: `/sqlreview/review/download_review_template`,
    method: 'get',
    params: params,
    responseType: 'blob'
  });
}

// 发起cq/效率云、cc审核、item审核 审核接口
export function reviewByFlownumberCreate(data = {}) {
  return Http({
    url: `/sqlreview/review/review_by_flownumber`,
    method: 'post',
    data
  });
}

// 定时审核 创建定时任务
export function regularTaskCreate(data = {}) {
  return Http({
    url: `/sqlreview/review/regular_task/create`,
    method: 'post',
    data
  });
}

// 定时审核 编辑定时任务
export function regularTaskEdit(data = {}) {
  return Http({
    url: `/sqlreview/review/regular_task/edit`,
    method: 'post',
    data
  });
}

// 定时审核 删除定时任务
export function regularTaskDelete(data = {}) {
  return Http({
    url: `/sqlreview/review/regular_task/delete`,
    method: 'post',
    data
  });
}
// 效率云/CQ  批量上传
export function uploadFileReview(data = {}) {
  return Http({
    url: `/sqlreview/review/upload_file_review`,
    method: 'post',
    data
  });
}
