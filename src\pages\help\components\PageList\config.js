
export default function (ctx) {
  const btns = [
    {
      label: '测试按钮',
      color: 'green',
      callback: (pctx) => {
        ctx.btnTest();
        // console.log(pctx)
        // 模拟请求后刷新table
        ctx.$showLoading();
        setTimeout(() => {
          ctx.$hideLoading();
          pctx.$refs.table.refresh();
        }, 3000);
      }
    }
  ];
  const searches = [
    {
      type: 'Input',
      placeholder: '测试input',
      key: 'testSearch'
    },
    {
      type: 'Select',
      placeholder: '测试select',
      key: 'testSelect',
      width: 200,
      props: {
        options: [
          {
            label: '1111111',
            value: '1'
          },
          {
            label: '2222222',
            value: '2'
          },
          {
            label: '3333333',
            value: '3'
          }
        ]
      }
    },
    {
      type: 'Input',
      placeholder: '项目编号|项目名|app|创建人UM',
      key: 'langInput',
      width: 250
    }
  ];
  const columns = [
    {
      dataIndex: 'name',
      key: 'name',
      slots: { title: 'customTitle' },
      scopedSlots: { customRender: 'name' },
      sorter: true
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age'
    },
    {
      title: 'Address',
      dataIndex: 'addressName',
      key: 'address'
    },
    {
      title: 'Tags',
      key: 'tags',
      dataIndex: 'tags',
      scopedSlots: { customRender: 'tags' }
    },
    {
      title: 'Action',
      key: 'action',
      scopedSlots: { customRender: 'action' }
    }
  ];
  return {
    columns,
    btns,
    searches
  };
};
