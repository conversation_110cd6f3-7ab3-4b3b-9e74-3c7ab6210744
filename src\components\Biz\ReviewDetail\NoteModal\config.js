import { maxFrequencyDynamicForm } from '@/api/home';
let options = [];
maxFrequencyDynamicForm().then((res) => {
  if (_.get(res, 'data.code') == 0) {
    options = res.data.data.results.map((item) => {
      return {
        label: item.element_name,
        value: item.element_value
      };
    });
  }
});
const sqlOptions = [
  { label: '低档（低于1000条/天）', value: 'low' },
  { label: '中档（1000-10000条/天）', value: 'middle' },
  { label: '高档（大于10000条/天）', value: 'high' }
];

export default function (ctx) {
  const fields = () => {
    return [
      {
        type: 'RadioGroup',
        label: '峰值调用频率',
        key: 'sqlmap_max_frequency',
        icon: 'line-chart',
        props: {
          options: options
        },
        width: 'auto',
        visible: true,
        slots: [
          { key: 'max_private', wrapperStyle: { display: 'inline-block' } }
        ]
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      {
        type: 'TableEdit',
        label: '每月数据量增加',
        key: 'sqlmap_monthly_increase',
        getDataMethod: 'getData',
        resetFieldsMethod: 'resetFields',
        initialValue: [],
        icon: 'pie-chart',
        props: {
          columns: [
            {
              title: '表名',
              dataIndex: 'table_name',
              key: 'table_name',
              width: 300
            },
            {
              title: 'SCHEMA名',
              dataIndex: 'schema',
              key: 'schema',
              width: 300
            },
            {
              title: '数据增加档数',
              dataIndex: 'frequency',
              key: 'frequency',
              width: 300,
              scopedSlots: { customRender: 'frequency' }
            }
          ],
          editConfig: {
            frequency: (row, record = {}) => {
              return {
                type: 'Select',
                props: {
                  options: sqlOptions,
                  placeholder: '请选择'
                },
                rules: [
                  // { required: true, message: '该项为必填项' }
                ]
              };
            }
          },
          initEditStatus: true,
          pagination: false,
          size: 'small'
        },
        width: '100%'
      },
      {
        type: 'Textarea',
        label: '备注',
        icon: 'lu-icon-table',
        key: 'sqlmap_note',
        width: '100%'
        // rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
    ];
  };

  return {
    fields
  };
}
