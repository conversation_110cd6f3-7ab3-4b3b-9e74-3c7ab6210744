<template>
  <a-drawer
    title="执行计划"
    placement="right"
    :maskClosable="true"
    :closable="true"
    :visible="visible"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="data-base-audit-sql-exception big-title"
    @close="onClose"
    width="720px"
  >
    <a-spin tip="加载中" :spinning="spinning" class="sql-exception">
      <!-- SQLID -->
      <!-- <div class="review-header">
        <h4>执行计划</h4>
      </div> -->

      <!-- SQL文本 -->
      <a-card type="small" :bordered="false">
        <div class="title">
          <a-tooltip slot="tab" placement="bottom">
            <template slot="title">
              <div>{{ pane.label + '(' + pane.db_url + ')' }}</div>
            </template>
            <InstanceItem
              view="new"
              mode="ellipsis"
              :tagText="pane.instance_usage"
              :src="pane.db_type"
              :text="pane.label"
            />
          </a-tooltip>

          <span class="sql-id" v-if="sqlId">{{ 'SQLID：' + sqlId }}</span>
        </div>
        <Coder
          v-model="sqlText"
          type="sql"
          :needFormat="true"
          v-if="sqlText"
          :options="options"
        ></Coder>
        <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>
      </a-card>
      <!-- 执行计划 -->
      <a-card type="small" :bordered="false" v-if="sqlPlan">
        <div class="title">
          <a-icon type="flag" />
          <span style="margin-left: 4px">执行计划</span>
        </div>
        <div>
          <Coder
            v-model="sqlPlan"
            type="txt"
            :needFormat="true"
            :options="options"
          ></Coder>
        </div>
        <!-- <div class="ai-comment-part" v-else>
          <span>暂无数据</span>
        </div>-->
      </a-card>
    </a-spin>

    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 10
      }"
    >
      <a-button @click="onClose">关闭</a-button>
    </div>
  </a-drawer>
</template>
<script>
import SqlHighlight from '@/components/SqlHighlight';
import Coder from '@/components/Coder';
import InstanceItem from '@/components/Biz/InstanceItem';
import {
  afterExplain,
  planHashValueExplain,
  getGaussdbPlan
} from '@/api/databaseaudit/topsql';
import {
  afterSqlTextExplain,
  getOboracleExplain
} from '@/api/databaseaudit/realtime';
export default {
  components: {
    SqlHighlight,
    InstanceItem,
    Coder
  },
  props: {
    id: Number | String,
    sqlPlanInfo: {
      type: Array,
      default: () => []
    },
    dbType: String
  },
  data() {
    return {
      visible: false,
      spinning: false,
      sqlPlan: '',
      sqlText: '',
      errorMessage: '',
      options: {
        readOnly: true
      },
      sqlId: null,
      pane: {}
    };
  },
  created() {},
  mounted() {},
  watch: {},
  destroyed() {},
  computed: {},
  methods: {
    show(type, record, data = {}, text) {
      this.pane = data;
      this.sqlText = null;
      this.sqlPlan = null;
      this.visible = true;
      this.sqlId = record.sql_id;
      this.spinning = true;
      let req = () => {};
      let params = {};
      if (text == 'plan_hash_value') {
        req = planHashValueExplain;
        params = {
          plan_hash_value: record.plan_hash_value,
          data_source_id: record.data_source_id,
          sql_id: record.sql_id
        };
      } else if (text == 'query_plan') {
        req = getGaussdbPlan;
        params = {
          id: record.id
        };
      } else if (type == 'topsql') {
        req = afterExplain;
        params = {
          after_id: record.sql_id,
          db_type: this.pane.db_type,
          id: record.id
        };
      } else {
        req = ['MYSQL', 'GOLDENDB'].includes(this.dbType)
          ? afterSqlTextExplain
          : getOboracleExplain;
        params = ['MYSQL', 'GOLDENDB'].includes(this.dbType)
          ? {
              sql_text: record.info,
              schema: record.db,
              data_source_id: this.id
            }
          : {
              id: this.id,
              sql: record.info
            };
      }
      req(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            const resData = _.get(res, 'data.data');
            this.sqlPlan = resData.sql_plan;
            this.sqlText = resData.sql_text;
            this.spinning = false;
            this.$hideLoading({ duration: 0 });
          } else {
            const resData = _.get(res, 'data.data');
            this.sqlText = resData.sql_text;
            this.sqlPlan = resData.msg;
            this.spinning = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.spinning = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e, 'response.data.message') || '请求失败'
          });
        });
    },
    onClose() {
      this.visible = false;
    }
  }
};
</script>
<style lang="less" scoped>
.sql-exception {
  color: rgba(86, 87, 89, 1);
  /deep/.ant-spin-container {
    background: #fff;
    // padding: 0 24px;
    .review-header {
      display: flex;
      align-items: center;
      padding: 0;
      > h4 {
        font-family: PingFangSC-Semibold;
        font-size: 20px;
        color: #27272a;
        font-weight: 600;
        margin-right: 32px;
        margin-bottom: 0;
      }
    }
    .ant-card {
      .ant-card-body {
        padding: 0 0 24px 0;
        padding-bottom: 0;
        border-radius: 12px;
        .title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 8px;
          .anticon {
            font-size: 16px;
            color: #333333;
          }
          > span {
            font-family: PingFangSC-Semibold;
            font-size: 14px;
            font-weight: 600;
          }
          .sql-id {
            padding: 4px 16px;
            border-radius: 16px;
            background: #ffffff;
            border: 1px solid #e4e4e7;
            font-size: 13px;
            color: #000000;
            font-weight: 400;
            margin-left: 24px;
          }
          .biz-instance-item {
            .instance-item-tag {
              border: none;
              .database-image {
                .iconClass {
                  .iconText {
                    pre {
                      font-size: 13px;
                      color: #27272a;
                      font-weight: 600;
                    }
                  }
                }
              }
            }
          }
        }
        .custom-coder {
          .CodeMirror {
            // border-radius: 0;
            height: 330px !important;
            .CodeMirror-scroll {
              .CodeMirror-gutters {
                // background-color: #2b2b2b;
              }
            }
          }
        }
      }
    }
    .ai-comment-part {
      height: 330px;
      padding: 16px;
      background-color: #2b2b2b;
      border-radius: 5px;
      > span {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #a9b7c6;
        font-weight: 400;
      }
    }
  }
}
</style>
