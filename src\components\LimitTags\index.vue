<template>
  <span class="limit-tags">
    <template v-if="limit < tags.length">
      <a-tag
        v-for="(item, index) in tags.slice(0, limit)"
        :key="index"
        v-bind="item"
        >{{ item.label }}</a-tag
      >
      <a-popover overlayClassName="limit-tags-popover">
        <template slot="content">
          <a-tag v-for="(item, index) in  tags.slice(limit)" :key="index" v-bind="item">{{
            item.label
          }}</a-tag>
        </template>
        <a class="more-text" style="display: inline-block" v-if="mode == 'textTag'">{{ moreText }}</a>
        <a-tag v-if="mode == 'numTag'">{{ `+${tags.length-limit}` }}</a-tag>
      </a-popover>
    </template>
    <span v-else>
      <a-tag v-for="(item, index) in tags" :key="index" v-bind="item">{{
        item.label
      }}</a-tag>
    </span>
  </span>
</template>

<script>
export default {
  inheritAttrs: false,
  components: {},
  props: {
    limit: Number,
    tags: {
      type: Array,
      default: () => []
    },
    moreText: {
      type: String,
      default: '更多'
    },
    mode: {
      type: String,
      default: 'textTag' // textTag显示更多, numTag+num
    }
  },
  data() {
    return {};
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.limit-tags {
  display: inline-block;
  .ant-tag {
    margin-bottom: 4px;
  }
  .more-text {
    font-size: 12px;
  }
}
</style>
<style lang="less">
</style>
