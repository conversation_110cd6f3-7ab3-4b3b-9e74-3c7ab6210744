import Http from '@/utils/request';

export function addDataSource(params = {}) {
  return Http({
    url: `/sqlreview/project/data_source`,
    method: 'post',
    data: params
  });
}
export function editDataSource(params = {}) {
  return Http({
    url: `/sqlreview/project/data_source_edit`,
    method: 'post',
    data: params
  });
}
export function deleteDataSource(params = {}) {
  return Http({
    url: `/sqlreview/project/data_source_edit`,
    method: 'delete',
    data: params
  });
}

export function testDataSource1(data = {}) {
  return Http({
    url: `/sqlreview/review/test-data-source/`,
    method: 'post',
    data: data
  });
}

export function testDataSource2(data = {}) {
  return Http({
    url: `/sqlreview/review/test-data-source/`,
    method: 'post',
    data: data
  });
}

export function testDataSource3(data = {}) {
  return Http({
    url: `/sqlreview/review/test-data-source/`,
    method: 'post',
    data: data
  });
}

export function testDataSource4(data = {}) {
  return Http({
    url: `/sqlreview/review/test-data-source/`,
    method: 'post',
    data: data
  });
}
// 统计信息获取
export function getStatisticsInfo(params = {}) {
  return Http({
    url: `/sqlreview/project/get_datasource_statistics`,
    method: 'get',
    params
  });
}

// 拉取密码
export function pullPassword(params = {}) {
  return Http({
    url: `/sqlreview/faced/get_password`,
    method: 'post',
    data: params
  });
}

// 拉取数据源
export function pullDatasource(params = {}) {
  return Http({
    url: `/sqlreview/faced/get_datasource_mess`,
    method: 'post',
    data: params
  });
}

// 实例权限管理 删除
export function permissionManageDelete(params = {}) {
  return Http({
    url: `/sqlreview/project/permission_manage_delete`,
    method: 'get',
    params: params
  });
}

// 授权用户 获取实例列表
export function getInstanceList(params = {}) {
  return Http({
    url: `/sqlreview/project/authorization_user_examples`,
    method: 'post',
    data: params
  });
}

// 授权用户 获取数据库列表
export function getDatabaseList(params = {}) {
  return Http({
    url: `/sqlreview/project/authorization_schema`,
    method: 'post',
    data: params
  });
}

// 授权用户 获取表列表
export function getTableDetailList(params = {}) {
  return Http({
    url: `/sqlreview/project/authorization_table`,
    method: 'post',
    data: params
  });
}

// 实例授权用户 保存按钮
export function saveInstanceAuth(params = {}) {
  return Http({
    url: `/sqlreview/project/authorization_user`,
    method: 'post',
    data: params
  });
}

// 数据库授权用户 保存按钮
export function saveDatabaseAuth(params = {}) {
  return Http({
    url: `/sqlreview/project/authorization_user_schema`,
    method: 'post',
    data: params
  });
}

// 表列表 保存按钮
export function saveTableDetailAuth(params = {}) {
  return Http({
    url: `/sqlreview/project/authorization_user_table`,
    method: 'post',
    data: params
  });
}

// 表列表 权限控制
export function schemaPermissionSwitch(params = {}) {
  return Http({
    url: `/sqlreview/project/permission_status_edit`,
    method: 'post',
    data: params
  });
}

// 执行SQL 采集开关
export function afterAuditStatuSwitch(params = {}) {
  return Http({
    url: `/sqlreview/project/after_audit_status_edit`,
    method: 'post',
    data: params
  });
}

// 数据列表实例详情获取数据接口
export function getInstanceDetailData(params = {}) {
  return Http({
    url: `/sqlreview/project/data_source`,
    method: 'get',
    params
  });
}

// 实例导入 实例信息模板下载
export function downloadDataSourceTemplate(params = {}) {
  return Http({
    url: `/sqlreview/project/download_ds_template `,
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 实例导入 实例信息模板 提交并获取信息
export function uploadDataSource(params = {}) {
  return Http({
    url: `/sqlreview/project/upload_data_source`,
    method: 'post',
    data: params
  });
}

// 实例导入 点击下载错误模板
export function downloadErrorEeport(params = {}) {
  return Http({
    url: `/sqlreview/project/download_error_report`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 字段-权限开关
export function fieldPermissionSwitch(params = {}) {
  return Http({
    url: `/sqlreview/project/permission_status_edit`,
    method: 'post',
    data: params
  });
}

// 字段-授权
export function authorizationUserColumns(params = {}) {
  return Http({
    url: `/sqlreview/project/authorization_user_columns`,
    method: 'post',
    data: params
  });
}

// 数据列表实例详情获取数据接口
export function getFieldList(params = {}) {
  return Http({
    url: `/sqlreview/project/authorization_columns `,
    method: 'post',
    data: params
  });
}

// 字段授权用户 保存按钮
export function saveFeildAuth(params = {}) {
  return Http({
    url: `/sqlreview/project/authorization_user_columns`,
    method: 'post',
    data: params
  });
}

// 字段脱敏控制，禁用启用
export function sensitiveSwitch(params = {}) {
  return Http({
    url: `/sqlreview/project/sensitive_info_switch`,
    method: 'post',
    data: params
  });
}

// 阿里云验证
export function checkOpenApi(params) {
  return Http({
    url: `/sqlreview/after_audit/check_openApi`,
    method: 'post',
    data: params
  });
}

// 查询mysql附加字段
export function getExtraColumns(params) {
  return Http({
    url: `/sqlreview/after_audit/extra_columns`,
    method: 'post',
    data: params
  });
}

// 统计信息导入导出

// 数据源检测
export function checkDatabase(params) {
  return Http({
    url: `/sqlreview/project/check_permission_for_oracle_stat`,
    method: 'get',
    params: params
  });
}

//  统计信息导入 立即导入
export function importOracleStatSql(params) {
  return Http({
    url: `/sqlreview/project/import_oracle_stat_sql`,
    method: 'post',
    data: params
  });
}

// 导入导出详情
export function getImportOrExportDetail(params) {
  return Http({
    url: `/sqlreview/project/oracle_stat_detail`,
    method: 'get',
    params: params
  });
}

// 统计信息导出 导出
export function exportOracleStatSql(params) {
  return Http({
    url: `/sqlreview/project/export_oracle_stat_sql`,
    method: 'post',
    data: params
  });
}

// 统计信息导入导入 文件下载

export function downloadSqlFile(params) {
  return Http({
    url: `/sqlreview/project/download_sql_file`,
    method: 'get',
    params: params,
    responseType: 'blob'
  });
}
export default {};