<template>
  <div :class="`search-area-simple`">
    <!-- <custom-form
      ref="form"
      :fields="localFields"
      v-bind="searchProps"
      :formData="data"
      class="search-area-simple-form"
    ></custom-form>-->
    <template v-for="item in localFields">
      <component
        :key="item.key"
        class="search-area-comp-item"
        v-model="data[item.key]"
        v-bind="item.props || {}"
        v-on="item.listeners || {}"
        :ref="item.key"
        :is="item.realType"
      >
        <template v-if="item.children">{{ item.children }}</template>
      </component>
    </template>
  </div>
</template>

<script>
import Select from '@/components/Select';
import InputSearch from '@/components/InputSearch';
import RadioGroup from '@/components/RadioGroup';
export default {
  components: { Select, InputSearch, RadioGroup },
  props: {
    fields: {
      type: Array,
      default: function() {
        return [];
      }
    },
    searchData: {
      type: Object,
      default: () => ({})
    },
    needCache: {
      type: <PERSON>olean,
      default: false
    },
    cacheKey: {
      type: String,
      default: ''
    }
  },
  data() {
    // const { needCache, cacheKey } = this;
    // let initData = {};
    // if (needCache) {
    //   const _key = cacheKey || this.$route.name;
    //   initData = _.merge({}, this.$store.state.common.searchCache[_key] || {});
    // }
    return {
      data: this.searchData
    };
  },
  computed: {
    // searchProps() {
    //   const { cols, gutter, labelOverflow } = this;
    //   const { labelCol, wrapperCol } = this.$attrs;
    //   const tmp = {
    //     multiCols: cols,
    //     gutter,
    //     labelCol,
    //     wrapperCol,
    //     labelOverflow
    //   };
    //   let props = {};
    //   _.forEach(tmp, (item, key) => {
    //     if (item) {
    //       props[key] = item;
    //     }
    //   });
    //   return { ...props };
    // }
    localFields() {
      return this.fields
        .filter(item => item.mainSearch)
        .map(item => {
          let realType = item.type;
          let trigger = 'change';
          let extraTriggers = {};
          if (item.type === 'Input') {
            realType = 'a-input-search';
            trigger = 'search';
          } else if (item.type === 'InputSearch') {
            trigger = 'search';
          } else if (item.type === 'RangePicker') {
            realType = 'a-range-picker';
            if (_.get(item, 'props.showTime')) {
              trigger = 'ok';
              extraTriggers = {
                change: val => {
                  if (val == null || val.length <= 0) {
                    this.doSearch(item, 'change');
                  }
                }
              };
            }
          } else if (item.type === 'RadioGroup') {
            realType = 'RadioGroup';
          } else if (item.type === 'Select') {
            // do nothing;
          } else {
            realType = `a${item.type.replace(
              /([A-Z])/g,
              $0 => '-' + $0.toLowerCase()
            )}`;
          }
          let res = { ...item };
          res.realType = realType;
          res.listeners = {
            ...(item.listeners || {}),
            [trigger]: () => {
              this.doSearch(item, trigger);
            },
            ...extraTriggers
          };
          return res;
        });
    }
  },
  created() {},
  mounted() {
    if (this.needCache) {
      // 默认需要向外emit，配合外部表格的isInitReq=false
      this.emitSearchData(this.data);
    }
  },
  destroyed() {},
  methods: {
    reset() {
      // const { form } = this.$refs;
      this.data = {};
      // form.resetFields();
      this.$emit('reset', {});
      // this.emitCacheData({});
    },
    search() {
      // this.emitCacheData();
      this.emitSearchData();
    },
    emitSearchData(data) {
      // const { localFields } = this;
      // const { form } = this.$refs;
      const formData = data || this.data;
      let res = {};

      // 处理数据
      _.forEach(formData, (item, key) => {
        if (key === '_searchBtns') return;
        // let match = localFields.find(f => f.key === key) || {};
        // if (match.type === 'RangePicker') {
        //   res[key] = item
        //     ? item.map(time => time.format('YYYY-MM-DD HH:mm:ss')).join(',')
        //     : item;
        // } else {
        //   res[key] = item;
        // }
        res[key] = item;
      });
      this.$emit('search', res);
    },
    emitCacheData(data) {
      const { needCache, cacheKey } = this;
      // const { form } = this.$refs;
      const formData = data || this.data;
      // 全局存储
      if (needCache) {
        const _key = cacheKey || this.$route.name;
        this.$store.commit('common/setSearchCache', {
          [_key]: formData
        });
      }
    },
    setData(data = {}) {
      if (JSON.stringify(this.data) !== JSON.stringify(data)) {
        this.data = data;
      }
    },
    doSearch(item, trigger) {
      const sourceCbk = _.get(item, `listeners.${trigger}`);
      sourceCbk && sourceCbk();
      this.search();
    }
  },
  watch: {
    searchData: {
      handler(newVal, oldVal) {
        // const { needCache, cacheKey } = this;
        // const _key = cacheKey || this.$route.name;
        // const initData = _.merge(
        //   {},
        //   this.$store.state.common.searchCache[_key] || {}
        // );
        // if (!needCache || _.isEmpty(initData)) {
        //   this.data = newVal || {};
        // }
        this.data = newVal;
      },
      immediate: true
      // deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.search-area-simple {
  position: relative;
  padding: 12px 0 12px 0;
  // display: inline-block;
  display: flex;
  align-items: center;
  // float: left;

  /deep/ .search-area-comp-item {
    &:not(:last-child) {
      margin-right: 16px;
    }
  }
  /deep/ .ant-input-search {
    .ant-input {
      width: 200px;
      // transition: width 0.4s;
    }
    // &:hover {
    //   .ant-input {
    //     width: 250px;
    //   }
    // }
  }
  /deep/ .ant-select {
    width: 200px;
    // transition: width 0.4s;
    // &:hover {
    //   width: 250px;
    // }
  }
  // /deep/ .search-area-simple-form {
  //   width: 200px;
  //   .ant-form-item {
  //     margin-bottom: 8px;
  //   }
  //   .ant-form-item-label {
  //     display: none;
  //   }
  // }
}
</style>
