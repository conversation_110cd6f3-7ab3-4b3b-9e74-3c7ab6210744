<template>
  <a-input-password
    v-bind="passordProps"
    v-on="passordListeners"
    autocomplete="new-password"
    :value="loacalVal"
  />
</template>

<script>
const defaultProps = {};

export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {},
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loacalVal: this.value
    };
  },
  computed: {
    passordProps() {
      return { ...defaultProps, ...this.$attrs };
    },
    passordListeners() {
      return { ...this.$listeners, change: this.onChange };
    }
  },
  created() {},
  mounted() {},
  methods: {
    onChange(e) {
      this.loacalVal = e.target.value;
      this.$emit('change', e.target.value);
    }
  },
  watch: {
    value(newVal) {
      this.loacalVal = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
</style>
