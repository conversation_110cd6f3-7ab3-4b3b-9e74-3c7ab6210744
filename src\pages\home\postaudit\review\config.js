export default function (ctx) {
  const columns = [
    {
      title: '风险等级',
      key: 'level',
      dataIndex: 'level',
      scopedSlots: { customRender: 'level' },
      width: 120
    },
    {
      title: '审核结果',
      key: 'status',
      dataIndex: 'status',
      scopedSlots: { customRender: 'status' },
      width: 120
    },
    {
      title: 'SQL文本',
      key: 'sql_text',
      dataIndex: 'sql_text',
      scopedSlots: { customRender: 'sql_text' },
      width: 300
    },
    {
      title: 'Schema',
      key: 'parsing_schema_name',
      dataIndex: 'parsing_schema_name',
      width: 120
    },
    {
      title: '等待事件',
      key: 'oracle_event',
      dataIndex: 'oracle_event',
      scopedSlots: { customRender: 'oracle_event' },
      width: 200
    },
    {
      title: '执行次数',
      key: 'executions_total',
      dataIndex: 'executions_total',
      scopedSlots: { customRender: 'thousandth' },
      sorter: true,
      width: 120
    },
    {
      title: '平均执行时间(ms)',
      key: 'elapsed_time',
      dataIndex: 'elapsed_time',
      scopedSlots: { customRender: 'thousandth' },
      // customRender: (text, record, index) => {
      //   return text;
      // },
      sorter: true,
      width: 180
    },
    {
      title: '平均物理读(次)',
      key: 'physical_read_requests_total',
      dataIndex: 'physical_read_requests_total',
      scopedSlots: { customRender: 'thousandth' },
      // customRender: (text, record, index) => {
      //   return text;
      // },
      sorter: true,
      width: 180
    },
    {
      title: '平均逻辑读(次)',
      key: 'buffer_gets_total',
      dataIndex: 'buffer_gets_total',
      scopedSlots: { customRender: 'thousandth' },
      sorter: true,
      width: 180
    },
    {
      title: 'SQL执行时间范围',
      dataIndex: 'sql_time_range',
      key: 'sql_time_range',
      width: 300
    },
    {
      title: 'SQLID',
      dataIndex: 'sql_id',
      key: 'sql_id',
      width: 150
    },
    // {
    //   title: '执行计划数量',
    //   key: 'plan_count',
    //   dataIndex: 'plan_count',
    //   scopedSlots: { customRender: 'thousandth' },
    //   width: 150
    // },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      width: 120
    }
  ].map(item => {
    return {
      ...item,
      width: undefined
    }
  });

  const mysqlColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: 'Schema',
      key: 'schema',
      dataIndex: 'schema',
      width: 120
    },
    {
      title: 'SQL文本',
      key: 'SQL_TEXT',
      dataIndex: 'SQL_TEXT',
      scopedSlots: { customRender: 'sql_text' },
      width: 300
    },
    {
      title: '审核结果',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'status' },
      width: 120
    },
    {
      title: '风险等级',
      key: 'level',
      dataIndex: 'level',
      scopedSlots: { customRender: 'level' },
      width: 120
    },
    {
      title: '查询最大时间(ms)',
      key: 'query_time_max',
      dataIndex: 'query_time_max',
      scopedSlots: { customRender: 'thousandth' },
      width: 180
    },
    {
      title: '查询最短时间(ms)',
      key: 'query_time_min',
      dataIndex: 'query_time_min',
      scopedSlots: { customRender: 'thousandth' },
      width: 180
    },
    {
      title: '查询时间的平均值(ms)',
      key: 'query_time_avg',
      dataIndex: 'query_time_avg',
      scopedSlots: { customRender: 'thousandth' },
      width: 200
    },
    {
      title: '查询时间95值(ms)',
      key: 'query_time_95',
      dataIndex: 'query_time_95',
      scopedSlots: { customRender: 'thousandth' },
      width: 180
    },
    {
      title: '锁时间95值(ms)',
      key: 'lock_time_95',
      dataIndex: 'lock_time_95',
      scopedSlots: { customRender: 'thousandth' },
      width: 180
    },
    {
      title: '返回行数95值',
      key: 'rows_sent_95',
      dataIndex: 'rows_sent_95',
      scopedSlots: { customRender: 'thousandth' },
      width: 180
    },
    {
      title: '返回行数平均值',
      key: 'rows_sent_avg',
      dataIndex: 'rows_sent_avg',
      scopedSlots: { customRender: 'thousandth' },
      width: 180
    },
    {
      title: '影响行数平均值',
      key: 'rows_affected_avg',
      dataIndex: 'rows_affected_avg',
      scopedSlots: { customRender: 'thousandth' },
      width: 180
    },
    {
      title: '影响行数95值',
      key: 'rows_affected_95',
      dataIndex: 'rows_affected_95',
      scopedSlots: { customRender: 'thousandth' },
      width: 180
    },
    {
      title: '查询总数',
      key: 'slow_query_count',
      dataIndex: 'slow_query_count',
      scopedSlots: { customRender: 'thousandth' },
      width: 100
    },
    {
      title: '查询涉及的表',
      key: 'slow_query_tables',
      dataIndex: 'slow_query_tables',
      scopedSlots: { customRender: 'slow_query_tables' },
      width: 200
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      width: 120
    }
  ].map(item => {
    return {
      ...item,
      width: undefined
    }
  });

  const searchFields = [
    {
      type: 'Input',
      label: 'SQL文本',
      key: 'sql_text'
    },
    {
      type: 'Input',
      label: '等待事件',
      key: 'oracle_event'
    },
    {
      type: 'Input',
      label: 'Schema',
      key: 'parsing_schema_name'
    },
    {
      type: 'Input',
      label: 'SQLID',
      key: 'sql_id',
      mainSearch: true,
      props: {
        placeholder: '请输入SQLID'
      }
    },
    {
      type: 'Select',
      label: '审核结果',
      key: 'status',
      props: {
        options: [
          {
            label: '未知',
            value: '0'
          },
          {
            label: '通过',
            value: '1'
          },
          {
            label: '未通过',
            value: '-1'
          },
          {
            label: '白名单通过',
            value: '2'
          },
          {
            label: '错误',
            value: '9'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: '风险等级',
      key: 'level',
      props: {
        options: [
          {
            label: '未知',
            value: '0'
          },
          {
            label: '高风险',
            value: '1'
          },
          {
            label: '中风险',
            value: '2'
          },
          {
            label: '低风险',
            value: '3'
          },
          {
            label: '无风险',
            value: '9'
          }
        ]
      }
    },
    {
      type: 'InputNumRange',
      label: '平均执行时间',
      key: 'elapsed_time',
      props: {
        min: 0
      }
    },
    {
      type: 'InputNumRange',
      label: '平均物理读',
      key: 'physical_read_requests_total',
      props: {
        min: 0
      }
    },
    {
      type: 'InputNumRange',
      label: '平均逻辑读',
      key: 'buffer_gets_total',
      props: {
        min: 0
      }
    }
  ];

  const mysqlSearchFields = [
    {
      type: 'Input',
      label: 'ID',
      key: 'id',
      mainSearch: true,
      props: {
        placeholder: '请输入id'
      }
    },
    {
      type: 'Input',
      label: 'Schema',
      key: 'schema',
      props: {
        placeholder: '请输入schema'
      }
    },
    {
      type: 'Input',
      label: 'SQL文本',
      key: 'SQL_TEXT',
      props: {
        placeholder: '请输入SQL文本'
      }
    }
  ];
  return {
    columns,
    mysqlColumns,
    searchFields,
    mysqlSearchFields
  };
}
