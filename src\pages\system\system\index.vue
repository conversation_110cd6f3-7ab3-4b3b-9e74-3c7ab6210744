<template>
  <div class="content page-list-single">
    <Table ref="table" v-bind="tableParams || {}" class="new-view-table small-size">
      <LimitLabel
        slot="item_value"
        slot-scope="{ record, text }"
        :limit="10"
        :label="getText(record, text) || '--'"
      ></LimitLabel>
      <custom-btns-wrapper slot="action" slot-scope="{ record }">
        <a @click="reset(record)" actionBtn>编辑</a>
      </custom-btns-wrapper>
      <LimitLabel slot="config_desc" slot-scope="{ text }" :label="text" :limit="20"></LimitLabel>
    </Table>
    <!-- 修改弹窗 -->
    <ResetModal ref="reset" @save="resetSystem"></ResetModal>
  </div>
</template>

<script>
import _ from 'lodash';
import Table from '@/components/Table';
import { updateSystem } from '@/api/config/system';
import LimitLabel from '@/components/LimitLabel';
import ResetModal from './components/ResetModal';
import config from './config';
const data = [];
export default {
  name: 'system-config',
  props: {},
  components: { Table, ResetModal, LimitLabel },
  data() {
    this.config = config(this);
    return {
      data,
      tableParams: {
        url: '/sqlreview/project_config/get_project_config',
        rowKey: 'id',
        columns: this.config.columns,
        isInitReq: false,
        needCache: true,
        searchFields: this.config.searchFields,
        needTools: true,
        needSearchArea: true,
        pagination: false,
        scroll: { x: 1600 }
      }
    };
  },
  computed: {},
  mounted() {},
  methods: {
    getText(record, text) {
      if (_.isArray(text)) {
        return text.join();
      } else {
        return (
          _.get(
            record.sql_review_dynamic_form.sql_review_dynamic_form_element.filter(
              x => x.element_value == text
            )[0],
            'element_name'
          ) ||
          text ||
          '--'
        );
      }
    },
    reset(record) {
      const { reset } = this.$refs;
      reset.show(record);
    },
    // 修改
    resetSystem(record) {
      const { reset } = this.$refs;
      this.$showLoading();
      return updateSystem({
        ...record,
        item_value: record.item_value
      })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            reset.onCancel();
            window.location.reload()
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.tagStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .tag-item {
    height: 24px;
    overflow: hidden;
  }
  .ant-tag {
    margin-bottom: 5px;
  }
  .anticon {
    margin-right: 30px;
  }
  .anticon-double-right {
    transform: rotate(90deg);
  }
  .anticon-double-left {
    transform: rotate(90deg);
  }
}
</style>
