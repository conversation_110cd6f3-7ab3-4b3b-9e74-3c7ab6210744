<template>
  <div class="psc-rqc-coder">
    <div class="psc-rqc-coder-header">
      <!-- 按钮区域 -->
      <div class="psc-rqc-ch-btns">
        <span
          :class="['psc-rqc-btn-item', item.disabled() && 'disabled']"
          v-for="item in btns"
          :key="item.key"
          @click="btnFnc(item)"
        >
          <custom-icon :type="getBtnIcon(item.icon)" />
          <span>{{item.label + (item.selection && selection ? '选中' : '')}}</span>
        </span>
      </div>
      <!-- 所属数据库 -->
      <div class="psc-rqc-ch-info" v-if="fixedDatabase">
        <custom-icon type="lu-icon-schema" />
        <span>{{fixedDatabase.name}}</span>
        <div style="display:inline-block;">
          <custom-icon class="icon-select-arrow" type="lu-icon-up" />
          <Select
            :class="['psc-rqc-schema-select', schemaSelectVisible && 'dropdown-show']"
            :value="fixedDatabase.id"
            :options="schemaOptions"
            v-bind="schemaParams"
            @change="onSchemaChange"
            @dropdownVisibleChange="onDropdownVisibleChange"
          />
        </div>
      </div>
    </div>
    <div class="psc-rqc-coder-content">
      <Coder
        ref="Coder"
        class="default-full-white"
        v-model="localValue"
        v-bind="coderParams"
        @selectionChange="onSelectionChange"
      />
    </div>
    <QuerySaveModal ref="QuerySaveModal" @save="onSaveSheet" />
    <!-- ludms新增工单弹窗 -->
    <template v-if="project === 'ludms'">
      <component :is="'AddOrderModal'" ref="AddOrderModal" @submitSuccess="onOrderSuccess"></component>
    </template>
  </div>
</template>

<script>
import {
  executeSql,
  saveSqlSheet,
  checkSqlText,
  examineSql
} from '@/api/sqlresolver';
import Coder from '@/components/Coder';
import Select from '@/components/Select';
import QuerySaveModal from '@/pages/sqlresolver/modals/querySave';
// import AddOrderModal from '@/pages/workOrder/components/AddOrderModal';
import HintTool from './hint';

export default {
  components: { Coder, Select, QuerySaveModal },
  inject: ['instanceItem'],
  props: {
    database: Object,
    queryTab: String,
    queryItem: {
      type: Object,
      default: () => ({})
    },
    queryActiveTab: String,
    defaultSql: String,
    schemaOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    this.HintTool = HintTool(this);
    return {
      project: GLOBAL_CONFIG.ProjectCode,
      fixedDatabase: this.database,
      schemaParams: this.getSchemaParams(),
      schemaSelectVisible: false,
      btns: [
        {
          key: 'run',
          label: '运行',
          icon: () => (this.running ? 'loading' : 'lu-icon-run'),
          selection: true,
          disabled: () => {
            return !this.localValue || this.running;
          }
        },
        // {
        //   key: 'stop',
        //   label: '停止',
        //   icon: 'lu-icon-stop',
        //   disabled: () => {
        //     return !this.running;
        //   }
        // },
        {
          key: 'format',
          label: '格式化',
          icon: 'lu-icon-format',
          selection: true,
          disabled: () => {
            return !this.localValue;
          }
        },
        {
          key: 'check',
          label: '审核',
          icon: () => (this.checking ? 'loading' : 'lu-icon-check'),
          selection: true,
          disabled: () => {
            return !this.localValue || this.checking || false;
          }
        },
        {
          key: 'save',
          label: '保存',
          icon: 'lu-icon-memory',
          disabled: () => {
            return !this.localValue;
          }
        }
      ],
      coderParams: {
        dbType: _.get(this.instanceItem, 'type'),
        height: '100%',
        hintDefault: false,
        options: {
          theme: 'default',
          gutters: [
            // 'CodeMirror-lintbyline',
            'CodeMirror-linenumbers',
            'CodeMirror-foldgutter'
          ],
          autofocus: true,
          hintOptions: {
            hint: async (cm, options) => {
              return this.HintTool.getHintList(cm, options);
            },
            completeSingle: false
          }
        },
        formatOptions: {
          // indentStyle: 'tabularLeft'
        }
        // needFormat: true
      },
      selection: '',
      running: false,
      checking: false,
      // 'CREATE TABLE public.tb_duguqiubai (name varchar(32) NOT NULL ,id int2 NOT NULL ,CONSTRAINT pk_tb_duguqiubai PRIMARY KEY (id))  WITH( OIDS=FALSE) TABLESPACE pg_default;'
      localValue: this.defaultSql
    };
  },
  computed: {
    dmlEnable() {
      return GLOBAL_CONFIG.ProjectCode === 'ludms';
    }
  },
  mounted() {
    this.$bus.$on('sqlresolver-instance-change', instanceId => {
      // 唤醒当前instance tab
      if (instanceId == _.get(this.instanceItem, 'key')) {
        this.refreshCoder();
      }
    });
    this.$bus.$on('sqlresolver-drag-node-move', params => {
      const { event, instanceId } = params;
      // 唤醒当前instance tab
      if (instanceId != _.get(this.instanceItem, 'key')) {
        return;
      }
      if (this.queryActiveTab !== this.queryTab) {
        return;
      }
      if (
        !this.isInTarget(
          event,
          this.$el.querySelector('.psc-rqc-coder-content')
        )
      ) {
        return;
      }
      const { Coder } = this.$refs;
      if (!Coder.exec('hasFocus')) {
        Coder.exec('focus');
      }
    });
    this.$bus.$on('sqlresolver-drag-node-end', params => {
      const { event, data, instanceId } = params;
      // 唤醒当前instance tab
      if (instanceId != _.get(this.instanceItem, 'key')) {
        return;
      }
      if (this.queryActiveTab !== this.queryTab) {
        return;
      }
      if (
        !this.isInTarget(
          event,
          this.$el.querySelector('.psc-rqc-coder-content')
        )
      ) {
        return;
      }
      const { Coder } = this.$refs;
      Coder.exec('focus');
      Coder.exec('replaceSelection', data.name);
    });
    this.$refs.Coder.exec('on', 'inputRead', e => {
      // const { line, ch } = e.getCursor() || {};
      // const lineStr = e.getLine(line) || '';
      // const char = lineStr.charAt(ch - 1);
      // if (/[a-zA-z]/.test(char.trim())) {
      //   this.$refs.Coder.exec('showHint');
      // }

      // console.log(line, ch, lineStr, char);
      const { Coder } = this.$refs;
      if (Coder && !Coder.pasting) {
        Coder.exec('showHint');
      }
    });
  },
  methods: {
    getSchemaParams() {
      return {
        placeholder: '请选择schema',
        dropdownMatchSelectWidth: true,
        allowClear: false,
        frontLimit: true,
        getPopupContainer: el => {
          return document.body;
        }
      };
    },
    onSchemaChange(val) {
      const schemaItem = this.schemaOptions.find(item => item.value == val);
      if (schemaItem) {
        this.fixedDatabase = schemaItem;
      }
    },
    onDropdownVisibleChange(e) {
      this.schemaSelectVisible = e;
    },
    isEdit() {
      return this.queryItem.sheet_id != null;
    },
    onSelectionChange(cm, selection) {
      this.selection = selection;
    },
    refreshCoder() {
      this.$nextTick(() => {
        const { Coder } = this.$refs;
        Coder && Coder.refresh();
      });
    },
    btnFnc(item) {
      // console.log(item);
      if (this[item.key]) {
        this[item.key](item);
      }
    },
    format() {
      const { Coder } = this.$refs;
      if (this.selection) {
        Coder.exec('replaceSelection', Coder.format(this.selection));
        return;
      }
      this.localValue = Coder.format(Coder.getData());
    },
    check() {
      // 请求
      // this.$showLoading({ useProgress: true });
      this.checking = true;
      const params = {
        instance_id: _.get(this.instanceItem, 'value'),
        schema_id: this.fixedDatabase.id,
        schema_name: this.fixedDatabase.name,
        sql_text: this.getDealValue()
      };
      examineSql(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ useProgress: true, tips: '执行成功' });
            // 清空标记
            this.clearMarks();
            this.$bus.$emit('sqlresolver-database-check-done', {
              list: (_.get(res, 'data.data') || []).filter(item => item),
              instanceId: _.get(this.instanceItem, 'key')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
          this.checking = false;
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          this.checking = false;
        });
      // mark test
      // this.$refs.Coder.exec(
      //   'markText',
      //   { line: 0, ch: 7 },
      //   { line: 0, ch: 16 },
      //   {
      //     css: 'background:rgba(255,0,0,.2);'
      //   }
      // );

      // if (this.xx == null) {
      //   this.xx = 0;
      // }
      // const xx = this.xx % 3;
      // let errList = [
      //   { line: xx == 0 ? 2 : 1, tips: '解析失败！【mock】' },
      //   {
      //     line: xx == 0 ? 3 : 4,
      //     tips: `<ul>
      //       <li>2012年9月25日，辽宁舰正式交付中国海军；</li>
      //       <li>2019年12月17日，山东舰入列，中国海军进入双航母时代；</li>
      //       <li>2022年6月17日，福建舰来了，我们有三艘航母了</li>
      //       <ul>`
      //   }
      // ];
      // errList = xx == 2 ? [] : errList;
      // this.$refs.Coder.setOption(
      //   'gutters',
      //   errList.length > 0
      //     ? [
      //         'CodeMirror-lintbyline',
      //         'CodeMirror-linenumbers',
      //         'CodeMirror-foldgutter'
      //       ]
      //     : [
      //         // 'CodeMirror-lintbyline',
      //         'CodeMirror-linenumbers',
      //         'CodeMirror-foldgutter'
      //       ]
      // );
      // const { Coder } = this.$refs;
      // Coder.lintByLine(errList);
      // this.xx += 1;
    },
    // 获取处理值
    getDealValue() {
      return this.selection ? this.selection : this.localValue;
    },
    getBtnIcon(icon) {
      return _.isFunction(icon) ? icon() : icon;
    },
    checkSqlText() {
      // 请求
      // this.$showLoading({ useProgress: true });
      this.running = true;
      const params = {
        instance_id: _.get(this.instanceItem, 'value'),
        schema_id: this.fixedDatabase.id,
        sql_text: this.getDealValue()
      };
      return new Promise((resolve, reject) => {
        checkSqlText(params)
          .then(res => {
            if (CommonUtil.isSuccessCode(res)) {
              const type = _.get(res, 'data.data.SQL_TYPE');
              if (type !== 'MIX') {
                resolve(type);
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: '不允许混合语句执行！'
                });
              }
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message'),
                response: res
              });
            }
            reject(new Error(''));
            this.running = false;
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
            reject(new Error(''));
            this.running = false;
          });
      });
    },
    async run() {
      // 非自由模式，需要校验sql
      if (_.get(this.instanceItem, 'manage_type') !== 'auto') {
        const sqlType = await this.checkSqlText();
        if (sqlType === 'DML') {
          if (!this.dmlEnable) {
            this.$hideLoading({
              method: 'warning',
              tips: '暂不支持dml语句'
            });
            return;
          }
          this.$hideLoading({ useProgress: true, tips: '执行成功' });
          this.running = false;
          this.$refs.AddOrderModal.show({
            from: 'sql',
            reqParams: {
              instance_group_id: _.get(this.instanceItem, 'instance_group_id'),
              schema_name: this.fixedDatabase.name,
              sql_content: this.getDealValue()
            }
          });
          return;
        }
      } else {
        // this.$showLoading({ useProgress: true });
        this.running = true;
      }
      // 请求
      // this.$showLoading();
      const params = {
        instance_id: _.get(this.instanceItem, 'value'),
        schema_name: this.fixedDatabase.name,
        sql_text: this.getDealValue()
      };
      executeSql(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ useProgress: true, tips: '执行成功' });
            this.$bus.$emit('sqlresolver-database-run-done', {
              list: (_.get(res, 'data.data') || []).filter(item => item),
              instanceId: _.get(this.instanceItem, 'key')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
          this.running = false;
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
          this.running = false;
        });
    },
    onOrderSuccess(data) {
      this.$refs.AddOrderModal.hide();
      this.$bus.$emit('sqlresolver-database-run-done', {
        list: [data],
        instanceId: _.get(this.instanceItem, 'key')
      });
    },
    save() {
      const data = this.isEdit() ? this.queryItem : {};
      console.log(this.queryItem, data, 'ddddd');
      this.$refs.QuerySaveModal.show(data);
    },
    onSaveSheet(data = {}) {
      // 请求
      this.$showLoading();
      const params = {
        sheet_name: data.sheet_name,
        sheet_id: this.queryItem.sheet_id,
        sql_text: this.localValue,
        instance_id: _.get(this.instanceItem, 'value'),
        schema_id: this.fixedDatabase.id
      };
      saveSqlSheet(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ useMessage: true, tips: '保存成功' });
            this.$emit('save-sheet', _.get(res, 'data.data') || {});
            this.$bus.$emit('sqlresolver-save-sheet', {
              instanceId: _.get(this.instanceItem, 'key')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message'),
              response: res
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({ method: 'error', tips: _.get(e || {}, 'response.data.message') || '请求失败' });
        });
    },
    markPos(pos = []) {
      const { Coder } = this.$refs;
      this.clearMarks();
      // 标记
      pos.forEach(item => {
        Coder.markText(
          { line: item.line, ch: item.from },
          { line: item.line, ch: item.from + (item.sql || '').length },
          {
            css: 'background:rgba(255,0,0,.4);'
          }
        );
      });
      // 跳转到第一行
      const jumpLine = _.get(pos, '0.line');
      if (jumpLine != null) {
        Coder.exec('scrollIntoView', jumpLine);
      }
    },
    clearMarks() {
      const { Coder } = this.$refs;
      // 清空标记
      Coder.clearMarks();
    },
    isInTarget(e, target) {
      let flag = false;
      if (!e || !target) {
        return flag;
      }
      let { top, bottom, left, right } = target.getBoundingClientRect();
      if (
        e.pageX > left &&
        e.pageX < right &&
        e.pageY > top &&
        e.pageY < bottom
      ) {
        flag = true;
      }
      return flag;
    }
  },
  watch: {
    database(newVal) {
      if (!this.fixedDatabase) {
        this.fixedDatabase = newVal;
      }
    },
    queryActiveTab(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.refreshCoder();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.psc-rqc-coder {
  display: flex;
  flex-direction: column;
  height: 100%;

  .psc-rqc-coder-header {
    padding: 12px 16px;
    display: flex;
    border-bottom: 1px solid @border-color;
    justify-content: space-between;
    .psc-rqc-btn-item {
      padding: 6px 12px;
      background: rgba(0, 0, 0, 0.04);
      margin-right: 8px;
      border-radius: 4px;
      cursor: pointer;
      color: @font-color-strong;
      .font-bold();
      font-size: 13px;

      .custom-icon {
        margin-right: 2px;
        // color: @primary-color;
        // padding: 4px;
        // background: #ffffff;
        // border-radius: 50%;
      }

      &:hover {
        background: @primary-1;
      }
      &.disabled {
        cursor: not-allowed;
        pointer-events: none;
        opacity: 0.5;
      }
    }

    /deep/ .psc-rqc-ch-info {
      margin-right: 12px;
      position: relative;

      .icon-select-arrow {
        transform: rotate(180deg);
        transition: all 0.3s;
      }

      .psc-rqc-schema-select {
        position: absolute;
        // left: 16px;
        // right: 16px;
        left: auto;
        right: 0;
        top: -4px;
        opacity: 0;
        width: 200px;

        // > .ant-select-selection {
        //   height: 40px;
        // }

        &.dropdown-show {
          opacity: 1;
        }
      }

      .ant-select-dropdown {
        right: 0 !important;
        left: auto !important;
      }
    }
  }
  .psc-rqc-coder-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;

    .custom-coder {
      height: 100%;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      // .border(1px, 0, 1px, 0);
      border: 0;

      /deep/ .CodeMirror {
        position: relative;
        flex-grow: 1;
        .CodeMirror-scroll {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          overflow: auto !important;

          .CodeMirror-sizer {
            border-right-width: 0 !important;
          }

          // .CodeMirror-gutters {
          //   background: #ffffff;
          //   border-color: rgba(0, 0, 0, 0);
          // }
        }
        // .CodeMirror-linenumber {
        //   color: @font-color-normal;
        // }
      }
    }
  }
}
</style>
<style lang="less">
.sqlresolver-hint-item-wrapper {
  display: flex;
  align-items: center;
}
.sqlresolver-hint-item-loading {
  padding: 2px 8px 2px 2px;

  .custom-icon {
    margin-right: 8px;
  }
}
</style>
