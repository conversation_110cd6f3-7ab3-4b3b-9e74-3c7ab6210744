import Bracket from '@/components/Biz/Bracket';
export default function (ctx) {
  const { type } = ctx;
  const isAdd = false;
  const baseInfo = [
    {
      type: 'Select',
      label: '数据库类型',
      key: 'db_type',
      props: {
        url: 'sqlreview/project/rule_support_db',
        reqParams: {},
        placeholder: '请选择数据库',
        disabled: type === 'add' ? isAdd : !isAdd
      },
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
      listeners: {
        change: (value) => {
          ctx.onSelectChange(value);
        }
      }
    },
    {
      type: 'Input',
      label: '规则集名称',
      key: 'name',
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
      props: {
        disabled: type == 'edit' ? !isAdd : isAdd,
        placeholder: '建议在名称前加数据库类型，例如mysql_规则集1'
      }
    }
  ];
  const ruleBaseInfo = [
    {
      type: 'Input',
      label: '规则名称',
      key: 'rule_name',
      // width: '95%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Input',
      label: '规则描述',
      key: 'rule_desc',
      // width: '95%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    }
  ];
  const conditionInfo = [
    {
      type: 'Select',
      label: '规则分类',
      key: 'category',
      props: {
        url: '/sqlreview/project/rule_category_list'
      },
      listeners: {
        change: (value) => {
          const condition = ctx.$refs.conditionForm;
          condition.saving({
            category: value,
            target_property: null
          });
          ctx.tableData = [{}];
        }
      },
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    (formData) => {
      return {
        type: 'Select',
        label: '目标属性',
        key: 'target_property',
        props: {
          url: '/sqlreview/project/rule_target_property',
          reqParams: {
            category: formData && formData.category
          }
        },
        width: '100%',
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      };
    }
  ];
  const columns = [
    {
      title: '规则名称',
      dataIndex: 'rule_name',
      key: 'rule_name',
      scopedSlots: { customRender: 'rule_name' }
    },
    {
      title: '规则描述',
      dataIndex: 'rule_desc',
      key: 'rule_desc',
      scopedSlots: { customRender: 'rule_desc' }
    },
    {
      title: '规则类型',
      dataIndex: 'rule_category',
      key: 'rule_category',
      scopedSlots: { customRender: 'rule_category' }
    }
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   key: 'action',
    //   scopedSlots: { customRender: 'action' },
    //   fixed: 'right'
    // }
  ];
  const condition = {
    columns: [
      {
        dataIndex: 'relation',
        key: 'relation',
        width: 120,
        scopedSlots: { customRender: 'relation' }
      },
      {
        dataIndex: 'method_comment',
        key: 'method_comment',
        width: 300,
        scopedSlots: { customRender: 'method_comment' }
      },
      {
        dataIndex: 'rule_value',
        key: 'rule_value',
        width: 300,
        scopedSlots: { customRender: 'rule_value' }
      },
      {
        key: 'action',
        width: 100,
        scopedSlots: { customRender: 'action' }
      }
    ],
    editConfig: (params = {}) => {
      return {
        relation: (row, record = {}, tableEdit, colVal) => {
          let options = [
            {
              label: '空置',
              value: ''
            },
            {
              label: 'AND',
              value: 'and'
            },
            {
              label: 'OR',
              value: 'or'
            }
          ];
          if (row > 0) {
            options = options.slice(1);
          }
          return {
            type: 'Select',
            props: {
              options,
              allowClear: false,
              disabled: row == 0,
              class: `relation-${colVal}`
            },
            initialValue: row == 0 ? '' : 'and',
            extra: (h) => {
              return (
                <Bracket color={colVal === 'and' ? '#d0eecc' : '#fce3cb'} />
              );
            }
            // rules: [
            //   { required: true, message: '该项为必填项' }
            // ]
          };
        },
        method_comment: (row, record = {}) => {
          return {
            type: 'Select',
            props: {
              url: '/sqlreview/project/rule_method_list',
              size: 'default',
              reqParams: { rule_type: 'DML' }
            },
            listeners: {
              change: (value) => {
                ctx.$refs.tableEdit.saving({
                  id: record.id,
                  method_id: value,
                  rule_value: null
                });
              }
            },
            rules: [{ required: true, message: '该项为必填项' }]
          };
        },
        rule_value: (row, record = {}) => {
          let type = '';
          let props = {};
          let rules = [{ required: true, message: '该项为必填项' }];
          const methodId = record.method_id;
          if (methodId == 16 || methodId == 15) {
            type = 'Select';
            props = {
              options: [
                {
                  label: '是',
                  value: 'true'
                },
                {
                  label: '否',
                  value: 'false'
                }
              ]
            };
          } else if (methodId == 4) {
            type = 'InputNumRange';
            rules = [
              {
                validator: function (rule, value, callback) {
                  if (value) {
                    if (value[0] == null || value[1] == null) {
                      callback(new Error('有输入项为空'));
                    }
                    if (value[0] >= value[1]) {
                      callback(new Error('最小值不能大于等于最大值'));
                    }
                    callback();
                  } else {
                    callback(new Error('有输入项为空'));
                  }
                }
              }
            ];
          } else {
            type = 'Input';
          }
          return {
            type,
            props: {
              ...props
            },
            rules: rules
          };
        }
      };
    }
  };
  const dmlCondition = {
    columns: [
      {
        dataIndex: 'relation',
        key: 'relation',
        width: 120,
        scopedSlots: { customRender: 'relation' }
      },
      {
        dataIndex: 'desc',
        key: 'desc',
        width: 300,
        scopedSlots: { customRender: 'desc' }
      },
      {
        dataIndex: 'condition',
        key: 'condition',
        width: 300,
        scopedSlots: { customRender: 'condition' }
      },
      {
        key: 'target_value',
        dataIndex: 'target_value',
        width: 300,
        scopedSlots: { customRender: 'target_value' }
      },
      {
        key: 'action',
        width: 100,
        scopedSlots: { customRender: 'action' }
      }
    ],
    editConfig: (params = {}) => {
      return {
        relation: (row, record = {}, tableEdit, colVal) => {
          let options = [
            {
              label: '空置',
              value: ''
            },
            {
              label: 'AND',
              value: 'and'
            },
            {
              label: 'OR',
              value: 'or'
            }
          ];
          if (row > 0) {
            options = options.slice(1);
          }
          return {
            type: 'Select',
            props: {
              options,
              allowClear: false,
              disabled: row == 0,
              class: `relation-${colVal}`
            },
            initialValue: row == 0 ? '' : 'and',
            extra: (h) => {
              return (
                <Bracket color={colVal === 'and' ? '#d0eecc' : '#fce3cb'} />
              );
            }
            // rules: [
            //   { required: true, message: '该项为必填项' }
            // ]
          };
        },
        desc: {
          type: 'InputModal',
          props: {
            style: 'width: 100%',
            suffixIcon: 'down'
          },
          rules: [{ required: true, message: '该项为必填项' }]
        },
        condition: {
          type: 'Select',
          props: {
            url: '/sqlreview/common/item-list/',
            size: 'default'
          },
          cellProps: (row, record = {}) => {
            let reqParams = {
              enable: 1,
              parent_item_key: 'rule_conditions'
            };
            if (record.value_operator) {
              reqParams.filter = record.value_operator;
            }
            // console.log(row, record, record.value_operator, reqParams, 888)
            return {
              reqParams
            };
          },
          rules: [{ required: true, message: '该项为必填项' }]
        },
        target_value: (row, record = {}) => {
          const { target } = record;
          let type = 'Select';
          let props = {};

          if (record.value_type === 'number') {
            type = 'InputNumber';
          } else if (record.value_type === 'boolean') {
            props = {
              options: [
                {
                  label: '是',
                  value: 'TRUE'
                },
                {
                  label: '否',
                  value: 'FALSE'
                }
              ]
            };
          } else {
            props = {
              url: '/sqlreview/common/item-list/',
              reqParams: {
                enable: 1,
                parent_item_key: target
              }
            };
          }
          return {
            type,
            props: {
              ...props,
              style: 'width: 100%',
              size: 'default'
            },
            rules: [{ required: true, message: '该项为必填项' }]
          };
        }
      };
    }
  };
  const searchFields = [
    {
      type: 'Input',
      label: '',
      props: {
        placeholder: '请输入关键字搜索'
      },
      key: 'rule_name'
    }
  ];

  return {
    baseInfo,
    ruleBaseInfo,
    columns,
    condition,
    dmlCondition,
    searchFields,
    conditionInfo
  };
}
