/*
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-03 16:54:50
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/config.js
 */

export default function (ctx) {
  const statusColor = {
    待评审: '#FC925E',
    评审中: '#4F96FB',
    已通过: '#6EC84A',
    未通过: '#F9676B',
    未提交: '#AFADAD'
  };
  const statusColors = {
    '0': 'rgb(252, 146, 94)',
    '1': 'rgb(110, 200, 74)',
    '-1': 'rgb(249, 103, 107)',
    null: 'rgb(110, 200, 74)'
  };
  const statusText = {
    '0': '待审核',
    '1': '已通过',
    '-1': '未通过',
    null: '已通过'
  };
  const cardColumns = [
    {
      title: '当前页',
      dataIndex: 'id',
      key: 'id',
      scopedSlots: { customRender: 'cardTable' }
    }
  ];
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      scopedSlots: { customRender: 'id' }
    },
    {
      title: '发起时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: 'Review对象名称',
      dataIndex: 'project_name',
      key: 'project_name',
      scopedSlots: { customRender: 'project_name' },
      width: 150
    },
    // {
    //   title: '任务类型',
    //   key: 'review_type',
    //   dataIndex: 'review_type',
    //   customRender: (text, record, index) => {
    //     return text == 1 ? '存储过程扫描' : '项目review';
    //   },
    //   width: 200
    // },
    {
      title: '项目组名',
      dataIndex: 'project_group',
      key: 'project_group',
      scopedSlots: { customRender: 'project_group' },
      width: 150
    },
    {
      title: 'Review版本',
      key: 'review_point',
      dataIndex: 'review_point',
      scopedSlots: { customRender: 'review_point' },
      width: 150
    },
    {
      title: 'SQL总数',
      key: 'sql_count',
      dataIndex: 'sql_count',
      customRender: (text, record, index) => {
        return text != null ? text : 0;
      },
      width: 100
    },
    {
      title: 'Review方式',
      key: 'mode',
      dataIndex: 'mode',
      customRender: (text, record, index) => {
        return text == '1' ? '增量' : '全量';
      },
      width: 120
    },
    {
      title: '历史标准基线',
      key: 'history_baseline',
      dataIndex: 'history_baseline',
      customRender: (text) => {
        return text == '1' ? '是' : '否';
      },
      width: 120
    },
    {
      title: 'DBA评审状态',
      key: 'dba_status',
      dataIndex: 'dba_status',
      scopedSlots: { customRender: 'dba_status' },
      width: 150
    },
    {
      title: '处理状态',
      dataIndex: 'review_process',
      key: 'review_process',
      scopedSlots: { customRender: 'review_process' },
      width: 150
    },
    {
      title: 'SQL通过率',
      key: 'passing_rate',
      dataIndex: 'passing_rate',
      width: 100
    },
    {
      title: 'DBA负责人',
      dataIndex: 'dba_leader',
      key: 'dba_leader',
      scopedSlots: { customRender: 'dba_leader' },
      width: 100
    },
    {
      title: '项目组负责人',
      dataIndex: 'project_group_leader',
      key: 'project_group_leader',
      scopedSlots: { customRender: 'project_group_leader' },
      width: 100
    },
    {
      title: '审核的DBA',
      dataIndex: 'operator_dba',
      key: 'operator_dba',
      scopedSlots: { customRender: 'operator_dba' },
      width: 110
    },
    {
      title: '发起人',
      dataIndex: 'created_by',
      key: 'created_by',
      scopedSlots: { customRender: 'created_by' },
      width: 100
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 180,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });
  const fields = [
    {
      type: 'Input',
      label: '项目组名',
      key: 'project_group'
    },
    {
      type: 'Input',
      label: 'ID',
      key: 'id',
      props: {
        placeholder: '搜索ID/应用名称'
      }
    },
    {
      type: 'RangePicker',
      label: '发起时间',
      key: 'created_at',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    },
    {
      type: 'Input',
      label: '审核对象名称',
      key: 'project'
    },
    {
      type: 'Input',
      label: 'Review版本',
      key: 'review_point'
    },
    {
      type: 'Select',
      label: '审核方式',
      key: 'mode',
      props: {
        options: [
          {
            label: '增量',
            value: '1'
          },
          {
            label: '全量',
            value: '0'
          }
        ]
      }
    },
    {
      type: 'Select',
      label: 'DBA评审状态',
      key: 'dba_status',
      props: {
        options: [
          {
            label: '未提交',
            value: '0'
          },
          {
            label: '待评审',
            value: '1'
          },
          {
            label: '评审中',
            value: '2'
          },
          {
            label: '已通过',
            value: '3'
          },
          {
            label: '未通过',
            value: '4'
          }
        ],
        separator: ',',
        mode: 'multiple'
      }
    },
    {
      type: 'Select',
      label: '历史标准基线',
      key: 'history_baseline',
      props: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ]
      }
    },
    {
      type: 'Input',
      label: '',
      key: 'btns',
      hideComponent: true,
      slots: [{ key: 'btns' }],
      rules: []
    }
  ];
  const dateSourceColumns = [
    {
      title: 'ID',
      dataIndex: 'data_source_id',
      key: 'data_source_id',
      scopedSlots: { customRender: 'id' }
    },
    {
      title: '数据源',
      dataIndex: 'data_source_name',
      key: 'data_source_name',
      scopedSlots: { customRender: 'data_source_name' },
      width: 100
    },
    {
      title: '关联项目',
      dataIndex: 'projects',
      key: 'projects',
      width: 220,
      scopedSlots: { customRender: 'limit-tag' }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      scopedSlots: { customRender: 'status' },
      width: 220
    },
    {
      title: '待审核标签数量',
      dataIndex: 'wait_audit_count',
      key: 'wait_audit_count',
      width: 150
    },
    {
      title: '白名单数量',
      dataIndex: 'white_count',
      key: 'white_count',
      width: 150
    },
    {
      title: '整改中数量',
      dataIndex: 'rectifying_count',
      key: 'rectifying_count',
      width: 120
    },
    {
      title: '已失效标签数量',
      dataIndex: 'expired_count',
      key: 'expired_count',
      width: 120
    },
    {
      title: 'DBA负责人',
      dataIndex: 'dba_leader',
      key: 'dba_leader',
      scopedSlots: { customRender: 'limit-tag' },
      width: 120
    },
    {
      title: '项目负责人',
      dataIndex: 'project_group_leader',
      key: 'project_group_leader',
      scopedSlots: { customRender: 'limit-tag' },
      width: 120
    },
    {
      title: '最近提交时间',
      dataIndex: 'latest_created_at',
      key: 'latest_created_at',
      scopedSlots: { customRender: 'latest_created_at' },
      width: 120
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 120,
      fixed: 'right'
    }
  ].map((item) => {
    return {
      ...item,
      width: undefined
    };
  });

  const dateSourceFields = [
    {
      type: 'Input',
      label: 'ID',
      key: 'data_source_id'
    },
    {
      type: 'Input',
      label: '数据源名称/IP',
      key: 'data_source_name'
    },
    {
      type: 'Input',
      label: '项目组名',
      key: 'project_group',
      props: {
        placeholder: '项目组名'
      }
    },
    {
      type: 'Select',
      label: '关联项目',
      key: 'projects',
      props: {
        url: '/sqlreview/review/all_project',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'Select',
      label: '负责人',
      key: 'leader',
      props: {
        url: ' /sqlreview/project_config/select_user',
        reqParams: {
          type: 'dba_and_leader'
        },
        allowSearch: true,
        backSearch: true,
        limit: 20
      }
    },
    {
      type: 'Select',
      label: '状态',
      key: 'status',
      props: {
        options: [
          { label: '待审核', value: 0 },
          { label: '已完成', value: 1 }
        ]
      }
    },
    {
      type: 'RangePicker',
      label: '提交时间',
      key: 'latest_created_at',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      }
    },
    {
      type: 'Input',
      label: '',
      key: 'btns',
      hideComponent: true,
      slots: [{ key: 'btns' }],
      rules: []
    }
  ];

  return {
    statusColor,
    statusColors,
    statusText,
    columns,
    cardColumns,
    searchFields: fields,
    dateSourceColumns,
    dateSourceFields
  };
}
