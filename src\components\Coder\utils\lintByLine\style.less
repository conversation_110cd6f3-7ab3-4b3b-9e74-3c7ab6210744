.CodeMirror-lintbyline {
  width: 20px;
}
.CodeMirror-lintbyline-item {
  width: 16px;
  height: 16px;
  top: 4px;
  right: 0;
  // bottom: 0;
  // background: #1890ff;
  position: absolute;
  cursor: pointer;
  // &:hover {
  //   background: #18b8ff;
  // }
  background: url('~@/assets/img/error.svg') no-repeat center;
  transform: scale(0.8);
  transform-origin: 100% 50%;
  // background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAHlBMVEW7AAC7AACxAAC7AAC7AAAAAAC4AAC5AAD///+7AAAUdclpAAAABnRSTlMXnORSiwCK0ZKSAAAATUlEQVR42mWPOQ7AQAgDuQLx/z8csYRmPRIFIwRGnosRrpamvkKi0FTIiMASR3hhKW+hAN6/tIWhu9PDWiTGNEkTtIOucA5Oyr9ckPgAWm0GPBog6v4AAAAASUVORK5CYII=);
}
.CodeMirror-lintbyline-item-tips {
  position: absolute;
  // left: 8px;
  // top: 8px;
  background: #FFFFFF;
  box-shadow: 0px 3px 6px -4px rgba(0,0,0,0.12);box-shadow: 0px 6px 16px 0px rgba(0,0,0,0.08);box-shadow: 0px 9px 28px 8px rgba(0,0,0,0.05);
  border-radius: 4px;
  padding: 12px;
  z-index: 1052;
  max-width: 600px;
  overflow: visible;
  font-size: 12px;
  color: #D9363E;
}
.CodeMirror-lintbyline-err-line {
  background: #FFF2F0;
}