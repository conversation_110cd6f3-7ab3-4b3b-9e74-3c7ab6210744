{"name": "dbcm-web", "version": "1.0.0", "description": "project DBCM", "author": "yin<PERSON>i", "private": true, "scripts": {"dev": "webpack-dev-server --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "unit": "jest --config test/unit/jest.conf.js --coverage", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "lint": "eslint --ext .js,.vue src test/unit test/e2e/specs", "build": "node build/build.js --report", "tf": "node scripts/copy.js toFrame", "ff": "node scripts/copy.js fromFrame"}, "dependencies": {"@toast-ui/editor": "^3.1.3", "ace-builds": "^1.40.0", "ant-design-vue": "^1.5.3", "axios": "^0.18.0", "clipboard": "^2.0.1", "codemirror": "5.63.3", "date-fns": "^1.29.0", "diff-match-patch": "^1.0.5", "diff2html": "^3.4.15", "driver.js": "^1.3.1", "echarts": "^5.0.0", "element-ui": "^2.15.14", "highlight.js": "^10.6.0", "js-base64": "^2.5.1", "js-cookie": "^2.2.1", "jsencrypt": "^3.2.1", "json-beautify": "^1.1.1", "jsplumb": "^2.12.8", "lodash": "^4.17.20", "markdown-it": "^12.3.0", "markdown-it-abbr": "^1.0.4", "markdown-it-container": "^2.0.0", "markdown-it-deflist": "^2.1.0", "markdown-it-emoji": "^2.0.0", "markdown-it-footnote": "^3.0.3", "markdown-it-ins": "^3.0.1", "markdown-it-mark": "^3.0.1", "markdown-it-sub": "^1.0.0", "markdown-it-sup": "^1.0.0", "nprogress": "^0.2.0", "panzoom": "^9.4.2", "prismjs": "^1.30.0", "sortablejs": "^1.10.1", "spark-md5": "^3.0.2", "sql-formatter": "^8.2.0", "v-tooltip": "^2.1.3", "vue": "2.7.10", "vue-contextmenujs": "^1.4.7", "vue-json-viewer": "^2.2.22", "vue-page-split": "^1.2.2", "vue-prism-component": "^1.2.0", "vue-resize-split-pane": "^0.1.5", "vue-router": "^3.0.1", "vuex": "^3.0.1", "wangeditor": "^4.7.12", "xml-formatter": "^2.6.0"}, "devDependencies": {"ansi_up": "^4.0.4", "autoprefixer": "^7.1.2", "babel-cli": "^6.26.0", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "^21.0.2", "babel-loader": "^7.1.1", "babel-plugin-dynamic-import-node": "^1.2.0", "babel-plugin-lodash": "^3.3.4", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-unicode-regexp-runtime": "^1.0.2", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "cross-spawn": "^5.0.1", "css-loader": "^0.28.0", "eslint": "^4.15.0", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^4.0.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "jest": "^22.0.4", "jest-serializer-vue": "^0.3.0", "less": "^3.7.1", "less-loader": "^4.1.0", "lodash-webpack-plugin": "^0.11.5", "nightwatch": "^0.9.12", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-plugin-px2rem": "^0.8.1", "postcss-url": "^7.2.1", "promise.prototype.finally": "^3.1.2", "resize-observer-polyfill": "^1.5.1", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "sass-resources-loader": "^2.0.1", "selenium-server": "^3.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-jest": "^1.0.2", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "2.7.10", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.13.1", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}