import Bracket from '@/components/Biz/Bracket';
export default function (ctx) {
  const { type } = ctx;
  const isAdd = false;
  const baseInfo = [
    {
      type: 'Select',
      label: '规则集类型',
      key: 'rule_type',
      props: {
        options: [
          {
            label: 'DDL规则',
            value: 'DDL'
          },
          {
            label: 'DML规则',
            value: 'DML'
          }
        ],
        disabled: type === 'add' ? isAdd : !isAdd
      },
      listeners: {
        change: (value) => {
          const baseInfo = ctx.$refs.baseInfo;
          baseInfo.saving({
            rule_type: value,
            db_type: null
          });
          if (value === 'DDL') {
            ctx.$emit('change', true);
          } else {
            ctx.$emit('change', false);
          }
        }
      },
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Select',
      label: '数据库类型',
      key: 'db_type',
      props: {
        url: 'sqlreview/project/rule_support_db',
        reqParams: {
          rule_set_type: 'DML'
        },
        placeholder: '请选择数据库',
        disabled: type === 'add' ? isAdd : !isAdd
      },
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
      listeners: {
        change: (value) => {
          ctx.onSelectChange(value);
        }
      }
    },
    {
      type: 'Input',
      label: '规则集名称',
      key: 'name',
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
      props: {
        disabled: type == 'edit' ? !isAdd : isAdd,
        placeholder: '建议在名称前加数据库类型，例如mysql_规则集1'
      }
    }
    // {
    //   type: 'Select',
    //   label: '是否为默认规则集',
    //   key: 'is_default',
    //   props: {
    //     options: [
    //       {
    //         label: '是',
    //         value: 1
    //       },
    //       {
    //         label: '否',
    //         value: 0
    //       }
    //     ],
    //     disabled: type === 'edit' ? !isAdd : isAdd
    //   },
    //   width: '50%'
    //   // props: {
    //   //   size: 'default',
    //   //   'checked-children': '开',
    //   //   'un-checked-children': '关'
    //   // },
    //   // width: '40',
    //   // rules: []
    // }
  ];
  const ruleBaseInfo = [
    {
      type: 'Input',
      label: '规则名称',
      key: 'name',
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Input',
      label: '规则描述',
      key: 'desc',
      width: '100%',
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    },
    {
      type: 'Select',
      label: '规则分类',
      key: 'type',
      width: '100%',
      props: {
        url: '/sqlreview/project/get_rule_type'
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    }
    // {
    //   type: 'Select',
    //   label: '风险等级',
    //   key: 'level',
    //   props: {
    //     url: '/sqlreview/project/get_rule_level',
    //     reqParams: {
    //       edit: 1
    //     }
    //   },
    //   rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
    // }
  ];
  const columns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      scopedSlots: { customRender: 'name' }
    },
    {
      title: '规则描述',
      dataIndex: 'desc',
      key: 'desc',
      scopedSlots: { customRender: 'desc' }
    },
    {
      title: '规则类型',
      dataIndex: 'rule_category',
      key: 'rule_category',
      scopedSlots: { customRender: 'rule_category' }
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      fixed: 'right'
    }
  ];
  const condition = {
    columns: [
      {
        dataIndex: 'relation',
        key: 'relation',
        width: 120,
        scopedSlots: { customRender: 'relation' }
      },
      {
        dataIndex: 'desc',
        key: 'desc',
        width: 300,
        scopedSlots: { customRender: 'desc' }
      },
      {
        dataIndex: 'condition',
        key: 'condition',
        width: 300,
        scopedSlots: { customRender: 'condition' }
      },
      {
        key: 'target_value',
        dataIndex: 'target_value',
        width: 300,
        scopedSlots: { customRender: 'target_value' }
      },
      {
        key: 'action',
        width: 100,
        scopedSlots: { customRender: 'action' }
      }
    ],
    editConfig: (params = {}) => {
      return {
        relation: (row, record = {}, tableEdit, colVal) => {
          let options = [
            {
              label: '空置',
              value: ''
            },
            {
              label: 'AND',
              value: 'and'
            },
            {
              label: 'OR',
              value: 'or'
            }
          ];
          if (row > 0) {
            options = options.slice(1);
          }
          return {
            type: 'Select',
            props: {
              options,
              allowClear: false,
              disabled: row == 0,
              class: `relation-${colVal}`
            },
            initialValue: row == 0 ? '' : 'and',
            extra: (h) => {
              return (
                <Bracket color={colVal === 'and' ? '#d0eecc' : '#fce3cb'} />
              );
            }
            // rules: [
            //   { required: true, message: '该项为必填项' }
            // ]
          };
        },
        desc: {
          type: 'InputModal',
          props: {
            style: 'width: 100%',
            suffixIcon: 'down'
          },
          rules: [{ required: true, message: '该项为必填项' }]
        },
        condition: {
          type: 'Select',
          props: {
            url: '/sqlreview/common/item-list/',
            size: 'default'
          },
          cellProps: (row, record = {}) => {
            let reqParams = {
              enable: 1,
              parent_item_key: 'rule_conditions'
            };
            if (record.value_operator) {
              reqParams.filter = record.value_operator;
            }
            // console.log(row, record, record.value_operator, reqParams, 888)
            return {
              reqParams
            };
          },
          rules: [{ required: true, message: '该项为必填项' }]
        },
        target_value: (row, record = {}) => {
          const { target } = record;
          let type = 'Select';
          let props = {};

          if (record.value_type === 'number') {
            type = 'InputNumber';
          } else if (record.value_type === 'boolean') {
            props = {
              options: [
                {
                  label: '是',
                  value: 'TRUE'
                },
                {
                  label: '否',
                  value: 'FALSE'
                }
              ]
            };
          } else {
            props = {
              url: '/sqlreview/common/item-list/',
              reqParams: {
                enable: 1,
                parent_item_key: target
              }
            };
          }
          return {
            type,
            props: {
              ...props,
              style: 'width: 100%',
              size: 'default'
            },
            rules: [{ required: true, message: '该项为必填项' }]
          };
        }
      };
    }
  };
  const outputConfig = {
    key: {
      options: [
        {
          label: '审批意见',
          value: 0
        }
      ]
    },
    value: {
      options: [
        {
          label: '拒绝',
          value: 0
        },
        {
          label: '通过',
          value: 1
        }
      ]
    }
  };
  const searchFields = [
    {
      type: 'Select',
      label: '',
      key: 'rule_category',
      props: {
        url: '/sqlreview/project/dml_rule_config_category',
        reqParams: {},
        loading: true,
        placeholder: '请选择规则分类'
      }
      // rules: [{ required: true, message: '该项为必填项' }]
    },
    {
      type: 'Input',
      label: '',
      props: {
        placeholder: '请输入关键字搜索'
      },
      key: 'rule_name'
    }
  ];
  const DMLSearchFields = [
    {
      type: 'Input',
      label: '规则名称',
      key: 'rule_name',
      sourceKey: 'name',
      props: {
        placeholder: '请输入规则名称'
      }
    },
    {
      type: 'Input',
      label: '规则描述',
      key: 'rule_desc',
      sourceKey: 'desc',
      props: {
        placeholder: '请输入规则描述'
      }
    },
    {
      type: 'Select',
      label: '风险等级',
      key: 'rule_level',
      sourceKey: 'level',
      props: {
        options: [
          {
            label: '未知',
            value: '0'
          },
          {
            label: '高风险',
            value: '1'
          },
          {
            label: '中风险',
            value: '2'
          },
          {
            label: '低风险',
            value: '3'
          },
          {
            label: '无风险',
            value: '9'
          }
        ],
        placeholder: '请选择风险等级'
      }
    }
  ];
  return {
    baseInfo,
    ruleBaseInfo,
    columns,
    condition,
    outputConfig,
    searchFields,
    DMLSearchFields
  };
}
