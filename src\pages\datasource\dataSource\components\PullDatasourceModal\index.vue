<template>
  <a-modal
    v-model="visible"
    title="拉取数据源"
    wrapClassName="config-datasource-pull-datasource-modal"
    width="60%"
    :dialogStyle="{ 'minWidth': '1200px', 'maxWidth': '1300px' }"
  >
    <a-spin :spinning="spinning">
      <div class="seach-area-content">
        <Form ref="form" v-bind="formParams" :formData="formData" class="fixed-label-left"></Form>
        <div class="seach-area-btns">
          <a-button @click="reset">重置</a-button>
          <a-button type="primary" @click="search">查询</a-button>
        </div>
      </div>
      <Table ref="table" v-bind="tableParams" @selectChange="selectChange"></Table>
    </a-spin>
    <template slot="footer">
      <a-button type="default" @click="hide">关闭</a-button>
      <a-button type="primary" @click="save">拉取</a-button>
    </template>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
import Table from '@/components/Table';
import config from './config';
import { pullDatasource } from '@/api/config/dataSource';
export default {
  components: {
    Form,
    Table
  },
  props: {},
  data() {
    this.config = config(this);
    return {
      visible: false,
      spinning: false,
      formData: {},
      formParams: {
        // gutter: 100,
        layout: 'horizontal',
        // layout: 'inline',
        // clone: true,
        labelCol: { span: 8 },
        wrapperCol: { span: 12 },
        multiCols: 9,
        fields: [
          {
            type: 'Select',
            label: '数据源类型',
            key: 'db_type',
            props: {
              options: [
                {
                  label: 'ORACLE',
                  value: 'ORACLE'
                },
                {
                  label: 'MYSQL',
                  value: 'MYSQL'
                }
                // {
                //   label: 'Pgsql',
                //   value: 'postgre'
                // },
                // {
                //   label: 'Tidb',
                //   value: 'tidb'
                // },
                // {
                //   label: 'OceanBase',
                //   value: 'OceanBase'
                // },
                // {
                //   label: 'TDSQL',
                //   value: 'TDSQL'
                // }
              ]
            }
          },
          {
            type: 'Input',
            label: '数据源IP',
            key: 'ip'
          },
          {
            type: 'Input',
            label: '数据源名称',
            key: 'db_name'
          }
        ]
      },
      tableParams: {
        url: '/sqlreview/faced/get_datasource_list',
        reqParams: {
          db_type: 'MYSQL',
          _t: +new Date()
        },
        method: 'post',
        rowKey: 'id',
        rowSelection: {},
        columns: this.config.columns
      }
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    show() {
      this.visible = true;
      this.tableParams.reqParams = Object.assign(
        {},
        this.tableParams.reqParams,
        { _t: +new Date() }
      );
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    reset() {
      const { form, table } = this.$refs;
      form.resetFields();
      table.refresh();
    },
    search() {
      const { form } = this.$refs;
      const params = form.getData();
      this.$set(this.tableParams, 'reqParams', params);
    },
    // 表格全选
    onSelectAll(e) {
      this.isSelectAll = e;
    },
    // 表格勾选值
    selectChange(data = {}) {
      this.selectedRowKeys = data.selectedRowKeys;
    },
    save() {
      const { table } = this.$refs;
      const params = table.selectedRows || [];
      if (_.isEmpty(params)) {
        return this.$message.warn('请选择需要拉取的数据源');
      }
      this.$showLoading();
      pullDatasource({ data_list: params })
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.$hideLoading({ useMessage: true, tips: '拉取成功' });
            this.hide();
            this.$emit('refresh');
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(err => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(err, 'data.message')
          });
        });
    }
  }
};
</script>

<style scoped lang="less">
.seach-area-content {
  display: flex;
  justify-content: space-between;
  // align-items: center;
  .seach-area-btns {
    text-align: right;
    padding-top: 4px;
    .ant-btn {
      margin-left: 8px;
    }
  }
}
/deep/.ant-row {
  width: 100%;
  display: flex;
  .ant-col-12 {
    min-width: 200px;
  }
}
/deep/ .multi-cols-item {
  display: flex !important;
  align-items: center;
  > .ant-col {
    padding-right: 8px;
  }
}
</style>
