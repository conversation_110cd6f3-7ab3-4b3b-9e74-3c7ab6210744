import Vue from 'vue';
import Form from './Form';
import Icon from './Icon';
import BtnsWrapper from './BtnsWrapper';
import Empty from './Empty';
import DbImg from '@/components/CustomImg/DbImg';
import CustomFormComponents from './Form/register';
import './Biz/index';

// 注册全局组件，可以防止基础组件递归引用
Vue.component(GLOBAL_COMPONENTS['custom-form'], Form);
Vue.component(GLOBAL_COMPONENTS['custom-icon'], Icon);
Vue.component(GLOBAL_COMPONENTS['custom-btns-wrapper'], BtnsWrapper);
Vue.component(GLOBAL_COMPONENTS['custom-empty'], Empty);

Vue.component('VNode', {
  functional: true,
  render(createElement, context) {
    const { props = {} } = context;
    const { node } = props;
    return node ? node() : '';
  }
});

// 注册审批模板组件
CustomFormComponents.setFormComp('DbImg', DbImg);