<template>
  <div :class="['custom-blocks', `${blockClass}`]">
    <template v-if="mode === 'default'">
      <template v-for="(item, index) in blocks">
        <div class="block-wrapper base-info" :style="item.style" :key="item.key">
          <div class="title">
            <div style="display: flex; align-items: center;">
              <a-icon v-if="item.icon" :type="item.icon" />
              <span>{{item.title}}</span>
              <a
                class="tips"
                :style="{ cursor: 'default' }"
                v-if="item.titleTips"
              >{{item.titleTips}}</a>
              <slot :name="item.titleSlot"></slot>
            </div>
            <slot :name="item.extra"></slot>
          </div>
          <slot v-if="spinning == false" :name="item.key"></slot>
          <a-skeleton v-else active />
        </div>
        <a-divider :key="item.key + 'divider'" v-if="index < blocks.length - 1 && divider" />
      </template>
    </template>
    <template v-else>
      <a-card v-for="(item, index) in blocks" v-bind="cardProps" :key="index">
        <template slot="title">
          <div style="display: flex; align-items: center;">
            <span>{{item.title}}</span>
            <slot v-if="item.titleSlot" :name="item.titleSlot"></slot>
          </div>
        </template>
        <slot :name="item.key"></slot>
      </a-card>
    </template>
  </div>
</template>

<script>
// import _ from 'lodash';

// 默认属性
const defaultProps = {
  bordered: false
};

export default {
  props: {
    mode: {
      type: String,
      default: 'default'
    },
    divider: {
      type: Boolean,
      default: true
    },
    blockClass: {
      type: String,
      default: '' // split-blocks
    },
    value: {
      type: Array,
      default: () => []
    },
    spinning: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  data() {
    return {
      blocks: this.value
    };
  },
  computed: {
    cardProps() {
      return { ...defaultProps, ...this.$attrs };
    }
  },
  mounted() {},
  methods: {},
  watch: {
    value(newVal, oldVal) {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        this.blocks = newVal;
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import '~@/style/private/index.less';
@hpadding: 40px;
.custom-blocks {
  .block-wrapper {
    background: #fff;
  }
  .custom-detail-page;
  .tips {
    font-size: 12px;
    margin-left: 10px;
  }
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &.split-blocks {
    .block-wrapper {
      margin-bottom: 20px;
      &:hover {
        background: #fff;
      }
      .title {
        padding-bottom: 20px;
        margin-bottom: 0;
        border-bottom: 1px solid #e8e8e8;

        .anticon {
          color: rgba(0, 0, 0, .85);
        }
      }
    }
  }
}
</style>
