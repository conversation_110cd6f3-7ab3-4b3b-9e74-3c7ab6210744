<template>
  <a-drawer
    title="权限管理"
    :visible="visible"
    @close="hide"
    :width="'55%'"
    :bodyStyle="{ padding: '24px 24px 80px 24px' }"
    wrapClassName="config-datasource-auth-manager-drawer"
  >
    <div class="datarsource-anth-manager-header-info">
      <a-row>
        <a-col :span="11">
          <div class="header-info-col">{{title}}</div>
          <div class="field-info" v-if="type == 'field'">
            <span>
              <custom-icon type="lu-icon-field" />
              {{data.name}}
            </span>
            <span>{{data.type}}</span>
          </div>
          <InstanceItem v-else :tagText="data.env" :src="data.db_type" :text="data.name"></InstanceItem>
        </a-col>
        <a-col :span="2"></a-col>
        <a-col :span="11" v-if="type == 'instance'">
          <div class="header-info-col">管理模式</div>
          <div class="manage-type">
            <custom-icon
              :type="data.is_permission_control == 1 ? 'lu-icon-safe' : 'lu-icon-operation'"
            ></custom-icon>
            <span style="margin-left: 8px">{{data.is_permission_control == 0 ? '自由操作' : '安全协同'}}</span>
          </div>
        </a-col>
        <a-col :span="11" v-if="['field', 'tableDetail'].includes(type)">
          <div class="header-info-col">表名</div>
          <div class="manage-type">
            <custom-icon type="lu-icon-list" style="margin-right: 8px" />
            <span>{{data.table_name}}</span>
          </div>
        </a-col>
      </a-row>
    </div>
    <div class="auth-middle-box">
      <span>全部授权用户</span>
      <a-badge
        :count="count"
        :overflowCount="99"
        :number-style="{
          backgroundColor: '#eee',
          fontSize: '12px',
          color: 'rgba(0,0,0,0.65)'
        }"
      />
    </div>
    <!-- 搜索区域 -->
    <div class="search-area">
      <a-button @click="onAuth" type="primary">+授权用户</a-button>
      <DropdownSearch
        :DropdownSearchList="DropdownSearchList"
        @dropdownSearch="dropdownSearch"
        @search="search"
        :placeholder="placeholder"
      ></DropdownSearch>
    </div>
    <Table
      ref="table"
      v-bind="tableParams || {}"
      :dataSource="dataSource || []"
      class="table-new-mode"
    >
      <Shower
        slot="Shower"
        slot-scope="{ text, record, column }"
        v-bind="{ showerValue: text, showerData: record, config: column.shower }"
      >
        <template slot="permission_type">
          <div>权限类型</div>
          <div v-if="type == 'instance'">
            <template v-for="item in text.split(',')">
              <a-tag :color="permissionTypeColor[item]" :key="item">{{item == 1 ? '数据库登录' : '实例登录'}}</a-tag>
            </template>
          </div>
          <div v-else>
            <template v-for="item in text.split(',')">
              <a-tag :key="item">{{dataSourcePermissionType[item]}}</a-tag>
            </template>
          </div>
        </template>
        <template slot="user_and_type">
          <div>{{record.user_name}}</div>
          <div>
            <a-icon type="user" style="margin-right: 8px" />
            <span>{{record.user_type == 1 ? '用户组' : '用户'}}</span>
          </div>
        </template>
        <template slot="created_and_updated">
          <div>
            <span>生效时间：</span>
            <DateFormat :text="record.updated_at" :limit="20" />
          </div>
          <div>
            <span>过期时间：</span>
            <span>{{'--'}}</span>
            <!-- <DateFormat :text="record.updated" :limit="20" /> -->
          </div>
        </template>
      </Shower>

      <custom-btns-wrapper slot="action" slot-scope="{ text, record }" :limit="3">
        <!-- <a @click="editProject(record)" actionBtn>编辑</a> -->
        <a-popconfirm title="确定删除?" @confirm="() => removeProject(record)" actionBtn>
          <a class="remove">删除</a>
        </a-popconfirm>
      </custom-btns-wrapper>
    </Table>
    <BatchAuthDrawer  @refresh="onRefresh" ref="batchAuth"></BatchAuthDrawer>
  </a-drawer>
</template>

<script>
import { permissionManageDelete } from '@/api/config/dataSource';
import BatchAuthDrawer from '../../components/batchAuthDrawer';
import DateFormat from '@/components/DateFormat';
import Shower from '@/components/Shower';
import InstanceItem from '@/components/Biz/InstanceItem';
import DropdownSearch from '@/components/Biz/DropdownSearch';
import Table from '@/components/Table';
import config from './config';

export default {
  components: {
    Table,
    Shower,
    DateFormat,
    InstanceItem,
    DropdownSearch,
    BatchAuthDrawer
  },
  props: {
    DropdownSearchList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    this.config = config(this);
    return {
      visible: false,
      tableParams: {
        url: '/sqlreview/project/permission_manage',
        reqParams: {},
        columns: this.config.columns,
        rowKey: 'id',
        // needTools: true,
        // needSearchArea: true,
        showHeader: false,
        searchFields: this.config.searchFields,
        loaded: this.onTableLoaded
      },
      dataSource: [],
      data: {},
      id: '',
      count: 0,
      placeholder: '请输入用户、用户组名',
      dataSourcePermissionType: {
        0: '查询',
        1: 'DDL',
        2: 'DML'
      },
      permissionTypeColor: {
        0: '#87E8DE',
        1: '#87d068'
      },
      type: '',
      title: ''
    };
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(data = {}, extra = {}, type) {
      _.isEmpty(extra)
        ? (this.data = data)
        : (this.data = { ...data, ...extra });
      this.id = data.id;
      this.type = type;

      const obj = {
        instance: '实例信息',
        database: '数据库信息',
        tableDetail: '数据库信息',
        field: '字段信息'
      };
      this.title = obj[type];

      let reqKeys = {
        instance: 'data_source_id',
        database: 'schema_id',
        tableDetail: 'table_id',
        field: 'columns_id'
      };
      let res = reqKeys[type];
      this.$set(this.tableParams, 'reqParams', {
        [res]: this.id,
        _t: +new Date()
      });

      this.visible = true;
    },
    onTableLoaded(req, res) {
      this.dataSource = res.data.data.results;
      this.count = res.data.data.count;
    },
    // 授权用户
    onAuth() {
      this.$refs.batchAuth.show([this.id], null, this.type);
    },
    // 刷新表格
    onRefresh() {
      this.$refs.table.refresh();
    },
    // 搜索
    search(params) {
      this.$refs.table.refresh(null, { searchValue: params });
    },
    // 下拉搜索
    dropdownSearch(data) {
      const { table } = this.$refs;
      const { searchParams } = table;
      Object.assign(searchParams, data);
      table.refresh();
    },
    hide() {
      this.visible = false;
    },
    // 编辑项目
    editProject(record) {},
    // 删除项目
    removeProject(record) {
      const { table } = this.$refs;
      // 请求
      this.$showLoading();
      let obj = {
        instance: 'data_source',
        database: 'schema',
        tableDetail: 'table',
        field: 'columns'
      };
      permissionManageDelete({
        permission_id: record.id,
        // type实例是 data_source, 数据库是 schema, 表 table
        type: obj[this.type]
      })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.$hideLoading({ tips: _.get(res, 'data.message') });
            table.refresh();
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.config-datasource-auth-manager-drawer {
  /deep/ .ant-drawer-content-wrapper {
    min-width: 700px;
  }
}
.search-area {
  margin-bottom: 8px;
  position: relative;
  .ant-btn {
    position: absolute;
    top: 0px;
    right: 210px;
  }
}
.datarsource-anth-manager-header-info {
  .manage-type {
    border: 1px solid rgba(240, 240, 240, 1);
    border-radius: 4px;
    line-height: 1.5;
    padding: 9px 16px;
  }
  .header-info-col {
    margin-bottom: 12px;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-weight: 500;
  }
}
.auth-middle-box {
  margin: 36px 0 16px 0;
  > span {
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-weight: 500;
  }
}
/deep/.shower-content {
  display: flex;
  flex-direction: column;
  > div:first-child {
    margin-bottom: 8px;
  }
}
.field-info {
  display: flex;
  justify-content: flex-start;
  border: 1px solid rgba(240, 240, 240, 1);
  border-radius: 4px;
  line-height: 1.5;
  padding: 9px 16px;
  .anticon {
    margin-right: 8px;
  }
  > span:first-child {
    width: 50%;
  }
  > span:last-child {
    width: 50%;
  }
}
</style>
