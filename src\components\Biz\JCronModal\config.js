export default function (ctx) {
  const cornInfo = [
    // 天 周 月
    {
      type: 'Select',
      label: '',
      key: 'type',
      width: '100%',
      props: {
        options: [
          {
            label: '每周',
            value: 'week'
          },
          {
            label: '每天',
            value: 'day'
          },
          {
            label: '每小时',
            value: 'hour'
          }
        ]
      },
      listeners: {
        change: (value) => {
          ctx.type = value;
          ctx.$refs.form.saving({
            type: value,
            week: null,
            time: null
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    },
    // 每周
    (formData = {}) => {
      return {
        type: 'Select',
        label: '',
        key: 'week',
        width: '100%',
        props: {
          options: [
            {
              label: '星期一',
              value: '2',
              cron: 2
            },
            {
              label: '星期二',
              value: '3',
              cron: 3
            },
            {
              label: '星期三',
              value: '4',
              cron: 4
            },
            {
              label: '星期四',
              value: '5',
              cron: 5
            },
            {
              label: '星期五',
              value: '6',
              cron: 6
            },
            {
              label: '星期六',
              value: '7',
              cron: 7
            },
            {
              label: '星期日',
              value: '1',
              cron: 1
            }
          ]
        },
        visible: formData.type === 'week',
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              week: value,
              time: null
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      };
    },
    // 时间
    (formData = {}) => {
      return {
        type: 'TimePicker',
        label: '',
        key: 'time',
        width: '100%',
        props: {
          secondStep: 60,
          format: formData.type === 'hour' ? 'mm:ss' : 'HH:mm:ss'
        },
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              time: value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      };
    }
  ];

  return { cornInfo };
}
