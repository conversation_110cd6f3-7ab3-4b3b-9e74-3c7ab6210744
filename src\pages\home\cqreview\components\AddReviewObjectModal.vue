<!--
 * @Author: your name
 * @Date: 2021-01-29 17:24:51
 * @LastEditTime: 2021-02-02 14:06:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blocksx/Users/<USER>/work/aisqlreview-web/src/pages/home/<USER>/components/AddModal.vue
-->
<template>
  <!-- 新建审核对象弹窗 -->
  <a-modal
    v-model="visible"
    title="新建审核对象"
    okText="保存"
    width="580px"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="spinning">
      <Form ref="form" v-bind="params" :formData="formData" class="add-form">
      </Form>
    </a-spin>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
const formParams = ctx => {
  return {
    layout: 'vertical',
    labelCol: { span: 24 },
    wrapperCol: { span: 24 },
    fields: [
      {
        type: 'Textarea',
        label: '效率云卡片号或CQ单号：',
        key: 'ref_id',
        props: {
          placeholder: '可填写多个，多个单号时以逗号“,”分隔，禁止同时输入两种类型单号',
          rows: 6
        },
        listeners: {
          change: value => {}
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      }
    ]
  };
};

export default {
  components: { Form },
  props: {},
  data() {
    return {
      spinning: false,
      visible: false,
      formData: {},
      params: formParams(this)
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(type = '') {
      this.type = type;
      this.data = {};
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      this.formData = {};
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { form } = this.$refs;
      const data = form.getData();
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .add-form {
  .ant-row .ant-form-item-control-wrapper {
    .biz-instance-item {
      .ant-tag {
        border: none !important;
      }
      .instance-item-tag {
        padding: 4px 16px !important;
        width: 150px;
      }
    }
  }
}
</style>
