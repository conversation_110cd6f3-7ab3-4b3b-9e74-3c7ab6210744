<template>
  <div class="config-system-input">
      <a-textarea v-model="inputValue" autoSize></a-textarea>
  </div>
</template>

<script>

export default {
  props: {
    data: Object
  },
  data() {
    return {
      inputValue: _.cloneDeep(this.data.item_value),
      id: null
    };
  },
  watch: {
    data(val) {
      this.inputValue = _.cloneDeep(this.data.item_value)
    }
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.config-system-input {
  margin-top: 20px;
  // margin-left: 20px;
  textarea {
    max-height: 300px !important;
  }
}
</style>
