<template>
  <a-collapse v-model="activeKey">
    <a-collapse-panel key="1" header="horizontal -> label固定、元素宽度自定义">
      <Form ref="form1" v-bind="params || {}" :formData="formData" class="test-form"></Form>
      <a-modal
        v-model="visible"
        title="弹窗测试"
        okText="保存"
        :maskClosable="false"
        :width="'50%'"
        :dialogStyle="{ 'minWidth': '400px', 'maxWidth': '800px' }"
        @cancel="onCancel"
        @ok="onOk"
      >
        <Coder ref="Coder" v-model="CoderData" type="xml" :needFormat="true" placeholder="请输入xml"></Coder>
      </a-modal>
      <a-button @click="test">测试</a-button>
      <a-button @click="openModal">弹窗</a-button>
      <a-button @click="setCoderErr">coder err</a-button>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="vertical -> 多列布局">
      <Form v-bind="params1 || {}" class="test-form"></Form>
    </a-collapse-panel>
    <a-collapse-panel key="3" header="radioForm">
      <RadioForm v-bind="params2 || {}"></RadioForm>
    </a-collapse-panel>
  </a-collapse>
</template>

<script>
import Form from '@/components/Form';
import RadioForm from '@/components/Biz/RadioForm';
import Coder from '@/components/Coder';
// import common from '@/utils/common';
import config from './config';

export default {
  components: { Form, RadioForm, Coder },
  props: {},
  data() {
    this.config = config(this);
    return {
      activeKey: ['1', '2'],
      params: {
        fields: this.config.fields,
        fixedLabel: true,
        layout: 'horizontal',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        valueFormat: 'YYYY'
      },
      visible: false,
      CoderData: '',
      formData: {
        YearPicker: '2090',
        Coder:
          'CREATE TABLE public.tb_duguqiubai (name varchar(32) NOT NULL ,id int2 NOT NULL ,CONSTRAINT pk_tb_duguqiubai PRIMARY KEY (id))  WITH( OIDS=FALSE) TABLESPACE pg_default;',
        Markdown: 'ssss,,,dfjfjfjkdkdkd,,,dfjdfj'
        // UserRoleModal: [
        //   {
        //     owner_type: 'user',
        //     owner_id: 30,
        //     owner_name: 'DEVS'
        //   },
        //   {
        //     owner_type: 'user',
        //     owner_id: 29,
        //     owner_name: 'TEST'
        //   },
        //   {
        //     owner_type: 'user',
        //     owner_id: 68,
        //     owner_name: 'JOJO14'
        //   },
        //   {
        //     owner_type: 'user',
        //     owner_id: 27,
        //     owner_name: 'ADMIN'
        //   },
        //   {
        //     owner_type: 'role',
        //     owner_id: 128,
        //     owner_name: 'dev'
        //   },
        //   {
        //     owner_type: 'group',
        //     owner_id: 36,
        //     owner_name: '测试2'
        //   }
        // ]
      },
      params1: {
        fields: this.config.fields1,
        gutter: 24,
        multiCols: 3,
        layout: 'vertical'
      },
      params2: {
        config: this.config.radioFormConfig
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    test() {
      this.$refs.form1.validate(valid => {
        console.log(valid, this.$refs.form1.getData(), this.formData, 777);
      });
    },
    openModal() {
      this.visible = true;
    },
    onCancel() {
      this.visible = false;
    },
    onOk() {
      this.visible = false;
      console.log('coder value ', this.CoderData);
    },
    setCoderErr() {
      this.xx = !this.xx;
      this.$refs.form1.$refs.Coder[0].signError([
        { line: this.xx ? 2 : 1, tips: '解析失败！' },
        {
          line: this.xx ? 3 : 4,
          tips:
            'dkfjksdjflj打开房间肯德基发快递今飞凯达dkfjksdjflj打开房间肯德基发快递今飞凯达dkfjksdjflj打开房间肯德基发快递今飞凯达dkfjksdjflj打开房间肯德基发快递今飞凯达dkfjksdjflj打开房间肯德基发快递今飞凯达'
        }
      ]);
    }
  },
  watch: {}
};
</script>

<style lang="less" scoped>
</style>