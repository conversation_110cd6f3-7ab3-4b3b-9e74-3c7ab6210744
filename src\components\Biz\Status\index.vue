<template>
  <div class="status">
    <span class="status-icon" :style="{background: currStatus.color}"></span>
    <span>{{currStatus.text}}</span>
    <a-tooltip v-if="status == '9' && message">
      <template slot="title">
        <span>{{message}}</span>
      </template>
      <a-icon style="marginLeft:4px" type="question-circle" />
    </a-tooltip>
  </div>
</template>

<script>
// import common from '@/utils/common';
// import _ from 'lodash';
const statusMap = {
  '0': {
    text: '队列中',
    color: '#5B83FD'
  },
  '1': {
    text: '通过',
    color: '#14c55f'
  },
  '-1': {
    text: '未通过',
    color: '#E71D36'
  },
  '9': {
    text: 'Review失败',
    color: '#E71D36'
  },
  '2': {
    text: '未通过',
    color: '#E71D36'
  },
  '3': {
    text: '拉取代码中',
    color: '#5B83FD'
  },
  '4': {
    text: '代码解析中',
    color: '#5B83FD'
  },
  '5': {
    text: '代码审核中',
    color: '#5B83FD'
  }
};
const newStatusMap = {
  '0': {
    text: '未知',
    color: '#ff9f28'
  },
  '1': {
    text: '通过',
    color: '#14c55f'
  },
  '-1': {
    text: '不通过',
    color: '#f73232'
  },
  '2': {
    text: '白名单通过',
    color: '#14c55f'
  },
  '9': {
    text: '错误',
    color: '#f73232'
  }
};
const procedureStatusMap = {
  '0': {
    // text: '进行中',
    text: '队列中',
    color: '#ff9f28'
  },
  '1': {
    text: '通过',
    color: '#14c55f'
  },
  '-1': {
    text: '未通过',
    color: '#f73232'
  },
  '9': {
    text: '失败',
    color: '#f73232'
  },
  '2': {
    text: '完成',
    color: '#14c55f'
  },
  '3': {
    text: '拉取代码中',
    color: '#ff9f28'
  },
  '4': {
    text: '代码解析中',
    color: '#ff9f28'
  },
  '5': {
    text: '代码审核中',
    color: '#ff9f28'
  }
};

export default {
  inheritAttrs: false,
  components: {},
  props: {
    status: {
      type: [Number, String],
      default: '0'
    },
    message: {
      type: String
    },
    from: {
      type: String
    }
  },
  data() {
    return {};
  },
  computed: {
    currStatus() {
      if (this.from === 'homedetail') {
        return newStatusMap[this.status];
      } else if (this.from === 'procedure') {
        return procedureStatusMap[this.status];
      } else {
        return statusMap[this.status];
      }
    }
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {}
};
</script>

<style lang="less" scoped>
.status {
  display: flex;
  align-items: center;

  .status-icon {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
  }
}
</style>
