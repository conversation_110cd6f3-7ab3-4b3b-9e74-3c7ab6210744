<template>
  <div class="user-role-add">
    <PageDetail title="角色新增">
      <div class="frame-button-wrapper-relative-blank" slot="btns">
        <a-button @click="back">返回上一级</a-button>
        <a-button type="primary" @click="save">保存</a-button>
      </div>
      <Content ref="content" slot="middle"></Content>
    </PageDetail>
  </div>
</template>

<script>
// import _ from 'lodash';
import { roleCreate } from '@/api/user';
import Content from '../components/Content';
import PageDetail from '@/components/Biz/PageDetail';

export default {
  props: {},
  components: { Content, PageDetail },
  data() {
    return {};
  },
  computed: {},
  mounted() {},
  methods: {
    back() {
      this.$router.push({ name: 'roleList' });
    },
    save() {
      this.$refs.content.validate(data => {
        // 请求
        this.$showLoading();
        roleCreate({
          ...data.baseInfo,
          ...data.authInfo
        })
          .then(res => {
            if (_.get(res, 'data.code') == 0) {
              this.$hideLoading({ useMessage: true, tips: '新增角色成功' });
              this.$router.push({
                name: 'roleEdit',
                params: { id: _.get(res, 'data.data') }
              });
            } else {
              this.$hideLoading({
                method: 'error',
                tips: _.get(res, 'data.message')
              });
            }
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({ method: 'error', tips: '请求失败' });
          });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.user-role-add {
  .ant-divider {
    margin: 0 0 24px 0;
  }
  .btns {
    text-align: right;
  }
}
</style>
