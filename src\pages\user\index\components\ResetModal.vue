<template>
  <!-- 重置密码弹窗 -->
  <a-modal
    v-model="visible"
    title="重置密码"
    okText="保存"
    :maskClosable="false"
    @cancel="onCancel"
    @ok="onOk"
  >
    <Form ref="form" v-bind="params" :formData="data"></Form>
  </a-modal>
</template>

<script>
import Form from '@/components/Form';
// import common from '@/utils/common';

export default {
  components: { Form },
  props: {},
  data() {
    return {
      visible: false,
      data: {},
      params: {
        layout: 'vertical',
        fields: [
          {
            type: 'Input',
            label: '用户名',
            key: 'name',
            props: {
              disabled: true
            }
          },
          {
            type: 'InputPassword',
            label: '用户新密码',
            key: 'pwd',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' },
              { pattern: /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!_%*#?&]{8,20}$/, message: '密码至少8个字符，必须包含字母、数字和特殊字符' }
            ]
          },
          {
            type: 'InputPassword',
            label: '确认用户新密码',
            key: 'confirm',
            rules: [
              { required: true, message: '该项为必填项', trigger: 'blur' },
              { validator: (rules, value, callback) => { this.handleCfmPwd(rules, value, callback); } }
            ]
          }
        ]
      }
    };
  },
  mounted() {},
  created() {},
  methods: {
    show(data) {
      this.data = {
        name: data.name
      };
      this.visible = true;
    },
    hide() {
      const { form } = this.$refs;
      form.resetFields();
      this.visible = false;
    },
    onCancel() {
      this.hide();
    },
    onOk() {
      const { data } = this;
      const { form } = this.$refs;
      form.validate(valid => {
        if (valid) {
          this.$emit('save', data);
        }
      });
    },
    handleCfmPwd(rules, value, callback) {
      const { form } = this.$refs;
      let pwd = form.getData().pwd;
      if (pwd && pwd !== value) {
        callback(new Error('两次密码输入不一致'));
      } else {
        callback();
      }
    }
  }
};
</script>

<style lang="less" scoped>
</style>
