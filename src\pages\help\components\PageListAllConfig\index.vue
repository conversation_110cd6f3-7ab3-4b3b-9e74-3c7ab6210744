<template>
  <PageListContent v-bind="params">
    <!-- table插槽 -->
    <template v-for="(item, key) in columnSlots" v-slot:[key]="{text, record}">
      <component :key="key" :is="key" v-bind="{text, record}"></component>
    </template>
    <!-- 注意：操作列一般不一样，无需配置 -->
    <span slot="action" slot-scope="{ record }">
      <a @click="lineClick(record, $event)">Invite 一 {{record.name }}</a>
      <a-divider type="vertical" />
      <a>Delete</a>
      <a-divider type="vertical" />
      <a class="ant-dropdown-link">
        More actions
        <a-icon type="down" />
      </a>
    </span>
  </PageListContent>
</template>

<script>
import PageListContent from '@/components/PageList/content';
// import common from '@/utils/common';
import config, { columnSlots } from './config';

export default {
  components: { PageListContent, ...columnSlots },
  props: {},
  data() {
    this.config = config(this);

    // 方法三：
    // 利用函数式组件配置columnSlots
    // 可以达到配置最大化！！！

    return {
      params: {
        tableParams: {
          url: '/api/home/<USER>/table',
          reqParams: {
            from: 'pageListAllConfig'
          },
          columns: this.config.columns,
          rowSelection: {
            // type: 'radio'
          }
        },
        btns: this.config.btns,
        searches: this.config.searches
      },
      columnSlots: columnSlots
    };
  },
  mounted() {
    // 全局loading测试
    // this.$showLoading();
    // setTimeout(() => {
    //   this.$hideLoading();
    // }, 2000);
  },
  created() {},
  methods: {
    btnTest() {
      console.log('hahaha btnTest!!!!');
    },
    lineClick(record, event) {
      console.log('lineTest!!!!', record, event);
    }
  }
};
</script>

<style lang="less" scoped>
</style>
