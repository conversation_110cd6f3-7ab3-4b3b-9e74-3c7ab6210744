<template>
  <div class="quick-audit">
    <a-spin :spinning="loading"  v-if="loading"/>
    <template>
      <div class="content-tabs">
        <a-tabs
          v-model="activeKey"
          type="editable-card"
          @edit="onEdit"
          @change="tabChange"
        >
          <a-tab-pane v-for="pane in panes" :key="pane.key">
            <div slot="tab" @dblclick="onEditTitle(pane)">
              <span v-if="!pane.isEdit">{{ pane.title }}</span>
              <a-input
                v-model="value"
                v-else
                @blur="onBlur(pane)"
                @pressEnter="onBlur(pane)"
                ref="inputField"
              />
            </div>
            <Content
              ref="quickContent"
              :pane="pane"
              :paneKey="pane.key"
              @saveTabs="saveTabs"
            ></Content>
          </a-tab-pane>
        </a-tabs>
      </div>
      <div class="history">
        <a @click="toHistory">SQL审核记录</a>
      </div>
    </template>
    <HistoryDrawer ref="history" @getDetail="getDetail"></HistoryDrawer>
  </div>
</template>
<script>
import Content from './Content/index';
import bodyMinWidth from '@/mixins/bodyMinWidth';
import HistoryDrawer from './HistoryDrawer';
import { getTabs, addTabs, deleteTabs } from '@/api/quick';
export default {
  components: { Content, HistoryDrawer },
  mixins: [bodyMinWidth(1366)],
  props: {},
  data() {
    return {
      panes: [],
      activeKey: '',
      newTabIndex: 1,
      historyTabIndex: 0,
      value: '',
      loading: false
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  computed: {},
  methods: {
    init(data = {}, type) {
      // this.$showLoading();
      this.loading = true;
      getTabs()
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.loading = false;
            // this.$hideLoading({ duration: 0 });
            const resData = _.get(res, 'data.data');
            const panes = resData.map(item => {
              return {
                ...item,
                title: item.name,
                key: item.tab_id,
                isEdit: false
              };
            });
            const uuid =
              new Date().getTime() +
              Math.random()
                .toString(36)
                .substr(2);
            let _panes = [
              {
                title: '文本1',
                content: '',
                key: uuid,
                isEdit: false,
                tab_id: uuid
              }
            ];
            const activekey = localStorage.getItem('quick-audit-active-key');
            const keys = panes.map(item => item.key);
            // this.activeKey = keys.includes(activekey) ? activekey : uuid;
            if (!keys.includes(activekey) && !_.isEmpty(panes)) {
              this.activeKey = panes[0].key;
            }
            if (keys.includes(activekey)) {
              this.activeKey = activekey;
            }
            if (_.isEmpty(panes)) {
              this.activeKey = uuid;
            }
            // 新增审核完之后，不需要再创建一个新建窗口1, 用原来落库那个新建窗口1
            this.panes = !_.isEmpty(panes) ? panes : [..._panes, ...panes];
            // 点击sql审核历史记录会新增一个窗口
            // 该窗口需要调接口渲染数据
            // 其他窗口保持原状
            if (type == 'history') {
              this.$nextTick(() => {
                let _refs = this.$refs.quickContent;
                (_refs || []).forEach(item => {
                  if (this.activeKey == item.paneKey) {
                    item.updateData(data, 'history');
                  }
                });
              });
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    // 双击编辑窗口标题
    onEditTitle(pane) {
      this.value = pane.title;
      pane.isEdit = true;
      this.$nextTick(() => {
        const _ref = this.$refs.inputField;
        _ref && _ref[0].focus();
      });
    },
    // 双击编辑窗口标题,失焦后事件
    onBlur(pane) {
      pane.isEdit = false;
      pane.title = this.value;
      pane.name = this.value;
      this.saveTabs(pane, 'edit');
    },
    // 切换 tab, 选项卡
    tabChange(activeKey) {
      this.activeKey = activeKey;
      this.cache();
    },
    onEdit(targetKey, action) {
      this[action](targetKey);
    },
    add(targetKey, isByHistory = false, record = {}) {
      const panes = this.panes;
      isByHistory
        ? (this.historyTabIndex = this.historyTabIndex + 1)
        : (this.newTabIndex = this.newTabIndex + 1);
      const uuid =
        new Date().getTime() +
        Math.random()
          .toString(36)
          .substr(2);
      let activeKey = isByHistory ? record.id : uuid;
      panes.push({
        title: isByHistory
          ? `历史记录${this.historyTabIndex}`
          : `文本${this.newTabIndex}`,
        content: '',
        key: activeKey,
        isEdit: false,
        tab_id: activeKey
      });
      this.panes = panes;
      this.activeKey = activeKey;
      this.cache();
      if (isByHistory) {
        let params = record;
        params.tab_id = record.id;
        this.saveTabs(params, 'history');
      }
    },
    saveTabs(data, type) {
      const params = {
        name:
          type == 'history'
            ? `历史记录${this.historyTabIndex}`
            : type == 'edit'
            ? data.name
            : `文本${this.newTabIndex}`,
        audit_type: data.audit_type,
        content_type: data.content_type,
        content: data.content,
        data_source_id: data.data_source_id,
        schema_id: data.schema_id,
        rule_set: data.rule_set,
        db_type: data.db_type,
        tab_id: data.tab_id
      };
      this.loading = true;
      addTabs(params)
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.loading = false;
            this.init(data, type);
            localStorage.setItem('quick-audit-active-key', data.tab_id);
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    remove(targetKey) {
      this.$confirm({
        content: '确认是否删除此窗口?',
        onOk: () => {
          let activeKey = this.activeKey;
          let lastIndex;
          this.panes.forEach((pane, i) => {
            if (pane.key === targetKey) {
              lastIndex = i - 1;
            }
          });
          const panes = this.panes.filter(pane => pane.key !== targetKey);
          if (panes.length && activeKey === targetKey) {
            if (lastIndex >= 0) {
              activeKey = panes[lastIndex].key;
            } else {
              activeKey = panes[0].key;
            }
          }
          let tabId = null;
          this.panes.forEach(item => {
            if (item.key == targetKey) {
              tabId = item.tab_id;
            }
          });
          this.panes = panes;
          this.activeKey = activeKey;
          this.cache();
          tabId && this.removeTabs(tabId);
        },
        onCancel: () => {}
      });
    },
    removeTabs(id) {
      this.$showLoading();
      deleteTabs({ tab_id: id })
        .then(res => {
          if (CommonUtil.isSuccessCode(res)) {
            this.init();
            this.$hideLoading({
              method: 'success',
              tips: _.get(res, 'data.message')
            });
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    cache() {
      localStorage.setItem('quick-audit-active-key', this.activeKey);
    },
    audit() {},
    toHistory() {
      this.$refs.history.show();
    },
    getDetail(record) {
      this.add({}, true, record);
    }
  },
  watch: {
    activeKey: {
      handler(newVal, oldVal) {
        let _refs = this.$refs.quickContent;
        (_refs || []).forEach(item => {
          // if (newVal == item.paneKey) {
          //   item.resume();
          // } else {
          //   item.pause();
          // }
          item.cancel();
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.quick-audit {
  height: 100%;
  // height: calc(100% + 50px);
  // margin: -28px -14px;
  background: #f9f9f9;
  border-radius: 12px;
  display: flex;
  justify-content: space-between;
  position: relative;
  .ant-spin {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100001;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .content-tabs {
    width: 100%;
    padding: 12px 0 0 0;
    background: #fff;
    /deep/.ant-tabs {
      height: 100%;
      .ant-tabs-bar {
        padding: 0;
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
        align-items: flex-end;
        .ant-tabs-nav-container {
          .ant-tabs-nav {
            > div {
              .ant-tabs-tab {
                padding: 0 12px;
                margin-right: 4px;
                border-radius: 8px 8px 0 0;
                background: rgb(243, 244, 246);
                transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
                &:first-child {
                  margin-left: 16px;
                }
                > div {
                  display: flex;
                  align-items: center;
                  .anticon {
                    font-size: 12px;
                    margin-left: 4px;
                    color: #a1a1aa;
                  }
                }
                &.ant-tabs-tab-active {
                  background: #fff;
                  > div {
                    .anticon {
                      color: #4db5f2;
                    }
                  }
                }
              }
            }
          }
        }
        .ant-tabs-extra-content {
          height: 40px;
          margin-bottom: -1px;
          padding-right: 120px;
          border-radius: 8px;
          .ant-tabs-new-tab {
            width: 40px;
            height: 40px;
            background: #f2f2f2;
            border: none;
            border-left: 1px solid #fff;
            border-radius: 8px 8px 0 0;
          }
        }
      }
    }
  }
  .history {
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    position: absolute;
    top: 20px;
    right: 16px;
    cursor: pointer;
  }
}
</style>
