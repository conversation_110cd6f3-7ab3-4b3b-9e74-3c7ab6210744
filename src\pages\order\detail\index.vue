<template>
  <div class="order-detail-container">
    <CodeReviewDetail ref="codeReviewDetail" v-if="activeKey == 'sqlreview'">
    </CodeReviewDetail>
    <DataSourceReviewDetail ref="codeReviewDetail" v-else>
    </DataSourceReviewDetail>
  </div>
</template>

<script>
import bodyMinWidth from '@/mixins/bodyMinWidth';
import CodeReviewDetail from './CodeReviewDetail';
import DataSourceReviewDetail from './DataSourceReviewDetail';

export default {
  name: 'orderDetail',
  components: {
    CodeReviewDetail,
    DataSourceReviewDetail
  },
  mixins: [bodyMinWidth(1280)],
  props: {},
  computed: {},
  data() {
    return {
      activeKey: 'sqlreview'
    };
  },
  mounted() {},
  created() {},
  methods: {},
  watch: {
    '$route.query.activeKey': {
      handler(newVal) {
        if (newVal) {
          this.activeKey = newVal;
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less">
</style>
