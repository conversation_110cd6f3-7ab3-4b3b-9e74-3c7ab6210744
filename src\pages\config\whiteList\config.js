export default function (ctx) {
  const columns = [
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 200
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      key: 'created_by',
      width: 200
    },
    {
      title: 'SQL内容',
      dataIndex: 'sql_text',
      key: 'sql_text',
      width: 200,
      scopedSlots: { customRender: 'sql_text' }
    },
    {
      title: '文件路径',
      dataIndex: 'sql_file_path',
      key: 'sql_file_path',
      scopedSlots: { customRender: 'sql_file_path' },
      width: 250
    },
    {
      title: '所属项目',
      dataIndex: 'project_name',
      key: 'project_name',
      width: 150
    },
    {
      title: '白名单分类',
      dataIndex: 'classify_name',
      key: 'classify_name',
      width: 150
    },
    {
      title: 'DBA建议',
      dataIndex: 'remark',
      key: 'remark',
      customRender: (text) => {
        return text || '-';
      },
      width: 150
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 100,
      fixed: 'right'
    }
  ];
  const tableColumns = [
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 200
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      key: 'created_by',
      width: 200
    },
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name',
      width: 200
    },
    {
      title: '数据源',
      dataIndex: 'data_source_id',
      key: 'data_source_id',
      scopedSlots: { customRender: 'data_source_id' },
      width: 200
    },
    {
      title: 'Schema',
      dataIndex: 'schema_name',
      key: 'schema_name',
      width: 200
    },
    {
      title: '生效时间',
      dataIndex: 'take_effect',
      key: 'take_effect',
      width: 200
    },
    {
      title: '失效时间',
      dataIndex: 'expire',
      key: 'expire',
      width: 200
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
      width: 100,
      fixed: 'right'
    }
  ];
  const searchFields = [
    {
      type: 'Input',
      label: '所属项目',
      key: 'project',
      mainSearch: true,
      props: {
        placeholder: '请输入所属项目'
      }
    },
    {
      type: 'Input',
      label: '文件路径',
      key: 'sql_file_path'
    },
    {
      type: 'Input',
      label: 'SQL内容',
      key: 'sql_text'
    }
  ];
  const tableSearchFields = [
    {
      type: 'Input',
      label: 'schema',
      key: 'schema_name',
      mainSearch: true,
      props: {
        placeholder: '请输入schema'
      }
    },
    {
      type: 'Input',
      label: '数据源',
      key: 'data_source_name',
      sourceKey: 'data_source_id',
      props: {
        url: '/sqlreview/project/data_source_choices',
        reqParams: {},
        allowSearch: true,
        backSearch: true,
        limit: 50
      },
      listeners: {
        // change: (value) => {
        //   ctx.$refs.form.saving({
        //     data_source: value,
        //     datasource_id: null
        //   });
        // }
      }
    },
    {
      type: 'Input',
      label: '表名',
      key: 'table_name'
    }
  ];
  return {
    columns,
    tableColumns,
    searchFields,
    tableSearchFields
  };
}
