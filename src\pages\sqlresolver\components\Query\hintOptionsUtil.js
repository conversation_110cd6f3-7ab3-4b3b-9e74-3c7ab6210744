const andOrOptions = [
  {
    'text': '*'
  },
  {
    'text': 'AND'
  },
  {
    'text': 'ARRAY'
  },
  {
    'text': 'BETWEEN'
  },
  {
    'text': 'BIGINT'
  },
  {
    'text': 'BINARY'
  },
  {
    'text': 'BOOLEAN'
  },
  {
    'text': 'CASE'
  },
  {
    'text': 'COLON'
  },
  {
    'text': 'CURRENT_DATE'
  },
  {
    'text': 'CURRENT_TIMESTAMP'
  },
  {
    'text': 'DOUBLE'
  },
  {
    'text': 'EXISTS'
  },
  {
    'text': 'FALSE'
  },
  {
    'text': 'FLOAT'
  },
  {
    'text': 'IF'
  },
  {
    'text': 'IN'
  },
  {
    'text': 'INT'
  },
  {
    'text': 'IS'
  },
  {
    'text': 'LIKE'
  },
  {
    'text': 'MAP'
  },
  {
    'text': 'NOT'
  },
  {
    'text': 'NULL'
  },
  {
    'text': 'OR'
  },
  {
    'text': 'REGEXP'
  },
  {
    'text': 'RLIKE'
  },
  {
    'text': 'SMALLINT'
  },
  {
    'text': 'STRUCT'
  },
  {
    'text': 'TRUE'
  },
  {
    'text': 'UNIONTYPE'
  },
  {
    'text': 'WHEN'
  }
];
const hintOptions = {
  SELECT: [
    { 'text': '*' },
    {
      'text': 'ALL'
    },
    {
      'text': 'ARRAY'
    },
    {
      'text': 'BIGINT'
    },
    {
      'text': 'BOOLEAN'
    },
    {
      'text': 'CASE'
    },
    {
      'text': 'CURRENT_DATE'
    },
    {
      'text': 'CURRENT_TIMESTAMP'
    },
    {
      'text': 'DISTINCT'
    },
    {
      'text': 'DOUBLE'
    },
    {
      'text': 'FALSE'
    },
    {
      'text': 'FLOAT'
    },
    {
      'text': 'IF'
    },
    {
      'text': 'INT'
    },
    {
      'text': 'MAP'
    },
    {
      'text': 'NULL'
    },
    {
      'text': 'SMALLINT'
    },
    {
      'text': 'STRUCT'
    },
    {
      'text': 'TIMESTAMP'
    },
    {
      'text': 'TRANSFORM'
    },
    {
      'text': 'TRUE'
    },
    {
      'text': 'UNIONTYPE'
    }
  ],
  '*': [
    {
      'text': 'AS'
    },
    {
      'text': 'CLUSTER'
    },
    {
      'text': 'DISTRIBUTE'
    },
    {
      'text': 'FROM'
    },
    {
      'text': 'GROUP'
    },
    {
      'text': 'HAVING'
    },
    {
      'text': 'LIMIT'
    },
    {
      'text': 'DROER'
    },
    {
      'text': 'SORT'
    },
    {
      'text': 'UNION'
    },
    {
      'text': 'WHERE'
    },
    {
      'text': 'WINDOW'
    }
  ],
  FROM: [
    {
      'text': 'UNIQUEJOIN'
    },
    {
      'text': 'VALUES'
    }
  ],
  WHERE: [
    {
      'text': '*'
    },
    {
      'text': 'AND'
    },
    {
      'text': 'ARRAY'
    },
    {
      'text': 'BETWEEN'
    },
    {
      'text': 'BIGINT'
    },
    {
      'text': 'BINARY'
    },
    {
      'text': 'BOOLEAN'
    },
    {
      'text': 'CASE'
    },
    {
      'text': 'COLON'
    },
    {
      'text': 'CURRENT_DATE'
    },
    {
      'text': 'CURRENT_TIMESTAMP'
    },
    {
      'text': 'DOUBLE'
    },
    {
      'text': 'EXISTS'
    },
    {
      'text': 'FLOAT'
    },
    {
      'text': 'IF'
    },
    {
      'text': 'IN'
    },
    {
      'text': 'INT'
    },
    {
      'text': 'IS'
    },
    {
      'text': 'LIKE'
    },
    {
      'text': 'MAP'
    },
    {
      'text': 'NOT'
    },
    {
      'text': 'NULL'
    },
    {
      'text': 'OR'
    },
    {
      'text': 'REGEXP'
    },
    {
      'text': 'RLIKE'
    },
    {
      'text': 'SMALLINT'
    },
    {
      'text': 'STRUCT'
    },
    {
      'text': 'TIMESTAMP'
    },
    {
      'text': 'TRUE'
    },
    {
      'text': 'UNIONTYPE'
    }
  ],
  AND: andOrOptions,
  OR: andOrOptions,
  ORDER: [{ text: 'BY' }],
  GROUP: [{ text: 'BY' }],
  INSERT: [
    {
      'text': 'INTO'
    },
    {
      'text': 'OVERWRITE'
    }
  ],
  DELETE: [{ text: 'FROM' }],
  CREATE: [
    {
      'text': 'DATABASE'
    },
    {
      'text': 'EXTERNAL'
    },
    {
      'text': 'FUNCTION'
    },
    {
      'text': 'INDEX'
    },
    {
      'text': 'MACRO'
    },
    {
      'text': 'OR'
    },
    {
      'text': 'SCHEMA'
    },
    {
      'text': 'TABLE'
    },
    {
      'text': 'TEMPORARY'
    },
    {
      'text': 'VIEW'
    },
    {
      'text': 'VIRTUAL'
    }
  ]
}

const getHintOptions = (key = '') => {
  return hintOptions[key.toUpperCase()];
}

export default {
  getHintOptions
};