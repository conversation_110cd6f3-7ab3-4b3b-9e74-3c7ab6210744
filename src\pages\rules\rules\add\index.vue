<template>
  <div class="config-rules-add">
    <Content
      type="add"
      ref="contentDDL"
      v-if="showDDL"
      @change="changeType"
      :rule_type="rule_type"
    />
    <ContentDML type="add" ref="contentDML" v-else @change="changeType" :rule_type="rule_type" />

    <div class="frame-button-wrapper">
      <a-button @click="onCancel" class="highlight">取消</a-button>
      <a-button type="primary" @click="onSave">保存</a-button>
    </div>
  </div>
</template>

<script>
import { ruleAdd, addDdlRule } from '@/api/config/rule';
import Content from '../components/Content';
import ContentDML from '../components/ContentDML/old';

export default {
  components: { Content, ContentDML },
  props: {},
  data() {
    const dbType = window.localStorage.getItem('db_type') || '';
    return {
      rule_type: '',
      showDDL: false,
      dbType,
      title: '新增规则'
    };
  },
  mounted() {},
  created() {},
  methods: {
    changeType(value) {
      this.showDDL = value;
      this.rule_type = value ? 'DDL' : 'DML';
    },
    onCancel() {
      this.$router.push({ name: 'rules-config' });
    },
    onSave() {
      const { contentDML, contentDDL } = this.$refs;
      let data = null;
      if (this.showDDL) {
        data = contentDDL.getData();
      } else {
        data = contentDML.getData();
      }
      data.then(data => {
        // 请求
        this.$showLoading();
        if (this.showDDL) {
          // ddl
          addDdlRule(data)
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.$hideLoading({ tips: '创建成功' });
                // this.$router.go(-1);
                this.$router.push({ name: 'rules-config' });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        } else {
          // dml
          // data.db_type = this.dbType;
          // data.rule_result = '0';
          ruleAdd(data)
            .then(res => {
              if (_.get(res, 'data.code') == 0) {
                this.$hideLoading({ tips: _.get(res, 'data.message') });
                // this.$router.go(-1);
                this.$router.push({ name: 'rules-config' });
              } else {
                this.$hideLoading({
                  method: 'error',
                  tips: _.get(res, 'data.message')
                });
              }
            })
            .catch(e => {
              console.error(e);
              this.$hideLoading({
                method: 'error',
                tips: _.get(e || {}, 'response.data.message') || '请求失败'
              });
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
