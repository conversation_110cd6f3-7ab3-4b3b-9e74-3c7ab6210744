<template>
  <div class="review-header">
    <a-card type="small" :bordered="false">
      <div class="content">
        <div class="title">
          <div class="title-project">Review对象</div>
          <!-- <div class="title-project-name">{{ headerInfo.project_name || '--' }}</div> -->
          <a-popover>
            <template slot="content">
              <span>{{headerInfo.project_name}}</span>
            </template>
            <span class="title-project-name">{{ headerInfo.project_name || '--' }}</span>
          </a-popover>
          <div class="title-project">SQL来源</div>
          <a-popover>
            <template slot="content">
              <span>{{headerInfo.file_path}}</span>
            </template>
            <span class="title-project-name file_path">{{ headerInfo.file_path || '--' }}</span>
          </a-popover>
          <div
            class="title-project"
            v-if="commentStatusList.includes(headerInfo.comment_status)"
          >申请类型</div>
          <div
            class="title-project-name"
            v-if="commentStatusList.includes(headerInfo.comment_status)"
          >{{ commentStatus[headerInfo.comment_status] || '--' }}</div>
        </div>
        <div class="frame-button-wrapper-relative-blank">
          <a-dropdown
            :disabled="!canDo && !isLeader && !isAdmin"
            v-if="(canDo || isLeader || isAdmin) && isOrder"
            overlayClassName="review-agree-overlay"
            :getPopupContainer="getPopupContainer"
            class="review-agree"
          >
            <a-menu
              class="review-agree-menu"
              slot="overlay"
              @click="(event) => handleMenuClick('agree', event)"
            >
              <a-menu-item v-for="item in agreeList" v-bind:key="item.key">
                {{
                item.name
                }}
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px;">
              <a-icon type="file-done" />通过的意见
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
          <a-dropdown
            :disabled="!canDo && !isLeader && !isAdmin"
            v-if="(canDo || isLeader || isAdmin) && isOrder"
            overlayClassName="review-disagree-overlay"
            :getPopupContainer="getPopupContainer"
            class="review-disagree"
          >
            <a-menu slot="overlay" @click="(event) => handleMenuClick('disagree', event)">
              <a-menu-item v-for="item in disagreeList" v-bind:key="item.key">
                {{
                item.name
                }}
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px;">
              <a-icon type="close-circle" />不通过的意见
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
          <a-button
            v-if="!isWhite && (canDo || isAdmin) && isOrder"
            :disabled="!canDo && !isAdmin"
            style="margin-left: 8px;"
            type="primary"
            @click="reviewHeaderBtns(1)"
          >
            <a-icon type="plus-circle" />加入白名单
          </a-button>

          <a-button
            v-if="!canDo && isShow && isShowSubmitButton"
            @click="reviewHeaderBtns(7)"
          >提交评审 {{this.plan}}</a-button>
          <a-button v-if="!canDo && isShow" type="primary" @click="reviewHeaderBtns(2)">申请通过</a-button>
          <!-- <a-button v-if="!canDo && isShow" @click="reviewHeaderBtns(0)">申请白名单</a-button>
          <a-button v-if="!canDo && isShow" type="primary" @click="reviewHeaderBtns(2)">申请通过</a-button>-->
        </div>
        <div class="btns">
          <a-button class="back-btn" @click="toBack" v-if="hasBack">返回</a-button>
          <a-button
            :disabled="idList.length > 0 ? currentNum === 0 : headerInfo.index === 1"
            @click="reviewHeaderBtns(5)"
          >
            <a-icon type="double-left" />prev
          </a-button>
          <a-button
            :disabled="idList.length > 0 ? currentNum == idList.length-1 :headerInfo.count === headerInfo.index"
            @click="reviewHeaderBtns(6)"
          >
            next
            <a-icon type="double-right" />
          </a-button>
          <span class="pageInfo" v-if="idList.length > 0">{{ currentNum + 1}}/{{ idList.length }}</span>
          <span class="pageInfo" v-else>{{ headerInfo.index }}/{{ headerInfo.count }}</span>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script>
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    agreeList: {
      type: Array,
      default: () => []
    },
    disagreeList: {
      type: Array,
      default: () => []
    },
    idList: {
      type: Array,
      default: () => []
    },
    filterList: {
      type: Array,
      default: () => []
    },
    currentNum: {
      type: Number,
      default: 0
    },
    isWhite: {
      type: Boolean,
      default: true
    },
    isShow: {
      type: Boolean,
      default: true
    },
    isOrder: {
      type: Boolean,
      default: false
    },
    isShowSubmitButton: {
      type: Boolean,
      default: false
    },
    plan: {
      type: String,
      default: ''
    },
    hasBack: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      canDo: false,
      commentStatusList: [1, 2, 3, 4, 5, 6, 7],
      commentStatus: {
        1: '待处理',
        2: '已通过',
        3: '已拒绝',
        4: '加入白名单',
        5: '临时通过',
        6: '申请白名单通过',
        7: '申请临时通过'
      }
    };
  },
  mounted() {
    // let user = this.$store.state.account.user || {};
    // let roleArr = ['dba', 'virtual_dev'];
    // this.canDo = roleArr.includes(user.role);
  },
  methods: {
    reviewHeaderBtns(number) {
      this.$emit('reviewHeaderBtns', number);
    },
    getPopupContainer() {
      return document.getElementById('rootContent');
    },
    handleMenuClick(type, args) {
      this.$emit('handleMenuClick', type, args);
    },
    toBack() {
      this.$emit('back');
    }
  },
  computed: {
    isLeader() {
      const user = this.$store.state.account.user || {};
      return user.role === 'leader';
    },
    isAdmin() {
      const user = this.$store.state.account.user || {};
      return user.role === 'admin';
    }
  },
  watch: {
    '$store.state.account.user': {
      handler(newVal = {}) {
        let roleArr = ['dba', 'virtual_dev'];
        this.canDo = roleArr.includes(newVal.role);
        // this.canDo = newVal.role === 'dba';
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .review-agree {
  border-color: rgba(35, 190, 108, 1);
  background: rgba(35, 190, 108, 1);
  color: #fff;
}
.review-agree-overlay {
  .ant-dropdown-menu {
    background: rgba(35, 190, 108, 1);
    .ant-dropdown-menu-item {
      color: #fff;
      &.ant-dropdown-menu-item-active {
        background: #52c287;
      }
    }
  }
}
/deep/ .review-disagree {
  border-color: rgba(255, 83, 84, 1);
  background: rgba(255, 83, 84, 1);
  color: #fff;
}
.review-disagree-overlay {
  .ant-dropdown-menu {
    background: rgba(255, 83, 84, 1);
    .ant-dropdown-menu-item {
      color: #fff;
      &.ant-dropdown-menu-item-active {
        background: #fb8283;
      }
    }
  }
}

.review-header {
  margin-bottom: 24px;
  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .title-project {
        font-size: 16px;
        font-weight: 500;
        color: #565759;
      }
      .title-project-name {
        display: inline-block;
        padding: 0 24px;
        color: @primary-color;
        font-size: 12px;
        border: 1px solid @primary-color;
        border-radius: 24px;
        line-height: 24px;
        margin: 0 24px 0 12px;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &::before {
          content: '';
          display: inline-block;
          position: relative;
          left: -8px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: @primary-color;
        }
      }
    }
    .btns {
      .ant-btn {
        margin-right: 8px;
      }
    }
  }
}
</style>