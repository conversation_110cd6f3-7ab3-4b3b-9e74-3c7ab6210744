export default function (ctx) {
  const fields = (bool) => [
    (formData = {}) => {
      return {
        type: 'RadioGroup',
        key: 'audit_type',
        label: '单选button类型',
        props: {
          mode: 'button',
          options: [
            { label: '离线审核', value: 'offline' },
            { label: '在线审核', value: 'online' }
          ],
          disabled: bool
        },
        listeners: {
          change: (value) => {
            ctx.auditType = value;
            ctx.$refs.form.saving({
              audit_type: value,
              data_source_id: null,
              schema_id: null,
              db_type: null,
              rule_set: null
            });
          }
        }
      };
    },
    // 数据源
    (formData = {}) => {
      return {
        type: 'DataBaseChoose',
        // label: '数据源',
        key: 'data_source_id',
        compIcon: 'lu-icon-database',
        props: {
          url: '/sqlreview/project/data_source_choices',
          reqParams: {},
          placeholder: '数据源',
          loaded(data) {
            ctx.dataSourceOption = data;
          },
          beforeLoaded(data) {
            return data.map((item) => {
              return {
                ...item,
                instance_usage: item.env,
                showText: item.label + '(' + item.db_url + ')'
              };
            });
          },
          getPopupContainer: (el) => {
            return document.body;
          },
          disabled: bool,
          mode: 'default',
          optionLabelProp: 'children',
          allowSearch: true,
          backSearch: true,
          limit: 50
        },
        visible: formData.audit_type == 'online',
        rules: [{ required: true, message: '该项为必填项' }],
        listeners: {
          change: (value) => {
            let ruleSet;
            ctx.dataSourceOption.forEach(item => {
              if (item.value == value) {
                ruleSet = item.default_rule_set
              }
            });
            ctx.$refs.form.saving({
              data_source_id: value,
              schema_id: null,
              rule_set: ruleSet
            });
          }
        }
      };
    },
    (formData = {}) => {
      return {
        type: 'Select',
        // label: 'Schema',
        key: 'schema_id',
        compIcon: 'lu-icon-schema1',
        props: {
          placeholder: 'Schema',
          url: '/sqlreview/project/get_schema_list',
          reqParams: {
            datasource_id: formData.data_source_id
          },
          disabled: bool,
          allowSearch: true,
          backSearch: true,
          limit: 50,
          getPopupContainer: (el) => {
            return document.body;
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        visible: formData.audit_type == 'online',
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              schema_id: value
            });
          }
        }
      };
    },
    // 任务类型
    (formData = {}) => {
      return {
        type: 'Select',
        // label: '数据库类型',
        key: 'db_type',
        compIcon: 'database',
        props: {
          url: '/sqlreview/review/select_all_db_type',
          placeholder: '数据库类型',
          disabled: bool,
          loaded(data) {
            ctx.dbTypeOption = data;
          }
        },
        rules: [{ required: true, message: '该项为必填项' }],
        visible: formData.audit_type == 'offline',
        listeners: {
          change: (value) => {
            let ruleSet;
            ctx.dbTypeOption.forEach(item => {
              if (item.value == value) {
                ruleSet = item.default_rule_set
              }
            });
            ctx.$refs.form.saving({
              db_type: value,
              rule_set: ruleSet
            });
          }
        }
      };
    },
    // 选择规则集
    (formData = {}) => {
      return {
        type: 'Select',
        // label: '规则集',
        key: 'rule_set',
        compIcon: 'lu-icon-rule',
        props: {
          url: '/sqlreview/project/rule_set_all',
          // mode: 'multiple',
          reqParams: { db_type: formData.db_type, data_source_id: formData.data_source_id },
          disabled: bool,
          allowSearch: true,
          backSearch: true,
          limit: 50,
          placeholder: '规则集'
        },
        rules: [{ required: true, message: '该项为必填项' }],
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              rule_set: value
            });
          }
        }
      };
    }
  ];
  const tableColumns = [
    {
      title: 'Schema',
      dataIndex: 'schema_name',
      key: 'schema_name'
    },
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name'
    },
    {
      title: '数据量',
      dataIndex: 'table_rows',
      key: 'table_rows'
    },
    {
      title: '分区类型',
      dataIndex: 'partition_type',
      key: 'partition_type'
    },
    {
      title: '分区键',
      key: 'part_column',
      dataIndex: 'part_column'
    },
    {
      title: '表状态',
      key: 'is_onlie',
      dataIndex: 'is_onlie',
      scopedSlots: { customRender: 'is_onlie' }
    },
    {
      title: '统计信息收集时间',
      key: 'last_collect_time',
      dataIndex: 'last_collect_time'
    },
    {
      title: '表注释',
      key: 'comment',
      dataIndex: 'comment',
      scopedSlots: { customRender: 'comment' }
    }
  ];

  const indexColumns = () => {
    return [
      {
        title: '索引名称',
        dataIndex: 'index_name',
        key: 'index_name'
      },
      {
        title: '索引类型',
        key: 'unique_name',
        dataIndex: 'unique_name'
      },
      {
        title: '索引字段',
        dataIndex: 'column_name',
        key: 'column_name'
      },
      {
        title: '字段类型',
        key: 'data_type',
        dataIndex: 'data_type'
      },
      {
        title: '字段位置',
        key: 'column_position',
        dataIndex: 'column_position'
      },
      {
        title: '区分度',
        key: 'cardinality',
        dataIndex: 'cardinality'
      }
    ];
  };
  const fieldColumns = [
    {
      title: '字段名',
      dataIndex: 'column_name',
      key: 'column_name'
    },
    {
      title: '数据类型',
      dataIndex: 'column_type',
      key: 'column_type'
    },
    {
      title: '可空',
      dataIndex: 'is_nullable',
      key: 'is_nullable',
      customRender: (text, record, index) => {
        return text == 'YES' ? '是' : '否'
      }
    },
    {
      title: '自增',
      key: 'auto_increment',
      dataIndex: 'auto_increment',
      customRender: (text, record, index) => {
        return text == 'YES' ? '是' : '否'
      }
    },
    {
      title: '缺省值',
      key: 'column_default',
      dataIndex: 'column_default'
    },
    {
      title: '备注',
      key: 'column_comment',
      dataIndex: 'column_comment'
    }
  ];
  return {
    fields,
    tableColumns,
    indexColumns,
    fieldColumns
  };
}
