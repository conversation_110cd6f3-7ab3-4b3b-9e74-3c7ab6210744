export default function (ctx) {
  // 项目review结果一览
  const reviewTagOption = (params = {}) => {
    const { data = [] } = params;
    let name = '';
    if (data.length <= 0) return;
    return () => ({
      grid: {
        top: '8%',
        bottom: '0%',
        left: '8%',
        right: '8%',
        containLabel: true
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          data.map((item) => {
            if (item.status === params.name) {
              name = item.status;
            }
          });
          return (
            name + '</br>' + params.marker + '数量占比' + params.value + '%'
          );
        }
      },
      // color: ['#f73232', '#b0aeae', '#52c41a', '#1edfa9', '#ff9f28'],
      legend: {
        orient: 'horizontal',
        left: 'center'
      },
      series: [
        {
          type: 'pie',
          radius: '70%',
          center: ['50%', '60%'],
          data: data.map((item, index) => {
            return {
              value: item.count,
              name: item.status
            };
          }),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    });
  };

  // 应用通过率排行
  const passReadOption = (params = {}) => {
    const { data = [] } = params;
    let names = [];
    let failRes = [];
    let passRes = [];
    let totalRes = [];
    if (data.fail_res) {
      names = data.fail_res.map((item) => item.days);
      failRes = data.fail_res.map((item) => item.count);
      passRes = data.pass_res.map((item) => item.count);
      totalRes = data.total_res.map((item) => item.count);
    }
    if (data.length <= 0) return;
    return () => ({
      grid: {
        top: '16%',
        bottom: '0%',
        left: '8%',
        right: '8%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        left: 'right'
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: names
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '自动识别率',
          type: 'line',
          data: totalRes
        },
        {
          name: '通过率',
          type: 'line',
          data: passRes
        },
        {
          name: '未通过率',
          type: 'line',
          data: failRes
        }
      ]
    });
  };

  // 未知/错误占比图
  const failReadOption = (params = {}) => {
    const { data = [] } = params;
    if (data.length <= 0) return;
    let names = data.map((item) => item.name);
    let falseData = data.map((item) => item.false);
    let unKnow = data.map((item) => item.un_know);
    return () => ({
      grid: {
        top: '16%',
        bottom: '0%',
        left: '8%',
        right: '8%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'none'
        }
      },
      legend: {
        data: ['未知', '错误'],
        left: 'right'
      },
      calculable: true,
      xAxis: [
        {
          type: 'category',
          data: names
        }
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '错误',
          type: 'bar',
          data: falseData
        },
        {
          name: '未知',
          type: 'bar',
          data: unKnow
        }
      ]
    });
  };

  // DBA review统计
  const dbaReviewOption = (params = {}) => {
    const { data = [] } = params;
    let names = data.map((item) => item.name);
    let record = data.map((item) => item.record);
    let detail = data.map((item) => item.detail);
    return () => ({
      grid: {
        top: '16%',
        bottom: '0%',
        left: '8%',
        right: '8%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'none'
        }
      },
      legend: {
        data: ['SQL条数', 'review任务数'],
        left: 'right'
      },
      calculable: true,
      xAxis: [
        {
          type: 'category',
          data: names
        }
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: 'review任务数',
          type: 'bar',
          data: record
        },
        {
          name: 'SQL条数',
          type: 'bar',
          data: detail
        }
      ]
    });
  };

  // 应用排行
  const projectReadOption = (params = {}) => {
    const { data = [] } = params;
    let names = data.map((item) => item.name);
    let record = data.map((item) => item.record);
    let detail = data.map((item) => item.detail);
    return () => ({
      grid: {
        top: '16%',
        bottom: '0%',
        left: '8%',
        right: '8%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'none'
        }
      },
      legend: {
        data: ['review任务数（DBA操作）', 'review条数'],
        left: 'right'
      },
      calculable: true,
      xAxis: [
        {
          type: 'category',
          data: names
        }
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: 'review条数',
          type: 'bar',
          data: record
        },
        {
          name: 'review任务数（DBA操作）',
          type: 'bar',
          data: detail
        }
      ]
    });
  };

  const baseInfo = [
    {
      type: 'RangePicker',
      label: '',
      key: 'time_range',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      },
      listeners: {
        change: (value) => {},
        ok: (value) => {
          const timeArr = [];
          value.forEach((item) => {
            timeArr.push(item.format('YYYY-MM-DD HH:mm:ss'));
          });
          ctx.activeKey = '';
          ctx.getData(timeArr.join(','));
        }
      }
    }
  ];

  return {
    reviewTagOption,
    passReadOption,
    failReadOption,
    dbaReviewOption,
    projectReadOption,
    baseInfo
  };
}
