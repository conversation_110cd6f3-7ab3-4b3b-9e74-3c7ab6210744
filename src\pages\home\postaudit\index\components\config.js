import moment from 'moment';
export default function (ctx) {
  const dataSource = (formData = {}) => {
    return {
      type: 'Select',
      // label: '数据源选择',
      label: '实例名称',
      key: 'data_source',
      props: {
        url: '/sqlreview/project/data_source_choices_for_after',
        reqParams: {
           db_type: ctx.dbType
        },
        allowSearch: true,
        backSearch: true,
        limit: 50,
        disabled: ctx.isDisabled,
        loaded: (data) => {
          ctx.granteeOptions = data;
        }
      },
      listeners: {
        change: (value) => {
          ctx.$refs.form.saving({
            data_source: value,
            datasource_id: null,
            frequency: null,
            type: null,
            interval_frequency: null,
            range_time: null,
            time_range: null,
            rule_set: null
          });
          const res =
            ctx.granteeOptions.find((item) => item.value == value) || {};
          ctx.$refs.form.saving({
            db_type: res.db_type || ''
          });
          if (['POSTGRE'].includes(res.db_type)) {
            ctx.$refs.form.saving({
              type: 2
            });
          }
          ctx.$set(ctx, 'dbType', res.db_type || '');
          ctx.loadBaseInfo();
        }
      },
      rules: [{ required: true, message: '该项为必填项' }]
    };
  }
  const type = (formData = {}) => {
    return {
      type: 'Select',
      label: '任务类型',
      key: 'type',
      props: {
        options: [
          { label: '单次', value: 1 },
          { label: '多次', value: 2 }
        ],
        disabled: ['POSTGRE'].includes(formData.db_type)
      },
      rules: [{ required: true, message: '该项为必填项' }],
      listeners: {
        change: (value) => {
          ctx.$refs.form.saving({
            type: value
          });
        }
      }
    };
  }
  const frequency = (formData = {}) => {
    return {
      type: 'Input',
      label: '任务频率',
      key: 'frequency',
      rules: [{ required: true, message: '', trigger: 'change' }],
      visible: formData.type === 2 && formData.db_type !== 'POSTGRE',
      hideComponent: true,
      slots: [{ key: 'frequency' }]
    };
  }
  const rangeTime = (formData = {}) => {
    return {
      type: 'Radio',
      label: '运行时间',
      key: 'range_time',
      rules: [{ required: true, message: '', trigger: 'blur' }],
      // visible: ['POSTGRE'].includes(formData.db_type),
      hideComponent: true,
      slots: [{ key: 'range_time' }]
    };
  }
  const intervalFrequency = (formData = {}) => {
    return {
      type: 'Select',
      label: '数据采集频率',
      key: 'interval_frequency',
      props: {
        options: [
          { label: '10分钟', value: 10 },
          { label: '20分钟', value: 20 },
          { label: '30分钟', value: 30 },
          { label: '1个小时', value: 60 },
          { label: '2个小时', value: 120 },
          { label: '6个小时', value: 360 },
          { label: '12个小时', value: 720 }
          // { label: '24个小时', value: 1440 }
        ]
      },
      rules: [{ required: true, message: '该项为必填项' }]
      // visible: ['POSTGRE', 'SQLSERVER'].includes(formData.db_type)
    };
  }
  const timeRange = (formData = {}) => {
    return {
      type: 'RangePicker',
      label: '执行时间范围',
      key: 'time_range',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        },
        disabledDate: (current) => {
          return current > moment().endOf('day');
        }
        // disabledRangeTime: (_, type) => {
        //   if (type === 'start') {
        //     return {
        //       disabledHours: () => this.range(0, 60).splice(4, 20),
        //       disabledMinutes: () => this.range(30, 60),
        //       disabledSeconds: () => [55, 56]
        //     };
        //   }
        //   return {
        //     disabledHours: () => this.range(0, 60).splice(20, 4),
        //     disabledMinutes: () => this.range(0, 31),
        //     disabledSeconds: () => [55, 56]
        //   };
        // }
      },
      listeners: {
        change: (value) => {
          const timeArr = [];
          value.forEach((item) => {
            timeArr.push(moment(item.format('YYYY-MM-DD HH:mm:ss')));
          });
          ctx.$refs.form.saving({
            time_range: timeArr
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项' }],
      visible: formData.type === 1 && formData.db_type == 'ORACLE'
    };
  }
  const ruleSet = (formData = {}) => {
    return {
      type: 'Select',
      // label: '规则集选择',
      label: '审核规则集',
      key: 'rule_set',
      props: {
        url: '/sqlreview/project/rule_set_all',
        mode: 'multiple',
        reqParams: {
          db_type: formData.db_type || ctx.dbType
        }
      },
      rules: [{ required: true, message: '该项为必填项' }]
    };
  };
  const baseInfo = [
    // 任务名称
    // {
    //   type: 'Input',
    //   label: '任务名称',
    //   key: 'name',
    //   rules: [{ required: true, message: '该项为必填项' }]
    // },
    // 数据源选择
    dataSource,
    // 任务类型
    type,
    // 任务频率
    frequency,
    // 运行时间
    // rangeTime,
    // 数据采集频率
    // intervalFrequency,
    // 审核用户
    // (formData = {}) => {
    //   return {
    //     type: 'Select',
    //     label: '审核用户',
    //     key: 'schema',
    //     props: {
    //       url: '/sqlreview/project/get_schema_list',
    //       reqParams: {
    //         datasource_id: formData.data_source
    //       },
    //       allowSearch: true,
    //       backSearch: true,
    //       limit: 50,
    //       disabled: ctx.isDisabled
    //     },
    //     visible: formData.db_type == 'oracle',
    //     rules: [{ required: true, message: '该项为必填项' }]
    //   };
    // },
    // 时间范围选择
    timeRange,
    // 规则集选择
    ruleSet
  ];

  const pgInfo = [
    // 数据源选择
    dataSource,
    // 类型
    type,
    // 运行时间
    rangeTime,
    // 数据采集频率
    intervalFrequency,
    // 规则集选择
    ruleSet
  ];

  const sqlserverInfo = [
    // 数据源选择
    dataSource,
    // 运行时间
    rangeTime,
    // 数据采集频率
    intervalFrequency,
    // 规则集选择
    ruleSet
  ];

  const cornInfo = [
    // 天 周 月
    {
      type: 'Select',
      label: '',
      key: 'type',
      width: '100%',
      props: {
        options: [
          {
            label: '每天',
            value: '每天'
          },
          {
            label: '每周',
            value: '每周'
          },
          {
            label: '每月',
            value: '每月'
          }
        ]
      },
      listeners: {
        change: (value) => {
          ctx.type = value;
          ctx.$refs.form.saving({
            type: value
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    },
    // 每周
    (formData = {}) => {
      return {
        type: 'Select',
        label: '',
        key: 'week',
        width: '100%',
        props: {
          options: ctx.weekOption
        },
        visible: formData.type === '每周',
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              week: value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      };
    },
    // 每月
    (formData = {}) => {
      return {
        type: 'Select',
        label: '',
        key: 'month',
        width: '100%',
        props: {
          options: ctx.monthOption
        },
        visible: formData.type === '每月',
        listeners: {
          change: (value) => {
            ctx.$refs.form.saving({
              month: value
            });
          }
        },
        rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
      };
    },
    // 时间
    {
      type: 'TimePicker',
      label: '',
      key: 'time',
      width: '100%',
      props: {
        showTime: {
          format: 'HH:mm:ss'
        }
      },
      listeners: {
        change: (value) => {
          ctx.$refs.form.saving({
            time: value
          });
        }
      },
      rules: [{ required: true, message: '该项为必填项', trigger: 'change' }]
    }
  ];

  const mysqlBaseInfo = [
    {
      type: 'RadioGroup',
      label: '登录方式',
      key: 'mode',
      props: {
        options: [
          { label: '密码登录', value: 1 },
          { label: 'key免密登录', value: 0 }
        ]
      },
      listeners: {
        change: (val) => {
          ctx.$refs.mysqlForm.saving({
            mode: val
          });
        }
      },
      span: 24,
      rules: [{ required: true, message: '该项为必填项' }]
    },
    // {
    //   type: 'Label',
    //   label: '',
    //   key: '',
    //   props: {},
    //   rules: []
    // },
    {
      type: 'Input',
      label: 'IP',
      key: 'server_ip',
      props: { placeholder: '请输入服务器IP' },
      rules: [{ required: true, message: '该项为必填项' }]
    },
    {
      type: 'Input',
      label: '端口号',
      key: 'server_port',
      props: { placeholder: '请输入服务器端口号' },
      rules: [{ required: true, message: '该项为必填项' }]
    },
    {
      type: 'Input',
      label: '用户名',
      key: 'server_username',
      props: {},
      rules: [{ required: true, message: '该项为必填项' }]
    },
    (formData = {}) => {
      return {
        type: 'InputPassword',
        label: '密码',
        key: 'server_password',
        visible: formData.mode == 1,
        props: {},
        rules: [{ required: true, message: '该项为必填项' }]
      };
    }
  ];

  return {
    baseInfo,
    POSTGRE_FIELDS: pgInfo,
    SQLSERVER_FIELDS: sqlserverInfo,
    cornInfo,
    mysqlBaseInfo
  };
}
