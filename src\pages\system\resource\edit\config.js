export default function (ctx) {
  const typeRelation = {
    root_resource: ['system', 'menu'],
    system: ['element'],
    menu: ['menu', 'page'],
    page: ['element']
  };
  const iconMap = {
    system: {
      type: 'home',
      color: '#f5793b'
    },
    menu: {
      type: 'folder',
      color: '#716dda'
    },
    page: {
      type: 'file-text',
      color: '#58b372'
    },
    element: {
      type: 'code-sandbox-square',
      color: '#efb176'
    },
    schema: {
      type: 'profile',
      color: '#95b75a'
    }
  };
  const getFields = (node = {}) => {
    const category = node.source_category;
    // const type = node.source_type;
    const isAdd = node.isAdd;
    const parentType = node.parentType;
    let parentNode = ctx.getNodeMap()[node.parent_id];
    const resources = ctx.resources;
    const { map: resMap = {}, treeData = [] } = resources;
    const codeOptions = (resMap[node.parent_id] ? resMap[node.parent_id].children || [] : treeData)
      .map(itm => ({ ...itm, label: itm.desc, value: itm.id }));

    // console.log(node, node.parent_id, resMap[node.parent_id])
    let fields = [];
    // code
    if (category === 'resource' && isAdd) {
      if (['page', 'system'].includes(node.parentType)) {
        let treeData = [];
        if (node.parentType == 'page') {
          treeData = (ctx.getAuthElementsByPage(node.parent_id.split('_')[1]) || []).map(item => {
            return {
              ...item,
              disabled: parentNode.children.find(_itm => _itm.key.startsWith(item.value)) != null
            }
          })
        } else {
          treeData = (codeOptions || []).map(item => {
            return {
              ...item,
              disabled: parentNode.children.find(_itm => _itm.key.startsWith(item.value)) != null
            }
          })
        }
        fields.push({
          type: 'TreeSelect',
          label: '资源code',
          key: 'id',
          props: {
            // treeData: (ctx.getAuthElementsByPage(node.parent_id.split('_')[1]) || []).map(item => {
            //   return {
            //     ...item,
            //     disabled: parentNode.children.find(_itm => _itm.key.startsWith(item.value)) != null
            //   }
            // }),
            treeData,
            placeholder: '请选择',
            disabled: !isAdd,
            dropdownStyle: { maxHeight: '50vh' },
            showSearch: true,
            treeNodeFilterProp: 'label'
          },
          listeners: {
            change(val) {
              const matchItem = window.AuthMap[val] || {};
              // let initSourceExt = [];
              // _.forEach((matchItem.initialValue || {}), (item, key) => {
              //   initSourceExt.push({
              //     _key: key,
              //     _value: item
              //   })
              // });
              ctx.$refs.form.saving({
                source_name: matchItem.label,
                source_type: 'element',
                source_deal_type: matchItem.type,
                source_data: matchItem.sourceData != null ? JSON.stringify(matchItem.sourceData) : null,
                source_init_value: matchItem.initialValue != null ? JSON.stringify(matchItem.initialValue) : null
                // source_ext: initSourceExt
              })
            }
          },
          rules: [
            { required: true, message: '该项为必填项', trigger: 'blur' }
          ]
        })
      } else {
        fields.push({
          type: 'Select',
          label: '资源code',
          key: 'id',
          props: {
            options: (codeOptions || []).map(item => {
              return {
                ...item,
                disabled: parentNode.children.find(_itm => _itm.key.startsWith(item.value)) != null
              }
            }),
            mode: 'tags',
            maxTags: 1,
            separator: ',',
            disabled: !isAdd
          },
          listeners: {
            change(val) {
              const matchItem = codeOptions.find(itm => itm.value === val) || {};
              ctx.$refs.form.saving({
                source_name: matchItem.desc,
                source_type: matchItem.source_type,
                source_deal_type: matchItem.type,
                source_data: null,
                source_init_value: null
              })
            }
          },
          rules: [
            { required: true, message: '该项为必填项', trigger: 'blur' }
          ]
        })
      }
    } else {
      fields.push({
        type: 'Input',
        label: '资源code',
        key: 'id',
        props: {
          disabled: !isAdd
        },
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      })
    }

    // name
    fields.push(
      {
        type: 'Input',
        label: '资源name',
        key: 'source_name',
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      }
    );

    // type
    if (category === 'resource') {
      fields.push({
        type: 'Select',
        label: '类型',
        key: 'source_type',
        props: {
          options: [
            { label: '系统', value: 'system' },
            { label: '菜单', value: 'menu' },
            { label: '页面', value: 'page' },
            { label: '元素', value: 'element' }
          ].filter(item => {
            if (isAdd) {
              return !typeRelation[parentType] || typeRelation[parentType].includes(item.value);
            }
            return true;
          }),
          disabled: !isAdd
        },
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      })
    } else {
      fields.push({
        type: 'Select',
        label: '类型',
        key: 'source_type',
        props: {
          options: [
            { label: 'schema', value: 'schema' }
          ].filter(item => {
            if (isAdd) {
              return !typeRelation[parentType] || typeRelation[parentType].includes(item.value);
            }
            return true;
          }),
          disabled: !isAdd
        },
        rules: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      })
    }

    // role
    fields.push(
      (data = {}) => {
        return {
          type: 'Select',
          label: '角色',
          key: 'role',
          props: {
            mode: 'multiple',
            url: `/sqlreview/project_config/role_info_by_select`
          },
          visible: data.source_type !== 'element'
          //   { required: true, message: '该项为必填项', trigger: 'blur' }
          // ]
        }
      }
    );

    // sort
    fields.push(
      (data = {}) => {
        return {
          type: 'InputNumber',
          label: '排序',
          key: 'sort_num',
          visible: data.source_type !== 'element'
          //   { required: true, message: '该项为必填项', trigger: 'blur' }
          // ]
        }
      }
    );

    // params type
    fields.push(
      (data = {}) => {
        // const matchItem = window.AuthMap[data.id] || {};
        // const realValue = matchItem.type;
        return {
          type: 'Input',
          label: '元素处理类型',
          key: 'source_deal_type',
          props: {
            disabled: true
          },
          // realValue,
          visible: data.source_type === 'element'
          //   { required: true, message: '该项为必填项', trigger: 'blur' }
          // ]
        }
      }
    );
    fields.push(
      (data = {}) => {
        // const matchItem = window.AuthMap[data.id] || {};
        // const realValue = matchItem.type;
        return {
          type: 'Coder',
          label: '元数据',
          key: 'source_data',
          tips: '编辑时请勿随意改动，数据的<删除>和<修改>需要后端刷数据',
          props: {
            // disabled: true
            options: {
              theme: 'default'
            },
            needFormat: true,
            type: 'json'
          },
          // realValue,
          visible: data.source_type === 'element' && ['ArrayMerge', 'BACK'].includes(_.get(data, 'source_deal_type'))
          //   { required: true, message: '该项为必填项', trigger: 'blur' }
          // ]
        }
      }
    );
    fields.push(
      (data = {}) => {
        // const matchItem = window.AuthMap[data.id] || {};
        // const realValue = matchItem.type;
        return {
          type: 'Input',
          label: '初始值',
          key: 'source_init_value',
          tips: '值为空时默认全部展示',
          props: {
            disabled: !isAdd
          },
          // realValue,
          visible: data.source_type === 'element' && ['ArrayMerge', 'BACK'].includes(_.get(data, 'source_deal_type'))
          //   { required: true, message: '该项为必填项', trigger: 'blur' }
          // ]
        }
      }
    );

    // params
    fields.push({
      type: 'TableEdit',
      label: '资源参数',
      // tips: '<div>用于设置文件/文件夹，从而不进行sql review扫描</div>',
      key: 'source_ext',
      getDataMethod: 'getData',
      resetFieldsMethod: 'resetFields',
      initialValue: [],
      visible: false,
      props: {
        columns: [
          {
            dataIndex: '_key',
            title: '字段名',
            key: '_key',
            width: 300,
            scopedSlots: { customRender: '_key' }
          },
          {
            dataIndex: '_value',
            title: '字段值',
            key: '_value',
            width: 300,
            scopedSlots: { customRender: '_value' }
          },
          {
            key: 'action',
            title: '操作',
            width: 60,
            scopedSlots: { customRender: 'action' }
          }
        ],
        editConfig: {
          _key: (row, record = {}) => {
            return {
              type: 'Input',
              props: {
                size: 'default',
                placeholder: 'key'
              },
              rules: [
                // { required: true, message: '该项为必填项' }
              ]
            }
          },
          _value: (row, record = {}) => {
            return {
              type: 'Textarea',
              props: {
                size: 'default',
                placeholder: 'value',
                rows: 1
              },
              rules: [
                // { required: true, message: '该项为必填项' }
              ]
            }
          }
        },
        // mode: 'list',
        initEditStatus: true,
        pagination: false,
        leastNum: 1,
        actionBtns: ['add', 'remove'],
        actionBtnsIcons: {
          add: 'plus-circle',
          remove: 'close-circle'
        }
      }
    })
    return fields;
  }
  return {
    typeRelation,
    getFields,
    iconMap
  };
};
